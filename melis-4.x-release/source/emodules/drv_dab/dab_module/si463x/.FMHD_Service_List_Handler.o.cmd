cmd_emodules/drv_dab/dab_module/si463x/FMHD_Service_List_Handler.o := /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/source/../toolchain/riscv64-elf-x86_64-20201104//bin/riscv64-unknown-elf-gcc -Wp,-MD,emodules/drv_dab/dab_module/si463x/.FMHD_Service_List_Handler.o.d  -isystem /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/include  -Iinclude -Iinclude/melis -Iinclude/melis/common -Iinclude/melis/eboot -Iinclude/melis/eboard -Iinclude/melis/ekernel -Iinclude/melis/ekernel/arch -Iinclude/melis/ekernel/csp -Iinclude/melis/ekernel/drivers -Iinclude/melis/ekernel/filesystem -Iinclude/melis/ekernel/pthread -Iinclude/melis/elibrary -Iinclude/melis/elibrary/libc -Iinclude/melis/elibrary/libc/mediainfo -Iinclude/melis/elibrary/libc/misc -Iinclude/melis/elibrary/libc/misc/pub0 -Iinclude/melis/emodules -Iinclude/melis/ipc -Iinclude/melis/misc -Iinclude/melis/of -Iinclude/melis/sys -Iinclude/melis/video -Iekernel/components/thirdparty/finsh_cli -Iekernel/core/rt-thread/include -Iekernel/components/thirdparty/dfs/include -Iekernel/drivers/rtos-hal/hal/source -Iekernel/drivers/include/osal -Iinclude/generated -Iinclude/generated/uapi/melis -I./include/melis/elibrary -include ./include/melis/kconfig.h -Iinclude/melis/ekernel/arch/riscv -Iinclude/melis/arch/riscv -DARCH_CPU_64BIT -mcmodel=medany -mabi=lp64d -march=rv64gcxthead -falign-functions=4 -mcmodel=medany -mabi=lp64d -march=rv64gcxthead -Wall -Wundef -Wstrict-prototypes -Wno-trigraphs -fno-strict-aliasing -fno-common -Werror-implicit-function-declaration -Wno-format-security -fno-builtin-printf -D_SYS__PTHREADTYPES_H_ -fno-delete-null-pointer-checks -ffunction-sections -fdata-sections -fzero-initialized-in-bss -Wmaybe-uninitialized -Os -g -gdwarf-2 -gstrict-dwarf -Wstrict-prototypes -Wundef -Wno-unused-function -Wno-unused-variable -fshort-enums -Wsizeof-pointer-memaccess --param=allow-store-data-races=0 -Wframe-larger-than=8192 -fno-stack-protector -Wno-unused-but-set-variable -fomit-frame-pointer -fno-var-tracking-assignments -g -Wdeclaration-after-statement -Wno-pointer-sign -fno-strict-overflow -fconserve-stack -Werror=implicit-int -Werror=strict-prototypes -DCC_HAVE_ASM_GOTO -Wno-declaration-after-statement -mcmodel=medany -mabi=lp64d -march=rv64gcxthead -Wno-unused-label -Wextra -Wno-unused-parameter -Wno-old-style-declaration -Wno-sign-compare -I./include/melis/ekernel -I./include/melis/ekernel/drivers -I./include/melis/emodules/mod_orange/gui/core -I./ekernel/drivers/include/hal -I./ekernel/drivers/include/drv -I./ekernel/core/rt-thread/include -I./livedesk/leopard/include/elibs/lib_ex -I./livedesk/leopard/include/mod_desktop/functions  -DMODULE  -D"KBUILD_STR(s)=\#s" -D"KBUILD_BASENAME=KBUILD_STR(FMHD_Service_List_Handler)"  -D"KBUILD_MODNAME=KBUILD_STR(dab)" -c -o emodules/drv_dab/dab_module/si463x/.tmp_FMHD_Service_List_Handler.o emodules/drv_dab/dab_module/si463x/FMHD_Service_List_Handler.c

source_emodules/drv_dab/dab_module/si463x/FMHD_Service_List_Handler.o := emodules/drv_dab/dab_module/si463x/FMHD_Service_List_Handler.c

deps_emodules/drv_dab/dab_module/si463x/FMHD_Service_List_Handler.o := \
  include/melis/kconfig.h \
    $(wildcard include/config/h.h) \
  emodules/drv_dab/dab_module/si463x/common_types.h \
  emodules/drv_dab/dab_module/si463x/../../drv_dab_i.h \
  include/melis/typedef.h \
    $(wildcard include/config/drivers/usb/gadget/qvh.h) \
  /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/lib/gcc/riscv64-unknown-elf/8.4.0/include/stddef.h \
  /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/lib/gcc/riscv64-unknown-elf/8.4.0/include/stdint.h \
  /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/riscv64-unknown-elf/include/stdint.h \
  /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/riscv64-unknown-elf/include/machine/_default_types.h \
  /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/riscv64-unknown-elf/include/sys/features.h \
  /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/riscv64-unknown-elf/include/_newlib_version.h \
  /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/riscv64-unknown-elf/include/sys/_intsup.h \
  /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/riscv64-unknown-elf/include/sys/_stdint.h \
  /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/lib/gcc/riscv64-unknown-elf/8.4.0/include/stdarg.h \
  /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/lib/gcc/riscv64-unknown-elf/8.4.0/include-fixed/limits.h \
  /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/lib/gcc/riscv64-unknown-elf/8.4.0/include-fixed/syslimits.h \
  /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/riscv64-unknown-elf/include/limits.h \
  /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/riscv64-unknown-elf/include/newlib.h \
  /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/riscv64-unknown-elf/include/sys/cdefs.h \
  include/melis/elibrary/libc/elibs_stdio.h \
  include/melis/ekernel/kapi.h \
    $(wildcard include/config/melis/legacy/driver/man.h) \
    $(wildcard include/config/t.h) \
    $(wildcard include/config/kernel/use/sbi.h) \
  /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/riscv64-unknown-elf/include/errno.h \
  /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/riscv64-unknown-elf/include/sys/errno.h \
  /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/riscv64-unknown-elf/include/sys/reent.h \
  /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/riscv64-unknown-elf/include/_ansi.h \
  /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/riscv64-unknown-elf/include/sys/config.h \
    $(wildcard include/config/h//.h) \
  /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/riscv64-unknown-elf/include/machine/ieeefp.h \
  /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/riscv64-unknown-elf/include/sys/_types.h \
  /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/riscv64-unknown-elf/include/machine/_types.h \
  /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/riscv64-unknown-elf/include/sys/lock.h \
  include/melis/ekernel/kmsg.h \
    $(wildcard include/config/drivers/usb/gadget/ncm.h) \
    $(wildcard include/config/setting/update.h) \
  include/melis/eboot/boot.h \
  include/melis/ekernel/drivers/sys_charset.h \
  include/melis/ekernel/drivers/sys_clock.h \
  include/melis/ekernel/ktype.h \
  include/melis/ekernel/csp/csp_ccm_para.h \
    $(wildcard include/config/drivers/sunxi/clk.h) \
    $(wildcard include/config/drivers/sunxi/ccu.h) \
  include/melis/kconfig.h \
  include/melis/ekernel/drivers/sys_device.h \
    $(wildcard include/config/spinor/cmmb/data/offset.h) \
    $(wildcard include/config/spinor/cmmb/data/size.h) \
    $(wildcard include/config/spinor/apps/data/offset.h) \
    $(wildcard include/config/spinor/apps/data/size.h) \
    $(wildcard include/config/spinor/mcu/update/data/offset.h) \
    $(wildcard include/config/spinor/mcu/update/data/size.h) \
  include/melis/ekernel/drivers/sys_fsys.h \
    $(wildcard include/config/soc/sun20iw1.h) \
    $(wildcard include/config/soc/sun8iw20.h) \
    $(wildcard include/config/driver/spinor.h) \
  /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/lib/gcc/riscv64-unknown-elf/8.4.0/include/stdbool.h \
  include/melis/ekernel/drivers/sys_device.h \
  include/melis/ekernel/drivers/sys_hwsc.h \
  include/melis/ekernel/drivers/sys_input.h \
  include/melis/ekernel/drivers/sys_mems.h \
  include/melis/ekernel/csp/csp_dram_para.h \
    $(wildcard include/config/soc/sun3iw2.h) \
    $(wildcard include/config/soc/sun8i.h) \
    $(wildcard include/config/soc/sun20iw3.h) \
    $(wildcard include/config/soc/sun3iw1.h) \
  include/melis/ekernel/drivers/sys_pins.h \
  include/melis/script.h \
  include/melis/ekernel/drivers/sys_powerman.h \
  include/melis/ekernel/drivers/sys_svc.h \
  include/melis/ekernel/drivers/sys_time.h \
  include/melis/ekernel/csp/csp_dma_para.h \
    $(wildcard include/config/soc/sun8iw19.h) \
  include/melis/ekernel/csp/csp_int_para.h \
  include/melis/emodules/mod_defs.h \
  include/melis/log.h \
    $(wildcard include/config/log/default/level.h) \
    $(wildcard include/config/log/release.h) \
    $(wildcard include/config/dynamic/log/level/support.h) \
    $(wildcard include/config/disable/all/debuglog.h) \
  include/melis/emodules/mod_dab.h \
  include/melis/emodules/mod_fm.h \
  include/melis/emodules/mod_audio.h \
    $(wildcard include/config/plan/sprite.h) \
  include/melis/emodules/mod_twi.h \
  include/melis/emodules/mod_spi.h \
  include/melis/elibrary/libc/elibs_string.h \
  include/melis/emodules/mod_mcu.h \
  include/melis/emodules/mod_cedar.h \
    $(wildcard include/config/video/lyr/ck/enable.h) \
  include/melis/misc/fb.h \
  include/melis/emodules/mod_fm.h \
  include/melis/emodules/mod_dab.h \
  include/melis/emodules/mod_aux.h \
  include/melis/emodules/mod_bp1064.h \
  include/melis/emodules/mod_bt_moudel.h \
  include/melis/emodules/SaveRds.h \
  include/melis/emodules/mod_video_play.h \
    $(wildcard include/config/para.h) \
  include/melis/emodules/../../../emodules/drv_mcu_uart/core/uart_cmd_set.h \
  include/melis/emodules/../../../emodules/drv_mcu_uart/core/sxi_defs.h \
  include/melis/emodules/../../../emodules/drv_mcu_uart/core/handlers/protocol/mcu_new_protocol_handler.h \
  include/melis/emodules/../../../emodules/drv_mcu_uart/core/handlers/protocol/../../mcu_cmd_dispatcher.h \
  include/melis/emodules/../../../emodules/drv_mcu_uart/core/handlers/protocol/../../uart_cmd_set.h \
  include/melis/emodules/../../../emodules/drv_mcu_uart/core/handlers/protocol/system/mcu_system_category_handler.h \
  include/melis/emodules/../../../emodules/drv_mcu_uart/core/handlers/protocol/audio/mcu_audio_category_handler.h \
  include/melis/emodules/../../../emodules/drv_mcu_uart/core/handlers/protocol/tuner/mcu_tuner_category_handler.h \
  include/melis/emodules/../../../emodules/drv_mcu_uart/core/handlers/protocol/tuner/../mcu_protocol_sync.h \
  include/melis/emodules/../../../emodules/drv_mcu_uart/core/handlers/protocol/usb/mcu_usb_category_handler.h \
  include/melis/emodules/../../../emodules/drv_mcu_uart/core/handlers/protocol/party/mcu_party_category_handler.h \
  include/melis/emodules/../../../emodules/drv_mcu_uart/core/handlers/protocol/settings/mcu_settings_category_handler.h \
  include/melis/emodules/mod_charset.h \
  include/melis/emodules/mod_bp1064.h \
  include/melis/emodules/mod_bt_moudel.h \
  include/melis/elibrary/libc.h \
  include/melis/elibrary/./libc/elibs_stdlib.h \
  include/melis/elibrary/./libc/elibs_string.h \
  include/melis/elibrary/./libc/elibs_stdio.h \
  include/melis/elibrary/./libc/elibs_misc.h \
  include/melis/elibrary/./libc/misc/pub0.h \
  include/melis/elibrary/./libc/misc/pub0/elibs_cacheop.h \
  include/melis/elibrary/./libc/misc/pub0/elibs_reg.h \
  include/melis/ekernel/arch/riscv/arch.h \
  include/melis/elibrary/libc/misc/pub0.h \
  /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/riscv64-unknown-elf/include/string.h \
  /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/riscv64-unknown-elf/include/_ansi.h \
  /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/riscv64-unknown-elf/include/xlocale.h \
  /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/riscv64-unknown-elf/include/strings.h \
  /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/riscv64-unknown-elf/include/sys/string.h \
  ekernel/core/rt-thread/include/rtthread.h \
  ekernel/core/rt-thread/include/rtconfig.h \
    $(wildcard include/config/rt/name/max.h) \
    $(wildcard include/config/rt/align/size.h) \
    $(wildcard include/config/modules.h) \
    $(wildcard include/config/rt/thread/priority/max.h) \
    $(wildcard include/config/hz.h) \
    $(wildcard include/config/rt/debug.h) \
    $(wildcard include/config/rt/debug/init/config.h) \
    $(wildcard include/config/rt/debug/thread/config.h) \
    $(wildcard include/config/rt/debug/scheduler/config.h) \
    $(wildcard include/config/rt/debug/ipc/config.h) \
    $(wildcard include/config/rt/debug/timer/config.h) \
    $(wildcard include/config/rt/debug/irq/config.h) \
    $(wildcard include/config/rt/debug/mem/config.h) \
    $(wildcard include/config/rt/debug/slab/config.h) \
    $(wildcard include/config/rt/debug/memheap/config.h) \
    $(wildcard include/config/rt/debug/module/config.h) \
    $(wildcard include/config/rt/using/overflow/check.h) \
    $(wildcard include/config/rt/using/hook.h) \
    $(wildcard include/config/rt/using/idle/hook.h) \
    $(wildcard include/config/rt/using/timer/soft.h) \
    $(wildcard include/config/rt/timer/thread/prio.h) \
    $(wildcard include/config/rt/timer/thread/stack/size.h) \
    $(wildcard include/config/rt/using/semaphore.h) \
    $(wildcard include/config/rt/using/mutex.h) \
    $(wildcard include/config/rt/using/event.h) \
    $(wildcard include/config/rt/using/mailbox.h) \
    $(wildcard include/config/rt/using/messagequeue.h) \
    $(wildcard include/config/rt/using/pipe.h) \
    $(wildcard include/config/rt/using/ringbuffer.h) \
    $(wildcard include/config/rt/using/waitqueue.h) \
    $(wildcard include/config/rt/using/workqueue.h) \
    $(wildcard include/config/rt/using/completion.h) \
    $(wildcard include/config/rt/using/signals.h) \
    $(wildcard include/config/rt/using/mempool.h) \
    $(wildcard include/config/rt/using/memheap.h) \
    $(wildcard include/config/rt/using/noheap.h) \
    $(wildcard include/config/rt/using/small/mem.h) \
    $(wildcard include/config/rt/using/slab.h) \
    $(wildcard include/config/rt/using/memheap/as/heap.h) \
    $(wildcard include/config/rt/using/memtrace.h) \
    $(wildcard include/config/rt/using/heap.h) \
    $(wildcard include/config/rt/using/device.h) \
    $(wildcard include/config/rt/using/interrupt/info.h) \
    $(wildcard include/config/rt/using/console.h) \
    $(wildcard include/config/rt/consolebuf/size.h) \
    $(wildcard include/config/rt/console/device/name.h) \
    $(wildcard include/config/rt/using/finsh.h) \
    $(wildcard include/config/finsh/using/symtab.h) \
    $(wildcard include/config/finsh/thread/name.h) \
    $(wildcard include/config/finsh/using/history.h) \
    $(wildcard include/config/finsh/history/lines.h) \
    $(wildcard include/config/finsh/using/description.h) \
    $(wildcard include/config/finsh/thread/priority.h) \
    $(wildcard include/config/finsh/thread/stack/size.h) \
    $(wildcard include/config/finsh/cmd/size.h) \
    $(wildcard include/config/finsh/using/msh.h) \
    $(wildcard include/config/finsh/using/msh/default.h) \
    $(wildcard include/config/finsh/arg/max.h) \
    $(wildcard include/config/rt/using/dfs.h) \
  ekernel/core/rt-thread/include/rtdebug.h \
  ekernel/core/rt-thread/include/rtdef.h \
    $(wildcard include/config/arch/riscv.h) \
    $(wildcard include/config/arch/riscv/vector.h) \
  include/melis/ekernel/arch/riscv/excep.h \
    $(wildcard include/config/fpu/double.h) \
  ekernel/core/rt-thread/include/rtservice.h \
  ekernel/core/rt-thread/include/rtm.h \
  ekernel/components/thirdparty/finsh_cli/finsh_api.h \
  ekernel/drivers/include/hal/aw_common.h \
    $(wildcard include/config/kernel/freertos.h) \
  ekernel/drivers/include/hal/sunxi_hal_common.h \
    $(wildcard include/config/debug/backtrace.h) \
    $(wildcard include/config/components/amp.h) \
  /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/riscv64-unknown-elf/include/stdio.h \
  /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/riscv64-unknown-elf/include/sys/types.h \
  /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/riscv64-unknown-elf/include/machine/endian.h \
  /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/riscv64-unknown-elf/include/machine/_endian.h \
  /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/riscv64-unknown-elf/include/sys/select.h \
  /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/riscv64-unknown-elf/include/sys/_sigset.h \
  /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/riscv64-unknown-elf/include/sys/_timeval.h \
  /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/riscv64-unknown-elf/include/sys/timespec.h \
  /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/riscv64-unknown-elf/include/sys/_timespec.h \
  /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/riscv64-unknown-elf/include/sys/_pthreadtypes.h \
  /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/riscv64-unknown-elf/include/machine/types.h \
  /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/riscv64-unknown-elf/include/sys/stdio.h \
  include/melis/backtrace.h \
  include/melis/ekernel/arch/riscv/barrier.h \
    $(wildcard include/config/arch/riscv/exten/instruction/set.h) \
  ekernel/components/thirdparty/dfs/include/dfs_posix.h \
  ekernel/components/thirdparty/dfs/include/dfs_file.h \
  ekernel/components/thirdparty/dfs/include/dfs.h \
  /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/riscv64-unknown-elf/include/stdlib.h \
  /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/riscv64-unknown-elf/include/machine/stdlib.h \
  /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/riscv64-unknown-elf/include/alloca.h \
  /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/riscv64-unknown-elf/include/time.h \
  /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/riscv64-unknown-elf/include/machine/time.h \
  /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/riscv64-unknown-elf/include/sys/stat.h \
  /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/riscv64-unknown-elf/include/fcntl.h \
  /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/riscv64-unknown-elf/include/sys/fcntl.h \
  /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/riscv64-unknown-elf/include/sys/_default_fcntl.h \
  /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/riscv64-unknown-elf/include/sys/unistd.h \
  ekernel/components/thirdparty/dfs/include/dfs_fs.h \
  ekernel/drivers/include/hal/sunxi_hal_twi.h \
    $(wildcard include/config/components/pm.h) \
    $(wildcard include/config/drivers/twi/debug.h) \
  ekernel/drivers/include/osal/hal_sem.h \
    $(wildcard include/config/rttkernel.h) \
  ekernel/drivers/include/hal/hal_clk.h \
  ekernel/drivers/rtos-hal/hal/source/ccmu/common_ccmu.h \
  ekernel/drivers/rtos-hal/hal/source/ccmu/sunxi-ng/clk.h \
  ekernel/drivers/rtos-hal/hal/source/ccmu/sunxi-ng/ccu.h \
  ekernel/drivers/include/hal/aw_list.h \
    $(wildcard include/config/debug/list.h) \
  ekernel/drivers/include/osal/hal_log.h \
  ekernel/drivers/include/osal/hal_atomic.h \
    $(wildcard include/config/core/dsp0.h) \
    $(wildcard include/config/os/melis.h) \
  ekernel/drivers/rtos-hal/hal/source/ccmu/sunxi-ng/ccu_slab.h \
  ekernel/drivers/rtos-hal/hal/source/ccmu/sunxi-ng/../common_ccmu.h \
  ekernel/drivers/rtos-hal/hal/source/ccmu/platform_ccmu.h \
    $(wildcard include/config/arch/sun20iw3.h) \
    $(wildcard include/config/arch/sun8iw21.h) \
    $(wildcard include/config/arch/sun20iw1.h) \
    $(wildcard include/config/arch/sun8iw20.h) \
    $(wildcard include/config/arch/sun20iw2.h) \
    $(wildcard include/config/arch/sun55iw3.h) \
    $(wildcard include/config/arch/sun55iw6.h) \
    $(wildcard include/config/arch/sun60iw1.h) \
    $(wildcard include/config/arch/sun300iw1.h) \
  ekernel/drivers/rtos-hal/hal/source/ccmu/./sunxi-ng/clk-fixed-rate.h \
  ekernel/drivers/rtos-hal/hal/source/ccmu/./sunxi-ng/ccu-sun8iw20.h \
  ekernel/drivers/rtos-hal/hal/source/ccmu/./sunxi-ng/ccu-sun8iw20-r.h \
  ekernel/drivers/rtos-hal/hal/source/ccmu/./sunxi-ng/ccu-sun8iw20-rtc.h \
  ekernel/drivers/include/hal/hal_reset.h \
  ekernel/drivers/rtos-hal/hal/source/ccmu/platform_rst.h \
  ekernel/drivers/rtos-hal/hal/source/ccmu/sunxi-ng/rst-sun8iw20.h \
  ekernel/drivers/rtos-hal/hal/source/ccmu/sunxi-ng/rst-sun8iw20-r.h \
  ekernel/drivers/include/hal/sunxi_hal_common.h \
  ekernel/drivers/include/hal/hal_gpio.h \
    $(wildcard include/config/drivers/gpio/debug.h) \
  ekernel/drivers/include/osal/hal_interrupt.h \
    $(wildcard include/config/arch/sun20iw2p1.h) \
    $(wildcard include/config/arch/dsp.h) \
  ekernel/drivers/rtos-hal/hal/source/gpio/gpio.h \
    $(wildcard include/config/drivers/gpio/ex/aw9523.h) \
    $(wildcard include/config/end.h) \
    $(wildcard include/config/max.h) \
    $(wildcard include/config/arch/sun8iw19.h) \
    $(wildcard include/config/arch/sun8iw18p1.h) \
    $(wildcard include/config/arch/sun50iw11.h) \
    $(wildcard include/config/standby.h) \
  ekernel/drivers/rtos-hal/hal/source/gpio/sun8iw20/platform-gpio.h \
    $(wildcard include/config/arch/arm.h) \
  ekernel/drivers/include/hal/sunxi_hal_regulator.h \
  ekernel/drivers/include/osal/hal_debug.h \
  include/melis/debug.h \
  ekernel/drivers/rtos-hal/hal/source/twi/platform_twi.h \
  ekernel/drivers/rtos-hal/hal/source/twi/platform/twi_sun8iw20.h \
  ekernel/drivers/rtos-hal/hal/source/twi/common_twi.h \
  ekernel/drivers/include/osal/hal_mutex.h \
  emodules/drv_dab/dab_module/si463x/platform_options.h \
  emodules/drv_dab/dab_module/si463x/platform_selector.h \
  emodules/drv_dab/dab_module/si463x/dab_linklist.h \
  emodules/drv_dab/dab_module/si463x/FMHD_Service_List_Handler.h \
  emodules/drv_dab/dab_module/si463x/feature_types.h \
  emodules/drv_dab/dab_module/si463x/si46xx_firmware_api.h \
  emodules/drv_dab/dab_module/si463x/si46xx_firmware_api_status.h \
  emodules/drv_dab/dab_module/si463x/JZMot-C/JZMot.h \
  emodules/drv_dab/dab_module/si463x/JZMot-C/typedef.h \
  emodules/drv_dab/dab_module/si463x/JZMot-C/../../../drv_dab_i.h \
  emodules/drv_dab/dab_module/si463x/JZMot-C/motData.h \
  emodules/drv_dab/dab_module/si463x/JZMot-C/motCheck.h \
  emodules/drv_dab/dab_module/si463x/JZMot-C/motGroup.h \
  emodules/drv_dab/dab_module/si463x/JZMot-C/motObject.h \
  emodules/drv_dab/dab_module/si463x/JZMot-C/motSegment.h \
  emodules/drv_dab/dab_module/si463x/JZMot-C/motDecoder.h \
  emodules/drv_dab/dab_module/si463x/JZMot-C/motBuffer.h \
  emodules/drv_dab/dab_module/si463x/JZMot-C/JZMot.h \
  emodules/drv_dab/dab_module/si463x/SDK_Callbacks.h \
  emodules/drv_dab/dab_module/si463x/firmware_API_Manager.h \
  emodules/drv_dab/dab_module/si463x/freq_stack.h \
  emodules/drv_dab/dab_module/si463x/si46xx_firmware_api_constants.h \
    $(wildcard include/config/mask.h) \
    $(wildcard include/config/index.h) \
    $(wildcard include/config/group.h) \
    $(wildcard include/config/value.h) \
    $(wildcard include/config/mono/lsb.h) \
    $(wildcard include/config/mono/msb.h) \
    $(wildcard include/config/mono/mask.h) \
    $(wildcard include/config/mono/bit.h) \
    $(wildcard include/config/mono/value.h) \
    $(wildcard include/config/mono/enum/stereo.h) \
    $(wildcard include/config/mono/enum/mono.h) \
    $(wildcard include/config/default.h) \
    $(wildcard include/config/enable/mask.h) \
    $(wildcard include/config/enable/index.h) \
    $(wildcard include/config/enable/group.h) \
    $(wildcard include/config/enable/value.h) \
    $(wildcard include/config/enable/intbouten/lsb.h) \
    $(wildcard include/config/enable/intbouten/msb.h) \
    $(wildcard include/config/enable/intbouten/mask.h) \
    $(wildcard include/config/enable/intbouten/bit.h) \
    $(wildcard include/config/enable/intbouten/value.h) \
    $(wildcard include/config/enable/i2souten/lsb.h) \
    $(wildcard include/config/enable/i2souten/msb.h) \
    $(wildcard include/config/enable/i2souten/mask.h) \
    $(wildcard include/config/enable/i2souten/bit.h) \
    $(wildcard include/config/enable/i2souten/value.h) \
    $(wildcard include/config/enable/dacouten/lsb.h) \
    $(wildcard include/config/enable/dacouten/msb.h) \
    $(wildcard include/config/enable/dacouten/mask.h) \
    $(wildcard include/config/enable/dacouten/bit.h) \
    $(wildcard include/config/enable/dacouten/value.h) \
    $(wildcard include/config/enable/default.h) \
    $(wildcard include/config/bbi/mask.h) \
    $(wildcard include/config/bbi/index.h) \
    $(wildcard include/config/bbi/group.h) \
    $(wildcard include/config/bbi/value.h) \
    $(wildcard include/config/bbi/shiftq/lsb.h) \
    $(wildcard include/config/bbi/shiftq/msb.h) \
    $(wildcard include/config/bbi/shiftq/mask.h) \
    $(wildcard include/config/bbi/shiftq/value.h) \
    $(wildcard include/config/bbi/shiftq/enum/none.h) \
    $(wildcard include/config/bbi/shiftq/enum/left1.h) \
    $(wildcard include/config/bbi/shiftq/enum/left2.h) \
    $(wildcard include/config/bbi/shiftq/enum/left3.h) \
    $(wildcard include/config/bbi/shiftq/enum/right3.h) \
    $(wildcard include/config/bbi/shiftq/enum/right2.h) \
    $(wildcard include/config/bbi/shiftq/enum/right1.h) \
    $(wildcard include/config/bbi/shifti/lsb.h) \
    $(wildcard include/config/bbi/shifti/msb.h) \
    $(wildcard include/config/bbi/shifti/mask.h) \
    $(wildcard include/config/bbi/shifti/value.h) \
    $(wildcard include/config/bbi/shifti/enum/none.h) \
    $(wildcard include/config/bbi/shifti/enum/left1.h) \
    $(wildcard include/config/bbi/shifti/enum/left2.h) \
    $(wildcard include/config/bbi/shifti/enum/left3.h) \
    $(wildcard include/config/bbi/shifti/enum/right3.h) \
    $(wildcard include/config/bbi/shifti/enum/right2.h) \
    $(wildcard include/config/bbi/shifti/enum/right1.h) \
    $(wildcard include/config/bbi/iqswap/lsb.h) \
    $(wildcard include/config/bbi/iqswap/msb.h) \
    $(wildcard include/config/bbi/iqswap/mask.h) \
    $(wildcard include/config/bbi/iqswap/bit.h) \
    $(wildcard include/config/bbi/iqswap/value.h) \
    $(wildcard include/config/bbi/iqswap/enum/false.h) \
    $(wildcard include/config/bbi/iqswap/enum/true.h) \
    $(wildcard include/config/bbi/format/lsb.h) \
    $(wildcard include/config/bbi/format/msb.h) \
    $(wildcard include/config/bbi/format/mask.h) \
    $(wildcard include/config/bbi/format/value.h) \
    $(wildcard include/config/bbi/format/enum/split.h) \
    $(wildcard include/config/bbi/format/enum/muxed.h) \
    $(wildcard include/config/bbi/format/enum/afe16.h) \
    $(wildcard include/config/bbi/format/enum/iobus.h) \
    $(wildcard include/config/bbi/format/enum/afe12.h) \
    $(wildcard include/config/bbi/default.h) \
    $(wildcard include/config/i2sout/pdrv/mask.h) \
    $(wildcard include/config/i2sout/pdrv/index.h) \
    $(wildcard include/config/i2sout/pdrv/group.h) \
    $(wildcard include/config/i2sout/pdrv/value.h) \
    $(wildcard include/config/i2sout/pdrv/dout/pdrv/lsb.h) \
    $(wildcard include/config/i2sout/pdrv/dout/pdrv/msb.h) \
    $(wildcard include/config/i2sout/pdrv/dout/pdrv/mask.h) \
    $(wildcard include/config/i2sout/pdrv/dout/pdrv/value.h) \
    $(wildcard include/config/i2sout/pdrv/dout/pdrv/range/min.h) \
    $(wildcard include/config/i2sout/pdrv/dout/pdrv/range/max.h) \
    $(wildcard include/config/i2sout/pdrv/dfs/pdrv/lsb.h) \
    $(wildcard include/config/i2sout/pdrv/dfs/pdrv/msb.h) \
    $(wildcard include/config/i2sout/pdrv/dfs/pdrv/mask.h) \
    $(wildcard include/config/i2sout/pdrv/dfs/pdrv/value.h) \
    $(wildcard include/config/i2sout/pdrv/dfs/pdrv/range/min.h) \
    $(wildcard include/config/i2sout/pdrv/dfs/pdrv/range/max.h) \
    $(wildcard include/config/i2sout/pdrv/dclk/pdrv/lsb.h) \
    $(wildcard include/config/i2sout/pdrv/dclk/pdrv/msb.h) \
    $(wildcard include/config/i2sout/pdrv/dclk/pdrv/mask.h) \
    $(wildcard include/config/i2sout/pdrv/dclk/pdrv/value.h) \
    $(wildcard include/config/i2sout/pdrv/dclk/pdrv/range/min.h) \
    $(wildcard include/config/i2sout/pdrv/dclk/pdrv/range/max.h) \
    $(wildcard include/config/i2sout/pdrv/default.h) \
    $(wildcard include/config/i2sout/odrv/mask.h) \
    $(wildcard include/config/i2sout/odrv/index.h) \
    $(wildcard include/config/i2sout/odrv/group.h) \
    $(wildcard include/config/i2sout/odrv/value.h) \
    $(wildcard include/config/i2sout/odrv/dout/odrv/lsb.h) \
    $(wildcard include/config/i2sout/odrv/dout/odrv/msb.h) \
    $(wildcard include/config/i2sout/odrv/dout/odrv/mask.h) \
    $(wildcard include/config/i2sout/odrv/dout/odrv/value.h) \
    $(wildcard include/config/i2sout/odrv/dout/odrv/range/min.h) \
    $(wildcard include/config/i2sout/odrv/dout/odrv/range/max.h) \
    $(wildcard include/config/i2sout/odrv/dfs/odrv/lsb.h) \
    $(wildcard include/config/i2sout/odrv/dfs/odrv/msb.h) \
    $(wildcard include/config/i2sout/odrv/dfs/odrv/mask.h) \
    $(wildcard include/config/i2sout/odrv/dfs/odrv/value.h) \
    $(wildcard include/config/i2sout/odrv/dfs/odrv/range/min.h) \
    $(wildcard include/config/i2sout/odrv/dfs/odrv/range/max.h) \
    $(wildcard include/config/i2sout/odrv/dclk/odrv/lsb.h) \
    $(wildcard include/config/i2sout/odrv/dclk/odrv/msb.h) \
    $(wildcard include/config/i2sout/odrv/dclk/odrv/mask.h) \
    $(wildcard include/config/i2sout/odrv/dclk/odrv/value.h) \
    $(wildcard include/config/i2sout/odrv/dclk/odrv/range/min.h) \
    $(wildcard include/config/i2sout/odrv/dclk/odrv/range/max.h) \
    $(wildcard include/config/i2sout/odrv/default.h) \
    $(wildcard include/config/blethb/lsb.h) \
    $(wildcard include/config/blethb/msb.h) \
    $(wildcard include/config/blethb/mask.h) \
    $(wildcard include/config/blethb/value.h) \
    $(wildcard include/config/blethb/enum/bler/no/errors.h) \
    $(wildcard include/config/blethb/enum/bler/1/2/errors.h) \
    $(wildcard include/config/blethb/enum/bler/3/5/errors.h) \
    $(wildcard include/config/blethb/enum/bler/uncorrectable.h) \
    $(wildcard include/config/blethcd/lsb.h) \
    $(wildcard include/config/blethcd/msb.h) \
    $(wildcard include/config/blethcd/mask.h) \
    $(wildcard include/config/blethcd/value.h) \
    $(wildcard include/config/blethcd/enum/bler/no/errors.h) \
    $(wildcard include/config/blethcd/enum/bler/1/2/errors.h) \
    $(wildcard include/config/blethcd/enum/bler/3/5/errors.h) \
    $(wildcard include/config/blethcd/enum/bler/uncorrectable.h) \
    $(wildcard include/config/rdsen/lsb.h) \
    $(wildcard include/config/rdsen/msb.h) \
    $(wildcard include/config/rdsen/mask.h) \
    $(wildcard include/config/rdsen/bit.h) \
    $(wildcard include/config/rdsen/value.h) \
    $(wildcard include/config/location/lsb.h) \
    $(wildcard include/config/location/msb.h) \
    $(wildcard include/config/location/mask.h) \
    $(wildcard include/config/location/bit.h) \
    $(wildcard include/config/location/value.h) \
    $(wildcard include/config/rsvd/lsb.h) \
    $(wildcard include/config/rsvd/msb.h) \
    $(wildcard include/config/rsvd/mask.h) \
    $(wildcard include/config/rsvd/bit.h) \
    $(wildcard include/config/rsvd/value.h) \
    $(wildcard include/config/name/lf/lsb.h) \
    $(wildcard include/config/name/lf/msb.h) \
    $(wildcard include/config/name/lf/mask.h) \
    $(wildcard include/config/name/lf/bit.h) \
    $(wildcard include/config/name/lf/value.h) \
    $(wildcard include/config/name/sf/lsb.h) \
    $(wildcard include/config/name/sf/msb.h) \
    $(wildcard include/config/name/sf/mask.h) \
    $(wildcard include/config/name/sf/bit.h) \
    $(wildcard include/config/name/sf/value.h) \
    $(wildcard include/config/id/lsb.h) \
    $(wildcard include/config/id/msb.h) \
    $(wildcard include/config/id/mask.h) \
    $(wildcard include/config/id/bit.h) \
    $(wildcard include/config/id/value.h) \
    $(wildcard include/config/play/tone/lsb.h) \
    $(wildcard include/config/play/tone/msb.h) \
    $(wildcard include/config/play/tone/mask.h) \
    $(wildcard include/config/play/tone/bit.h) \
    $(wildcard include/config/play/tone/value.h) \
    $(wildcard include/config/enable/lsb.h) \
    $(wildcard include/config/enable/msb.h) \
    $(wildcard include/config/enable/bit.h) \
    $(wildcard include/config/min/svrlist/period/reconfig/lsb.h) \
    $(wildcard include/config/lsb.h) \
    $(wildcard include/config/min/svrlist/period/reconfig/msb.h) \
    $(wildcard include/config/msb.h) \
    $(wildcard include/config/min/svrlist/period/reconfig/mask.h) \
    $(wildcard include/config/min/svrlist/period/reconfig/value.h) \
    $(wildcard include/config/pattern/lsb.h) \
    $(wildcard include/config/pattern/msb.h) \
    $(wildcard include/config/pattern/mask.h) \
    $(wildcard include/config/pattern/value.h) \
  emodules/drv_dab/dab_module/si463x/RDS_Handler.h \
  emodules/drv_dab/dab_module/si463x/rds.h \
  emodules/drv_dab/dab_module/si463x/DAB_Service_List_Handler.h \
  emodules/drv_dab/dab_module/si463x/HAL.h \

emodules/drv_dab/dab_module/si463x/FMHD_Service_List_Handler.o: $(deps_emodules/drv_dab/dab_module/si463x/FMHD_Service_List_Handler.o)

$(deps_emodules/drv_dab/dab_module/si463x/FMHD_Service_List_Handler.o):
