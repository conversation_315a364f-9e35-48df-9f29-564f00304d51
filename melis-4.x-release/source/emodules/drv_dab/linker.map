Archive member included to satisfy reference by file (symbol)

./elibrary/bin//libsyscall.a(syscall.o)
                              emodules/drv_dab/dab_module/si463x/DAB_DLS_Handler.o (esCHS_Char2Uni)
./elibrary/bin//libsyscall.a(syscall_fops.o)
                              emodules/drv_dab/drv_dab.o (ioctl)
./elibrary/bin//libminic.a(elibs_sprintf.o)
                              emodules/drv_dab/dab_module/si463x/Firmware_API_Manager.o (eLIBs_sprintf)
./elibrary/bin//libminic.a(elibs_stdio.o)
                              emodules/drv_dab/dab_module/si463x/cmmbSpi.o (eLIBs_fopen)
./elibrary/bin//libminic.a(elibs_string.o)
                              emodules/drv_dab/dab_module/si463x/si468x.o (eLIBs_strlen)
/home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/../../../../riscv64-unknown-elf/lib/rv64imafdcxtheadc/lp64d/libc.a(lib_a-memcpy.o)
                              emodules/drv_dab/dab_module/si463x/DAB_Service_List_Handler.o (memcpy)
/home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/../../../../riscv64-unknown-elf/lib/rv64imafdcxtheadc/lp64d/libc.a(lib_a-memset.o)
                              emodules/drv_dab/dab_module/si463x/DAB_DLS_Handler.o (memset)
/home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/../../../../riscv64-unknown-elf/lib/rv64imafdcxtheadc/lp64d/libc.a(lib_a-sysclose.o)
                              emodules/drv_dab/drv_dab.o (close)
/home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/../../../../riscv64-unknown-elf/lib/rv64imafdcxtheadc/lp64d/libc.a(lib_a-sysopen.o)
                              emodules/drv_dab/drv_dab.o (open)
/home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/../../../../riscv64-unknown-elf/lib/rv64imafdcxtheadc/lp64d/libc.a(lib_a-impure.o)
                              /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/../../../../riscv64-unknown-elf/lib/rv64imafdcxtheadc/lp64d/libc.a(lib_a-sysclose.o) (_impure_ptr)

Discarded input sections

 .text          0x0000000000000000        0x0 emodules/drv_dab/dev_dab.o
 .data          0x0000000000000000        0x0 emodules/drv_dab/dev_dab.o
 .bss           0x0000000000000000        0x0 emodules/drv_dab/dev_dab.o
 .text          0x0000000000000000        0x0 emodules/drv_dab/drv_dab.o
 .data          0x0000000000000000        0x0 emodules/drv_dab/drv_dab.o
 .bss           0x0000000000000000        0x0 emodules/drv_dab/drv_dab.o
 .text          0x0000000000000000        0x0 emodules/drv_dab/dab_module/si463x/cmmbSpi.o
 .data          0x0000000000000000        0x0 emodules/drv_dab/dab_module/si463x/cmmbSpi.o
 .bss           0x0000000000000000        0x0 emodules/drv_dab/dab_module/si463x/cmmbSpi.o
 .text.spi_write_lock
                0x0000000000000000        0xc emodules/drv_dab/dab_module/si463x/cmmbSpi.o
 .text.spi_write_unlock
                0x0000000000000000        0xa emodules/drv_dab/dab_module/si463x/cmmbSpi.o
 .text.spi_init
                0x0000000000000000       0x6a emodules/drv_dab/dab_module/si463x/cmmbSpi.o
 .text.spi_exit
                0x0000000000000000       0x4a emodules/drv_dab/dab_module/si463x/cmmbSpi.o
 .text.spi_read_write
                0x0000000000000000       0xac emodules/drv_dab/dab_module/si463x/cmmbSpi.o
 .text.spi_read_write_together
                0x0000000000000000       0x90 emodules/drv_dab/dab_module/si463x/cmmbSpi.o
 .text.spi_read
                0x0000000000000000       0x4c emodules/drv_dab/dab_module/si463x/cmmbSpi.o
 .text.spi_write
                0x0000000000000000       0x52 emodules/drv_dab/dab_module/si463x/cmmbSpi.o
 .text.SpiTsInit
                0x0000000000000000       0x78 emodules/drv_dab/dab_module/si463x/cmmbSpi.o
 .text.SpiTsDeInit
                0x0000000000000000       0x50 emodules/drv_dab/dab_module/si463x/cmmbSpi.o
 .text.SpiTsRead
                0x0000000000000000       0x7e emodules/drv_dab/dab_module/si463x/cmmbSpi.o
 .text.SpiRead  0x0000000000000000        0x4 emodules/drv_dab/dab_module/si463x/cmmbSpi.o
 .text.SpiWrite
                0x0000000000000000       0x42 emodules/drv_dab/dab_module/si463x/cmmbSpi.o
 .text.SpiTsWrite
                0x0000000000000000       0x86 emodules/drv_dab/dab_module/si463x/cmmbSpi.o
 .bss.g_SpiRdBuf
                0x0000000000000000        0x8 emodules/drv_dab/dab_module/si463x/cmmbSpi.o
 .bss.g_SpiWtBuf
                0x0000000000000000        0x8 emodules/drv_dab/dab_module/si463x/cmmbSpi.o
 .bss.g_spiFile
                0x0000000000000000        0x8 emodules/drv_dab/dab_module/si463x/cmmbSpi.o
 .bss.g_spiMutex
                0x0000000000000000        0x8 emodules/drv_dab/dab_module/si463x/cmmbSpi.o
 .bss.g_write_lock
                0x0000000000000000        0x4 emodules/drv_dab/dab_module/si463x/cmmbSpi.o
 .rodata.spi_init.str1.8
                0x0000000000000000       0x14 emodules/drv_dab/dab_module/si463x/cmmbSpi.o
 .debug_info    0x0000000000000000     0x242a emodules/drv_dab/dab_module/si463x/cmmbSpi.o
 .debug_abbrev  0x0000000000000000      0x40e emodules/drv_dab/dab_module/si463x/cmmbSpi.o
 .debug_loc     0x0000000000000000      0x919 emodules/drv_dab/dab_module/si463x/cmmbSpi.o
 .debug_aranges
                0x0000000000000000      0x100 emodules/drv_dab/dab_module/si463x/cmmbSpi.o
 .debug_line    0x0000000000000000     0x14b4 emodules/drv_dab/dab_module/si463x/cmmbSpi.o
 .debug_str     0x0000000000000000     0x1a97 emodules/drv_dab/dab_module/si463x/cmmbSpi.o
 .comment       0x0000000000000000       0x33 emodules/drv_dab/dab_module/si463x/cmmbSpi.o
 .debug_frame   0x0000000000000000      0x278 emodules/drv_dab/dab_module/si463x/cmmbSpi.o
 .riscv.attributes
                0x0000000000000000       0x3d emodules/drv_dab/dab_module/si463x/cmmbSpi.o
 .text          0x0000000000000000        0x0 emodules/drv_dab/dab_module/si463x/common_types.o
 .data          0x0000000000000000        0x0 emodules/drv_dab/dab_module/si463x/common_types.o
 .bss           0x0000000000000000        0x0 emodules/drv_dab/dab_module/si463x/common_types.o
 .debug_info    0x0000000000000000     0x192b emodules/drv_dab/dab_module/si463x/common_types.o
 .debug_abbrev  0x0000000000000000      0x203 emodules/drv_dab/dab_module/si463x/common_types.o
 .debug_aranges
                0x0000000000000000       0x20 emodules/drv_dab/dab_module/si463x/common_types.o
 .debug_line    0x0000000000000000      0x4d1 emodules/drv_dab/dab_module/si463x/common_types.o
 .debug_str     0x0000000000000000     0x102c emodules/drv_dab/dab_module/si463x/common_types.o
 .comment       0x0000000000000000       0x33 emodules/drv_dab/dab_module/si463x/common_types.o
 .riscv.attributes
                0x0000000000000000       0x3d emodules/drv_dab/dab_module/si463x/common_types.o
 .text          0x0000000000000000        0x0 emodules/drv_dab/dab_module/si463x/DAB_DLS_Handler.o
 .data          0x0000000000000000        0x0 emodules/drv_dab/dab_module/si463x/DAB_DLS_Handler.o
 .bss           0x0000000000000000        0x0 emodules/drv_dab/dab_module/si463x/DAB_DLS_Handler.o
 .text          0x0000000000000000        0x0 emodules/drv_dab/dab_module/si463x/DAB_Service_List_Handler.o
 .data          0x0000000000000000        0x0 emodules/drv_dab/dab_module/si463x/DAB_Service_List_Handler.o
 .bss           0x0000000000000000        0x0 emodules/drv_dab/dab_module/si463x/DAB_Service_List_Handler.o
 .text.DeleteServiceListElement
                0x0000000000000000      0x156 emodules/drv_dab/dab_module/si463x/DAB_Service_List_Handler.o
 .text.findServiceInListExAndEid
                0x0000000000000000       0x8c emodules/drv_dab/dab_module/si463x/DAB_Service_List_Handler.o
 .text.findEnsembleInList
                0x0000000000000000       0x5c emodules/drv_dab/dab_module/si463x/DAB_Service_List_Handler.o
 .text.findListFocusInList
                0x0000000000000000       0x94 emodules/drv_dab/dab_module/si463x/DAB_Service_List_Handler.o
 .text._insertion_sequence_arrange
                0x0000000000000000       0x8a emodules/drv_dab/dab_module/si463x/DAB_Service_List_Handler.o
 .text._repack_stored_dab_list
                0x0000000000000000      0x1e0 emodules/drv_dab/dab_module/si463x/DAB_Service_List_Handler.o
 .text._error_in_list
                0x0000000000000000       0x14 emodules/drv_dab/dab_module/si463x/DAB_Service_List_Handler.o
 .text          0x0000000000000000        0x0 emodules/drv_dab/dab_module/si463x/Firmware_API_Manager.o
 .data          0x0000000000000000        0x0 emodules/drv_dab/dab_module/si463x/Firmware_API_Manager.o
 .bss           0x0000000000000000        0x0 emodules/drv_dab/dab_module/si463x/Firmware_API_Manager.o
 .text.updateIdentifyByServiceIndex
                0x0000000000000000       0x44 emodules/drv_dab/dab_module/si463x/Firmware_API_Manager.o
 .text.fm_configure_properties_rds_on_off
                0x0000000000000000       0x5e emodules/drv_dab/dab_module/si463x/Firmware_API_Manager.o
 .text._si46xx_powerdown
                0x0000000000000000        0x2 emodules/drv_dab/dab_module/si463x/Firmware_API_Manager.o
 .text._return_service_index_from_service_name
                0x0000000000000000       0x6c emodules/drv_dab/dab_module/si463x/Firmware_API_Manager.o
 .text._update_DAB_DAB_service_linking_worker
                0x0000000000000000      0x166 emodules/drv_dab/dab_module/si463x/Firmware_API_Manager.o
 .text.check_if_have_linkage_set_info
                0x0000000000000000       0x2a emodules/drv_dab/dab_module/si463x/Firmware_API_Manager.o
 .text.Finalize
                0x0000000000000000       0x1c emodules/drv_dab/dab_module/si463x/Firmware_API_Manager.o
 .text.SetBandLimits
                0x0000000000000000       0xbe emodules/drv_dab/dab_module/si463x/Firmware_API_Manager.o
 .text.SetFrequencyList_DAB
                0x0000000000000000        0x8 emodules/drv_dab/dab_module/si463x/Firmware_API_Manager.o
 .text.GetFrequencyList_DAB
                0x0000000000000000       0x82 emodules/drv_dab/dab_module/si463x/Firmware_API_Manager.o
 .text.ScanBandCancel_DAB
                0x0000000000000000        0xa emodules/drv_dab/dab_module/si463x/Firmware_API_Manager.o
 .text.GetCurrentServiceIdentification_DAB
                0x0000000000000000       0x28 emodules/drv_dab/dab_module/si463x/Firmware_API_Manager.o
 .text.get_cur_freq_from_serviceIndex
                0x0000000000000000       0x54 emodules/drv_dab/dab_module/si463x/Firmware_API_Manager.o
 .text.FavoritesSetCurrentService_DAB
                0x0000000000000000       0x1a emodules/drv_dab/dab_module/si463x/Firmware_API_Manager.o
 .text.FavoritesRemoveCurrentService_DAB
                0x0000000000000000       0x18 emodules/drv_dab/dab_module/si463x/Firmware_API_Manager.o
 .text.FavoritesBrowseOnly_DAB
                0x0000000000000000        0xa emodules/drv_dab/dab_module/si463x/Firmware_API_Manager.o
 .text.FavoritesIsCurrentServiceAFavorite_DAB
                0x0000000000000000       0x1e emodules/drv_dab/dab_module/si463x/Firmware_API_Manager.o
 .text.SaveCurrentPlayServiceIndex_DAB
                0x0000000000000000        0x4 emodules/drv_dab/dab_module/si463x/Firmware_API_Manager.o
 .text.EraseServiceList_DAB
                0x0000000000000000        0x2 emodules/drv_dab/dab_module/si463x/Firmware_API_Manager.o
 .text.UCS2_To_EBU
                0x0000000000000000       0x64 emodules/drv_dab/dab_module/si463x/Firmware_API_Manager.o
 .text.UTF8_To_EBU
                0x0000000000000000      0x172 emodules/drv_dab/dab_module/si463x/Firmware_API_Manager.o
 .text.GetCurrentBrowseServiceString_DAB
                0x0000000000000000       0x1a emodules/drv_dab/dab_module/si463x/Firmware_API_Manager.o
 .text.decode_ensemble_str
                0x0000000000000000       0x7e emodules/drv_dab/dab_module/si463x/Firmware_API_Manager.o
 .text.ForceMono_FM
                0x0000000000000000       0x92 emodules/drv_dab/dab_module/si463x/Firmware_API_Manager.o
 .text.Get_part
                0x0000000000000000       0x44 emodules/drv_dab/dab_module/si463x/Firmware_API_Manager.o
 .text._step_to_next_service_hd
                0x0000000000000000       0xca emodules/drv_dab/dab_module/si463x/Firmware_API_Manager.o
 .text._FM_confirm_seek_complete
                0x0000000000000000       0x14 emodules/drv_dab/dab_module/si463x/Firmware_API_Manager.o
 .text._FMHD_confirm_seek_complete
                0x0000000000000000       0x14 emodules/drv_dab/dab_module/si463x/Firmware_API_Manager.o
 .text._AM_confirm_seek_complete
                0x0000000000000000       0x18 emodules/drv_dab/dab_module/si463x/Firmware_API_Manager.o
 .text.SeekStart
                0x0000000000000000      0x2e2 emodules/drv_dab/dab_module/si463x/Firmware_API_Manager.o
 .text.ManualTune_DAB
                0x0000000000000000       0x24 emodules/drv_dab/dab_module/si463x/Firmware_API_Manager.o
 .text.TuneStep
                0x0000000000000000      0x164 emodules/drv_dab/dab_module/si463x/Firmware_API_Manager.o
 .text.SetStepSize
                0x0000000000000000      0x19c emodules/drv_dab/dab_module/si463x/Firmware_API_Manager.o
 .text.SetProperty
                0x0000000000000000       0x22 emodules/drv_dab/dab_module/si463x/Firmware_API_Manager.o
 .text.GetProperty
                0x0000000000000000       0x2a emodules/drv_dab/dab_module/si463x/Firmware_API_Manager.o
 .text.StartFirmwareUpdate
                0x0000000000000000       0x5a emodules/drv_dab/dab_module/si463x/Firmware_API_Manager.o
 .text.set_lookat_dab_announcement
                0x0000000000000000        0xa emodules/drv_dab/dab_module/si463x/Firmware_API_Manager.o
 .text.UpdateRadioDNS_FMRDS
                0x0000000000000000       0x70 emodules/drv_dab/dab_module/si463x/Firmware_API_Manager.o
 .text.getLabelAbbrevMaskByServiceIndex
                0x0000000000000000       0x5a emodules/drv_dab/dab_module/si463x/Firmware_API_Manager.o
 .text.UpdateRadioDNS_DAB
                0x0000000000000000       0xe6 emodules/drv_dab/dab_module/si463x/Firmware_API_Manager.o
 .text.Test_DeterminePeakANTCAP_DAB
                0x0000000000000000       0xd8 emodules/drv_dab/dab_module/si463x/Firmware_API_Manager.o
 .text.Test_GetBER_DAB
                0x0000000000000000       0xb8 emodules/drv_dab/dab_module/si463x/Firmware_API_Manager.o
 .text.Test_GetBER_FMHD
                0x0000000000000000       0xd4 emodules/drv_dab/dab_module/si463x/Firmware_API_Manager.o
 .text.Test_StartFreeRunningBER_FMHD
                0x0000000000000000       0x36 emodules/drv_dab/dab_module/si463x/Firmware_API_Manager.o
 .text.Test_GetFreeRunningBER_FMHD
                0x0000000000000000       0x88 emodules/drv_dab/dab_module/si463x/Firmware_API_Manager.o
 .rodata.Test_GetFreeRunningBER_FMHD
                0x0000000000000000       0x14 emodules/drv_dab/dab_module/si463x/Firmware_API_Manager.o
 .text.FMHDServiceListFastPtr
                0x0000000000000000        0xa emodules/drv_dab/dab_module/si463x/Firmware_API_Manager.o
 .text.FMHDServiceListAudioPtr
                0x0000000000000000        0xa emodules/drv_dab/dab_module/si463x/Firmware_API_Manager.o
 .text.SISGetSloganPtr
                0x0000000000000000        0x4 emodules/drv_dab/dab_module/si463x/Firmware_API_Manager.o
 .text.SISGetUSNPtr
                0x0000000000000000        0x4 emodules/drv_dab/dab_module/si463x/Firmware_API_Manager.o
 .text.SISGetSMPtr
                0x0000000000000000        0x4 emodules/drv_dab/dab_module/si463x/Firmware_API_Manager.o
 .text.SISGetSNLPtr
                0x0000000000000000        0x4 emodules/drv_dab/dab_module/si463x/Firmware_API_Manager.o
 .text.SISGetSNSPtr
                0x0000000000000000        0x4 emodules/drv_dab/dab_module/si463x/Firmware_API_Manager.o
 .text.SISGetStationIDPtr
                0x0000000000000000        0x4 emodules/drv_dab/dab_module/si463x/Firmware_API_Manager.o
 .text.SISGetStationLocLatPtr
                0x0000000000000000        0x4 emodules/drv_dab/dab_module/si463x/Firmware_API_Manager.o
 .text.SISGetStationLocLongPtr
                0x0000000000000000        0x4 emodules/drv_dab/dab_module/si463x/Firmware_API_Manager.o
 .text.PSDGetTitlePtr
                0x0000000000000000        0x4 emodules/drv_dab/dab_module/si463x/Firmware_API_Manager.o
 .text.PSDGetArtistPtr
                0x0000000000000000        0x4 emodules/drv_dab/dab_module/si463x/Firmware_API_Manager.o
 .text.PSDGetAlbumPtr
                0x0000000000000000        0x4 emodules/drv_dab/dab_module/si463x/Firmware_API_Manager.o
 .text.PSDGetGenrePtr
                0x0000000000000000        0x4 emodules/drv_dab/dab_module/si463x/Firmware_API_Manager.o
 .text.HDAlertGetPtr
                0x0000000000000000        0x4 emodules/drv_dab/dab_module/si463x/Firmware_API_Manager.o
 .text.DABServiceListAudioPtr
                0x0000000000000000        0xa emodules/drv_dab/dab_module/si463x/Firmware_API_Manager.o
 .bss._current_fm_blend_rssi_limits
                0x0000000000000000        0x2 emodules/drv_dab/dab_module/si463x/Firmware_API_Manager.o
 .text          0x0000000000000000        0x0 emodules/drv_dab/dab_module/si463x/Firmware_Load_Helpers.o
 .data          0x0000000000000000        0x0 emodules/drv_dab/dab_module/si463x/Firmware_Load_Helpers.o
 .bss           0x0000000000000000        0x0 emodules/drv_dab/dab_module/si463x/Firmware_Load_Helpers.o
 .bss.acamhd_radio_2_0_11
                0x0000000000000000        0x8 emodules/drv_dab/dab_module/si463x/Firmware_Load_Helpers.o
 .bss.acdab_radio_3_2_12
                0x0000000000000000        0x8 emodules/drv_dab/dab_module/si463x/Firmware_Load_Helpers.o
 .bss.acfmhd_radio_5_0_4
                0x0000000000000000        0x8 emodules/drv_dab/dab_module/si463x/Firmware_Load_Helpers.o
 .bss.firmware_len
                0x0000000000000000        0x4 emodules/drv_dab/dab_module/si463x/Firmware_Load_Helpers.o
 .bss.patch_full_buff
                0x0000000000000000        0x8 emodules/drv_dab/dab_module/si463x/Firmware_Load_Helpers.o
 .text          0x0000000000000000        0x0 emodules/drv_dab/dab_module/si463x/FMHD_PSD_Handler.o
 .data          0x0000000000000000        0x0 emodules/drv_dab/dab_module/si463x/FMHD_PSD_Handler.o
 .bss           0x0000000000000000        0x0 emodules/drv_dab/dab_module/si463x/FMHD_PSD_Handler.o
 .text          0x0000000000000000        0x0 emodules/drv_dab/dab_module/si463x/FMHD_Service_List_Handler.o
 .data          0x0000000000000000        0x0 emodules/drv_dab/dab_module/si463x/FMHD_Service_List_Handler.o
 .bss           0x0000000000000000        0x0 emodules/drv_dab/dab_module/si463x/FMHD_Service_List_Handler.o
 .text          0x0000000000000000        0x0 emodules/drv_dab/dab_module/si463x/FMHD_SIS_Handler.o
 .data          0x0000000000000000        0x0 emodules/drv_dab/dab_module/si463x/FMHD_SIS_Handler.o
 .bss           0x0000000000000000        0x0 emodules/drv_dab/dab_module/si463x/FMHD_SIS_Handler.o
 .text          0x0000000000000000        0x0 emodules/drv_dab/dab_module/si463x/FMHD_Alert_Handler.o
 .data          0x0000000000000000        0x0 emodules/drv_dab/dab_module/si463x/FMHD_Alert_Handler.o
 .bss           0x0000000000000000        0x0 emodules/drv_dab/dab_module/si463x/FMHD_Alert_Handler.o
 .text          0x0000000000000000        0x0 emodules/drv_dab/dab_module/si463x/freq_stack.o
 .data          0x0000000000000000        0x0 emodules/drv_dab/dab_module/si463x/freq_stack.o
 .bss           0x0000000000000000        0x0 emodules/drv_dab/dab_module/si463x/freq_stack.o
 .text.freqStack_init
                0x0000000000000000       0x2c emodules/drv_dab/dab_module/si463x/freq_stack.o
 .text.Push_freqStack
                0x0000000000000000       0x4e emodules/drv_dab/dab_module/si463x/freq_stack.o
 .text.Out_freqStack
                0x0000000000000000        0x6 emodules/drv_dab/dab_module/si463x/freq_stack.o
 .text.freqStack_Empty
                0x0000000000000000        0xe emodules/drv_dab/dab_module/si463x/freq_stack.o
 .text.pop_freqStack
                0x0000000000000000       0x1e emodules/drv_dab/dab_module/si463x/freq_stack.o
 .text.delete_freqStack
                0x0000000000000000       0x24 emodules/drv_dab/dab_module/si463x/freq_stack.o
 .data.Length   0x0000000000000000        0x4 emodules/drv_dab/dab_module/si463x/freq_stack.o
 .debug_info    0x0000000000000000     0x1ec3 emodules/drv_dab/dab_module/si463x/freq_stack.o
 .debug_abbrev  0x0000000000000000      0x303 emodules/drv_dab/dab_module/si463x/freq_stack.o
 .debug_loc     0x0000000000000000      0x23c emodules/drv_dab/dab_module/si463x/freq_stack.o
 .debug_aranges
                0x0000000000000000       0x80 emodules/drv_dab/dab_module/si463x/freq_stack.o
 .debug_line    0x0000000000000000      0xa3e emodules/drv_dab/dab_module/si463x/freq_stack.o
 .debug_str     0x0000000000000000     0x182b emodules/drv_dab/dab_module/si463x/freq_stack.o
 .comment       0x0000000000000000       0x33 emodules/drv_dab/dab_module/si463x/freq_stack.o
 .debug_frame   0x0000000000000000       0xe0 emodules/drv_dab/dab_module/si463x/freq_stack.o
 .riscv.attributes
                0x0000000000000000       0x3d emodules/drv_dab/dab_module/si463x/freq_stack.o
 .text          0x0000000000000000        0x0 emodules/drv_dab/dab_module/si463x/HAL.o
 .data          0x0000000000000000        0x0 emodules/drv_dab/dab_module/si463x/HAL.o
 .bss           0x0000000000000000        0x0 emodules/drv_dab/dab_module/si463x/HAL.o
 .text.audioEnable
                0x0000000000000000        0x4 emodules/drv_dab/dab_module/si463x/HAL.o
 .text.audioDisable
                0x0000000000000000        0x4 emodules/drv_dab/dab_module/si463x/HAL.o
 .text.powerDownHardware
                0x0000000000000000        0x4 emodules/drv_dab/dab_module/si463x/HAL.o
 .text          0x0000000000000000        0x0 emodules/drv_dab/dab_module/si463x/Hardware_Calibration_Helpers.o
 .data          0x0000000000000000        0x0 emodules/drv_dab/dab_module/si463x/Hardware_Calibration_Helpers.o
 .bss           0x0000000000000000        0x0 emodules/drv_dab/dab_module/si463x/Hardware_Calibration_Helpers.o
 .text          0x0000000000000000        0x0 emodules/drv_dab/dab_module/si463x/rds.o
 .data          0x0000000000000000        0x0 emodules/drv_dab/dab_module/si463x/rds.o
 .bss           0x0000000000000000        0x0 emodules/drv_dab/dab_module/si463x/rds.o
 .text.T_ReplaceFreq
                0x0000000000000000        0x2 emodules/drv_dab/dab_module/si463x/rds.o
 .text.T_Network_get_af
                0x0000000000000000       0x2a emodules/drv_dab/dab_module/si463x/rds.o
 .text          0x0000000000000000        0x0 emodules/drv_dab/dab_module/si463x/RDS_Handler.o
 .data          0x0000000000000000        0x0 emodules/drv_dab/dab_module/si463x/RDS_Handler.o
 .bss           0x0000000000000000        0x0 emodules/drv_dab/dab_module/si463x/RDS_Handler.o
 .text.ignoreAB
                0x0000000000000000        0xa emodules/drv_dab/dab_module/si463x/RDS_Handler.o
 .text          0x0000000000000000        0x0 emodules/drv_dab/dab_module/si463x/si46xx_firmware_api.o
 .data          0x0000000000000000        0x0 emodules/drv_dab/dab_module/si463x/si46xx_firmware_api.o
 .bss           0x0000000000000000        0x0 emodules/drv_dab/dab_module/si463x/si46xx_firmware_api.o
 .text.printf_cmdArg
                0x0000000000000000        0x2 emodules/drv_dab/dab_module/si463x/si46xx_firmware_api.o
 .text.printf_freq
                0x0000000000000000        0x2 emodules/drv_dab/dab_module/si463x/si46xx_firmware_api.o
 .text.get_power_up_args__command
                0x0000000000000000       0x46 emodules/drv_dab/dab_module/si463x/si46xx_firmware_api.o
 .text.get_power_up_args__reply
                0x0000000000000000       0x8c emodules/drv_dab/dab_module/si463x/si46xx_firmware_api.o
 .text.write_storage__command
                0x0000000000000000       0x82 emodules/drv_dab/dab_module/si463x/si46xx_firmware_api.o
 .text.read_storage__command
                0x0000000000000000       0x5a emodules/drv_dab/dab_module/si463x/si46xx_firmware_api.o
 .text.read_storage__reply
                0x0000000000000000       0x86 emodules/drv_dab/dab_module/si463x/si46xx_firmware_api.o
 .text.test_get_rssi__command
                0x0000000000000000       0x46 emodules/drv_dab/dab_module/si463x/si46xx_firmware_api.o
 .text.test_get_rssi__reply
                0x0000000000000000       0x50 emodules/drv_dab/dab_module/si463x/si46xx_firmware_api.o
 .text.fm_am_seek_start__command
                0x0000000000000000       0xe6 emodules/drv_dab/dab_module/si463x/si46xx_firmware_api.o
 .text.fm_acf_status__command
                0x0000000000000000       0x5e emodules/drv_dab/dab_module/si463x/si46xx_firmware_api.o
 .text.fm_acf_status__reply
                0x0000000000000000       0xe4 emodules/drv_dab/dab_module/si463x/si46xx_firmware_api.o
 .text.fm_rds_blockcount__command
                0x0000000000000000       0x5e emodules/drv_dab/dab_module/si463x/si46xx_firmware_api.o
 .text.fm_rds_blockcount__reply
                0x0000000000000000       0x62 emodules/drv_dab/dab_module/si463x/si46xx_firmware_api.o
 .text.fmhd_test_get_ber_info__command
                0x0000000000000000       0x48 emodules/drv_dab/dab_module/si463x/si46xx_firmware_api.o
 .text.fmhd_test_get_ber_info__reply
                0x0000000000000000       0x6a emodules/drv_dab/dab_module/si463x/si46xx_firmware_api.o
 .text.fmhd_set_enabled_ports__command
                0x0000000000000000       0x72 emodules/drv_dab/dab_module/si463x/si46xx_firmware_api.o
 .text.fmhd_get_enabled_ports__command
                0x0000000000000000       0x48 emodules/drv_dab/dab_module/si463x/si46xx_firmware_api.o
 .text.fmhd_get_enabled_ports__reply
                0x0000000000000000       0x70 emodules/drv_dab/dab_module/si463x/si46xx_firmware_api.o
 .text.dab_set_freq_list__command
                0x0000000000000000       0x72 emodules/drv_dab/dab_module/si463x/si46xx_firmware_api.o
 .text.dab_get_service_info__command
                0x0000000000000000       0x56 emodules/drv_dab/dab_module/si463x/si46xx_firmware_api.o
 .text.dab_get_service_info__reply
                0x0000000000000000       0xc4 emodules/drv_dab/dab_module/si463x/si46xx_firmware_api.o
 .text.dab_acf_status__command
                0x0000000000000000       0x48 emodules/drv_dab/dab_module/si463x/si46xx_firmware_api.o
 .text.dab_acf_status__reply
                0x0000000000000000       0x54 emodules/drv_dab/dab_module/si463x/si46xx_firmware_api.o
 .text.dab_test_ber_info__command
                0x0000000000000000       0x46 emodules/drv_dab/dab_module/si463x/si46xx_firmware_api.o
 .text.dab_test_ber_info__reply
                0x0000000000000000       0x4c emodules/drv_dab/dab_module/si463x/si46xx_firmware_api.o
 .data.current_mode
                0x0000000000000000        0x1 emodules/drv_dab/dab_module/si463x/si46xx_firmware_api.o
 .data.reply_buffer_i16
                0x0000000000000000        0x8 emodules/drv_dab/dab_module/si463x/si46xx_firmware_api.o
 .text          0x0000000000000000        0x0 emodules/drv_dab/dab_module/si463x/si463x_PresetListCreat.o
 .data          0x0000000000000000        0x0 emodules/drv_dab/dab_module/si463x/si463x_PresetListCreat.o
 .bss           0x0000000000000000        0x0 emodules/drv_dab/dab_module/si463x/si463x_PresetListCreat.o
 .debug_line    0x0000000000000000        0x0 emodules/drv_dab/dab_module/si463x/si463x_PresetListCreat.o
 .debug_str     0x0000000000000000      0x28b emodules/drv_dab/dab_module/si463x/si463x_PresetListCreat.o
 .comment       0x0000000000000000       0x33 emodules/drv_dab/dab_module/si463x/si463x_PresetListCreat.o
 .riscv.attributes
                0x0000000000000000       0x3d emodules/drv_dab/dab_module/si463x/si463x_PresetListCreat.o
 .text          0x0000000000000000        0x0 emodules/drv_dab/dab_module/si463x/si468x.o
 .data          0x0000000000000000        0x0 emodules/drv_dab/dab_module/si463x/si468x.o
 .bss           0x0000000000000000        0x0 emodules/drv_dab/dab_module/si463x/si468x.o
 .text.get_dab_lock
                0x0000000000000000        0xc emodules/drv_dab/dab_module/si463x/si468x.o
 .text.get_on_dab_view_app_flg
                0x0000000000000000        0xa emodules/drv_dab/dab_module/si463x/si468x.o
 .text.get_dab_fm_rds_hard_link_flag
                0x0000000000000000        0xa emodules/drv_dab/dab_module/si463x/si468x.o
 .text.dab_save_deinit_para
                0x0000000000000000        0xc emodules/drv_dab/dab_module/si463x/si468x.o
 .text.volume_work_mode
                0x0000000000000000       0x2e emodules/drv_dab/dab_module/si463x/si468x.o
 .text.GetCurrentDabInformation
                0x0000000000000000      0x11e emodules/drv_dab/dab_module/si463x/si468x.o
 .text.si4634_dab_check_current_service_match
                0x0000000000000000       0x82 emodules/drv_dab/dab_module/si463x/si468x.o
 .text.update_service_data
                0x0000000000000000       0x1e emodules/drv_dab/dab_module/si463x/si468x.o
 .text.__tryTuneToFreq__
                0x0000000000000000       0x96 emodules/drv_dab/dab_module/si463x/si468x.o
 .text.dab_seek_break
                0x0000000000000000       0x54 emodules/drv_dab/dab_module/si463x/si468x.o
 .text.si4634Preset
                0x0000000000000000      0x282 emodules/drv_dab/dab_module/si463x/si468x.o
 .text.si4634_dab_reset
                0x0000000000000000       0xb6 emodules/drv_dab/dab_module/si463x/si468x.o
 .text.si463x_set_rds_on_off
                0x0000000000000000       0x4e emodules/drv_dab/dab_module/si463x/si468x.o
 .text.si463x_set_rds_af_on_off
                0x0000000000000000       0x26 emodules/drv_dab/dab_module/si463x/si468x.o
 .text.si463x_set_rds_ta_on_off
                0x0000000000000000       0x48 emodules/drv_dab/dab_module/si463x/si468x.o
 .text.si463x_set_rds_ct_on_off
                0x0000000000000000       0x26 emodules/drv_dab/dab_module/si463x/si468x.o
 .text.si463x_set_local_seek_on_off
                0x0000000000000000       0x58 emodules/drv_dab/dab_module/si463x/si468x.o
 .text.si4634_set_search_stop
                0x0000000000000000       0x58 emodules/drv_dab/dab_module/si463x/si468x.o
 .text.PtySeekStart
                0x0000000000000000       0x34 emodules/drv_dab/dab_module/si463x/si468x.o
 .text.AutoSeekStart
                0x0000000000000000       0x56 emodules/drv_dab/dab_module/si463x/si468x.o
 .text.k_RadioAutoStore
                0x0000000000000000       0xa8 emodules/drv_dab/dab_module/si463x/si468x.o
 .text.fm_am_store_preset
                0x0000000000000000       0xfc emodules/drv_dab/dab_module/si463x/si468x.o
 .text.fm_am_set_parameter_default
                0x0000000000000000       0x82 emodules/drv_dab/dab_module/si463x/si468x.o
 .text.set_freq_spacing
                0x0000000000000000       0xaa emodules/drv_dab/dab_module/si463x/si468x.o
 .text.Fm_key_pty_Seek
                0x0000000000000000       0x38 emodules/drv_dab/dab_module/si463x/si468x.o
 .text._FM_AM_AUTOSEEK_KEY_
                0x0000000000000000       0x3a emodules/drv_dab/dab_module/si463x/si468x.o
 .text._FM_AM_AUTOSTORE_KEY_
                0x0000000000000000       0x12 emodules/drv_dab/dab_module/si463x/si468x.o
 .text.fm_am_save_data
                0x0000000000000000        0x4 emodules/drv_dab/dab_module/si463x/si468x.o
 .text.fm_am_func_set_are
                0x0000000000000000       0x6a emodules/drv_dab/dab_module/si463x/si468x.o
 .text.T_Meth_Clear
                0x0000000000000000        0xa emodules/drv_dab/dab_module/si463x/si468x.o
 .text.GetDivisor_idx
                0x0000000000000000       0x1c emodules/drv_dab/dab_module/si463x/si468x.o
 .text.fm_am_select_preset
                0x0000000000000000      0x1e2 emodules/drv_dab/dab_module/si463x/si468x.o
 .text._FM_AM_MANUALSEEK_KEY_
                0x0000000000000000      0x19c emodules/drv_dab/dab_module/si463x/si468x.o
 .text.si4634_fm_am_Key_Process
                0x0000000000000000       0x42 emodules/drv_dab/dab_module/si463x/si468x.o
 .rodata.si4634_fm_am_Key_Process
                0x0000000000000000       0x18 emodules/drv_dab/dab_module/si463x/si468x.o
 .text.si4634_am_exit
                0x0000000000000000        0x8 emodules/drv_dab/dab_module/si463x/si468x.o
 .bss.ATS_test_uk_freq_flag
                0x0000000000000000        0x1 emodules/drv_dab/dab_module/si463x/si468x.o
 .bss.RadioTuneFlag
                0x0000000000000000        0x1 emodules/drv_dab/dab_module/si463x/si468x.o
 .bss.T_F_af_ta_seek
                0x0000000000000000        0x1 emodules/drv_dab/dab_module/si463x/si468x.o
 .bss.T_Reg_sw  0x0000000000000000        0x1 emodules/drv_dab/dab_module/si463x/si468x.o
 .bss.T_Tu_last_divisor
                0x0000000000000000        0x2 emodules/drv_dab/dab_module/si463x/si468x.o
 .bss.T_af_count_bk
                0x0000000000000000        0x1 emodules/drv_dab/dab_module/si463x/si468x.o
 .bss.T_af_freq_is_bad
                0x0000000000000000        0x1 emodules/drv_dab/dab_module/si463x/si468x.o
 .bss.T_af_last_fm_freq
                0x0000000000000000        0x2 emodules/drv_dab/dab_module/si463x/si468x.o
 .bss.T_af_list_bk
                0x0000000000000000       0x32 emodules/drv_dab/dab_module/si463x/si468x.o
 .bss.T_af_pi_bk
                0x0000000000000000        0x2 emodules/drv_dab/dab_module/si463x/si468x.o
 .bss.T_af_pi_now
                0x0000000000000000        0x2 emodules/drv_dab/dab_module/si463x/si468x.o
 .bss.af_para   0x0000000000000000       0xd0 emodules/drv_dab/dab_module/si463x/si468x.o
 .bss.am_freq_step_size
                0x0000000000000000        0x1 emodules/drv_dab/dab_module/si463x/si468x.o
 .bss.amhd_freq_step_size
                0x0000000000000000        0x1 emodules/drv_dab/dab_module/si463x/si468x.o
 .bss.ats_encode_test_flag
                0x0000000000000000        0x1 emodules/drv_dab/dab_module/si463x/si468x.o
 .bss.ats_key   0x0000000000000000        0x1 emodules/drv_dab/dab_module/si463x/si468x.o
 .bss.ats_key_flag
                0x0000000000000000        0x1 emodules/drv_dab/dab_module/si463x/si468x.o
 .bss.ats_keypad_test_flag
                0x0000000000000000        0x1 emodules/drv_dab/dab_module/si463x/si468x.o
 .bss.ats_mode_switch_flag
                0x0000000000000000        0x1 emodules/drv_dab/dab_module/si463x/si468x.o
 .bss.ats_test_enable_flag
                0x0000000000000000        0x1 emodules/drv_dab/dab_module/si463x/si468x.o
 .bss.bAbortAutoSeek
                0x0000000000000000        0x1 emodules/drv_dab/dab_module/si463x/si468x.o
 .bss.bAbortAutoStore
                0x0000000000000000        0x1 emodules/drv_dab/dab_module/si463x/si468x.o
 .bss.bFastRecovery
                0x0000000000000000        0x1 emodules/drv_dab/dab_module/si463x/si468x.o
 .bss.bTuneSeek
                0x0000000000000000        0x1 emodules/drv_dab/dab_module/si463x/si468x.o
 .bss.band_size
                0x0000000000000000        0x1 emodules/drv_dab/dab_module/si463x/si468x.o
 .bss.ber_running_flag
                0x0000000000000000        0x1 emodules/drv_dab/dab_module/si463x/si468x.o
 .bss.curBandIndex
                0x0000000000000000        0x1 emodules/drv_dab/dab_module/si463x/si468x.o
 .bss.display_mode
                0x0000000000000000        0x1 emodules/drv_dab/dab_module/si463x/si468x.o
 .bss.fm_freq_step_size
                0x0000000000000000        0x1 emodules/drv_dab/dab_module/si463x/si468x.o
 .bss.fmhd_ber_mode
                0x0000000000000000        0x1 emodules/drv_dab/dab_module/si463x/si468x.o
 .bss.fmhd_freq_step_size
                0x0000000000000000        0x1 emodules/drv_dab/dab_module/si463x/si468x.o
 .bss.fmhd_pset_table
                0x0000000000000000       0xfc emodules/drv_dab/dab_module/si463x/si468x.o
 .bss.fmhd_seekonly
                0x0000000000000000        0x1 emodules/drv_dab/dab_module/si463x/si468x.o
 .bss.fmhd_split_mode_setting
                0x0000000000000000        0x2 emodules/drv_dab/dab_module/si463x/si468x.o
 .bss.pset_table_index
                0x0000000000000000        0x1 emodules/drv_dab/dab_module/si463x/si468x.o
 .bss.work_mode_index
                0x0000000000000000        0x1 emodules/drv_dab/dab_module/si463x/si468x.o
 .data.fmhd_pset_table_default
                0x0000000000000000       0x2a emodules/drv_dab/dab_module/si463x/si468x.o
 .rodata.GetCurrentDabInformation.str1.8
                0x0000000000000000       0x10 emodules/drv_dab/dab_module/si463x/si468x.o
 .text          0x0000000000000000        0x0 emodules/drv_dab/dab_module/si463x/si468x_bus.o
 .data          0x0000000000000000        0x0 emodules/drv_dab/dab_module/si463x/si468x_bus.o
 .bss           0x0000000000000000        0x0 emodules/drv_dab/dab_module/si463x/si468x_bus.o
 .text          0x0000000000000000        0x0 emodules/drv_dab/dab_module/si463x/mot.o
 .data          0x0000000000000000        0x0 emodules/drv_dab/dab_module/si463x/mot.o
 .bss           0x0000000000000000        0x0 emodules/drv_dab/dab_module/si463x/mot.o
 .text.get_mot_sls_image
                0x0000000000000000       0x3c emodules/drv_dab/dab_module/si463x/mot.o
 .text          0x0000000000000000        0x0 emodules/drv_dab/magic.o
 .data          0x0000000000000000        0x0 emodules/drv_dab/magic.o
 .bss           0x0000000000000000        0x0 emodules/drv_dab/magic.o
 .text          0x0000000000000000        0x0 ./elibrary/bin//libsyscall.a(syscall.o)
 .data          0x0000000000000000        0x0 ./elibrary/bin//libsyscall.a(syscall.o)
 .bss           0x0000000000000000        0x0 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esCFG_Exit_Ex
                0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esCFG_GetGPIOSecData
                0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esCFG_GetGPIOSecData_Ex
                0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esCFG_GetGPIOSecKeyCount
                0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esCFG_GetGPIOSecKeyCount_Ex
                0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esCFG_GetKeyValue
                0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esCFG_GetKeyValue_Ex
                0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esCFG_GetSecCount
                0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esCFG_GetSecCount_Ex
                0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esCFG_GetSecKeyCount
                0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esCFG_GetSecKeyCount_Ex
                0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esCFG_Init_Ex
                0x0000000000000000       0x16 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esCHS_GetChLowerTbl
                0x0000000000000000       0x1c ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esCHS_GetChUpperTbl
                0x0000000000000000       0x1c ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esCLK_CloseMclk
                0x0000000000000000       0x1c ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esCLK_GetMclkDiv
                0x0000000000000000       0x1c ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esCLK_GetMclkSrc
                0x0000000000000000       0x1c ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esCLK_GetRound_Rate
                0x0000000000000000       0x20 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esCLK_GetSrcFreq
                0x0000000000000000       0x1c ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esCLK_MclkAssert
                0x0000000000000000       0x1c ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esCLK_MclkDeassert
                0x0000000000000000       0x1c ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esCLK_MclkGetRstStatus
                0x0000000000000000       0x1c ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esCLK_MclkOnOff
                0x0000000000000000       0x20 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esCLK_MclkRegCb
                0x0000000000000000       0x1c ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esCLK_MclkReset
                0x0000000000000000       0x1c ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esCLK_MclkUnregCb
                0x0000000000000000       0x1c ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esCLK_ModInfo
                0x0000000000000000       0x16 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esCLK_OpenMclk
                0x0000000000000000       0x1c ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esCLK_SetMclkDiv
                0x0000000000000000       0x20 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esCLK_SetMclkSrc
                0x0000000000000000       0x20 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esCLK_SetSrcFreq
                0x0000000000000000       0x20 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esCLK_SysInfo
                0x0000000000000000       0x16 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esDEV_Close
                0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esDEV_DevReg
                0x0000000000000000       0x16 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esDEV_DevUnreg
                0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esDEV_Insmod
                0x0000000000000000       0x1c ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esDEV_Ioctl
                0x0000000000000000       0x1c ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esDEV_Lock
                0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esDEV_Open
                0x0000000000000000       0x1a ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esDEV_Plugin
                0x0000000000000000       0x1c ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esDEV_Plugout
                0x0000000000000000       0x1c ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esDEV_Read
                0x0000000000000000       0x20 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esDEV_Unimod
                0x0000000000000000       0x1c ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esDEV_Unlock
                0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esDEV_Write
                0x0000000000000000       0x20 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esDMA_ChangeMode
                0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esDMA_DisableINT
                0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esDMA_EnableINT
                0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esDMA_Information
                0x0000000000000000       0x16 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esDMA_QueryDst
                0x0000000000000000       0x16 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esDMA_QueryRestCount
                0x0000000000000000       0x16 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esDMA_QuerySrc
                0x0000000000000000       0x16 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esDMA_QueryStat
                0x0000000000000000       0x1a ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esDMA_RegDmaHdler
                0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esDMA_Release
                0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esDMA_Request
                0x0000000000000000       0x16 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esDMA_Restart
                0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esDMA_Setting
                0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esDMA_Start
                0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esDMA_Stop
                0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esDMA_UnregDmaHdler
                0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esEXEC_PCreate
                0x0000000000000000       0x1e ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esEXEC_PDel
                0x0000000000000000       0x1a ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esEXEC_PDelReq
                0x0000000000000000       0x1a ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esEXEC_Run
                0x0000000000000000       0x1e ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esFSYS_clearpartupdateflag
                0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esFSYS_closedir
                0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esFSYS_fd2file
                0x0000000000000000       0x16 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esFSYS_ferrclr
                0x0000000000000000       0x16 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esFSYS_ferror
                0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esFSYS_file2fd
                0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esFSYS_fsdbg
                0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esFSYS_fseekex
                0x0000000000000000       0x16 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esFSYS_fsreg
                0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esFSYS_fstat
                0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esFSYS_fsunreg
                0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esFSYS_fsync
                0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esFSYS_ftellex
                0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esFSYS_ftruncate
                0x0000000000000000       0x1c ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esFSYS_getfscharset
                0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esFSYS_mkdir
                0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esFSYS_mntfs
                0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esFSYS_mntparts
                0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esFSYS_open
                0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esFSYS_opendir
                0x0000000000000000       0x16 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esFSYS_partfslck
                0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esFSYS_partfsunlck
                0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esFSYS_pclose
                0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esFSYS_pdreg
                0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esFSYS_pdunreg
                0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esFSYS_perrclr
                0x0000000000000000       0x16 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esFSYS_perror
                0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esFSYS_pioctrl
                0x0000000000000000       0x1c ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esFSYS_popen
                0x0000000000000000       0x16 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esFSYS_pread
                0x0000000000000000       0x20 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esFSYS_premove
                0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esFSYS_prename
                0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esFSYS_pwrite
                0x0000000000000000       0x20 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esFSYS_querypartupdateflag
                0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esFSYS_readdir
                0x0000000000000000       0x16 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esFSYS_remove
                0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esFSYS_rename
                0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esFSYS_rewinddir
                0x0000000000000000       0x16 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esFSYS_rmdir
                0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esFSYS_setfs
                0x0000000000000000       0x1c ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esFSYS_statfs
                0x0000000000000000       0x1c ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esFSYS_statpt
                0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esFSYS_umntfs
                0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esFSYS_umntparts
                0x0000000000000000       0x1c ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esHID_hiddevreg
                0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esHID_hiddevunreg
                0x0000000000000000       0x1c ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esHID_SendMsg
                0x0000000000000000       0x1c ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esINPUT_GetLdevID
                0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esINPUT_LdevCtl
                0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esINPUT_LdevFeedback
                0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esINPUT_LdevGrab
                0x0000000000000000       0x16 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esINPUT_LdevRelease
                0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esINPUT_RegDev
                0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esINPUT_SendEvent
                0x0000000000000000       0x20 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esINPUT_UnregDev
                0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esINT_DisableINT
                0x0000000000000000       0x1c ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esINT_EnableINT
                0x0000000000000000       0x1c ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esINT_InsFIR
                0x0000000000000000       0x1c ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esINT_InsISR
                0x0000000000000000       0x1c ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esINT_SetIRQPrio
                0x0000000000000000       0x20 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esINT_UniFIR
                0x0000000000000000       0x1c ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esINT_UniISR
                0x0000000000000000       0x1c ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esKRNL_CallBack
                0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esKRNL_DumpStack
                0x0000000000000000       0x16 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esKRNL_FlagAccept
                0x0000000000000000       0x1c ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esKRNL_FlagCreate
                0x0000000000000000       0x1a ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esKRNL_FlagDel
                0x0000000000000000       0x16 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esKRNL_FlagNameGet
                0x0000000000000000       0x1a ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esKRNL_FlagNameSet
                0x0000000000000000       0x16 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esKRNL_FlagPend
                0x0000000000000000       0x20 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esKRNL_FlagPendGetFlagsRdy
                0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esKRNL_FlagPost
                0x0000000000000000       0x1c ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esKRNL_FlagQuery
                0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esKRNL_GetCallBack
                0x0000000000000000       0x16 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esKRNL_GetTIDCur
                0x0000000000000000       0x16 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esKRNL_InterruptDisable
                0x0000000000000000       0x16 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esKRNL_InterruptEnable
                0x0000000000000000       0x16 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esKRNL_MboxAccept
                0x0000000000000000       0x16 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esKRNL_MboxCreate
                0x0000000000000000       0x1a ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esKRNL_MboxDel
                0x0000000000000000       0x16 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esKRNL_MboxPend
                0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esKRNL_MboxPost
                0x0000000000000000       0x1a ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esKRNL_MboxPostOpt
                0x0000000000000000       0x1e ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esKRNL_MboxQuery
                0x0000000000000000       0x1a ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esKRNL_MemLeakChk
                0x0000000000000000       0x1a ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esKRNL_MutexCreate
                0x0000000000000000       0x16 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esKRNL_MutexDel
                0x0000000000000000       0x16 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esKRNL_MutexPend
                0x0000000000000000       0x16 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esKRNL_MutexPost
                0x0000000000000000       0x1a ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esKRNL_QAccept
                0x0000000000000000       0x16 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esKRNL_QCreate
                0x0000000000000000       0x16 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esKRNL_QDel
                0x0000000000000000       0x16 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esKRNL_QFlush
                0x0000000000000000       0x1a ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esKRNL_QPend
                0x0000000000000000       0x16 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esKRNL_QPost
                0x0000000000000000       0x1a ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esKRNL_QPostFront
                0x0000000000000000       0x1a ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esKRNL_QPostOpt
                0x0000000000000000       0x1a ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esKRNL_QQuery
                0x0000000000000000       0x1a ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esKRNL_SchedLock
                0x0000000000000000       0x16 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esKRNL_SchedUnlock
                0x0000000000000000       0x16 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esKRNL_SemAccept
                0x0000000000000000       0x1a ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esKRNL_SemQuery
                0x0000000000000000       0x1a ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esKRNL_SemSet
                0x0000000000000000       0x16 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esKRNL_SktAccept
                0x0000000000000000       0x16 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esKRNL_SktCreate
                0x0000000000000000       0x22 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esKRNL_SktDel
                0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esKRNL_SktPend
                0x0000000000000000       0x1a ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esKRNL_SktPost
                0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esKRNL_TaskPrefEn
                0x0000000000000000       0x1a ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esKRNL_TaskQuery
                0x0000000000000000       0x1e ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esKRNL_TaskResume
                0x0000000000000000       0x1e ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esKRNL_TaskNameSet
                0x0000000000000000       0x1a ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esKRNL_TaskSuspend
                0x0000000000000000       0x1e ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esKRNL_Time
                0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esKRNL_TimeDlyResume
                0x0000000000000000       0x1e ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esKRNL_TimeSet
                0x0000000000000000       0x1a ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esKRNL_TmrCreate
                0x0000000000000000       0x1a ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esKRNL_TmrDel
                0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esKRNL_TmrError
                0x0000000000000000       0x1a ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esKRNL_TmrRemainGet
                0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esKRNL_TmrStart
                0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esKRNL_TmrStateGet
                0x0000000000000000       0x1a ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esKRNL_TmrStop
                0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esKRNL_Version
                0x0000000000000000       0x1a ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esKRNL_Ioctrl
                0x0000000000000000       0x16 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esKSRV_ClearWatchDog
                0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esKSRV_DisableWatchDog
                0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esKSRV_DisableWatchDogSrv
                0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esKSRV_EnableWatchDog
                0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esKSRV_EnableWatchDogSrv
                0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esKSRV_GetAddPara
                0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esKSRV_GetDramCfgPara
                0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esKSRV_GetHighMsg
                0x0000000000000000       0x16 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esKSRV_GetLowMsg
                0x0000000000000000       0x16 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esKSRV_GetMsg
                0x0000000000000000       0x16 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esKSRV_GetPara
                0x0000000000000000       0x1c ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esKSRV_GetSocID
                0x0000000000000000       0x1a ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esKSRV_GetTargetPara
                0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esKSRV_GetVersion
                0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esKSRV_Get_Display_Hld
                0x0000000000000000       0x16 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esKSRV_Save_Mixture_Hld
                0x0000000000000000       0x16 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esKSRV_Get_Mixture_Hld
                0x0000000000000000       0x16 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esKSRV_Save_Raw_Decoder_Hld
                0x0000000000000000       0x16 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esKSRV_Get_Raw_Decoder_Hld
                0x0000000000000000       0x16 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esKSRV_memcpy
                0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esKSRV_memset
                0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esKSRV_PowerOff
                0x0000000000000000       0x16 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esKSRV_Random
                0x0000000000000000       0x1c ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esKSRV_Reset
                0x0000000000000000       0x16 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esKSRV_Save_Display_Hld
                0x0000000000000000       0x16 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esKSRV_SendMsgEx
                0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esKSRV_SysInfo
                0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esMEMS_Calloc
                0x0000000000000000       0x1e ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esMEMS_CleanDCache
                0x0000000000000000       0x16 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esMEMS_CleanDCacheRegion
                0x0000000000000000       0x1a ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esMEMS_CleanFlushCache
                0x0000000000000000       0x16 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esMEMS_CleanFlushCacheRegion
                0x0000000000000000       0x1a ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esMEMS_CleanFlushDCache
                0x0000000000000000       0x16 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esMEMS_CleanFlushDCacheRegion
                0x0000000000000000       0x1a ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esMEMS_CleanInvalidateCacheAll
                0x0000000000000000       0x16 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esMEMS_FlushCache
                0x0000000000000000       0x16 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esMEMS_FlushCacheRegion
                0x0000000000000000       0x1a ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esMEMS_FlushDCache
                0x0000000000000000       0x16 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esMEMS_FlushDCacheRegion
                0x0000000000000000       0x1a ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esMEMS_FlushICache
                0x0000000000000000       0x16 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esMEMS_FlushICacheRegion
                0x0000000000000000       0x1a ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esMEMS_FreeMemSize
                0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esMEMS_GetIoVAByPA
                0x0000000000000000       0x1c ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esMEMS_HeapCreate
                0x0000000000000000       0x1c ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esMEMS_HeapDel
                0x0000000000000000       0x16 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esMEMS_Info
                0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esMEMS_Palloc
                0x0000000000000000       0x1e ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esMEMS_Pfree
                0x0000000000000000       0x1a ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esMEMS_Realloc
                0x0000000000000000       0x1a ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esMEMS_TotalMemSize
                0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esMEMS_VA2PA
                0x0000000000000000       0x16 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esMEMS_VMalloc
                0x0000000000000000       0x16 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esMEMS_VMCreate
                0x0000000000000000       0x1c ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esMEMS_VMDelete
                0x0000000000000000       0x1a ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esMEMS_VMfree
                0x0000000000000000       0x16 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esMEM_BWDisable
                0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esMEM_BWEnable
                0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esMEM_BWGet
                0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esMEM_DramSuspend
                0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esMEM_DramWakeup
                0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esMEM_MasterGet
                0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esMEM_MasterSet
                0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esMEM_RegDramAccess
                0x0000000000000000       0x16 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esMEM_ReleaseDramUsrMode
                0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esMEM_RequestDramUsrMode
                0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esMEM_SramRelBlk
                0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esMEM_SramReqBlk
                0x0000000000000000       0x16 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esMEM_SramSwitchBlk
                0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esMEM_UnRegDramAccess
                0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esMODS_MClose
                0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esMODS_MInstall
                0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esMODS_MIoctrl
                0x0000000000000000       0x1a ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esMODS_MOpen
                0x0000000000000000       0x16 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esMODS_MRead
                0x0000000000000000       0x20 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esMODS_MUninstall
                0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esMODS_MWrite
                0x0000000000000000       0x20 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esMSTUB_GetFuncEntry
                0x0000000000000000       0x1e ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esMSTUB_RegFuncTbl
                0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esMSTUB_UnRegFuncTbl
                0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esPINS_ClearPending
                0x0000000000000000       0x16 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esPINS_DisbaleInt
                0x0000000000000000       0x16 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esPINS_EnbaleInt
                0x0000000000000000       0x16 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esPINS_GetPinGrpStat
                0x0000000000000000       0x1a ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esPINS_GetPinStat
                0x0000000000000000       0x16 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esPINS_PinGrpRel
                0x0000000000000000       0x16 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esPINS_PinGrpReq
                0x0000000000000000       0x16 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esPINS_QueryInt
                0x0000000000000000       0x16 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esPINS_ReadPinData
                0x0000000000000000       0x16 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esPINS_RegIntHdler
                0x0000000000000000       0x16 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esPINS_SetIntMode
                0x0000000000000000       0x1a ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esPINS_SetPinDrive
                0x0000000000000000       0x1a ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esPINS_SetPinIO
                0x0000000000000000       0x16 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esPINS_SetPinPull
                0x0000000000000000       0x1a ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esPINS_SetPinStat
                0x0000000000000000       0x16 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esPINS_UnregIntHdler
                0x0000000000000000       0x16 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esPINS_WritePinData
                0x0000000000000000       0x1a ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esPWRMAN_EnterStandby
                0x0000000000000000       0x1a ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esPWRMAN_GetStandbyPara
                0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esPWRMAN_LockCpuFreq
                0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esPWRMAN_RegDevice
                0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esPWRMAN_RelPwrmanMode
                0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esPWRMAN_ReqPwrmanMode
                0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esPWRMAN_SetStandbyMode
                0x0000000000000000       0x1c ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esPWRMAN_UnlockCpuFreq
                0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esPWRMAN_UnregDevice
                0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esPWRMAN_UsrEventNotify
                0x0000000000000000       0x16 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esRESM_RClose
                0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esRESM_ROpen
                0x0000000000000000       0x16 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esRESM_RRead
                0x0000000000000000       0x20 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esRESM_RSeek
                0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esSIOS_getchar
                0x0000000000000000       0x16 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esSIOS_gets
                0x0000000000000000       0x14 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esSIOS_putarg
                0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esSIOS_setbaud
                0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esSVC_RegCloseNode
                0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esSVC_RegClrError
                0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esSVC_RegCreateKey
                0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esSVC_RegCreatePath
                0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esSVC_RegCreateSet
                0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esSVC_RegDeleteKey
                0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esSVC_RegDeleteNode
                0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esSVC_RegDeletePath
                0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esSVC_RegDeleteSet
                0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esSVC_RegGetError
                0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esSVC_RegGetFirstKey
                0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esSVC_RegGetFirstSet
                0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esSVC_RegGetKeyCount
                0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esSVC_RegGetKeyValue
                0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esSVC_RegGetNextKey
                0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esSVC_RegGetNextSet
                0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esSVC_RegGetRootPath
                0x0000000000000000       0x16 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esSVC_RegGetSetCount
                0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esSVC_RegIni2Reg
                0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esSVC_RegOpenNode
                0x0000000000000000       0x16 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esSVC_RegReg2Ini
                0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esSVC_RegSetKeyValue
                0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esSVC_RegSetRootPath
                0x0000000000000000       0x16 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esSVC_ResourceRel
                0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esSVC_ResourceReq
                0x0000000000000000       0x22 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esTIME_ContiCntr
                0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esTIME_GetDate
                0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esTIME_GetTime
                0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esTIME_PauseCntr
                0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esTIME_QuerryAlarm
                0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esTIME_QuerryCntr
                0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esTIME_QuerryCntrStat
                0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esTIME_QuerryTimer
                0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esTIME_ReleaseAlarm
                0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esTIME_ReleaseCntr
                0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esTIME_ReleaseTimer
                0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esTIME_RequestAlarm
                0x0000000000000000       0x1a ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esTIME_RequestCntr
                0x0000000000000000       0x16 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esTIME_RequestTimer
                0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esTIME_SetCntrPrescale
                0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esTIME_SetCntrValue
                0x0000000000000000       0x1c ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esTIME_SetDate
                0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esTIME_SetTime
                0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esTIME_StartAlarm
                0x0000000000000000       0x1c ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esTIME_StartCntr
                0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esTIME_StartTimer
                0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esTIME_StopAlarm
                0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esTIME_StopCntr
                0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esTIME_StopTimer
                0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.backtrace
                0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.select   0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.mmap     0x0000000000000000       0x16 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.munmap   0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.poll     0x0000000000000000       0x1c ./elibrary/bin//libsyscall.a(syscall.o)
 .text.pipe     0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall.o)
 .text          0x0000000000000000        0x0 ./elibrary/bin//libsyscall.a(syscall_fops.o)
 .data          0x0000000000000000        0x0 ./elibrary/bin//libsyscall.a(syscall_fops.o)
 .bss           0x0000000000000000        0x0 ./elibrary/bin//libsyscall.a(syscall_fops.o)
 .text.dup      0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall_fops.o)
 .text.dup2     0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall_fops.o)
 .text.closedir
                0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall_fops.o)
 .text.fioctrl  0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall_fops.o)
 .text._fstat_r
                0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall_fops.o)
 .text._lseek_r
                0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall_fops.o)
 .text._rename_r
                0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall_fops.o)
 .text._stat_r  0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall_fops.o)
 .text._mkdir_r
                0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall_fops.o)
 .text.fsync    0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall_fops.o)
 .text._unlink_r
                0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall_fops.o)
 .text._read_r  0x0000000000000000       0x16 ./elibrary/bin//libsyscall.a(syscall_fops.o)
 .text._write_r
                0x0000000000000000       0x16 ./elibrary/bin//libsyscall.a(syscall_fops.o)
 .text._gettimeofday_r
                0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall_fops.o)
 .text.settimeofday
                0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall_fops.o)
 .text._malloc_r
                0x0000000000000000       0x16 ./elibrary/bin//libsyscall.a(syscall_fops.o)
 .text._realloc_r
                0x0000000000000000       0x16 ./elibrary/bin//libsyscall.a(syscall_fops.o)
 .text._calloc_r
                0x0000000000000000       0x16 ./elibrary/bin//libsyscall.a(syscall_fops.o)
 .text._free_r  0x0000000000000000       0x16 ./elibrary/bin//libsyscall.a(syscall_fops.o)
 .text._isatty_r
                0x0000000000000000       0x16 ./elibrary/bin//libsyscall.a(syscall_fops.o)
 .text._isatty  0x0000000000000000       0x16 ./elibrary/bin//libsyscall.a(syscall_fops.o)
 .text.__libc_init_array
                0x0000000000000000       0x16 ./elibrary/bin//libsyscall.a(syscall_fops.o)
 .text._system  0x0000000000000000       0x16 ./elibrary/bin//libsyscall.a(syscall_fops.o)
 .text.__malloc_lock
                0x0000000000000000        0x2 ./elibrary/bin//libsyscall.a(syscall_fops.o)
 .text.__malloc_unlock
                0x0000000000000000        0x2 ./elibrary/bin//libsyscall.a(syscall_fops.o)
 .text.ftruncate
                0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall_fops.o)
 .text.mkdir    0x0000000000000000       0x1c ./elibrary/bin//libsyscall.a(syscall_fops.o)
 .text.opendir  0x0000000000000000       0x16 ./elibrary/bin//libsyscall.a(syscall_fops.o)
 .text.readdir  0x0000000000000000       0x16 ./elibrary/bin//libsyscall.a(syscall_fops.o)
 .text.remove   0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall_fops.o)
 .text.rewinddir
                0x0000000000000000       0x16 ./elibrary/bin//libsyscall.a(syscall_fops.o)
 .text.rmdir    0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall_fops.o)
 .text.statfs   0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall_fops.o)
 .text.access   0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall_fops.o)
 .text          0x0000000000000000        0x0 ./elibrary/bin//libminic.a(elibs_sprintf.o)
 .data          0x0000000000000000        0x0 ./elibrary/bin//libminic.a(elibs_sprintf.o)
 .bss           0x0000000000000000        0x0 ./elibrary/bin//libminic.a(elibs_sprintf.o)
 .text.eLIBs_snprintf
                0x0000000000000000       0x20 ./elibrary/bin//libminic.a(elibs_sprintf.o)
 .text.eLIBs_scnprintf
                0x0000000000000000       0x2e ./elibrary/bin//libminic.a(elibs_sprintf.o)
 .text.eLIBs_vsprintf
                0x0000000000000000        0xe ./elibrary/bin//libminic.a(elibs_sprintf.o)
 .text          0x0000000000000000        0x0 ./elibrary/bin//libminic.a(elibs_stdio.o)
 .data          0x0000000000000000        0x0 ./elibrary/bin//libminic.a(elibs_stdio.o)
 .bss           0x0000000000000000        0x0 ./elibrary/bin//libminic.a(elibs_stdio.o)
 .text.eLIBs_remove
                0x0000000000000000        0x8 ./elibrary/bin//libminic.a(elibs_stdio.o)
 .text.eLIBs_rename
                0x0000000000000000        0x8 ./elibrary/bin//libminic.a(elibs_stdio.o)
 .text.eLIBs_fsync
                0x0000000000000000        0x8 ./elibrary/bin//libminic.a(elibs_stdio.o)
 .text.eLIBs_ftruncate
                0x0000000000000000        0x8 ./elibrary/bin//libminic.a(elibs_stdio.o)
 .text.eLIBs_fllseek
                0x0000000000000000       0x1e ./elibrary/bin//libminic.a(elibs_stdio.o)
 .text.eLIBs_flltell
                0x0000000000000000       0x26 ./elibrary/bin//libminic.a(elibs_stdio.o)
 .text.eLIBs_ferror
                0x0000000000000000        0x8 ./elibrary/bin//libminic.a(elibs_stdio.o)
 .text.eLIBs_ferrclr
                0x0000000000000000        0x8 ./elibrary/bin//libminic.a(elibs_stdio.o)
 .text.eLIBs_opendir
                0x0000000000000000        0x8 ./elibrary/bin//libminic.a(elibs_stdio.o)
 .text.eLIBs_closedir
                0x0000000000000000        0x8 ./elibrary/bin//libminic.a(elibs_stdio.o)
 .text.eLIBs_readdir
                0x0000000000000000        0x8 ./elibrary/bin//libminic.a(elibs_stdio.o)
 .text.eLIBs_rewinddir
                0x0000000000000000        0x8 ./elibrary/bin//libminic.a(elibs_stdio.o)
 .text.eLIBs_mkdir
                0x0000000000000000        0x8 ./elibrary/bin//libminic.a(elibs_stdio.o)
 .text.eLIBs_rmdir
                0x0000000000000000        0x8 ./elibrary/bin//libminic.a(elibs_stdio.o)
 .text.eLIBs_DirEnt2Attr
                0x0000000000000000        0x8 ./elibrary/bin//libminic.a(elibs_stdio.o)
 .text.eLIBs_DirEnt2Name
                0x0000000000000000       0x2e ./elibrary/bin//libminic.a(elibs_stdio.o)
 .text.eLIBs_DirEnt2Size
                0x0000000000000000        0x6 ./elibrary/bin//libminic.a(elibs_stdio.o)
 .text.eLIBs_DirEnt2Time
                0x0000000000000000        0x4 ./elibrary/bin//libminic.a(elibs_stdio.o)
 .text.eLIBs_GetNumFiles
                0x0000000000000000       0x3e ./elibrary/bin//libminic.a(elibs_stdio.o)
 .text.eLIBs_GetNumSubItems
                0x0000000000000000       0x4e ./elibrary/bin//libminic.a(elibs_stdio.o)
 .text.eLIBs_GetFileAttributes
                0x0000000000000000       0x4c ./elibrary/bin//libminic.a(elibs_stdio.o)
 .text.eLIBs_SetFileAttributes
                0x0000000000000000        0x4 ./elibrary/bin//libminic.a(elibs_stdio.o)
 .text.eLIBs_GetDirSize
                0x0000000000000000      0x108 ./elibrary/bin//libminic.a(elibs_stdio.o)
 .text.eLIBs_GetFileSize
                0x0000000000000000       0x40 ./elibrary/bin//libminic.a(elibs_stdio.o)
 .text.eLIBs_GetVolName
                0x0000000000000000       0x80 ./elibrary/bin//libminic.a(elibs_stdio.o)
 .text.eLIBs_GetVolNameLen
                0x0000000000000000       0x50 ./elibrary/bin//libminic.a(elibs_stdio.o)
 .text.eLIBs_GetVolNameCharset
                0x0000000000000000       0x52 ./elibrary/bin//libminic.a(elibs_stdio.o)
 .text.eLIBs_GetVolTSpace
                0x0000000000000000       0x30 ./elibrary/bin//libminic.a(elibs_stdio.o)
 .text.eLIBs_GetVolFSpace
                0x0000000000000000       0x30 ./elibrary/bin//libminic.a(elibs_stdio.o)
 .text.eLIBs_GetVolClustSize
                0x0000000000000000       0x26 ./elibrary/bin//libminic.a(elibs_stdio.o)
 .text.eLIBs_GetFSName
                0x0000000000000000       0x80 ./elibrary/bin//libminic.a(elibs_stdio.o)
 .text.eLIBs_GetFSNameLen
                0x0000000000000000       0x50 ./elibrary/bin//libminic.a(elibs_stdio.o)
 .text.eLIBs_GetFSAttribute
                0x0000000000000000       0x36 ./elibrary/bin//libminic.a(elibs_stdio.o)
 .text.eLIBs_GetPTName
                0x0000000000000000       0x62 ./elibrary/bin//libminic.a(elibs_stdio.o)
 .text.eLIBs_IsPartFormated
                0x0000000000000000       0x9c ./elibrary/bin//libminic.a(elibs_stdio.o)
 .text.eLIBs_disklock
                0x0000000000000000        0x8 ./elibrary/bin//libminic.a(elibs_stdio.o)
 .text.eLIBs_diskunlock
                0x0000000000000000        0x8 ./elibrary/bin//libminic.a(elibs_stdio.o)
 .text.eLIBs_GetDiskArray
                0x0000000000000000       0x62 ./elibrary/bin//libminic.a(elibs_stdio.o)
 .text.eLIBs_feof
                0x0000000000000000       0x1e ./elibrary/bin//libminic.a(elibs_stdio.o)
 .text.eLIBs_fgetc
                0x0000000000000000       0x2c ./elibrary/bin//libminic.a(elibs_stdio.o)
 .text.eLIBs_fgets
                0x0000000000000000       0x6c ./elibrary/bin//libminic.a(elibs_stdio.o)
 .text.eLIBs_fputc
                0x0000000000000000       0x32 ./elibrary/bin//libminic.a(elibs_stdio.o)
 .text.eLIBs_fputs
                0x0000000000000000       0x42 ./elibrary/bin//libminic.a(elibs_stdio.o)
 .text.eLIBs_GetFileATime
                0x0000000000000000       0x50 ./elibrary/bin//libminic.a(elibs_stdio.o)
 .text.eLIBs_GetFileMTime
                0x0000000000000000       0x50 ./elibrary/bin//libminic.a(elibs_stdio.o)
 .text.eLIBs_GetFileCTime
                0x0000000000000000       0x50 ./elibrary/bin//libminic.a(elibs_stdio.o)
 .text.eLIBs_GetDirATime
                0x0000000000000000       0x48 ./elibrary/bin//libminic.a(elibs_stdio.o)
 .text.eLIBs_GetDirMTime
                0x0000000000000000       0x48 ./elibrary/bin//libminic.a(elibs_stdio.o)
 .text.eLIBs_GetDirCTime
                0x0000000000000000       0x48 ./elibrary/bin//libminic.a(elibs_stdio.o)
 .text.eLIBs_GetFSCharset
                0x0000000000000000       0x10 ./elibrary/bin//libminic.a(elibs_stdio.o)
 .text.eLIBs_file2fd
                0x0000000000000000        0x8 ./elibrary/bin//libminic.a(elibs_stdio.o)
 .text.eLIBs_fd2file
                0x0000000000000000        0x8 ./elibrary/bin//libminic.a(elibs_stdio.o)
 .text.eLIBs_open
                0x0000000000000000        0x8 ./elibrary/bin//libminic.a(elibs_stdio.o)
 .text.eLIBs_fstat
                0x0000000000000000        0x8 ./elibrary/bin//libminic.a(elibs_stdio.o)
 .text.eLIBs_statfs
                0x0000000000000000        0xc ./elibrary/bin//libminic.a(elibs_stdio.o)
 .text.eLIBs_GetDevName
                0x0000000000000000       0x6a ./elibrary/bin//libminic.a(elibs_stdio.o)
 .text.eLIBs_GetDevParts
                0x0000000000000000       0x88 ./elibrary/bin//libminic.a(elibs_stdio.o)
 .text.eLIBs_GetTime
                0x0000000000000000        0x8 ./elibrary/bin//libminic.a(elibs_stdio.o)
 .text.eLIBs_GetDate
                0x0000000000000000        0x8 ./elibrary/bin//libminic.a(elibs_stdio.o)
 .text.eLIBs_setfs
                0x0000000000000000       0x14 ./elibrary/bin//libminic.a(elibs_stdio.o)
 .bss.dir_level.6470
                0x0000000000000000        0x4 ./elibrary/bin//libminic.a(elibs_stdio.o)
 .rodata.eLIBs_GetDirSize.str1.8
                0x0000000000000000        0x2 ./elibrary/bin//libminic.a(elibs_stdio.o)
 .rodata.eLIBs_GetFileAttributes.str1.8
                0x0000000000000000        0x3 ./elibrary/bin//libminic.a(elibs_stdio.o)
 .rodata.eLIBs_GetVolNameCharset.str1.8
                0x0000000000000000        0x5 ./elibrary/bin//libminic.a(elibs_stdio.o)
 .text          0x0000000000000000        0x0 ./elibrary/bin//libminic.a(elibs_string.o)
 .data          0x0000000000000000        0x0 ./elibrary/bin//libminic.a(elibs_string.o)
 .bss           0x0000000000000000        0x0 ./elibrary/bin//libminic.a(elibs_string.o)
 .text.eLIBs_strnlen
                0x0000000000000000        0xc ./elibrary/bin//libminic.a(elibs_string.o)
 .text.eLIBs_strcat
                0x0000000000000000        0xc ./elibrary/bin//libminic.a(elibs_string.o)
 .text.eLIBs_strncat
                0x0000000000000000        0xc ./elibrary/bin//libminic.a(elibs_string.o)
 .text.eLIBs_strcmp
                0x0000000000000000        0xc ./elibrary/bin//libminic.a(elibs_string.o)
 .text.eLIBs_stricmp
                0x0000000000000000        0xc ./elibrary/bin//libminic.a(elibs_string.o)
 .text.eLIBs_strnicmp
                0x0000000000000000        0xc ./elibrary/bin//libminic.a(elibs_string.o)
 .text.eLIBs_strchr
                0x0000000000000000        0xc ./elibrary/bin//libminic.a(elibs_string.o)
 .text.eLIBs_strnchr
                0x0000000000000000        0xc ./elibrary/bin//libminic.a(elibs_string.o)
 .text.eLIBs_strchrlast
                0x0000000000000000        0xc ./elibrary/bin//libminic.a(elibs_string.o)
 .text.eLIBs_strstr
                0x0000000000000000        0xc ./elibrary/bin//libminic.a(elibs_string.o)
 .text.eLIBs_memclr
                0x0000000000000000       0x10 ./elibrary/bin//libminic.a(elibs_string.o)
 .text.eLIBs_memmove
                0x0000000000000000       0x3c ./elibrary/bin//libminic.a(elibs_string.o)
 .data          0x0000000000000000        0x0 /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/../../../../riscv64-unknown-elf/lib/rv64imafdcxtheadc/lp64d/libc.a(lib_a-memcpy.o)
 .bss           0x0000000000000000        0x0 /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/../../../../riscv64-unknown-elf/lib/rv64imafdcxtheadc/lp64d/libc.a(lib_a-memcpy.o)
 .data          0x0000000000000000        0x0 /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/../../../../riscv64-unknown-elf/lib/rv64imafdcxtheadc/lp64d/libc.a(lib_a-memset.o)
 .bss           0x0000000000000000        0x0 /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/../../../../riscv64-unknown-elf/lib/rv64imafdcxtheadc/lp64d/libc.a(lib_a-memset.o)
 .data          0x0000000000000000        0x0 /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/../../../../riscv64-unknown-elf/lib/rv64imafdcxtheadc/lp64d/libc.a(lib_a-sysclose.o)
 .bss           0x0000000000000000        0x0 /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/../../../../riscv64-unknown-elf/lib/rv64imafdcxtheadc/lp64d/libc.a(lib_a-sysclose.o)
 .data          0x0000000000000000        0x0 /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/../../../../riscv64-unknown-elf/lib/rv64imafdcxtheadc/lp64d/libc.a(lib_a-sysopen.o)
 .bss           0x0000000000000000        0x0 /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/../../../../riscv64-unknown-elf/lib/rv64imafdcxtheadc/lp64d/libc.a(lib_a-sysopen.o)
 .text          0x0000000000000000        0x0 /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/../../../../riscv64-unknown-elf/lib/rv64imafdcxtheadc/lp64d/libc.a(lib_a-impure.o)
 .bss           0x0000000000000000        0x0 /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/../../../../riscv64-unknown-elf/lib/rv64imafdcxtheadc/lp64d/libc.a(lib_a-impure.o)
 .rodata        0x0000000000000000        0x8 /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/../../../../riscv64-unknown-elf/lib/rv64imafdcxtheadc/lp64d/libc.a(lib_a-impure.o)

Memory Configuration

Name             Origin             Length             Attributes
dram0_0_seg      0x00000000c2000000 0x0000000004000000 xrw
dram0_1_seg      0x00000000e0000000 0x0000000010000000 xrw
dram0_2_seg      0x00000000e8c00000 0x0000000000060000 xrw
magic_info_seg   0x00000000ffff0000 0x0000000000000100 r
*default*        0x0000000000000000 0xffffffffffffffff

Linker script and memory map

LOAD emodules/drv_dab/dev_dab.o
LOAD emodules/drv_dab/drv_dab.o
LOAD emodules/drv_dab/dab_module/si463x/cmmbSpi.o
LOAD emodules/drv_dab/dab_module/si463x/common_types.o
LOAD emodules/drv_dab/dab_module/si463x/DAB_DLS_Handler.o
LOAD emodules/drv_dab/dab_module/si463x/DAB_Service_List_Handler.o
LOAD emodules/drv_dab/dab_module/si463x/Firmware_API_Manager.o
LOAD emodules/drv_dab/dab_module/si463x/Firmware_Load_Helpers.o
LOAD emodules/drv_dab/dab_module/si463x/FMHD_PSD_Handler.o
LOAD emodules/drv_dab/dab_module/si463x/FMHD_Service_List_Handler.o
LOAD emodules/drv_dab/dab_module/si463x/FMHD_SIS_Handler.o
LOAD emodules/drv_dab/dab_module/si463x/FMHD_Alert_Handler.o
LOAD emodules/drv_dab/dab_module/si463x/freq_stack.o
LOAD emodules/drv_dab/dab_module/si463x/HAL.o
LOAD emodules/drv_dab/dab_module/si463x/Hardware_Calibration_Helpers.o
LOAD emodules/drv_dab/dab_module/si463x/rds.o
LOAD emodules/drv_dab/dab_module/si463x/RDS_Handler.o
LOAD emodules/drv_dab/dab_module/si463x/si46xx_firmware_api.o
LOAD emodules/drv_dab/dab_module/si463x/si463x_PresetListCreat.o
LOAD emodules/drv_dab/dab_module/si463x/si468x.o
LOAD emodules/drv_dab/dab_module/si463x/si468x_bus.o
LOAD emodules/drv_dab/dab_module/si463x/mot.o
LOAD emodules/drv_dab/magic.o
START GROUP
START GROUP
LOAD ./elibrary/bin//libsyscall.a
LOAD ./elibrary/bin//libminic.a
LOAD ./elibrary/bin//libpub0.a
END GROUP
START GROUP
LOAD ./elibrary/bin//libcheck_app.a
END GROUP
LOAD /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/../../../../riscv64-unknown-elf/lib/rv64imafdcxtheadc/lp64d/libstdc++.a
LOAD /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/../../../../riscv64-unknown-elf/lib/rv64imafdcxtheadc/lp64d/libc.a
LOAD /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/rv64imafdcxtheadc/lp64d/libgcc.a
LOAD /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/../../../../riscv64-unknown-elf/lib/rv64imafdcxtheadc/lp64d/libm.a
END GROUP
                0x00000000e8c00000                __DRAM0_MOD_BASE = ORIGIN (dram0_2_seg)
                0x00000000ffff0000                __DRAM0_INF_BASE = ORIGIN (magic_info_seg)

.mod.text       0x00000000e8c00000    0x10fb4
 *(.text)
 .text          0x00000000e8c00000       0xd0 /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/../../../../riscv64-unknown-elf/lib/rv64imafdcxtheadc/lp64d/libc.a(lib_a-memcpy.o)
                0x00000000e8c00000                memcpy
 .text          0x00000000e8c000d0       0xaa /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/../../../../riscv64-unknown-elf/lib/rv64imafdcxtheadc/lp64d/libc.a(lib_a-memset.o)
                0x00000000e8c000d0                memset
 *fill*         0x00000000e8c0017a        0x6 
 .text          0x00000000e8c00180       0x1e /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/../../../../riscv64-unknown-elf/lib/rv64imafdcxtheadc/lp64d/libc.a(lib_a-sysclose.o)
                0x00000000e8c00180                close
 *fill*         0x00000000e8c0019e        0x2 
 .text          0x00000000e8c001a0       0x3e /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/../../../../riscv64-unknown-elf/lib/rv64imafdcxtheadc/lp64d/libc.a(lib_a-sysopen.o)
                0x00000000e8c001a0                open
 *(.text.*)
 .text.hal_dab_init
                0x00000000e8c001de        0x4 emodules/drv_dab/dev_dab.o
                0x00000000e8c001de                hal_dab_init
 .text.sunxi_dab_init
                0x00000000e8c001e2        0x4 emodules/drv_dab/dev_dab.o
 .text.sunxi_dab_open
                0x00000000e8c001e6       0x20 emodules/drv_dab/dev_dab.o
 .text.sunxi_dab_close
                0x00000000e8c00206       0x20 emodules/drv_dab/dev_dab.o
 .text.sunxi_dab_write
                0x00000000e8c00226        0x4 emodules/drv_dab/dev_dab.o
 .text.sunxi_dab_control
                0x00000000e8c0022a       0x26 emodules/drv_dab/dev_dab.o
 .text.hal_dab_control
                0x00000000e8c00250      0x1b0 emodules/drv_dab/dev_dab.o
                0x00000000e8c00250                hal_dab_control
 .text.sunxi_dab_read
                0x00000000e8c00400        0x4 emodules/drv_dab/dev_dab.o
 .text.hal_dab_uninit
                0x00000000e8c00404        0x4 emodules/drv_dab/dev_dab.o
                0x00000000e8c00404                hal_dab_uninit
 .text.sunxi_driver_dab_init
                0x00000000e8c00408       0x74 emodules/drv_dab/dev_dab.o
                0x00000000e8c00408                sunxi_driver_dab_init
 .text.sunxi_driver_dab_uninit
                0x00000000e8c0047c       0x1a emodules/drv_dab/dev_dab.o
                0x00000000e8c0047c                sunxi_driver_dab_uninit
 .text.DRV_DAB_MInit
                0x00000000e8c00496       0x22 emodules/drv_dab/drv_dab.o
                0x00000000e8c00496                DRV_DAB_MInit
 .text.DRV_DAB_MExit
                0x00000000e8c004b8       0x20 emodules/drv_dab/drv_dab.o
                0x00000000e8c004b8                DRV_DAB_MExit
 .text.DRV_DAB_MOpen
                0x00000000e8c004d8        0xe emodules/drv_dab/drv_dab.o
                0x00000000e8c004d8                DRV_DAB_MOpen
 .text.DRV_DAB_MClose
                0x00000000e8c004e6        0x4 emodules/drv_dab/drv_dab.o
                0x00000000e8c004e6                DRV_DAB_MClose
 .text.DRV_DAB_MRead
                0x00000000e8c004ea        0x6 emodules/drv_dab/drv_dab.o
                0x00000000e8c004ea                DRV_DAB_MRead
 .text.DRV_DAB_MWrite
                0x00000000e8c004f0        0x6 emodules/drv_dab/drv_dab.o
                0x00000000e8c004f0                DRV_DAB_MWrite
 .text.DRV_DAB_MIoctrl
                0x00000000e8c004f6       0xa8 emodules/drv_dab/drv_dab.o
                0x00000000e8c004f6                DRV_DAB_MIoctrl
 .text.dls_ebu_to_utf16
                0x00000000e8c0059e       0x90 emodules/drv_dab/dab_module/si463x/DAB_DLS_Handler.o
                0x00000000e8c0059e                dls_ebu_to_utf16
 .text.charset_convert
                0x00000000e8c0062e       0x86 emodules/drv_dab/dab_module/si463x/DAB_DLS_Handler.o
                0x00000000e8c0062e                charset_convert
 .text.dls_decode_str.part.1
                0x00000000e8c006b4       0x4c emodules/drv_dab/dab_module/si463x/DAB_DLS_Handler.o
 .text.dls_decode_str
                0x00000000e8c00700       0x46 emodules/drv_dab/dab_module/si463x/DAB_DLS_Handler.o
                0x00000000e8c00700                dls_decode_str
 .text._update_generic_dls_string
                0x00000000e8c00746       0x20 emodules/drv_dab/dab_module/si463x/DAB_DLS_Handler.o
                0x00000000e8c00746                _update_generic_dls_string
 .text.initDLS  0x00000000e8c00766       0x2e emodules/drv_dab/dab_module/si463x/DAB_DLS_Handler.o
                0x00000000e8c00766                initDLS
 .text.updateDLS
                0x00000000e8c00794       0x7e emodules/drv_dab/dab_module/si463x/DAB_DLS_Handler.o
                0x00000000e8c00794                updateDLS
 .text.initDAB_ServiceList
                0x00000000e8c00812       0x68 emodules/drv_dab/dab_module/si463x/DAB_Service_List_Handler.o
                0x00000000e8c00812                initDAB_ServiceList
 .text.getServiceListElement
                0x00000000e8c0087a       0x72 emodules/drv_dab/dab_module/si463x/DAB_Service_List_Handler.o
                0x00000000e8c0087a                getServiceListElement
 .text.setServiceListElement
                0x00000000e8c008ec       0x9c emodules/drv_dab/dab_module/si463x/DAB_Service_List_Handler.o
                0x00000000e8c008ec                setServiceListElement
 .text.DeleteMultiServiceListElement
                0x00000000e8c00988      0x162 emodules/drv_dab/dab_module/si463x/DAB_Service_List_Handler.o
                0x00000000e8c00988                DeleteMultiServiceListElement
 .text.findServiceInList
                0x00000000e8c00aea       0x92 emodules/drv_dab/dab_module/si463x/DAB_Service_List_Handler.o
                0x00000000e8c00aea                findServiceInList
 .text.findServiceInListEx
                0x00000000e8c00b7c       0x7c emodules/drv_dab/dab_module/si463x/DAB_Service_List_Handler.o
                0x00000000e8c00b7c                findServiceInListEx
 .text.findSameServiceInList
                0x00000000e8c00bf8       0x66 emodules/drv_dab/dab_module/si463x/DAB_Service_List_Handler.o
                0x00000000e8c00bf8                findSameServiceInList
 .text.findServiceFreqByListIndex
                0x00000000e8c00c5e       0x40 emodules/drv_dab/dab_module/si463x/DAB_Service_List_Handler.o
                0x00000000e8c00c5e                findServiceFreqByListIndex
 .text.findServiceListByFreq
                0x00000000e8c00c9e       0x56 emodules/drv_dab/dab_module/si463x/DAB_Service_List_Handler.o
                0x00000000e8c00c9e                findServiceListByFreq
 .text.findServiceIndexInList
                0x00000000e8c00cf4       0xae emodules/drv_dab/dab_module/si463x/DAB_Service_List_Handler.o
                0x00000000e8c00cf4                findServiceIndexInList
 .text.findService_In_Original_List
                0x00000000e8c00da2       0x92 emodules/drv_dab/dab_module/si463x/DAB_Service_List_Handler.o
                0x00000000e8c00da2                findService_In_Original_List
 .text.get_scids_In_Offset_List_by_sid_scid
                0x00000000e8c00e34       0x84 emodules/drv_dab/dab_module/si463x/DAB_Service_List_Handler.o
                0x00000000e8c00e34                get_scids_In_Offset_List_by_sid_scid
 .text._convert_ASCII_upper
                0x00000000e8c00eb8       0x16 emodules/drv_dab/dab_module/si463x/DAB_Service_List_Handler.o
                0x00000000e8c00eb8                _convert_ASCII_upper
 .text._insertion_sort_alphabetical_single_element
                0x00000000e8c00ece      0x104 emodules/drv_dab/dab_module/si463x/DAB_Service_List_Handler.o
                0x00000000e8c00ece                _insertion_sort_alphabetical_single_element
 .text._insertion_sort_alphabetical_list
                0x00000000e8c00fd2       0xec emodules/drv_dab/dab_module/si463x/DAB_Service_List_Handler.o
                0x00000000e8c00fd2                _insertion_sort_alphabetical_list
 .text._add_data_to_buffer
                0x00000000e8c010be       0x3e emodules/drv_dab/dab_module/si463x/DAB_Service_List_Handler.o
                0x00000000e8c010be                _add_data_to_buffer
 .text.updateDAB_ServiceList
                0x00000000e8c010fc      0x550 emodules/drv_dab/dab_module/si463x/DAB_Service_List_Handler.o
                0x00000000e8c010fc                updateDAB_ServiceList
 .text._update_DAB_DAB_service_linking
                0x00000000e8c0164c      0x128 emodules/drv_dab/dab_module/si463x/Firmware_API_Manager.o
 .text.dab_find_linkset.isra.3
                0x00000000e8c01774       0x54 emodules/drv_dab/dab_module/si463x/Firmware_API_Manager.o
 .text._set_property
                0x00000000e8c017c8        0xc emodules/drv_dab/dab_module/si463x/Firmware_API_Manager.o
                0x00000000e8c017c8                _set_property
 .text._get_property
                0x00000000e8c017d4       0x28 emodules/drv_dab/dab_module/si463x/Firmware_API_Manager.o
                0x00000000e8c017d4                _get_property
 .text._set_property_field
                0x00000000e8c017fc       0x5a emodules/drv_dab/dab_module/si463x/Firmware_API_Manager.o
                0x00000000e8c017fc                _set_property_field
 .text._host_load
                0x00000000e8c01856       0x8e emodules/drv_dab/dab_module/si463x/Firmware_API_Manager.o
                0x00000000e8c01856                _host_load
 .text._flash_load
                0x00000000e8c018e4       0x1c emodules/drv_dab/dab_module/si463x/Firmware_API_Manager.o
                0x00000000e8c018e4                _flash_load
 .text._adjust_NVMSPI_rate
                0x00000000e8c01900       0x1e emodules/drv_dab/dab_module/si463x/Firmware_API_Manager.o
                0x00000000e8c01900                _adjust_NVMSPI_rate
 .text._load_bootloader_patch__flash_load_mini
                0x00000000e8c0191e       0x4e emodules/drv_dab/dab_module/si463x/Firmware_API_Manager.o
                0x00000000e8c0191e                _load_bootloader_patch__flash_load_mini
 .text._load_firmware__flash_load_mini
                0x00000000e8c0196c       0x40 emodules/drv_dab/dab_module/si463x/Firmware_API_Manager.o
                0x00000000e8c0196c                _load_firmware__flash_load_mini
 .text._si46xx_initialize
                0x00000000e8c019ac       0x94 emodules/drv_dab/dab_module/si463x/Firmware_API_Manager.o
                0x00000000e8c019ac                _si46xx_initialize
 .text._si46xx_configure_properties
                0x00000000e8c01a40      0x286 emodules/drv_dab/dab_module/si463x/Firmware_API_Manager.o
                0x00000000e8c01a40                _si46xx_configure_properties
 .text._empty_service_list
                0x00000000e8c01cc6       0x26 emodules/drv_dab/dab_module/si463x/Firmware_API_Manager.o
                0x00000000e8c01cc6                _empty_service_list
 .text._empty_status
                0x00000000e8c01cec        0xa emodules/drv_dab/dab_module/si463x/Firmware_API_Manager.o
                0x00000000e8c01cec                _empty_status
 .text._update_status
                0x00000000e8c01cf6        0xc emodules/drv_dab/dab_module/si463x/Firmware_API_Manager.o
                0x00000000e8c01cf6                _update_status
 .text._deleteNode
                0x00000000e8c01d02       0x42 emodules/drv_dab/dab_module/si463x/Firmware_API_Manager.o
                0x00000000e8c01d02                _deleteNode
 .text._empty_metrics_fm
                0x00000000e8c01d44       0x12 emodules/drv_dab/dab_module/si463x/Firmware_API_Manager.o
                0x00000000e8c01d44                _empty_metrics_fm
 .text._empty_metrics_am
                0x00000000e8c01d56       0x12 emodules/drv_dab/dab_module/si463x/Firmware_API_Manager.o
                0x00000000e8c01d56                _empty_metrics_am
 .text._empty_metrics_fmhd
                0x00000000e8c01d68       0x26 emodules/drv_dab/dab_module/si463x/Firmware_API_Manager.o
                0x00000000e8c01d68                _empty_metrics_fmhd
 .text._empty_metrics_amhd
                0x00000000e8c01d8e       0x22 emodules/drv_dab/dab_module/si463x/Firmware_API_Manager.o
                0x00000000e8c01d8e                _empty_metrics_amhd
 .text._empty_metrics_dab
                0x00000000e8c01db0       0x2a emodules/drv_dab/dab_module/si463x/Firmware_API_Manager.o
                0x00000000e8c01db0                _empty_metrics_dab
 .text._update_SIS_worker
                0x00000000e8c01dda       0x2a emodules/drv_dab/dab_module/si463x/Firmware_API_Manager.o
                0x00000000e8c01dda                _update_SIS_worker
 .text._update_HD_service_list_worker
                0x00000000e8c01e04       0x46 emodules/drv_dab/dab_module/si463x/Firmware_API_Manager.o
                0x00000000e8c01e04                _update_HD_service_list_worker
 .text._update_current_service_list_index_fmhd
                0x00000000e8c01e4a       0x52 emodules/drv_dab/dab_module/si463x/Firmware_API_Manager.o
                0x00000000e8c01e4a                _update_current_service_list_index_fmhd
 .text.is_signal_strong_to_start_audio
                0x00000000e8c01e9c       0x3e emodules/drv_dab/dab_module/si463x/Firmware_API_Manager.o
                0x00000000e8c01e9c                is_signal_strong_to_start_audio
 .text._DAB_tune
                0x00000000e8c01eda       0x40 emodules/drv_dab/dab_module/si463x/Firmware_API_Manager.o
                0x00000000e8c01eda                _DAB_tune
 .text._service_linking_clear_service_links
                0x00000000e8c01f1a        0xa emodules/drv_dab/dab_module/si463x/Firmware_API_Manager.o
                0x00000000e8c01f1a                _service_linking_clear_service_links
 .text._service_linking_update_freq_info
                0x00000000e8c01f24      0x144 emodules/drv_dab/dab_module/si463x/Firmware_API_Manager.o
                0x00000000e8c01f24                _service_linking_update_freq_info
 .text._service_linking_add_linking_info_to_list_element
                0x00000000e8c02068       0x40 emodules/drv_dab/dab_module/si463x/Firmware_API_Manager.o
                0x00000000e8c02068                _service_linking_add_linking_info_to_list_element
 .text._update_DAB_service_following_worker
                0x00000000e8c020a8      0x174 emodules/drv_dab/dab_module/si463x/Firmware_API_Manager.o
                0x00000000e8c020a8                _update_DAB_service_following_worker
 .text.dab_linkset_init
                0x00000000e8c0221c       0x3a emodules/drv_dab/dab_module/si463x/Firmware_API_Manager.o
                0x00000000e8c0221c                dab_linkset_init
 .text.get_hard_rds_linkset
                0x00000000e8c02256       0x54 emodules/drv_dab/dab_module/si463x/Firmware_API_Manager.o
                0x00000000e8c02256                get_hard_rds_linkset
 .text._deactive_rds_linkage_set_by_sid
                0x00000000e8c022aa       0x32 emodules/drv_dab/dab_module/si463x/Firmware_API_Manager.o
                0x00000000e8c022aa                _deactive_rds_linkage_set_by_sid
 .text._clear_all_fig021024_help_linking_info
                0x00000000e8c022dc       0x5a emodules/drv_dab/dab_module/si463x/Firmware_API_Manager.o
                0x00000000e8c022dc                _clear_all_fig021024_help_linking_info
 .text._update_DAB_DAB_RDS_service_linking
                0x00000000e8c02336       0x48 emodules/drv_dab/dab_module/si463x/Firmware_API_Manager.o
                0x00000000e8c02336                _update_DAB_DAB_RDS_service_linking
 .text._update_DAB_service_linkage_set
                0x00000000e8c0237e      0x1f0 emodules/drv_dab/dab_module/si463x/Firmware_API_Manager.o
                0x00000000e8c0237e                _update_DAB_service_linkage_set
 .text.Initialize
                0x00000000e8c0256e       0xe8 emodules/drv_dab/dab_module/si463x/Firmware_API_Manager.o
                0x00000000e8c0256e                Initialize
 .text.Tune     0x00000000e8c02656      0x124 emodules/drv_dab/dab_module/si463x/Firmware_API_Manager.o
                0x00000000e8c02656                Tune
 .text.SeekStop
                0x00000000e8c0277a       0x44 emodules/drv_dab/dab_module/si463x/Firmware_API_Manager.o
                0x00000000e8c0277a                SeekStop
 .text.BrowseServicesReset_DAB
                0x00000000e8c027be       0x16 emodules/drv_dab/dab_module/si463x/Firmware_API_Manager.o
                0x00000000e8c027be                BrowseServicesReset_DAB
 .text._DAB_update_metrics_current_service_in_list
                0x00000000e8c027d4       0x6a emodules/drv_dab/dab_module/si463x/Firmware_API_Manager.o
                0x00000000e8c027d4                _DAB_update_metrics_current_service_in_list
 .text._update_DAB_service_list_worker
                0x00000000e8c0283e       0xfe emodules/drv_dab/dab_module/si463x/Firmware_API_Manager.o
                0x00000000e8c0283e                _update_DAB_service_list_worker
 .text._dab_browse_step_up_down_worker
                0x00000000e8c0293c       0x3e emodules/drv_dab/dab_module/si463x/Firmware_API_Manager.o
                0x00000000e8c0293c                _dab_browse_step_up_down_worker
 .text.SaveServiceList_para
                0x00000000e8c0297a       0xe2 emodules/drv_dab/dab_module/si463x/Firmware_API_Manager.o
                0x00000000e8c0297a                SaveServiceList_para
 .text.GetServiceList_para
                0x00000000e8c02a5c       0xe2 emodules/drv_dab/dab_module/si463x/Firmware_API_Manager.o
                0x00000000e8c02a5c                GetServiceList_para
 .text.SaveServiceList_DAB
                0x00000000e8c02b3e       0x84 emodules/drv_dab/dab_module/si463x/Firmware_API_Manager.o
                0x00000000e8c02b3e                SaveServiceList_DAB
 .text.ebu_to_utf16
                0x00000000e8c02bc2       0x24 emodules/drv_dab/dab_module/si463x/Firmware_API_Manager.o
                0x00000000e8c02bc2                ebu_to_utf16
 .text.decode_str.part.9
                0x00000000e8c02be6       0x4c emodules/drv_dab/dab_module/si463x/Firmware_API_Manager.o
 .text.decode_str
                0x00000000e8c02c32       0x46 emodules/drv_dab/dab_module/si463x/Firmware_API_Manager.o
                0x00000000e8c02c32                decode_str
 .text._return_service_string_info_from_service_list
                0x00000000e8c02c78       0xcc emodules/drv_dab/dab_module/si463x/Firmware_API_Manager.o
                0x00000000e8c02c78                _return_service_string_info_from_service_list
 .text.BrowseServicesChangeSelection_DAB
                0x00000000e8c02d44      0x126 emodules/drv_dab/dab_module/si463x/Firmware_API_Manager.o
                0x00000000e8c02d44                BrowseServicesChangeSelection_DAB
 .text.GetCurrentServiceString_DAB
                0x00000000e8c02e6a       0x16 emodules/drv_dab/dab_module/si463x/Firmware_API_Manager.o
                0x00000000e8c02e6a                GetCurrentServiceString_DAB
 .text.GetlistServiceString_DAB
                0x00000000e8c02e80      0x15a emodules/drv_dab/dab_module/si463x/Firmware_API_Manager.o
                0x00000000e8c02e80                GetlistServiceString_DAB
 .text.LoadServiceList_DAB
                0x00000000e8c02fda      0x17c emodules/drv_dab/dab_module/si463x/Firmware_API_Manager.o
                0x00000000e8c02fda                LoadServiceList_DAB
 .text.GetCurrentEnsembleNameString_DAB
                0x00000000e8c03156       0x48 emodules/drv_dab/dab_module/si463x/Firmware_API_Manager.o
                0x00000000e8c03156                GetCurrentEnsembleNameString_DAB
 .text.GetCurrentTime_DAB
                0x00000000e8c0319e       0x54 emodules/drv_dab/dab_module/si463x/Firmware_API_Manager.o
                0x00000000e8c0319e                GetCurrentTime_DAB
 .text.AudioLevel
                0x00000000e8c031f2       0x1e emodules/drv_dab/dab_module/si463x/Firmware_API_Manager.o
                0x00000000e8c031f2                AudioLevel
 .text.AudioMute
                0x00000000e8c03210       0x14 emodules/drv_dab/dab_module/si463x/Firmware_API_Manager.o
                0x00000000e8c03210                AudioMute
 .text.Get_part_ex
                0x00000000e8c03224       0x1e emodules/drv_dab/dab_module/si463x/Firmware_API_Manager.o
                0x00000000e8c03224                Get_part_ex
 .text.Get_function_info
                0x00000000e8c03242       0x52 emodules/drv_dab/dab_module/si463x/Firmware_API_Manager.o
                0x00000000e8c03242                Get_function_info
 .text.UpdateMetrics
                0x00000000e8c03294      0x2fe emodules/drv_dab/dab_module/si463x/Firmware_API_Manager.o
                0x00000000e8c03294                UpdateMetrics
 .text.UpdateServiceList
                0x00000000e8c03592       0x76 emodules/drv_dab/dab_module/si463x/Firmware_API_Manager.o
                0x00000000e8c03592                UpdateServiceList
 .text._DAB_confirm_service_list_complete
                0x00000000e8c03608       0x58 emodules/drv_dab/dab_module/si463x/Firmware_API_Manager.o
                0x00000000e8c03608                _DAB_confirm_service_list_complete
 .text.UpdateDataServiceData
                0x00000000e8c03660      0x244 emodules/drv_dab/dab_module/si463x/Firmware_API_Manager.o
                0x00000000e8c03660                UpdateDataServiceData
 .text.StartProcessingChannel
                0x00000000e8c038a4      0x126 emodules/drv_dab/dab_module/si463x/Firmware_API_Manager.o
                0x00000000e8c038a4                StartProcessingChannel
 .text.dab_find_hardlink_by_tune_onlyone_freq_play_service
                0x00000000e8c039ca       0x74 emodules/drv_dab/dab_module/si463x/Firmware_API_Manager.o
 .text.StopProcessingChannel
                0x00000000e8c03a3e       0xa4 emodules/drv_dab/dab_module/si463x/Firmware_API_Manager.o
                0x00000000e8c03a3e                StopProcessingChannel
 .text.GetSi46XXStatus
                0x00000000e8c03ae2       0x8c emodules/drv_dab/dab_module/si463x/Firmware_API_Manager.o
                0x00000000e8c03ae2                GetSi46XXStatus
 .text.SeekTuneCompleteCheck
                0x00000000e8c03b6e       0x10 emodules/drv_dab/dab_module/si463x/Firmware_API_Manager.o
                0x00000000e8c03b6e                SeekTuneCompleteCheck
 .text._dab_wait_stc_complete
                0x00000000e8c03b7e       0x48 emodules/drv_dab/dab_module/si463x/Firmware_API_Manager.o
                0x00000000e8c03b7e                _dab_wait_stc_complete
 .text._DAB_confirm_tune_complete
                0x00000000e8c03bc6       0x9c emodules/drv_dab/dab_module/si463x/Firmware_API_Manager.o
                0x00000000e8c03bc6                _DAB_confirm_tune_complete
 .text._DAB_start_service_worker
                0x00000000e8c03c62       0x80 emodules/drv_dab/dab_module/si463x/Firmware_API_Manager.o
                0x00000000e8c03c62                _DAB_start_service_worker
 .text._DAB_start_service_worker_ex
                0x00000000e8c03ce2      0x1dc emodules/drv_dab/dab_module/si463x/Firmware_API_Manager.o
                0x00000000e8c03ce2                _DAB_start_service_worker_ex
 .text.dab_dolinking_hard_dab_fig021024_helper
                0x00000000e8c03ebe       0x5a emodules/drv_dab/dab_module/si463x/Firmware_API_Manager.o
                0x00000000e8c03ebe                dab_dolinking_hard_dab_fig021024_helper
 .text.dab_dolinking_soft_dab_fig021024_helper
                0x00000000e8c03f18       0x5a emodules/drv_dab/dab_module/si463x/Firmware_API_Manager.o
                0x00000000e8c03f18                dab_dolinking_soft_dab_fig021024_helper
 .text._DAB_tune_full_process
                0x00000000e8c03f72       0x32 emodules/drv_dab/dab_module/si463x/Firmware_API_Manager.o
                0x00000000e8c03f72                _DAB_tune_full_process
 .text.dab_find_curr_service_hardlink_by_tune_onlyone_freq
                0x00000000e8c03fa4      0x1c0 emodules/drv_dab/dab_module/si463x/Firmware_API_Manager.o
                0x00000000e8c03fa4                dab_find_curr_service_hardlink_by_tune_onlyone_freq
 .text._DAB_start_service_from_list
                0x00000000e8c04164      0x2b4 emodules/drv_dab/dab_module/si463x/Firmware_API_Manager.o
                0x00000000e8c04164                _DAB_start_service_from_list
 .text.dab_dolinking_hard_dab_A0A1A2
                0x00000000e8c04418       0x2c emodules/drv_dab/dab_module/si463x/Firmware_API_Manager.o
                0x00000000e8c04418                dab_dolinking_hard_dab_A0A1A2
 .text.StartLastServiceByIndex
                0x00000000e8c04444       0x36 emodules/drv_dab/dab_module/si463x/Firmware_API_Manager.o
                0x00000000e8c04444                StartLastServiceByIndex
 .text.BrowseServicesStartCurrentSelection_DAB
                0x00000000e8c0447a       0x44 emodules/drv_dab/dab_module/si463x/Firmware_API_Manager.o
                0x00000000e8c0447a                BrowseServicesStartCurrentSelection_DAB
 .text.SLStartServiceByIndex
                0x00000000e8c044be       0x46 emodules/drv_dab/dab_module/si463x/Firmware_API_Manager.o
                0x00000000e8c044be                SLStartServiceByIndex
 .text.dab_find_link_by_match_sid_inlist.isra.15
                0x00000000e8c04504      0x1e0 emodules/drv_dab/dab_module/si463x/Firmware_API_Manager.o
 .text.dab_find_curr_service_soft_by_match_sid_inlist
                0x00000000e8c046e4       0x1e emodules/drv_dab/dab_module/si463x/Firmware_API_Manager.o
                0x00000000e8c046e4                dab_find_curr_service_soft_by_match_sid_inlist
 .text.dab_find_curr_service_hard_by_match_sid_inlist
                0x00000000e8c04702       0x1e emodules/drv_dab/dab_module/si463x/Firmware_API_Manager.o
                0x00000000e8c04702                dab_find_curr_service_hard_by_match_sid_inlist
 .text.dab_check_curr_service_soft_by_match_sid_inlist
                0x00000000e8c04720      0x146 emodules/drv_dab/dab_module/si463x/Firmware_API_Manager.o
                0x00000000e8c04720                dab_check_curr_service_soft_by_match_sid_inlist
 .text.clear_dab_announcement
                0x00000000e8c04866       0x4e emodules/drv_dab/dab_module/si463x/Firmware_API_Manager.o
                0x00000000e8c04866                clear_dab_announcement
 .text.init_dab_announcement
                0x00000000e8c048b4       0x18 emodules/drv_dab/dab_module/si463x/Firmware_API_Manager.o
                0x00000000e8c048b4                init_dab_announcement
 .text.dab_get_announcement_support
                0x00000000e8c048cc       0x7e emodules/drv_dab/dab_module/si463x/Firmware_API_Manager.o
                0x00000000e8c048cc                dab_get_announcement_support
 .text.dab_get_announcement_switching
                0x00000000e8c0494a       0x5a emodules/drv_dab/dab_module/si463x/Firmware_API_Manager.o
                0x00000000e8c0494a                dab_get_announcement_switching
 .text.MetricsGetFMPtr
                0x00000000e8c049a4        0xa emodules/drv_dab/dab_module/si463x/Firmware_API_Manager.o
                0x00000000e8c049a4                MetricsGetFMPtr
 .text.MetricsGetAMPtr
                0x00000000e8c049ae        0xa emodules/drv_dab/dab_module/si463x/Firmware_API_Manager.o
                0x00000000e8c049ae                MetricsGetAMPtr
 .text.MetricsGetAMHDPtr
                0x00000000e8c049b8        0xa emodules/drv_dab/dab_module/si463x/Firmware_API_Manager.o
                0x00000000e8c049b8                MetricsGetAMHDPtr
 .text.MetricsGetFMHDPtr
                0x00000000e8c049c2        0xa emodules/drv_dab/dab_module/si463x/Firmware_API_Manager.o
                0x00000000e8c049c2                MetricsGetFMHDPtr
 .text.MetricsGetDABPtr
                0x00000000e8c049cc        0xa emodules/drv_dab/dab_module/si463x/Firmware_API_Manager.o
                0x00000000e8c049cc                MetricsGetDABPtr
 .text.firmwareSetImage
                0x00000000e8c049d6       0x28 emodules/drv_dab/dab_module/si463x/Firmware_Load_Helpers.o
                0x00000000e8c049d6                firmwareSetImage
 .text.firmwareLoadOver
                0x00000000e8c049fe        0x2 emodules/drv_dab/dab_module/si463x/Firmware_Load_Helpers.o
                0x00000000e8c049fe                firmwareLoadOver
 .text.firmwareGetSegment
                0x00000000e8c04a00       0xcc emodules/drv_dab/dab_module/si463x/Firmware_Load_Helpers.o
                0x00000000e8c04a00                firmwareGetSegment
 .text.firmwareGetFlashAddress
                0x00000000e8c04acc       0x6a emodules/drv_dab/dab_module/si463x/Firmware_Load_Helpers.o
                0x00000000e8c04acc                firmwareGetFlashAddress
 .text._update_generic_psd
                0x00000000e8c04b36       0x22 emodules/drv_dab/dab_module/si463x/FMHD_PSD_Handler.o
                0x00000000e8c04b36                _update_generic_psd
 .text.initPSD  0x00000000e8c04b58       0xa0 emodules/drv_dab/dab_module/si463x/FMHD_PSD_Handler.o
                0x00000000e8c04b58                initPSD
 .text.updatePSD
                0x00000000e8c04bf8       0xca emodules/drv_dab/dab_module/si463x/FMHD_PSD_Handler.o
                0x00000000e8c04bf8                updatePSD
 .text.initHD_ServiceList
                0x00000000e8c04cc2       0x48 emodules/drv_dab/dab_module/si463x/FMHD_Service_List_Handler.o
                0x00000000e8c04cc2                initHD_ServiceList
 .text.updateFMHD_ServiceList_Audio
                0x00000000e8c04d0a      0x26e emodules/drv_dab/dab_module/si463x/FMHD_Service_List_Handler.o
                0x00000000e8c04d0a                updateFMHD_ServiceList_Audio
 .text.initSIS  0x00000000e8c04f78       0xb8 emodules/drv_dab/dab_module/si463x/FMHD_SIS_Handler.o
                0x00000000e8c04f78                initSIS
 .text.updateSIS
                0x00000000e8c05030      0x20a emodules/drv_dab/dab_module/si463x/FMHD_SIS_Handler.o
                0x00000000e8c05030                updateSIS
 .text.initAlerts
                0x00000000e8c0523a       0x3a emodules/drv_dab/dab_module/si463x/FMHD_Alert_Handler.o
                0x00000000e8c0523a                initAlerts
 .text.updateAlerts
                0x00000000e8c05274       0xc2 emodules/drv_dab/dab_module/si463x/FMHD_Alert_Handler.o
                0x00000000e8c05274                updateAlerts
 .text.si4384_set_reset_io
                0x00000000e8c05336       0x68 emodules/drv_dab/dab_module/si463x/HAL.o
                0x00000000e8c05336                si4384_set_reset_io
 .text.initHardware
                0x00000000e8c0539e       0x10 emodules/drv_dab/dab_module/si463x/HAL.o
                0x00000000e8c0539e                initHardware
 .text.uninitHardware
                0x00000000e8c053ae       0x10 emodules/drv_dab/dab_module/si463x/HAL.o
                0x00000000e8c053ae                uninitHardware
 .text.wait_for_CTS
                0x00000000e8c053be       0x4a emodules/drv_dab/dab_module/si463x/HAL.o
                0x00000000e8c053be                wait_for_CTS
 .text.writeCommand
                0x00000000e8c05408       0x8a emodules/drv_dab/dab_module/si463x/HAL.o
                0x00000000e8c05408                writeCommand
 .text.readReply
                0x00000000e8c05492       0x26 emodules/drv_dab/dab_module/si463x/HAL.o
                0x00000000e8c05492                readReply
 .text.calibrationGetPowerUpArgs
                0x00000000e8c054b8       0x32 emodules/drv_dab/dab_module/si463x/Hardware_Calibration_Helpers.o
                0x00000000e8c054b8                calibrationGetPowerUpArgs
 .text.calibrationGetFrontEndArgs
                0x00000000e8c054ea       0x4c emodules/drv_dab/dab_module/si463x/Hardware_Calibration_Helpers.o
                0x00000000e8c054ea                calibrationGetFrontEndArgs
 .text.T_PTY_Clear
                0x00000000e8c05536       0x12 emodules/drv_dab/dab_module/si463x/rds.o
                0x00000000e8c05536                T_PTY_Clear
 .text.T_TPTA_Clear
                0x00000000e8c05548       0x22 emodules/drv_dab/dab_module/si463x/rds.o
                0x00000000e8c05548                T_TPTA_Clear
 .text.T_Caculate_SMeter
                0x00000000e8c0556a       0x66 emodules/drv_dab/dab_module/si463x/rds.o
                0x00000000e8c0556a                T_Caculate_SMeter
 .text.update_pi
                0x00000000e8c055d0       0x7a emodules/drv_dab/dab_module/si463x/rds.o
                0x00000000e8c055d0                update_pi
 .text.T_Network_af_count
                0x00000000e8c0564a       0x30 emodules/drv_dab/dab_module/si463x/rds.o
                0x00000000e8c0564a                T_Network_af_count
 .text.T_Network_set_sequence
                0x00000000e8c0567a      0x16c emodules/drv_dab/dab_module/si463x/rds.o
                0x00000000e8c0567a                T_Network_set_sequence
 .text.T_Network_get_frq
                0x00000000e8c057e6       0x22 emodules/drv_dab/dab_module/si463x/rds.o
                0x00000000e8c057e6                T_Network_get_frq
 .text.T_Network_clear
                0x00000000e8c05808       0xd0 emodules/drv_dab/dab_module/si463x/rds.o
                0x00000000e8c05808                T_Network_clear
 .text.T_RDS_Reset
                0x00000000e8c058d8       0x5a emodules/drv_dab/dab_module/si463x/rds.o
                0x00000000e8c058d8                T_RDS_Reset
 .text.T_Network_lookup_af
                0x00000000e8c05932       0x36 emodules/drv_dab/dab_module/si463x/rds.o
                0x00000000e8c05932                T_Network_lookup_af
 .text.T_Network_pi_cmp
                0x00000000e8c05968       0x2a emodules/drv_dab/dab_module/si463x/rds.o
                0x00000000e8c05968                T_Network_pi_cmp
 .text.T_Network_netpi
                0x00000000e8c05992        0xa emodules/drv_dab/dab_module/si463x/rds.o
                0x00000000e8c05992                T_Network_netpi
 .text.T_Network_lookup_pi
                0x00000000e8c0599c       0x56 emodules/drv_dab/dab_module/si463x/rds.o
                0x00000000e8c0599c                T_Network_lookup_pi
 .text.T_Network_af_store
                0x00000000e8c059f2      0x18a emodules/drv_dab/dab_module/si463x/rds.o
                0x00000000e8c059f2                T_Network_af_store
 .text.T_Init_Rds_para
                0x00000000e8c05b7c       0x34 emodules/drv_dab/dab_module/si463x/rds.o
                0x00000000e8c05b7c                T_Init_Rds_para
 .text.T_Network_get_ctx
                0x00000000e8c05bb0       0x38 emodules/drv_dab/dab_module/si463x/rds.o
                0x00000000e8c05bb0                T_Network_get_ctx
 .text.T_Network_resume
                0x00000000e8c05be8       0x30 emodules/drv_dab/dab_module/si463x/rds.o
                0x00000000e8c05be8                T_Network_resume
 .text.T_Rds_QualityCheck
                0x00000000e8c05c18      0x2fc emodules/drv_dab/dab_module/si463x/rds.o
                0x00000000e8c05c18                T_Rds_QualityCheck
 .text.update_ps
                0x00000000e8c05f14      0x108 emodules/drv_dab/dab_module/si463x/RDS_Handler.o
 .text.rds_update_mcu_time
                0x00000000e8c0601c       0x78 emodules/drv_dab/dab_module/si463x/RDS_Handler.o
 .text.display_rt
                0x00000000e8c06094       0x92 emodules/drv_dab/dab_module/si463x/RDS_Handler.o
 .text.update_rt_advance
                0x00000000e8c06126      0x1e0 emodules/drv_dab/dab_module/si463x/RDS_Handler.o
 .text.initRDS  0x00000000e8c06306      0x140 emodules/drv_dab/dab_module/si463x/RDS_Handler.o
                0x00000000e8c06306                initRDS
 .text.TA_Read  0x00000000e8c06446       0x6c emodules/drv_dab/dab_module/si463x/RDS_Handler.o
                0x00000000e8c06446                TA_Read
 .text.updateRDS
                0x00000000e8c064b2      0x476 emodules/drv_dab/dab_module/si463x/RDS_Handler.o
                0x00000000e8c064b2                updateRDS
 .text._recover_32bit_from_little_endian_buffer
                0x00000000e8c06928        0x4 emodules/drv_dab/dab_module/si463x/si46xx_firmware_api.o
                0x00000000e8c06928                _recover_32bit_from_little_endian_buffer
 .text._recover_16bit_from_little_endian_buffer
                0x00000000e8c0692c        0x6 emodules/drv_dab/dab_module/si463x/si46xx_firmware_api.o
                0x00000000e8c0692c                _recover_16bit_from_little_endian_buffer
 .text.read_status
                0x00000000e8c06932       0x9a emodules/drv_dab/dab_module/si463x/si46xx_firmware_api.o
                0x00000000e8c06932                read_status
 .text.power_up__command
                0x00000000e8c069cc       0xec emodules/drv_dab/dab_module/si463x/si46xx_firmware_api.o
                0x00000000e8c069cc                power_up__command
 .text.host_load__command
                0x00000000e8c06ab8       0x68 emodules/drv_dab/dab_module/si463x/si46xx_firmware_api.o
                0x00000000e8c06ab8                host_load__command
 .text.flash_load__command
                0x00000000e8c06b20       0x4c emodules/drv_dab/dab_module/si463x/si46xx_firmware_api.o
                0x00000000e8c06b20                flash_load__command
 .text.load_init__command
                0x00000000e8c06b6c       0x3e emodules/drv_dab/dab_module/si463x/si46xx_firmware_api.o
                0x00000000e8c06b6c                load_init__command
 .text.boot__command
                0x00000000e8c06baa       0x3e emodules/drv_dab/dab_module/si463x/si46xx_firmware_api.o
                0x00000000e8c06baa                boot__command
 .text.read_offset__command
                0x00000000e8c06be8       0x52 emodules/drv_dab/dab_module/si463x/si46xx_firmware_api.o
                0x00000000e8c06be8                read_offset__command
 .text.get_part_info__command
                0x00000000e8c06c3a       0x3e emodules/drv_dab/dab_module/si463x/si46xx_firmware_api.o
                0x00000000e8c06c3a                get_part_info__command
 .text.get_part_info__reply
                0x00000000e8c06c78       0x60 emodules/drv_dab/dab_module/si463x/si46xx_firmware_api.o
                0x00000000e8c06c78                get_part_info__reply
 .text.get_sys_state__command
                0x00000000e8c06cd8       0x3e emodules/drv_dab/dab_module/si463x/si46xx_firmware_api.o
                0x00000000e8c06cd8                get_sys_state__command
 .text.get_sys_state__reply
                0x00000000e8c06d16       0x44 emodules/drv_dab/dab_module/si463x/si46xx_firmware_api.o
                0x00000000e8c06d16                get_sys_state__reply
 .text.get_func_info__command
                0x00000000e8c06d5a       0x3e emodules/drv_dab/dab_module/si463x/si46xx_firmware_api.o
                0x00000000e8c06d5a                get_func_info__command
 .text.get_func_info__reply
                0x00000000e8c06d98       0x9a emodules/drv_dab/dab_module/si463x/si46xx_firmware_api.o
                0x00000000e8c06d98                get_func_info__reply
 .text.set_property__command
                0x00000000e8c06e32       0x5e emodules/drv_dab/dab_module/si463x/si46xx_firmware_api.o
                0x00000000e8c06e32                set_property__command
 .text.get_property__command
                0x00000000e8c06e90       0x5a emodules/drv_dab/dab_module/si463x/si46xx_firmware_api.o
                0x00000000e8c06e90                get_property__command
 .text.get_property__reply
                0x00000000e8c06eea       0x48 emodules/drv_dab/dab_module/si463x/si46xx_firmware_api.o
                0x00000000e8c06eea                get_property__reply
 .text.get_digital_service_list__command
                0x00000000e8c06f32       0x60 emodules/drv_dab/dab_module/si463x/si46xx_firmware_api.o
                0x00000000e8c06f32                get_digital_service_list__command
 .text.get_digital_service_list__reply
                0x00000000e8c06f92      0x14e emodules/drv_dab/dab_module/si463x/si46xx_firmware_api.o
                0x00000000e8c06f92                get_digital_service_list__reply
 .text.realign_service_list_buffer
                0x00000000e8c070e0       0x4a emodules/drv_dab/dab_module/si463x/si46xx_firmware_api.o
                0x00000000e8c070e0                realign_service_list_buffer
 .text.start_digital_service__command
                0x00000000e8c0712a       0x72 emodules/drv_dab/dab_module/si463x/si46xx_firmware_api.o
                0x00000000e8c0712a                start_digital_service__command
 .text.stop_digital_service__command
                0x00000000e8c0719c       0x72 emodules/drv_dab/dab_module/si463x/si46xx_firmware_api.o
                0x00000000e8c0719c                stop_digital_service__command
 .text.get_digital_service_data__command
                0x00000000e8c0720e       0x72 emodules/drv_dab/dab_module/si463x/si46xx_firmware_api.o
                0x00000000e8c0720e                get_digital_service_data__command
 .text.get_digital_service_data__reply
                0x00000000e8c07280       0xe4 emodules/drv_dab/dab_module/si463x/si46xx_firmware_api.o
                0x00000000e8c07280                get_digital_service_data__reply
 .text.fm_am_tune_freq__command
                0x00000000e8c07364       0xc8 emodules/drv_dab/dab_module/si463x/si46xx_firmware_api.o
                0x00000000e8c07364                fm_am_tune_freq__command
 .text.fm_rsq_status__command
                0x00000000e8c0742c       0xa4 emodules/drv_dab/dab_module/si463x/si46xx_firmware_api.o
                0x00000000e8c0742c                fm_rsq_status__command
 .text.fm_rsq_status__reply
                0x00000000e8c074d0       0xd6 emodules/drv_dab/dab_module/si463x/si46xx_firmware_api.o
                0x00000000e8c074d0                fm_rsq_status__reply
 .text.am_rsq_status__command
                0x00000000e8c075a6       0xa4 emodules/drv_dab/dab_module/si463x/si46xx_firmware_api.o
                0x00000000e8c075a6                am_rsq_status__command
 .text.am_rsq_status__reply
                0x00000000e8c0764a       0xd6 emodules/drv_dab/dab_module/si463x/si46xx_firmware_api.o
                0x00000000e8c0764a                am_rsq_status__reply
 .text.fm_rds_status__command
                0x00000000e8c07720       0x8e emodules/drv_dab/dab_module/si463x/si46xx_firmware_api.o
                0x00000000e8c07720                fm_rds_status__command
 .text.fm_rds_status__reply
                0x00000000e8c077ae      0x128 emodules/drv_dab/dab_module/si463x/si46xx_firmware_api.o
                0x00000000e8c077ae                fm_rds_status__reply
 .text.hd_digrad_status__command
                0x00000000e8c078d6       0x56 emodules/drv_dab/dab_module/si463x/si46xx_firmware_api.o
                0x00000000e8c078d6                hd_digrad_status__command
 .text.hd_digrad_status__reply
                0x00000000e8c0792c      0x15c emodules/drv_dab/dab_module/si463x/si46xx_firmware_api.o
                0x00000000e8c0792c                hd_digrad_status__reply
 .text.hd_get_event_status__command
                0x00000000e8c07a88       0x56 emodules/drv_dab/dab_module/si463x/si46xx_firmware_api.o
                0x00000000e8c07a88                hd_get_event_status__command
 .text.hd_get_event_status__reply
                0x00000000e8c07ade       0xfe emodules/drv_dab/dab_module/si463x/si46xx_firmware_api.o
                0x00000000e8c07ade                hd_get_event_status__reply
 .text.fmhd_get_psd_decode__command
                0x00000000e8c07bdc       0x58 emodules/drv_dab/dab_module/si463x/si46xx_firmware_api.o
                0x00000000e8c07bdc                fmhd_get_psd_decode__command
 .text.fmhd_get_psd_decode__reply
                0x00000000e8c07c34       0x7c emodules/drv_dab/dab_module/si463x/si46xx_firmware_api.o
                0x00000000e8c07c34                fmhd_get_psd_decode__reply
 .text.fmhd_get_alert_message__command
                0x00000000e8c07cb0       0x40 emodules/drv_dab/dab_module/si463x/si46xx_firmware_api.o
                0x00000000e8c07cb0                fmhd_get_alert_message__command
 .text.fmhd_get_alert_message__reply
                0x00000000e8c07cf0       0x7e emodules/drv_dab/dab_module/si463x/si46xx_firmware_api.o
                0x00000000e8c07cf0                fmhd_get_alert_message__reply
 .text.fmhd_play_alert_tone__command
                0x00000000e8c07d6e       0x40 emodules/drv_dab/dab_module/si463x/si46xx_firmware_api.o
                0x00000000e8c07d6e                fmhd_play_alert_tone__command
 .text.fmhd_get_station_info__command
                0x00000000e8c07dae       0x54 emodules/drv_dab/dab_module/si463x/si46xx_firmware_api.o
                0x00000000e8c07dae                fmhd_get_station_info__command
 .text.fmhd_get_station_info__reply
                0x00000000e8c07e02       0x7a emodules/drv_dab/dab_module/si463x/si46xx_firmware_api.o
                0x00000000e8c07e02                fmhd_get_station_info__reply
 .text.dab_tune_freq__command
                0x00000000e8c07e7c       0x76 emodules/drv_dab/dab_module/si463x/si46xx_firmware_api.o
                0x00000000e8c07e7c                dab_tune_freq__command
 .text.dab_digrad_status__command
                0x00000000e8c07ef2       0xa4 emodules/drv_dab/dab_module/si463x/si46xx_firmware_api.o
                0x00000000e8c07ef2                dab_digrad_status__command
 .text.dab_digrad_status__reply
                0x00000000e8c07f96       0xfc emodules/drv_dab/dab_module/si463x/si46xx_firmware_api.o
                0x00000000e8c07f96                dab_digrad_status__reply
 .text.dab_get_event_status__command
                0x00000000e8c08092       0x56 emodules/drv_dab/dab_module/si463x/si46xx_firmware_api.o
                0x00000000e8c08092                dab_get_event_status__command
 .text.dab_get_event_status__reply
                0x00000000e8c080e8       0xec emodules/drv_dab/dab_module/si463x/si46xx_firmware_api.o
                0x00000000e8c080e8                dab_get_event_status__reply
 .text.dab_get_ensemble_info__command
                0x00000000e8c081d4       0x40 emodules/drv_dab/dab_module/si463x/si46xx_firmware_api.o
                0x00000000e8c081d4                dab_get_ensemble_info__command
 .text.dab_get_ensemble_info__reply
                0x00000000e8c08214       0x76 emodules/drv_dab/dab_module/si463x/si46xx_firmware_api.o
                0x00000000e8c08214                dab_get_ensemble_info__reply
 .text.dab_get_announcement_support_info__command
                0x00000000e8c0828a       0x64 emodules/drv_dab/dab_module/si463x/si46xx_firmware_api.o
                0x00000000e8c0828a                dab_get_announcement_support_info__command
 .text.dab_get_announcement_support_info__reply
                0x00000000e8c082ee       0x7a emodules/drv_dab/dab_module/si463x/si46xx_firmware_api.o
                0x00000000e8c082ee                dab_get_announcement_support_info__reply
 .text.dab_get_announcement_info__command
                0x00000000e8c08368       0x46 emodules/drv_dab/dab_module/si463x/si46xx_firmware_api.o
                0x00000000e8c08368                dab_get_announcement_info__command
 .text.dab_get_announcement_info__reply
                0x00000000e8c083ae       0xbc emodules/drv_dab/dab_module/si463x/si46xx_firmware_api.o
                0x00000000e8c083ae                dab_get_announcement_info__reply
 .text.dab_get_freq_list__command
                0x00000000e8c0846a       0x40 emodules/drv_dab/dab_module/si463x/si46xx_firmware_api.o
                0x00000000e8c0846a                dab_get_freq_list__command
 .text.dab_get_freq_list__reply
                0x00000000e8c084aa       0xb2 emodules/drv_dab/dab_module/si463x/si46xx_firmware_api.o
                0x00000000e8c084aa                dab_get_freq_list__reply
 .text.dab_get_component_info__command
                0x00000000e8c0855c       0x5c emodules/drv_dab/dab_module/si463x/si46xx_firmware_api.o
                0x00000000e8c0855c                dab_get_component_info__command
 .text.dab_get_component_info__reply
                0x00000000e8c085b8       0xac emodules/drv_dab/dab_module/si463x/si46xx_firmware_api.o
                0x00000000e8c085b8                dab_get_component_info__reply
 .text.dab_get_time__command
                0x00000000e8c08664       0x4c emodules/drv_dab/dab_module/si463x/si46xx_firmware_api.o
                0x00000000e8c08664                dab_get_time__command
 .text.dab_get_time__reply
                0x00000000e8c086b0       0x80 emodules/drv_dab/dab_module/si463x/si46xx_firmware_api.o
                0x00000000e8c086b0                dab_get_time__reply
 .text.dab_get_audio_info__command
                0x00000000e8c08730       0x40 emodules/drv_dab/dab_module/si463x/si46xx_firmware_api.o
                0x00000000e8c08730                dab_get_audio_info__command
 .text.dab_get_audio_info__reply
                0x00000000e8c08770       0x8a emodules/drv_dab/dab_module/si463x/si46xx_firmware_api.o
                0x00000000e8c08770                dab_get_audio_info__reply
 .text.dab_get_service_linking_info__command
                0x00000000e8c087fa       0x66 emodules/drv_dab/dab_module/si463x/si46xx_firmware_api.o
                0x00000000e8c087fa                dab_get_service_linking_info__command
 .text.dab_get_service_linking_info__reply
                0x00000000e8c08860       0x8a emodules/drv_dab/dab_module/si463x/si46xx_firmware_api.o
                0x00000000e8c08860                dab_get_service_linking_info__reply
 .text.dab_get_service_linking_info__get_element
                0x00000000e8c088ea       0x8e emodules/drv_dab/dab_module/si463x/si46xx_firmware_api.o
                0x00000000e8c088ea                dab_get_service_linking_info__get_element
 .text.dab_get_freq_info__command
                0x00000000e8c08978       0x40 emodules/drv_dab/dab_module/si463x/si46xx_firmware_api.o
                0x00000000e8c08978                dab_get_freq_info__command
 .text.dab_get_freq_info__reply
                0x00000000e8c089b8       0x7a emodules/drv_dab/dab_module/si463x/si46xx_firmware_api.o
                0x00000000e8c089b8                dab_get_freq_info__reply
 .text.dab_get_oe_services_info__command
                0x00000000e8c08a32       0x4e emodules/drv_dab/dab_module/si463x/si46xx_firmware_api.o
                0x00000000e8c08a32                dab_get_oe_services_info__command
 .text.dab_get_oe_services_info__reply
                0x00000000e8c08a80       0x78 emodules/drv_dab/dab_module/si463x/si46xx_firmware_api.o
                0x00000000e8c08a80                dab_get_oe_services_info__reply
 .text.is_poor_signal_conditions_to_fm
                0x00000000e8c08af8       0x5a emodules/drv_dab/dab_module/si463x/si468x.o
 .text.dab_send_cmd2fm
                0x00000000e8c08b52       0x40 emodules/drv_dab/dab_module/si463x/si468x.o
 .text.set_current_audio_output
                0x00000000e8c08b92       0x38 emodules/drv_dab/dab_module/si463x/si468x.o
 .text.si4634_get_reg_handle
                0x00000000e8c08bca       0xa2 emodules/drv_dab/dab_module/si463x/si468x.o
 .text.dab_send_hard_rds_link
                0x00000000e8c08c6c       0x60 emodules/drv_dab/dab_module/si463x/si468x.o
 .text.prepare_hard_rds_link_atfirst
                0x00000000e8c08ccc       0x2a emodules/drv_dab/dab_module/si463x/si468x.o
 .text.try_switch_to_fm_audio
                0x00000000e8c08cf6       0x3c emodules/drv_dab/dab_module/si463x/si468x.o
 .text.si4634_lock_create
                0x00000000e8c08d32       0x24 emodules/drv_dab/dab_module/si463x/si468x.o
                0x00000000e8c08d32                si4634_lock_create
 .text.si4634_lock_delect
                0x00000000e8c08d56       0x32 emodules/drv_dab/dab_module/si463x/si468x.o
                0x00000000e8c08d56                si4634_lock_delect
 .text.si4634_OpLock
                0x00000000e8c08d88       0x1e emodules/drv_dab/dab_module/si463x/si468x.o
                0x00000000e8c08d88                si4634_OpLock
 .text.si4634_OpUnlock
                0x00000000e8c08da6        0xe emodules/drv_dab/dab_module/si463x/si468x.o
                0x00000000e8c08da6                si4634_OpUnlock
 .text.dab_break_rds_search_pi_link
                0x00000000e8c08db4        0xa emodules/drv_dab/dab_module/si463x/si468x.o
                0x00000000e8c08db4                dab_break_rds_search_pi_link
 .text.init_by_change_service
                0x00000000e8c08dbe       0x6c emodules/drv_dab/dab_module/si463x/si468x.o
 .text.set_on_dab_view_channel_list_flg
                0x00000000e8c08e2a       0x38 emodules/drv_dab/dab_module/si463x/si468x.o
                0x00000000e8c08e2a                set_on_dab_view_channel_list_flg
 .text.get_on_dab_view_channel_list_flg
                0x00000000e8c08e62       0x20 emodules/drv_dab/dab_module/si463x/si468x.o
                0x00000000e8c08e62                get_on_dab_view_channel_list_flg
 .text.dab_is_wait_CTS_TIMEOUT_reach_threshold
                0x00000000e8c08e82       0x10 emodules/drv_dab/dab_module/si463x/si468x.o
                0x00000000e8c08e82                dab_is_wait_CTS_TIMEOUT_reach_threshold
 .text.set_dab_fm_rds_hard_link_flag
                0x00000000e8c08e92        0xa emodules/drv_dab/dab_module/si463x/si468x.o
                0x00000000e8c08e92                set_dab_fm_rds_hard_link_flag
 .text.dab_switch_dab_output_if_rds_link
                0x00000000e8c08e9c       0x12 emodules/drv_dab/dab_module/si463x/si468x.o
                0x00000000e8c08e9c                dab_switch_dab_output_if_rds_link
 .text.set_on_dab_view_app_flg
                0x00000000e8c08eae       0x3e emodules/drv_dab/dab_module/si463x/si468x.o
                0x00000000e8c08eae                set_on_dab_view_app_flg
 .text.dab_save_init_para
                0x00000000e8c08eec      0x254 emodules/drv_dab/dab_module/si463x/si468x.o
                0x00000000e8c08eec                dab_save_init_para
 .text.dab_save_flush
                0x00000000e8c09140      0x11c emodules/drv_dab/dab_module/si463x/si468x.o
                0x00000000e8c09140                dab_save_flush
 .text.dab_save_erase
                0x00000000e8c0925c      0x10c emodules/drv_dab/dab_module/si463x/si468x.o
                0x00000000e8c0925c                dab_save_erase
 .text.get_scan_process_rate
                0x00000000e8c09368        0xa emodules/drv_dab/dab_module/si463x/si468x.o
                0x00000000e8c09368                get_scan_process_rate
 .text.TuneByNum
                0x00000000e8c09372       0x9a emodules/drv_dab/dab_module/si463x/si468x.o
                0x00000000e8c09372                TuneByNum
 .text.dab_back_to_old_service
                0x00000000e8c0940c       0xd0 emodules/drv_dab/dab_module/si463x/si468x.o
 .text.dab_announcement_comeback
                0x00000000e8c094dc       0x56 emodules/drv_dab/dab_module/si463x/si468x.o
 .text.FastTuneByNum
                0x00000000e8c09532       0xb4 emodules/drv_dab/dab_module/si463x/si468x.o
                0x00000000e8c09532                FastTuneByNum
 .text.get_dab_display_handle
                0x00000000e8c095e6        0xc emodules/drv_dab/dab_module/si463x/si468x.o
                0x00000000e8c095e6                get_dab_display_handle
 .text.GetDabCurrentStatus
                0x00000000e8c095f2       0x10 emodules/drv_dab/dab_module/si463x/si468x.o
                0x00000000e8c095f2                GetDabCurrentStatus
 .text.GetDabScanStatus
                0x00000000e8c09602       0x10 emodules/drv_dab/dab_module/si463x/si468x.o
                0x00000000e8c09602                GetDabScanStatus
 .text.GetSi4634CurrrntMode
                0x00000000e8c09612        0xe emodules/drv_dab/dab_module/si463x/si468x.o
                0x00000000e8c09612                GetSi4634CurrrntMode
 .text.dab_set_TI
                0x00000000e8c09620       0x36 emodules/drv_dab/dab_module/si463x/si468x.o
                0x00000000e8c09620                dab_set_TI
 .text.dab_set_announcement
                0x00000000e8c09656       0x44 emodules/drv_dab/dab_module/si463x/si468x.o
                0x00000000e8c09656                dab_set_announcement
 .text.dab_set_priority
                0x00000000e8c0969a       0x40 emodules/drv_dab/dab_module/si463x/si468x.o
                0x00000000e8c0969a                dab_set_priority
 .text.dab_set_related_service
                0x00000000e8c096da       0x20 emodules/drv_dab/dab_module/si463x/si468x.o
                0x00000000e8c096da                dab_set_related_service
 .text.get_dab_is_at_annnoucement_mode
                0x00000000e8c096fa        0xa emodules/drv_dab/dab_module/si463x/si468x.o
                0x00000000e8c096fa                get_dab_is_at_annnoucement_mode
 .text.get_dab_audio_output_channel
                0x00000000e8c09704        0xa emodules/drv_dab/dab_module/si463x/si468x.o
                0x00000000e8c09704                get_dab_audio_output_channel
 .text.GetCurrentTime
                0x00000000e8c0970e        0x8 emodules/drv_dab/dab_module/si463x/si468x.o
                0x00000000e8c0970e                GetCurrentTime
 .text.GetCurrentEnsembleNameString
                0x00000000e8c09716       0x60 emodules/drv_dab/dab_module/si463x/si468x.o
                0x00000000e8c09716                GetCurrentEnsembleNameString
 .text.GetCurrentRDS_PtyString
                0x00000000e8c09776       0x68 emodules/drv_dab/dab_module/si463x/si468x.o
                0x00000000e8c09776                GetCurrentRDS_PtyString
 .text.GetCurrentDAB_EID
                0x00000000e8c097de       0x40 emodules/drv_dab/dab_module/si463x/si468x.o
                0x00000000e8c097de                GetCurrentDAB_EID
 .text.GetCurrentDAB_ChannelNum
                0x00000000e8c0981e       0x6a emodules/drv_dab/dab_module/si463x/si468x.o
                0x00000000e8c0981e                GetCurrentDAB_ChannelNum
 .text.GetCurrentDAB_FreqIndex
                0x00000000e8c09888       0x52 emodules/drv_dab/dab_module/si463x/si468x.o
                0x00000000e8c09888                GetCurrentDAB_FreqIndex
 .text.dab_rescan_by_current_index
                0x00000000e8c098da        0x4 emodules/drv_dab/dab_module/si463x/si468x.o
                0x00000000e8c098da                dab_rescan_by_current_index
 .text.Init_SendMsgQueue
                0x00000000e8c098de       0x3c emodules/drv_dab/dab_module/si463x/si468x.o
                0x00000000e8c098de                Init_SendMsgQueue
 .text.Uninit_SendMsgQueue
                0x00000000e8c0991a       0x2c emodules/drv_dab/dab_module/si463x/si468x.o
                0x00000000e8c0991a                Uninit_SendMsgQueue
 .text.push_msg_to_queue
                0x00000000e8c09946       0x48 emodules/drv_dab/dab_module/si463x/si468x.o
                0x00000000e8c09946                push_msg_to_queue
 .text.pop_msg_from_queue
                0x00000000e8c0998e       0x56 emodules/drv_dab/dab_module/si463x/si468x.o
                0x00000000e8c0998e                pop_msg_from_queue
 .text.update_display_info
                0x00000000e8c099e4       0xce emodules/drv_dab/dab_module/si463x/si468x.o
                0x00000000e8c099e4                update_display_info
 .text.updateItemByCase
                0x00000000e8c09ab2       0x30 emodules/drv_dab/dab_module/si463x/si468x.o
                0x00000000e8c09ab2                updateItemByCase
 .text.SLStateCtrl
                0x00000000e8c09ae2       0x2e emodules/drv_dab/dab_module/si463x/si468x.o
                0x00000000e8c09ae2                SLStateCtrl
 .text.si4634_dab_mode_is_busy
                0x00000000e8c09b10       0x22 emodules/drv_dab/dab_module/si463x/si468x.o
                0x00000000e8c09b10                si4634_dab_mode_is_busy
 .text.si4634_dab_check_work_status
                0x00000000e8c09b32      0x2a2 emodules/drv_dab/dab_module/si463x/si468x.o
                0x00000000e8c09b32                si4634_dab_check_work_status
 .text.si4634_dab_check_current_service_index_freq_match
                0x00000000e8c09dd4       0x54 emodules/drv_dab/dab_module/si463x/si468x.o
                0x00000000e8c09dd4                si4634_dab_check_current_service_index_freq_match
 .text.si4634_dab_get_list_focus
                0x00000000e8c09e28      0x154 emodules/drv_dab/dab_module/si463x/si468x.o
                0x00000000e8c09e28                si4634_dab_get_list_focus
 .text.si4634_dab_check_current_service_dab_freq_match
                0x00000000e8c09f7c       0x8e emodules/drv_dab/dab_module/si463x/si468x.o
                0x00000000e8c09f7c                si4634_dab_check_current_service_dab_freq_match
 .text.si4634_dab_try_play_old_service
                0x00000000e8c0a00a      0x1ae emodules/drv_dab/dab_module/si463x/si468x.o
                0x00000000e8c0a00a                si4634_dab_try_play_old_service
 .text.si463x_thread_init
                0x00000000e8c0a1b8       0x3a emodules/drv_dab/dab_module/si463x/si468x.o
                0x00000000e8c0a1b8                si463x_thread_init
 .text.si463x_thread_uninit
                0x00000000e8c0a1f2       0x42 emodules/drv_dab/dab_module/si463x/si468x.o
                0x00000000e8c0a1f2                si463x_thread_uninit
 .text.si4634_send_data_to_mcu
                0x00000000e8c0a234       0x88 emodules/drv_dab/dab_module/si463x/si468x.o
                0x00000000e8c0a234                si4634_send_data_to_mcu
 .text.dab_goto_link_related_service_link
                0x00000000e8c0a2bc      0x15c emodules/drv_dab/dab_module/si463x/si468x.o
                0x00000000e8c0a2bc                dab_goto_link_related_service_link
 .text.dab_play_service
                0x00000000e8c0a418      0x134 emodules/drv_dab/dab_module/si463x/si468x.o
 .text.__pset_operate__
                0x00000000e8c0a54c      0x182 emodules/drv_dab/dab_module/si463x/si468x.o
                0x00000000e8c0a54c                __pset_operate__
 .text.si463x_dab_check_current_service_valid
                0x00000000e8c0a6ce       0x46 emodules/drv_dab/dab_module/si463x/si468x.o
                0x00000000e8c0a6ce                si463x_dab_check_current_service_valid
 .text.tuneToSpecificPsetByNum
                0x00000000e8c0a714      0x2dc emodules/drv_dab/dab_module/si463x/si468x.o
                0x00000000e8c0a714                tuneToSpecificPsetByNum
 .text.dab_exit_TI
                0x00000000e8c0a9f0      0x164 emodules/drv_dab/dab_module/si463x/si468x.o
                0x00000000e8c0a9f0                dab_exit_TI
 .text.dab_preset_add
                0x00000000e8c0ab54      0x14c emodules/drv_dab/dab_module/si463x/si468x.o
                0x00000000e8c0ab54                dab_preset_add
 .text.dab_pty_seek
                0x00000000e8c0aca0       0xc2 emodules/drv_dab/dab_module/si463x/si468x.o
                0x00000000e8c0aca0                dab_pty_seek
 .text.dab_initialize
                0x00000000e8c0ad62       0x2e emodules/drv_dab/dab_module/si463x/si468x.o
                0x00000000e8c0ad62                dab_initialize
 .text.am_initialize
                0x00000000e8c0ad90       0x28 emodules/drv_dab/dab_module/si463x/si468x.o
                0x00000000e8c0ad90                am_initialize
 .text.fm_initialize
                0x00000000e8c0adb8       0x28 emodules/drv_dab/dab_module/si463x/si468x.o
                0x00000000e8c0adb8                fm_initialize
 .text.dab_key_process
                0x00000000e8c0ade0      0x448 emodules/drv_dab/dab_module/si463x/si468x.o
                0x00000000e8c0ade0                dab_key_process
 .text.dab_check_reset_dab_request
                0x00000000e8c0b228       0x56 emodules/drv_dab/dab_module/si463x/si468x.o
                0x00000000e8c0b228                dab_check_reset_dab_request
 .text.si463x_dab_Mode_Proc
                0x00000000e8c0b27e     0x2494 emodules/drv_dab/dab_module/si463x/si468x.o
 .text.dab_para_init
                0x00000000e8c0d712       0x6e emodules/drv_dab/dab_module/si463x/si468x.o
                0x00000000e8c0d712                dab_para_init
 .text.dab_switch_band_load_and_play
                0x00000000e8c0d780      0x3b8 emodules/drv_dab/dab_module/si463x/si468x.o
                0x00000000e8c0d780                dab_switch_band_load_and_play
 .text.dab_switch_band
                0x00000000e8c0db38       0x92 emodules/drv_dab/dab_module/si463x/si468x.o
                0x00000000e8c0db38                dab_switch_band
 .text.dab_load_list_and_play
                0x00000000e8c0dbca      0x440 emodules/drv_dab/dab_module/si463x/si468x.o
                0x00000000e8c0dbca                dab_load_list_and_play
 .text.dab_para_uninit
                0x00000000e8c0e00a       0x16 emodules/drv_dab/dab_module/si463x/si468x.o
                0x00000000e8c0e00a                dab_para_uninit
 .text.dab_init
                0x00000000e8c0e020       0x3e emodules/drv_dab/dab_module/si463x/si468x.o
                0x00000000e8c0e020                dab_init
 .text.dab_exit
                0x00000000e8c0e05e       0x2e emodules/drv_dab/dab_module/si463x/si468x.o
                0x00000000e8c0e05e                dab_exit
 .text.psetTuneFreq
                0x00000000e8c0e08c       0xdc emodules/drv_dab/dab_module/si463x/si468x.o
                0x00000000e8c0e08c                psetTuneFreq
 .text.AutoSeekEnd
                0x00000000e8c0e168       0xc0 emodules/drv_dab/dab_module/si463x/si468x.o
                0x00000000e8c0e168                AutoSeekEnd
 .text.AutoSeekAbort
                0x00000000e8c0e228       0xb8 emodules/drv_dab/dab_module/si463x/si468x.o
                0x00000000e8c0e228                AutoSeekAbort
 .text.AutoSeekProcess
                0x00000000e8c0e2e0      0x320 emodules/drv_dab/dab_module/si463x/si468x.o
                0x00000000e8c0e2e0                AutoSeekProcess
 .text.AutoSeekTask
                0x00000000e8c0e600       0x2a emodules/drv_dab/dab_module/si463x/si468x.o
                0x00000000e8c0e600                AutoSeekTask
 .text.RadioAutoStore
                0x00000000e8c0e62a      0x3e2 emodules/drv_dab/dab_module/si463x/si468x.o
                0x00000000e8c0e62a                RadioAutoStore
 .text.Tuner_Set_Reg
                0x00000000e8c0ea0c      0x11a emodules/drv_dab/dab_module/si463x/si468x.o
                0x00000000e8c0ea0c                Tuner_Set_Reg
 .text.Tuner_Get_Reg
                0x00000000e8c0eb26      0x15e emodules/drv_dab/dab_module/si463x/si468x.o
                0x00000000e8c0eb26                Tuner_Get_Reg
 .text.fm_am_first_preset_value
                0x00000000e8c0ec84      0x2f0 emodules/drv_dab/dab_module/si463x/si468x.o
                0x00000000e8c0ec84                fm_am_first_preset_value
 .text.fm_am_set_area
                0x00000000e8c0ef74      0x26a emodules/drv_dab/dab_module/si463x/si468x.o
                0x00000000e8c0ef74                fm_am_set_area
 .text.Get_Freq_Range_By_Area
                0x00000000e8c0f1de      0x21c emodules/drv_dab/dab_module/si463x/si468x.o
                0x00000000e8c0f1de                Get_Freq_Range_By_Area
 .text.wait_af_seek_now_pty
                0x00000000e8c0f3fa       0x98 emodules/drv_dab/dab_module/si463x/si468x.o
                0x00000000e8c0f3fa                wait_af_seek_now_pty
 .text.T_PI_Clear
                0x00000000e8c0f492       0x26 emodules/drv_dab/dab_module/si463x/si468x.o
                0x00000000e8c0f492                T_PI_Clear
 .text.GetFmCurPreset
                0x00000000e8c0f4b8       0x3c emodules/drv_dab/dab_module/si463x/si468x.o
                0x00000000e8c0f4b8                GetFmCurPreset
 .text.GetFreq  0x00000000e8c0f4f4       0x1e emodules/drv_dab/dab_module/si463x/si468x.o
                0x00000000e8c0f4f4                GetFreq
 .text.T_Tune_AF_Seek_end
                0x00000000e8c0f512       0xbc emodules/drv_dab/dab_module/si463x/si468x.o
                0x00000000e8c0f512                T_Tune_AF_Seek_end
 .text.wait_af_seek_rds_sysnc_int
                0x00000000e8c0f5ce       0x7c emodules/drv_dab/dab_module/si463x/si468x.o
                0x00000000e8c0f5ce                wait_af_seek_rds_sysnc_int
 .text.wait_af_seek_rds_pi
                0x00000000e8c0f64a       0x64 emodules/drv_dab/dab_module/si463x/si468x.o
                0x00000000e8c0f64a                wait_af_seek_rds_pi
 .text.si463x_bus_thread
                0x00000000e8c0f6ae      0xcd6 emodules/drv_dab/dab_module/si463x/si468x.o
 .text.si4634_fm_load_firmware_and_tune
                0x00000000e8c10384      0x162 emodules/drv_dab/dab_module/si463x/si468x.o
                0x00000000e8c10384                si4634_fm_load_firmware_and_tune
 .text.si4634_fm_init
                0x00000000e8c104e6       0x3e emodules/drv_dab/dab_module/si463x/si468x.o
                0x00000000e8c104e6                si4634_fm_init
 .text.si4634_fm_exit
                0x00000000e8c10524       0x18 emodules/drv_dab/dab_module/si463x/si468x.o
                0x00000000e8c10524                si4634_fm_exit
 .text.si4634_am_load_firmware_and_tune
                0x00000000e8c1053c       0x84 emodules/drv_dab/dab_module/si463x/si468x.o
                0x00000000e8c1053c                si4634_am_load_firmware_and_tune
 .text.si4634_am_init
                0x00000000e8c105c0       0x42 emodules/drv_dab/dab_module/si463x/si468x.o
                0x00000000e8c105c0                si4634_am_init
 .text.si4634_mode_init
                0x00000000e8c10602       0x38 emodules/drv_dab/dab_module/si463x/si468x.o
                0x00000000e8c10602                si4634_mode_init
 .text.si4634_mode_uninit
                0x00000000e8c1063a       0x34 emodules/drv_dab/dab_module/si463x/si468x.o
                0x00000000e8c1063a                si4634_mode_uninit
 .text.dabiic_write_bytes
                0x00000000e8c1066e       0x32 emodules/drv_dab/dab_module/si463x/si468x_bus.o
 .text._dabiic_init
                0x00000000e8c106a0       0x30 emodules/drv_dab/dab_module/si463x/si468x_bus.o
                0x00000000e8c106a0                _dabiic_init
 .text._dabiic_exit
                0x00000000e8c106d0       0x28 emodules/drv_dab/dab_module/si463x/si468x_bus.o
                0x00000000e8c106d0                _dabiic_exit
 .text.si468x_writeCommand
                0x00000000e8c106f8        0xa emodules/drv_dab/dab_module/si463x/si468x_bus.o
                0x00000000e8c106f8                si468x_writeCommand
 .text.si468x_readReply
                0x00000000e8c10702       0x4c emodules/drv_dab/dab_module/si463x/si468x_bus.o
                0x00000000e8c10702                si468x_readReply
 .text.si468x_reset_io
                0x00000000e8c1074e       0x18 emodules/drv_dab/dab_module/si463x/si468x_bus.o
                0x00000000e8c1074e                si468x_reset_io
 .text.si468x_initHardware
                0x00000000e8c10766       0x10 emodules/drv_dab/dab_module/si463x/si468x_bus.o
                0x00000000e8c10766                si468x_initHardware
 .text.clear_uiview_mot_sls_image
                0x00000000e8c10776       0x3c emodules/drv_dab/dab_module/si463x/mot.o
                0x00000000e8c10776                clear_uiview_mot_sls_image
 .text.handle_msc_datagroup
                0x00000000e8c107b2      0x37e emodules/drv_dab/dab_module/si463x/mot.o
                0x00000000e8c107b2                handle_msc_datagroup
 .text.mot_init
                0x00000000e8c10b30       0x30 emodules/drv_dab/dab_module/si463x/mot.o
                0x00000000e8c10b30                mot_init
 .text.mot_exit
                0x00000000e8c10b60       0x14 emodules/drv_dab/dab_module/si463x/mot.o
                0x00000000e8c10b60                mot_exit
 .text.esCHS_Char2Uni
                0x00000000e8c10b74       0x1c ./elibrary/bin//libsyscall.a(syscall.o)
                0x00000000e8c10b74                esCHS_Char2Uni
 .text.esCHS_Uni2Char
                0x00000000e8c10b90       0x1c ./elibrary/bin//libsyscall.a(syscall.o)
                0x00000000e8c10b90                esCHS_Uni2Char
 .text.rt_device_register
                0x00000000e8c10bac       0x16 ./elibrary/bin//libsyscall.a(syscall.o)
                0x00000000e8c10bac                rt_device_register
 .text.rt_device_unregister
                0x00000000e8c10bc2       0x16 ./elibrary/bin//libsyscall.a(syscall.o)
                0x00000000e8c10bc2                rt_device_unregister
 .text.esFSYS_fclose
                0x00000000e8c10bd8       0x18 ./elibrary/bin//libsyscall.a(syscall.o)
                0x00000000e8c10bd8                esFSYS_fclose
 .text.esFSYS_fioctrl
                0x00000000e8c10bf0       0x18 ./elibrary/bin//libsyscall.a(syscall.o)
                0x00000000e8c10bf0                esFSYS_fioctrl
 .text.esFSYS_fopen
                0x00000000e8c10c08       0x16 ./elibrary/bin//libsyscall.a(syscall.o)
                0x00000000e8c10c08                esFSYS_fopen
 .text.esFSYS_format
                0x00000000e8c10c1e       0x18 ./elibrary/bin//libsyscall.a(syscall.o)
                0x00000000e8c10c1e                esFSYS_format
 .text.esFSYS_fread
                0x00000000e8c10c36       0x20 ./elibrary/bin//libsyscall.a(syscall.o)
                0x00000000e8c10c36                esFSYS_fread
 .text.esFSYS_fseek
                0x00000000e8c10c56       0x18 ./elibrary/bin//libsyscall.a(syscall.o)
                0x00000000e8c10c56                esFSYS_fseek
 .text.esFSYS_ftell
                0x00000000e8c10c6e       0x18 ./elibrary/bin//libsyscall.a(syscall.o)
                0x00000000e8c10c6e                esFSYS_ftell
 .text.esFSYS_fwrite
                0x00000000e8c10c86       0x20 ./elibrary/bin//libsyscall.a(syscall.o)
                0x00000000e8c10c86                esFSYS_fwrite
 .text.esKRNL_SemCreate
                0x00000000e8c10ca6       0x16 ./elibrary/bin//libsyscall.a(syscall.o)
                0x00000000e8c10ca6                esKRNL_SemCreate
 .text.esKRNL_SemDel
                0x00000000e8c10cbc       0x16 ./elibrary/bin//libsyscall.a(syscall.o)
                0x00000000e8c10cbc                esKRNL_SemDel
 .text.esKRNL_SemPend
                0x00000000e8c10cd2       0x16 ./elibrary/bin//libsyscall.a(syscall.o)
                0x00000000e8c10cd2                esKRNL_SemPend
 .text.esKRNL_SemPost
                0x00000000e8c10ce8       0x1a ./elibrary/bin//libsyscall.a(syscall.o)
                0x00000000e8c10ce8                esKRNL_SemPost
 .text.esKRNL_TCreate
                0x00000000e8c10d02       0x1c ./elibrary/bin//libsyscall.a(syscall.o)
                0x00000000e8c10d02                esKRNL_TCreate
 .text.esKRNL_TDel
                0x00000000e8c10d1e       0x1e ./elibrary/bin//libsyscall.a(syscall.o)
                0x00000000e8c10d1e                esKRNL_TDel
 .text.esKRNL_TDelReq
                0x00000000e8c10d3c       0x1e ./elibrary/bin//libsyscall.a(syscall.o)
                0x00000000e8c10d3c                esKRNL_TDelReq
 .text.esKRNL_TimeDly
                0x00000000e8c10d5a       0x16 ./elibrary/bin//libsyscall.a(syscall.o)
                0x00000000e8c10d5a                esKRNL_TimeDly
 .text.esKRNL_TimeGet
                0x00000000e8c10d70       0x18 ./elibrary/bin//libsyscall.a(syscall.o)
                0x00000000e8c10d70                esKRNL_TimeGet
 .text.esKSRV_SendMsg
                0x00000000e8c10d88       0x20 ./elibrary/bin//libsyscall.a(syscall.o)
                0x00000000e8c10d88                esKSRV_SendMsg
 .text.esMEMS_Balloc
                0x00000000e8c10da8       0x16 ./elibrary/bin//libsyscall.a(syscall.o)
                0x00000000e8c10da8                esMEMS_Balloc
 .text.esMEMS_Bfree
                0x00000000e8c10dbe       0x16 ./elibrary/bin//libsyscall.a(syscall.o)
                0x00000000e8c10dbe                esMEMS_Bfree
 .text.esMEMS_Malloc
                0x00000000e8c10dd4       0x1a ./elibrary/bin//libsyscall.a(syscall.o)
                0x00000000e8c10dd4                esMEMS_Malloc
 .text.esMEMS_Mfree
                0x00000000e8c10dee       0x16 ./elibrary/bin//libsyscall.a(syscall.o)
                0x00000000e8c10dee                esMEMS_Mfree
 .text.esSIOS_putstr
                0x00000000e8c10e04       0x14 ./elibrary/bin//libsyscall.a(syscall.o)
                0x00000000e8c10e04                esSIOS_putstr
 .text._close_r
                0x00000000e8c10e18       0x18 ./elibrary/bin//libsyscall.a(syscall_fops.o)
                0x00000000e8c10e18                _close_r
 .text._open_r  0x00000000e8c10e30       0x18 ./elibrary/bin//libsyscall.a(syscall_fops.o)
                0x00000000e8c10e30                _open_r
 .text.ioctl    0x00000000e8c10e48       0x28 ./elibrary/bin//libsyscall.a(syscall_fops.o)
                0x00000000e8c10e48                ioctl
 .text.eLIBs_vsnprintf
                0x00000000e8c10e70        0xc ./elibrary/bin//libminic.a(elibs_sprintf.o)
                0x00000000e8c10e70                eLIBs_vsnprintf
 .text.eLIBs_vscnprintf
                0x00000000e8c10e7c       0x1c ./elibrary/bin//libminic.a(elibs_sprintf.o)
                0x00000000e8c10e7c                eLIBs_vscnprintf
 .text.eLIBs_sprintf
                0x00000000e8c10e98       0x22 ./elibrary/bin//libminic.a(elibs_sprintf.o)
                0x00000000e8c10e98                eLIBs_sprintf
 .text.eLIBs_fopen
                0x00000000e8c10eba        0x4 ./elibrary/bin//libminic.a(elibs_stdio.o)
                0x00000000e8c10eba                eLIBs_fopen
 .text.eLIBs_fclose
                0x00000000e8c10ebe        0x4 ./elibrary/bin//libminic.a(elibs_stdio.o)
                0x00000000e8c10ebe                eLIBs_fclose
 .text.eLIBs_fread
                0x00000000e8c10ec2        0x4 ./elibrary/bin//libminic.a(elibs_stdio.o)
                0x00000000e8c10ec2                eLIBs_fread
 .text.eLIBs_fwrite
                0x00000000e8c10ec6        0x4 ./elibrary/bin//libminic.a(elibs_stdio.o)
                0x00000000e8c10ec6                eLIBs_fwrite
 .text.eLIBs_fseek
                0x00000000e8c10eca       0x12 ./elibrary/bin//libminic.a(elibs_stdio.o)
                0x00000000e8c10eca                eLIBs_fseek
 .text.eLIBs_ftell
                0x00000000e8c10edc        0x4 ./elibrary/bin//libminic.a(elibs_stdio.o)
                0x00000000e8c10edc                eLIBs_ftell
 .text.eLIBs_fioctrl
                0x00000000e8c10ee0        0x4 ./elibrary/bin//libminic.a(elibs_stdio.o)
                0x00000000e8c10ee0                eLIBs_fioctrl
 .text.eLIBs_format
                0x00000000e8c10ee4        0x4 ./elibrary/bin//libminic.a(elibs_stdio.o)
                0x00000000e8c10ee4                eLIBs_format
 .text.eLIBs_vprintf
                0x00000000e8c10ee8       0x4c ./elibrary/bin//libminic.a(elibs_stdio.o)
                0x00000000e8c10ee8                eLIBs_vprintf
 .text.eLIBs_printf
                0x00000000e8c10f34       0x20 ./elibrary/bin//libminic.a(elibs_stdio.o)
                0x00000000e8c10f34                eLIBs_printf
                0x00000000e8c10f34                printk
 .text.eLIBs_strlen
                0x00000000e8c10f54        0xc ./elibrary/bin//libminic.a(elibs_string.o)
                0x00000000e8c10f54                eLIBs_strlen
 .text.eLIBs_strcpy
                0x00000000e8c10f60        0xc ./elibrary/bin//libminic.a(elibs_string.o)
                0x00000000e8c10f60                eLIBs_strcpy
 .text.eLIBs_strncpy
                0x00000000e8c10f6c        0xc ./elibrary/bin//libminic.a(elibs_string.o)
                0x00000000e8c10f6c                eLIBs_strncpy
 .text.eLIBs_strncmp
                0x00000000e8c10f78        0xc ./elibrary/bin//libminic.a(elibs_string.o)
                0x00000000e8c10f78                eLIBs_strncmp
 .text.eLIBs_memset
                0x00000000e8c10f84        0xc ./elibrary/bin//libminic.a(elibs_string.o)
                0x00000000e8c10f84                eLIBs_memset
 .text.eLIBs_memcpy
                0x00000000e8c10f90        0xc ./elibrary/bin//libminic.a(elibs_string.o)
                0x00000000e8c10f90                eLIBs_memcpy
 .text.eLIBs_memcmp
                0x00000000e8c10f9c       0x18 ./elibrary/bin//libminic.a(elibs_string.o)
                0x00000000e8c10f9c                eLIBs_memcmp
 *(.stub)
 *(.gnu.warning)
 *(.gnu.linkonce.t*)
                0x00000000e8c10fb4                . = ALIGN (0x4)
 *fill*         0x00000000e8c10fb4        0x0 

.mod.rodata     0x00000000e8c10fb4      0xba4
 *(.rodata)
 *(.rodata.*)
 .rodata.hal_dab_control
                0x00000000e8c10fb4       0xa4 emodules/drv_dab/dev_dab.o
 .rodata.sunxi_driver_dab_init.str1.8
                0x00000000e8c11058        0x4 emodules/drv_dab/dev_dab.o
 *fill*         0x00000000e8c1105c        0x4 
 .rodata.sunxi_hal_dab_driver
                0x00000000e8c11060       0x18 emodules/drv_dab/dev_dab.o
 .rodata.DRV_DAB_MIoctrl.str1.8
                0x00000000e8c11078        0x9 emodules/drv_dab/drv_dab.o
 *fill*         0x00000000e8c11081        0x7 
 .rodata.DLS_EBU_TO_UCS2
                0x00000000e8c11088      0x200 emodules/drv_dab/dab_module/si463x/DAB_DLS_Handler.o
                0x00000000e8c11088                DLS_EBU_TO_UCS2
 .rodata.UpdateMetrics
                0x00000000e8c11288       0x1c emodules/drv_dab/dab_module/si463x/Firmware_API_Manager.o
 *fill*         0x00000000e8c112a4        0x4 
 .rodata.EBU_TO_UCS2
                0x00000000e8c112a8      0x200 emodules/drv_dab/dab_module/si463x/Firmware_API_Manager.o
                0x00000000e8c112a8                EBU_TO_UCS2
 .rodata.Get_function_info.str1.8
                0x00000000e8c114a8        0xc emodules/drv_dab/dab_module/si463x/Firmware_API_Manager.o
 *fill*         0x00000000e8c114b4        0x4 
 .rodata.Initialize.str1.8
                0x00000000e8c114b8       0x12 emodules/drv_dab/dab_module/si463x/Firmware_API_Manager.o
 *fill*         0x00000000e8c114ca        0x6 
 .rodata._adjust_NVMSPI_rate.cst8
                0x00000000e8c114d0        0x8 emodules/drv_dab/dab_module/si463x/Firmware_API_Manager.o
 .rodata._host_load.str1.8
                0x00000000e8c114d8       0x11 emodules/drv_dab/dab_module/si463x/Firmware_API_Manager.o
 *fill*         0x00000000e8c114e9        0x7 
 .rodata._si46xx_initialize.str1.8
                0x00000000e8c114f0       0x13 emodules/drv_dab/dab_module/si463x/Firmware_API_Manager.o
 *fill*         0x00000000e8c11503        0x5 
 .rodata.minipatch
                0x00000000e8c11508      0x3ac emodules/drv_dab/dab_module/si463x/Firmware_Load_Helpers.o
 .rodata.si4384_set_reset_io.str1.8
                0x00000000e8c118b4        0x9 emodules/drv_dab/dab_module/si463x/HAL.o
 *fill*         0x00000000e8c118b4        0x4 
 .rodata.writeCommand.str1.8
                0x00000000e8c118b8       0x16 emodules/drv_dab/dab_module/si463x/HAL.o
 *fill*         0x00000000e8c118ce        0x2 
 .rodata.updateRDS
                0x00000000e8c118d0       0x24 emodules/drv_dab/dab_module/si463x/RDS_Handler.o
 .rodata.rds_update_mcu_time.str1.8
                0x00000000e8c118f4        0x9 emodules/drv_dab/dab_module/si463x/RDS_Handler.o
 .rodata.dab_key_process
                0x00000000e8c118f4       0x68 emodules/drv_dab/dab_module/si463x/si468x.o
 .rodata.si463x_dab_Mode_Proc
                0x00000000e8c1195c       0xf8 emodules/drv_dab/dab_module/si463x/si468x.o
 .rodata.RadioAutoStore
                0x00000000e8c11a54       0x14 emodules/drv_dab/dab_module/si463x/si468x.o
 .rodata.CSWTCH.2350
                0x00000000e8c11a68       0x20 emodules/drv_dab/dab_module/si463x/si468x.o
 .rodata.dab_save_flush.str1.8
                0x00000000e8c11a88       0x22 emodules/drv_dab/dab_module/si463x/si468x.o
 *fill*         0x00000000e8c11aaa        0x6 
 .rodata.dab_save_init_para.str1.8
                0x00000000e8c11ab0       0x3c emodules/drv_dab/dab_module/si463x/si468x.o
 *fill*         0x00000000e8c11aec        0x4 
 .rodata.dab_send_cmd2fm.str1.8
                0x00000000e8c11af0        0x8 emodules/drv_dab/dab_module/si463x/si468x.o
 .rodata.fm_am_first_preset_value.cst8
                0x00000000e8c11af8       0x50 emodules/drv_dab/dab_module/si463x/si468x.o
 .rodata.si4634_get_reg_handle.str1.8
                0x00000000e8c11b48        0x9 emodules/drv_dab/dab_module/si463x/si468x.o
 .rodata._dabiic_init.str1.8
                0x00000000e8c11b48       0x10 emodules/drv_dab/dab_module/si463x/si468x_bus.o
                                          0xb (size before relaxing)
 *(.gnu.linkonce.r*)
                0x00000000e8c11b58                . = ALIGN (0x4)

.mod.data       0x00000000e8c11b58      0x998
 *(.data)
 .data          0x00000000e8c11b58      0x750 /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/../../../../riscv64-unknown-elf/lib/rv64imafdcxtheadc/lp64d/libc.a(lib_a-impure.o)
                0x00000000e8c11b58                _impure_ptr
 *(.data.*)
 .data.tuner_mode
                0x00000000e8c122a8        0x1 emodules/drv_dab/drv_dab.o
                0x00000000e8c122a8                tuner_mode
 .data._current_image_type
                0x00000000e8c122a9        0x1 emodules/drv_dab/dab_module/si463x/Firmware_API_Manager.o
                0x00000000e8c122a9                _current_image_type
 .data.currentMode
                0x00000000e8c122aa        0x1 emodules/drv_dab/dab_module/si463x/Firmware_Load_Helpers.o
                0x00000000e8c122aa                currentMode
 .data.rdsIgnoreAB
                0x00000000e8c122ab        0x1 emodules/drv_dab/dab_module/si463x/RDS_Handler.o
                0x00000000e8c122ab                rdsIgnoreAB
 *fill*         0x00000000e8c122ac        0x4 
 .data.cmd_arg  0x00000000e8c122b0        0x8 emodules/drv_dab/dab_module/si463x/si46xx_firmware_api.o
                0x00000000e8c122b0                cmd_arg
 .data.cmd_arg_u16
                0x00000000e8c122b8        0x8 emodules/drv_dab/dab_module/si463x/si46xx_firmware_api.o
                0x00000000e8c122b8                cmd_arg_u16
 .data.reply_buffer
                0x00000000e8c122c0        0x8 emodules/drv_dab/dab_module/si463x/si46xx_firmware_api.o
                0x00000000e8c122c0                reply_buffer
 .data.reply_buffer_u16
                0x00000000e8c122c8        0x8 emodules/drv_dab/dab_module/si463x/si46xx_firmware_api.o
                0x00000000e8c122c8                reply_buffer_u16
 .data.RDS_Pty_List
                0x00000000e8c122d0      0x200 emodules/drv_dab/dab_module/si463x/si468x.o
                0x00000000e8c122d0                RDS_Pty_List
 .data.Si4634CurrentMode
                0x00000000e8c124d0        0x1 emodules/drv_dab/dab_module/si463x/si468x.o
                0x00000000e8c124d0                Si4634CurrentMode
 .data.TempServiceIndex.19719
                0x00000000e8c124d1        0x1 emodules/drv_dab/dab_module/si463x/si468x.o
 .data.TempServiceIndex.19755
                0x00000000e8c124d2        0x1 emodules/drv_dab/dab_module/si463x/si468x.o
 .data.TempServiceIndex.19793
                0x00000000e8c124d3        0x1 emodules/drv_dab/dab_module/si463x/si468x.o
 .data.curPtyIndex.19751
                0x00000000e8c124d4        0x1 emodules/drv_dab/dab_module/si463x/si468x.o
 .data.dab_nosignal_tune_freqindex
                0x00000000e8c124d5        0x1 emodules/drv_dab/dab_module/si463x/si468x.o
                0x00000000e8c124d5                dab_nosignal_tune_freqindex
 .data.gThreshold_RSSI
                0x00000000e8c124d6        0x1 emodules/drv_dab/dab_module/si463x/si468x.o
                0x00000000e8c124d6                gThreshold_RSSI
 *fill*         0x00000000e8c124d7        0x1 
 .data.timeout.19700
                0x00000000e8c124d8        0x2 emodules/drv_dab/dab_module/si463x/si468x.o
 .data.timeout.19707
                0x00000000e8c124da        0x2 emodules/drv_dab/dab_module/si463x/si468x.o
 .data.timeout.19734
                0x00000000e8c124dc        0x2 emodules/drv_dab/dab_module/si463x/si468x.o
 .data.timeout.19741
                0x00000000e8c124de        0x2 emodules/drv_dab/dab_module/si463x/si468x.o
 .data.timeout.19771
                0x00000000e8c124e0        0x2 emodules/drv_dab/dab_module/si463x/si468x.o
 .data.timeout.19780
                0x00000000e8c124e2        0x2 emodules/drv_dab/dab_module/si463x/si468x.o
 .data.timeout.19808
                0x00000000e8c124e4        0x2 emodules/drv_dab/dab_module/si463x/si468x.o
 .data.timeout.19815
                0x00000000e8c124e6        0x2 emodules/drv_dab/dab_module/si463x/si468x.o
 .data.timeout.20024
                0x00000000e8c124e8        0x2 emodules/drv_dab/dab_module/si463x/si468x.o
 .data.timeout.20036
                0x00000000e8c124ea        0x2 emodules/drv_dab/dab_module/si463x/si468x.o
 .data.volume   0x00000000e8c124ec        0x1 emodules/drv_dab/dab_module/si463x/si468x.o
                0x00000000e8c124ec                volume
 *fill*         0x00000000e8c124ed        0x1 
 .data.mot_header_mode_body_transport_id
                0x00000000e8c124ee        0x2 emodules/drv_dab/dab_module/si463x/mot.o
 *(.gnu.linkonce.d.*)
 *(.sdata)
 *(.sdata.*)
 *(.gnu.linkonce.s.*)
 *(.sdata2)
 *(.sdata2.*)
 *(.gnu.linkonce.s2.*)
                0x00000000e8c124f0                . = ALIGN (0x4)

.mod.bss        0x00000000e8c124f0     0x3f84
                0x00000000e8c124f0                __bss_start = ABSOLUTE (.)
 *(.bss)
 *(.bss.*)
 .bss.dab_t     0x00000000e8c124f0       0xb8 emodules/drv_dab/dev_dab.o
                0x00000000e8c124f0                dab_t
 .bss.dab_drv   0x00000000e8c125a8       0x20 emodules/drv_dab/drv_dab.o
                0x00000000e8c125a8                dab_drv
 .bss.dab_lock  0x00000000e8c125c8        0x8 emodules/drv_dab/drv_dab.o
                0x00000000e8c125c8                dab_lock
 .bss.DAB_service_list_updated_for_current_freq
                0x00000000e8c125d0        0x1 emodules/drv_dab/dab_module/si463x/DAB_Service_List_Handler.o
                0x00000000e8c125d0                DAB_service_list_updated_for_current_freq
 .bss.SeekServiceCount
                0x00000000e8c125d1        0x1 emodules/drv_dab/dab_module/si463x/DAB_Service_List_Handler.o
                0x00000000e8c125d1                SeekServiceCount
 .bss._currentListVersion
                0x00000000e8c125d2        0x2 emodules/drv_dab/dab_module/si463x/DAB_Service_List_Handler.o
                0x00000000e8c125d2                _currentListVersion
 .bss._current_service_list_buffer_end_as_offset
                0x00000000e8c125d4        0x2 emodules/drv_dab/dab_module/si463x/DAB_Service_List_Handler.o
                0x00000000e8c125d4                _current_service_list_buffer_end_as_offset
 .bss.favoriteServicesShifted
                0x00000000e8c125d6        0x1 emodules/drv_dab/dab_module/si463x/DAB_Service_List_Handler.o
                0x00000000e8c125d6                favoriteServicesShifted
 *fill*         0x00000000e8c125d7        0x1 
 .bss.svcListDAB
                0x00000000e8c125d8      0x3d8 emodules/drv_dab/dab_module/si463x/DAB_Service_List_Handler.o
                0x00000000e8c125d8                svcListDAB
 .bss._AM_seek_continue
                0x00000000e8c129b0        0x1 emodules/drv_dab/dab_module/si463x/Firmware_API_Manager.o
                0x00000000e8c129b0                _AM_seek_continue
 *fill*         0x00000000e8c129b1        0x1 
 .bss._ANTCAP_DAB
                0x00000000e8c129b2        0x2 emodules/drv_dab/dab_module/si463x/Firmware_API_Manager.o
                0x00000000e8c129b2                _ANTCAP_DAB
 .bss._DAB_auto_seek_key_type
                0x00000000e8c129b4        0x1 emodules/drv_dab/dab_module/si463x/Firmware_API_Manager.o
                0x00000000e8c129b4                _DAB_auto_seek_key_type
 .bss._DAB_current_browse_service_index
                0x00000000e8c129b5        0x1 emodules/drv_dab/dab_module/si463x/Firmware_API_Manager.o
                0x00000000e8c129b5                _DAB_current_browse_service_index
 .bss._DAB_current_manual_freq_index
                0x00000000e8c129b6        0x1 emodules/drv_dab/dab_module/si463x/Firmware_API_Manager.o
                0x00000000e8c129b6                _DAB_current_manual_freq_index
 .bss._DAB_last_manual_browse_service_index
                0x00000000e8c129b7        0x1 emodules/drv_dab/dab_module/si463x/Firmware_API_Manager.o
                0x00000000e8c129b7                _DAB_last_manual_browse_service_index
 .bss._DAB_last_manual_freq_index
                0x00000000e8c129b8        0x1 emodules/drv_dab/dab_module/si463x/Firmware_API_Manager.o
                0x00000000e8c129b8                _DAB_last_manual_freq_index
 .bss._DAB_pty_seek_key_type
                0x00000000e8c129b9        0x1 emodules/drv_dab/dab_module/si463x/Firmware_API_Manager.o
                0x00000000e8c129b9                _DAB_pty_seek_key_type
 .bss._DAB_scan_continue
                0x00000000e8c129ba        0x1 emodules/drv_dab/dab_module/si463x/Firmware_API_Manager.o
                0x00000000e8c129ba                _DAB_scan_continue
 .bss._DAB_service_lost
                0x00000000e8c129bb        0x1 emodules/drv_dab/dab_module/si463x/Firmware_API_Manager.o
                0x00000000e8c129bb                _DAB_service_lost
 .bss._FM_seek_continue
                0x00000000e8c129bc        0x1 emodules/drv_dab/dab_module/si463x/Firmware_API_Manager.o
                0x00000000e8c129bc                _FM_seek_continue
 *fill*         0x00000000e8c129bd        0x3 
 .bss._current_am_metrics
                0x00000000e8c129c0        0x6 emodules/drv_dab/dab_module/si463x/Firmware_API_Manager.o
                0x00000000e8c129c0                _current_am_metrics
 *fill*         0x00000000e8c129c6        0x2 
 .bss._current_amhd_metrics
                0x00000000e8c129c8        0xe emodules/drv_dab/dab_module/si463x/Firmware_API_Manager.o
                0x00000000e8c129c8                _current_amhd_metrics
 *fill*         0x00000000e8c129d6        0x2 
 .bss._current_audio_component_id
                0x00000000e8c129d8        0x4 emodules/drv_dab/dab_module/si463x/Firmware_API_Manager.o
                0x00000000e8c129d8                _current_audio_component_id
 .bss._current_audio_global_id
                0x00000000e8c129dc        0x1 emodules/drv_dab/dab_module/si463x/Firmware_API_Manager.o
                0x00000000e8c129dc                _current_audio_global_id
 *fill*         0x00000000e8c129dd        0x3 
 .bss._current_audio_service_id
                0x00000000e8c129e0        0x4 emodules/drv_dab/dab_module/si463x/Firmware_API_Manager.o
                0x00000000e8c129e0                _current_audio_service_id
 .bss._current_audio_services_stop
                0x00000000e8c129e4        0x1 emodules/drv_dab/dab_module/si463x/Firmware_API_Manager.o
                0x00000000e8c129e4                _current_audio_services_stop
 *fill*         0x00000000e8c129e5        0x1 
 .bss._current_band_bottom
                0x00000000e8c129e6        0x2 emodules/drv_dab/dab_module/si463x/Firmware_API_Manager.o
                0x00000000e8c129e6                _current_band_bottom
 .bss._current_band_manu_step
                0x00000000e8c129e8        0x2 emodules/drv_dab/dab_module/si463x/Firmware_API_Manager.o
                0x00000000e8c129e8                _current_band_manu_step
 .bss._current_band_step
                0x00000000e8c129ea        0x2 emodules/drv_dab/dab_module/si463x/Firmware_API_Manager.o
                0x00000000e8c129ea                _current_band_step
 .bss._current_band_top
                0x00000000e8c129ec        0x2 emodules/drv_dab/dab_module/si463x/Firmware_API_Manager.o
                0x00000000e8c129ec                _current_band_top
 *fill*         0x00000000e8c129ee        0x2 
 .bss._current_dab_metrics
                0x00000000e8c129f0       0x12 emodules/drv_dab/dab_module/si463x/Firmware_API_Manager.o
                0x00000000e8c129f0                _current_dab_metrics
 *fill*         0x00000000e8c12a02        0x6 
 .bss._current_fm_metrics
                0x00000000e8c12a08        0x6 emodules/drv_dab/dab_module/si463x/Firmware_API_Manager.o
                0x00000000e8c12a08                _current_fm_metrics
 *fill*         0x00000000e8c12a0e        0x2 
 .bss._current_fmhd_metrics
                0x00000000e8c12a10        0xe emodules/drv_dab/dab_module/si463x/Firmware_API_Manager.o
                0x00000000e8c12a10                _current_fmhd_metrics
 .bss._current_hd_audio_service_as_bitmask
                0x00000000e8c12a1e        0x1 emodules/drv_dab/dab_module/si463x/Firmware_API_Manager.o
                0x00000000e8c12a1e                _current_hd_audio_service_as_bitmask
 *fill*         0x00000000e8c12a1f        0x1 
 .bss._current_service_links
                0x00000000e8c12a20       0x54 emodules/drv_dab/dab_module/si463x/Firmware_API_Manager.o
                0x00000000e8c12a20                _current_service_links
 .bss._dab_favorites_only_browse_enable
                0x00000000e8c12a74        0x1 emodules/drv_dab/dab_module/si463x/Firmware_API_Manager.o
                0x00000000e8c12a74                _dab_favorites_only_browse_enable
 *fill*         0x00000000e8c12a75        0x3 
 .bss._freqInfo
                0x00000000e8c12a78       0x3e emodules/drv_dab/dab_module/si463x/Firmware_API_Manager.o
                0x00000000e8c12a78                _freqInfo
 .bss._last_frequency_index
                0x00000000e8c12ab6        0x1 emodules/drv_dab/dab_module/si463x/Firmware_API_Manager.o
                0x00000000e8c12ab6                _last_frequency_index
 *fill*         0x00000000e8c12ab7        0x1 
 .bss._rds_freq_info
                0x00000000e8c12ab8      0x130 emodules/drv_dab/dab_module/si463x/Firmware_API_Manager.o
                0x00000000e8c12ab8                _rds_freq_info
 .bss._service_following_enabled
                0x00000000e8c12be8        0x1 emodules/drv_dab/dab_module/si463x/Firmware_API_Manager.o
                0x00000000e8c12be8                _service_following_enabled
 *fill*         0x00000000e8c12be9        0x7 
 .bss._service_list
                0x00000000e8c12bf0        0x8 emodules/drv_dab/dab_module/si463x/Firmware_API_Manager.o
                0x00000000e8c12bf0                _service_list
 .bss._si46xx_status
                0x00000000e8c12bf8        0x9 emodules/drv_dab/dab_module/si463x/Firmware_API_Manager.o
                0x00000000e8c12bf8                _si46xx_status
 *fill*         0x00000000e8c12c01        0x7 
 .bss.dab_announcement
                0x00000000e8c12c08       0x2c emodules/drv_dab/dab_module/si463x/Firmware_API_Manager.o
                0x00000000e8c12c08                dab_announcement
 *fill*         0x00000000e8c12c34        0x4 
 .bss.dab_dab_hard_links_fig021024helper
                0x00000000e8c12c38       0x2c emodules/drv_dab/dab_module/si463x/Firmware_API_Manager.o
                0x00000000e8c12c38                dab_dab_hard_links_fig021024helper
 *fill*         0x00000000e8c12c64        0x4 
 .bss.dab_dab_soft_links_fig021024helper
                0x00000000e8c12c68       0x2c emodules/drv_dab/dab_module/si463x/Firmware_API_Manager.o
                0x00000000e8c12c68                dab_dab_soft_links_fig021024helper
 .bss.dab_implicit_rds_dead_link_flg
                0x00000000e8c12c94        0x1 emodules/drv_dab/dab_module/si463x/Firmware_API_Manager.o
                0x00000000e8c12c94                dab_implicit_rds_dead_link_flg
 *fill*         0x00000000e8c12c95        0x3 
 .bss.dab_linkage_set
                0x00000000e8c12c98      0x500 emodules/drv_dab/dab_module/si463x/Firmware_API_Manager.o
                0x00000000e8c12c98                dab_linkage_set
 .bss.dab_linkset_lists
                0x00000000e8c13198       0x10 emodules/drv_dab/dab_module/si463x/Firmware_API_Manager.o
 .bss.dab_rds_hard_links_fig021024helper
                0x00000000e8c131a8       0x40 emodules/drv_dab/dab_module/si463x/Firmware_API_Manager.o
                0x00000000e8c131a8                dab_rds_hard_links_fig021024helper
 .bss.dab_rds_hard_links_implicit_rds
                0x00000000e8c131e8       0x40 emodules/drv_dab/dab_module/si463x/Firmware_API_Manager.o
                0x00000000e8c131e8                dab_rds_hard_links_implicit_rds
 .bss.dab_rds_soft_links_fig021024helper
                0x00000000e8c13228       0x40 emodules/drv_dab/dab_module/si463x/Firmware_API_Manager.o
                0x00000000e8c13228                dab_rds_soft_links_fig021024helper
 .bss.dab_tmp_links
                0x00000000e8c13268       0x2c emodules/drv_dab/dab_module/si463x/Firmware_API_Manager.o
 *fill*         0x00000000e8c13294        0x4 
 .bss.freq_list
                0x00000000e8c13298       0x10 emodules/drv_dab/dab_module/si463x/Firmware_API_Manager.o
                0x00000000e8c13298                freq_list
 .bss.initial_count.19799
                0x00000000e8c132a8        0x1 emodules/drv_dab/dab_module/si463x/Firmware_API_Manager.o
 .bss.miss_the_update_servicelinking_flg
                0x00000000e8c132a9        0x1 emodules/drv_dab/dab_module/si463x/Firmware_API_Manager.o
 .bss.progressRate
                0x00000000e8c132aa        0x1 emodules/drv_dab/dab_module/si463x/Firmware_API_Manager.o
                0x00000000e8c132aa                progressRate
 *fill*         0x00000000e8c132ab        0x1 
 .bss.numBytesSent
                0x00000000e8c132ac        0x4 emodules/drv_dab/dab_module/si463x/Firmware_Load_Helpers.o
                0x00000000e8c132ac                numBytesSent
 .bss.totalLength
                0x00000000e8c132b0        0x4 emodules/drv_dab/dab_module/si463x/Firmware_Load_Helpers.o
                0x00000000e8c132b0                totalLength
 *fill*         0x00000000e8c132b4        0x4 
 .bss.albumDisplay
                0x00000000e8c132b8       0x82 emodules/drv_dab/dab_module/si463x/FMHD_PSD_Handler.o
                0x00000000e8c132b8                albumDisplay
 *fill*         0x00000000e8c1333a        0x6 
 .bss.artistDisplay
                0x00000000e8c13340       0x82 emodules/drv_dab/dab_module/si463x/FMHD_PSD_Handler.o
                0x00000000e8c13340                artistDisplay
 *fill*         0x00000000e8c133c2        0x6 
 .bss.genreDisplay
                0x00000000e8c133c8       0x82 emodules/drv_dab/dab_module/si463x/FMHD_PSD_Handler.o
                0x00000000e8c133c8                genreDisplay
 *fill*         0x00000000e8c1344a        0x6 
 .bss.titleDisplay
                0x00000000e8c13450       0x82 emodules/drv_dab/dab_module/si463x/FMHD_PSD_Handler.o
                0x00000000e8c13450                titleDisplay
 .bss._currentAudioListVersion
                0x00000000e8c134d2        0x2 emodules/drv_dab/dab_module/si463x/FMHD_Service_List_Handler.o
                0x00000000e8c134d2                _currentAudioListVersion
 .bss._currentDataListVersion
                0x00000000e8c134d4        0x2 emodules/drv_dab/dab_module/si463x/FMHD_Service_List_Handler.o
                0x00000000e8c134d4                _currentDataListVersion
 *fill*         0x00000000e8c134d6        0x2 
 .bss.svcListFast
                0x00000000e8c134d8        0x2 emodules/drv_dab/dab_module/si463x/FMHD_Service_List_Handler.o
                0x00000000e8c134d8                svcListFast
 *fill*         0x00000000e8c134da        0x6 
 .bss.svcListFull_Audio
                0x00000000e8c134e0       0xb2 emodules/drv_dab/dab_module/si463x/FMHD_Service_List_Handler.o
                0x00000000e8c134e0                svcListFull_Audio
 .bss.SISupdateStatus
                0x00000000e8c13592        0x1 emodules/drv_dab/dab_module/si463x/FMHD_SIS_Handler.o
                0x00000000e8c13592                SISupdateStatus
 *fill*         0x00000000e8c13593        0x5 
 .bss.slgnDisplay
                0x00000000e8c13598       0x60 emodules/drv_dab/dab_module/si463x/FMHD_SIS_Handler.o
                0x00000000e8c13598                slgnDisplay
 .bss.smDisplay
                0x00000000e8c135f8       0xc0 emodules/drv_dab/dab_module/si463x/FMHD_SIS_Handler.o
                0x00000000e8c135f8                smDisplay
 .bss.snlDisplay
                0x00000000e8c136b8       0x38 emodules/drv_dab/dab_module/si463x/FMHD_SIS_Handler.o
                0x00000000e8c136b8                snlDisplay
 .bss.snsDisplay
                0x00000000e8c136f0        0x7 emodules/drv_dab/dab_module/si463x/FMHD_SIS_Handler.o
                0x00000000e8c136f0                snsDisplay
 *fill*         0x00000000e8c136f7        0x1 
 .bss.stationID
                0x00000000e8c136f8        0x4 emodules/drv_dab/dab_module/si463x/FMHD_SIS_Handler.o
                0x00000000e8c136f8                stationID
 *fill*         0x00000000e8c136fc        0x4 
 .bss.stationLocationLat
                0x00000000e8c13700        0x4 emodules/drv_dab/dab_module/si463x/FMHD_SIS_Handler.o
                0x00000000e8c13700                stationLocationLat
 *fill*         0x00000000e8c13704        0x4 
 .bss.stationLocationLong
                0x00000000e8c13708        0x4 emodules/drv_dab/dab_module/si463x/FMHD_SIS_Handler.o
                0x00000000e8c13708                stationLocationLong
 *fill*         0x00000000e8c1370c        0x4 
 .bss.usnDisplay
                0x00000000e8c13710       0x11 emodules/drv_dab/dab_module/si463x/FMHD_SIS_Handler.o
                0x00000000e8c13710                usnDisplay
 *fill*         0x00000000e8c13721        0x7 
 .bss.alertDisplay
                0x00000000e8c13728      0x190 emodules/drv_dab/dab_module/si463x/FMHD_Alert_Handler.o
                0x00000000e8c13728                alertDisplay
 .bss.currentMessageID
                0x00000000e8c138b8        0x2 emodules/drv_dab/dab_module/si463x/FMHD_Alert_Handler.o
                0x00000000e8c138b8                currentMessageID
 *fill*         0x00000000e8c138ba        0x6 
 .bss.RdsGroupCounters
                0x00000000e8c138c0       0x40 emodules/drv_dab/dab_module/si463x/RDS_Handler.o
 .bss.afCount   0x00000000e8c13900        0x1 emodules/drv_dab/dab_module/si463x/RDS_Handler.o
                0x00000000e8c13900                afCount
 *fill*         0x00000000e8c13901        0x7 
 .bss.afList    0x00000000e8c13908       0x32 emodules/drv_dab/dab_module/si463x/RDS_Handler.o
                0x00000000e8c13908                afList
 *fill*         0x00000000e8c1393a        0x6 
 .bss.currentTime
                0x00000000e8c13940        0x8 emodules/drv_dab/dab_module/si463x/RDS_Handler.o
                0x00000000e8c13940                currentTime
 .bss.eccDisplay
                0x00000000e8c13948        0x1 emodules/drv_dab/dab_module/si463x/RDS_Handler.o
                0x00000000e8c13948                eccDisplay
 .bss.firstInitDone
                0x00000000e8c13949        0x1 emodules/drv_dab/dab_module/si463x/RDS_Handler.o
                0x00000000e8c13949                firstInitDone
 .bss.piDisplay
                0x00000000e8c1394a        0x2 emodules/drv_dab/dab_module/si463x/RDS_Handler.o
                0x00000000e8c1394a                piDisplay
 *fill*         0x00000000e8c1394c        0x4 
 .bss.psCnt     0x00000000e8c13950        0x8 emodules/drv_dab/dab_module/si463x/RDS_Handler.o
 .bss.psDisplay
                0x00000000e8c13958        0x8 emodules/drv_dab/dab_module/si463x/RDS_Handler.o
                0x00000000e8c13958                psDisplay
 .bss.psTmp0    0x00000000e8c13960        0x8 emodules/drv_dab/dab_module/si463x/RDS_Handler.o
 .bss.psTmp1    0x00000000e8c13968        0x8 emodules/drv_dab/dab_module/si463x/RDS_Handler.o
 .bss.ptyDisplay
                0x00000000e8c13970        0x1 emodules/drv_dab/dab_module/si463x/RDS_Handler.o
                0x00000000e8c13970                ptyDisplay
 *fill*         0x00000000e8c13971        0x7 
 .bss.rdsBlerMax
                0x00000000e8c13978        0x4 emodules/drv_dab/dab_module/si463x/RDS_Handler.o
                0x00000000e8c13978                rdsBlerMax
 *fill*         0x00000000e8c1397c        0x4 
 .bss.rtCnt     0x00000000e8c13980       0x40 emodules/drv_dab/dab_module/si463x/RDS_Handler.o
 .bss.rtDisplay
                0x00000000e8c139c0       0x40 emodules/drv_dab/dab_module/si463x/RDS_Handler.o
                0x00000000e8c139c0                rtDisplay
 .bss.rtFlag    0x00000000e8c13a00        0x1 emodules/drv_dab/dab_module/si463x/RDS_Handler.o
 .bss.rtFlagValid
                0x00000000e8c13a01        0x1 emodules/drv_dab/dab_module/si463x/RDS_Handler.o
 *fill*         0x00000000e8c13a02        0x6 
 .bss.rtTmp0    0x00000000e8c13a08       0x40 emodules/drv_dab/dab_module/si463x/RDS_Handler.o
 .bss.rtTmp1    0x00000000e8c13a48       0x40 emodules/drv_dab/dab_module/si463x/RDS_Handler.o
 .bss.cmd_arg_u32
                0x00000000e8c13a88      0x400 emodules/drv_dab/dab_module/si463x/si46xx_firmware_api.o
                0x00000000e8c13a88                cmd_arg_u32
 .bss.fmhd_get_station_info__info_select_sent
                0x00000000e8c13e88        0x1 emodules/drv_dab/dab_module/si463x/si46xx_firmware_api.o
                0x00000000e8c13e88                fmhd_get_station_info__info_select_sent
 .bss.lastCommand
                0x00000000e8c13e89        0x1 emodules/drv_dab/dab_module/si463x/si46xx_firmware_api.o
                0x00000000e8c13e89                lastCommand
 .bss.lastSertype
                0x00000000e8c13e8a        0x1 emodules/drv_dab/dab_module/si463x/si46xx_firmware_api.o
                0x00000000e8c13e8a                lastSertype
 *fill*         0x00000000e8c13e8b        0x5 
 .bss.reply_buffer_u32
                0x00000000e8c13e90     0x201c emodules/drv_dab/dab_module/si463x/si46xx_firmware_api.o
                0x00000000e8c13e90                reply_buffer_u32
 .bss.AS_NUMs   0x00000000e8c15eac        0x1 emodules/drv_dab/dab_module/si463x/si468x.o
                0x00000000e8c15eac                AS_NUMs
 .bss.ATS_RDS_valid_flag
                0x00000000e8c15ead        0x1 emodules/drv_dab/dab_module/si463x/si468x.o
                0x00000000e8c15ead                ATS_RDS_valid_flag
 .bss.Area      0x00000000e8c15eae        0x1 emodules/drv_dab/dab_module/si463x/si468x.o
                0x00000000e8c15eae                Area
 .bss.AutoSeekCase
                0x00000000e8c15eaf        0x1 emodules/drv_dab/dab_module/si463x/si468x.o
                0x00000000e8c15eaf                AutoSeekCase
 .bss.AutoStoreCase
                0x00000000e8c15eb0        0x1 emodules/drv_dab/dab_module/si463x/si468x.o
                0x00000000e8c15eb0                AutoStoreCase
 .bss.Band      0x00000000e8c15eb1        0x1 emodules/drv_dab/dab_module/si463x/si468x.o
                0x00000000e8c15eb1                Band
 .bss.T_Af_fastflag
                0x00000000e8c15eb2        0x1 emodules/drv_dab/dab_module/si463x/si468x.o
                0x00000000e8c15eb2                T_Af_fastflag
 .bss.T_CT_sw   0x00000000e8c15eb3        0x1 emodules/drv_dab/dab_module/si463x/si468x.o
                0x00000000e8c15eb3                T_CT_sw
 *fill*         0x00000000e8c15eb4        0x4 
 .bss.T_Eonpi   0x00000000e8c15eb8        0x2 emodules/drv_dab/dab_module/si463x/si468x.o
                0x00000000e8c15eb8                T_Eonpi
 .bss.T_F_Local
                0x00000000e8c15eba        0x1 emodules/drv_dab/dab_module/si463x/si468x.o
                0x00000000e8c15eba                T_F_Local
 .bss.T_F_Valid_channl
                0x00000000e8c15ebb        0x1 emodules/drv_dab/dab_module/si463x/si468x.o
                0x00000000e8c15ebb                T_F_Valid_channl
 .bss.T_F_rds_on
                0x00000000e8c15ebc        0x1 emodules/drv_dab/dab_module/si463x/si468x.o
                0x00000000e8c15ebc                T_F_rds_on
 .bss.T_RdsInt  0x00000000e8c15ebd        0x1 emodules/drv_dab/dab_module/si463x/si468x.o
                0x00000000e8c15ebd                T_RdsInt
 .bss.T_RdsSync
                0x00000000e8c15ebe        0x1 emodules/drv_dab/dab_module/si463x/si468x.o
                0x00000000e8c15ebe                T_RdsSync
 .bss.T_ResumeAfFlag
                0x00000000e8c15ebf        0x1 emodules/drv_dab/dab_module/si463x/si468x.o
                0x00000000e8c15ebf                T_ResumeAfFlag
 .bss.T_Tu_Snr  0x00000000e8c15ec0        0x1 emodules/drv_dab/dab_module/si463x/si468x.o
                0x00000000e8c15ec0                T_Tu_Snr
 *fill*         0x00000000e8c15ec1        0x7 
 .bss.T_af_1st_divisor
                0x00000000e8c15ec8        0x2 emodules/drv_dab/dab_module/si463x/si468x.o
                0x00000000e8c15ec8                T_af_1st_divisor
 .bss.T_af_cnt  0x00000000e8c15eca        0x1 emodules/drv_dab/dab_module/si463x/si468x.o
                0x00000000e8c15eca                T_af_cnt
 .bss.T_af_inf_f
                0x00000000e8c15ecb        0x1 emodules/drv_dab/dab_module/si463x/si468x.o
                0x00000000e8c15ecb                T_af_inf_f
 *fill*         0x00000000e8c15ecc        0x4 
 .bss.T_af_mask_timer
                0x00000000e8c15ed0        0xa emodules/drv_dab/dab_module/si463x/si468x.o
                0x00000000e8c15ed0                T_af_mask_timer
 .bss.T_af_sw   0x00000000e8c15eda        0x1 emodules/drv_dab/dab_module/si463x/si468x.o
                0x00000000e8c15eda                T_af_sw
 *fill*         0x00000000e8c15edb        0x1 
 .bss.T_af_time
                0x00000000e8c15edc        0x2 emodules/drv_dab/dab_module/si463x/si468x.o
                0x00000000e8c15edc                T_af_time
 .bss.T_af_time2
                0x00000000e8c15ede        0x1 emodules/drv_dab/dab_module/si463x/si468x.o
                0x00000000e8c15ede                T_af_time2
 .bss.T_af_time3
                0x00000000e8c15edf        0x1 emodules/drv_dab/dab_module/si463x/si468x.o
                0x00000000e8c15edf                T_af_time3
 .bss.T_afgo_f  0x00000000e8c15ee0        0x1 emodules/drv_dab/dab_module/si463x/si468x.o
                0x00000000e8c15ee0                T_afgo_f
 *fill*         0x00000000e8c15ee1        0x7 
 .bss.T_bfpi    0x00000000e8c15ee8        0x2 emodules/drv_dab/dab_module/si463x/si468x.o
                0x00000000e8c15ee8                T_bfpi
 *fill*         0x00000000e8c15eea        0x6 
 .bss.T_bkpi    0x00000000e8c15ef0        0x2 emodules/drv_dab/dab_module/si463x/si468x.o
                0x00000000e8c15ef0                T_bkpi
 .bss.T_curr_af
                0x00000000e8c15ef2        0x1 emodules/drv_dab/dab_module/si463x/si468x.o
                0x00000000e8c15ef2                T_curr_af
 *fill*         0x00000000e8c15ef3        0x5 
 .bss.T_curr_ctx
                0x00000000e8c15ef8        0x4 emodules/drv_dab/dab_module/si463x/si468x.o
                0x00000000e8c15ef8                T_curr_ctx
 *fill*         0x00000000e8c15efc        0x4 
 .bss.T_divisor
                0x00000000e8c15f00        0x2 emodules/drv_dab/dab_module/si463x/si468x.o
                0x00000000e8c15f00                T_divisor
 .bss.T_goodcnt
                0x00000000e8c15f02        0x1 emodules/drv_dab/dab_module/si463x/si468x.o
                0x00000000e8c15f02                T_goodcnt
 *fill*         0x00000000e8c15f03        0x5 
 .bss.T_last_divisor
                0x00000000e8c15f08        0x2 emodules/drv_dab/dab_module/si463x/si468x.o
                0x00000000e8c15f08                T_last_divisor
 *fill*         0x00000000e8c15f0a        0x6 
 .bss.T_ltpi    0x00000000e8c15f10        0x2 emodules/drv_dab/dab_module/si463x/si468x.o
                0x00000000e8c15f10                T_ltpi
 .bss.T_network_ptr
                0x00000000e8c15f12        0x1 emodules/drv_dab/dab_module/si463x/si468x.o
                0x00000000e8c15f12                T_network_ptr
 *fill*         0x00000000e8c15f13        0x5 
 .bss.T_networks
                0x00000000e8c15f18      0x1b8 emodules/drv_dab/dab_module/si463x/si468x.o
                0x00000000e8c15f18                T_networks
 .bss.T_pi_bak  0x00000000e8c160d0        0x2 emodules/drv_dab/dab_module/si463x/si468x.o
                0x00000000e8c160d0                T_pi_bak
 .bss.T_piok_f  0x00000000e8c160d2        0x1 emodules/drv_dab/dab_module/si463x/si468x.o
                0x00000000e8c160d2                T_piok_f
 *fill*         0x00000000e8c160d3        0x5 
 .bss.T_preset_pi
                0x00000000e8c160d8       0x24 emodules/drv_dab/dab_module/si463x/si468x.o
                0x00000000e8c160d8                T_preset_pi
 .bss.T_psallok_f
                0x00000000e8c160fc        0x1 emodules/drv_dab/dab_module/si463x/si468x.o
                0x00000000e8c160fc                T_psallok_f
 .bss.T_pty_mode
                0x00000000e8c160fd        0x1 emodules/drv_dab/dab_module/si463x/si468x.o
                0x00000000e8c160fd                T_pty_mode
 .bss.T_pty_seek_last_fm_freq
                0x00000000e8c160fe        0x2 emodules/drv_dab/dab_module/si463x/si468x.o
                0x00000000e8c160fe                T_pty_seek_last_fm_freq
 .bss.T_pty_seek_type
                0x00000000e8c16100        0x1 emodules/drv_dab/dab_module/si463x/si468x.o
                0x00000000e8c16100                T_pty_seek_type
 .bss.T_pty_seek_type_now
                0x00000000e8c16101        0x1 emodules/drv_dab/dab_module/si463x/si468x.o
                0x00000000e8c16101                T_pty_seek_type_now
 .bss.T_ptyok_f
                0x00000000e8c16102        0x1 emodules/drv_dab/dab_module/si463x/si468x.o
                0x00000000e8c16102                T_ptyok_f
 .bss.T_smeter  0x00000000e8c16103        0x1 emodules/drv_dab/dab_module/si463x/si468x.o
                0x00000000e8c16103                T_smeter
 .bss.T_smeter_bak
                0x00000000e8c16104        0x1 emodules/drv_dab/dab_module/si463x/si468x.o
                0x00000000e8c16104                T_smeter_bak
 .bss.T_ta_active
                0x00000000e8c16105        0x1 emodules/drv_dab/dab_module/si463x/si468x.o
                0x00000000e8c16105                T_ta_active
 .bss.T_ta_cnt  0x00000000e8c16106        0x1 emodules/drv_dab/dab_module/si463x/si468x.o
                0x00000000e8c16106                T_ta_cnt
 .bss.T_ta_f    0x00000000e8c16107        0x1 emodules/drv_dab/dab_module/si463x/si468x.o
                0x00000000e8c16107                T_ta_f
 .bss.T_ta_mode
                0x00000000e8c16108        0x1 emodules/drv_dab/dab_module/si463x/si468x.o
                0x00000000e8c16108                T_ta_mode
 .bss.T_ta_on   0x00000000e8c16109        0x1 emodules/drv_dab/dab_module/si463x/si468x.o
                0x00000000e8c16109                T_ta_on
 .bss.T_ta_sw   0x00000000e8c1610a        0x1 emodules/drv_dab/dab_module/si463x/si468x.o
                0x00000000e8c1610a                T_ta_sw
 .bss.T_ta_timer
                0x00000000e8c1610b        0x1 emodules/drv_dab/dab_module/si463x/si468x.o
                0x00000000e8c1610b                T_ta_timer
 .bss.T_tanow_f
                0x00000000e8c1610c        0x1 emodules/drv_dab/dab_module/si463x/si468x.o
                0x00000000e8c1610c                T_tanow_f
 .bss.T_tp_f    0x00000000e8c1610d        0x1 emodules/drv_dab/dab_module/si463x/si468x.o
                0x00000000e8c1610d                T_tp_f
 .bss.T_tpnow_f
                0x00000000e8c1610e        0x1 emodules/drv_dab/dab_module/si463x/si468x.o
                0x00000000e8c1610e                T_tpnow_f
 *fill*         0x00000000e8c1610f        0x1 
 .bss.TunerProcessTimer
                0x00000000e8c16110        0x4 emodules/drv_dab/dab_module/si463x/si468x.o
                0x00000000e8c16110                TunerProcessTimer
 .bss.alert_update_ready_item
                0x00000000e8c16114        0x1 emodules/drv_dab/dab_module/si463x/si468x.o
                0x00000000e8c16114                alert_update_ready_item
 *fill*         0x00000000e8c16115        0x1 
 .bss.am_freq   0x00000000e8c16116        0x2 emodules/drv_dab/dab_module/si463x/si468x.o
                0x00000000e8c16116                am_freq
 .bss.am_pset_table
                0x00000000e8c16118       0xb4 emodules/drv_dab/dab_module/si463x/si468x.o
                0x00000000e8c16118                am_pset_table
 .bss.am_status
                0x00000000e8c161cc        0x1 emodules/drv_dab/dab_module/si463x/si468x.o
                0x00000000e8c161cc                am_status
 *fill*         0x00000000e8c161cd        0x1 
 .bss.amhd_freq
                0x00000000e8c161ce        0x2 emodules/drv_dab/dab_module/si463x/si468x.o
                0x00000000e8c161ce                amhd_freq
 .bss.announcement_start_flag
                0x00000000e8c161d0        0x2 emodules/drv_dab/dab_module/si463x/si468x.o
 *fill*         0x00000000e8c161d2        0x6 
 .bss.bt_para   0x00000000e8c161d8        0x8 emodules/drv_dab/dab_module/si463x/si468x.o
                0x00000000e8c161d8                bt_para
 .bss.curIndex.19715
                0x00000000e8c161e0        0x1 emodules/drv_dab/dab_module/si463x/si468x.o
 .bss.curIndex.19750
                0x00000000e8c161e1        0x1 emodules/drv_dab/dab_module/si463x/si468x.o
 .bss.curIndex.19790
                0x00000000e8c161e2        0x1 emodules/drv_dab/dab_module/si463x/si468x.o
 .bss.cur_dab_band
                0x00000000e8c161e3        0x1 emodules/drv_dab/dab_module/si463x/si468x.o
                0x00000000e8c161e3                cur_dab_band
 .bss.current_ouput_audio_flg
                0x00000000e8c161e4        0x1 emodules/drv_dab/dab_module/si463x/si468x.o
 *fill*         0x00000000e8c161e5        0x3 
 .bss.dab_all_para
                0x00000000e8c161e8        0x8 emodules/drv_dab/dab_module/si463x/si468x.o
                0x00000000e8c161e8                dab_all_para
 .bss.dab_auto_seek_1_status
                0x00000000e8c161f0        0x1 emodules/drv_dab/dab_module/si463x/si468x.o
                0x00000000e8c161f0                dab_auto_seek_1_status
 *fill*         0x00000000e8c161f1        0x3 
 .bss.dab_finding_service_counter
                0x00000000e8c161f4        0x4 emodules/drv_dab/dab_module/si463x/si468x.o
 .bss.dab_fm_rds_link_send_info_flag
                0x00000000e8c161f8        0x4 emodules/drv_dab/dab_module/si463x/si468x.o
                0x00000000e8c161f8                dab_fm_rds_link_send_info_flag
 .bss.dab_get_ensemble_name_flag
                0x00000000e8c161fc        0x1 emodules/drv_dab/dab_module/si463x/si468x.o
                0x00000000e8c161fc                dab_get_ensemble_name_flag
 *fill*         0x00000000e8c161fd        0x3 
 .bss.dab_have_signal_back_dab_count
                0x00000000e8c16200        0x4 emodules/drv_dab/dab_module/si463x/si468x.o
                0x00000000e8c16200                dab_have_signal_back_dab_count
 .bss.dab_last_played_service_list_index
                0x00000000e8c16204        0x1 emodules/drv_dab/dab_module/si463x/si468x.o
                0x00000000e8c16204                dab_last_played_service_list_index
 .bss.dab_manual_status
                0x00000000e8c16205        0x1 emodules/drv_dab/dab_module/si463x/si468x.o
                0x00000000e8c16205                dab_manual_status
 .bss.dab_normalplay
                0x00000000e8c16206        0x1 emodules/drv_dab/dab_module/si463x/si468x.o
 .bss.dab_nosignal_tune_freqcount
                0x00000000e8c16207        0x1 emodules/drv_dab/dab_module/si463x/si468x.o
                0x00000000e8c16207                dab_nosignal_tune_freqcount
 .bss.dab_para  0x00000000e8c16208        0x8 emodules/drv_dab/dab_module/si463x/si468x.o
                0x00000000e8c16208                dab_para
 .bss.dab_pty_seek_status
                0x00000000e8c16210        0x1 emodules/drv_dab/dab_module/si463x/si468x.o
                0x00000000e8c16210                dab_pty_seek_status
 .bss.dab_pty_seek_type
                0x00000000e8c16211        0x1 emodules/drv_dab/dab_module/si463x/si468x.o
                0x00000000e8c16211                dab_pty_seek_type
 .bss.dab_reset_flag
                0x00000000e8c16212        0x1 emodules/drv_dab/dab_module/si463x/si468x.o
 .bss.dab_scan_nosiganl
                0x00000000e8c16213        0x1 emodules/drv_dab/dab_module/si463x/si468x.o
                0x00000000e8c16213                dab_scan_nosiganl
 .bss.dab_select_nosiganl
                0x00000000e8c16214        0x1 emodules/drv_dab/dab_module/si463x/si468x.o
                0x00000000e8c16214                dab_select_nosiganl
 *fill*         0x00000000e8c16215        0x3 
 .bss.dab_sendtomcu_rds_linkings
                0x00000000e8c16218       0x80 emodules/drv_dab/dab_module/si463x/si468x.o
 .bss.dab_setup_info
                0x00000000e8c16298        0x4 emodules/drv_dab/dab_module/si463x/si468x.o
                0x00000000e8c16298                dab_setup_info
 .bss.dab_signal_quality
                0x00000000e8c1629c        0x1 emodules/drv_dab/dab_module/si463x/si468x.o
 *fill*         0x00000000e8c1629d        0x3 
 .bss.dab_skip_update_metrics_audio_index_bysidscids_in_srvlist
                0x00000000e8c162a0        0x4 emodules/drv_dab/dab_module/si463x/si468x.o
                0x00000000e8c162a0                dab_skip_update_metrics_audio_index_bysidscids_in_srvlist
 .bss.dab_to_fm_debounce
                0x00000000e8c162a4        0x1 emodules/drv_dab/dab_module/si463x/si468x.o
 .bss.dab_wait_cts_timeout_count
                0x00000000e8c162a5        0x1 emodules/drv_dab/dab_module/si463x/si468x.o
                0x00000000e8c162a5                dab_wait_cts_timeout_count
 *fill*         0x00000000e8c162a6        0x2 
 .bss.display_para
                0x00000000e8c162a8        0x8 emodules/drv_dab/dab_module/si463x/si468x.o
                0x00000000e8c162a8                display_para
 .bss.do_linking_bkinfo
                0x00000000e8c162b0        0x8 emodules/drv_dab/dab_module/si463x/si468x.o
                0x00000000e8c162b0                do_linking_bkinfo
 .bss.fm_2_dab_searching_flg
                0x00000000e8c162b8        0x1 emodules/drv_dab/dab_module/si463x/si468x.o
 *fill*         0x00000000e8c162b9        0x1 
 .bss.fm_freq   0x00000000e8c162ba        0x2 emodules/drv_dab/dab_module/si463x/si468x.o
                0x00000000e8c162ba                fm_freq
 *fill*         0x00000000e8c162bc        0x4 
 .bss.fm_para   0x00000000e8c162c0        0x8 emodules/drv_dab/dab_module/si463x/si468x.o
                0x00000000e8c162c0                fm_para
 .bss.fm_pset_table
                0x00000000e8c162c8       0xfc emodules/drv_dab/dab_module/si463x/si468x.o
                0x00000000e8c162c8                fm_pset_table
 .bss.fm_status
                0x00000000e8c163c4        0x1 emodules/drv_dab/dab_module/si463x/si468x.o
                0x00000000e8c163c4                fm_status
 *fill*         0x00000000e8c163c5        0x1 
 .bss.fmhd_freq
                0x00000000e8c163c6        0x2 emodules/drv_dab/dab_module/si463x/si468x.o
                0x00000000e8c163c6                fmhd_freq
 .bss.freq.20153
                0x00000000e8c163c8        0x2 emodules/drv_dab/dab_module/si463x/si468x.o
 .bss.freqIndex.19690
                0x00000000e8c163ca        0x1 emodules/drv_dab/dab_module/si463x/si468x.o
 *fill*         0x00000000e8c163cb        0x1 
 .bss.freq_start.20113
                0x00000000e8c163cc        0x2 emodules/drv_dab/dab_module/si463x/si468x.o
 .bss.gSeekUpDn
                0x00000000e8c163ce        0x1 emodules/drv_dab/dab_module/si463x/si468x.o
 .bss.gThreshold_SNR
                0x00000000e8c163cf        0x1 emodules/drv_dab/dab_module/si463x/si468x.o
                0x00000000e8c163cf                gThreshold_SNR
 .bss.i.19713   0x00000000e8c163d0        0x1 emodules/drv_dab/dab_module/si463x/si468x.o
 .bss.i.19748   0x00000000e8c163d1        0x1 emodules/drv_dab/dab_module/si463x/si468x.o
 .bss.i.19788   0x00000000e8c163d2        0x1 emodules/drv_dab/dab_module/si463x/si468x.o
 .bss.index.20155
                0x00000000e8c163d3        0x1 emodules/drv_dab/dab_module/si463x/si468x.o
 .bss.into_preset_list
                0x00000000e8c163d4        0x1 emodules/drv_dab/dab_module/si463x/si468x.o
                0x00000000e8c163d4                into_preset_list
 .bss.itemCase.19633
                0x00000000e8c163d5        0x1 emodules/drv_dab/dab_module/si463x/si468x.o
 *fill*         0x00000000e8c163d6        0x2 
 .bss.msg_queue
                0x00000000e8c163d8        0x8 emodules/drv_dab/dab_module/si463x/si468x.o
                0x00000000e8c163d8                msg_queue
 .bss.novalid_count.19645
                0x00000000e8c163e0        0x1 emodules/drv_dab/dab_module/si463x/si468x.o
 .bss.on_dab_view_app_flg
                0x00000000e8c163e1        0x1 emodules/drv_dab/dab_module/si463x/si468x.o
 .bss.on_dab_view_channel_list_flg
                0x00000000e8c163e2        0x1 emodules/drv_dab/dab_module/si463x/si468x.o
 *fill*         0x00000000e8c163e3        0x5 
 .bss.para_init
                0x00000000e8c163e8        0x8 emodules/drv_dab/dab_module/si463x/si468x.o
                0x00000000e8c163e8                para_init
 .bss.play_announcement_bkinfo
                0x00000000e8c163f0        0x8 emodules/drv_dab/dab_module/si463x/si468x.o
                0x00000000e8c163f0                play_announcement_bkinfo
 .bss.play_hard_rds_success_flg
                0x00000000e8c163f8        0x1 emodules/drv_dab/dab_module/si463x/si468x.o
 .bss.preset_num
                0x00000000e8c163f9        0x1 emodules/drv_dab/dab_module/si463x/si468x.o
                0x00000000e8c163f9                preset_num
 .bss.psd_update_ready_item
                0x00000000e8c163fa        0x1 emodules/drv_dab/dab_module/si463x/si468x.o
                0x00000000e8c163fa                psd_update_ready_item
 *fill*         0x00000000e8c163fb        0x5 
 .bss.pty_serach_bkinfo
                0x00000000e8c16400        0x8 emodules/drv_dab/dab_module/si463x/si468x.o
                0x00000000e8c16400                pty_serach_bkinfo
 .bss.pty_tune_count
                0x00000000e8c16408        0x1 emodules/drv_dab/dab_module/si463x/si468x.o
 .bss.rds_check_count.19681
                0x00000000e8c16409        0x1 emodules/drv_dab/dab_module/si463x/si468x.o
 .bss.rds_update_ready_item
                0x00000000e8c1640a        0x1 emodules/drv_dab/dab_module/si463x/si468x.o
                0x00000000e8c1640a                rds_update_ready_item
 .bss.result.19716
                0x00000000e8c1640b        0x1 emodules/drv_dab/dab_module/si463x/si468x.o
 .bss.result.19752
                0x00000000e8c1640c        0x1 emodules/drv_dab/dab_module/si463x/si468x.o
 .bss.result.19791
                0x00000000e8c1640d        0x1 emodules/drv_dab/dab_module/si463x/si468x.o
 *fill*         0x00000000e8c1640e        0x2 
 .bss.root_para
                0x00000000e8c16410        0x8 emodules/drv_dab/dab_module/si463x/si468x.o
                0x00000000e8c16410                root_para
 .bss.seek_ensemble_falg.19717
                0x00000000e8c16418        0x1 emodules/drv_dab/dab_module/si463x/si468x.o
 .bss.seek_ensemble_falg.19753
                0x00000000e8c16419        0x1 emodules/drv_dab/dab_module/si463x/si468x.o
 .bss.seek_state
                0x00000000e8c1641a        0x1 emodules/drv_dab/dab_module/si463x/si468x.o
                0x00000000e8c1641a                seek_state
 *fill*         0x00000000e8c1641b        0x1 
 .bss.sendtomcu_start_seek_pi
                0x00000000e8c1641c        0x2 emodules/drv_dab/dab_module/si463x/si468x.o
 *fill*         0x00000000e8c1641e        0x2 
 .bss.si4634_lock
                0x00000000e8c16420        0x8 emodules/drv_dab/dab_module/si463x/si468x.o
                0x00000000e8c16420                si4634_lock
 .bss.si463x_bus_tid
                0x00000000e8c16428        0x4 emodules/drv_dab/dab_module/si463x/si468x.o
 .bss.sis_update_ready_item
                0x00000000e8c1642c        0x1 emodules/drv_dab/dab_module/si463x/si468x.o
                0x00000000e8c1642c                sis_update_ready_item
 .bss.startIndex.19714
                0x00000000e8c1642d        0x1 emodules/drv_dab/dab_module/si463x/si468x.o
 .bss.startIndex.19749
                0x00000000e8c1642e        0x1 emodules/drv_dab/dab_module/si463x/si468x.o
 .bss.startIndex.19789
                0x00000000e8c1642f        0x1 emodules/drv_dab/dab_module/si463x/si468x.o
 .bss.system_para
                0x00000000e8c16430        0x8 emodules/drv_dab/dab_module/si463x/si468x.o
                0x00000000e8c16430                system_para
 .bss.time1.19646
                0x00000000e8c16438        0x4 emodules/drv_dab/dab_module/si463x/si468x.o
 .bss.wait_pty_count
                0x00000000e8c1643c        0x1 emodules/drv_dab/dab_module/si463x/si468x.o
 *fill*         0x00000000e8c1643d        0x3 
 .bss.dab_fiic  0x00000000e8c16440        0x4 emodules/drv_dab/dab_module/si463x/si468x_bus.o
                0x00000000e8c16440                dab_fiic
 .bss.mot_header_mode_body_completed_size
                0x00000000e8c16444        0x4 emodules/drv_dab/dab_module/si463x/mot.o
 .bss.mot_header_mode_body_next_segment_number
                0x00000000e8c16448        0x2 emodules/drv_dab/dab_module/si463x/mot.o
 *fill*         0x00000000e8c1644a        0x6 
 .bss.mot_header_mode_object_buf
                0x00000000e8c16450        0x8 emodules/drv_dab/dab_module/si463x/mot.o
 .bss.mot_header_mode_object_received_len
                0x00000000e8c16458        0x4 emodules/drv_dab/dab_module/si463x/mot.o
 *fill*         0x00000000e8c1645c        0x4 
 .bss.mot_header_total_buf
                0x00000000e8c16460        0x8 emodules/drv_dab/dab_module/si463x/mot.o
 .bss.sls_obj_send_to_ui_buf
                0x00000000e8c16468        0x8 emodules/drv_dab/dab_module/si463x/mot.o
 .bss.sls_obj_send_to_ui_received_size
                0x00000000e8c16470        0x4 emodules/drv_dab/dab_module/si463x/mot.o
 *(.gnu.linkonce.b.*)
 *(.sbss)
 *(.sbss.*)
 *(.gnu.linkonce.sb.*)
 *(.sbss2)
 *(.sbss2.*)
 *(.gnu.linkonce.sb2.*)
 *(COMMON)
 *(.scommon)
                0x00000000e8c16474                . = ALIGN (0x4)
                0x00000000e8c16474                __bss_end = ABSOLUTE (.)
                0x00000000e8c16474                _end = ABSOLUTE (.)

MAGIC           0x00000000ffff0000       0x50
 *.o(.magic)
 .magic         0x00000000ffff0000       0x50 emodules/drv_dab/magic.o
                0x00000000ffff0000                modinfo

.note
 *(.note)

.stab
 *(.stab)

.stabstr
 *(.stabstr)

.stab.excl
 *(.stab.excl)

.stab.exclstr
 *(.stab.exclstr)

.stab.index
 *(.stab.index)

.stab.indexstr
 *(.stab.indexstr)

.mdebug
 *(.mdebug)

.reginfo
 *(.reginfo)

.comment        0x0000000000000000       0x32
 *(.comment)
 .comment       0x0000000000000000       0x32 emodules/drv_dab/dev_dab.o
                                         0x33 (size before relaxing)
 .comment       0x0000000000000032       0x33 emodules/drv_dab/drv_dab.o
 .comment       0x0000000000000032       0x33 emodules/drv_dab/dab_module/si463x/DAB_DLS_Handler.o
 .comment       0x0000000000000032       0x33 emodules/drv_dab/dab_module/si463x/DAB_Service_List_Handler.o
 .comment       0x0000000000000032       0x33 emodules/drv_dab/dab_module/si463x/Firmware_API_Manager.o
 .comment       0x0000000000000032       0x33 emodules/drv_dab/dab_module/si463x/Firmware_Load_Helpers.o
 .comment       0x0000000000000032       0x33 emodules/drv_dab/dab_module/si463x/FMHD_PSD_Handler.o
 .comment       0x0000000000000032       0x33 emodules/drv_dab/dab_module/si463x/FMHD_Service_List_Handler.o
 .comment       0x0000000000000032       0x33 emodules/drv_dab/dab_module/si463x/FMHD_SIS_Handler.o
 .comment       0x0000000000000032       0x33 emodules/drv_dab/dab_module/si463x/FMHD_Alert_Handler.o
 .comment       0x0000000000000032       0x33 emodules/drv_dab/dab_module/si463x/HAL.o
 .comment       0x0000000000000032       0x33 emodules/drv_dab/dab_module/si463x/Hardware_Calibration_Helpers.o
 .comment       0x0000000000000032       0x33 emodules/drv_dab/dab_module/si463x/rds.o
 .comment       0x0000000000000032       0x33 emodules/drv_dab/dab_module/si463x/RDS_Handler.o
 .comment       0x0000000000000032       0x33 emodules/drv_dab/dab_module/si463x/si46xx_firmware_api.o
 .comment       0x0000000000000032       0x33 emodules/drv_dab/dab_module/si463x/si468x.o
 .comment       0x0000000000000032       0x33 emodules/drv_dab/dab_module/si463x/si468x_bus.o
 .comment       0x0000000000000032       0x33 emodules/drv_dab/dab_module/si463x/mot.o
 .comment       0x0000000000000032       0x33 emodules/drv_dab/magic.o
 .comment       0x0000000000000032       0x33 ./elibrary/bin//libsyscall.a(syscall.o)
 .comment       0x0000000000000032       0x33 ./elibrary/bin//libsyscall.a(syscall_fops.o)
 .comment       0x0000000000000032       0x33 ./elibrary/bin//libminic.a(elibs_sprintf.o)
 .comment       0x0000000000000032       0x33 ./elibrary/bin//libminic.a(elibs_stdio.o)
 .comment       0x0000000000000032       0x33 ./elibrary/bin//libminic.a(elibs_string.o)
 .comment       0x0000000000000032       0x33 /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/../../../../riscv64-unknown-elf/lib/rv64imafdcxtheadc/lp64d/libc.a(lib_a-memcpy.o)
 .comment       0x0000000000000032       0x33 /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/../../../../riscv64-unknown-elf/lib/rv64imafdcxtheadc/lp64d/libc.a(lib_a-sysclose.o)
 .comment       0x0000000000000032       0x33 /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/../../../../riscv64-unknown-elf/lib/rv64imafdcxtheadc/lp64d/libc.a(lib_a-sysopen.o)
 .comment       0x0000000000000032       0x33 /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/../../../../riscv64-unknown-elf/lib/rv64imafdcxtheadc/lp64d/libc.a(lib_a-impure.o)

.ARM.attributes
 *(.ARM.attributes)

.riscv.attributes
                0x0000000000000000       0x48
 *(.riscv.attributes)
 .riscv.attributes
                0x0000000000000000       0x3d emodules/drv_dab/dev_dab.o
 .riscv.attributes
                0x000000000000003d       0x3d emodules/drv_dab/drv_dab.o
 .riscv.attributes
                0x000000000000007a       0x3d emodules/drv_dab/dab_module/si463x/DAB_DLS_Handler.o
 .riscv.attributes
                0x00000000000000b7       0x3d emodules/drv_dab/dab_module/si463x/DAB_Service_List_Handler.o
 .riscv.attributes
                0x00000000000000f4       0x3d emodules/drv_dab/dab_module/si463x/Firmware_API_Manager.o
 .riscv.attributes
                0x0000000000000131       0x3d emodules/drv_dab/dab_module/si463x/Firmware_Load_Helpers.o
 .riscv.attributes
                0x000000000000016e       0x3d emodules/drv_dab/dab_module/si463x/FMHD_PSD_Handler.o
 .riscv.attributes
                0x00000000000001ab       0x3d emodules/drv_dab/dab_module/si463x/FMHD_Service_List_Handler.o
 .riscv.attributes
                0x00000000000001e8       0x3d emodules/drv_dab/dab_module/si463x/FMHD_SIS_Handler.o
 .riscv.attributes
                0x0000000000000225       0x3d emodules/drv_dab/dab_module/si463x/FMHD_Alert_Handler.o
 .riscv.attributes
                0x0000000000000262       0x3d emodules/drv_dab/dab_module/si463x/HAL.o
 .riscv.attributes
                0x000000000000029f       0x3d emodules/drv_dab/dab_module/si463x/Hardware_Calibration_Helpers.o
 .riscv.attributes
                0x00000000000002dc       0x3d emodules/drv_dab/dab_module/si463x/rds.o
 .riscv.attributes
                0x0000000000000319       0x3d emodules/drv_dab/dab_module/si463x/RDS_Handler.o
 .riscv.attributes
                0x0000000000000356       0x3d emodules/drv_dab/dab_module/si463x/si46xx_firmware_api.o
 .riscv.attributes
                0x0000000000000393       0x3d emodules/drv_dab/dab_module/si463x/si468x.o
 .riscv.attributes
                0x00000000000003d0       0x3d emodules/drv_dab/dab_module/si463x/si468x_bus.o
 .riscv.attributes
                0x000000000000040d       0x3d emodules/drv_dab/dab_module/si463x/mot.o
 .riscv.attributes
                0x000000000000044a       0x3d emodules/drv_dab/magic.o
 .riscv.attributes
                0x0000000000000487       0x3d ./elibrary/bin//libsyscall.a(syscall.o)
 .riscv.attributes
                0x00000000000004c4       0x3d ./elibrary/bin//libsyscall.a(syscall_fops.o)
 .riscv.attributes
                0x0000000000000501       0x3d ./elibrary/bin//libminic.a(elibs_sprintf.o)
 .riscv.attributes
                0x000000000000053e       0x3d ./elibrary/bin//libminic.a(elibs_stdio.o)
 .riscv.attributes
                0x000000000000057b       0x3d ./elibrary/bin//libminic.a(elibs_string.o)
 .riscv.attributes
                0x00000000000005b8       0x3e /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/../../../../riscv64-unknown-elf/lib/rv64imafdcxtheadc/lp64d/libc.a(lib_a-memcpy.o)
 .riscv.attributes
                0x00000000000005f6       0x3e /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/../../../../riscv64-unknown-elf/lib/rv64imafdcxtheadc/lp64d/libc.a(lib_a-memset.o)
 .riscv.attributes
                0x0000000000000634       0x3e /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/../../../../riscv64-unknown-elf/lib/rv64imafdcxtheadc/lp64d/libc.a(lib_a-sysclose.o)
 .riscv.attributes
                0x0000000000000672       0x3e /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/../../../../riscv64-unknown-elf/lib/rv64imafdcxtheadc/lp64d/libc.a(lib_a-sysopen.o)
 .riscv.attributes
                0x00000000000006b0       0x3e /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/../../../../riscv64-unknown-elf/lib/rv64imafdcxtheadc/lp64d/libc.a(lib_a-impure.o)

/DISCARD/
 *(.note.GNU-stack)
 *(.ARM.exidx* .gnu.linkonce.armexidx.*)
OUTPUT(emodules/drv_dab/dab.o elf64-littleriscv)

.debug_info     0x0000000000000000    0x6946e
 .debug_info    0x0000000000000000     0x36fa emodules/drv_dab/dev_dab.o
 .debug_info    0x00000000000036fa     0x2c14 emodules/drv_dab/drv_dab.o
 .debug_info    0x000000000000630e     0x31e8 emodules/drv_dab/dab_module/si463x/DAB_DLS_Handler.o
 .debug_info    0x00000000000094f6     0x487a emodules/drv_dab/dab_module/si463x/DAB_Service_List_Handler.o
 .debug_info    0x000000000000dd70     0xb52a emodules/drv_dab/dab_module/si463x/Firmware_API_Manager.o
 .debug_info    0x000000000001929a     0x22c0 emodules/drv_dab/dab_module/si463x/Firmware_Load_Helpers.o
 .debug_info    0x000000000001b55a     0x24ee emodules/drv_dab/dab_module/si463x/FMHD_PSD_Handler.o
 .debug_info    0x000000000001da48     0x2558 emodules/drv_dab/dab_module/si463x/FMHD_Service_List_Handler.o
 .debug_info    0x000000000001ffa0     0x2781 emodules/drv_dab/dab_module/si463x/FMHD_SIS_Handler.o
 .debug_info    0x0000000000022721     0x23c1 emodules/drv_dab/dab_module/si463x/FMHD_Alert_Handler.o
 .debug_info    0x0000000000024ae2     0x1cf9 emodules/drv_dab/dab_module/si463x/HAL.o
 .debug_info    0x00000000000267db     0x1adb emodules/drv_dab/dab_module/si463x/Hardware_Calibration_Helpers.o
 .debug_info    0x00000000000282b6     0x3238 emodules/drv_dab/dab_module/si463x/rds.o
 .debug_info    0x000000000002b4ee     0x652b emodules/drv_dab/dab_module/si463x/RDS_Handler.o
 .debug_info    0x0000000000031a19     0x6e7a emodules/drv_dab/dab_module/si463x/si46xx_firmware_api.o
 .debug_info    0x0000000000038893    0x1188a emodules/drv_dab/dab_module/si463x/si468x.o
 .debug_info    0x000000000004a11d     0x209c emodules/drv_dab/dab_module/si463x/si468x_bus.o
 .debug_info    0x000000000004c1b9     0x1a6e emodules/drv_dab/dab_module/si463x/mot.o
 .debug_info    0x000000000004dc27     0x1edb emodules/drv_dab/magic.o
 .debug_info    0x000000000004fb02    0x12bf0 ./elibrary/bin//libsyscall.a(syscall.o)
 .debug_info    0x00000000000626f2     0x2f5e ./elibrary/bin//libsyscall.a(syscall_fops.o)
 .debug_info    0x0000000000065650      0x7d5 ./elibrary/bin//libminic.a(elibs_sprintf.o)
 .debug_info    0x0000000000065e25     0x2b1c ./elibrary/bin//libminic.a(elibs_stdio.o)
 .debug_info    0x0000000000068941      0xb2d ./elibrary/bin//libminic.a(elibs_string.o)

.debug_abbrev   0x0000000000000000     0x58c0
 .debug_abbrev  0x0000000000000000      0x483 emodules/drv_dab/dev_dab.o
 .debug_abbrev  0x0000000000000483      0x3d3 emodules/drv_dab/drv_dab.o
 .debug_abbrev  0x0000000000000856      0x42d emodules/drv_dab/dab_module/si463x/DAB_DLS_Handler.o
 .debug_abbrev  0x0000000000000c83      0x4aa emodules/drv_dab/dab_module/si463x/DAB_Service_List_Handler.o
 .debug_abbrev  0x000000000000112d      0x66a emodules/drv_dab/dab_module/si463x/Firmware_API_Manager.o
 .debug_abbrev  0x0000000000001797      0x333 emodules/drv_dab/dab_module/si463x/Firmware_Load_Helpers.o
 .debug_abbrev  0x0000000000001aca      0x2c0 emodules/drv_dab/dab_module/si463x/FMHD_PSD_Handler.o
 .debug_abbrev  0x0000000000001d8a      0x2d8 emodules/drv_dab/dab_module/si463x/FMHD_Service_List_Handler.o
 .debug_abbrev  0x0000000000002062      0x30f emodules/drv_dab/dab_module/si463x/FMHD_SIS_Handler.o
 .debug_abbrev  0x0000000000002371      0x2d2 emodules/drv_dab/dab_module/si463x/FMHD_Alert_Handler.o
 .debug_abbrev  0x0000000000002643      0x32e emodules/drv_dab/dab_module/si463x/HAL.o
 .debug_abbrev  0x0000000000002971      0x25a emodules/drv_dab/dab_module/si463x/Hardware_Calibration_Helpers.o
 .debug_abbrev  0x0000000000002bcb      0x477 emodules/drv_dab/dab_module/si463x/rds.o
 .debug_abbrev  0x0000000000003042      0x4e6 emodules/drv_dab/dab_module/si463x/RDS_Handler.o
 .debug_abbrev  0x0000000000003528      0x472 emodules/drv_dab/dab_module/si463x/si46xx_firmware_api.o
 .debug_abbrev  0x000000000000399a      0x6d8 emodules/drv_dab/dab_module/si463x/si468x.o
 .debug_abbrev  0x0000000000004072      0x3e8 emodules/drv_dab/dab_module/si463x/si468x_bus.o
 .debug_abbrev  0x000000000000445a      0x3ff emodules/drv_dab/dab_module/si463x/mot.o
 .debug_abbrev  0x0000000000004859      0x267 emodules/drv_dab/magic.o
 .debug_abbrev  0x0000000000004ac0      0x3a2 ./elibrary/bin//libsyscall.a(syscall.o)
 .debug_abbrev  0x0000000000004e62      0x3ec ./elibrary/bin//libsyscall.a(syscall_fops.o)
 .debug_abbrev  0x000000000000524e      0x14d ./elibrary/bin//libminic.a(elibs_sprintf.o)
 .debug_abbrev  0x000000000000539b      0x3d0 ./elibrary/bin//libminic.a(elibs_stdio.o)
 .debug_abbrev  0x000000000000576b      0x155 ./elibrary/bin//libminic.a(elibs_string.o)

.debug_loc      0x0000000000000000    0x2644e
 .debug_loc     0x0000000000000000      0xc45 emodules/drv_dab/dev_dab.o
 .debug_loc     0x0000000000000c45      0x2bb emodules/drv_dab/drv_dab.o
 .debug_loc     0x0000000000000f00      0x840 emodules/drv_dab/dab_module/si463x/DAB_DLS_Handler.o
 .debug_loc     0x0000000000001740     0x2472 emodules/drv_dab/dab_module/si463x/DAB_Service_List_Handler.o
 .debug_loc     0x0000000000003bb2     0x5d42 emodules/drv_dab/dab_module/si463x/Firmware_API_Manager.o
 .debug_loc     0x00000000000098f4      0x1cd emodules/drv_dab/dab_module/si463x/Firmware_Load_Helpers.o
 .debug_loc     0x0000000000009ac1      0x278 emodules/drv_dab/dab_module/si463x/FMHD_PSD_Handler.o
 .debug_loc     0x0000000000009d39      0x2ab emodules/drv_dab/dab_module/si463x/FMHD_Service_List_Handler.o
 .debug_loc     0x0000000000009fe4      0x285 emodules/drv_dab/dab_module/si463x/FMHD_SIS_Handler.o
 .debug_loc     0x000000000000a269      0x152 emodules/drv_dab/dab_module/si463x/FMHD_Alert_Handler.o
 .debug_loc     0x000000000000a3bb      0x427 emodules/drv_dab/dab_module/si463x/HAL.o
 .debug_loc     0x000000000000a7e2      0x108 emodules/drv_dab/dab_module/si463x/Hardware_Calibration_Helpers.o
 .debug_loc     0x000000000000a8ea      0x6e5 emodules/drv_dab/dab_module/si463x/rds.o
 .debug_loc     0x000000000000afcf      0xdbe emodules/drv_dab/dab_module/si463x/RDS_Handler.o
 .debug_loc     0x000000000000bd8d     0x465c emodules/drv_dab/dab_module/si463x/si46xx_firmware_api.o
 .debug_loc     0x00000000000103e9     0x39a2 emodules/drv_dab/dab_module/si463x/si468x.o
 .debug_loc     0x0000000000013d8b      0x335 emodules/drv_dab/dab_module/si463x/si468x_bus.o
 .debug_loc     0x00000000000140c0      0x550 emodules/drv_dab/dab_module/si463x/mot.o
 .debug_loc     0x0000000000014610     0xda5d ./elibrary/bin//libsyscall.a(syscall.o)
 .debug_loc     0x000000000002206d     0x1325 ./elibrary/bin//libsyscall.a(syscall_fops.o)
 .debug_loc     0x0000000000023392      0x3f2 ./elibrary/bin//libminic.a(elibs_sprintf.o)
 .debug_loc     0x0000000000023784     0x25c5 ./elibrary/bin//libminic.a(elibs_stdio.o)
 .debug_loc     0x0000000000025d49      0x705 ./elibrary/bin//libminic.a(elibs_string.o)

.debug_aranges  0x0000000000000000     0x4210
 .debug_aranges
                0x0000000000000000       0xb0 emodules/drv_dab/dev_dab.o
 .debug_aranges
                0x00000000000000b0       0x80 emodules/drv_dab/drv_dab.o
 .debug_aranges
                0x0000000000000130       0x90 emodules/drv_dab/dab_module/si463x/DAB_DLS_Handler.o
 .debug_aranges
                0x00000000000001c0      0x1a0 emodules/drv_dab/dab_module/si463x/DAB_Service_List_Handler.o
 .debug_aranges
                0x0000000000000360      0x970 emodules/drv_dab/dab_module/si463x/Firmware_API_Manager.o
 .debug_aranges
                0x0000000000000cd0       0x60 emodules/drv_dab/dab_module/si463x/Firmware_Load_Helpers.o
 .debug_aranges
                0x0000000000000d30       0x50 emodules/drv_dab/dab_module/si463x/FMHD_PSD_Handler.o
 .debug_aranges
                0x0000000000000d80       0x40 emodules/drv_dab/dab_module/si463x/FMHD_Service_List_Handler.o
 .debug_aranges
                0x0000000000000dc0       0x40 emodules/drv_dab/dab_module/si463x/FMHD_SIS_Handler.o
 .debug_aranges
                0x0000000000000e00       0x40 emodules/drv_dab/dab_module/si463x/FMHD_Alert_Handler.o
 .debug_aranges
                0x0000000000000e40       0x90 emodules/drv_dab/dab_module/si463x/HAL.o
 .debug_aranges
                0x0000000000000ed0       0x40 emodules/drv_dab/dab_module/si463x/Hardware_Calibration_Helpers.o
 .debug_aranges
                0x0000000000000f10      0x160 emodules/drv_dab/dab_module/si463x/rds.o
 .debug_aranges
                0x0000000000001070       0xa0 emodules/drv_dab/dab_module/si463x/RDS_Handler.o
 .debug_aranges
                0x0000000000001110      0x600 emodules/drv_dab/dab_module/si463x/si46xx_firmware_api.o
 .debug_aranges
                0x0000000000001710      0x8f0 emodules/drv_dab/dab_module/si463x/si468x.o
 .debug_aranges
                0x0000000000002000       0x90 emodules/drv_dab/dab_module/si463x/si468x_bus.o
 .debug_aranges
                0x0000000000002090       0x70 emodules/drv_dab/dab_module/si463x/mot.o
 .debug_aranges
                0x0000000000002100       0x20 emodules/drv_dab/magic.o
 .debug_aranges
                0x0000000000002120     0x1840 ./elibrary/bin//libsyscall.a(syscall.o)
 .debug_aranges
                0x0000000000003960      0x260 ./elibrary/bin//libsyscall.a(syscall_fops.o)
 .debug_aranges
                0x0000000000003bc0       0x80 ./elibrary/bin//libminic.a(elibs_sprintf.o)
 .debug_aranges
                0x0000000000003c40      0x480 ./elibrary/bin//libminic.a(elibs_stdio.o)
 .debug_aranges
                0x00000000000040c0      0x150 ./elibrary/bin//libminic.a(elibs_string.o)

.debug_line     0x0000000000000000    0x48ced
 .debug_line    0x0000000000000000     0x1291 emodules/drv_dab/dev_dab.o
 .debug_line    0x0000000000001291      0xabb emodules/drv_dab/drv_dab.o
 .debug_line    0x0000000000001d4c      0xd6b emodules/drv_dab/dab_module/si463x/DAB_DLS_Handler.o
 .debug_line    0x0000000000002ab7     0x33e4 emodules/drv_dab/dab_module/si463x/DAB_Service_List_Handler.o
 .debug_line    0x0000000000005e9b     0xb6e9 emodules/drv_dab/dab_module/si463x/Firmware_API_Manager.o
 .debug_line    0x0000000000011584      0xac5 emodules/drv_dab/dab_module/si463x/Firmware_Load_Helpers.o
 .debug_line    0x0000000000012049      0xa06 emodules/drv_dab/dab_module/si463x/FMHD_PSD_Handler.o
 .debug_line    0x0000000000012a4f      0xec5 emodules/drv_dab/dab_module/si463x/FMHD_Service_List_Handler.o
 .debug_line    0x0000000000013914      0xc36 emodules/drv_dab/dab_module/si463x/FMHD_SIS_Handler.o
 .debug_line    0x000000000001454a      0x9de emodules/drv_dab/dab_module/si463x/FMHD_Alert_Handler.o
 .debug_line    0x0000000000014f28      0x99e emodules/drv_dab/dab_module/si463x/HAL.o
 .debug_line    0x00000000000158c6      0x683 emodules/drv_dab/dab_module/si463x/Hardware_Calibration_Helpers.o
 .debug_line    0x0000000000015f49     0x1cae emodules/drv_dab/dab_module/si463x/rds.o
 .debug_line    0x0000000000017bf7     0x1dde emodules/drv_dab/dab_module/si463x/RDS_Handler.o
 .debug_line    0x00000000000199d5     0x7598 emodules/drv_dab/dab_module/si463x/si46xx_firmware_api.o
 .debug_line    0x0000000000020f6d    0x12f72 emodules/drv_dab/dab_module/si463x/si468x.o
 .debug_line    0x0000000000033edf      0xb5c emodules/drv_dab/dab_module/si463x/si468x_bus.o
 .debug_line    0x0000000000034a3b      0xfca emodules/drv_dab/dab_module/si463x/mot.o
 .debug_line    0x0000000000035a05      0x6be emodules/drv_dab/magic.o
 .debug_line    0x00000000000360c3     0xe68f ./elibrary/bin//libsyscall.a(syscall.o)
 .debug_line    0x0000000000044752     0x1864 ./elibrary/bin//libsyscall.a(syscall_fops.o)
 .debug_line    0x0000000000045fb6      0x495 ./elibrary/bin//libminic.a(elibs_sprintf.o)
 .debug_line    0x000000000004644b     0x22a9 ./elibrary/bin//libminic.a(elibs_stdio.o)
 .debug_line    0x00000000000486f4      0x5f9 ./elibrary/bin//libminic.a(elibs_string.o)

.debug_str      0x0000000000000000    0x15283
 .debug_str     0x0000000000000000     0x2752 emodules/drv_dab/dev_dab.o
                                       0x2a70 (size before relaxing)
 .debug_str     0x0000000000002752      0x21c emodules/drv_dab/drv_dab.o
                                       0x1f81 (size before relaxing)
 .debug_str     0x000000000000296e      0xcc2 emodules/drv_dab/dab_module/si463x/DAB_DLS_Handler.o
                                       0x2959 (size before relaxing)
 .debug_str     0x0000000000003630      0xcd2 emodules/drv_dab/dab_module/si463x/DAB_Service_List_Handler.o
                                       0x30e8 (size before relaxing)
 .debug_str     0x0000000000004302     0x308a emodules/drv_dab/dab_module/si463x/Firmware_API_Manager.o
                                       0x688f (size before relaxing)
 .debug_str     0x000000000000738c      0x15a emodules/drv_dab/dab_module/si463x/Firmware_Load_Helpers.o
                                       0x1bba (size before relaxing)
 .debug_str     0x00000000000074e6       0x64 emodules/drv_dab/dab_module/si463x/FMHD_PSD_Handler.o
                                       0x1e18 (size before relaxing)
 .debug_str     0x000000000000754a       0xf1 emodules/drv_dab/dab_module/si463x/FMHD_Service_List_Handler.o
                                       0x1e7a (size before relaxing)
 .debug_str     0x000000000000763b      0x1ec emodules/drv_dab/dab_module/si463x/FMHD_SIS_Handler.o
                                       0x202b (size before relaxing)
 .debug_str     0x0000000000007827       0x8a emodules/drv_dab/dab_module/si463x/FMHD_Alert_Handler.o
                                       0x1cd6 (size before relaxing)
 .debug_str     0x00000000000078b1      0x334 emodules/drv_dab/dab_module/si463x/HAL.o
                                       0x14b4 (size before relaxing)
 .debug_str     0x0000000000007be5       0x84 emodules/drv_dab/dab_module/si463x/Hardware_Calibration_Helpers.o
                                       0x119c (size before relaxing)
 .debug_str     0x0000000000007c69      0xc34 emodules/drv_dab/dab_module/si463x/rds.o
                                       0x21d8 (size before relaxing)
 .debug_str     0x000000000000889d     0x247a emodules/drv_dab/dab_module/si463x/RDS_Handler.o
                                       0x4d36 (size before relaxing)
 .debug_str     0x000000000000ad17      0xe54 emodules/drv_dab/dab_module/si463x/si46xx_firmware_api.o
                                       0x3fea (size before relaxing)
 .debug_str     0x000000000000bb6b     0x5a17 emodules/drv_dab/dab_module/si463x/si468x.o
                                       0xc502 (size before relaxing)
 .debug_str     0x0000000000011582      0x112 emodules/drv_dab/dab_module/si463x/si468x_bus.o
                                       0x18db (size before relaxing)
 .debug_str     0x0000000000011694      0x32a emodules/drv_dab/dab_module/si463x/mot.o
                                       0x1001 (size before relaxing)
 .debug_str     0x00000000000119be       0x5f emodules/drv_dab/magic.o
                                       0x181c (size before relaxing)
 .debug_str     0x0000000000011a1d     0x2c44 ./elibrary/bin//libsyscall.a(syscall.o)
                                       0x3c33 (size before relaxing)
 .debug_str     0x0000000000014661      0x1aa ./elibrary/bin//libsyscall.a(syscall_fops.o)
                                       0x1068 (size before relaxing)
 .debug_str     0x000000000001480b      0x285 ./elibrary/bin//libminic.a(elibs_sprintf.o)
                                        0x5d3 (size before relaxing)
 .debug_str     0x0000000000014a90      0x676 ./elibrary/bin//libminic.a(elibs_stdio.o)
                                       0x1624 (size before relaxing)
 .debug_str     0x0000000000015106      0x17d ./elibrary/bin//libminic.a(elibs_string.o)
                                        0x6d8 (size before relaxing)

.debug_frame    0x0000000000000000     0xa780
 .debug_frame   0x0000000000000000      0x228 emodules/drv_dab/dev_dab.o
 .debug_frame   0x0000000000000228       0xf8 emodules/drv_dab/drv_dab.o
 .debug_frame   0x0000000000000320      0x160 emodules/drv_dab/dab_module/si463x/DAB_DLS_Handler.o
 .debug_frame   0x0000000000000480      0x588 emodules/drv_dab/dab_module/si463x/DAB_Service_List_Handler.o
 .debug_frame   0x0000000000000a08     0x1a00 emodules/drv_dab/dab_module/si463x/Firmware_API_Manager.o
 .debug_frame   0x0000000000002408       0x98 emodules/drv_dab/dab_module/si463x/Firmware_Load_Helpers.o
 .debug_frame   0x00000000000024a0       0x90 emodules/drv_dab/dab_module/si463x/FMHD_PSD_Handler.o
 .debug_frame   0x0000000000002530       0x80 emodules/drv_dab/dab_module/si463x/FMHD_Service_List_Handler.o
 .debug_frame   0x00000000000025b0       0xa0 emodules/drv_dab/dab_module/si463x/FMHD_SIS_Handler.o
 .debug_frame   0x0000000000002650       0x70 emodules/drv_dab/dab_module/si463x/FMHD_Alert_Handler.o
 .debug_frame   0x00000000000026c0      0x178 emodules/drv_dab/dab_module/si463x/HAL.o
 .debug_frame   0x0000000000002838       0x40 emodules/drv_dab/dab_module/si463x/Hardware_Calibration_Helpers.o
 .debug_frame   0x0000000000002878      0x2d0 emodules/drv_dab/dab_module/si463x/rds.o
 .debug_frame   0x0000000000002b48      0x168 emodules/drv_dab/dab_module/si463x/RDS_Handler.o
 .debug_frame   0x0000000000002cb0     0x12f0 emodules/drv_dab/dab_module/si463x/si46xx_firmware_api.o
 .debug_frame   0x0000000000003fa0     0x16d0 emodules/drv_dab/dab_module/si463x/si468x.o
 .debug_frame   0x0000000000005670      0x120 emodules/drv_dab/dab_module/si463x/si468x_bus.o
 .debug_frame   0x0000000000005790       0xe8 emodules/drv_dab/dab_module/si463x/mot.o
 .debug_frame   0x0000000000005878     0x3c60 ./elibrary/bin//libsyscall.a(syscall.o)
 .debug_frame   0x00000000000094d8      0x5b8 ./elibrary/bin//libsyscall.a(syscall_fops.o)
 .debug_frame   0x0000000000009a90       0xe0 ./elibrary/bin//libminic.a(elibs_sprintf.o)
 .debug_frame   0x0000000000009b70      0xa28 ./elibrary/bin//libminic.a(elibs_stdio.o)
 .debug_frame   0x000000000000a598      0x1e8 ./elibrary/bin//libminic.a(elibs_string.o)

Cross Reference Table

Symbol                                            File
AS_NUMs                                           emodules/drv_dab/dab_module/si463x/si468x.o
ATS_RDS_valid_flag                                emodules/drv_dab/dab_module/si463x/si468x.o
ATS_test_uk_freq_flag                             emodules/drv_dab/dab_module/si463x/si468x.o
Area                                              emodules/drv_dab/dab_module/si463x/si468x.o
                                                  emodules/drv_dab/dab_module/si463x/Firmware_API_Manager.o
AudioLevel                                        emodules/drv_dab/dab_module/si463x/Firmware_API_Manager.o
                                                  emodules/drv_dab/dab_module/si463x/si468x.o
AudioMute                                         emodules/drv_dab/dab_module/si463x/Firmware_API_Manager.o
                                                  emodules/drv_dab/dab_module/si463x/si468x.o
AutoSeekAbort                                     emodules/drv_dab/dab_module/si463x/si468x.o
AutoSeekCase                                      emodules/drv_dab/dab_module/si463x/si468x.o
AutoSeekEnd                                       emodules/drv_dab/dab_module/si463x/si468x.o
AutoSeekProcess                                   emodules/drv_dab/dab_module/si463x/si468x.o
AutoSeekStart                                     emodules/drv_dab/dab_module/si463x/si468x.o
AutoSeekTask                                      emodules/drv_dab/dab_module/si463x/si468x.o
AutoStoreCase                                     emodules/drv_dab/dab_module/si463x/si468x.o
Band                                              emodules/drv_dab/dab_module/si463x/si468x.o
                                                  emodules/drv_dab/dab_module/si463x/rds.o
BrowseServicesChangeSelection_DAB                 emodules/drv_dab/dab_module/si463x/Firmware_API_Manager.o
                                                  emodules/drv_dab/dab_module/si463x/si468x.o
BrowseServicesReset_DAB                           emodules/drv_dab/dab_module/si463x/Firmware_API_Manager.o
BrowseServicesStartCurrentSelection_DAB           emodules/drv_dab/dab_module/si463x/Firmware_API_Manager.o
                                                  emodules/drv_dab/dab_module/si463x/si468x.o
DABServiceListAudioPtr                            emodules/drv_dab/dab_module/si463x/Firmware_API_Manager.o
DAB_service_list_updated_for_current_freq         emodules/drv_dab/dab_module/si463x/DAB_Service_List_Handler.o
                                                  emodules/drv_dab/dab_module/si463x/si468x.o
                                                  emodules/drv_dab/dab_module/si463x/Firmware_API_Manager.o
DLS_EBU_TO_UCS2                                   emodules/drv_dab/dab_module/si463x/DAB_DLS_Handler.o
DRV_DAB_MClose                                    emodules/drv_dab/drv_dab.o
                                                  emodules/drv_dab/magic.o
DRV_DAB_MExit                                     emodules/drv_dab/drv_dab.o
                                                  emodules/drv_dab/magic.o
DRV_DAB_MInit                                     emodules/drv_dab/drv_dab.o
                                                  emodules/drv_dab/magic.o
DRV_DAB_MIoctrl                                   emodules/drv_dab/drv_dab.o
                                                  emodules/drv_dab/magic.o
DRV_DAB_MOpen                                     emodules/drv_dab/drv_dab.o
                                                  emodules/drv_dab/magic.o
DRV_DAB_MRead                                     emodules/drv_dab/drv_dab.o
                                                  emodules/drv_dab/magic.o
DRV_DAB_MWrite                                    emodules/drv_dab/drv_dab.o
                                                  emodules/drv_dab/magic.o
DeleteMultiServiceListElement                     emodules/drv_dab/dab_module/si463x/DAB_Service_List_Handler.o
DeleteServiceListElement                          emodules/drv_dab/dab_module/si463x/DAB_Service_List_Handler.o
EBU_TO_UCS2                                       emodules/drv_dab/dab_module/si463x/Firmware_API_Manager.o
EraseServiceList_DAB                              emodules/drv_dab/dab_module/si463x/Firmware_API_Manager.o
FMHDServiceListAudioPtr                           emodules/drv_dab/dab_module/si463x/Firmware_API_Manager.o
FMHDServiceListFastPtr                            emodules/drv_dab/dab_module/si463x/Firmware_API_Manager.o
FastTuneByNum                                     emodules/drv_dab/dab_module/si463x/si468x.o
FavoritesBrowseOnly_DAB                           emodules/drv_dab/dab_module/si463x/Firmware_API_Manager.o
FavoritesIsCurrentServiceAFavorite_DAB            emodules/drv_dab/dab_module/si463x/Firmware_API_Manager.o
FavoritesRemoveCurrentService_DAB                 emodules/drv_dab/dab_module/si463x/Firmware_API_Manager.o
FavoritesSetCurrentService_DAB                    emodules/drv_dab/dab_module/si463x/Firmware_API_Manager.o
Finalize                                          emodules/drv_dab/dab_module/si463x/Firmware_API_Manager.o
Fm_key_pty_Seek                                   emodules/drv_dab/dab_module/si463x/si468x.o
ForceMono_FM                                      emodules/drv_dab/dab_module/si463x/Firmware_API_Manager.o
GetCurrentBrowseServiceString_DAB                 emodules/drv_dab/dab_module/si463x/Firmware_API_Manager.o
GetCurrentDAB_ChannelNum                          emodules/drv_dab/dab_module/si463x/si468x.o
GetCurrentDAB_EID                                 emodules/drv_dab/dab_module/si463x/si468x.o
GetCurrentDAB_FreqIndex                           emodules/drv_dab/dab_module/si463x/si468x.o
GetCurrentDabInformation                          emodules/drv_dab/dab_module/si463x/si468x.o
GetCurrentEnsembleNameString                      emodules/drv_dab/dab_module/si463x/si468x.o
GetCurrentEnsembleNameString_DAB                  emodules/drv_dab/dab_module/si463x/Firmware_API_Manager.o
                                                  emodules/drv_dab/dab_module/si463x/si468x.o
GetCurrentRDS_PtyString                           emodules/drv_dab/dab_module/si463x/si468x.o
                                                  emodules/drv_dab/dab_module/si463x/Firmware_API_Manager.o
GetCurrentServiceIdentification_DAB               emodules/drv_dab/dab_module/si463x/Firmware_API_Manager.o
GetCurrentServiceString_DAB                       emodules/drv_dab/dab_module/si463x/Firmware_API_Manager.o
                                                  emodules/drv_dab/dab_module/si463x/si468x.o
GetCurrentTime                                    emodules/drv_dab/dab_module/si463x/si468x.o
                                                  emodules/drv_dab/dev_dab.o
GetCurrentTime_DAB                                emodules/drv_dab/dab_module/si463x/Firmware_API_Manager.o
                                                  emodules/drv_dab/dab_module/si463x/si468x.o
GetDabCurrentStatus                               emodules/drv_dab/dab_module/si463x/si468x.o
                                                  emodules/drv_dab/dev_dab.o
GetDabScanStatus                                  emodules/drv_dab/dab_module/si463x/si468x.o
                                                  emodules/drv_dab/dev_dab.o
GetDivisor_idx                                    emodules/drv_dab/dab_module/si463x/si468x.o
GetFmCurPreset                                    emodules/drv_dab/dab_module/si463x/si468x.o
GetFreq                                           emodules/drv_dab/dab_module/si463x/si468x.o
GetFrequencyList_DAB                              emodules/drv_dab/dab_module/si463x/Firmware_API_Manager.o
GetProperty                                       emodules/drv_dab/dab_module/si463x/Firmware_API_Manager.o
GetServiceList_para                               emodules/drv_dab/dab_module/si463x/Firmware_API_Manager.o
GetSi4634CurrrntMode                              emodules/drv_dab/dab_module/si463x/si468x.o
                                                  emodules/drv_dab/dev_dab.o
GetSi46XXStatus                                   emodules/drv_dab/dab_module/si463x/Firmware_API_Manager.o
Get_Freq_Range_By_Area                            emodules/drv_dab/dab_module/si463x/si468x.o
Get_function_info                                 emodules/drv_dab/dab_module/si463x/Firmware_API_Manager.o
                                                  emodules/drv_dab/dab_module/si463x/si468x.o
Get_part                                          emodules/drv_dab/dab_module/si463x/Firmware_API_Manager.o
Get_part_ex                                       emodules/drv_dab/dab_module/si463x/Firmware_API_Manager.o
                                                  emodules/drv_dab/dab_module/si463x/si468x.o
GetlistServiceString_DAB                          emodules/drv_dab/dab_module/si463x/Firmware_API_Manager.o
                                                  emodules/drv_dab/dab_module/si463x/si468x.o
HDAlertGetPtr                                     emodules/drv_dab/dab_module/si463x/Firmware_API_Manager.o
Init_SendMsgQueue                                 emodules/drv_dab/dab_module/si463x/si468x.o
Initialize                                        emodules/drv_dab/dab_module/si463x/Firmware_API_Manager.o
                                                  emodules/drv_dab/dab_module/si463x/si468x.o
Length                                            emodules/drv_dab/dab_module/si463x/freq_stack.o
LoadServiceList_DAB                               emodules/drv_dab/dab_module/si463x/Firmware_API_Manager.o
                                                  emodules/drv_dab/dab_module/si463x/si468x.o
ManualTune_DAB                                    emodules/drv_dab/dab_module/si463x/Firmware_API_Manager.o
                                                  emodules/drv_dab/dab_module/si463x/si468x.o
MetricsGetAMHDPtr                                 emodules/drv_dab/dab_module/si463x/Firmware_API_Manager.o
                                                  emodules/drv_dab/dab_module/si463x/si468x.o
MetricsGetAMPtr                                   emodules/drv_dab/dab_module/si463x/Firmware_API_Manager.o
                                                  emodules/drv_dab/dab_module/si463x/si468x.o
MetricsGetDABPtr                                  emodules/drv_dab/dab_module/si463x/Firmware_API_Manager.o
                                                  emodules/drv_dab/dab_module/si463x/si468x.o
                                                  emodules/drv_dab/dab_module/si463x/DAB_Service_List_Handler.o
MetricsGetFMHDPtr                                 emodules/drv_dab/dab_module/si463x/Firmware_API_Manager.o
                                                  emodules/drv_dab/dab_module/si463x/si468x.o
MetricsGetFMPtr                                   emodules/drv_dab/dab_module/si463x/Firmware_API_Manager.o
                                                  emodules/drv_dab/dab_module/si463x/si468x.o
Out_freqStack                                     emodules/drv_dab/dab_module/si463x/freq_stack.o
PSDGetAlbumPtr                                    emodules/drv_dab/dab_module/si463x/Firmware_API_Manager.o
PSDGetArtistPtr                                   emodules/drv_dab/dab_module/si463x/Firmware_API_Manager.o
PSDGetGenrePtr                                    emodules/drv_dab/dab_module/si463x/Firmware_API_Manager.o
PSDGetTitlePtr                                    emodules/drv_dab/dab_module/si463x/Firmware_API_Manager.o
PtySeekStart                                      emodules/drv_dab/dab_module/si463x/si468x.o
Push_freqStack                                    emodules/drv_dab/dab_module/si463x/freq_stack.o
RDS_Pty_List                                      emodules/drv_dab/dab_module/si463x/si468x.o
RadioAutoStore                                    emodules/drv_dab/dab_module/si463x/si468x.o
RadioTuneFlag                                     emodules/drv_dab/dab_module/si463x/si468x.o
SISGetSMPtr                                       emodules/drv_dab/dab_module/si463x/Firmware_API_Manager.o
SISGetSNLPtr                                      emodules/drv_dab/dab_module/si463x/Firmware_API_Manager.o
SISGetSNSPtr                                      emodules/drv_dab/dab_module/si463x/Firmware_API_Manager.o
SISGetSloganPtr                                   emodules/drv_dab/dab_module/si463x/Firmware_API_Manager.o
SISGetStationIDPtr                                emodules/drv_dab/dab_module/si463x/Firmware_API_Manager.o
SISGetStationLocLatPtr                            emodules/drv_dab/dab_module/si463x/Firmware_API_Manager.o
SISGetStationLocLongPtr                           emodules/drv_dab/dab_module/si463x/Firmware_API_Manager.o
SISGetUSNPtr                                      emodules/drv_dab/dab_module/si463x/Firmware_API_Manager.o
SISupdateStatus                                   emodules/drv_dab/dab_module/si463x/FMHD_SIS_Handler.o
SLStartServiceByIndex                             emodules/drv_dab/dab_module/si463x/Firmware_API_Manager.o
                                                  emodules/drv_dab/dab_module/si463x/si468x.o
SLStateCtrl                                       emodules/drv_dab/dab_module/si463x/si468x.o
SaveCurrentPlayServiceIndex_DAB                   emodules/drv_dab/dab_module/si463x/Firmware_API_Manager.o
SaveServiceList_DAB                               emodules/drv_dab/dab_module/si463x/Firmware_API_Manager.o
                                                  emodules/drv_dab/dab_module/si463x/si468x.o
SaveServiceList_para                              emodules/drv_dab/dab_module/si463x/Firmware_API_Manager.o
ScanBandCancel_DAB                                emodules/drv_dab/dab_module/si463x/Firmware_API_Manager.o
SeekServiceCount                                  emodules/drv_dab/dab_module/si463x/DAB_Service_List_Handler.o
                                                  emodules/drv_dab/dab_module/si463x/si468x.o
SeekStart                                         emodules/drv_dab/dab_module/si463x/Firmware_API_Manager.o
SeekStop                                          emodules/drv_dab/dab_module/si463x/Firmware_API_Manager.o
                                                  emodules/drv_dab/dab_module/si463x/si468x.o
SeekTuneCompleteCheck                             emodules/drv_dab/dab_module/si463x/Firmware_API_Manager.o
                                                  emodules/drv_dab/dab_module/si463x/si468x.o
                                                  emodules/drv_dab/dab_module/si463x/rds.o
SetBandLimits                                     emodules/drv_dab/dab_module/si463x/Firmware_API_Manager.o
SetFrequencyList_DAB                              emodules/drv_dab/dab_module/si463x/Firmware_API_Manager.o
SetProperty                                       emodules/drv_dab/dab_module/si463x/Firmware_API_Manager.o
SetStepSize                                       emodules/drv_dab/dab_module/si463x/Firmware_API_Manager.o
                                                  emodules/drv_dab/dab_module/si463x/si468x.o
Si4634CurrentMode                                 emodules/drv_dab/dab_module/si463x/si468x.o
                                                  emodules/drv_dab/dab_module/si463x/rds.o
                                                  emodules/drv_dab/dab_module/si463x/Firmware_API_Manager.o
SpiRead                                           emodules/drv_dab/dab_module/si463x/cmmbSpi.o
SpiTsDeInit                                       emodules/drv_dab/dab_module/si463x/cmmbSpi.o
SpiTsInit                                         emodules/drv_dab/dab_module/si463x/cmmbSpi.o
SpiTsRead                                         emodules/drv_dab/dab_module/si463x/cmmbSpi.o
SpiTsWrite                                        emodules/drv_dab/dab_module/si463x/cmmbSpi.o
SpiWrite                                          emodules/drv_dab/dab_module/si463x/cmmbSpi.o
StartFirmwareUpdate                               emodules/drv_dab/dab_module/si463x/Firmware_API_Manager.o
StartLastServiceByIndex                           emodules/drv_dab/dab_module/si463x/Firmware_API_Manager.o
StartProcessingChannel                            emodules/drv_dab/dab_module/si463x/Firmware_API_Manager.o
                                                  emodules/drv_dab/dab_module/si463x/si468x.o
StopProcessingChannel                             emodules/drv_dab/dab_module/si463x/Firmware_API_Manager.o
                                                  emodules/drv_dab/dab_module/si463x/si468x.o
TA_Read                                           emodules/drv_dab/dab_module/si463x/RDS_Handler.o
T_Af_fastflag                                     emodules/drv_dab/dab_module/si463x/si468x.o
                                                  emodules/drv_dab/dab_module/si463x/rds.o
T_CT_sw                                           emodules/drv_dab/dab_module/si463x/si468x.o
                                                  emodules/drv_dab/dab_module/si463x/RDS_Handler.o
T_Caculate_SMeter                                 emodules/drv_dab/dab_module/si463x/rds.o
                                                  emodules/drv_dab/dab_module/si463x/si468x.o
T_Eonpi                                           emodules/drv_dab/dab_module/si463x/si468x.o
                                                  emodules/drv_dab/dab_module/si463x/RDS_Handler.o
T_F_Local                                         emodules/drv_dab/dab_module/si463x/si468x.o
T_F_Valid_channl                                  emodules/drv_dab/dab_module/si463x/si468x.o
                                                  emodules/drv_dab/dab_module/si463x/RDS_Handler.o
                                                  emodules/drv_dab/dab_module/si463x/rds.o
T_F_af_ta_seek                                    emodules/drv_dab/dab_module/si463x/si468x.o
T_F_rds_on                                        emodules/drv_dab/dab_module/si463x/si468x.o
                                                  emodules/drv_dab/dab_module/si463x/rds.o
                                                  emodules/drv_dab/dab_module/si463x/Firmware_API_Manager.o
T_Init_Rds_para                                   emodules/drv_dab/dab_module/si463x/rds.o
                                                  emodules/drv_dab/dab_module/si463x/si468x.o
T_Meth_Clear                                      emodules/drv_dab/dab_module/si463x/si468x.o
T_Network_af_count                                emodules/drv_dab/dab_module/si463x/rds.o
T_Network_af_store                                emodules/drv_dab/dab_module/si463x/rds.o
                                                  emodules/drv_dab/dab_module/si463x/RDS_Handler.o
T_Network_clear                                   emodules/drv_dab/dab_module/si463x/rds.o
                                                  emodules/drv_dab/dab_module/si463x/si468x.o
T_Network_get_af                                  emodules/drv_dab/dab_module/si463x/rds.o
T_Network_get_ctx                                 emodules/drv_dab/dab_module/si463x/rds.o
                                                  emodules/drv_dab/dab_module/si463x/si468x.o
T_Network_get_frq                                 emodules/drv_dab/dab_module/si463x/rds.o
                                                  emodules/drv_dab/dab_module/si463x/si468x.o
T_Network_lookup_af                               emodules/drv_dab/dab_module/si463x/rds.o
T_Network_lookup_pi                               emodules/drv_dab/dab_module/si463x/rds.o
T_Network_netpi                                   emodules/drv_dab/dab_module/si463x/rds.o
                                                  emodules/drv_dab/dab_module/si463x/si468x.o
T_Network_pi_cmp                                  emodules/drv_dab/dab_module/si463x/rds.o
T_Network_resume                                  emodules/drv_dab/dab_module/si463x/rds.o
                                                  emodules/drv_dab/dab_module/si463x/si468x.o
T_Network_set_sequence                            emodules/drv_dab/dab_module/si463x/rds.o
                                                  emodules/drv_dab/dab_module/si463x/si468x.o
T_PI_Clear                                        emodules/drv_dab/dab_module/si463x/si468x.o
                                                  emodules/drv_dab/dab_module/si463x/rds.o
T_PTY_Clear                                       emodules/drv_dab/dab_module/si463x/rds.o
T_RDS_Reset                                       emodules/drv_dab/dab_module/si463x/rds.o
                                                  emodules/drv_dab/dab_module/si463x/si468x.o
                                                  emodules/drv_dab/dab_module/si463x/Firmware_API_Manager.o
T_RdsInt                                          emodules/drv_dab/dab_module/si463x/si468x.o
                                                  emodules/drv_dab/dab_module/si463x/RDS_Handler.o
                                                  emodules/drv_dab/dab_module/si463x/rds.o
T_RdsSync                                         emodules/drv_dab/dab_module/si463x/si468x.o
                                                  emodules/drv_dab/dab_module/si463x/RDS_Handler.o
                                                  emodules/drv_dab/dab_module/si463x/rds.o
T_Rds_QualityCheck                                emodules/drv_dab/dab_module/si463x/rds.o
                                                  emodules/drv_dab/dab_module/si463x/si468x.o
T_Reg_sw                                          emodules/drv_dab/dab_module/si463x/si468x.o
T_ReplaceFreq                                     emodules/drv_dab/dab_module/si463x/rds.o
T_ResumeAfFlag                                    emodules/drv_dab/dab_module/si463x/si468x.o
T_TPTA_Clear                                      emodules/drv_dab/dab_module/si463x/rds.o
                                                  emodules/drv_dab/dab_module/si463x/RDS_Handler.o
T_Tu_Snr                                          emodules/drv_dab/dab_module/si463x/si468x.o
                                                  emodules/drv_dab/dab_module/si463x/rds.o
                                                  emodules/drv_dab/dab_module/si463x/Firmware_API_Manager.o
T_Tu_last_divisor                                 emodules/drv_dab/dab_module/si463x/si468x.o
T_Tune_AF_Seek_end                                emodules/drv_dab/dab_module/si463x/si468x.o
T_af_1st_divisor                                  emodules/drv_dab/dab_module/si463x/si468x.o
T_af_cnt                                          emodules/drv_dab/dab_module/si463x/si468x.o
T_af_count_bk                                     emodules/drv_dab/dab_module/si463x/si468x.o
T_af_freq_is_bad                                  emodules/drv_dab/dab_module/si463x/si468x.o
T_af_inf_f                                        emodules/drv_dab/dab_module/si463x/si468x.o
                                                  emodules/drv_dab/dab_module/si463x/rds.o
T_af_last_fm_freq                                 emodules/drv_dab/dab_module/si463x/si468x.o
T_af_list_bk                                      emodules/drv_dab/dab_module/si463x/si468x.o
T_af_mask_timer                                   emodules/drv_dab/dab_module/si463x/si468x.o
                                                  emodules/drv_dab/dab_module/si463x/rds.o
T_af_pi_bk                                        emodules/drv_dab/dab_module/si463x/si468x.o
T_af_pi_now                                       emodules/drv_dab/dab_module/si463x/si468x.o
T_af_sw                                           emodules/drv_dab/dab_module/si463x/si468x.o
                                                  emodules/drv_dab/dab_module/si463x/rds.o
T_af_time                                         emodules/drv_dab/dab_module/si463x/si468x.o
                                                  emodules/drv_dab/dab_module/si463x/rds.o
T_af_time2                                        emodules/drv_dab/dab_module/si463x/si468x.o
                                                  emodules/drv_dab/dab_module/si463x/rds.o
T_af_time3                                        emodules/drv_dab/dab_module/si463x/si468x.o
                                                  emodules/drv_dab/dab_module/si463x/rds.o
T_afgo_f                                          emodules/drv_dab/dab_module/si463x/si468x.o
                                                  emodules/drv_dab/dab_module/si463x/rds.o
T_bfpi                                            emodules/drv_dab/dab_module/si463x/si468x.o
                                                  emodules/drv_dab/dab_module/si463x/RDS_Handler.o
                                                  emodules/drv_dab/dab_module/si463x/rds.o
T_bkpi                                            emodules/drv_dab/dab_module/si463x/si468x.o
                                                  emodules/drv_dab/dab_module/si463x/rds.o
T_curr_af                                         emodules/drv_dab/dab_module/si463x/si468x.o
T_curr_ctx                                        emodules/drv_dab/dab_module/si463x/si468x.o
                                                  emodules/drv_dab/dab_module/si463x/rds.o
T_divisor                                         emodules/drv_dab/dab_module/si463x/si468x.o
                                                  emodules/drv_dab/dab_module/si463x/rds.o
                                                  emodules/drv_dab/dab_module/si463x/Firmware_API_Manager.o
T_goodcnt                                         emodules/drv_dab/dab_module/si463x/si468x.o
                                                  emodules/drv_dab/dab_module/si463x/rds.o
T_last_divisor                                    emodules/drv_dab/dab_module/si463x/si468x.o
T_ltpi                                            emodules/drv_dab/dab_module/si463x/si468x.o
                                                  emodules/drv_dab/dab_module/si463x/rds.o
T_network_ptr                                     emodules/drv_dab/dab_module/si463x/si468x.o
                                                  emodules/drv_dab/dab_module/si463x/rds.o
T_networks                                        emodules/drv_dab/dab_module/si463x/si468x.o
                                                  emodules/drv_dab/dab_module/si463x/rds.o
T_pi_bak                                          emodules/drv_dab/dab_module/si463x/si468x.o
                                                  emodules/drv_dab/dab_module/si463x/rds.o
T_piok_f                                          emodules/drv_dab/dab_module/si463x/si468x.o
                                                  emodules/drv_dab/dab_module/si463x/RDS_Handler.o
                                                  emodules/drv_dab/dab_module/si463x/rds.o
T_preset_pi                                       emodules/drv_dab/dab_module/si463x/si468x.o
                                                  emodules/drv_dab/dab_module/si463x/rds.o
T_psallok_f                                       emodules/drv_dab/dab_module/si463x/si468x.o
                                                  emodules/drv_dab/dab_module/si463x/rds.o
T_pty_mode                                        emodules/drv_dab/dab_module/si463x/si468x.o
                                                  emodules/drv_dab/dab_module/si463x/rds.o
T_pty_seek_last_fm_freq                           emodules/drv_dab/dab_module/si463x/si468x.o
T_pty_seek_type                                   emodules/drv_dab/dab_module/si463x/si468x.o
T_pty_seek_type_now                               emodules/drv_dab/dab_module/si463x/si468x.o
T_ptyok_f                                         emodules/drv_dab/dab_module/si463x/si468x.o
                                                  emodules/drv_dab/dab_module/si463x/RDS_Handler.o
                                                  emodules/drv_dab/dab_module/si463x/rds.o
T_smeter                                          emodules/drv_dab/dab_module/si463x/si468x.o
                                                  emodules/drv_dab/dab_module/si463x/RDS_Handler.o
                                                  emodules/drv_dab/dab_module/si463x/rds.o
                                                  emodules/drv_dab/dab_module/si463x/Firmware_API_Manager.o
T_smeter_bak                                      emodules/drv_dab/dab_module/si463x/si468x.o
T_ta_active                                       emodules/drv_dab/dab_module/si463x/si468x.o
                                                  emodules/drv_dab/dab_module/si463x/RDS_Handler.o
                                                  emodules/drv_dab/dab_module/si463x/rds.o
T_ta_cnt                                          emodules/drv_dab/dab_module/si463x/si468x.o
T_ta_f                                            emodules/drv_dab/dab_module/si463x/si468x.o
                                                  emodules/drv_dab/dab_module/si463x/RDS_Handler.o
                                                  emodules/drv_dab/dab_module/si463x/rds.o
T_ta_mode                                         emodules/drv_dab/dab_module/si463x/si468x.o
                                                  emodules/drv_dab/dab_module/si463x/RDS_Handler.o
                                                  emodules/drv_dab/dab_module/si463x/rds.o
T_ta_on                                           emodules/drv_dab/dab_module/si463x/si468x.o
                                                  emodules/drv_dab/dab_module/si463x/RDS_Handler.o
                                                  emodules/drv_dab/dab_module/si463x/rds.o
T_ta_sw                                           emodules/drv_dab/dab_module/si463x/si468x.o
                                                  emodules/drv_dab/dab_module/si463x/rds.o
T_ta_timer                                        emodules/drv_dab/dab_module/si463x/si468x.o
                                                  emodules/drv_dab/dab_module/si463x/RDS_Handler.o
T_tanow_f                                         emodules/drv_dab/dab_module/si463x/si468x.o
                                                  emodules/drv_dab/dab_module/si463x/RDS_Handler.o
                                                  emodules/drv_dab/dab_module/si463x/rds.o
T_tp_f                                            emodules/drv_dab/dab_module/si463x/si468x.o
                                                  emodules/drv_dab/dab_module/si463x/RDS_Handler.o
                                                  emodules/drv_dab/dab_module/si463x/rds.o
T_tpnow_f                                         emodules/drv_dab/dab_module/si463x/si468x.o
                                                  emodules/drv_dab/dab_module/si463x/RDS_Handler.o
                                                  emodules/drv_dab/dab_module/si463x/rds.o
Test_DeterminePeakANTCAP_DAB                      emodules/drv_dab/dab_module/si463x/Firmware_API_Manager.o
Test_GetBER_DAB                                   emodules/drv_dab/dab_module/si463x/Firmware_API_Manager.o
                                                  emodules/drv_dab/dab_module/si463x/si468x.o
Test_GetBER_FMHD                                  emodules/drv_dab/dab_module/si463x/Firmware_API_Manager.o
Test_GetFreeRunningBER_FMHD                       emodules/drv_dab/dab_module/si463x/Firmware_API_Manager.o
Test_StartFreeRunningBER_FMHD                     emodules/drv_dab/dab_module/si463x/Firmware_API_Manager.o
Tune                                              emodules/drv_dab/dab_module/si463x/Firmware_API_Manager.o
                                                  emodules/drv_dab/dab_module/si463x/si468x.o
TuneByNum                                         emodules/drv_dab/dab_module/si463x/si468x.o
TuneStep                                          emodules/drv_dab/dab_module/si463x/Firmware_API_Manager.o
TunerProcessTimer                                 emodules/drv_dab/dab_module/si463x/si468x.o
Tuner_Get_Reg                                     emodules/drv_dab/dab_module/si463x/si468x.o
Tuner_Set_Reg                                     emodules/drv_dab/dab_module/si463x/si468x.o
UCS2_To_EBU                                       emodules/drv_dab/dab_module/si463x/Firmware_API_Manager.o
UTF8_To_EBU                                       emodules/drv_dab/dab_module/si463x/Firmware_API_Manager.o
Uninit_SendMsgQueue                               emodules/drv_dab/dab_module/si463x/si468x.o
UpdateDataServiceData                             emodules/drv_dab/dab_module/si463x/Firmware_API_Manager.o
                                                  emodules/drv_dab/dab_module/si463x/si468x.o
UpdateMetrics                                     emodules/drv_dab/dab_module/si463x/Firmware_API_Manager.o
                                                  emodules/drv_dab/dab_module/si463x/si468x.o
                                                  emodules/drv_dab/dab_module/si463x/rds.o
UpdateRadioDNS_DAB                                emodules/drv_dab/dab_module/si463x/Firmware_API_Manager.o
UpdateRadioDNS_FMRDS                              emodules/drv_dab/dab_module/si463x/Firmware_API_Manager.o
UpdateServiceList                                 emodules/drv_dab/dab_module/si463x/Firmware_API_Manager.o
                                                  emodules/drv_dab/dab_module/si463x/si468x.o
_AM_confirm_seek_complete                         emodules/drv_dab/dab_module/si463x/Firmware_API_Manager.o
_AM_seek_continue                                 emodules/drv_dab/dab_module/si463x/Firmware_API_Manager.o
                                                  emodules/drv_dab/dab_module/si463x/si468x.o
_ANTCAP_DAB                                       emodules/drv_dab/dab_module/si463x/Firmware_API_Manager.o
_DAB_auto_seek_key_type                           emodules/drv_dab/dab_module/si463x/Firmware_API_Manager.o
                                                  emodules/drv_dab/dab_module/si463x/si468x.o
                                                  emodules/drv_dab/dab_module/si463x/DAB_Service_List_Handler.o
_DAB_confirm_service_list_complete                emodules/drv_dab/dab_module/si463x/Firmware_API_Manager.o
_DAB_confirm_tune_complete                        emodules/drv_dab/dab_module/si463x/Firmware_API_Manager.o
_DAB_current_browse_service_index                 emodules/drv_dab/dab_module/si463x/Firmware_API_Manager.o
                                                  emodules/drv_dab/dab_module/si463x/si468x.o
_DAB_current_manual_freq_index                    emodules/drv_dab/dab_module/si463x/Firmware_API_Manager.o
                                                  emodules/drv_dab/dab_module/si463x/si468x.o
_DAB_last_manual_browse_service_index             emodules/drv_dab/dab_module/si463x/Firmware_API_Manager.o
                                                  emodules/drv_dab/dab_module/si463x/si468x.o
_DAB_last_manual_freq_index                       emodules/drv_dab/dab_module/si463x/Firmware_API_Manager.o
                                                  emodules/drv_dab/dab_module/si463x/si468x.o
_DAB_pty_seek_key_type                            emodules/drv_dab/dab_module/si463x/Firmware_API_Manager.o
                                                  emodules/drv_dab/dab_module/si463x/si468x.o
                                                  emodules/drv_dab/dab_module/si463x/DAB_Service_List_Handler.o
_DAB_scan_continue                                emodules/drv_dab/dab_module/si463x/Firmware_API_Manager.o
                                                  emodules/drv_dab/dab_module/si463x/si468x.o
_DAB_service_lost                                 emodules/drv_dab/dab_module/si463x/Firmware_API_Manager.o
                                                  emodules/drv_dab/dab_module/si463x/si468x.o
_DAB_start_service_from_list                      emodules/drv_dab/dab_module/si463x/Firmware_API_Manager.o
_DAB_start_service_worker                         emodules/drv_dab/dab_module/si463x/Firmware_API_Manager.o
                                                  emodules/drv_dab/dab_module/si463x/si468x.o
_DAB_start_service_worker_ex                      emodules/drv_dab/dab_module/si463x/Firmware_API_Manager.o
_DAB_tune                                         emodules/drv_dab/dab_module/si463x/Firmware_API_Manager.o
                                                  emodules/drv_dab/dab_module/si463x/si468x.o
_DAB_tune_full_process                            emodules/drv_dab/dab_module/si463x/Firmware_API_Manager.o
_DAB_update_metrics_current_service_in_list       emodules/drv_dab/dab_module/si463x/Firmware_API_Manager.o
                                                  emodules/drv_dab/dab_module/si463x/si468x.o
_FMHD_confirm_seek_complete                       emodules/drv_dab/dab_module/si463x/Firmware_API_Manager.o
_FM_AM_AUTOSEEK_KEY_                              emodules/drv_dab/dab_module/si463x/si468x.o
_FM_AM_AUTOSTORE_KEY_                             emodules/drv_dab/dab_module/si463x/si468x.o
_FM_AM_MANUALSEEK_KEY_                            emodules/drv_dab/dab_module/si463x/si468x.o
_FM_confirm_seek_complete                         emodules/drv_dab/dab_module/si463x/Firmware_API_Manager.o
_FM_seek_continue                                 emodules/drv_dab/dab_module/si463x/Firmware_API_Manager.o
                                                  emodules/drv_dab/dab_module/si463x/si468x.o
__libc_init_array                                 ./elibrary/bin//libsyscall.a(syscall_fops.o)
__malloc_lock                                     ./elibrary/bin//libsyscall.a(syscall_fops.o)
__malloc_unlock                                   ./elibrary/bin//libsyscall.a(syscall_fops.o)
__pset_operate__                                  emodules/drv_dab/dab_module/si463x/si468x.o
__tryTuneToFreq__                                 emodules/drv_dab/dab_module/si463x/si468x.o
_add_data_to_buffer                               emodules/drv_dab/dab_module/si463x/DAB_Service_List_Handler.o
_adjust_NVMSPI_rate                               emodules/drv_dab/dab_module/si463x/Firmware_API_Manager.o
_calloc_r                                         ./elibrary/bin//libsyscall.a(syscall_fops.o)
_clear_all_fig021024_help_linking_info            emodules/drv_dab/dab_module/si463x/Firmware_API_Manager.o
                                                  emodules/drv_dab/dab_module/si463x/si468x.o
_close_r                                          ./elibrary/bin//libsyscall.a(syscall_fops.o)
                                                  /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/../../../../riscv64-unknown-elf/lib/rv64imafdcxtheadc/lp64d/libc.a(lib_a-sysclose.o)
_convert_ASCII_upper                              emodules/drv_dab/dab_module/si463x/DAB_Service_List_Handler.o
_currentAudioListVersion                          emodules/drv_dab/dab_module/si463x/FMHD_Service_List_Handler.o
_currentDataListVersion                           emodules/drv_dab/dab_module/si463x/FMHD_Service_List_Handler.o
_currentListVersion                               emodules/drv_dab/dab_module/si463x/DAB_Service_List_Handler.o
                                                  emodules/drv_dab/dab_module/si463x/Firmware_API_Manager.o
_current_am_metrics                               emodules/drv_dab/dab_module/si463x/Firmware_API_Manager.o
_current_amhd_metrics                             emodules/drv_dab/dab_module/si463x/Firmware_API_Manager.o
_current_audio_component_id                       emodules/drv_dab/dab_module/si463x/Firmware_API_Manager.o
                                                  emodules/drv_dab/dab_module/si463x/si468x.o
_current_audio_global_id                          emodules/drv_dab/dab_module/si463x/Firmware_API_Manager.o
                                                  emodules/drv_dab/dab_module/si463x/si468x.o
_current_audio_service_id                         emodules/drv_dab/dab_module/si463x/Firmware_API_Manager.o
                                                  emodules/drv_dab/dab_module/si463x/si468x.o
_current_audio_services_stop                      emodules/drv_dab/dab_module/si463x/Firmware_API_Manager.o
                                                  emodules/drv_dab/dab_module/si463x/si468x.o
_current_band_bottom                              emodules/drv_dab/dab_module/si463x/Firmware_API_Manager.o
                                                  emodules/drv_dab/dab_module/si463x/si468x.o
_current_band_manu_step                           emodules/drv_dab/dab_module/si463x/Firmware_API_Manager.o
                                                  emodules/drv_dab/dab_module/si463x/si468x.o
_current_band_step                                emodules/drv_dab/dab_module/si463x/Firmware_API_Manager.o
                                                  emodules/drv_dab/dab_module/si463x/si468x.o
                                                  emodules/drv_dab/dab_module/si463x/rds.o
_current_band_top                                 emodules/drv_dab/dab_module/si463x/Firmware_API_Manager.o
                                                  emodules/drv_dab/dab_module/si463x/si468x.o
_current_dab_metrics                              emodules/drv_dab/dab_module/si463x/Firmware_API_Manager.o
                                                  emodules/drv_dab/dab_module/si463x/si468x.o
_current_fm_blend_rssi_limits                     emodules/drv_dab/dab_module/si463x/Firmware_API_Manager.o
_current_fm_metrics                               emodules/drv_dab/dab_module/si463x/Firmware_API_Manager.o
                                                  emodules/drv_dab/dab_module/si463x/si468x.o
                                                  emodules/drv_dab/dab_module/si463x/RDS_Handler.o
                                                  emodules/drv_dab/dab_module/si463x/rds.o
_current_fmhd_metrics                             emodules/drv_dab/dab_module/si463x/Firmware_API_Manager.o
_current_hd_audio_service_as_bitmask              emodules/drv_dab/dab_module/si463x/Firmware_API_Manager.o
_current_image_type                               emodules/drv_dab/dab_module/si463x/Firmware_API_Manager.o
                                                  emodules/drv_dab/dab_module/si463x/si468x.o
_current_service_links                            emodules/drv_dab/dab_module/si463x/Firmware_API_Manager.o
_current_service_list_buffer_end_as_offset        emodules/drv_dab/dab_module/si463x/DAB_Service_List_Handler.o
                                                  emodules/drv_dab/dab_module/si463x/Firmware_API_Manager.o
_dab_browse_step_up_down_worker                   emodules/drv_dab/dab_module/si463x/Firmware_API_Manager.o
_dab_favorites_only_browse_enable                 emodules/drv_dab/dab_module/si463x/Firmware_API_Manager.o
_dab_wait_stc_complete                            emodules/drv_dab/dab_module/si463x/Firmware_API_Manager.o
_dabiic_exit                                      emodules/drv_dab/dab_module/si463x/si468x_bus.o
                                                  emodules/drv_dab/dab_module/si463x/HAL.o
_dabiic_init                                      emodules/drv_dab/dab_module/si463x/si468x_bus.o
_deactive_rds_linkage_set_by_sid                  emodules/drv_dab/dab_module/si463x/Firmware_API_Manager.o
                                                  emodules/drv_dab/dab_module/si463x/si468x.o
_deleteNode                                       emodules/drv_dab/dab_module/si463x/Firmware_API_Manager.o
_empty_metrics_am                                 emodules/drv_dab/dab_module/si463x/Firmware_API_Manager.o
_empty_metrics_amhd                               emodules/drv_dab/dab_module/si463x/Firmware_API_Manager.o
_empty_metrics_dab                                emodules/drv_dab/dab_module/si463x/Firmware_API_Manager.o
_empty_metrics_fm                                 emodules/drv_dab/dab_module/si463x/Firmware_API_Manager.o
_empty_metrics_fmhd                               emodules/drv_dab/dab_module/si463x/Firmware_API_Manager.o
_empty_service_list                               emodules/drv_dab/dab_module/si463x/Firmware_API_Manager.o
_empty_status                                     emodules/drv_dab/dab_module/si463x/Firmware_API_Manager.o
_error_in_list                                    emodules/drv_dab/dab_module/si463x/DAB_Service_List_Handler.o
_flash_load                                       emodules/drv_dab/dab_module/si463x/Firmware_API_Manager.o
_free_r                                           ./elibrary/bin//libsyscall.a(syscall_fops.o)
_freqInfo                                         emodules/drv_dab/dab_module/si463x/Firmware_API_Manager.o
_fstat_r                                          ./elibrary/bin//libsyscall.a(syscall_fops.o)
_get_property                                     emodules/drv_dab/dab_module/si463x/Firmware_API_Manager.o
_gettimeofday_r                                   ./elibrary/bin//libsyscall.a(syscall_fops.o)
_global_impure_ptr                                /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/../../../../riscv64-unknown-elf/lib/rv64imafdcxtheadc/lp64d/libc.a(lib_a-impure.o)
_host_load                                        emodules/drv_dab/dab_module/si463x/Firmware_API_Manager.o
_impure_ptr                                       /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/../../../../riscv64-unknown-elf/lib/rv64imafdcxtheadc/lp64d/libc.a(lib_a-impure.o)
                                                  /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/../../../../riscv64-unknown-elf/lib/rv64imafdcxtheadc/lp64d/libc.a(lib_a-sysopen.o)
                                                  /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/../../../../riscv64-unknown-elf/lib/rv64imafdcxtheadc/lp64d/libc.a(lib_a-sysclose.o)
_insertion_sequence_arrange                       emodules/drv_dab/dab_module/si463x/DAB_Service_List_Handler.o
_insertion_sort_alphabetical_list                 emodules/drv_dab/dab_module/si463x/DAB_Service_List_Handler.o
_insertion_sort_alphabetical_single_element       emodules/drv_dab/dab_module/si463x/DAB_Service_List_Handler.o
_isatty                                           ./elibrary/bin//libsyscall.a(syscall_fops.o)
_isatty_r                                         ./elibrary/bin//libsyscall.a(syscall_fops.o)
_last_frequency_index                             emodules/drv_dab/dab_module/si463x/Firmware_API_Manager.o
_load_bootloader_patch__flash_load_mini           emodules/drv_dab/dab_module/si463x/Firmware_API_Manager.o
_load_firmware__flash_load_mini                   emodules/drv_dab/dab_module/si463x/Firmware_API_Manager.o
_lseek_r                                          ./elibrary/bin//libsyscall.a(syscall_fops.o)
_malloc_r                                         ./elibrary/bin//libsyscall.a(syscall_fops.o)
_mkdir_r                                          ./elibrary/bin//libsyscall.a(syscall_fops.o)
_open_r                                           ./elibrary/bin//libsyscall.a(syscall_fops.o)
                                                  /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/../../../../riscv64-unknown-elf/lib/rv64imafdcxtheadc/lp64d/libc.a(lib_a-sysopen.o)
_rds_freq_info                                    emodules/drv_dab/dab_module/si463x/Firmware_API_Manager.o
_read_r                                           ./elibrary/bin//libsyscall.a(syscall_fops.o)
_realloc_r                                        ./elibrary/bin//libsyscall.a(syscall_fops.o)
_recover_16bit_from_little_endian_buffer          emodules/drv_dab/dab_module/si463x/si46xx_firmware_api.o
                                                  emodules/drv_dab/dab_module/si463x/FMHD_Alert_Handler.o
                                                  emodules/drv_dab/dab_module/si463x/FMHD_Service_List_Handler.o
                                                  emodules/drv_dab/dab_module/si463x/DAB_Service_List_Handler.o
_recover_32bit_from_little_endian_buffer          emodules/drv_dab/dab_module/si463x/si46xx_firmware_api.o
                                                  emodules/drv_dab/dab_module/si463x/DAB_Service_List_Handler.o
_rename_r                                         ./elibrary/bin//libsyscall.a(syscall_fops.o)
_repack_stored_dab_list                           emodules/drv_dab/dab_module/si463x/DAB_Service_List_Handler.o
_return_service_index_from_service_name           emodules/drv_dab/dab_module/si463x/Firmware_API_Manager.o
_return_service_string_info_from_service_list     emodules/drv_dab/dab_module/si463x/Firmware_API_Manager.o
_service_following_enabled                        emodules/drv_dab/dab_module/si463x/Firmware_API_Manager.o
                                                  emodules/drv_dab/dab_module/si463x/si468x.o
_service_linking_add_linking_info_to_list_element emodules/drv_dab/dab_module/si463x/Firmware_API_Manager.o
_service_linking_clear_service_links              emodules/drv_dab/dab_module/si463x/Firmware_API_Manager.o
                                                  emodules/drv_dab/dab_module/si463x/si468x.o
_service_linking_update_freq_info                 emodules/drv_dab/dab_module/si463x/Firmware_API_Manager.o
_service_list                                     emodules/drv_dab/dab_module/si463x/Firmware_API_Manager.o
_set_property                                     emodules/drv_dab/dab_module/si463x/Firmware_API_Manager.o
_set_property_field                               emodules/drv_dab/dab_module/si463x/Firmware_API_Manager.o
_si46xx_configure_properties                      emodules/drv_dab/dab_module/si463x/Firmware_API_Manager.o
_si46xx_initialize                                emodules/drv_dab/dab_module/si463x/Firmware_API_Manager.o
_si46xx_powerdown                                 emodules/drv_dab/dab_module/si463x/Firmware_API_Manager.o
_si46xx_status                                    emodules/drv_dab/dab_module/si463x/Firmware_API_Manager.o
                                                  emodules/drv_dab/dab_module/si463x/si468x.o
_stat_r                                           ./elibrary/bin//libsyscall.a(syscall_fops.o)
_step_to_next_service_hd                          emodules/drv_dab/dab_module/si463x/Firmware_API_Manager.o
_system                                           ./elibrary/bin//libsyscall.a(syscall_fops.o)
_unlink_r                                         ./elibrary/bin//libsyscall.a(syscall_fops.o)
_update_DAB_DAB_RDS_service_linking               emodules/drv_dab/dab_module/si463x/Firmware_API_Manager.o
_update_DAB_DAB_service_linking_worker            emodules/drv_dab/dab_module/si463x/Firmware_API_Manager.o
_update_DAB_service_following_worker              emodules/drv_dab/dab_module/si463x/Firmware_API_Manager.o
_update_DAB_service_linkage_set                   emodules/drv_dab/dab_module/si463x/Firmware_API_Manager.o
_update_DAB_service_list_worker                   emodules/drv_dab/dab_module/si463x/Firmware_API_Manager.o
_update_HD_service_list_worker                    emodules/drv_dab/dab_module/si463x/Firmware_API_Manager.o
_update_SIS_worker                                emodules/drv_dab/dab_module/si463x/Firmware_API_Manager.o
_update_current_service_list_index_fmhd           emodules/drv_dab/dab_module/si463x/Firmware_API_Manager.o
_update_generic_dls_string                        emodules/drv_dab/dab_module/si463x/DAB_DLS_Handler.o
_update_generic_psd                               emodules/drv_dab/dab_module/si463x/FMHD_PSD_Handler.o
_update_status                                    emodules/drv_dab/dab_module/si463x/Firmware_API_Manager.o
                                                  emodules/drv_dab/dab_module/si463x/si468x.o
_write_r                                          ./elibrary/bin//libsyscall.a(syscall_fops.o)
acamhd_radio_2_0_11                               emodules/drv_dab/dab_module/si463x/Firmware_Load_Helpers.o
access                                            ./elibrary/bin//libsyscall.a(syscall_fops.o)
acdab_radio_3_2_12                                emodules/drv_dab/dab_module/si463x/Firmware_Load_Helpers.o
acfmhd_radio_5_0_4                                emodules/drv_dab/dab_module/si463x/Firmware_Load_Helpers.o
afCount                                           emodules/drv_dab/dab_module/si463x/RDS_Handler.o
                                                  emodules/drv_dab/dab_module/si463x/rds.o
afList                                            emodules/drv_dab/dab_module/si463x/RDS_Handler.o
af_para                                           emodules/drv_dab/dab_module/si463x/si468x.o
albumDisplay                                      emodules/drv_dab/dab_module/si463x/FMHD_PSD_Handler.o
alertDisplay                                      emodules/drv_dab/dab_module/si463x/FMHD_Alert_Handler.o
alert_update_ready_item                           emodules/drv_dab/dab_module/si463x/si468x.o
am_freq                                           emodules/drv_dab/dab_module/si463x/si468x.o
am_freq_step_size                                 emodules/drv_dab/dab_module/si463x/si468x.o
am_initialize                                     emodules/drv_dab/dab_module/si463x/si468x.o
am_pset_table                                     emodules/drv_dab/dab_module/si463x/si468x.o
am_rsq_status__command                            emodules/drv_dab/dab_module/si463x/si46xx_firmware_api.o
                                                  emodules/drv_dab/dab_module/si463x/Firmware_API_Manager.o
am_rsq_status__reply                              emodules/drv_dab/dab_module/si463x/si46xx_firmware_api.o
                                                  emodules/drv_dab/dab_module/si463x/Firmware_API_Manager.o
am_status                                         emodules/drv_dab/dab_module/si463x/si468x.o
amhd_freq                                         emodules/drv_dab/dab_module/si463x/si468x.o
amhd_freq_step_size                               emodules/drv_dab/dab_module/si463x/si468x.o
artistDisplay                                     emodules/drv_dab/dab_module/si463x/FMHD_PSD_Handler.o
ats_encode_test_flag                              emodules/drv_dab/dab_module/si463x/si468x.o
ats_key                                           emodules/drv_dab/dab_module/si463x/si468x.o
ats_key_flag                                      emodules/drv_dab/dab_module/si463x/si468x.o
ats_keypad_test_flag                              emodules/drv_dab/dab_module/si463x/si468x.o
ats_mode_switch_flag                              emodules/drv_dab/dab_module/si463x/si468x.o
ats_test_enable_flag                              emodules/drv_dab/dab_module/si463x/si468x.o
audioDisable                                      emodules/drv_dab/dab_module/si463x/HAL.o
audioEnable                                       emodules/drv_dab/dab_module/si463x/HAL.o
bAbortAutoSeek                                    emodules/drv_dab/dab_module/si463x/si468x.o
bAbortAutoStore                                   emodules/drv_dab/dab_module/si463x/si468x.o
bFastRecovery                                     emodules/drv_dab/dab_module/si463x/si468x.o
bTuneSeek                                         emodules/drv_dab/dab_module/si463x/si468x.o
backtrace                                         ./elibrary/bin//libsyscall.a(syscall.o)
band_size                                         emodules/drv_dab/dab_module/si463x/si468x.o
ber_running_flag                                  emodules/drv_dab/dab_module/si463x/si468x.o
boot__command                                     emodules/drv_dab/dab_module/si463x/si46xx_firmware_api.o
                                                  emodules/drv_dab/dab_module/si463x/Firmware_API_Manager.o
bt_para                                           emodules/drv_dab/dab_module/si463x/si468x.o
calibrationGetFrontEndArgs                        emodules/drv_dab/dab_module/si463x/Hardware_Calibration_Helpers.o
                                                  emodules/drv_dab/dab_module/si463x/Firmware_API_Manager.o
calibrationGetPowerUpArgs                         emodules/drv_dab/dab_module/si463x/Hardware_Calibration_Helpers.o
                                                  emodules/drv_dab/dab_module/si463x/Firmware_API_Manager.o
charset_convert                                   emodules/drv_dab/dab_module/si463x/DAB_DLS_Handler.o
                                                  emodules/drv_dab/dab_module/si463x/Firmware_API_Manager.o
check_if_have_linkage_set_info                    emodules/drv_dab/dab_module/si463x/Firmware_API_Manager.o
clear_dab_announcement                            emodules/drv_dab/dab_module/si463x/Firmware_API_Manager.o
                                                  emodules/drv_dab/dab_module/si463x/si468x.o
clear_uiview_mot_sls_image                        emodules/drv_dab/dab_module/si463x/mot.o
                                                  emodules/drv_dab/dab_module/si463x/si468x.o
close                                             /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/../../../../riscv64-unknown-elf/lib/rv64imafdcxtheadc/lp64d/libc.a(lib_a-sysclose.o)
                                                  emodules/drv_dab/dab_module/si463x/si468x_bus.o
                                                  emodules/drv_dab/dab_module/si463x/si468x.o
                                                  emodules/drv_dab/dab_module/si463x/RDS_Handler.o
                                                  emodules/drv_dab/dab_module/si463x/HAL.o
                                                  emodules/drv_dab/drv_dab.o
closedir                                          ./elibrary/bin//libsyscall.a(syscall_fops.o)
cmd_arg                                           emodules/drv_dab/dab_module/si463x/si46xx_firmware_api.o
cmd_arg_u16                                       emodules/drv_dab/dab_module/si463x/si46xx_firmware_api.o
cmd_arg_u32                                       emodules/drv_dab/dab_module/si463x/si46xx_firmware_api.o
curBandIndex                                      emodules/drv_dab/dab_module/si463x/si468x.o
cur_dab_band                                      emodules/drv_dab/dab_module/si463x/si468x.o
currentMessageID                                  emodules/drv_dab/dab_module/si463x/FMHD_Alert_Handler.o
currentMode                                       emodules/drv_dab/dab_module/si463x/Firmware_Load_Helpers.o
currentTime                                       emodules/drv_dab/dab_module/si463x/RDS_Handler.o
current_mode                                      emodules/drv_dab/dab_module/si463x/si46xx_firmware_api.o
dab_acf_status__command                           emodules/drv_dab/dab_module/si463x/si46xx_firmware_api.o
dab_acf_status__reply                             emodules/drv_dab/dab_module/si463x/si46xx_firmware_api.o
dab_all_para                                      emodules/drv_dab/dab_module/si463x/si468x.o
dab_announcement                                  emodules/drv_dab/dab_module/si463x/Firmware_API_Manager.o
                                                  emodules/drv_dab/dab_module/si463x/si468x.o
dab_auto_seek_1_status                            emodules/drv_dab/dab_module/si463x/si468x.o
dab_break_rds_search_pi_link                      emodules/drv_dab/dab_module/si463x/si468x.o
dab_check_curr_service_soft_by_match_sid_inlist   emodules/drv_dab/dab_module/si463x/Firmware_API_Manager.o
                                                  emodules/drv_dab/dab_module/si463x/si468x.o
dab_check_reset_dab_request                       emodules/drv_dab/dab_module/si463x/si468x.o
dab_dab_hard_links_fig021024helper                emodules/drv_dab/dab_module/si463x/Firmware_API_Manager.o
dab_dab_soft_links_fig021024helper                emodules/drv_dab/dab_module/si463x/Firmware_API_Manager.o
dab_digrad_status__command                        emodules/drv_dab/dab_module/si463x/si46xx_firmware_api.o
                                                  emodules/drv_dab/dab_module/si463x/si468x.o
                                                  emodules/drv_dab/dab_module/si463x/Firmware_API_Manager.o
                                                  emodules/drv_dab/dab_module/si463x/DAB_Service_List_Handler.o
dab_digrad_status__reply                          emodules/drv_dab/dab_module/si463x/si46xx_firmware_api.o
                                                  emodules/drv_dab/dab_module/si463x/si468x.o
                                                  emodules/drv_dab/dab_module/si463x/Firmware_API_Manager.o
                                                  emodules/drv_dab/dab_module/si463x/DAB_Service_List_Handler.o
dab_dolinking_hard_dab_A0A1A2                     emodules/drv_dab/dab_module/si463x/Firmware_API_Manager.o
                                                  emodules/drv_dab/dab_module/si463x/si468x.o
dab_dolinking_hard_dab_fig021024_helper           emodules/drv_dab/dab_module/si463x/Firmware_API_Manager.o
                                                  emodules/drv_dab/dab_module/si463x/si468x.o
dab_dolinking_soft_dab_fig021024_helper           emodules/drv_dab/dab_module/si463x/Firmware_API_Manager.o
                                                  emodules/drv_dab/dab_module/si463x/si468x.o
dab_drv                                           emodules/drv_dab/drv_dab.o
dab_exit                                          emodules/drv_dab/dab_module/si463x/si468x.o
dab_exit_TI                                       emodules/drv_dab/dab_module/si463x/si468x.o
                                                  emodules/drv_dab/dev_dab.o
dab_fiic                                          emodules/drv_dab/dab_module/si463x/si468x_bus.o
dab_find_curr_service_hard_by_match_sid_inlist    emodules/drv_dab/dab_module/si463x/Firmware_API_Manager.o
                                                  emodules/drv_dab/dab_module/si463x/si468x.o
dab_find_curr_service_hardlink_by_tune_onlyone_freq emodules/drv_dab/dab_module/si463x/Firmware_API_Manager.o
                                                  emodules/drv_dab/dab_module/si463x/si468x.o
dab_find_curr_service_soft_by_match_sid_inlist    emodules/drv_dab/dab_module/si463x/Firmware_API_Manager.o
                                                  emodules/drv_dab/dab_module/si463x/si468x.o
dab_fm_rds_link_send_info_flag                    emodules/drv_dab/dab_module/si463x/si468x.o
dab_get_announcement_info__command                emodules/drv_dab/dab_module/si463x/si46xx_firmware_api.o
                                                  emodules/drv_dab/dab_module/si463x/Firmware_API_Manager.o
dab_get_announcement_info__reply                  emodules/drv_dab/dab_module/si463x/si46xx_firmware_api.o
                                                  emodules/drv_dab/dab_module/si463x/Firmware_API_Manager.o
dab_get_announcement_support                      emodules/drv_dab/dab_module/si463x/Firmware_API_Manager.o
                                                  emodules/drv_dab/dab_module/si463x/si468x.o
dab_get_announcement_support_info__command        emodules/drv_dab/dab_module/si463x/si46xx_firmware_api.o
                                                  emodules/drv_dab/dab_module/si463x/Firmware_API_Manager.o
dab_get_announcement_support_info__reply          emodules/drv_dab/dab_module/si463x/si46xx_firmware_api.o
                                                  emodules/drv_dab/dab_module/si463x/Firmware_API_Manager.o
dab_get_announcement_switching                    emodules/drv_dab/dab_module/si463x/Firmware_API_Manager.o
                                                  emodules/drv_dab/dab_module/si463x/si468x.o
dab_get_audio_info__command                       emodules/drv_dab/dab_module/si463x/si46xx_firmware_api.o
                                                  emodules/drv_dab/dab_module/si463x/Firmware_API_Manager.o
dab_get_audio_info__reply                         emodules/drv_dab/dab_module/si463x/si46xx_firmware_api.o
                                                  emodules/drv_dab/dab_module/si463x/Firmware_API_Manager.o
dab_get_component_info__command                   emodules/drv_dab/dab_module/si463x/si46xx_firmware_api.o
                                                  emodules/drv_dab/dab_module/si463x/Firmware_API_Manager.o
                                                  emodules/drv_dab/dab_module/si463x/DAB_Service_List_Handler.o
dab_get_component_info__reply                     emodules/drv_dab/dab_module/si463x/si46xx_firmware_api.o
                                                  emodules/drv_dab/dab_module/si463x/Firmware_API_Manager.o
                                                  emodules/drv_dab/dab_module/si463x/DAB_Service_List_Handler.o
dab_get_ensemble_info__command                    emodules/drv_dab/dab_module/si463x/si46xx_firmware_api.o
                                                  emodules/drv_dab/dab_module/si463x/si468x.o
                                                  emodules/drv_dab/dab_module/si463x/Firmware_API_Manager.o
dab_get_ensemble_info__reply                      emodules/drv_dab/dab_module/si463x/si46xx_firmware_api.o
                                                  emodules/drv_dab/dab_module/si463x/si468x.o
                                                  emodules/drv_dab/dab_module/si463x/Firmware_API_Manager.o
dab_get_ensemble_name_flag                        emodules/drv_dab/dab_module/si463x/si468x.o
dab_get_event_status__command                     emodules/drv_dab/dab_module/si463x/si46xx_firmware_api.o
                                                  emodules/drv_dab/dab_module/si463x/Firmware_API_Manager.o
dab_get_event_status__reply                       emodules/drv_dab/dab_module/si463x/si46xx_firmware_api.o
                                                  emodules/drv_dab/dab_module/si463x/Firmware_API_Manager.o
dab_get_freq_info__command                        emodules/drv_dab/dab_module/si463x/si46xx_firmware_api.o
                                                  emodules/drv_dab/dab_module/si463x/Firmware_API_Manager.o
dab_get_freq_info__reply                          emodules/drv_dab/dab_module/si463x/si46xx_firmware_api.o
                                                  emodules/drv_dab/dab_module/si463x/Firmware_API_Manager.o
dab_get_freq_list__command                        emodules/drv_dab/dab_module/si463x/si46xx_firmware_api.o
                                                  emodules/drv_dab/dab_module/si463x/si468x.o
                                                  emodules/drv_dab/dab_module/si463x/Firmware_API_Manager.o
dab_get_freq_list__reply                          emodules/drv_dab/dab_module/si463x/si46xx_firmware_api.o
                                                  emodules/drv_dab/dab_module/si463x/si468x.o
                                                  emodules/drv_dab/dab_module/si463x/Firmware_API_Manager.o
dab_get_oe_services_info__command                 emodules/drv_dab/dab_module/si463x/si46xx_firmware_api.o
                                                  emodules/drv_dab/dab_module/si463x/Firmware_API_Manager.o
dab_get_oe_services_info__reply                   emodules/drv_dab/dab_module/si463x/si46xx_firmware_api.o
                                                  emodules/drv_dab/dab_module/si463x/Firmware_API_Manager.o
dab_get_service_info__command                     emodules/drv_dab/dab_module/si463x/si46xx_firmware_api.o
                                                  emodules/drv_dab/dab_module/si463x/Firmware_API_Manager.o
dab_get_service_info__reply                       emodules/drv_dab/dab_module/si463x/si46xx_firmware_api.o
                                                  emodules/drv_dab/dab_module/si463x/Firmware_API_Manager.o
dab_get_service_linking_info__command             emodules/drv_dab/dab_module/si463x/si46xx_firmware_api.o
                                                  emodules/drv_dab/dab_module/si463x/Firmware_API_Manager.o
dab_get_service_linking_info__get_element         emodules/drv_dab/dab_module/si463x/si46xx_firmware_api.o
                                                  emodules/drv_dab/dab_module/si463x/Firmware_API_Manager.o
dab_get_service_linking_info__reply               emodules/drv_dab/dab_module/si463x/si46xx_firmware_api.o
                                                  emodules/drv_dab/dab_module/si463x/Firmware_API_Manager.o
dab_get_time__command                             emodules/drv_dab/dab_module/si463x/si46xx_firmware_api.o
                                                  emodules/drv_dab/dab_module/si463x/Firmware_API_Manager.o
dab_get_time__reply                               emodules/drv_dab/dab_module/si463x/si46xx_firmware_api.o
                                                  emodules/drv_dab/dab_module/si463x/Firmware_API_Manager.o
dab_goto_link_related_service_link                emodules/drv_dab/dab_module/si463x/si468x.o
                                                  emodules/drv_dab/dev_dab.o
dab_have_signal_back_dab_count                    emodules/drv_dab/dab_module/si463x/si468x.o
dab_implicit_rds_dead_link_flg                    emodules/drv_dab/dab_module/si463x/Firmware_API_Manager.o
                                                  emodules/drv_dab/dab_module/si463x/si468x.o
dab_init                                          emodules/drv_dab/dab_module/si463x/si468x.o
dab_initialize                                    emodules/drv_dab/dab_module/si463x/si468x.o
dab_is_wait_CTS_TIMEOUT_reach_threshold           emodules/drv_dab/dab_module/si463x/si468x.o
                                                  emodules/drv_dab/dab_module/si463x/HAL.o
dab_key_process                                   emodules/drv_dab/dab_module/si463x/si468x.o
                                                  emodules/drv_dab/dev_dab.o
dab_last_played_service_list_index                emodules/drv_dab/dab_module/si463x/si468x.o
                                                  emodules/drv_dab/dab_module/si463x/Firmware_API_Manager.o
dab_linkage_set                                   emodules/drv_dab/dab_module/si463x/Firmware_API_Manager.o
dab_linkset_init                                  emodules/drv_dab/dab_module/si463x/Firmware_API_Manager.o
                                                  emodules/drv_dab/dab_module/si463x/si468x.o
dab_load_list_and_play                            emodules/drv_dab/dab_module/si463x/si468x.o
                                                  emodules/drv_dab/dev_dab.o
dab_lock                                          emodules/drv_dab/drv_dab.o
dab_manual_status                                 emodules/drv_dab/dab_module/si463x/si468x.o
dab_nosignal_tune_freqcount                       emodules/drv_dab/dab_module/si463x/si468x.o
                                                  emodules/drv_dab/dab_module/si463x/Firmware_API_Manager.o
dab_nosignal_tune_freqindex                       emodules/drv_dab/dab_module/si463x/si468x.o
                                                  emodules/drv_dab/dab_module/si463x/Firmware_API_Manager.o
dab_para                                          emodules/drv_dab/dab_module/si463x/si468x.o
                                                  emodules/drv_dab/dab_module/si463x/si46xx_firmware_api.o
                                                  emodules/drv_dab/dab_module/si463x/Firmware_API_Manager.o
                                                  emodules/drv_dab/dab_module/si463x/DAB_Service_List_Handler.o
                                                  emodules/drv_dab/dab_module/si463x/DAB_DLS_Handler.o
dab_para_init                                     emodules/drv_dab/dab_module/si463x/si468x.o
dab_para_uninit                                   emodules/drv_dab/dab_module/si463x/si468x.o
dab_preset_add                                    emodules/drv_dab/dab_module/si463x/si468x.o
                                                  emodules/drv_dab/dev_dab.o
dab_pty_seek                                      emodules/drv_dab/dab_module/si463x/si468x.o
                                                  emodules/drv_dab/dev_dab.o
dab_pty_seek_status                               emodules/drv_dab/dab_module/si463x/si468x.o
dab_pty_seek_type                                 emodules/drv_dab/dab_module/si463x/si468x.o
dab_rds_hard_links_fig021024helper                emodules/drv_dab/dab_module/si463x/Firmware_API_Manager.o
dab_rds_hard_links_implicit_rds                   emodules/drv_dab/dab_module/si463x/Firmware_API_Manager.o
dab_rds_soft_links_fig021024helper                emodules/drv_dab/dab_module/si463x/Firmware_API_Manager.o
dab_rescan_by_current_index                       emodules/drv_dab/dab_module/si463x/si468x.o
                                                  emodules/drv_dab/dev_dab.o
dab_save_deinit_para                              emodules/drv_dab/dab_module/si463x/si468x.o
dab_save_erase                                    emodules/drv_dab/dab_module/si463x/si468x.o
                                                  emodules/drv_dab/dev_dab.o
dab_save_flush                                    emodules/drv_dab/dab_module/si463x/si468x.o
                                                  emodules/drv_dab/dev_dab.o
dab_save_init_para                                emodules/drv_dab/dab_module/si463x/si468x.o
dab_scan_nosiganl                                 emodules/drv_dab/dab_module/si463x/si468x.o
dab_seek_break                                    emodules/drv_dab/dab_module/si463x/si468x.o
dab_select_nosiganl                               emodules/drv_dab/dab_module/si463x/si468x.o
dab_set_TI                                        emodules/drv_dab/dab_module/si463x/si468x.o
                                                  emodules/drv_dab/dev_dab.o
dab_set_announcement                              emodules/drv_dab/dab_module/si463x/si468x.o
                                                  emodules/drv_dab/dev_dab.o
dab_set_freq_list__command                        emodules/drv_dab/dab_module/si463x/si46xx_firmware_api.o
                                                  emodules/drv_dab/dab_module/si463x/Firmware_API_Manager.o
dab_set_priority                                  emodules/drv_dab/dab_module/si463x/si468x.o
                                                  emodules/drv_dab/dev_dab.o
dab_set_related_service                           emodules/drv_dab/dab_module/si463x/si468x.o
                                                  emodules/drv_dab/dev_dab.o
dab_setup_info                                    emodules/drv_dab/dab_module/si463x/si468x.o
dab_skip_update_metrics_audio_index_bysidscids_in_srvlist emodules/drv_dab/dab_module/si463x/si468x.o
                                                  emodules/drv_dab/dab_module/si463x/Firmware_API_Manager.o
dab_switch_band                                   emodules/drv_dab/dab_module/si463x/si468x.o
                                                  emodules/drv_dab/dev_dab.o
dab_switch_band_load_and_play                     emodules/drv_dab/dab_module/si463x/si468x.o
dab_switch_dab_output_if_rds_link                 emodules/drv_dab/dab_module/si463x/si468x.o
dab_t                                             emodules/drv_dab/dev_dab.o
dab_test_ber_info__command                        emodules/drv_dab/dab_module/si463x/si46xx_firmware_api.o
                                                  emodules/drv_dab/dab_module/si463x/Firmware_API_Manager.o
dab_test_ber_info__reply                          emodules/drv_dab/dab_module/si463x/si46xx_firmware_api.o
                                                  emodules/drv_dab/dab_module/si463x/Firmware_API_Manager.o
dab_tune_freq__command                            emodules/drv_dab/dab_module/si463x/si46xx_firmware_api.o
                                                  emodules/drv_dab/dab_module/si463x/Firmware_API_Manager.o
dab_wait_cts_timeout_count                        emodules/drv_dab/dab_module/si463x/si468x.o
                                                  emodules/drv_dab/dab_module/si463x/HAL.o
decode_ensemble_str                               emodules/drv_dab/dab_module/si463x/Firmware_API_Manager.o
decode_str                                        emodules/drv_dab/dab_module/si463x/Firmware_API_Manager.o
                                                  emodules/drv_dab/dab_module/si463x/si468x.o
delete_freqStack                                  emodules/drv_dab/dab_module/si463x/freq_stack.o
display_mode                                      emodules/drv_dab/dab_module/si463x/si468x.o
display_para                                      emodules/drv_dab/dab_module/si463x/si468x.o
                                                  emodules/drv_dab/dab_module/si463x/mot.o
                                                  emodules/drv_dab/dab_module/si463x/Firmware_API_Manager.o
dls_decode_str                                    emodules/drv_dab/dab_module/si463x/DAB_DLS_Handler.o
dls_ebu_to_utf16                                  emodules/drv_dab/dab_module/si463x/DAB_DLS_Handler.o
do_linking_bkinfo                                 emodules/drv_dab/dab_module/si463x/si468x.o
                                                  emodules/drv_dab/dab_module/si463x/Firmware_API_Manager.o
dup                                               ./elibrary/bin//libsyscall.a(syscall_fops.o)
dup2                                              ./elibrary/bin//libsyscall.a(syscall_fops.o)
eLIBs_DirEnt2Attr                                 ./elibrary/bin//libminic.a(elibs_stdio.o)
eLIBs_DirEnt2Name                                 ./elibrary/bin//libminic.a(elibs_stdio.o)
eLIBs_DirEnt2Size                                 ./elibrary/bin//libminic.a(elibs_stdio.o)
eLIBs_DirEnt2Time                                 ./elibrary/bin//libminic.a(elibs_stdio.o)
eLIBs_GetDate                                     ./elibrary/bin//libminic.a(elibs_stdio.o)
eLIBs_GetDevName                                  ./elibrary/bin//libminic.a(elibs_stdio.o)
eLIBs_GetDevParts                                 ./elibrary/bin//libminic.a(elibs_stdio.o)
eLIBs_GetDirATime                                 ./elibrary/bin//libminic.a(elibs_stdio.o)
eLIBs_GetDirCTime                                 ./elibrary/bin//libminic.a(elibs_stdio.o)
eLIBs_GetDirMTime                                 ./elibrary/bin//libminic.a(elibs_stdio.o)
eLIBs_GetDirSize                                  ./elibrary/bin//libminic.a(elibs_stdio.o)
eLIBs_GetDiskArray                                ./elibrary/bin//libminic.a(elibs_stdio.o)
eLIBs_GetFSAttribute                              ./elibrary/bin//libminic.a(elibs_stdio.o)
eLIBs_GetFSCharset                                ./elibrary/bin//libminic.a(elibs_stdio.o)
eLIBs_GetFSName                                   ./elibrary/bin//libminic.a(elibs_stdio.o)
eLIBs_GetFSNameLen                                ./elibrary/bin//libminic.a(elibs_stdio.o)
eLIBs_GetFileATime                                ./elibrary/bin//libminic.a(elibs_stdio.o)
eLIBs_GetFileAttributes                           ./elibrary/bin//libminic.a(elibs_stdio.o)
eLIBs_GetFileCTime                                ./elibrary/bin//libminic.a(elibs_stdio.o)
eLIBs_GetFileMTime                                ./elibrary/bin//libminic.a(elibs_stdio.o)
eLIBs_GetFileSize                                 ./elibrary/bin//libminic.a(elibs_stdio.o)
eLIBs_GetNumFiles                                 ./elibrary/bin//libminic.a(elibs_stdio.o)
eLIBs_GetNumSubItems                              ./elibrary/bin//libminic.a(elibs_stdio.o)
eLIBs_GetPTName                                   ./elibrary/bin//libminic.a(elibs_stdio.o)
eLIBs_GetTime                                     ./elibrary/bin//libminic.a(elibs_stdio.o)
eLIBs_GetVolClustSize                             ./elibrary/bin//libminic.a(elibs_stdio.o)
eLIBs_GetVolFSpace                                ./elibrary/bin//libminic.a(elibs_stdio.o)
eLIBs_GetVolName                                  ./elibrary/bin//libminic.a(elibs_stdio.o)
eLIBs_GetVolNameCharset                           ./elibrary/bin//libminic.a(elibs_stdio.o)
eLIBs_GetVolNameLen                               ./elibrary/bin//libminic.a(elibs_stdio.o)
eLIBs_GetVolTSpace                                ./elibrary/bin//libminic.a(elibs_stdio.o)
eLIBs_IsPartFormated                              ./elibrary/bin//libminic.a(elibs_stdio.o)
eLIBs_SetFileAttributes                           ./elibrary/bin//libminic.a(elibs_stdio.o)
eLIBs_closedir                                    ./elibrary/bin//libminic.a(elibs_stdio.o)
eLIBs_disklock                                    ./elibrary/bin//libminic.a(elibs_stdio.o)
eLIBs_diskunlock                                  ./elibrary/bin//libminic.a(elibs_stdio.o)
eLIBs_fclose                                      ./elibrary/bin//libminic.a(elibs_stdio.o)
                                                  emodules/drv_dab/dab_module/si463x/si468x.o
                                                  emodules/drv_dab/dab_module/si463x/cmmbSpi.o
eLIBs_fd2file                                     ./elibrary/bin//libminic.a(elibs_stdio.o)
eLIBs_feof                                        ./elibrary/bin//libminic.a(elibs_stdio.o)
eLIBs_ferrclr                                     ./elibrary/bin//libminic.a(elibs_stdio.o)
eLIBs_ferror                                      ./elibrary/bin//libminic.a(elibs_stdio.o)
eLIBs_fgetc                                       ./elibrary/bin//libminic.a(elibs_stdio.o)
eLIBs_fgets                                       ./elibrary/bin//libminic.a(elibs_stdio.o)
eLIBs_file2fd                                     ./elibrary/bin//libminic.a(elibs_stdio.o)
eLIBs_fioctrl                                     ./elibrary/bin//libminic.a(elibs_stdio.o)
                                                  emodules/drv_dab/dab_module/si463x/si468x.o
eLIBs_fllseek                                     ./elibrary/bin//libminic.a(elibs_stdio.o)
eLIBs_flltell                                     ./elibrary/bin//libminic.a(elibs_stdio.o)
eLIBs_fopen                                       ./elibrary/bin//libminic.a(elibs_stdio.o)
                                                  emodules/drv_dab/dab_module/si463x/si468x.o
                                                  emodules/drv_dab/dab_module/si463x/cmmbSpi.o
eLIBs_format                                      ./elibrary/bin//libminic.a(elibs_stdio.o)
                                                  emodules/drv_dab/dab_module/si463x/si468x.o
eLIBs_fputc                                       ./elibrary/bin//libminic.a(elibs_stdio.o)
eLIBs_fputs                                       ./elibrary/bin//libminic.a(elibs_stdio.o)
eLIBs_fread                                       ./elibrary/bin//libminic.a(elibs_stdio.o)
                                                  emodules/drv_dab/dab_module/si463x/si468x.o
eLIBs_fseek                                       ./elibrary/bin//libminic.a(elibs_stdio.o)
                                                  emodules/drv_dab/dab_module/si463x/si468x.o
eLIBs_fstat                                       ./elibrary/bin//libminic.a(elibs_stdio.o)
eLIBs_fsync                                       ./elibrary/bin//libminic.a(elibs_stdio.o)
eLIBs_ftell                                       ./elibrary/bin//libminic.a(elibs_stdio.o)
                                                  emodules/drv_dab/dab_module/si463x/si468x.o
eLIBs_ftruncate                                   ./elibrary/bin//libminic.a(elibs_stdio.o)
eLIBs_fwrite                                      ./elibrary/bin//libminic.a(elibs_stdio.o)
                                                  emodules/drv_dab/dab_module/si463x/si468x.o
eLIBs_memclr                                      ./elibrary/bin//libminic.a(elibs_string.o)
eLIBs_memcmp                                      ./elibrary/bin//libminic.a(elibs_string.o)
                                                  emodules/drv_dab/dab_module/si463x/Firmware_API_Manager.o
eLIBs_memcpy                                      ./elibrary/bin//libminic.a(elibs_string.o)
                                                  ./elibrary/bin//libminic.a(elibs_stdio.o)
                                                  emodules/drv_dab/dab_module/si463x/mot.o
                                                  emodules/drv_dab/dab_module/si463x/si468x.o
                                                  emodules/drv_dab/dab_module/si463x/si46xx_firmware_api.o
                                                  emodules/drv_dab/dab_module/si463x/FMHD_Alert_Handler.o
                                                  emodules/drv_dab/dab_module/si463x/FMHD_SIS_Handler.o
                                                  emodules/drv_dab/dab_module/si463x/FMHD_Service_List_Handler.o
                                                  emodules/drv_dab/dab_module/si463x/Firmware_Load_Helpers.o
                                                  emodules/drv_dab/dab_module/si463x/Firmware_API_Manager.o
                                                  emodules/drv_dab/dab_module/si463x/DAB_Service_List_Handler.o
                                                  emodules/drv_dab/dab_module/si463x/DAB_DLS_Handler.o
                                                  emodules/drv_dab/dab_module/si463x/cmmbSpi.o
eLIBs_memmove                                     ./elibrary/bin//libminic.a(elibs_string.o)
eLIBs_memset                                      ./elibrary/bin//libminic.a(elibs_string.o)
                                                  emodules/drv_dab/dab_module/si463x/mot.o
                                                  emodules/drv_dab/dab_module/si463x/si468x.o
                                                  emodules/drv_dab/dab_module/si463x/si46xx_firmware_api.o
                                                  emodules/drv_dab/dab_module/si463x/RDS_Handler.o
                                                  emodules/drv_dab/dab_module/si463x/HAL.o
                                                  emodules/drv_dab/dab_module/si463x/FMHD_Alert_Handler.o
                                                  emodules/drv_dab/dab_module/si463x/FMHD_SIS_Handler.o
                                                  emodules/drv_dab/dab_module/si463x/FMHD_PSD_Handler.o
                                                  emodules/drv_dab/dab_module/si463x/Firmware_Load_Helpers.o
                                                  emodules/drv_dab/dab_module/si463x/Firmware_API_Manager.o
                                                  emodules/drv_dab/dab_module/si463x/DAB_Service_List_Handler.o
                                                  emodules/drv_dab/dab_module/si463x/DAB_DLS_Handler.o
eLIBs_mkdir                                       ./elibrary/bin//libminic.a(elibs_stdio.o)
eLIBs_open                                        ./elibrary/bin//libminic.a(elibs_stdio.o)
eLIBs_opendir                                     ./elibrary/bin//libminic.a(elibs_stdio.o)
eLIBs_printf                                      ./elibrary/bin//libminic.a(elibs_stdio.o)
                                                  emodules/drv_dab/dab_module/si463x/si468x.o
                                                  emodules/drv_dab/dab_module/si463x/HAL.o
                                                  emodules/drv_dab/dab_module/si463x/Firmware_API_Manager.o
eLIBs_readdir                                     ./elibrary/bin//libminic.a(elibs_stdio.o)
eLIBs_remove                                      ./elibrary/bin//libminic.a(elibs_stdio.o)
eLIBs_rename                                      ./elibrary/bin//libminic.a(elibs_stdio.o)
eLIBs_rewinddir                                   ./elibrary/bin//libminic.a(elibs_stdio.o)
eLIBs_rmdir                                       ./elibrary/bin//libminic.a(elibs_stdio.o)
eLIBs_scnprintf                                   ./elibrary/bin//libminic.a(elibs_sprintf.o)
eLIBs_setfs                                       ./elibrary/bin//libminic.a(elibs_stdio.o)
eLIBs_snprintf                                    ./elibrary/bin//libminic.a(elibs_sprintf.o)
eLIBs_sprintf                                     ./elibrary/bin//libminic.a(elibs_sprintf.o)
                                                  emodules/drv_dab/dab_module/si463x/Firmware_API_Manager.o
eLIBs_statfs                                      ./elibrary/bin//libminic.a(elibs_stdio.o)
eLIBs_strcat                                      ./elibrary/bin//libminic.a(elibs_string.o)
                                                  ./elibrary/bin//libminic.a(elibs_stdio.o)
eLIBs_strchr                                      ./elibrary/bin//libminic.a(elibs_string.o)
eLIBs_strchrlast                                  ./elibrary/bin//libminic.a(elibs_string.o)
eLIBs_strcmp                                      ./elibrary/bin//libminic.a(elibs_string.o)
                                                  ./elibrary/bin//libminic.a(elibs_stdio.o)
eLIBs_strcpy                                      ./elibrary/bin//libminic.a(elibs_string.o)
                                                  ./elibrary/bin//libminic.a(elibs_stdio.o)
                                                  emodules/drv_dab/dab_module/si463x/si468x.o
eLIBs_stricmp                                     ./elibrary/bin//libminic.a(elibs_string.o)
                                                  ./elibrary/bin//libminic.a(elibs_stdio.o)
eLIBs_strlen                                      ./elibrary/bin//libminic.a(elibs_string.o)
                                                  ./elibrary/bin//libminic.a(elibs_stdio.o)
                                                  emodules/drv_dab/dab_module/si463x/si468x.o
eLIBs_strncat                                     ./elibrary/bin//libminic.a(elibs_string.o)
eLIBs_strnchr                                     ./elibrary/bin//libminic.a(elibs_string.o)
eLIBs_strncmp                                     ./elibrary/bin//libminic.a(elibs_string.o)
                                                  emodules/drv_dab/dab_module/si463x/si468x.o
eLIBs_strncpy                                     ./elibrary/bin//libminic.a(elibs_string.o)
                                                  ./elibrary/bin//libminic.a(elibs_stdio.o)
                                                  emodules/drv_dab/dab_module/si463x/si468x.o
                                                  emodules/drv_dab/dab_module/si463x/FMHD_PSD_Handler.o
                                                  emodules/drv_dab/dab_module/si463x/Firmware_API_Manager.o
                                                  emodules/drv_dab/dab_module/si463x/DAB_Service_List_Handler.o
eLIBs_strnicmp                                    ./elibrary/bin//libminic.a(elibs_string.o)
eLIBs_strnlen                                     ./elibrary/bin//libminic.a(elibs_string.o)
eLIBs_strstr                                      ./elibrary/bin//libminic.a(elibs_string.o)
eLIBs_vprintf                                     ./elibrary/bin//libminic.a(elibs_stdio.o)
eLIBs_vscnprintf                                  ./elibrary/bin//libminic.a(elibs_sprintf.o)
                                                  ./elibrary/bin//libminic.a(elibs_stdio.o)
eLIBs_vsnprintf                                   ./elibrary/bin//libminic.a(elibs_sprintf.o)
eLIBs_vsprintf                                    ./elibrary/bin//libminic.a(elibs_sprintf.o)
ebu_to_utf16                                      emodules/drv_dab/dab_module/si463x/Firmware_API_Manager.o
eccDisplay                                        emodules/drv_dab/dab_module/si463x/RDS_Handler.o
esCFG_Exit_Ex                                     ./elibrary/bin//libsyscall.a(syscall.o)
esCFG_GetGPIOSecData                              ./elibrary/bin//libsyscall.a(syscall.o)
esCFG_GetGPIOSecData_Ex                           ./elibrary/bin//libsyscall.a(syscall.o)
esCFG_GetGPIOSecKeyCount                          ./elibrary/bin//libsyscall.a(syscall.o)
esCFG_GetGPIOSecKeyCount_Ex                       ./elibrary/bin//libsyscall.a(syscall.o)
esCFG_GetKeyValue                                 ./elibrary/bin//libsyscall.a(syscall.o)
esCFG_GetKeyValue_Ex                              ./elibrary/bin//libsyscall.a(syscall.o)
esCFG_GetSecCount                                 ./elibrary/bin//libsyscall.a(syscall.o)
esCFG_GetSecCount_Ex                              ./elibrary/bin//libsyscall.a(syscall.o)
esCFG_GetSecKeyCount                              ./elibrary/bin//libsyscall.a(syscall.o)
esCFG_GetSecKeyCount_Ex                           ./elibrary/bin//libsyscall.a(syscall.o)
esCFG_Init_Ex                                     ./elibrary/bin//libsyscall.a(syscall.o)
esCHS_Char2Uni                                    ./elibrary/bin//libsyscall.a(syscall.o)
                                                  emodules/drv_dab/dab_module/si463x/DAB_DLS_Handler.o
esCHS_GetChLowerTbl                               ./elibrary/bin//libsyscall.a(syscall.o)
esCHS_GetChUpperTbl                               ./elibrary/bin//libsyscall.a(syscall.o)
esCHS_Uni2Char                                    ./elibrary/bin//libsyscall.a(syscall.o)
                                                  emodules/drv_dab/dab_module/si463x/DAB_DLS_Handler.o
esCLK_CloseMclk                                   ./elibrary/bin//libsyscall.a(syscall.o)
esCLK_GetMclkDiv                                  ./elibrary/bin//libsyscall.a(syscall.o)
esCLK_GetMclkSrc                                  ./elibrary/bin//libsyscall.a(syscall.o)
esCLK_GetRound_Rate                               ./elibrary/bin//libsyscall.a(syscall.o)
esCLK_GetSrcFreq                                  ./elibrary/bin//libsyscall.a(syscall.o)
esCLK_MclkAssert                                  ./elibrary/bin//libsyscall.a(syscall.o)
esCLK_MclkDeassert                                ./elibrary/bin//libsyscall.a(syscall.o)
esCLK_MclkGetRstStatus                            ./elibrary/bin//libsyscall.a(syscall.o)
esCLK_MclkOnOff                                   ./elibrary/bin//libsyscall.a(syscall.o)
esCLK_MclkRegCb                                   ./elibrary/bin//libsyscall.a(syscall.o)
esCLK_MclkReset                                   ./elibrary/bin//libsyscall.a(syscall.o)
esCLK_MclkUnregCb                                 ./elibrary/bin//libsyscall.a(syscall.o)
esCLK_ModInfo                                     ./elibrary/bin//libsyscall.a(syscall.o)
esCLK_OpenMclk                                    ./elibrary/bin//libsyscall.a(syscall.o)
esCLK_SetMclkDiv                                  ./elibrary/bin//libsyscall.a(syscall.o)
esCLK_SetMclkSrc                                  ./elibrary/bin//libsyscall.a(syscall.o)
esCLK_SetSrcFreq                                  ./elibrary/bin//libsyscall.a(syscall.o)
esCLK_SysInfo                                     ./elibrary/bin//libsyscall.a(syscall.o)
esDEV_Close                                       ./elibrary/bin//libsyscall.a(syscall.o)
esDEV_DevReg                                      ./elibrary/bin//libsyscall.a(syscall.o)
esDEV_DevUnreg                                    ./elibrary/bin//libsyscall.a(syscall.o)
esDEV_Insmod                                      ./elibrary/bin//libsyscall.a(syscall.o)
esDEV_Ioctl                                       ./elibrary/bin//libsyscall.a(syscall.o)
esDEV_Lock                                        ./elibrary/bin//libsyscall.a(syscall.o)
esDEV_Open                                        ./elibrary/bin//libsyscall.a(syscall.o)
esDEV_Plugin                                      ./elibrary/bin//libsyscall.a(syscall.o)
esDEV_Plugout                                     ./elibrary/bin//libsyscall.a(syscall.o)
esDEV_Read                                        ./elibrary/bin//libsyscall.a(syscall.o)
esDEV_Unimod                                      ./elibrary/bin//libsyscall.a(syscall.o)
esDEV_Unlock                                      ./elibrary/bin//libsyscall.a(syscall.o)
esDEV_Write                                       ./elibrary/bin//libsyscall.a(syscall.o)
esDMA_ChangeMode                                  ./elibrary/bin//libsyscall.a(syscall.o)
esDMA_DisableINT                                  ./elibrary/bin//libsyscall.a(syscall.o)
esDMA_EnableINT                                   ./elibrary/bin//libsyscall.a(syscall.o)
esDMA_Information                                 ./elibrary/bin//libsyscall.a(syscall.o)
esDMA_QueryDst                                    ./elibrary/bin//libsyscall.a(syscall.o)
esDMA_QueryRestCount                              ./elibrary/bin//libsyscall.a(syscall.o)
esDMA_QuerySrc                                    ./elibrary/bin//libsyscall.a(syscall.o)
esDMA_QueryStat                                   ./elibrary/bin//libsyscall.a(syscall.o)
esDMA_RegDmaHdler                                 ./elibrary/bin//libsyscall.a(syscall.o)
esDMA_Release                                     ./elibrary/bin//libsyscall.a(syscall.o)
esDMA_Request                                     ./elibrary/bin//libsyscall.a(syscall.o)
esDMA_Restart                                     ./elibrary/bin//libsyscall.a(syscall.o)
esDMA_Setting                                     ./elibrary/bin//libsyscall.a(syscall.o)
esDMA_Start                                       ./elibrary/bin//libsyscall.a(syscall.o)
esDMA_Stop                                        ./elibrary/bin//libsyscall.a(syscall.o)
esDMA_UnregDmaHdler                               ./elibrary/bin//libsyscall.a(syscall.o)
esEXEC_PCreate                                    ./elibrary/bin//libsyscall.a(syscall.o)
esEXEC_PDel                                       ./elibrary/bin//libsyscall.a(syscall.o)
esEXEC_PDelReq                                    ./elibrary/bin//libsyscall.a(syscall.o)
esEXEC_Run                                        ./elibrary/bin//libsyscall.a(syscall.o)
esFSYS_clearpartupdateflag                        ./elibrary/bin//libsyscall.a(syscall.o)
esFSYS_closedir                                   ./elibrary/bin//libsyscall.a(syscall.o)
                                                  ./elibrary/bin//libminic.a(elibs_stdio.o)
esFSYS_fclose                                     ./elibrary/bin//libsyscall.a(syscall.o)
                                                  ./elibrary/bin//libminic.a(elibs_stdio.o)
esFSYS_fd2file                                    ./elibrary/bin//libsyscall.a(syscall.o)
                                                  ./elibrary/bin//libminic.a(elibs_stdio.o)
esFSYS_ferrclr                                    ./elibrary/bin//libsyscall.a(syscall.o)
                                                  ./elibrary/bin//libminic.a(elibs_stdio.o)
esFSYS_ferror                                     ./elibrary/bin//libsyscall.a(syscall.o)
                                                  ./elibrary/bin//libminic.a(elibs_stdio.o)
esFSYS_file2fd                                    ./elibrary/bin//libsyscall.a(syscall.o)
                                                  ./elibrary/bin//libminic.a(elibs_stdio.o)
esFSYS_fioctrl                                    ./elibrary/bin//libsyscall.a(syscall.o)
                                                  ./elibrary/bin//libminic.a(elibs_stdio.o)
esFSYS_fopen                                      ./elibrary/bin//libsyscall.a(syscall.o)
                                                  ./elibrary/bin//libminic.a(elibs_stdio.o)
esFSYS_format                                     ./elibrary/bin//libsyscall.a(syscall.o)
                                                  ./elibrary/bin//libminic.a(elibs_stdio.o)
esFSYS_fread                                      ./elibrary/bin//libsyscall.a(syscall.o)
                                                  ./elibrary/bin//libminic.a(elibs_stdio.o)
esFSYS_fsdbg                                      ./elibrary/bin//libsyscall.a(syscall.o)
esFSYS_fseek                                      ./elibrary/bin//libsyscall.a(syscall.o)
                                                  ./elibrary/bin//libminic.a(elibs_stdio.o)
esFSYS_fseekex                                    ./elibrary/bin//libsyscall.a(syscall.o)
                                                  ./elibrary/bin//libminic.a(elibs_stdio.o)
esFSYS_fsreg                                      ./elibrary/bin//libsyscall.a(syscall.o)
esFSYS_fstat                                      ./elibrary/bin//libsyscall.a(syscall.o)
                                                  ./elibrary/bin//libminic.a(elibs_stdio.o)
esFSYS_fsunreg                                    ./elibrary/bin//libsyscall.a(syscall.o)
esFSYS_fsync                                      ./elibrary/bin//libsyscall.a(syscall.o)
                                                  ./elibrary/bin//libminic.a(elibs_stdio.o)
esFSYS_ftell                                      ./elibrary/bin//libsyscall.a(syscall.o)
                                                  ./elibrary/bin//libminic.a(elibs_stdio.o)
esFSYS_ftellex                                    ./elibrary/bin//libsyscall.a(syscall.o)
                                                  ./elibrary/bin//libminic.a(elibs_stdio.o)
esFSYS_ftruncate                                  ./elibrary/bin//libsyscall.a(syscall.o)
                                                  ./elibrary/bin//libminic.a(elibs_stdio.o)
esFSYS_fwrite                                     ./elibrary/bin//libsyscall.a(syscall.o)
                                                  ./elibrary/bin//libminic.a(elibs_stdio.o)
esFSYS_getfscharset                               ./elibrary/bin//libsyscall.a(syscall.o)
                                                  ./elibrary/bin//libminic.a(elibs_stdio.o)
esFSYS_mkdir                                      ./elibrary/bin//libsyscall.a(syscall.o)
                                                  ./elibrary/bin//libminic.a(elibs_stdio.o)
esFSYS_mntfs                                      ./elibrary/bin//libsyscall.a(syscall.o)
esFSYS_mntparts                                   ./elibrary/bin//libsyscall.a(syscall.o)
esFSYS_open                                       ./elibrary/bin//libsyscall.a(syscall.o)
                                                  ./elibrary/bin//libminic.a(elibs_stdio.o)
esFSYS_opendir                                    ./elibrary/bin//libsyscall.a(syscall.o)
                                                  ./elibrary/bin//libminic.a(elibs_stdio.o)
esFSYS_partfslck                                  ./elibrary/bin//libsyscall.a(syscall.o)
                                                  ./elibrary/bin//libminic.a(elibs_stdio.o)
esFSYS_partfsunlck                                ./elibrary/bin//libsyscall.a(syscall.o)
                                                  ./elibrary/bin//libminic.a(elibs_stdio.o)
esFSYS_pclose                                     ./elibrary/bin//libsyscall.a(syscall.o)
esFSYS_pdreg                                      ./elibrary/bin//libsyscall.a(syscall.o)
esFSYS_pdunreg                                    ./elibrary/bin//libsyscall.a(syscall.o)
esFSYS_perrclr                                    ./elibrary/bin//libsyscall.a(syscall.o)
esFSYS_perror                                     ./elibrary/bin//libsyscall.a(syscall.o)
esFSYS_pioctrl                                    ./elibrary/bin//libsyscall.a(syscall.o)
esFSYS_popen                                      ./elibrary/bin//libsyscall.a(syscall.o)
esFSYS_pread                                      ./elibrary/bin//libsyscall.a(syscall.o)
esFSYS_premove                                    ./elibrary/bin//libsyscall.a(syscall.o)
esFSYS_prename                                    ./elibrary/bin//libsyscall.a(syscall.o)
esFSYS_pwrite                                     ./elibrary/bin//libsyscall.a(syscall.o)
esFSYS_querypartupdateflag                        ./elibrary/bin//libsyscall.a(syscall.o)
esFSYS_readdir                                    ./elibrary/bin//libsyscall.a(syscall.o)
                                                  ./elibrary/bin//libminic.a(elibs_stdio.o)
esFSYS_remove                                     ./elibrary/bin//libsyscall.a(syscall.o)
                                                  ./elibrary/bin//libminic.a(elibs_stdio.o)
esFSYS_rename                                     ./elibrary/bin//libsyscall.a(syscall.o)
                                                  ./elibrary/bin//libminic.a(elibs_stdio.o)
esFSYS_rewinddir                                  ./elibrary/bin//libsyscall.a(syscall.o)
                                                  ./elibrary/bin//libminic.a(elibs_stdio.o)
esFSYS_rmdir                                      ./elibrary/bin//libsyscall.a(syscall.o)
                                                  ./elibrary/bin//libminic.a(elibs_stdio.o)
esFSYS_setfs                                      ./elibrary/bin//libsyscall.a(syscall.o)
                                                  ./elibrary/bin//libminic.a(elibs_stdio.o)
esFSYS_statfs                                     ./elibrary/bin//libsyscall.a(syscall.o)
                                                  ./elibrary/bin//libminic.a(elibs_stdio.o)
esFSYS_statpt                                     ./elibrary/bin//libsyscall.a(syscall.o)
                                                  ./elibrary/bin//libminic.a(elibs_stdio.o)
esFSYS_umntfs                                     ./elibrary/bin//libsyscall.a(syscall.o)
esFSYS_umntparts                                  ./elibrary/bin//libsyscall.a(syscall.o)
esHID_SendMsg                                     ./elibrary/bin//libsyscall.a(syscall.o)
esHID_hiddevreg                                   ./elibrary/bin//libsyscall.a(syscall.o)
esHID_hiddevunreg                                 ./elibrary/bin//libsyscall.a(syscall.o)
esINPUT_GetLdevID                                 ./elibrary/bin//libsyscall.a(syscall.o)
esINPUT_LdevCtl                                   ./elibrary/bin//libsyscall.a(syscall.o)
esINPUT_LdevFeedback                              ./elibrary/bin//libsyscall.a(syscall.o)
esINPUT_LdevGrab                                  ./elibrary/bin//libsyscall.a(syscall.o)
esINPUT_LdevRelease                               ./elibrary/bin//libsyscall.a(syscall.o)
esINPUT_RegDev                                    ./elibrary/bin//libsyscall.a(syscall.o)
esINPUT_SendEvent                                 ./elibrary/bin//libsyscall.a(syscall.o)
esINPUT_UnregDev                                  ./elibrary/bin//libsyscall.a(syscall.o)
esINT_DisableINT                                  ./elibrary/bin//libsyscall.a(syscall.o)
esINT_EnableINT                                   ./elibrary/bin//libsyscall.a(syscall.o)
esINT_InsFIR                                      ./elibrary/bin//libsyscall.a(syscall.o)
esINT_InsISR                                      ./elibrary/bin//libsyscall.a(syscall.o)
esINT_SetIRQPrio                                  ./elibrary/bin//libsyscall.a(syscall.o)
esINT_UniFIR                                      ./elibrary/bin//libsyscall.a(syscall.o)
esINT_UniISR                                      ./elibrary/bin//libsyscall.a(syscall.o)
esKRNL_CallBack                                   ./elibrary/bin//libsyscall.a(syscall.o)
esKRNL_DumpStack                                  ./elibrary/bin//libsyscall.a(syscall.o)
esKRNL_FlagAccept                                 ./elibrary/bin//libsyscall.a(syscall.o)
esKRNL_FlagCreate                                 ./elibrary/bin//libsyscall.a(syscall.o)
esKRNL_FlagDel                                    ./elibrary/bin//libsyscall.a(syscall.o)
esKRNL_FlagNameGet                                ./elibrary/bin//libsyscall.a(syscall.o)
esKRNL_FlagNameSet                                ./elibrary/bin//libsyscall.a(syscall.o)
esKRNL_FlagPend                                   ./elibrary/bin//libsyscall.a(syscall.o)
esKRNL_FlagPendGetFlagsRdy                        ./elibrary/bin//libsyscall.a(syscall.o)
esKRNL_FlagPost                                   ./elibrary/bin//libsyscall.a(syscall.o)
esKRNL_FlagQuery                                  ./elibrary/bin//libsyscall.a(syscall.o)
esKRNL_GetCallBack                                ./elibrary/bin//libsyscall.a(syscall.o)
esKRNL_GetTIDCur                                  ./elibrary/bin//libsyscall.a(syscall.o)
esKRNL_InterruptDisable                           ./elibrary/bin//libsyscall.a(syscall.o)
esKRNL_InterruptEnable                            ./elibrary/bin//libsyscall.a(syscall.o)
esKRNL_Ioctrl                                     ./elibrary/bin//libsyscall.a(syscall.o)
esKRNL_MboxAccept                                 ./elibrary/bin//libsyscall.a(syscall.o)
esKRNL_MboxCreate                                 ./elibrary/bin//libsyscall.a(syscall.o)
esKRNL_MboxDel                                    ./elibrary/bin//libsyscall.a(syscall.o)
esKRNL_MboxPend                                   ./elibrary/bin//libsyscall.a(syscall.o)
esKRNL_MboxPost                                   ./elibrary/bin//libsyscall.a(syscall.o)
esKRNL_MboxPostOpt                                ./elibrary/bin//libsyscall.a(syscall.o)
esKRNL_MboxQuery                                  ./elibrary/bin//libsyscall.a(syscall.o)
esKRNL_MemLeakChk                                 ./elibrary/bin//libsyscall.a(syscall.o)
esKRNL_MutexCreate                                ./elibrary/bin//libsyscall.a(syscall.o)
esKRNL_MutexDel                                   ./elibrary/bin//libsyscall.a(syscall.o)
esKRNL_MutexPend                                  ./elibrary/bin//libsyscall.a(syscall.o)
esKRNL_MutexPost                                  ./elibrary/bin//libsyscall.a(syscall.o)
esKRNL_QAccept                                    ./elibrary/bin//libsyscall.a(syscall.o)
esKRNL_QCreate                                    ./elibrary/bin//libsyscall.a(syscall.o)
esKRNL_QDel                                       ./elibrary/bin//libsyscall.a(syscall.o)
esKRNL_QFlush                                     ./elibrary/bin//libsyscall.a(syscall.o)
esKRNL_QPend                                      ./elibrary/bin//libsyscall.a(syscall.o)
esKRNL_QPost                                      ./elibrary/bin//libsyscall.a(syscall.o)
esKRNL_QPostFront                                 ./elibrary/bin//libsyscall.a(syscall.o)
esKRNL_QPostOpt                                   ./elibrary/bin//libsyscall.a(syscall.o)
esKRNL_QQuery                                     ./elibrary/bin//libsyscall.a(syscall.o)
esKRNL_SchedLock                                  ./elibrary/bin//libsyscall.a(syscall.o)
esKRNL_SchedUnlock                                ./elibrary/bin//libsyscall.a(syscall.o)
esKRNL_SemAccept                                  ./elibrary/bin//libsyscall.a(syscall.o)
esKRNL_SemCreate                                  ./elibrary/bin//libsyscall.a(syscall.o)
                                                  emodules/drv_dab/dab_module/si463x/si468x.o
                                                  emodules/drv_dab/dab_module/si463x/cmmbSpi.o
                                                  emodules/drv_dab/drv_dab.o
esKRNL_SemDel                                     ./elibrary/bin//libsyscall.a(syscall.o)
                                                  emodules/drv_dab/dab_module/si463x/si468x.o
                                                  emodules/drv_dab/dab_module/si463x/cmmbSpi.o
                                                  emodules/drv_dab/drv_dab.o
esKRNL_SemPend                                    ./elibrary/bin//libsyscall.a(syscall.o)
                                                  emodules/drv_dab/dab_module/si463x/si468x.o
esKRNL_SemPost                                    ./elibrary/bin//libsyscall.a(syscall.o)
                                                  emodules/drv_dab/dab_module/si463x/si468x.o
esKRNL_SemQuery                                   ./elibrary/bin//libsyscall.a(syscall.o)
esKRNL_SemSet                                     ./elibrary/bin//libsyscall.a(syscall.o)
esKRNL_SktAccept                                  ./elibrary/bin//libsyscall.a(syscall.o)
esKRNL_SktCreate                                  ./elibrary/bin//libsyscall.a(syscall.o)
esKRNL_SktDel                                     ./elibrary/bin//libsyscall.a(syscall.o)
esKRNL_SktPend                                    ./elibrary/bin//libsyscall.a(syscall.o)
esKRNL_SktPost                                    ./elibrary/bin//libsyscall.a(syscall.o)
esKRNL_TCreate                                    ./elibrary/bin//libsyscall.a(syscall.o)
                                                  emodules/drv_dab/dab_module/si463x/si468x.o
esKRNL_TDel                                       ./elibrary/bin//libsyscall.a(syscall.o)
                                                  emodules/drv_dab/dab_module/si463x/si468x.o
esKRNL_TDelReq                                    ./elibrary/bin//libsyscall.a(syscall.o)
                                                  emodules/drv_dab/dab_module/si463x/si468x.o
esKRNL_TaskNameSet                                ./elibrary/bin//libsyscall.a(syscall.o)
esKRNL_TaskPrefEn                                 ./elibrary/bin//libsyscall.a(syscall.o)
esKRNL_TaskQuery                                  ./elibrary/bin//libsyscall.a(syscall.o)
esKRNL_TaskResume                                 ./elibrary/bin//libsyscall.a(syscall.o)
esKRNL_TaskSuspend                                ./elibrary/bin//libsyscall.a(syscall.o)
esKRNL_Time                                       ./elibrary/bin//libsyscall.a(syscall.o)
esKRNL_TimeDly                                    ./elibrary/bin//libsyscall.a(syscall.o)
                                                  emodules/drv_dab/dab_module/si463x/si468x.o
                                                  emodules/drv_dab/dab_module/si463x/HAL.o
                                                  emodules/drv_dab/dab_module/si463x/Firmware_API_Manager.o
                                                  emodules/drv_dab/dab_module/si463x/cmmbSpi.o
esKRNL_TimeDlyResume                              ./elibrary/bin//libsyscall.a(syscall.o)
esKRNL_TimeGet                                    ./elibrary/bin//libsyscall.a(syscall.o)
                                                  emodules/drv_dab/dab_module/si463x/si468x.o
esKRNL_TimeSet                                    ./elibrary/bin//libsyscall.a(syscall.o)
esKRNL_TmrCreate                                  ./elibrary/bin//libsyscall.a(syscall.o)
esKRNL_TmrDel                                     ./elibrary/bin//libsyscall.a(syscall.o)
esKRNL_TmrError                                   ./elibrary/bin//libsyscall.a(syscall.o)
esKRNL_TmrRemainGet                               ./elibrary/bin//libsyscall.a(syscall.o)
esKRNL_TmrStart                                   ./elibrary/bin//libsyscall.a(syscall.o)
esKRNL_TmrStateGet                                ./elibrary/bin//libsyscall.a(syscall.o)
esKRNL_TmrStop                                    ./elibrary/bin//libsyscall.a(syscall.o)
esKRNL_Version                                    ./elibrary/bin//libsyscall.a(syscall.o)
esKSRV_ClearWatchDog                              ./elibrary/bin//libsyscall.a(syscall.o)
esKSRV_DisableWatchDog                            ./elibrary/bin//libsyscall.a(syscall.o)
esKSRV_DisableWatchDogSrv                         ./elibrary/bin//libsyscall.a(syscall.o)
esKSRV_EnableWatchDog                             ./elibrary/bin//libsyscall.a(syscall.o)
esKSRV_EnableWatchDogSrv                          ./elibrary/bin//libsyscall.a(syscall.o)
esKSRV_GetAddPara                                 ./elibrary/bin//libsyscall.a(syscall.o)
esKSRV_GetDramCfgPara                             ./elibrary/bin//libsyscall.a(syscall.o)
esKSRV_GetHighMsg                                 ./elibrary/bin//libsyscall.a(syscall.o)
esKSRV_GetLowMsg                                  ./elibrary/bin//libsyscall.a(syscall.o)
esKSRV_GetMsg                                     ./elibrary/bin//libsyscall.a(syscall.o)
esKSRV_GetPara                                    ./elibrary/bin//libsyscall.a(syscall.o)
esKSRV_GetSocID                                   ./elibrary/bin//libsyscall.a(syscall.o)
esKSRV_GetTargetPara                              ./elibrary/bin//libsyscall.a(syscall.o)
esKSRV_GetVersion                                 ./elibrary/bin//libsyscall.a(syscall.o)
esKSRV_Get_Display_Hld                            ./elibrary/bin//libsyscall.a(syscall.o)
esKSRV_Get_Mixture_Hld                            ./elibrary/bin//libsyscall.a(syscall.o)
esKSRV_Get_Raw_Decoder_Hld                        ./elibrary/bin//libsyscall.a(syscall.o)
esKSRV_PowerOff                                   ./elibrary/bin//libsyscall.a(syscall.o)
esKSRV_Random                                     ./elibrary/bin//libsyscall.a(syscall.o)
esKSRV_Reset                                      ./elibrary/bin//libsyscall.a(syscall.o)
esKSRV_Save_Display_Hld                           ./elibrary/bin//libsyscall.a(syscall.o)
esKSRV_Save_Mixture_Hld                           ./elibrary/bin//libsyscall.a(syscall.o)
esKSRV_Save_Raw_Decoder_Hld                       ./elibrary/bin//libsyscall.a(syscall.o)
esKSRV_SendMsg                                    ./elibrary/bin//libsyscall.a(syscall.o)
                                                  emodules/drv_dab/dab_module/si463x/mot.o
                                                  emodules/drv_dab/dab_module/si463x/si468x.o
                                                  emodules/drv_dab/dab_module/si463x/Firmware_API_Manager.o
                                                  emodules/drv_dab/dab_module/si463x/DAB_DLS_Handler.o
esKSRV_SendMsgEx                                  ./elibrary/bin//libsyscall.a(syscall.o)
esKSRV_SysInfo                                    ./elibrary/bin//libsyscall.a(syscall.o)
esKSRV_memcpy                                     ./elibrary/bin//libsyscall.a(syscall.o)
esKSRV_memset                                     ./elibrary/bin//libsyscall.a(syscall.o)
esMEMS_Balloc                                     ./elibrary/bin//libsyscall.a(syscall.o)
                                                  emodules/drv_dab/dab_module/si463x/si468x.o
                                                  emodules/drv_dab/dab_module/si463x/cmmbSpi.o
esMEMS_Bfree                                      ./elibrary/bin//libsyscall.a(syscall.o)
                                                  emodules/drv_dab/dab_module/si463x/si468x.o
                                                  emodules/drv_dab/dab_module/si463x/cmmbSpi.o
esMEMS_Calloc                                     ./elibrary/bin//libsyscall.a(syscall.o)
esMEMS_CleanDCache                                ./elibrary/bin//libsyscall.a(syscall.o)
esMEMS_CleanDCacheRegion                          ./elibrary/bin//libsyscall.a(syscall.o)
esMEMS_CleanFlushCache                            ./elibrary/bin//libsyscall.a(syscall.o)
esMEMS_CleanFlushCacheRegion                      ./elibrary/bin//libsyscall.a(syscall.o)
esMEMS_CleanFlushDCache                           ./elibrary/bin//libsyscall.a(syscall.o)
esMEMS_CleanFlushDCacheRegion                     ./elibrary/bin//libsyscall.a(syscall.o)
esMEMS_CleanInvalidateCacheAll                    ./elibrary/bin//libsyscall.a(syscall.o)
esMEMS_FlushCache                                 ./elibrary/bin//libsyscall.a(syscall.o)
esMEMS_FlushCacheRegion                           ./elibrary/bin//libsyscall.a(syscall.o)
esMEMS_FlushDCache                                ./elibrary/bin//libsyscall.a(syscall.o)
esMEMS_FlushDCacheRegion                          ./elibrary/bin//libsyscall.a(syscall.o)
esMEMS_FlushICache                                ./elibrary/bin//libsyscall.a(syscall.o)
esMEMS_FlushICacheRegion                          ./elibrary/bin//libsyscall.a(syscall.o)
esMEMS_FreeMemSize                                ./elibrary/bin//libsyscall.a(syscall.o)
esMEMS_GetIoVAByPA                                ./elibrary/bin//libsyscall.a(syscall.o)
esMEMS_HeapCreate                                 ./elibrary/bin//libsyscall.a(syscall.o)
esMEMS_HeapDel                                    ./elibrary/bin//libsyscall.a(syscall.o)
esMEMS_Info                                       ./elibrary/bin//libsyscall.a(syscall.o)
esMEMS_Malloc                                     ./elibrary/bin//libsyscall.a(syscall.o)
                                                  ./elibrary/bin//libminic.a(elibs_stdio.o)
                                                  emodules/drv_dab/dab_module/si463x/mot.o
                                                  emodules/drv_dab/dab_module/si463x/freq_stack.o
                                                  emodules/drv_dab/dab_module/si463x/Firmware_API_Manager.o
esMEMS_Mfree                                      ./elibrary/bin//libsyscall.a(syscall.o)
                                                  ./elibrary/bin//libminic.a(elibs_stdio.o)
                                                  emodules/drv_dab/dab_module/si463x/mot.o
                                                  emodules/drv_dab/dab_module/si463x/freq_stack.o
                                                  emodules/drv_dab/dab_module/si463x/Firmware_API_Manager.o
esMEMS_Palloc                                     ./elibrary/bin//libsyscall.a(syscall.o)
esMEMS_Pfree                                      ./elibrary/bin//libsyscall.a(syscall.o)
esMEMS_Realloc                                    ./elibrary/bin//libsyscall.a(syscall.o)
                                                  emodules/drv_dab/dab_module/si463x/freq_stack.o
esMEMS_TotalMemSize                               ./elibrary/bin//libsyscall.a(syscall.o)
esMEMS_VA2PA                                      ./elibrary/bin//libsyscall.a(syscall.o)
esMEMS_VMCreate                                   ./elibrary/bin//libsyscall.a(syscall.o)
esMEMS_VMDelete                                   ./elibrary/bin//libsyscall.a(syscall.o)
esMEMS_VMalloc                                    ./elibrary/bin//libsyscall.a(syscall.o)
esMEMS_VMfree                                     ./elibrary/bin//libsyscall.a(syscall.o)
esMEM_BWDisable                                   ./elibrary/bin//libsyscall.a(syscall.o)
esMEM_BWEnable                                    ./elibrary/bin//libsyscall.a(syscall.o)
esMEM_BWGet                                       ./elibrary/bin//libsyscall.a(syscall.o)
esMEM_DramSuspend                                 ./elibrary/bin//libsyscall.a(syscall.o)
esMEM_DramWakeup                                  ./elibrary/bin//libsyscall.a(syscall.o)
esMEM_MasterGet                                   ./elibrary/bin//libsyscall.a(syscall.o)
esMEM_MasterSet                                   ./elibrary/bin//libsyscall.a(syscall.o)
esMEM_RegDramAccess                               ./elibrary/bin//libsyscall.a(syscall.o)
esMEM_ReleaseDramUsrMode                          ./elibrary/bin//libsyscall.a(syscall.o)
esMEM_RequestDramUsrMode                          ./elibrary/bin//libsyscall.a(syscall.o)
esMEM_SramRelBlk                                  ./elibrary/bin//libsyscall.a(syscall.o)
esMEM_SramReqBlk                                  ./elibrary/bin//libsyscall.a(syscall.o)
esMEM_SramSwitchBlk                               ./elibrary/bin//libsyscall.a(syscall.o)
esMEM_UnRegDramAccess                             ./elibrary/bin//libsyscall.a(syscall.o)
esMODS_MClose                                     ./elibrary/bin//libsyscall.a(syscall.o)
esMODS_MInstall                                   ./elibrary/bin//libsyscall.a(syscall.o)
esMODS_MIoctrl                                    ./elibrary/bin//libsyscall.a(syscall.o)
esMODS_MOpen                                      ./elibrary/bin//libsyscall.a(syscall.o)
esMODS_MRead                                      ./elibrary/bin//libsyscall.a(syscall.o)
esMODS_MUninstall                                 ./elibrary/bin//libsyscall.a(syscall.o)
esMODS_MWrite                                     ./elibrary/bin//libsyscall.a(syscall.o)
esMSTUB_GetFuncEntry                              ./elibrary/bin//libsyscall.a(syscall.o)
esMSTUB_RegFuncTbl                                ./elibrary/bin//libsyscall.a(syscall.o)
esMSTUB_UnRegFuncTbl                              ./elibrary/bin//libsyscall.a(syscall.o)
esPINS_ClearPending                               ./elibrary/bin//libsyscall.a(syscall.o)
esPINS_DisbaleInt                                 ./elibrary/bin//libsyscall.a(syscall.o)
esPINS_EnbaleInt                                  ./elibrary/bin//libsyscall.a(syscall.o)
esPINS_GetPinGrpStat                              ./elibrary/bin//libsyscall.a(syscall.o)
esPINS_GetPinStat                                 ./elibrary/bin//libsyscall.a(syscall.o)
esPINS_PinGrpRel                                  ./elibrary/bin//libsyscall.a(syscall.o)
esPINS_PinGrpReq                                  ./elibrary/bin//libsyscall.a(syscall.o)
esPINS_QueryInt                                   ./elibrary/bin//libsyscall.a(syscall.o)
esPINS_ReadPinData                                ./elibrary/bin//libsyscall.a(syscall.o)
esPINS_RegIntHdler                                ./elibrary/bin//libsyscall.a(syscall.o)
esPINS_SetIntMode                                 ./elibrary/bin//libsyscall.a(syscall.o)
esPINS_SetPinDrive                                ./elibrary/bin//libsyscall.a(syscall.o)
esPINS_SetPinIO                                   ./elibrary/bin//libsyscall.a(syscall.o)
esPINS_SetPinPull                                 ./elibrary/bin//libsyscall.a(syscall.o)
esPINS_SetPinStat                                 ./elibrary/bin//libsyscall.a(syscall.o)
esPINS_UnregIntHdler                              ./elibrary/bin//libsyscall.a(syscall.o)
esPINS_WritePinData                               ./elibrary/bin//libsyscall.a(syscall.o)
esPWRMAN_EnterStandby                             ./elibrary/bin//libsyscall.a(syscall.o)
esPWRMAN_GetStandbyPara                           ./elibrary/bin//libsyscall.a(syscall.o)
esPWRMAN_LockCpuFreq                              ./elibrary/bin//libsyscall.a(syscall.o)
esPWRMAN_RegDevice                                ./elibrary/bin//libsyscall.a(syscall.o)
esPWRMAN_RelPwrmanMode                            ./elibrary/bin//libsyscall.a(syscall.o)
esPWRMAN_ReqPwrmanMode                            ./elibrary/bin//libsyscall.a(syscall.o)
esPWRMAN_SetStandbyMode                           ./elibrary/bin//libsyscall.a(syscall.o)
esPWRMAN_UnlockCpuFreq                            ./elibrary/bin//libsyscall.a(syscall.o)
esPWRMAN_UnregDevice                              ./elibrary/bin//libsyscall.a(syscall.o)
esPWRMAN_UsrEventNotify                           ./elibrary/bin//libsyscall.a(syscall.o)
esRESM_RClose                                     ./elibrary/bin//libsyscall.a(syscall.o)
esRESM_ROpen                                      ./elibrary/bin//libsyscall.a(syscall.o)
esRESM_RRead                                      ./elibrary/bin//libsyscall.a(syscall.o)
esRESM_RSeek                                      ./elibrary/bin//libsyscall.a(syscall.o)
esSIOS_getchar                                    ./elibrary/bin//libsyscall.a(syscall.o)
esSIOS_gets                                       ./elibrary/bin//libsyscall.a(syscall.o)
esSIOS_putarg                                     ./elibrary/bin//libsyscall.a(syscall.o)
esSIOS_putstr                                     ./elibrary/bin//libsyscall.a(syscall.o)
                                                  ./elibrary/bin//libminic.a(elibs_stdio.o)
esSIOS_setbaud                                    ./elibrary/bin//libsyscall.a(syscall.o)
esSVC_RegCloseNode                                ./elibrary/bin//libsyscall.a(syscall.o)
esSVC_RegClrError                                 ./elibrary/bin//libsyscall.a(syscall.o)
esSVC_RegCreateKey                                ./elibrary/bin//libsyscall.a(syscall.o)
esSVC_RegCreatePath                               ./elibrary/bin//libsyscall.a(syscall.o)
esSVC_RegCreateSet                                ./elibrary/bin//libsyscall.a(syscall.o)
esSVC_RegDeleteKey                                ./elibrary/bin//libsyscall.a(syscall.o)
esSVC_RegDeleteNode                               ./elibrary/bin//libsyscall.a(syscall.o)
esSVC_RegDeletePath                               ./elibrary/bin//libsyscall.a(syscall.o)
esSVC_RegDeleteSet                                ./elibrary/bin//libsyscall.a(syscall.o)
esSVC_RegGetError                                 ./elibrary/bin//libsyscall.a(syscall.o)
esSVC_RegGetFirstKey                              ./elibrary/bin//libsyscall.a(syscall.o)
esSVC_RegGetFirstSet                              ./elibrary/bin//libsyscall.a(syscall.o)
esSVC_RegGetKeyCount                              ./elibrary/bin//libsyscall.a(syscall.o)
esSVC_RegGetKeyValue                              ./elibrary/bin//libsyscall.a(syscall.o)
esSVC_RegGetNextKey                               ./elibrary/bin//libsyscall.a(syscall.o)
esSVC_RegGetNextSet                               ./elibrary/bin//libsyscall.a(syscall.o)
esSVC_RegGetRootPath                              ./elibrary/bin//libsyscall.a(syscall.o)
esSVC_RegGetSetCount                              ./elibrary/bin//libsyscall.a(syscall.o)
esSVC_RegIni2Reg                                  ./elibrary/bin//libsyscall.a(syscall.o)
esSVC_RegOpenNode                                 ./elibrary/bin//libsyscall.a(syscall.o)
esSVC_RegReg2Ini                                  ./elibrary/bin//libsyscall.a(syscall.o)
esSVC_RegSetKeyValue                              ./elibrary/bin//libsyscall.a(syscall.o)
esSVC_RegSetRootPath                              ./elibrary/bin//libsyscall.a(syscall.o)
esSVC_ResourceRel                                 ./elibrary/bin//libsyscall.a(syscall.o)
esSVC_ResourceReq                                 ./elibrary/bin//libsyscall.a(syscall.o)
esTIME_ContiCntr                                  ./elibrary/bin//libsyscall.a(syscall.o)
esTIME_GetDate                                    ./elibrary/bin//libsyscall.a(syscall.o)
                                                  ./elibrary/bin//libminic.a(elibs_stdio.o)
esTIME_GetTime                                    ./elibrary/bin//libsyscall.a(syscall.o)
                                                  ./elibrary/bin//libminic.a(elibs_stdio.o)
esTIME_PauseCntr                                  ./elibrary/bin//libsyscall.a(syscall.o)
esTIME_QuerryAlarm                                ./elibrary/bin//libsyscall.a(syscall.o)
esTIME_QuerryCntr                                 ./elibrary/bin//libsyscall.a(syscall.o)
esTIME_QuerryCntrStat                             ./elibrary/bin//libsyscall.a(syscall.o)
esTIME_QuerryTimer                                ./elibrary/bin//libsyscall.a(syscall.o)
esTIME_ReleaseAlarm                               ./elibrary/bin//libsyscall.a(syscall.o)
esTIME_ReleaseCntr                                ./elibrary/bin//libsyscall.a(syscall.o)
esTIME_ReleaseTimer                               ./elibrary/bin//libsyscall.a(syscall.o)
esTIME_RequestAlarm                               ./elibrary/bin//libsyscall.a(syscall.o)
esTIME_RequestCntr                                ./elibrary/bin//libsyscall.a(syscall.o)
esTIME_RequestTimer                               ./elibrary/bin//libsyscall.a(syscall.o)
esTIME_SetCntrPrescale                            ./elibrary/bin//libsyscall.a(syscall.o)
esTIME_SetCntrValue                               ./elibrary/bin//libsyscall.a(syscall.o)
esTIME_SetDate                                    ./elibrary/bin//libsyscall.a(syscall.o)
esTIME_SetTime                                    ./elibrary/bin//libsyscall.a(syscall.o)
esTIME_StartAlarm                                 ./elibrary/bin//libsyscall.a(syscall.o)
esTIME_StartCntr                                  ./elibrary/bin//libsyscall.a(syscall.o)
esTIME_StartTimer                                 ./elibrary/bin//libsyscall.a(syscall.o)
esTIME_StopAlarm                                  ./elibrary/bin//libsyscall.a(syscall.o)
esTIME_StopCntr                                   ./elibrary/bin//libsyscall.a(syscall.o)
esTIME_StopTimer                                  ./elibrary/bin//libsyscall.a(syscall.o)
favoriteServicesShifted                           emodules/drv_dab/dab_module/si463x/DAB_Service_List_Handler.o
findEnsembleInList                                emodules/drv_dab/dab_module/si463x/DAB_Service_List_Handler.o
findListFocusInList                               emodules/drv_dab/dab_module/si463x/DAB_Service_List_Handler.o
findSameServiceInList                             emodules/drv_dab/dab_module/si463x/DAB_Service_List_Handler.o
                                                  emodules/drv_dab/dab_module/si463x/si468x.o
                                                  emodules/drv_dab/dab_module/si463x/Firmware_API_Manager.o
findServiceFreqByListIndex                        emodules/drv_dab/dab_module/si463x/DAB_Service_List_Handler.o
                                                  emodules/drv_dab/dab_module/si463x/si468x.o
findServiceInList                                 emodules/drv_dab/dab_module/si463x/DAB_Service_List_Handler.o
                                                  emodules/drv_dab/dab_module/si463x/si468x.o
                                                  emodules/drv_dab/dab_module/si463x/Firmware_API_Manager.o
findServiceInListEx                               emodules/drv_dab/dab_module/si463x/DAB_Service_List_Handler.o
findServiceInListExAndEid                         emodules/drv_dab/dab_module/si463x/DAB_Service_List_Handler.o
findServiceIndexInList                            emodules/drv_dab/dab_module/si463x/DAB_Service_List_Handler.o
                                                  emodules/drv_dab/dab_module/si463x/si468x.o
findServiceListByFreq                             emodules/drv_dab/dab_module/si463x/DAB_Service_List_Handler.o
                                                  emodules/drv_dab/dab_module/si463x/si468x.o
findService_In_Original_List                      emodules/drv_dab/dab_module/si463x/DAB_Service_List_Handler.o
                                                  emodules/drv_dab/dab_module/si463x/Firmware_API_Manager.o
fioctrl                                           ./elibrary/bin//libsyscall.a(syscall_fops.o)
firmwareGetFlashAddress                           emodules/drv_dab/dab_module/si463x/Firmware_Load_Helpers.o
                                                  emodules/drv_dab/dab_module/si463x/Firmware_API_Manager.o
firmwareGetSegment                                emodules/drv_dab/dab_module/si463x/Firmware_Load_Helpers.o
                                                  emodules/drv_dab/dab_module/si463x/Firmware_API_Manager.o
firmwareLoadOver                                  emodules/drv_dab/dab_module/si463x/Firmware_Load_Helpers.o
                                                  emodules/drv_dab/dab_module/si463x/Firmware_API_Manager.o
firmwareSetImage                                  emodules/drv_dab/dab_module/si463x/Firmware_Load_Helpers.o
                                                  emodules/drv_dab/dab_module/si463x/Firmware_API_Manager.o
firmware_len                                      emodules/drv_dab/dab_module/si463x/Firmware_Load_Helpers.o
firstInitDone                                     emodules/drv_dab/dab_module/si463x/RDS_Handler.o
flash_load__command                               emodules/drv_dab/dab_module/si463x/si46xx_firmware_api.o
                                                  emodules/drv_dab/dab_module/si463x/Firmware_API_Manager.o
fm_acf_status__command                            emodules/drv_dab/dab_module/si463x/si46xx_firmware_api.o
fm_acf_status__reply                              emodules/drv_dab/dab_module/si463x/si46xx_firmware_api.o
fm_am_first_preset_value                          emodules/drv_dab/dab_module/si463x/si468x.o
fm_am_func_set_are                                emodules/drv_dab/dab_module/si463x/si468x.o
fm_am_save_data                                   emodules/drv_dab/dab_module/si463x/si468x.o
fm_am_seek_start__command                         emodules/drv_dab/dab_module/si463x/si46xx_firmware_api.o
                                                  emodules/drv_dab/dab_module/si463x/Firmware_API_Manager.o
fm_am_select_preset                               emodules/drv_dab/dab_module/si463x/si468x.o
fm_am_set_area                                    emodules/drv_dab/dab_module/si463x/si468x.o
fm_am_set_parameter_default                       emodules/drv_dab/dab_module/si463x/si468x.o
fm_am_store_preset                                emodules/drv_dab/dab_module/si463x/si468x.o
fm_am_tune_freq__command                          emodules/drv_dab/dab_module/si463x/si46xx_firmware_api.o
                                                  emodules/drv_dab/dab_module/si463x/Firmware_API_Manager.o
fm_configure_properties_rds_on_off                emodules/drv_dab/dab_module/si463x/Firmware_API_Manager.o
                                                  emodules/drv_dab/dab_module/si463x/si468x.o
fm_freq                                           emodules/drv_dab/dab_module/si463x/si468x.o
fm_freq_step_size                                 emodules/drv_dab/dab_module/si463x/si468x.o
fm_initialize                                     emodules/drv_dab/dab_module/si463x/si468x.o
fm_para                                           emodules/drv_dab/dab_module/si463x/si468x.o
                                                  emodules/drv_dab/dab_module/si463x/RDS_Handler.o
fm_pset_table                                     emodules/drv_dab/dab_module/si463x/si468x.o
fm_rds_blockcount__command                        emodules/drv_dab/dab_module/si463x/si46xx_firmware_api.o
fm_rds_blockcount__reply                          emodules/drv_dab/dab_module/si463x/si46xx_firmware_api.o
fm_rds_status__command                            emodules/drv_dab/dab_module/si463x/si46xx_firmware_api.o
                                                  emodules/drv_dab/dab_module/si463x/si468x.o
                                                  emodules/drv_dab/dab_module/si463x/Firmware_API_Manager.o
fm_rds_status__reply                              emodules/drv_dab/dab_module/si463x/si46xx_firmware_api.o
                                                  emodules/drv_dab/dab_module/si463x/si468x.o
                                                  emodules/drv_dab/dab_module/si463x/Firmware_API_Manager.o
fm_rsq_status__command                            emodules/drv_dab/dab_module/si463x/si46xx_firmware_api.o
                                                  emodules/drv_dab/dab_module/si463x/Firmware_API_Manager.o
fm_rsq_status__reply                              emodules/drv_dab/dab_module/si463x/si46xx_firmware_api.o
                                                  emodules/drv_dab/dab_module/si463x/Firmware_API_Manager.o
fm_status                                         emodules/drv_dab/dab_module/si463x/si468x.o
                                                  emodules/drv_dab/dab_module/si463x/RDS_Handler.o
                                                  emodules/drv_dab/dab_module/si463x/rds.o
fmhd_ber_mode                                     emodules/drv_dab/dab_module/si463x/si468x.o
fmhd_freq                                         emodules/drv_dab/dab_module/si463x/si468x.o
fmhd_freq_step_size                               emodules/drv_dab/dab_module/si463x/si468x.o
fmhd_get_alert_message__command                   emodules/drv_dab/dab_module/si463x/si46xx_firmware_api.o
                                                  emodules/drv_dab/dab_module/si463x/Firmware_API_Manager.o
fmhd_get_alert_message__reply                     emodules/drv_dab/dab_module/si463x/si46xx_firmware_api.o
                                                  emodules/drv_dab/dab_module/si463x/Firmware_API_Manager.o
fmhd_get_enabled_ports__command                   emodules/drv_dab/dab_module/si463x/si46xx_firmware_api.o
fmhd_get_enabled_ports__reply                     emodules/drv_dab/dab_module/si463x/si46xx_firmware_api.o
fmhd_get_psd_decode__command                      emodules/drv_dab/dab_module/si463x/si46xx_firmware_api.o
                                                  emodules/drv_dab/dab_module/si463x/Firmware_API_Manager.o
fmhd_get_psd_decode__reply                        emodules/drv_dab/dab_module/si463x/si46xx_firmware_api.o
                                                  emodules/drv_dab/dab_module/si463x/Firmware_API_Manager.o
fmhd_get_station_info__command                    emodules/drv_dab/dab_module/si463x/si46xx_firmware_api.o
                                                  emodules/drv_dab/dab_module/si463x/Firmware_API_Manager.o
fmhd_get_station_info__info_select_sent           emodules/drv_dab/dab_module/si463x/si46xx_firmware_api.o
fmhd_get_station_info__reply                      emodules/drv_dab/dab_module/si463x/si46xx_firmware_api.o
                                                  emodules/drv_dab/dab_module/si463x/Firmware_API_Manager.o
fmhd_play_alert_tone__command                     emodules/drv_dab/dab_module/si463x/si46xx_firmware_api.o
                                                  emodules/drv_dab/dab_module/si463x/FMHD_Alert_Handler.o
fmhd_pset_table                                   emodules/drv_dab/dab_module/si463x/si468x.o
fmhd_pset_table_default                           emodules/drv_dab/dab_module/si463x/si468x.o
fmhd_seekonly                                     emodules/drv_dab/dab_module/si463x/si468x.o
fmhd_set_enabled_ports__command                   emodules/drv_dab/dab_module/si463x/si46xx_firmware_api.o
fmhd_split_mode_setting                           emodules/drv_dab/dab_module/si463x/si468x.o
fmhd_test_get_ber_info__command                   emodules/drv_dab/dab_module/si463x/si46xx_firmware_api.o
                                                  emodules/drv_dab/dab_module/si463x/Firmware_API_Manager.o
fmhd_test_get_ber_info__reply                     emodules/drv_dab/dab_module/si463x/si46xx_firmware_api.o
                                                  emodules/drv_dab/dab_module/si463x/Firmware_API_Manager.o
freqStack_Empty                                   emodules/drv_dab/dab_module/si463x/freq_stack.o
freqStack_init                                    emodules/drv_dab/dab_module/si463x/freq_stack.o
freq_list                                         emodules/drv_dab/dab_module/si463x/Firmware_API_Manager.o
                                                  emodules/drv_dab/dab_module/si463x/si468x.o
fsync                                             ./elibrary/bin//libsyscall.a(syscall_fops.o)
ftruncate                                         ./elibrary/bin//libsyscall.a(syscall_fops.o)
gThreshold_RSSI                                   emodules/drv_dab/dab_module/si463x/si468x.o
gThreshold_SNR                                    emodules/drv_dab/dab_module/si463x/si468x.o
g_SpiRdBuf                                        emodules/drv_dab/dab_module/si463x/cmmbSpi.o
g_SpiWtBuf                                        emodules/drv_dab/dab_module/si463x/cmmbSpi.o
g_spiFile                                         emodules/drv_dab/dab_module/si463x/cmmbSpi.o
g_spiMutex                                        emodules/drv_dab/dab_module/si463x/cmmbSpi.o
g_write_lock                                      emodules/drv_dab/dab_module/si463x/cmmbSpi.o
genreDisplay                                      emodules/drv_dab/dab_module/si463x/FMHD_PSD_Handler.o
getLabelAbbrevMaskByServiceIndex                  emodules/drv_dab/dab_module/si463x/Firmware_API_Manager.o
getServiceListElement                             emodules/drv_dab/dab_module/si463x/DAB_Service_List_Handler.o
                                                  emodules/drv_dab/dab_module/si463x/si468x.o
                                                  emodules/drv_dab/dab_module/si463x/Firmware_API_Manager.o
get_cur_freq_from_serviceIndex                    emodules/drv_dab/dab_module/si463x/Firmware_API_Manager.o
get_dab_audio_output_channel                      emodules/drv_dab/dab_module/si463x/si468x.o
                                                  emodules/drv_dab/dev_dab.o
get_dab_display_handle                            emodules/drv_dab/dab_module/si463x/si468x.o
                                                  emodules/drv_dab/dev_dab.o
get_dab_fm_rds_hard_link_flag                     emodules/drv_dab/dab_module/si463x/si468x.o
get_dab_is_at_annnoucement_mode                   emodules/drv_dab/dab_module/si463x/si468x.o
                                                  emodules/drv_dab/dev_dab.o
get_dab_lock                                      emodules/drv_dab/dab_module/si463x/si468x.o
get_digital_service_data__command                 emodules/drv_dab/dab_module/si463x/si46xx_firmware_api.o
                                                  emodules/drv_dab/dab_module/si463x/Firmware_API_Manager.o
get_digital_service_data__reply                   emodules/drv_dab/dab_module/si463x/si46xx_firmware_api.o
                                                  emodules/drv_dab/dab_module/si463x/Firmware_API_Manager.o
get_digital_service_list__command                 emodules/drv_dab/dab_module/si463x/si46xx_firmware_api.o
                                                  emodules/drv_dab/dab_module/si463x/Firmware_API_Manager.o
                                                  emodules/drv_dab/dab_module/si463x/DAB_Service_List_Handler.o
get_digital_service_list__reply                   emodules/drv_dab/dab_module/si463x/si46xx_firmware_api.o
                                                  emodules/drv_dab/dab_module/si463x/Firmware_API_Manager.o
                                                  emodules/drv_dab/dab_module/si463x/DAB_Service_List_Handler.o
get_func_info__command                            emodules/drv_dab/dab_module/si463x/si46xx_firmware_api.o
                                                  emodules/drv_dab/dab_module/si463x/Firmware_API_Manager.o
get_func_info__reply                              emodules/drv_dab/dab_module/si463x/si46xx_firmware_api.o
                                                  emodules/drv_dab/dab_module/si463x/Firmware_API_Manager.o
get_hard_rds_linkset                              emodules/drv_dab/dab_module/si463x/Firmware_API_Manager.o
                                                  emodules/drv_dab/dab_module/si463x/si468x.o
get_mot_sls_image                                 emodules/drv_dab/dab_module/si463x/mot.o
get_on_dab_view_app_flg                           emodules/drv_dab/dab_module/si463x/si468x.o
get_on_dab_view_channel_list_flg                  emodules/drv_dab/dab_module/si463x/si468x.o
                                                  emodules/drv_dab/dab_module/si463x/DAB_Service_List_Handler.o
                                                  emodules/drv_dab/dev_dab.o
get_part_info__command                            emodules/drv_dab/dab_module/si463x/si46xx_firmware_api.o
                                                  emodules/drv_dab/dab_module/si463x/Firmware_API_Manager.o
get_part_info__reply                              emodules/drv_dab/dab_module/si463x/si46xx_firmware_api.o
                                                  emodules/drv_dab/dab_module/si463x/Firmware_API_Manager.o
get_power_up_args__command                        emodules/drv_dab/dab_module/si463x/si46xx_firmware_api.o
get_power_up_args__reply                          emodules/drv_dab/dab_module/si463x/si46xx_firmware_api.o
get_property__command                             emodules/drv_dab/dab_module/si463x/si46xx_firmware_api.o
                                                  emodules/drv_dab/dab_module/si463x/Firmware_API_Manager.o
get_property__reply                               emodules/drv_dab/dab_module/si463x/si46xx_firmware_api.o
                                                  emodules/drv_dab/dab_module/si463x/Firmware_API_Manager.o
get_scan_process_rate                             emodules/drv_dab/dab_module/si463x/si468x.o
                                                  emodules/drv_dab/dev_dab.o
get_scids_In_Offset_List_by_sid_scid              emodules/drv_dab/dab_module/si463x/DAB_Service_List_Handler.o
                                                  emodules/drv_dab/dab_module/si463x/si468x.o
get_sys_state__command                            emodules/drv_dab/dab_module/si463x/si46xx_firmware_api.o
                                                  emodules/drv_dab/dab_module/si463x/Firmware_API_Manager.o
get_sys_state__reply                              emodules/drv_dab/dab_module/si463x/si46xx_firmware_api.o
                                                  emodules/drv_dab/dab_module/si463x/Firmware_API_Manager.o
hal_dab_control                                   emodules/drv_dab/dev_dab.o
hal_dab_init                                      emodules/drv_dab/dev_dab.o
hal_dab_uninit                                    emodules/drv_dab/dev_dab.o
handle_msc_datagroup                              emodules/drv_dab/dab_module/si463x/mot.o
                                                  emodules/drv_dab/dab_module/si463x/Firmware_API_Manager.o
hd_digrad_status__command                         emodules/drv_dab/dab_module/si463x/si46xx_firmware_api.o
                                                  emodules/drv_dab/dab_module/si463x/Firmware_API_Manager.o
hd_digrad_status__reply                           emodules/drv_dab/dab_module/si463x/si46xx_firmware_api.o
                                                  emodules/drv_dab/dab_module/si463x/Firmware_API_Manager.o
hd_get_event_status__command                      emodules/drv_dab/dab_module/si463x/si46xx_firmware_api.o
                                                  emodules/drv_dab/dab_module/si463x/Firmware_API_Manager.o
hd_get_event_status__reply                        emodules/drv_dab/dab_module/si463x/si46xx_firmware_api.o
                                                  emodules/drv_dab/dab_module/si463x/Firmware_API_Manager.o
host_load__command                                emodules/drv_dab/dab_module/si463x/si46xx_firmware_api.o
                                                  emodules/drv_dab/dab_module/si463x/Firmware_API_Manager.o
ignoreAB                                          emodules/drv_dab/dab_module/si463x/RDS_Handler.o
initAlerts                                        emodules/drv_dab/dab_module/si463x/FMHD_Alert_Handler.o
                                                  emodules/drv_dab/dab_module/si463x/Firmware_API_Manager.o
initDAB_ServiceList                               emodules/drv_dab/dab_module/si463x/DAB_Service_List_Handler.o
                                                  emodules/drv_dab/dab_module/si463x/si468x.o
                                                  emodules/drv_dab/dab_module/si463x/Firmware_API_Manager.o
initDLS                                           emodules/drv_dab/dab_module/si463x/DAB_DLS_Handler.o
                                                  emodules/drv_dab/dab_module/si463x/Firmware_API_Manager.o
initHD_ServiceList                                emodules/drv_dab/dab_module/si463x/FMHD_Service_List_Handler.o
                                                  emodules/drv_dab/dab_module/si463x/Firmware_API_Manager.o
initHardware                                      emodules/drv_dab/dab_module/si463x/HAL.o
                                                  emodules/drv_dab/dab_module/si463x/Firmware_API_Manager.o
initPSD                                           emodules/drv_dab/dab_module/si463x/FMHD_PSD_Handler.o
                                                  emodules/drv_dab/dab_module/si463x/Firmware_API_Manager.o
initRDS                                           emodules/drv_dab/dab_module/si463x/RDS_Handler.o
                                                  emodules/drv_dab/dab_module/si463x/rds.o
                                                  emodules/drv_dab/dab_module/si463x/Firmware_API_Manager.o
initSIS                                           emodules/drv_dab/dab_module/si463x/FMHD_SIS_Handler.o
                                                  emodules/drv_dab/dab_module/si463x/Firmware_API_Manager.o
init_dab_announcement                             emodules/drv_dab/dab_module/si463x/Firmware_API_Manager.o
                                                  emodules/drv_dab/dab_module/si463x/si468x.o
into_preset_list                                  emodules/drv_dab/dab_module/si463x/si468x.o
ioctl                                             ./elibrary/bin//libsyscall.a(syscall_fops.o)
                                                  emodules/drv_dab/dab_module/si463x/si468x_bus.o
                                                  emodules/drv_dab/dab_module/si463x/si468x.o
                                                  emodules/drv_dab/dab_module/si463x/RDS_Handler.o
                                                  emodules/drv_dab/dab_module/si463x/HAL.o
                                                  emodules/drv_dab/dab_module/si463x/cmmbSpi.o
                                                  emodules/drv_dab/drv_dab.o
is_signal_strong_to_start_audio                   emodules/drv_dab/dab_module/si463x/Firmware_API_Manager.o
                                                  emodules/drv_dab/dab_module/si463x/si468x.o
k_RadioAutoStore                                  emodules/drv_dab/dab_module/si463x/si468x.o
lastCommand                                       emodules/drv_dab/dab_module/si463x/si46xx_firmware_api.o
lastSertype                                       emodules/drv_dab/dab_module/si463x/si46xx_firmware_api.o
load_init__command                                emodules/drv_dab/dab_module/si463x/si46xx_firmware_api.o
                                                  emodules/drv_dab/dab_module/si463x/Firmware_API_Manager.o
memcpy                                            /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/../../../../riscv64-unknown-elf/lib/rv64imafdcxtheadc/lp64d/libc.a(lib_a-memcpy.o)
                                                  emodules/drv_dab/dab_module/si463x/Firmware_API_Manager.o
                                                  emodules/drv_dab/dab_module/si463x/DAB_Service_List_Handler.o
memset                                            /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/../../../../riscv64-unknown-elf/lib/rv64imafdcxtheadc/lp64d/libc.a(lib_a-memset.o)
                                                  ./elibrary/bin//libminic.a(elibs_stdio.o)
                                                  emodules/drv_dab/dab_module/si463x/si468x.o
                                                  emodules/drv_dab/dab_module/si463x/RDS_Handler.o
                                                  emodules/drv_dab/dab_module/si463x/HAL.o
                                                  emodules/drv_dab/dab_module/si463x/Firmware_API_Manager.o
                                                  emodules/drv_dab/dab_module/si463x/DAB_Service_List_Handler.o
                                                  emodules/drv_dab/dab_module/si463x/DAB_DLS_Handler.o
mkdir                                             ./elibrary/bin//libsyscall.a(syscall_fops.o)
mmap                                              ./elibrary/bin//libsyscall.a(syscall.o)
modinfo                                           emodules/drv_dab/magic.o
mot_exit                                          emodules/drv_dab/dab_module/si463x/mot.o
                                                  emodules/drv_dab/dab_module/si463x/si468x.o
mot_init                                          emodules/drv_dab/dab_module/si463x/mot.o
                                                  emodules/drv_dab/dab_module/si463x/si468x.o
msg_queue                                         emodules/drv_dab/dab_module/si463x/si468x.o
munmap                                            ./elibrary/bin//libsyscall.a(syscall.o)
numBytesSent                                      emodules/drv_dab/dab_module/si463x/Firmware_Load_Helpers.o
open                                              /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/../../../../riscv64-unknown-elf/lib/rv64imafdcxtheadc/lp64d/libc.a(lib_a-sysopen.o)
                                                  emodules/drv_dab/dab_module/si463x/si468x_bus.o
                                                  emodules/drv_dab/dab_module/si463x/si468x.o
                                                  emodules/drv_dab/dab_module/si463x/RDS_Handler.o
                                                  emodules/drv_dab/dab_module/si463x/HAL.o
                                                  emodules/drv_dab/drv_dab.o
opendir                                           ./elibrary/bin//libsyscall.a(syscall_fops.o)
para_init                                         emodules/drv_dab/dab_module/si463x/si468x.o
patch_full_buff                                   emodules/drv_dab/dab_module/si463x/Firmware_Load_Helpers.o
piDisplay                                         emodules/drv_dab/dab_module/si463x/RDS_Handler.o
pipe                                              ./elibrary/bin//libsyscall.a(syscall.o)
play_announcement_bkinfo                          emodules/drv_dab/dab_module/si463x/si468x.o
poll                                              ./elibrary/bin//libsyscall.a(syscall.o)
pop_freqStack                                     emodules/drv_dab/dab_module/si463x/freq_stack.o
pop_msg_from_queue                                emodules/drv_dab/dab_module/si463x/si468x.o
powerDownHardware                                 emodules/drv_dab/dab_module/si463x/HAL.o
power_up__command                                 emodules/drv_dab/dab_module/si463x/si46xx_firmware_api.o
                                                  emodules/drv_dab/dab_module/si463x/Firmware_API_Manager.o
preset_num                                        emodules/drv_dab/dab_module/si463x/si468x.o
printf_cmdArg                                     emodules/drv_dab/dab_module/si463x/si46xx_firmware_api.o
printf_freq                                       emodules/drv_dab/dab_module/si463x/si46xx_firmware_api.o
printk                                            ./elibrary/bin//libminic.a(elibs_stdio.o)
progressRate                                      emodules/drv_dab/dab_module/si463x/Firmware_API_Manager.o
                                                  emodules/drv_dab/dab_module/si463x/si468x.o
psDisplay                                         emodules/drv_dab/dab_module/si463x/RDS_Handler.o
psd_update_ready_item                             emodules/drv_dab/dab_module/si463x/si468x.o
psetTuneFreq                                      emodules/drv_dab/dab_module/si463x/si468x.o
pset_table_index                                  emodules/drv_dab/dab_module/si463x/si468x.o
ptyDisplay                                        emodules/drv_dab/dab_module/si463x/RDS_Handler.o
                                                  emodules/drv_dab/dab_module/si463x/si468x.o
                                                  emodules/drv_dab/dab_module/si463x/rds.o
pty_serach_bkinfo                                 emodules/drv_dab/dab_module/si463x/si468x.o
push_msg_to_queue                                 emodules/drv_dab/dab_module/si463x/si468x.o
rdsBlerMax                                        emodules/drv_dab/dab_module/si463x/RDS_Handler.o
rdsIgnoreAB                                       emodules/drv_dab/dab_module/si463x/RDS_Handler.o
rds_update_ready_item                             emodules/drv_dab/dab_module/si463x/si468x.o
readReply                                         emodules/drv_dab/dab_module/si463x/HAL.o
                                                  emodules/drv_dab/dab_module/si463x/si46xx_firmware_api.o
read_offset__command                              emodules/drv_dab/dab_module/si463x/si46xx_firmware_api.o
read_status                                       emodules/drv_dab/dab_module/si463x/si46xx_firmware_api.o
                                                  emodules/drv_dab/dab_module/si463x/Firmware_API_Manager.o
read_storage__command                             emodules/drv_dab/dab_module/si463x/si46xx_firmware_api.o
read_storage__reply                               emodules/drv_dab/dab_module/si463x/si46xx_firmware_api.o
readdir                                           ./elibrary/bin//libsyscall.a(syscall_fops.o)
realign_service_list_buffer                       emodules/drv_dab/dab_module/si463x/si46xx_firmware_api.o
                                                  emodules/drv_dab/dab_module/si463x/FMHD_Service_List_Handler.o
                                                  emodules/drv_dab/dab_module/si463x/DAB_Service_List_Handler.o
remove                                            ./elibrary/bin//libsyscall.a(syscall_fops.o)
reply_buffer                                      emodules/drv_dab/dab_module/si463x/si46xx_firmware_api.o
reply_buffer_i16                                  emodules/drv_dab/dab_module/si463x/si46xx_firmware_api.o
reply_buffer_u16                                  emodules/drv_dab/dab_module/si463x/si46xx_firmware_api.o
reply_buffer_u32                                  emodules/drv_dab/dab_module/si463x/si46xx_firmware_api.o
rewinddir                                         ./elibrary/bin//libsyscall.a(syscall_fops.o)
rmdir                                             ./elibrary/bin//libsyscall.a(syscall_fops.o)
root_para                                         emodules/drv_dab/dab_module/si463x/si468x.o
                                                  emodules/drv_dab/dab_module/si463x/rds.o
rtDisplay                                         emodules/drv_dab/dab_module/si463x/RDS_Handler.o
rt_device_register                                ./elibrary/bin//libsyscall.a(syscall.o)
                                                  emodules/drv_dab/dev_dab.o
rt_device_unregister                              ./elibrary/bin//libsyscall.a(syscall.o)
                                                  emodules/drv_dab/dev_dab.o
seek_state                                        emodules/drv_dab/dab_module/si463x/si468x.o
select                                            ./elibrary/bin//libsyscall.a(syscall.o)
setServiceListElement                             emodules/drv_dab/dab_module/si463x/DAB_Service_List_Handler.o
                                                  emodules/drv_dab/dab_module/si463x/Firmware_API_Manager.o
set_dab_fm_rds_hard_link_flag                     emodules/drv_dab/dab_module/si463x/si468x.o
                                                  emodules/drv_dab/dev_dab.o
set_freq_spacing                                  emodules/drv_dab/dab_module/si463x/si468x.o
set_lookat_dab_announcement                       emodules/drv_dab/dab_module/si463x/Firmware_API_Manager.o
set_on_dab_view_app_flg                           emodules/drv_dab/dab_module/si463x/si468x.o
                                                  emodules/drv_dab/dev_dab.o
set_on_dab_view_channel_list_flg                  emodules/drv_dab/dab_module/si463x/si468x.o
                                                  emodules/drv_dab/dev_dab.o
set_property__command                             emodules/drv_dab/dab_module/si463x/si46xx_firmware_api.o
                                                  emodules/drv_dab/dab_module/si463x/Firmware_API_Manager.o
settimeofday                                      ./elibrary/bin//libsyscall.a(syscall_fops.o)
si4384_set_reset_io                               emodules/drv_dab/dab_module/si463x/HAL.o
                                                  emodules/drv_dab/dab_module/si463x/si468x_bus.o
si4634Preset                                      emodules/drv_dab/dab_module/si463x/si468x.o
si4634_OpLock                                     emodules/drv_dab/dab_module/si463x/si468x.o
si4634_OpUnlock                                   emodules/drv_dab/dab_module/si463x/si468x.o
si4634_am_exit                                    emodules/drv_dab/dab_module/si463x/si468x.o
si4634_am_init                                    emodules/drv_dab/dab_module/si463x/si468x.o
si4634_am_load_firmware_and_tune                  emodules/drv_dab/dab_module/si463x/si468x.o
                                                  emodules/drv_dab/dev_dab.o
si4634_dab_check_current_service_dab_freq_match   emodules/drv_dab/dab_module/si463x/si468x.o
si4634_dab_check_current_service_index_freq_match emodules/drv_dab/dab_module/si463x/si468x.o
si4634_dab_check_current_service_match            emodules/drv_dab/dab_module/si463x/si468x.o
si4634_dab_check_work_status                      emodules/drv_dab/dab_module/si463x/si468x.o
si4634_dab_get_list_focus                         emodules/drv_dab/dab_module/si463x/si468x.o
                                                  emodules/drv_dab/dev_dab.o
si4634_dab_mode_is_busy                           emodules/drv_dab/dab_module/si463x/si468x.o
si4634_dab_reset                                  emodules/drv_dab/dab_module/si463x/si468x.o
si4634_dab_try_play_old_service                   emodules/drv_dab/dab_module/si463x/si468x.o
si4634_fm_am_Key_Process                          emodules/drv_dab/dab_module/si463x/si468x.o
si4634_fm_exit                                    emodules/drv_dab/dab_module/si463x/si468x.o
si4634_fm_init                                    emodules/drv_dab/dab_module/si463x/si468x.o
si4634_fm_load_firmware_and_tune                  emodules/drv_dab/dab_module/si463x/si468x.o
                                                  emodules/drv_dab/dev_dab.o
si4634_lock                                       emodules/drv_dab/dab_module/si463x/si468x.o
si4634_lock_create                                emodules/drv_dab/dab_module/si463x/si468x.o
si4634_lock_delect                                emodules/drv_dab/dab_module/si463x/si468x.o
si4634_mode_init                                  emodules/drv_dab/dab_module/si463x/si468x.o
                                                  emodules/drv_dab/dev_dab.o
si4634_mode_uninit                                emodules/drv_dab/dab_module/si463x/si468x.o
                                                  emodules/drv_dab/dev_dab.o
si4634_send_data_to_mcu                           emodules/drv_dab/dab_module/si463x/si468x.o
                                                  emodules/drv_dab/dab_module/si463x/Firmware_API_Manager.o
si4634_set_search_stop                            emodules/drv_dab/dab_module/si463x/si468x.o
si463x_dab_check_current_service_valid            emodules/drv_dab/dab_module/si463x/si468x.o
si463x_set_local_seek_on_off                      emodules/drv_dab/dab_module/si463x/si468x.o
si463x_set_rds_af_on_off                          emodules/drv_dab/dab_module/si463x/si468x.o
si463x_set_rds_ct_on_off                          emodules/drv_dab/dab_module/si463x/si468x.o
si463x_set_rds_on_off                             emodules/drv_dab/dab_module/si463x/si468x.o
si463x_set_rds_ta_on_off                          emodules/drv_dab/dab_module/si463x/si468x.o
si463x_thread_init                                emodules/drv_dab/dab_module/si463x/si468x.o
si463x_thread_uninit                              emodules/drv_dab/dab_module/si463x/si468x.o
si468x_initHardware                               emodules/drv_dab/dab_module/si463x/si468x_bus.o
                                                  emodules/drv_dab/dab_module/si463x/HAL.o
si468x_readReply                                  emodules/drv_dab/dab_module/si463x/si468x_bus.o
                                                  emodules/drv_dab/dab_module/si463x/si468x.o
                                                  emodules/drv_dab/dab_module/si463x/HAL.o
                                                  emodules/drv_dab/dab_module/si463x/Firmware_API_Manager.o
si468x_reset_io                                   emodules/drv_dab/dab_module/si463x/si468x_bus.o
si468x_writeCommand                               emodules/drv_dab/dab_module/si463x/si468x_bus.o
                                                  emodules/drv_dab/dab_module/si463x/HAL.o
sis_update_ready_item                             emodules/drv_dab/dab_module/si463x/si468x.o
slgnDisplay                                       emodules/drv_dab/dab_module/si463x/FMHD_SIS_Handler.o
smDisplay                                         emodules/drv_dab/dab_module/si463x/FMHD_SIS_Handler.o
snlDisplay                                        emodules/drv_dab/dab_module/si463x/FMHD_SIS_Handler.o
snsDisplay                                        emodules/drv_dab/dab_module/si463x/FMHD_SIS_Handler.o
spi_exit                                          emodules/drv_dab/dab_module/si463x/cmmbSpi.o
spi_init                                          emodules/drv_dab/dab_module/si463x/cmmbSpi.o
spi_read                                          emodules/drv_dab/dab_module/si463x/cmmbSpi.o
spi_read_write                                    emodules/drv_dab/dab_module/si463x/cmmbSpi.o
spi_read_write_together                           emodules/drv_dab/dab_module/si463x/cmmbSpi.o
spi_write                                         emodules/drv_dab/dab_module/si463x/cmmbSpi.o
spi_write_lock                                    emodules/drv_dab/dab_module/si463x/cmmbSpi.o
spi_write_unlock                                  emodules/drv_dab/dab_module/si463x/cmmbSpi.o
start_digital_service__command                    emodules/drv_dab/dab_module/si463x/si46xx_firmware_api.o
                                                  emodules/drv_dab/dab_module/si463x/Firmware_API_Manager.o
statfs                                            ./elibrary/bin//libsyscall.a(syscall_fops.o)
stationID                                         emodules/drv_dab/dab_module/si463x/FMHD_SIS_Handler.o
stationLocationLat                                emodules/drv_dab/dab_module/si463x/FMHD_SIS_Handler.o
stationLocationLong                               emodules/drv_dab/dab_module/si463x/FMHD_SIS_Handler.o
stop_digital_service__command                     emodules/drv_dab/dab_module/si463x/si46xx_firmware_api.o
                                                  emodules/drv_dab/dab_module/si463x/Firmware_API_Manager.o
sunxi_driver_dab_init                             emodules/drv_dab/dev_dab.o
                                                  emodules/drv_dab/drv_dab.o
sunxi_driver_dab_uninit                           emodules/drv_dab/dev_dab.o
                                                  emodules/drv_dab/drv_dab.o
svcListDAB                                        emodules/drv_dab/dab_module/si463x/DAB_Service_List_Handler.o
                                                  emodules/drv_dab/dab_module/si463x/si468x.o
                                                  emodules/drv_dab/dab_module/si463x/Firmware_API_Manager.o
svcListFast                                       emodules/drv_dab/dab_module/si463x/FMHD_Service_List_Handler.o
                                                  emodules/drv_dab/dab_module/si463x/Firmware_API_Manager.o
svcListFull_Audio                                 emodules/drv_dab/dab_module/si463x/FMHD_Service_List_Handler.o
                                                  emodules/drv_dab/dab_module/si463x/Firmware_API_Manager.o
system_para                                       emodules/drv_dab/dab_module/si463x/si468x.o
                                                  emodules/drv_dab/dab_module/si463x/RDS_Handler.o
test_get_rssi__command                            emodules/drv_dab/dab_module/si463x/si46xx_firmware_api.o
                                                  emodules/drv_dab/dab_module/si463x/Firmware_API_Manager.o
test_get_rssi__reply                              emodules/drv_dab/dab_module/si463x/si46xx_firmware_api.o
                                                  emodules/drv_dab/dab_module/si463x/Firmware_API_Manager.o
titleDisplay                                      emodules/drv_dab/dab_module/si463x/FMHD_PSD_Handler.o
totalLength                                       emodules/drv_dab/dab_module/si463x/Firmware_Load_Helpers.o
tuneToSpecificPsetByNum                           emodules/drv_dab/dab_module/si463x/si468x.o
                                                  emodules/drv_dab/dev_dab.o
tuner_mode                                        emodules/drv_dab/drv_dab.o
uninitHardware                                    emodules/drv_dab/dab_module/si463x/HAL.o
                                                  emodules/drv_dab/dab_module/si463x/si468x.o
updateAlerts                                      emodules/drv_dab/dab_module/si463x/FMHD_Alert_Handler.o
                                                  emodules/drv_dab/dab_module/si463x/Firmware_API_Manager.o
updateDAB_ServiceList                             emodules/drv_dab/dab_module/si463x/DAB_Service_List_Handler.o
                                                  emodules/drv_dab/dab_module/si463x/Firmware_API_Manager.o
updateDLS                                         emodules/drv_dab/dab_module/si463x/DAB_DLS_Handler.o
                                                  emodules/drv_dab/dab_module/si463x/Firmware_API_Manager.o
updateFMHD_ServiceList_Audio                      emodules/drv_dab/dab_module/si463x/FMHD_Service_List_Handler.o
                                                  emodules/drv_dab/dab_module/si463x/Firmware_API_Manager.o
updateIdentifyByServiceIndex                      emodules/drv_dab/dab_module/si463x/Firmware_API_Manager.o
updateItemByCase                                  emodules/drv_dab/dab_module/si463x/si468x.o
updatePSD                                         emodules/drv_dab/dab_module/si463x/FMHD_PSD_Handler.o
                                                  emodules/drv_dab/dab_module/si463x/Firmware_API_Manager.o
updateRDS                                         emodules/drv_dab/dab_module/si463x/RDS_Handler.o
                                                  emodules/drv_dab/dab_module/si463x/Firmware_API_Manager.o
updateSIS                                         emodules/drv_dab/dab_module/si463x/FMHD_SIS_Handler.o
                                                  emodules/drv_dab/dab_module/si463x/Firmware_API_Manager.o
update_display_info                               emodules/drv_dab/dab_module/si463x/si468x.o
update_pi                                         emodules/drv_dab/dab_module/si463x/rds.o
                                                  emodules/drv_dab/dab_module/si463x/si468x.o
                                                  emodules/drv_dab/dab_module/si463x/RDS_Handler.o
update_service_data                               emodules/drv_dab/dab_module/si463x/si468x.o
usnDisplay                                        emodules/drv_dab/dab_module/si463x/FMHD_SIS_Handler.o
volume                                            emodules/drv_dab/dab_module/si463x/si468x.o
                                                  emodules/drv_dab/dab_module/si463x/Firmware_API_Manager.o
volume_work_mode                                  emodules/drv_dab/dab_module/si463x/si468x.o
wait_af_seek_now_pty                              emodules/drv_dab/dab_module/si463x/si468x.o
wait_af_seek_rds_pi                               emodules/drv_dab/dab_module/si463x/si468x.o
wait_af_seek_rds_sysnc_int                        emodules/drv_dab/dab_module/si463x/si468x.o
wait_for_CTS                                      emodules/drv_dab/dab_module/si463x/HAL.o
work_mode_index                                   emodules/drv_dab/dab_module/si463x/si468x.o
writeCommand                                      emodules/drv_dab/dab_module/si463x/HAL.o
                                                  emodules/drv_dab/dab_module/si463x/si46xx_firmware_api.o
                                                  emodules/drv_dab/dab_module/si463x/Firmware_API_Manager.o
write_storage__command                            emodules/drv_dab/dab_module/si463x/si46xx_firmware_api.o
