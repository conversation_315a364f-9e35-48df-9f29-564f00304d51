# USB Category Handler - MCU UART Protocol v2.0

## Overview

This USB Category Handler implements the USB communication protocol for the MCU UART system, supporting both USB DISK (Category 0x20) and USB iPod (Category 0x21) devices. The implementation follows the HOST-MCU architecture where this codebase serves as the HOST end.

## Architecture

### HOST vs MCU Roles

- **HOST End (This Codebase)**: Responsible for display, key/touch input, and menu management
- **MCU End (External MCU)**: Handles low-level device communication and some processing

### USB Category Distinction

#### USB DISK (Category 0x20) - HOST Control Mode
- **Control**: HOST端(本代码库)解码播放
- **Data Flow**: HOST → MCU (状态推送用于MCU转发给其他设备)
- **Operations**: Primarily SET operations to push status updates
- **Responsibility**: HOST handles all decoding, playback, and file management

#### USB iPod (Category 0x21) - MCU Control Mode  
- **Control**: 外部MCU端解码播放
- **Data Flow**: Bidirectional (HOST ↔ MCU)
- **Operations**: CONTROL/GET/EVENT/ACK for full interaction
- **Responsibility**: MCU handles decoding/playback, H<PERSON><PERSON> handles UI and user input

## Protocol Implementation

### Data Type Bitmaps (32-bit)

Both USB categories use 32-bit data type bitmaps to specify which data types are being transmitted:

#### Common Data Types (Both Categories)
- **Bit 0**: Media type (0=USB DISK, 1=USB iPod)
- **Bit 1-3**: Status information (connection, reading, device ID)
- **Bit 4-10**: Playback information (track, time, ID3 metadata)
- **Bit 11-13**: Playback state (play/pause, repeat, shuffle)
- **Bit 14-19**: Playback controls (next, prev, FF, REW, seek, track select)
- **Bit 20-23**: Browser functionality (mode, list, pagination, items)
- **Bit 24-31**: Reserved for future expansion

### Operation Types

- **GET (0x01)**: Query current status (HOST → MCU)
- **SET (0x02)**: Set configuration or push status (HOST → MCU for iPod controls, HOST → MCU for DISK status)
- **CONTROL (0x03)**: Execute control commands (HOST → MCU for iPod)
- **EVENT (0x04)**: Status updates or GET responses (MCU → HOST)
- **ACK (0x05)**: Acknowledgment responses (MCU → HOST)

## File Structure

```
usb/
├── mcu_usb_category_handler.h     # Header with data types and function declarations
├── mcu_usb_category_handler.c     # Implementation with main handlers
├── Makefile                       # Build configuration
└── README.md                      # This documentation
```

## Key Functions

### Main Handlers
- `usb_disk_main_handler()`: Processes USB DISK category commands
- `usb_ipod_main_handler()`: Processes USB iPod category commands

### Registration Functions
- `mcu_usb_disk_category_handler_register()`: Registers USB DISK handler
- `mcu_usb_ipod_category_handler_register()`: Registers USB iPod handler

### State Management
- `usb_disk_get_state()`: Get current USB DISK state
- `usb_ipod_get_state()`: Get current USB iPod state
- `usb_handler_init()`: Initialize USB handlers
- `usb_handler_deinit()`: Cleanup USB handlers

### HOST-side Operations (Internal APIs)
- `usb_disk_push_status_to_mcu()`: Push DISK status to MCU
- `usb_ipod_send_control_to_mcu()`: Send iPod control commands
- `usb_ipod_query_status_from_mcu()`: Query iPod status from MCU

## Data Structures

### Core State Structure
```c
typedef struct {
    __u8 category;                      // 0x20 (USB_DISK) or 0x21 (USB_IPOD)
    usb_connection_status_t connection; // Connection status
    usb_reading_status_t reading_status; // Reading status
    usb_device_info_t device_info;      // Device information
    usb_track_info_t track_info;        // Track information
    usb_play_state_t play_state;        // Playback state
    usb_play_time_t play_time;          // Playback time
    usb_repeat_mode_t repeat_mode;      // Repeat mode
    usb_shuffle_mode_t shuffle_mode;    // Shuffle mode
    usb_id3_metadata_t current_id3;     // Current ID3 info
    usb_browser_list_t browser_list;    // Browser list state
    __u8 browser_mode;                  // Current browser mode
    __bool album_art_available;        // Album art availability
} usb_handler_state_t;
```

### ID3 Metadata Structure
```c
typedef struct {
    __u32 track_index;          // Track index
    __u8  encoding_type;        // Encoding type: 0=UTF-8, 1=UTF-16, 2=GBK
    __u8  field_bitmap;         // Field bitmap: bit0=title, bit1=artist, bit2=album, bit3=genre
    __u8  reserved[2];          // Reserved
    char  title[USB_ID3_TITLE_MAX_LEN];   // Title
    char  artist[USB_ID3_ARTIST_MAX_LEN]; // Artist
    char  album[USB_ID3_ALBUM_MAX_LEN];   // Album
    char  genre[USB_ID3_GENRE_MAX_LEN];   // Genre
    char  year[8];              // Year
    __u8  track_number;         // Track number
    __u8  total_tracks;         // Total tracks
} usb_id3_metadata_t;
```

## Communication Examples

### USB DISK Status Push (HOST → MCU)
```c
// Push current track info to MCU
Command: 0xF100 20 02 00000010 0008 [0001 0064 12345678]
// Category=0x20, Operation=SET, DataType=TRACK_INFO
// Data: current_track=1, total_tracks=100, track_index=0x12345678
Response: 0xF100 20 05 00000010 0000  // ACK confirmation
```

### USB iPod Control (HOST → MCU)
```c
// Send next track command to MCU
Command: 0xF100 21 03 00004000 0000
// Category=0x21, Operation=CONTROL, DataType=TRACK_NEXT
Response: 0xF100 21 05 00004000 0000  // ACK confirmation
```

### USB iPod Status Update (MCU → HOST)
```c
// MCU reports playback status change
Event: 0xF100 21 04 00000800 0001 [01]
// Category=0x21, Operation=EVENT, DataType=PLAY_STATE
// Data: play_state=1 (playing)
```

## Browser System

### USB DISK Browser Modes
- **FOLDER**: Browse by file system folders
- **TITLE**: Browse by track titles
- **ARTIST**: Browse by artist names
- **ALBUM**: Browse by album names
- **GENRE**: Browse by music genres
- **YEAR**: Browse by release year

### USB iPod Browser Modes
- **PLAYLIST**: Browse playlists
- **ARTIST**: Browse by artists
- **ALBUM**: Browse by albums
- **GENRE**: Browse by genres
- **TRACK**: Browse individual tracks
- **COMPOSER**: Browse by composers
- **PODCAST**: Browse podcasts
- **AUDIOBOOK**: Browse audiobooks

## Error Handling

The implementation includes comprehensive error handling:
- Parameter validation for all input data
- Operation type validation (HOST should only receive EVENT/ACK)
- Data length verification
- State consistency checks
- Statistical error counting

## Debugging

Debug output can be enabled via the `USB_HANDLER_DEBUG` macro:
```c
#define USB_HANDLER_DEBUG 1
```

Debug messages include:
- Command processing flow
- Data type parsing
- State updates
- Error conditions
- Statistical information

## Integration

### Registration
The USB handlers must be registered with the main protocol dispatcher:
```c
// Register both USB category handlers
mcu_usb_disk_category_handler_register();
mcu_usb_ipod_category_handler_register();
```

### Initialization
```c
// Initialize USB handler subsystem
usb_handler_init();
```

### Usage in Application Code
```c
// Get current USB DISK state
usb_handler_state_t disk_state;
usb_disk_get_state(&disk_state);

// Push status update for USB DISK
usb_disk_push_status_to_mcu(USB_DISK_DATA_PLAY_STATE | USB_DISK_DATA_TRACK_INFO);

// Send control command for USB iPod
usb_ipod_send_control_to_mcu(USB_IPOD_DATA_TRACK_NEXT, NULL, 0);
```

## Protocol Compliance

This implementation follows:
- MCU UART Protocol v2.0 specification
- USB Protocol Table v2.0 data type definitions
- HOST-MCU communication architecture
- 32-bit data type bitmap design
- Unified command handler pattern

## Future Extensions

The design supports future enhancements:
- Additional USB device types (Android, MTP)
- Extended metadata formats
- Enhanced browser capabilities
- Album art transmission
- Playlist management
- Advanced search functionality

---

*This implementation provides a complete, robust USB category handler that correctly implements the HOST-MCU communication protocol while maintaining clear separation of responsibilities between USB DISK and USB iPod operational modes.*