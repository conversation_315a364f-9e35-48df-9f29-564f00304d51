# 设置分类数据类型位图设计文档

## 介绍

本文档基于用户界面显示的设置功能，为MCU UART协议v2中的设置分类（CATEGORY_SETTINGS = 0x03）设计32位数据类型位图。设计排除了已在系统分类（CATEGORY_SYSTEM = 0x01）中实现的功能：电压、主题、亮度、语言和系统信息。

## 架构设计

### 设计原则

1. **功能分离**: 避免与系统分类重复，专注于用户配置和设备管理
2. **位图效率**: 32位位图支持灵活的数据类型组合
3. **分层组织**: 按功能域逻辑分组相关设置
4. **扩展性**: 预留位用于未来功能扩展
5. **协议兼容**: 遵循New Protocol v2的0xF100架构

### 功能域划分

基于用户界面分析，设置分类包含以下主要功能域：

1. **通用设置域** (Bit 0-7): 重命名、AUX IN、收音机区域、蜂鸣器
2. **信源管理域** (Bit 8-11): 信源顺序、工厂默认设置
3. **蓝牙设置域** (Bit 12-19): 蓝牙开关、设备名称、设备列表、配对管理
4. **网络设置域** (Bit 20-27): 设备Zone配置、远程设备管理
5. **系统维护域** (Bit 28-31): 固件更新、OTA更新

## 组件和接口

### 32位数据类型位图设计

```c
typedef enum {
    /* === Basic Settings Domain (Bit 0-7) === */
    SETTINGS_DATA_BEEP_SWITCH          = 0x00000001,  // Beep switch control
    SETTINGS_DATA_AUX_IN_SWITCH        = 0x00000002,  // AUX IN switch control
    SETTINGS_DATA_DEVICE_RENAME        = 0x00000004,  // Device rename (type distinguished by first data byte)
    SETTINGS_DATA_SOURCE_ORDER         = 0x00000008,  // Source switching order configuration
    SETTINGS_DATA_BASIC_RESERVED1      = 0x00000010,  // Basic settings reserved 1
    SETTINGS_DATA_BASIC_RESERVED2      = 0x00000020,  // Basic settings reserved 2
    SETTINGS_DATA_BASIC_RESERVED3      = 0x00000040,  // Basic settings reserved 3
    SETTINGS_DATA_BASIC_RESERVED4      = 0x00000080,  // Basic settings reserved 4
    
    /* === Bluetooth Settings Domain (Bit 8-15) === */
    SETTINGS_DATA_BT_SWITCH            = 0x00000100,  // Bluetooth function switch
    SETTINGS_DATA_BT_NAME              = 0x00000200,  // Bluetooth device name setting
    SETTINGS_DATA_BT_DEVICE_LIST       = 0x00000400,  // Bluetooth device list management
    SETTINGS_DATA_BT_ADD_DEVICE        = 0x00000800,  // Bluetooth device add/pairing
    SETTINGS_DATA_BT_REMOVE_DEVICE     = 0x00001000,  // Bluetooth device removal
    SETTINGS_DATA_BT_AUTO_CONNECT      = 0x00002000,  // Bluetooth auto-connect setting
    SETTINGS_DATA_BT_RESERVED1         = 0x00004000,  // Bluetooth settings reserved 1
    SETTINGS_DATA_BT_RESERVED2         = 0x00008000,  // Bluetooth settings reserved 2
    
    /* === Network Settings Domain (Bit 16-23) === */
    SETTINGS_DATA_OWN_DEVICE           = 0x00010000,  // Own device configuration
    SETTINGS_DATA_CONNECTED_DEVICE     = 0x00020000,  // Connected device management (head unit/wired remote distinguished by first data byte)
    SETTINGS_DATA_NETWORK_RESERVED1    = 0x00040000,  // Network settings reserved 1
    SETTINGS_DATA_NETWORK_RESERVED2    = 0x00080000,  // Network settings reserved 2
    SETTINGS_DATA_NETWORK_RESERVED3    = 0x00100000,  // Network settings reserved 3
    SETTINGS_DATA_NETWORK_RESERVED4    = 0x00200000,  // Network settings reserved 4
    SETTINGS_DATA_NETWORK_RESERVED5    = 0x00400000,  // Network settings reserved 5
    SETTINGS_DATA_NETWORK_RESERVED6    = 0x00800000,  // Network settings reserved 6
    
    /* === System Maintenance Domain (Bit 24-30) === */
    SETTINGS_DATA_FW_UPDATE_USB        = 0x01000000,  // USB firmware update
    SETTINGS_DATA_FW_UPDATE_OTA        = 0x02000000,  // OTA firmware update
    SETTINGS_DATA_MAINTENANCE_RESERVED1= 0x04000000,  // System maintenance reserved 1
    SETTINGS_DATA_MAINTENANCE_RESERVED2= 0x08000000,  // System maintenance reserved 2
    SETTINGS_DATA_MAINTENANCE_RESERVED3= 0x10000000,  // System maintenance reserved 3
    SETTINGS_DATA_MAINTENANCE_RESERVED4= 0x20000000,  // System maintenance reserved 4
    SETTINGS_DATA_MAINTENANCE_RESERVED5= 0x40000000,  // System maintenance reserved 5
    
    /* === Factory Settings Domain (Bit 31) === */
    SETTINGS_DATA_FACTORY_DEFAULT      = 0x80000000,  // Factory default settings operation
} settings_data_type_t;
```

### 数据类型位图表格

| 位置 | 位图值 | 数据长度 | 数据类型 | 描述 |
|------|--------|----------|----------|------|
| **基础设置域 (Bit 0-7)** |
| Bit 0 | 0x00000001 | 1 byte | SETTINGS_DATA_BEEP_SWITCH | 蜂鸣器开关控制 |
| Bit 1 | 0x00000002 | 1 byte | SETTINGS_DATA_AUX_IN_SWITCH | AUX IN开关控制 |
| Bit 2 | 0x00000004 | 33 bytes | SETTINGS_DATA_DEVICE_RENAME | 设备重命名 (1字节类型+32字节名称) |
| Bit 3 | 0x00000008 | Variable | SETTINGS_DATA_SOURCE_ORDER | 信源切换顺序 |
| Bit 4-7 | 0x00000010-0x00000080 | - | Reserved | 基础设置预留位 |
| **蓝牙设置域 (Bit 8-15)** |
| Bit 8 | 0x00000100 | 4 bytes | SETTINGS_DATA_BT_SWITCH | 蓝牙配置 |
| Bit 9 | 0x00000200 | 33 bytes | SETTINGS_DATA_BT_NAME | 蓝牙设备名称 (1字节类型+32字节名称) |
| Bit 10 | 0x00000400 | Variable | SETTINGS_DATA_BT_DEVICE_LIST | 蓝牙设备列表 |
| Bit 11 | 0x00000800 | Variable | SETTINGS_DATA_BT_ADD_DEVICE | 蓝牙设备添加 |
| Bit 12-15 | 0x00001000-0x00008000 | - | Reserved | 蓝牙设置预留位 |
| **网络设置域 (Bit 16-23)** |
| Bit 16 | 0x00010000 | Variable | SETTINGS_DATA_OWN_DEVICE | 本机设备配置 |
| Bit 17 | 0x00020000 | Variable | SETTINGS_DATA_CONNECTED_DEVICE | 连接设备管理 |
| Bit 18-23 | 0x00040000-0x00800000 | - | Reserved | 网络设置预留位 |
| **系统维护域 (Bit 24-30)** |
| Bit 24 | 0x01000000 | Variable | SETTINGS_DATA_FW_UPDATE_USB | USB固件更新 |
| Bit 25 | 0x02000000 | Variable | SETTINGS_DATA_FW_UPDATE_OTA | OTA固件更新 |
| Bit 26-30 | 0x04000000-0x40000000 | - | Reserved | 系统维护预留位 |
| **工厂设置域 (Bit 31)** |
| Bit 31 | 0x80000000 | 1 byte | SETTINGS_DATA_FACTORY_DEFAULT | 工厂默认设置 |

### 数据结构定义

#### 基础设置域数据结构

```c
// Renamable device name type enumeration
typedef enum {
    NAME_TYPE_UNKNOWN = 0,                // Unknown device name
    NAME_TYPE_ZONE1   = 1,                // Zone1 device name
    NAME_TYPE_ZONE2   = 2,                // Zone2 device name  
    NAME_TYPE_AUX     = 3,                // AUX IN device name
    NAME_TYPE_BT      = 4,                // Bluetooth device name
    NAME_TYPE_MAX     = 5                 // Maximum name types
} settings_name_type_t;

// Renamable device data structure
typedef struct {
    __u8 name_type;                       // Name type (settings_name_type_t)
    char device_name[32];                 // Device name (UTF-8, max 31 chars + null)
} settings_renamable_t;

// Settings status data structure - complete settings state collection
typedef struct {
    // Basic settings status (Bit 0-7)
    __u8 beep_enabled;                    // Beep switch status (0=Off, 1=On)
    __u8 aux_in_enabled;                  // AUX IN switch status (0=Off, 1=On)
    settings_renamable_t renamable[NAME_TYPE_MAX]; // All renamable device names
    settings_source_order_t source_order; // Current source order configuration
    
    // Bluetooth settings status (Bit 8-15)
    settings_bt_config_t bt_config;       // Bluetooth configuration status
    settings_bt_device_list_t bt_devices; // Current Bluetooth device list
    settings_bt_add_device_t bt_pairing;  // Current pairing status
    
    // Network settings status (Bit 16-23)
    settings_own_device_t own_device;     // Own device configuration status
    settings_connected_device_t connected_devices; // Connected devices status
    
    // System maintenance status (Bit 24-30)
    settings_fw_update_usb_t usb_update;  // USB firmware update status
    settings_fw_update_ota_t ota_update;  // OTA firmware update status
    
    // Factory settings status (Bit 31)
    __u8 factory_reset_status;            // Factory reset status (0=Idle, 1=In progress, 2=Complete, 3=Failed)
    
    // Overall status flags
    __u32 active_data_types;              // Bitmap of currently active data types
    __u8 last_operation_result;           // Last operation result (0=Success, others=Error codes)
    __u8 reserved[3];                     // Reserved bytes
} settings_status_t;
```

```c
// Source order configuration data structure
typedef struct {
    __u8 source_count;                    // Total source count
    __u8 current_order[16];               // Source order array
    // Source ID definition: 1=FM, 2=AM, 3=WB, 4=USB_DISK, 5=USB_IPOD,
    //                      6=BT_AUDIO, 7=BT_PHONE, 8=AUX_IN, 9=DIGITAL_IN
    __u8 custom_order_enabled;            // Custom order switch
    __u8 reserved[3];                     // Reserved bytes
} settings_source_order_t;


```

#### 蓝牙设置域数据结构

```c
// Bluetooth configuration data structure
typedef struct {
    __u8 bt_enabled;                      // Bluetooth function switch (0=Off, 1=On)
    __u8 discoverable;                    // Discoverable mode (0=Off, 1=On)
    __u8 auto_connect;                    // Auto connect (0=Off, 1=On)
    __u8 reserved;                        // Reserved byte
} settings_bt_config_t;



// Bluetooth device list data structure
typedef struct {
    __u8 device_count;                    // Device count
    __u8 max_devices;                     // Maximum supported devices
    __u8 reserved[2];                     // Reserved bytes
    struct {
        char name[32];                    // Device name
        __u8 mac_address[6];              // MAC address
        __u8 device_type;                 // Device type (0=Phone, 1=Audio, 2=Mixed)
        __u8 connection_status;           // Connection status (0=Disconnected, 1=Connected, 2=Connecting)
        __u8 signal_strength;             // Signal strength (0-100)
        __u8 last_connected;              // Last connected flag (0=No, 1=Yes)
        __u8 reserved[4];                 // Reserved bytes
    } devices[8];                         // Support up to 8 devices
} settings_bt_device_list_t;

// Bluetooth device add data structure
typedef struct {
    __u8 pairing_mode;                    // Pairing mode (0=Off, 1=On)
    __u8 pairing_timeout;                 // Pairing timeout (seconds, 0=unlimited)
    __u8 pin_code[4];                     // PIN code (if required)
    __u8 reserved[2];                     // Reserved bytes
} settings_bt_add_device_t;
```

#### 网络设置域数据结构

```c
// Own device configuration data structure
typedef struct {
    char device_name[32];                 // Own device name
    __u8 zone_assignment;                 // Zone assignment (1-4)
    __u8 device_role;                     // Device role (0=Master, 1=Slave)
    __u8 network_enabled;                 // Network function enabled
    __u8 reserved;                        // Reserved byte
} settings_own_device_t;

// Connected device management data structure
typedef struct {
    __u8 device_type;                     // Device type (0=Head unit, 1=Wired remote)
    __u8 device_count;                    // Connected device count
    __u8 max_devices;                     // Maximum supported devices
    __u8 reserved;                        // Reserved byte
    struct {
        char device_name[32];             // Device name
        char custom_name[32];             // Custom name
        __u8 device_id;                   // Device ID
        __u8 zone_assignment;             // Zone assignment
        __u8 connection_status;           // Connection status
        __u8 name_source;                 // Name source (0=Auto, 1=Custom)
        __u8 reserved[4];                 // Reserved bytes
    } devices[8];                         // Support up to 8 devices
} settings_connected_device_t;
```

#### 系统维护域数据结构

```c
// USB firmware update data structure
typedef struct {
    __u8 update_status;                   // Update status
    // 0=Idle, 1=Checking, 2=Downloading, 3=Installing, 4=Complete, 5=Failed
    __u8 update_progress;                 // Update progress (0-100%)
    __u8 update_target;                   // Update target
    // 0=All, 1=Main controller, 2=Audio, 3=Bluetooth, 4=Tuner
    __u8 reserved;                        // Reserved byte
    char file_path[128];                  // Update file path
    __u32 file_size;                      // File size
    __u32 checksum;                       // File checksum
} settings_fw_update_usb_t;

// OTA firmware update data structure
typedef struct {
    __u8 update_status;                   // Update status
    __u8 update_progress;                 // Update progress (0-100%)
    __u8 update_target;                   // Update target
    __u8 auto_check;                      // Auto check update (0=Off, 1=On)
    char server_url[128];                 // Update server URL
    char version_info[32];                // Version information
    __u32 download_size;                  // Download size
    __u32 checksum;                       // File checksum
} settings_fw_update_ota_t;
```

## 数据模型

### 位图处理算法

```c
// Settings category main handler function
__s32 settings_main_handler(__u8 operation, __u32 data_type, __u8 *data, __u16 data_len)
{
    __u32 offset = 0;
    __s32 ret = EPDK_OK;
    
    // Basic settings domain processing (Bit 0-7)
    if (data_type & SETTINGS_DATA_BEEP_SWITCH) {
        ret = handle_beep_switch_data(operation, data + offset, 1);  // Single byte
        if (operation == OPERATION_EVENT) offset += 1;
        RETURN_IF_FAIL(ret);
    }
    
    if (data_type & SETTINGS_DATA_AUX_IN_SWITCH) {
        ret = handle_aux_in_switch_data(operation, data + offset, 1);  // Single byte
        if (operation == OPERATION_EVENT) offset += 1;
        RETURN_IF_FAIL(ret);
    }
    
    if (data_type & SETTINGS_DATA_DEVICE_RENAME) {
        ret = handle_device_rename_data(operation, data + offset, sizeof(settings_renamable_t));
        if (operation == OPERATION_EVENT) offset += sizeof(settings_renamable_t);
        RETURN_IF_FAIL(ret);
    }
    
    if (data_type & SETTINGS_DATA_SOURCE_ORDER) {
        ret = handle_source_order_data(operation, data + offset, sizeof(settings_source_order_t));
        if (operation == OPERATION_EVENT) offset += sizeof(settings_source_order_t);
        RETURN_IF_FAIL(ret);
    }
    
    // Bluetooth settings domain processing (Bit 8-15)
    if (data_type & SETTINGS_DATA_BT_SWITCH) {
        ret = handle_bt_switch_data(operation, data + offset, sizeof(settings_bt_config_t));
        if (operation == OPERATION_EVENT) offset += sizeof(settings_bt_config_t);
        RETURN_IF_FAIL(ret);
    }
    
    if (data_type & SETTINGS_DATA_BT_NAME) {
        ret = handle_bt_name_data(operation, data + offset, sizeof(settings_renamable_t));
        if (operation == OPERATION_EVENT) offset += sizeof(settings_renamable_t);
        RETURN_IF_FAIL(ret);
    }
    
    if (data_type & SETTINGS_DATA_BT_DEVICE_LIST) {
        ret = handle_bt_device_list_data(operation, data + offset, sizeof(settings_bt_device_list_t));
        if (operation == OPERATION_EVENT) offset += sizeof(settings_bt_device_list_t);
        RETURN_IF_FAIL(ret);
    }
    
    if (data_type & SETTINGS_DATA_BT_ADD_DEVICE) {
        ret = handle_bt_add_device_data(operation, data + offset, sizeof(settings_bt_add_device_t));
        if (operation == OPERATION_EVENT) offset += sizeof(settings_bt_add_device_t);
        RETURN_IF_FAIL(ret);
    }
    
    // Network settings domain processing (Bit 16-23)
    if (data_type & SETTINGS_DATA_OWN_DEVICE) {
        ret = handle_own_device_data(operation, data + offset, sizeof(settings_own_device_t));
        if (operation == OPERATION_EVENT) offset += sizeof(settings_own_device_t);
        RETURN_IF_FAIL(ret);
    }
    
    if (data_type & SETTINGS_DATA_CONNECTED_DEVICE) {
        ret = handle_connected_device_data(operation, data + offset, sizeof(settings_connected_device_t));
        if (operation == OPERATION_EVENT) offset += sizeof(settings_connected_device_t);
        RETURN_IF_FAIL(ret);
    }
    
    // System maintenance domain processing (Bit 24-30)
    if (data_type & SETTINGS_DATA_FW_UPDATE_USB) {
        ret = handle_fw_update_usb_data(operation, data + offset, sizeof(settings_fw_update_usb_t));
        if (operation == OPERATION_EVENT) offset += sizeof(settings_fw_update_usb_t);
        RETURN_IF_FAIL(ret);
    }
    
    if (data_type & SETTINGS_DATA_FW_UPDATE_OTA) {
        ret = handle_fw_update_ota_data(operation, data + offset, sizeof(settings_fw_update_ota_t));
        if (operation == OPERATION_EVENT) offset += sizeof(settings_fw_update_ota_t);
        RETURN_IF_FAIL(ret);
    }
    
    // Factory settings domain processing (Bit 31)
    if (data_type & SETTINGS_DATA_FACTORY_DEFAULT) {
        ret = handle_factory_default_data(operation, data + offset, 1);  // Single byte operation
        if (operation == OPERATION_EVENT) offset += 1;
        RETURN_IF_FAIL(ret);
    }
    
    return ret;
}
```

### 状态管理和查询

#### 完整状态查询
```c
// Get complete settings status (similar to system/audio category pattern)
new_cmd_packet_t cmd = {
    .main_cmd = 0xF100,
    .category = CATEGORY_SETTINGS,
    .operation = OPERATION_GET,
    .data_type = 0xFFFFFFFF,  // All data types
    .data_len = 0,
};

// MCU responds with complete settings_status_t structure
// Response includes all current settings in one packet
```

#### 选择性状态查询
```c
// Get basic settings status only
new_cmd_packet_t cmd = {
    .main_cmd = 0xF100,
    .category = CATEGORY_SETTINGS,
    .operation = OPERATION_GET,
    .data_type = SETTINGS_DATA_BEEP_SWITCH | SETTINGS_DATA_AUX_IN_SWITCH | 
                 SETTINGS_DATA_DEVICE_RENAME | SETTINGS_DATA_SOURCE_ORDER,
    .data_len = 0,
};
```

#### 设备重命名示例
```c
// Rename Zone1 device
new_cmd_packet_t cmd = {
    .main_cmd = 0xF100,
    .category = CATEGORY_SETTINGS,
    .operation = OPERATION_SET,
    .data_type = SETTINGS_DATA_DEVICE_RENAME,
    .data_len = 33,
    .data = {NAME_TYPE_ZONE1, 'M', 'y', ' ', 'Z', 'o', 'n', 'e', '1', 0, ...} // name_type + device_name
};

// Rename Bluetooth device (same as BT_NAME data type)
new_cmd_packet_t cmd = {
    .main_cmd = 0xF100,
    .category = CATEGORY_SETTINGS,
    .operation = OPERATION_SET,
    .data_type = SETTINGS_DATA_BT_NAME,  // or SETTINGS_DATA_DEVICE_RENAME
    .data_len = 33,
    .data = {NAME_TYPE_BT, 'M', 'y', ' ', 'B', 'T', ' ', 'D', 'e', 'v', 'i', 'c', 'e', 0, ...}
};
```

### 数据持久化策略

1. **配置存储**: 所有设置数据存储到非易失性存储器
2. **版本管理**: 支持设置数据的版本控制和迁移
3. **原子操作**: 相关设置的原子性更新
4. **备份恢复**: 支持设置的备份和恢复功能
5. **状态同步**: settings_status_t结构与实际硬件状态保持同步

## 错误处理

### 错误代码定义

```c
typedef enum {
    SETTINGS_ERROR_NONE                = 0x00,  // No error
    SETTINGS_ERROR_INVALID_PARAM       = 0x01,  // Invalid parameter
    SETTINGS_ERROR_DEVICE_NOT_FOUND    = 0x02,  // Device not found
    SETTINGS_ERROR_OPERATION_FAILED    = 0x03,  // Operation failed
    SETTINGS_ERROR_STORAGE_FULL        = 0x04,  // Storage full
    SETTINGS_ERROR_PERMISSION_DENIED   = 0x05,  // Permission denied
    SETTINGS_ERROR_TIMEOUT             = 0x06,  // Operation timeout
    SETTINGS_ERROR_NETWORK_ERROR       = 0x07,  // Network error
    SETTINGS_ERROR_UPDATE_FAILED       = 0x08,  // Update failed
    SETTINGS_ERROR_CHECKSUM_MISMATCH   = 0x09,  // Checksum mismatch
} settings_error_code_t;
```

### 错误处理机制

1. **参数验证**: 所有输入参数的有效性检查
2. **状态检查**: 操作前的系统状态验证
3. **回滚机制**: 失败操作的自动回滚
4. **错误报告**: 详细的错误信息反馈

## 测试策略

### 单元测试

1. **数据类型验证**: 每个数据类型的独立测试
2. **位图组合测试**: 多数据类型组合的测试
3. **边界条件测试**: 参数边界值的测试
4. **错误处理测试**: 异常情况的处理测试

### 集成测试

1. **协议兼容性**: 与其他分类的协议兼容性测试
2. **性能测试**: 大数据量的处理性能测试
3. **稳定性测试**: 长时间运行的稳定性测试
4. **用户场景测试**: 实际使用场景的端到端测试

这个设计提供了完整的设置分类数据类型位图架构，支持用户界面中显示的所有设置功能，同时避免了与系统分类的重复，并为未来扩展预留了充足的空间。