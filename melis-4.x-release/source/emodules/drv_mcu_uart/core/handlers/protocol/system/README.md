# 系统分类处理器 (System Category Handler)

## 概述

本目录实现了基于 **MCU UART 协议统一表格 - 系统分类 (SYS)** 文档的系统控制功能处理器。该处理器负责处理分类代码为 `0x01` 的所有系统相关命令，包括信源控制、电源管理、系统设置等核心功能。

## 设计特点

### 🎯 核心功能
- **信源管理**: 支持11种信源的切换和使能控制
- **电源控制**: 完整的电源状态管理（开机/关机/待机/休眠）
- **系统设置**: 语言、亮度等系统参数配置
- **状态监控**: 实时温度、电压、错误状态监控
- **扩展功能**: 版本信息、工厂信息、调试功能

### 📋 支持的数据类型
按照文档定义，支持以下数据类型：

#### 基础数据类型 (Bit 0-9)
- `SYS_DATA_SOURCE` (0x0001): 当前信源
- `SYS_DATA_SOURCE_ENABLE` (0x0002): 可用信源使能
- `SYS_DATA_DIMMER` (0x0004): DIMMER开关
- `SYS_DATA_DIMMER_ON_LEVEL` (0x0008): DIMMER开启时的亮度等级
- `SYS_DATA_DIMMER_OFF_LEVEL` (0x0010): DIMMER关闭时的亮度等级
- `SYS_DATA_TEMPERATURE` (0x0020): 系统温度
- `SYS_DATA_VOLTAGE` (0x0040): 系统电压
- `SYS_DATA_POWER_STATE` (0x0080): 电源状态
- `SYS_DATA_VERSION` (0x0100): 版本信息
- `SYS_DATA_LANGUAGE` (0x0200): 语言设置

#### 扩展数据类型 (Bit 10-15)
- `SYS_DATA_FACTORY_INFO` (0x0400): 工厂信息
- `SYS_DATA_ERROR_CODE` (0x0800): 错误代码
- `SYS_DATA_DEBUG_INFO` (0x1000): 调试信息
- `SYS_DATA_RESERVED` (0x2000): 保留位

## 架构设计

### 📦 子命令支持
严格按照文档定义实现3个子命令：

1. **SUBCMD_PLAYBACK (0x01)** - 播放控制
   - 信源切换、音量控制、电源控制
   - DIMMER开关和亮度等级控制
   - 状态监控（温度、电压、电源状态）

2. **SUBCMD_SETTINGS (0x02)** - 设置配置
   - 系统参数配置、语言设置、用户偏好
   - 信源使能配置、DIMMER开关和亮度等级设置

3. **SUBCMD_EXTENSION (0xFF)** - 扩展功能
   - 系统信息查询、调试功能、维护模式
   - 版本信息、工厂信息、系统诊断

### 🔧 操作类型支持
- `OPERATION_GET`: 获取系统状态和配置
- `OPERATION_SET`: 设置系统参数和控制
- `OPERATION_EVENT`: 系统事件通知（预留）
- `OPERATION_CONTROL`: 系统控制命令（预留）

## 文件结构

```
system/
├── README.md                               # 本文件
├── Makefile                               # 编译配置
├── mcu_system_category_handler.h          # 系统处理器头文件
├── mcu_system_category_handler.c          # 系统处理器实现
└── system_test_demo.c                     # 测试和演示代码
```

## API 接口

### 核心处理器函数

```c
/**
 * @brief 系统播放控制处理器
 * @param operation 操作类型 (GET/SET/EVENT/CONTROL)
 * @param data_type 数据类型位图
 * @param data 数据缓冲区
 * @param data_len 数据长度
 * @return EPDK_OK: 成功, EPDK_FAIL: 失败
 */
__s32 system_playback_handler(__u8 operation, __u16 data_type, __u8 *data, __u8 data_len);

/**
 * @brief 系统设置配置处理器
 */
__s32 system_settings_handler(__u8 operation, __u16 data_type, __u8 *data, __u8 data_len);

/**
 * @brief 系统扩展功能处理器
 */
__s32 system_extension_handler(__u8 operation, __u16 data_type, __u8 *data, __u8 data_len);
```

### 便捷API函数

```c
/* 信源管理 */
/* 系统控制 */
__s32 system_set_source(__u8 source);
__u8 system_get_current_source(void);
__s32 system_set_source_enable(__u32 enable_mask);

/* DIMMER控制 */
__s32 system_set_dimmer_switch(__u8 dimmer_switch);
__s32 system_set_dimmer_on_level(__u8 on_level);
__s32 system_set_dimmer_off_level(__u8 off_level);

/* 电源和语言控制 */
__s32 system_set_power_state(__u8 power_state);
__s32 system_set_language(__u8 language);

/* 状态查询 */
__s32 system_get_status(system_status_t *status);
__s32 system_get_version(system_version_t *version);
__s32 system_get_factory_info(system_factory_info_t *factory_info);
```

### 注册和统计

```c
/* 注册处理器 */
__s32 mcu_system_category_handler_register(void);

/* 统计信息 */
void system_get_handler_stats(__u32 *playback_count, __u32 *settings_count, __u32 *extension_count);
void system_reset_handler_stats(void);
void system_print_handler_status(void);
```

## 数据结构

### 系统状态结构
```c
typedef struct {
    __u8  current_source;               /* 当前信源 */
    __u32 source_enable_mask;           /* 信源使能掩码 */
    __u8  dimmer_switch;                /* DIMMER开关 0=OFF, 1=ON */
    __u8  dimmer_on_level;              /* DIMMER开启时亮度等级 1-10 */
    __u8  dimmer_off_level;             /* DIMMER关闭时亮度等级 11-20 */
    __s16 temperature;                  /* 温度 (0.1°C) */
    __u16 voltage;                      /* 电压 (0.1V) */
    __u8  power_state;                  /* 电源状态 */
    __u8  language;                     /* 语言设置 */
    __u16 error_code;                   /* 错误代码 */
} system_status_t;
```

### 版本信息结构
```c
typedef struct {
    __u8  major;                        /* 主版本号 */
    __u8  minor;                        /* 次版本号 */
    __u8  patch;                        /* 补丁版本号 */
    __u8  build;                        /* 构建版本号 */
    char  version_string[16];           /* 版本字符串 */
} system_version_t;
```

### 工厂信息结构
```c
typedef struct {
    char  manufacturer[16];             /* 制造商 */
    char  model_number[16];             /* 型号 */
    char  serial_number[16];            /* 序列号 */
    __u32 manufacture_date;             /* 生产日期 (YYYYMMDD) */
    __u16 hardware_version;             /* 硬件版本 */
} system_factory_info_t;
```

## 使用示例

### 示例1: 设置机种型号为PMX20
```c
__u8 model_data = SYS_MODEL_PMX20;
__s32 ret = system_playback_handler(OPERATION_SET, SYS_DATA_MODEL, &model_data, 1);
if (ret == EPDK_OK) {
    __msg("Model set to PMX20 successfully\n");
}
```

### 示例2: 信源切换到USB
```c
__u8 source_data = SYS_SOURCE_USB;
__s32 ret = system_playback_handler(OPERATION_SET, SYS_DATA_SOURCE, &source_data, 1);
if (ret == EPDK_OK) {
    __msg("Source switched to USB successfully\n");
}
```

### 示例3: 设置DIMMER开关和亮度等级
```c
/* 设置DIMMER开关为开启 */
__u8 dimmer_switch = 1;
__s32 ret = system_playback_handler(OPERATION_SET, SYS_DATA_DIMMER, &dimmer_switch, 1);

/* 设置DIMMER开启时亮度等级 */
__u8 on_level = 7;  /* 1-10范围 */
ret = system_settings_handler(OPERATION_SET, SYS_DATA_DIMMER_ON_LEVEL, &on_level, 1);

/* 设置DIMMER关闭时亮度等级 */
__u8 off_level = 15;  /* 11-20范围 */
ret = system_settings_handler(OPERATION_SET, SYS_DATA_DIMMER_OFF_LEVEL, &off_level, 1);
```

### 示例4: 获取系统完整状态
```c
__u8 status_data[8];
__s32 ret = system_playback_handler(OPERATION_GET, 
                                   SYS_DATA_MODEL | SYS_DATA_SOURCE | SYS_DATA_POWER_STATE | 
                                   SYS_DATA_TEMPERATURE | SYS_DATA_VOLTAGE,
                                   status_data, sizeof(status_data));
```

### 示例5: 配置信源使能
```c
__u32 enable_mask = 0x003F;  /* 启用前6个信源 */
__s32 ret = system_set_source_enable(enable_mask);
```

### 示例5: 获取版本信息
```c
__u8 version_data[16];
__s32 ret = system_extension_handler(OPERATION_GET, SYS_DATA_VERSION, 
                                    version_data, sizeof(version_data));
```

## 常量定义

### 信源代码
| 信源 | 代码 | 说明 |
|------|------|------|
| SYS_SOURCE_FM | 0x01 | FM收音机 |
| SYS_SOURCE_AM | 0x02 | AM收音机 |
| SYS_SOURCE_WB | 0x03 | 天气广播 |
| SYS_SOURCE_USB | 0x04 | USB设备 |
| SYS_SOURCE_BT_AUDIO | 0x05 | 蓝牙音频 |
| SYS_SOURCE_DAB | 0x06 | 数字广播 |
| SYS_SOURCE_SXM | 0x07 | 卫星收音机 |
| SYS_SOURCE_AUXIN | 0x08 | 辅助输入 |
| SYS_SOURCE_AUXIN2 | 0x09 | 辅助输入2 |
| SYS_SOURCE_CAMERA | 0x0A | 摄像头 |
| SYS_SOURCE_PARTY | 0x0B | Party模式 |

### 电源状态
| 状态 | 代码 | 说明 |
|------|------|------|
| SYS_POWER_ON | 0x00 | 正常工作 |
| SYS_POWER_OFF | 0x01 | 系统关闭 |
| SYS_POWER_STANDBY | 0x02 | 低功耗待机 |
| SYS_POWER_SLEEP | 0x03 | 深度休眠 |

### 语言代码
| 语言 | 代码 | 说明 |
|------|------|------|
| SYS_LANG_ENGLISH | 0x00 | 英语 |
| SYS_LANG_CHINESE | 0x01 | 简体中文 |
| SYS_LANG_JAPANESE | 0x02 | 日语 |
| SYS_LANG_GERMAN | 0x03 | 德语 |
| SYS_LANG_FRENCH | 0x04 | 法语 |

### DIMMER亮度控制定义
### 机种型号定义
| 机种 | 代码 | 说明 |
|------|------|------|
| SYS_MODEL_PMX30 | 0x00 | PMX30系列 |
| SYS_MODEL_PMX20 | 0x01 | PMX20系列 |
| SYS_MODEL_PMXR20 | 0x10 | PMXR20系列 |
| 自定义机种 | 0x20-0xFF | 用户定义机种 |

### DIMMER亮度控制定义
| 数据类型 | 说明 | 取值范围 |
|----------|------|----------|
| SYS_DATA_DIMMER | DIMMER开关状态 | 0=关闭, 1=开启 |
| SYS_DATA_DIMMER_ON_LEVEL | DIMMER开启时的亮度等级 | 1-10 (1最暗，10最亮) |
| SYS_DATA_DIMMER_OFF_LEVEL | DIMMER关闭时的亮度等级 | 11-20 (11最暗，20最亮) |

## 测试

### 自动化测试套件

提供完整的测试覆盖：

```c
/* 初始化测试环境 */
system_test_init();

/* 运行完整测试套件 */
system_test_run_all();

/* 打印测试状态 */
system_test_print_status();

/* 清理测试环境 */
system_test_cleanup();
```

### 测试覆盖范围

- ✅ 信源切换和获取测试
- ✅ 信源使能掩码设置测试
- ✅ DIMMER开关和亮度等级控制测试
- ✅ 电源状态管理测试
- ✅ 系统状态监控测试
- ✅ 语言设置功能测试
- ✅ 版本信息查询测试
- ✅ 工厂信息查询测试
- ✅ 调试功能测试
- ✅ 组合数据类型操作测试
- ✅ 错误处理和边界条件测试

### 命令行接口

```bash
# 初始化测试
system_test init

# 运行测试
system_test test

# 查看状态
system_test status

# 清理测试
system_test cleanup
```

## 集成指南

### 1. 注册处理器

```c
#include "system/mcu_system_category_handler.h"

/* 在初始化阶段注册 */
__s32 ret = mcu_system_category_handler_register();
if (ret != EPDK_OK) {
    __err("Failed to register system category handler\n");
}
```

### 2. 使用便捷API

```c
/* 直接使用API函数 */
system_set_model(SYS_MODEL_PMX20);
system_set_source(SYS_SOURCE_USB);
system_set_dimmer_switch(1);
system_set_dimmer_on_level(8);
system_set_dimmer_off_level(16);
system_set_language(SYS_LANG_CHINESE);

/* 获取系统状态 */
system_status_t status;
system_get_status(&status);
```

### 3. 处理协议包

系统分类处理器会自动处理来自新协议框架的命令包，无需手动解析。

## 性能特点

- **内存占用**: 约300字节静态内存
- **处理速度**: 微秒级响应时间
- **数据验证**: 完整的输入验证和边界检查
- **错误处理**: 详细的错误信息和状态码

## 调试和诊断

### 启用调试输出
```c
#define SYS_HANDLER_DEBUG    1
```

### 统计信息收集
```c
__u32 playback_count, settings_count, extension_count;
system_get_handler_stats(&playback_count, &settings_count, &extension_count);
```

### 状态监控
```c
system_print_handler_status();  /* 打印完整系统状态 */
```

## 注意事项

1. **线程安全**: 当前实现不是线程安全的，多线程环境下需要外部同步
2. **数据持久化**: 系统状态仅保存在内存中，断电后会丢失
3. **硬件接口**: 某些功能（如温度、电压、DIMMER控制）需要相应的硬件支持
4. **错误恢复**: 建议在关键操作失败时实现重试机制
5. **DIMMER等级**: 开启和关闭时的亮度等级范围不同，需要注意取值范围

## 扩展能力

- 支持新的信源类型（修改枚举定义）
- 支持新的电源状态（扩展状态定义）
- 支持新的语言（添加语言代码）
- 支持自定义数据类型（使用保留位）

## 兼容性

- 完全兼容v2_Unified_Table.md文档规范
- 向后兼容现有系统功能
- 支持未来协议扩展

---

**版本**: v1.0  
**最后更新**: 2024-12-19  
**基于文档**: MCU UART Protocol v2 System Table  
**开发者**: Sunny