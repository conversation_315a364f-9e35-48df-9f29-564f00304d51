# 新协议处理器 (0xF100) - v3.0 表格化统一命令协议

## 概述

本目录包含了基于 **MCU UART 表格化统一命令协议设计文档 v3.0** 实现的新协议处理器。该处理器负责处理命令码为 `0xF100` 的统一协议命令，提供了一个模块化、可扩展的架构来处理各种设备分类的命令。

## 设计特点

### 🎯 主要优势
- **统一命令结构**: 所有设备分类使用相同的命令包格式
- **模块化处理**: 每个设备分类独立处理，易于维护和扩展
- **表格化设计**: 通过注册表方式管理分类处理器
- **高效分发**: 基于现有的 `mcu_cmd_dispatcher` 架构
- **完整错误处理**: 统一的错误处理和状态反馈机制

### 📋 支持的设备分类
按照 v3.0 文档定义，支持以下设备分类：

#### 系统控制类 (0x01-0x0F)
- 0x01: 系统控制 (SYSTEM)
- 0x02: 音频控制 (AUDIO)  
- 0x03: 全局设置 (SETTINGS)
- 0x04: UI界面控制 (UI)

#### 广播媒体类 (0x10-0x1F)
- 0x10: FM收音机 (FM)
- 0x11: AM收音机 (AM)
- 0x12: 天气广播 (WB)
- 0x13: 数字广播 (DAB)
- 0x14: 卫星收音机 (SXM)

#### USB设备类 (0x20-0x2F)
- 0x20: USB存储设备 (USB_DISK)
- 0x21: iPod设备 (USB_IPOD)
- 0x22: Android设备 (USB_ANDROID)
- 0x23: MTP设备 (USB_MTP)

#### 蓝牙设备类 (0x30-0x3F)
- 0x30: 蓝牙音频 (BT_AUDIO)
- 0x31: 蓝牙电话 (BT_PHONE)
- 0x32: 蓝牙设置 (BT_SETTINGS)

#### 外部输入类 (0x40-0x4F)
- 0x40: 辅助输入 (AUX_IN)
- 0x41: 线路输入 (LINE_IN)
- 0x42: 数字输入 (DIGITAL_IN)
- 0x43: 视频输入 (VIDEO_IN)
- 0x44: 摄像头 (CAMERA)

#### 智能设备类 (0x50-0x5F)
- 0x50: WiFi管理 (WIFI)
- 0x51: 苹果CarPlay (CARPLAY)
- 0x52: 谷歌Android Auto (ANDROID_AUTO)
- 0x53: 无线投屏 (MIRACAST)

#### 车辆集成类 (0x60-0x6F)
- 0x60: CAN总线 (CAN_BUS)
- 0x61: 方向盘控制 (STEERING)
- 0x62: 空调控制 (CLIMATE)
- 0x63: 车辆信息 (VEHICLE_INFO)

#### 维护工具类 (0x70-0x7F)
- 0x70: 系统升级 (UPDATE)
- 0x71: 调试工具 (DEBUG)
- 0x72: 测试工具 (TEST)
- 0x73: 工厂测试 (FACTORY)

## 架构设计

### 📦 命令包结构
```c
typedef struct {
    __u16 main_cmd;         // 0xF100 (固定主命令)
    __u8  category;         // 功能分类
    __u8  operation;        // 操作类型
    __u8  subcmd;           // 子命令
    __u16 data_type;        // 数据类型位图 (16位)
    __u8  data_len;         // 数据总长度
    __u8  data[];           // 可变长数据
} new_cmd_packet_t;
```

### 🔧 操作类型
- `OPERATION_GET` (0x01): 获取数据
- `OPERATION_SET` (0x02): 设置数据
- `OPERATION_EVENT` (0x03): 事件通知
- `OPERATION_CONTROL` (0x04): 控制命令

### 📝 统一子命令
- `SUBCMD_PLAYBACK` (0x01): 播放控制
- `SUBCMD_SETTINGS` (0x02): 设置配置
- `SUBCMD_PRESET_LIST` (0x03): 预设列表
- `SUBCMD_BROWSER_LIST` (0x04): 浏览列表
- `SUBCMD_EXTENSION` (0xFF): 扩展功能

## 文件结构

```
protocol/
├── README.md                           # 本文件
├── Makefile                           # 编译配置
├── mcu_new_protocol_handler.h         # 协议处理器头文件
├── mcu_new_protocol_handler.c         # 协议处理器实现
├── example_fm_handler.c               # FM收音机处理器示例
└── protocol_init_demo.c               # 初始化和演示代码
```

## 使用方法

### 1. 初始化协议处理器

```c
#include "handlers/protocol/mcu_new_protocol_handler.h"

// 初始化协议处理器
__s32 ret = mcu_new_protocol_handler_register();
if (ret != EPDK_OK) {
    __err("Failed to register new protocol handler\n");
    return EPDK_FAIL;
}
```

### 2. 注册分类处理器

```c
// 定义分类处理器
unified_cmd_handler_t my_handler = {
    .category = CATEGORY_FM,
    .name = "FM_Radio",
    .playback_handler = my_fm_playback_handler,
    .settings_handler = my_fm_settings_handler,
    .preset_handler = my_fm_preset_handler,
    .browser_handler = my_fm_browser_handler,
    .extension_handler = my_fm_extension_handler
};

// 注册分类处理器
ret = mcu_new_protocol_register_category_handler(&my_handler);
```

### 3. 实现分类处理函数

```c
static __s32 my_fm_playback_handler(__u8 operation, __u16 data_type, 
                                   __u8 *data, __u8 data_len)
{
    switch (operation) {
        case OPERATION_GET:
            // 处理获取播放状态
            break;
        case OPERATION_SET:
            // 处理设置播放参数
            break;
        case OPERATION_CONTROL:
            // 处理播放控制命令
            break;
        case OPERATION_EVENT:
            // 处理播放事件通知
            break;
        default:
            return EPDK_FAIL;
    }
    return EPDK_OK;
}
```

## 示例代码

### FM收音机处理器示例

参考 `example_fm_handler.c` 文件，该文件展示了如何为 FM 收音机分类实现完整的处理器，包括：

- 播放控制 (频率调节、搜台、播放/暂停)
- 设置配置 (频段、步进、区域设置)
- 预设列表 (预设管理、自动预设)
- 浏览列表 (电台浏览、搜索)
- 扩展功能 (RDS信息、信号强度)

### 演示和测试代码

参考 `protocol_init_demo.c` 文件，该文件提供了：

- 完整的初始化流程
- 自动化测试套件
- 错误处理测试
- 统计信息收集
- 命令行接口

## 测试

### 运行测试套件

```c
// 初始化演示环境
protocol_init_demo();

// 运行测试套件
protocol_run_tests();

// 打印状态信息
protocol_print_demo_status();
```

### 命令行接口

```c
// 示例命令行用法
char *test_argv[] = {"protocol_demo", "test"};
protocol_demo_cli(2, test_argv);
```

## 集成指南

### 1. 在现有系统中集成

1. **添加头文件包含**:
   ```c
   #include "core/handlers/protocol/mcu_new_protocol_handler.h"
   ```

2. **在命令分发器初始化时注册**:
   ```c
   // 在 mcu_cmd_dispatcher.c 的 register_default_handlers() 中
   ret |= mcu_new_protocol_handler_register();
   ```

3. **注册您的分类处理器**:
   ```c
   // 在您的模块初始化函数中
   ret |= mcu_new_protocol_register_category_handler(&your_handler);
   ```

### 2. 扩展新的设备分类

1. **创建新的分类处理器文件**:
   ```c
   // example_your_device_handler.c
   #include "mcu_new_protocol_handler.h"
   
   static __s32 your_device_playback_handler(__u8 operation, __u16 data_type, 
                                            __u8 *data, __u8 data_len) {
       // 实现您的逻辑
       return EPDK_OK;
   }
   
   __s32 your_device_handler_register(void) {
       unified_cmd_handler_t handler = {
           .category = CATEGORY_YOUR_DEVICE,
           .name = "Your_Device",
           .playback_handler = your_device_playback_handler,
           // ... 其他处理器函数
       };
       return mcu_new_protocol_register_category_handler(&handler);
   }
   ```

2. **更新 Makefile**:
   ```makefile
   obj-y += example_your_device_handler.o
   ```

## 调试和诊断

### 启用调试输出

在 `mcu_new_protocol_handler.h` 中设置：
```c
#define NEW_PROTOCOL_DEBUG    1
```

### 获取统计信息

```c
__u32 cmd_count, error_count;
mcu_new_protocol_get_stats(&cmd_count, &error_count);
__msg("Commands: %d, Errors: %d\n", cmd_count, error_count);
```

### 打印详细状态

```c
mcu_new_protocol_print_status();
```

## 性能考虑

- **内存使用**: 每个分类处理器占用约 20 字节内存
- **处理速度**: 基于哈希表的快速查找，O(1) 时间复杂度
- **扩展性**: 最多支持 32 个分类处理器 (可通过 `MAX_CATEGORY_HANDLERS` 调整)

## 错误码

- `EPDK_OK`: 成功
- `EPDK_FAIL`: 失败
- 详细错误信息通过调试输出提供

## 注意事项

1. **线程安全**: 当前实现不是线程安全的，如需在多线程环境中使用，请添加互斥锁
2. **内存管理**: 所有数据结构都是静态分配的，不会产生内存泄漏
3. **向后兼容**: 新协议与现有协议完全兼容，不影响现有功能
4. **错误处理**: 建议在处理器函数中添加详细的错误检查和日志记录

## 未来扩展

- 支持动态分类处理器注册/注销
- 添加协议版本协商机制
- 支持分类处理器优先级
- 添加更多的诊断和调试功能
- 支持协议数据的序列化和反序列化

## 联系信息

如有问题或建议，请联系开发团队。

---

*最后更新时间: 2024-12-19*
*版本: v1.0*
*基于: MCU UART 表格化统一命令协议设计文档 v3.0*