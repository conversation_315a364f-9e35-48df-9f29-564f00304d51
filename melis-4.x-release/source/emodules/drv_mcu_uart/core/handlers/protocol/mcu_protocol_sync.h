/*
*********************************************************************************************************
*                                              MELIS RTOS
*                         MCU UART Protocol v2 Sync Framework - Core Header
*
*                                    (c) Copyright 2024, Sunny China
*                                             All Rights Reserved
*
* File    : mcu_protocol_sync.h
* By      : Integration Team
* Version : v1.0
* Date    : 2024-12-XX
* Descript: Protocol v2 synchronization framework for category handlers
* Update  : date                auther      ver     notes
*********************************************************************************************************
*/

#ifndef __MCU_PROTOCOL_SYNC_H__
#define __MCU_PROTOCOL_SYNC_H__

//#include <elibs.h>
#include <typedef.h>

#ifdef __cplusplus
extern "C" {
#endif

/*
*********************************************************************************************************
*                                           SYNC BASE STRUCTURE
*********************************************************************************************************
*/

/**
 * @brief Base synchronization structure for Protocol v2 category handlers
 * This structure tracks data changes without coupling to specific messaging systems
 * Uses atomic-like operations to avoid race conditions between driver and application
 */
typedef struct {
    volatile __u32 dirty_mask_current;    /* Driver side write buffer - tracks current changes */
    volatile __u32 dirty_mask_pending;    /* Application side read buffer - accumulated changes */
    volatile __u32 sequence_number;       /* Data sequence number for app switching detection */
    volatile __u8  msg_in_queue;          /* Flag: 1 if message already in queue, 0 otherwise */
    __u8 reserved[3];                     /* Reserved for alignment */
} mcu_protocol_sync_t;

/*
*********************************************************************************************************
*                                           CORE SYNC MACROS
*********************************************************************************************************
*/

/**
 * @brief Initialize sync structure
 * Usage: MCU_PROTOCOL_SYNC_INIT(&status->sync);
 */
#define MCU_PROTOCOL_SYNC_INIT(sync_ptr) do { \
    (sync_ptr)->dirty_mask_current = 0; \
    (sync_ptr)->dirty_mask_pending = 0; \
    (sync_ptr)->sequence_number = 0; \
    (sync_ptr)->msg_in_queue = 0; \
} while(0)

/**
 * @brief Mark data changes (Driver side usage)
 * Usage: MCU_PROTOCOL_SYNC_MARK(&status->sync, FM_DATA_FREQUENCY | FM_DATA_RDS_PS);
 */
#define MCU_PROTOCOL_SYNC_MARK(sync_ptr, data_bits) \
    mcu_protocol_sync_mark_changed(&(sync_ptr)->dirty_mask_current, (data_bits))

/**
 * @brief Commit changes to pending buffer
 * Usage: __u32 changes = MCU_PROTOCOL_SYNC_COMMIT(&status->sync);
 */
#define MCU_PROTOCOL_SYNC_COMMIT(sync_ptr) \
    mcu_protocol_sync_commit_changes(&(sync_ptr)->dirty_mask_current, \
                                     &(sync_ptr)->dirty_mask_pending, \
                                     &(sync_ptr)->sequence_number)

/**
 * @brief Get and clear pending changes (Application side usage)
 * Usage: __u32 changes = MCU_PROTOCOL_SYNC_GET_CHANGES(&status->sync);
 */
#define MCU_PROTOCOL_SYNC_GET_CHANGES(sync_ptr) \
    mcu_protocol_sync_get_and_clear(&(sync_ptr)->dirty_mask_pending)

/**
 * @brief Check if application switch occurred
 * Usage: if (MCU_PROTOCOL_SYNC_CHECK_SWITCH(&status->sync, &last_seq)) {  full sync }
 */
#define MCU_PROTOCOL_SYNC_CHECK_SWITCH(sync_ptr, last_seq_ptr) \
    mcu_protocol_sync_check_switch((sync_ptr)->sequence_number, (last_seq_ptr))

/**
 * @brief Check if there are any pending changes
 * Usage: if (MCU_PROTOCOL_SYNC_HAS_CHANGES(&status->sync)) { process }
 */
#define MCU_PROTOCOL_SYNC_HAS_CHANGES(sync_ptr) \
    ((sync_ptr)->dirty_mask_pending != 0)

/**
 * @brief Check and set message queue flag atomically
 * Returns 1 if message needs to be queued, 0 if already in queue
 * Usage: if (MCU_PROTOCOL_SYNC_NEED_QUEUE(&status->sync)) { mcu_push_cmd_to_queue(cmd); }
 */
#define MCU_PROTOCOL_SYNC_NEED_QUEUE(sync_ptr) \
    mcu_protocol_sync_check_and_set_queue_flag(&(sync_ptr)->msg_in_queue)

/**
 * @brief Application processes changes and clears queue flag
 * This should be called when application processes the queued message
 * Usage: __u32 changes = MCU_PROTOCOL_SYNC_PROCESS(&status->sync);
 */
#define MCU_PROTOCOL_SYNC_PROCESS(sync_ptr) \
    mcu_protocol_sync_process_changes(&(sync_ptr)->dirty_mask_pending, &(sync_ptr)->msg_in_queue)

/*
*********************************************************************************************************
*                                           CORE INLINE FUNCTIONS
*********************************************************************************************************
*/

/**
 * @brief Mark data changed using bit operations
 * @param current_mask Pointer to current buffer mask
 * @param data_type_bits Changed data type bitmap
 */
static inline void mcu_protocol_sync_mark_changed(volatile __u32 *current_mask, __u32 data_type_bits) 
{
    *current_mask |= data_type_bits;  /* Simple OR operation */
}

/**
 * @brief Commit changes to pending buffer using bit operations
 * @param current_mask Pointer to current buffer mask
 * @param pending_mask Pointer to pending buffer mask
 * @param seq_num Pointer to sequence number
 * @return Committed change bitmap
 */
static inline __u32 mcu_protocol_sync_commit_changes(volatile __u32 *current_mask, 
                                                     volatile __u32 *pending_mask,
                                                     volatile __u32 *seq_num) 
{
    __u32 changes = *current_mask;    /* Read current changes */
    *current_mask = 0;                /* Clear current mask */
    if (changes != 0) {
        *pending_mask |= changes;     /* Accumulate to pending */
        (*seq_num)++;                 /* Increment sequence number */
    }
    return changes;
}

/**
 * @brief Get and clear pending changes using bit operations
 * @param pending_mask Pointer to pending buffer mask
 * @return Change bitmap
 */
static inline __u32 mcu_protocol_sync_get_and_clear(volatile __u32 *pending_mask) 
{
    __u32 changes = *pending_mask;    /* Read pending changes */
    *pending_mask = 0;                /* Clear pending mask */
    return changes;
}

/**
 * @brief Check application switch by sequence number
 * @param seq_num Current sequence number
 * @param last_seq Pointer to last processed sequence number
 * @return EPDK_TRUE: need full sync, EPDK_FALSE: no need
 */
static inline __bool mcu_protocol_sync_check_switch(__u32 seq_num, __u32 *last_seq) 
{
    if (seq_num != *last_seq) {
        *last_seq = seq_num;
        return EPDK_TRUE;  /* Need full sync */
    }
    return EPDK_FALSE;     /* No need for full sync */
}

/**
 * @brief Get change count (number of bits set)
 * @param changes Change bitmap
 * @return Number of changes
 */
static inline __u32 mcu_protocol_sync_count_changes(__u32 changes)
{
    __u32 count = 0;
    while (changes) {
        count++;
        changes &= (changes - 1);  /* Clear lowest set bit */
    }
    return count;
}

/**
 * @brief Check and set queue flag atomically using bit operations
 * @param queue_flag Pointer to queue flag
 * @return 1 if flag was 0 (need to queue), 0 if flag was already 1 (already in queue)
 */
static inline __bool mcu_protocol_sync_check_and_set_queue_flag(volatile __u8 *queue_flag)
{
    __u8 old_val = *queue_flag;
    if (old_val == 0) {
        *queue_flag = 1;
        return EPDK_TRUE;  /* Need to queue */
    }
    return EPDK_FALSE;     /* Already in queue */
}

/**
 * @brief Application processes changes and clears both pending mask and queue flag
 * @param pending_mask Pointer to pending mask
 * @param queue_flag Pointer to queue flag
 * @return Change bitmap that was processed
 */
static inline __u32 mcu_protocol_sync_process_changes(volatile __u32 *pending_mask, 
                                                      volatile __u8 *queue_flag)
{
    __u32 changes = *pending_mask;
    *pending_mask = 0;      /* Clear pending changes */
    *queue_flag = 0;        /* Clear queue flag - allow new messages */
    return changes;
}

#ifdef __cplusplus
}
#endif

#endif /* __MCU_PROTOCOL_SYNC_H__ */