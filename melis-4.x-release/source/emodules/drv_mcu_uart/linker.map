Archive member included to satisfy reference by file (symbol)

./elibrary/bin//libsyscall.a(syscall.o)
                              emodules/drv_mcu_uart/dev_mcu.o (rt_device_register)
./elibrary/bin//libsyscall.a(syscall_fops.o)
                              emodules/drv_mcu_uart/core/mcu_interrupt_process.o (ioctl)
./elibrary/bin//libsyscall.a(syscall.o)
                              emodules/drv_mcu_uart/core/mcu_interrupt_process.o (usleep)
./elibrary/bin//libminic.a(elibs_stdio.o)
                              emodules/drv_mcu_uart/core/mcu_interrupt_process.o (eLIBs_fopen)
./elibrary/bin//libminic.a(elibs_stdlib.o)
                              emodules/drv_mcu_uart/core/mcu_interrupt_process.o (eLIBs_ramdom)
./elibrary/bin//libminic.a(elibs_string.o)
                              emodules/drv_mcu_uart/core/mcu_interrupt_process.o (eLIBs_strlen)
./elibrary/bin//libminic.a(elibs_sprintf.o)
                              ./elibrary/bin//libminic.a(elibs_stdio.o) (eLIBs_vscnprintf)
/home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/../../../../riscv64-unknown-elf/lib/rv64imafdcxtheadc/lp64d/libc.a(lib_a-memcpy.o)
                              emodules/drv_mcu_uart/core/mcu_interrupt_process.o (memcpy)
/home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/../../../../riscv64-unknown-elf/lib/rv64imafdcxtheadc/lp64d/libc.a(lib_a-memset.o)
                              emodules/drv_mcu_uart/core/mcu_interrupt_process.o (memset)
/home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/../../../../riscv64-unknown-elf/lib/rv64imafdcxtheadc/lp64d/libc.a(lib_a-printf.o)
                              emodules/drv_mcu_uart/core/mcu_interrupt_process.o (printf)
/home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/../../../../riscv64-unknown-elf/lib/rv64imafdcxtheadc/lp64d/libc.a(lib_a-sysclose.o)
                              emodules/drv_mcu_uart/core/mcu_interrupt_process.o (close)
/home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/../../../../riscv64-unknown-elf/lib/rv64imafdcxtheadc/lp64d/libc.a(lib_a-sysopen.o)
                              emodules/drv_mcu_uart/core/mcu_interrupt_process.o (open)
/home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/../../../../riscv64-unknown-elf/lib/rv64imafdcxtheadc/lp64d/libc.a(lib_a-vfprintf.o)
                              /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/../../../../riscv64-unknown-elf/lib/rv64imafdcxtheadc/lp64d/libc.a(lib_a-printf.o) (_vfprintf_r)
/home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/../../../../riscv64-unknown-elf/lib/rv64imafdcxtheadc/lp64d/libc.a(lib_a-wsetup.o)
                              /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/../../../../riscv64-unknown-elf/lib/rv64imafdcxtheadc/lp64d/libc.a(lib_a-vfprintf.o) (__swsetup_r)
/home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/../../../../riscv64-unknown-elf/lib/rv64imafdcxtheadc/lp64d/libc.a(lib_a-fflush.o)
                              /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/../../../../riscv64-unknown-elf/lib/rv64imafdcxtheadc/lp64d/libc.a(lib_a-vfprintf.o) (_fflush_r)
/home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/../../../../riscv64-unknown-elf/lib/rv64imafdcxtheadc/lp64d/libc.a(lib_a-findfp.o)
                              /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/../../../../riscv64-unknown-elf/lib/rv64imafdcxtheadc/lp64d/libc.a(lib_a-vfprintf.o) (__sinit)
/home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/../../../../riscv64-unknown-elf/lib/rv64imafdcxtheadc/lp64d/libc.a(lib_a-fwalk.o)
                              /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/../../../../riscv64-unknown-elf/lib/rv64imafdcxtheadc/lp64d/libc.a(lib_a-findfp.o) (_fwalk)
/home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/../../../../riscv64-unknown-elf/lib/rv64imafdcxtheadc/lp64d/libc.a(lib_a-impure.o)
                              /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/../../../../riscv64-unknown-elf/lib/rv64imafdcxtheadc/lp64d/libc.a(lib_a-fflush.o) (_global_impure_ptr)
/home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/../../../../riscv64-unknown-elf/lib/rv64imafdcxtheadc/lp64d/libc.a(lib_a-ldtoa.o)
                              /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/../../../../riscv64-unknown-elf/lib/rv64imafdcxtheadc/lp64d/libc.a(lib_a-vfprintf.o) (_ldtoa_r)
/home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/../../../../riscv64-unknown-elf/lib/rv64imafdcxtheadc/lp64d/libc.a(lib_a-localeconv.o)
                              /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/../../../../riscv64-unknown-elf/lib/rv64imafdcxtheadc/lp64d/libc.a(lib_a-vfprintf.o) (_localeconv_r)
/home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/../../../../riscv64-unknown-elf/lib/rv64imafdcxtheadc/lp64d/libc.a(lib_a-locale.o)
                              /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/../../../../riscv64-unknown-elf/lib/rv64imafdcxtheadc/lp64d/libc.a(lib_a-localeconv.o) (__global_locale)
/home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/../../../../riscv64-unknown-elf/lib/rv64imafdcxtheadc/lp64d/libc.a(lib_a-makebuf.o)
                              /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/../../../../riscv64-unknown-elf/lib/rv64imafdcxtheadc/lp64d/libc.a(lib_a-wsetup.o) (__smakebuf_r)
/home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/../../../../riscv64-unknown-elf/lib/rv64imafdcxtheadc/lp64d/libc.a(lib_a-mbtowc_r.o)
                              /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/../../../../riscv64-unknown-elf/lib/rv64imafdcxtheadc/lp64d/libc.a(lib_a-locale.o) (__ascii_mbtowc)
/home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/../../../../riscv64-unknown-elf/lib/rv64imafdcxtheadc/lp64d/libc.a(lib_a-memchr.o)
                              /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/../../../../riscv64-unknown-elf/lib/rv64imafdcxtheadc/lp64d/libc.a(lib_a-vfprintf.o) (memchr)
/home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/../../../../riscv64-unknown-elf/lib/rv64imafdcxtheadc/lp64d/libc.a(lib_a-mprec.o)
                              /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/../../../../riscv64-unknown-elf/lib/rv64imafdcxtheadc/lp64d/libc.a(lib_a-ldtoa.o) (_Balloc)
/home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/../../../../riscv64-unknown-elf/lib/rv64imafdcxtheadc/lp64d/libc.a(lib_a-s_frexp.o)
                              /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/../../../../riscv64-unknown-elf/lib/rv64imafdcxtheadc/lp64d/libc.a(lib_a-vfprintf.o) (frexp)
/home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/../../../../riscv64-unknown-elf/lib/rv64imafdcxtheadc/lp64d/libc.a(lib_a-sprintf.o)
                              /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/../../../../riscv64-unknown-elf/lib/rv64imafdcxtheadc/lp64d/libc.a(lib_a-ldtoa.o) (sprintf)
/home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/../../../../riscv64-unknown-elf/lib/rv64imafdcxtheadc/lp64d/libc.a(lib_a-stdio.o)
                              /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/../../../../riscv64-unknown-elf/lib/rv64imafdcxtheadc/lp64d/libc.a(lib_a-findfp.o) (__sread)
/home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/../../../../riscv64-unknown-elf/lib/rv64imafdcxtheadc/lp64d/libc.a(lib_a-strcmp.o)
                              /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/../../../../riscv64-unknown-elf/lib/rv64imafdcxtheadc/lp64d/libc.a(lib_a-locale.o) (strcmp)
/home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/../../../../riscv64-unknown-elf/lib/rv64imafdcxtheadc/lp64d/libc.a(lib_a-strcpy.o)
                              /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/../../../../riscv64-unknown-elf/lib/rv64imafdcxtheadc/lp64d/libc.a(lib_a-ldtoa.o) (strcpy)
/home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/../../../../riscv64-unknown-elf/lib/rv64imafdcxtheadc/lp64d/libc.a(lib_a-strlen.o)
                              /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/../../../../riscv64-unknown-elf/lib/rv64imafdcxtheadc/lp64d/libc.a(lib_a-vfprintf.o) (strlen)
/home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/../../../../riscv64-unknown-elf/lib/rv64imafdcxtheadc/lp64d/libc.a(lib_a-strncpy.o)
                              /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/../../../../riscv64-unknown-elf/lib/rv64imafdcxtheadc/lp64d/libc.a(lib_a-vfprintf.o) (strncpy)
/home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/../../../../riscv64-unknown-elf/lib/rv64imafdcxtheadc/lp64d/libc.a(lib_a-svfprintf.o)
                              /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/../../../../riscv64-unknown-elf/lib/rv64imafdcxtheadc/lp64d/libc.a(lib_a-sprintf.o) (_svfprintf_r)
/home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/../../../../riscv64-unknown-elf/lib/rv64imafdcxtheadc/lp64d/libc.a(lib_a-vfiprintf.o)
                              /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/../../../../riscv64-unknown-elf/lib/rv64imafdcxtheadc/lp64d/libc.a(lib_a-vfprintf.o) (__sprint_r)
/home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/../../../../riscv64-unknown-elf/lib/rv64imafdcxtheadc/lp64d/libc.a(lib_a-wctomb_r.o)
                              /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/../../../../riscv64-unknown-elf/lib/rv64imafdcxtheadc/lp64d/libc.a(lib_a-locale.o) (__ascii_wctomb)
/home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/../../../../riscv64-unknown-elf/lib/rv64imafdcxtheadc/lp64d/libc.a(lib_a-ctype_.o)
                              /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/../../../../riscv64-unknown-elf/lib/rv64imafdcxtheadc/lp64d/libc.a(lib_a-locale.o) (_ctype_)
/home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/../../../../riscv64-unknown-elf/lib/rv64imafdcxtheadc/lp64d/libc.a(lib_a-fclose.o)
                              /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/../../../../riscv64-unknown-elf/lib/rv64imafdcxtheadc/lp64d/libc.a(lib_a-findfp.o) (_fclose_r)
/home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/../../../../riscv64-unknown-elf/lib/rv64imafdcxtheadc/lp64d/libc.a(lib_a-fputwc.o)
                              /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/../../../../riscv64-unknown-elf/lib/rv64imafdcxtheadc/lp64d/libc.a(lib_a-vfiprintf.o) (_fputwc_r)
/home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/../../../../riscv64-unknown-elf/lib/rv64imafdcxtheadc/lp64d/libc.a(lib_a-fvwrite.o)
                              /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/../../../../riscv64-unknown-elf/lib/rv64imafdcxtheadc/lp64d/libc.a(lib_a-vfiprintf.o) (__sfvwrite_r)
/home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/../../../../riscv64-unknown-elf/lib/rv64imafdcxtheadc/lp64d/libc.a(lib_a-memmove-stub.o)
                              /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/../../../../riscv64-unknown-elf/lib/rv64imafdcxtheadc/lp64d/libc.a(lib_a-fvwrite.o) (memmove)
/home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/../../../../riscv64-unknown-elf/lib/rv64imafdcxtheadc/lp64d/libc.a(lib_a-svfiprintf.o)
                              /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/../../../../riscv64-unknown-elf/lib/rv64imafdcxtheadc/lp64d/libc.a(lib_a-svfprintf.o) (__ssprint_r)
/home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/../../../../riscv64-unknown-elf/lib/rv64imafdcxtheadc/lp64d/libc.a(lib_a-wbuf.o)
                              /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/../../../../riscv64-unknown-elf/lib/rv64imafdcxtheadc/lp64d/libc.a(lib_a-fputwc.o) (__swbuf_r)
/home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/../../../../riscv64-unknown-elf/lib/rv64imafdcxtheadc/lp64d/libc.a(lib_a-wcrtomb.o)
                              /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/../../../../riscv64-unknown-elf/lib/rv64imafdcxtheadc/lp64d/libc.a(lib_a-fputwc.o) (_wcrtomb_r)
/home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/rv64imafdcxtheadc/lp64d/libgcc.a(eqtf2.o)
                              /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/../../../../riscv64-unknown-elf/lib/rv64imafdcxtheadc/lp64d/libc.a(lib_a-vfprintf.o) (__eqtf2)
/home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/rv64imafdcxtheadc/lp64d/libgcc.a(getf2.o)
                              /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/../../../../riscv64-unknown-elf/lib/rv64imafdcxtheadc/lp64d/libc.a(lib_a-vfprintf.o) (__gttf2)
/home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/rv64imafdcxtheadc/lp64d/libgcc.a(letf2.o)
                              /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/../../../../riscv64-unknown-elf/lib/rv64imafdcxtheadc/lp64d/libc.a(lib_a-vfprintf.o) (__lttf2)
/home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/rv64imafdcxtheadc/lp64d/libgcc.a(multf3.o)
                              /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/../../../../riscv64-unknown-elf/lib/rv64imafdcxtheadc/lp64d/libc.a(lib_a-vfprintf.o) (__multf3)
/home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/rv64imafdcxtheadc/lp64d/libgcc.a(subtf3.o)
                              /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/../../../../riscv64-unknown-elf/lib/rv64imafdcxtheadc/lp64d/libc.a(lib_a-vfprintf.o) (__subtf3)
/home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/rv64imafdcxtheadc/lp64d/libgcc.a(fixtfsi.o)
                              /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/../../../../riscv64-unknown-elf/lib/rv64imafdcxtheadc/lp64d/libc.a(lib_a-vfprintf.o) (__fixtfsi)
/home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/rv64imafdcxtheadc/lp64d/libgcc.a(floatsitf.o)
                              /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/../../../../riscv64-unknown-elf/lib/rv64imafdcxtheadc/lp64d/libc.a(lib_a-vfprintf.o) (__floatsitf)
/home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/rv64imafdcxtheadc/lp64d/libgcc.a(extenddftf2.o)
                              /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/../../../../riscv64-unknown-elf/lib/rv64imafdcxtheadc/lp64d/libc.a(lib_a-vfprintf.o) (__extenddftf2)
/home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/rv64imafdcxtheadc/lp64d/libgcc.a(trunctfdf2.o)
                              /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/../../../../riscv64-unknown-elf/lib/rv64imafdcxtheadc/lp64d/libc.a(lib_a-vfprintf.o) (__trunctfdf2)
/home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/rv64imafdcxtheadc/lp64d/libgcc.a(_clzsi2.o)
                              /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/rv64imafdcxtheadc/lp64d/libgcc.a(multf3.o) (__clzdi2)
/home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/rv64imafdcxtheadc/lp64d/libgcc.a(_clz.o)
                              /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/rv64imafdcxtheadc/lp64d/libgcc.a(_clzsi2.o) (__clz_tab)

Discarded input sections

 .text          0x0000000000000000        0x0 emodules/drv_mcu_uart/dev_mcu.o
 .data          0x0000000000000000        0x0 emodules/drv_mcu_uart/dev_mcu.o
 .bss           0x0000000000000000        0x0 emodules/drv_mcu_uart/dev_mcu.o
 .text          0x0000000000000000        0x0 emodules/drv_mcu_uart/drv_mcu.o
 .data          0x0000000000000000        0x0 emodules/drv_mcu_uart/drv_mcu.o
 .bss           0x0000000000000000        0x0 emodules/drv_mcu_uart/drv_mcu.o
 .text          0x0000000000000000        0x0 emodules/drv_mcu_uart/magic.o
 .data          0x0000000000000000        0x0 emodules/drv_mcu_uart/magic.o
 .bss           0x0000000000000000        0x0 emodules/drv_mcu_uart/magic.o
 .text          0x0000000000000000        0x0 emodules/drv_mcu_uart/core/mcu_interrupt_process.o
 .data          0x0000000000000000        0x0 emodules/drv_mcu_uart/core/mcu_interrupt_process.o
 .bss           0x0000000000000000        0x0 emodules/drv_mcu_uart/core/mcu_interrupt_process.o
 .text.sxm_bitbuff_ensure
                0x0000000000000000       0x6e emodules/drv_mcu_uart/core/mcu_interrupt_process.o
 .text.mcu_print_memery
                0x0000000000000000       0x70 emodules/drv_mcu_uart/core/mcu_interrupt_process.o
 .text.check_memzero
                0x0000000000000000       0x18 emodules/drv_mcu_uart/core/mcu_interrupt_process.o
 .text.uart3_tx2gpio
                0x0000000000000000       0x48 emodules/drv_mcu_uart/core/mcu_interrupt_process.o
 .text.calc_crc16
                0x0000000000000000       0x40 emodules/drv_mcu_uart/core/mcu_interrupt_process.o
 .text.uart_send_data
                0x0000000000000000       0xb8 emodules/drv_mcu_uart/core/mcu_interrupt_process.o
 .text.uart_sub_send_data
                0x0000000000000000       0x7c emodules/drv_mcu_uart/core/mcu_interrupt_process.o
 .text.mcu_set_lcd_off
                0x0000000000000000       0x42 emodules/drv_mcu_uart/core/mcu_interrupt_process.o
 .text.get_mcu_ack_pro
                0x0000000000000000        0x4 emodules/drv_mcu_uart/core/mcu_interrupt_process.o
 .text.send_ack_to_mcu
                0x0000000000000000        0x4 emodules/drv_mcu_uart/core/mcu_interrupt_process.o
 .text.push_data_to_queue
                0x0000000000000000        0x4 emodules/drv_mcu_uart/core/mcu_interrupt_process.o
 .text.pop_data_from_queue
                0x0000000000000000        0x4 emodules/drv_mcu_uart/core/mcu_interrupt_process.o
 .text.sxm_crc32_part
                0x0000000000000000       0x2e emodules/drv_mcu_uart/core/mcu_interrupt_process.o
 .text.sxm_crc32_check
                0x0000000000000000       0x58 emodules/drv_mcu_uart/core/mcu_interrupt_process.o
 .text.sxm_bitbuff_align
                0x0000000000000000        0x8 emodules/drv_mcu_uart/core/mcu_interrupt_process.o
 .text.sxm_bitbuff_setup
                0x0000000000000000       0x18 emodules/drv_mcu_uart/core/mcu_interrupt_process.o
 .text.sxm_bitbuff_read
                0x0000000000000000       0x66 emodules/drv_mcu_uart/core/mcu_interrupt_process.o
 .text.sxm_bitbuff_baudot
                0x0000000000000000       0xe4 emodules/drv_mcu_uart/core/mcu_interrupt_process.o
 .text.handle_image_data
                0x0000000000000000      0x32a emodules/drv_mcu_uart/core/mcu_interrupt_process.o
 .text.sxm_pix_complete_au
                0x0000000000000000       0x80 emodules/drv_mcu_uart/core/mcu_interrupt_process.o
 .text.sxm_find_au_index
                0x0000000000000000       0x3e emodules/drv_mcu_uart/core/mcu_interrupt_process.o
 .bss.AlbumArtBuf
                0x0000000000000000     0x4014 emodules/drv_mcu_uart/core/mcu_interrupt_process.o
 .bss.get_cmd_err
                0x0000000000000000        0x1 emodules/drv_mcu_uart/core/mcu_interrupt_process.o
 .bss.last_get_cmd_no
                0x0000000000000000        0x4 emodules/drv_mcu_uart/core/mcu_interrupt_process.o
 .bss.last_sdtp_header
                0x0000000000000000        0x4 emodules/drv_mcu_uart/core/mcu_interrupt_process.o
 .bss.send_data
                0x0000000000000000        0x8 emodules/drv_mcu_uart/core/mcu_interrupt_process.o
 .data.sxm_baudot_table
                0x0000000000000000       0xc0 emodules/drv_mcu_uart/core/mcu_interrupt_process.o
 .rodata.mcu_print_memery.str1.8
                0x0000000000000000       0xd5 emodules/drv_mcu_uart/core/mcu_interrupt_process.o
 .text          0x0000000000000000        0x0 emodules/drv_mcu_uart/core/mcu_aux_init_para.o
 .data          0x0000000000000000        0x0 emodules/drv_mcu_uart/core/mcu_aux_init_para.o
 .bss           0x0000000000000000        0x0 emodules/drv_mcu_uart/core/mcu_aux_init_para.o
 .text          0x0000000000000000        0x0 emodules/drv_mcu_uart/core/mcu_bypass_cmd_proc.o
 .data          0x0000000000000000        0x0 emodules/drv_mcu_uart/core/mcu_bypass_cmd_proc.o
 .bss           0x0000000000000000        0x0 emodules/drv_mcu_uart/core/mcu_bypass_cmd_proc.o
 .text.hu_pass2remote_fw_version
                0x0000000000000000       0x6a emodules/drv_mcu_uart/core/mcu_bypass_cmd_proc.o
 .text.hu_pass2remote_usb_status
                0x0000000000000000       0x40 emodules/drv_mcu_uart/core/mcu_bypass_cmd_proc.o
 .rodata.hu_pass2remote_fw_version.str1.8
                0x0000000000000000        0xe emodules/drv_mcu_uart/core/mcu_bypass_cmd_proc.o
 .text          0x0000000000000000        0x0 emodules/drv_mcu_uart/core/mcu_update_proc.o
 .data          0x0000000000000000        0x0 emodules/drv_mcu_uart/core/mcu_update_proc.o
 .bss           0x0000000000000000        0x0 emodules/drv_mcu_uart/core/mcu_update_proc.o
 .text          0x0000000000000000        0x0 emodules/drv_mcu_uart/core/mcu_cmd_dispatcher.o
 .data          0x0000000000000000        0x0 emodules/drv_mcu_uart/core/mcu_cmd_dispatcher.o
 .bss           0x0000000000000000        0x0 emodules/drv_mcu_uart/core/mcu_cmd_dispatcher.o
 .text.mcu_cmd_get_stats
                0x0000000000000000       0x24 emodules/drv_mcu_uart/core/mcu_cmd_dispatcher.o
 .text.mcu_cmd_reset_stats
                0x0000000000000000       0x14 emodules/drv_mcu_uart/core/mcu_cmd_dispatcher.o
 .text.mcu_cmd_print_status
                0x0000000000000000        0x2 emodules/drv_mcu_uart/core/mcu_cmd_dispatcher.o
 .text          0x0000000000000000        0x0 emodules/drv_mcu_uart/core/handlers/system/mcu_system_handler.o
 .data          0x0000000000000000        0x0 emodules/drv_mcu_uart/core/handlers/system/mcu_system_handler.o
 .bss           0x0000000000000000        0x0 emodules/drv_mcu_uart/core/handlers/system/mcu_system_handler.o
 .text.mcu_system_handler_register
                0x0000000000000000       0x1c emodules/drv_mcu_uart/core/handlers/system/mcu_system_handler.o
 .rodata.mcu_system_handler_register.str1.8
                0x0000000000000000        0xe emodules/drv_mcu_uart/core/handlers/system/mcu_system_handler.o
 .text          0x0000000000000000        0x0 emodules/drv_mcu_uart/core/handlers/media/mcu_tuner_handler.o
 .data          0x0000000000000000        0x0 emodules/drv_mcu_uart/core/handlers/media/mcu_tuner_handler.o
 .bss           0x0000000000000000        0x0 emodules/drv_mcu_uart/core/handlers/media/mcu_tuner_handler.o
 .text.mcu_tuner_handler_register
                0x0000000000000000       0x20 emodules/drv_mcu_uart/core/handlers/media/mcu_tuner_handler.o
 .rodata.mcu_tuner_handler_register.str1.8
                0x0000000000000000        0xd emodules/drv_mcu_uart/core/handlers/media/mcu_tuner_handler.o
 .text          0x0000000000000000        0x0 emodules/drv_mcu_uart/core/handlers/media/mcu_usb_handler.o
 .data          0x0000000000000000        0x0 emodules/drv_mcu_uart/core/handlers/media/mcu_usb_handler.o
 .bss           0x0000000000000000        0x0 emodules/drv_mcu_uart/core/handlers/media/mcu_usb_handler.o
 .text.mcu_usb_handler_register
                0x0000000000000000       0x20 emodules/drv_mcu_uart/core/handlers/media/mcu_usb_handler.o
 .rodata.mcu_usb_handler_register.str1.8
                0x0000000000000000        0xb emodules/drv_mcu_uart/core/handlers/media/mcu_usb_handler.o
 .text          0x0000000000000000        0x0 emodules/drv_mcu_uart/core/handlers/bluetooth/mcu_bt_handler.o
 .data          0x0000000000000000        0x0 emodules/drv_mcu_uart/core/handlers/bluetooth/mcu_bt_handler.o
 .bss           0x0000000000000000        0x0 emodules/drv_mcu_uart/core/handlers/bluetooth/mcu_bt_handler.o
 .text.mcu_bt_handler_register
                0x0000000000000000       0x20 emodules/drv_mcu_uart/core/handlers/bluetooth/mcu_bt_handler.o
 .rodata.mcu_bt_handler_register.str1.8
                0x0000000000000000       0x11 emodules/drv_mcu_uart/core/handlers/bluetooth/mcu_bt_handler.o
 .text          0x0000000000000000        0x0 emodules/drv_mcu_uart/core/handlers/sxm/mcu_sxm_handler.o
 .data          0x0000000000000000        0x0 emodules/drv_mcu_uart/core/handlers/sxm/mcu_sxm_handler.o
 .bss           0x0000000000000000        0x0 emodules/drv_mcu_uart/core/handlers/sxm/mcu_sxm_handler.o
 .text.mcu_sxm_handler_register
                0x0000000000000000       0x20 emodules/drv_mcu_uart/core/handlers/sxm/mcu_sxm_handler.o
 .rodata.mcu_sxm_handler_register.str1.8
                0x0000000000000000        0xb emodules/drv_mcu_uart/core/handlers/sxm/mcu_sxm_handler.o
 .text          0x0000000000000000        0x0 emodules/drv_mcu_uart/core/handlers/settings/mcu_settings_handler.o
 .data          0x0000000000000000        0x0 emodules/drv_mcu_uart/core/handlers/settings/mcu_settings_handler.o
 .bss           0x0000000000000000        0x0 emodules/drv_mcu_uart/core/handlers/settings/mcu_settings_handler.o
 .text.mcu_settings_handler_register
                0x0000000000000000       0x20 emodules/drv_mcu_uart/core/handlers/settings/mcu_settings_handler.o
 .rodata.mcu_settings_handler_register.str1.8
                0x0000000000000000       0x10 emodules/drv_mcu_uart/core/handlers/settings/mcu_settings_handler.o
 .text          0x0000000000000000        0x0 emodules/drv_mcu_uart/core/handlers/audio/mcu_audio_handler.o
 .data          0x0000000000000000        0x0 emodules/drv_mcu_uart/core/handlers/audio/mcu_audio_handler.o
 .bss           0x0000000000000000        0x0 emodules/drv_mcu_uart/core/handlers/audio/mcu_audio_handler.o
 .text.mcu_audio_handler_register
                0x0000000000000000       0x1e emodules/drv_mcu_uart/core/handlers/audio/mcu_audio_handler.o
 .rodata.mcu_audio_handler_register.str1.8
                0x0000000000000000        0xd emodules/drv_mcu_uart/core/handlers/audio/mcu_audio_handler.o
 .text          0x0000000000000000        0x0 emodules/drv_mcu_uart/core/handlers/upgrade/mcu_upgrade_handler.o
 .data          0x0000000000000000        0x0 emodules/drv_mcu_uart/core/handlers/upgrade/mcu_upgrade_handler.o
 .bss           0x0000000000000000        0x0 emodules/drv_mcu_uart/core/handlers/upgrade/mcu_upgrade_handler.o
 .text.mcu_upgrade_handler_register
                0x0000000000000000       0x22 emodules/drv_mcu_uart/core/handlers/upgrade/mcu_upgrade_handler.o
 .rodata.mcu_upgrade_handler_register.str1.8
                0x0000000000000000        0xf emodules/drv_mcu_uart/core/handlers/upgrade/mcu_upgrade_handler.o
 .text          0x0000000000000000        0x0 emodules/drv_mcu_uart/core/handlers/bypass/mcu_bypass_handler.o
 .data          0x0000000000000000        0x0 emodules/drv_mcu_uart/core/handlers/bypass/mcu_bypass_handler.o
 .bss           0x0000000000000000        0x0 emodules/drv_mcu_uart/core/handlers/bypass/mcu_bypass_handler.o
 .text.mcu_bypass_handler_register
                0x0000000000000000       0x1c emodules/drv_mcu_uart/core/handlers/bypass/mcu_bypass_handler.o
 .rodata.mcu_bypass_handler_register.str1.8
                0x0000000000000000        0xe emodules/drv_mcu_uart/core/handlers/bypass/mcu_bypass_handler.o
 .text          0x0000000000000000        0x0 emodules/drv_mcu_uart/core/handlers/misc/mcu_misc_handler.o
 .data          0x0000000000000000        0x0 emodules/drv_mcu_uart/core/handlers/misc/mcu_misc_handler.o
 .bss           0x0000000000000000        0x0 emodules/drv_mcu_uart/core/handlers/misc/mcu_misc_handler.o
 .text.mcu_misc_handler_register
                0x0000000000000000       0x1e emodules/drv_mcu_uart/core/handlers/misc/mcu_misc_handler.o
 .rodata.mcu_misc_handler_register.str1.8
                0x0000000000000000        0xc emodules/drv_mcu_uart/core/handlers/misc/mcu_misc_handler.o
 .text          0x0000000000000000        0x0 emodules/drv_mcu_uart/core/handlers/protocol/mcu_new_protocol_handler.o
 .data          0x0000000000000000        0x0 emodules/drv_mcu_uart/core/handlers/protocol/mcu_new_protocol_handler.o
 .bss           0x0000000000000000        0x0 emodules/drv_mcu_uart/core/handlers/protocol/mcu_new_protocol_handler.o
 .text.isCPUbigEndian
                0x0000000000000000        0x4 emodules/drv_mcu_uart/core/handlers/protocol/mcu_new_protocol_handler.o
 .text.mcu_new_protocol_get_stats
                0x0000000000000000       0x1e emodules/drv_mcu_uart/core/handlers/protocol/mcu_new_protocol_handler.o
 .text.mcu_new_protocol_reset_stats
                0x0000000000000000       0x12 emodules/drv_mcu_uart/core/handlers/protocol/mcu_new_protocol_handler.o
 .text.mcu_new_protocol_print_status
                0x0000000000000000        0x2 emodules/drv_mcu_uart/core/handlers/protocol/mcu_new_protocol_handler.o
 .text          0x0000000000000000        0x0 emodules/drv_mcu_uart/core/handlers/protocol/system/mcu_system_category_handler.o
 .data          0x0000000000000000        0x0 emodules/drv_mcu_uart/core/handlers/protocol/system/mcu_system_category_handler.o
 .bss           0x0000000000000000        0x0 emodules/drv_mcu_uart/core/handlers/protocol/system/mcu_system_category_handler.o
 .text.system_set_source
                0x0000000000000000       0x24 emodules/drv_mcu_uart/core/handlers/protocol/system/mcu_system_category_handler.o
 .text.system_get_current_source
                0x0000000000000000       0x10 emodules/drv_mcu_uart/core/handlers/protocol/system/mcu_system_category_handler.o
 .text.system_set_model
                0x0000000000000000       0x12 emodules/drv_mcu_uart/core/handlers/protocol/system/mcu_system_category_handler.o
 .text.system_get_current_model
                0x0000000000000000       0x10 emodules/drv_mcu_uart/core/handlers/protocol/system/mcu_system_category_handler.o
 .text.system_set_source_enable
                0x0000000000000000       0x12 emodules/drv_mcu_uart/core/handlers/protocol/system/mcu_system_category_handler.o
 .text.system_set_dimmer_switch
                0x0000000000000000       0x1c emodules/drv_mcu_uart/core/handlers/protocol/system/mcu_system_category_handler.o
 .text.system_set_tft_dimmer_on_level
                0x0000000000000000       0x24 emodules/drv_mcu_uart/core/handlers/protocol/system/mcu_system_category_handler.o
 .text.system_set_tft_dimmer_off_level
                0x0000000000000000       0x24 emodules/drv_mcu_uart/core/handlers/protocol/system/mcu_system_category_handler.o
 .text.system_set_button_dimmer_on_level
                0x0000000000000000       0x24 emodules/drv_mcu_uart/core/handlers/protocol/system/mcu_system_category_handler.o
 .text.system_set_button_dimmer_off_level
                0x0000000000000000       0x24 emodules/drv_mcu_uart/core/handlers/protocol/system/mcu_system_category_handler.o
 .text.system_set_power_state
                0x0000000000000000       0x1c emodules/drv_mcu_uart/core/handlers/protocol/system/mcu_system_category_handler.o
 .text.system_set_theme
                0x0000000000000000       0x1c emodules/drv_mcu_uart/core/handlers/protocol/system/mcu_system_category_handler.o
 .text.system_get_current_theme
                0x0000000000000000       0x10 emodules/drv_mcu_uart/core/handlers/protocol/system/mcu_system_category_handler.o
 .text.system_set_language
                0x0000000000000000       0x1c emodules/drv_mcu_uart/core/handlers/protocol/system/mcu_system_category_handler.o
 .text.system_get_version
                0x0000000000000000        0xa emodules/drv_mcu_uart/core/handlers/protocol/system/mcu_system_category_handler.o
 .text.system_get_factory_info
                0x0000000000000000        0xa emodules/drv_mcu_uart/core/handlers/protocol/system/mcu_system_category_handler.o
 .text          0x0000000000000000        0x0 emodules/drv_mcu_uart/core/handlers/protocol/audio/mcu_audio_category_handler.o
 .data          0x0000000000000000        0x0 emodules/drv_mcu_uart/core/handlers/protocol/audio/mcu_audio_category_handler.o
 .bss           0x0000000000000000        0x0 emodules/drv_mcu_uart/core/handlers/protocol/audio/mcu_audio_category_handler.o
 .text.audio_get_status
                0x0000000000000000       0x28 emodules/drv_mcu_uart/core/handlers/protocol/audio/mcu_audio_category_handler.o
 .text.audio_set_main_volume
                0x0000000000000000       0x1e emodules/drv_mcu_uart/core/handlers/protocol/audio/mcu_audio_category_handler.o
 .text.audio_get_main_volume
                0x0000000000000000       0x10 emodules/drv_mcu_uart/core/handlers/protocol/audio/mcu_audio_category_handler.o
 .text.audio_set_zone_volume
                0x0000000000000000       0x38 emodules/drv_mcu_uart/core/handlers/protocol/audio/mcu_audio_category_handler.o
 .text.audio_get_zone_volume
                0x0000000000000000       0x2a emodules/drv_mcu_uart/core/handlers/protocol/audio/mcu_audio_category_handler.o
 .text.audio_set_subwoofer_volume
                0x0000000000000000       0x1c emodules/drv_mcu_uart/core/handlers/protocol/audio/mcu_audio_category_handler.o
 .text.audio_get_subwoofer_volume
                0x0000000000000000       0x10 emodules/drv_mcu_uart/core/handlers/protocol/audio/mcu_audio_category_handler.o
 .text.audio_set_mute_state
                0x0000000000000000       0x1c emodules/drv_mcu_uart/core/handlers/protocol/audio/mcu_audio_category_handler.o
 .text.audio_get_mute_state
                0x0000000000000000       0x10 emodules/drv_mcu_uart/core/handlers/protocol/audio/mcu_audio_category_handler.o
 .text.audio_set_source_gain
                0x0000000000000000       0x3a emodules/drv_mcu_uart/core/handlers/protocol/audio/mcu_audio_category_handler.o
 .text.audio_get_source_gain
                0x0000000000000000       0x20 emodules/drv_mcu_uart/core/handlers/protocol/audio/mcu_audio_category_handler.o
 .text.audio_set_equalizer
                0x0000000000000000       0x8e emodules/drv_mcu_uart/core/handlers/protocol/audio/mcu_audio_category_handler.o
 .text.audio_get_equalizer
                0x0000000000000000       0x4a emodules/drv_mcu_uart/core/handlers/protocol/audio/mcu_audio_category_handler.o
 .text.audio_set_eq_type_gains
                0x0000000000000000       0x80 emodules/drv_mcu_uart/core/handlers/protocol/audio/mcu_audio_category_handler.o
 .text.audio_get_eq_type_gains
                0x0000000000000000       0x62 emodules/drv_mcu_uart/core/handlers/protocol/audio/mcu_audio_category_handler.o
 .text.audio_switch_eq_type
                0x0000000000000000       0x3e emodules/drv_mcu_uart/core/handlers/protocol/audio/mcu_audio_category_handler.o
 .text.audio_set_balance
                0x0000000000000000       0x28 emodules/drv_mcu_uart/core/handlers/protocol/audio/mcu_audio_category_handler.o
 .text.audio_get_balance
                0x0000000000000000       0x14 emodules/drv_mcu_uart/core/handlers/protocol/audio/mcu_audio_category_handler.o
 .text.audio_set_fade
                0x0000000000000000       0x28 emodules/drv_mcu_uart/core/handlers/protocol/audio/mcu_audio_category_handler.o
 .text.audio_get_fade
                0x0000000000000000       0x14 emodules/drv_mcu_uart/core/handlers/protocol/audio/mcu_audio_category_handler.o
 .text.audio_set_crossover
                0x0000000000000000       0x26 emodules/drv_mcu_uart/core/handlers/protocol/audio/mcu_audio_category_handler.o
 .text.audio_get_crossover
                0x0000000000000000       0x1c emodules/drv_mcu_uart/core/handlers/protocol/audio/mcu_audio_category_handler.o
 .text.audio_set_subwoofer_switch
                0x0000000000000000       0x20 emodules/drv_mcu_uart/core/handlers/protocol/audio/mcu_audio_category_handler.o
 .text.audio_set_subwoofer_phase
                0x0000000000000000       0x1c emodules/drv_mcu_uart/core/handlers/protocol/audio/mcu_audio_category_handler.o
 .text.audio_set_zones_enable
                0x0000000000000000       0x18 emodules/drv_mcu_uart/core/handlers/protocol/audio/mcu_audio_category_handler.o
 .text.audio_get_zones_enable
                0x0000000000000000       0x14 emodules/drv_mcu_uart/core/handlers/protocol/audio/mcu_audio_category_handler.o
 .text.audio_reset_all_settings
                0x0000000000000000       0x42 emodules/drv_mcu_uart/core/handlers/protocol/audio/mcu_audio_category_handler.o
 .text          0x0000000000000000        0x0 emodules/drv_mcu_uart/core/handlers/protocol/tuner/mcu_tuner_category_handler.o
 .data          0x0000000000000000        0x0 emodules/drv_mcu_uart/core/handlers/protocol/tuner/mcu_tuner_category_handler.o
 .bss           0x0000000000000000        0x0 emodules/drv_mcu_uart/core/handlers/protocol/tuner/mcu_tuner_category_handler.o
 .text.fm_get_status
                0x0000000000000000       0x26 emodules/drv_mcu_uart/core/handlers/protocol/tuner/mcu_tuner_category_handler.o
 .text.am_get_status
                0x0000000000000000       0x26 emodules/drv_mcu_uart/core/handlers/protocol/tuner/mcu_tuner_category_handler.o
 .text.wb_get_status
                0x0000000000000000       0x26 emodules/drv_mcu_uart/core/handlers/protocol/tuner/mcu_tuner_category_handler.o
 .text          0x0000000000000000        0x0 emodules/drv_mcu_uart/core/handlers/protocol/party/mcu_party_category_handler.o
 .data          0x0000000000000000        0x0 emodules/drv_mcu_uart/core/handlers/protocol/party/mcu_party_category_handler.o
 .bss           0x0000000000000000        0x0 emodules/drv_mcu_uart/core/handlers/protocol/party/mcu_party_category_handler.o
 .text.party_get_status
                0x0000000000000000       0x26 emodules/drv_mcu_uart/core/handlers/protocol/party/mcu_party_category_handler.o
 .text.party_get_mode_status
                0x0000000000000000       0x10 emodules/drv_mcu_uart/core/handlers/protocol/party/mcu_party_category_handler.o
 .text.party_get_device_role
                0x0000000000000000       0x10 emodules/drv_mcu_uart/core/handlers/protocol/party/mcu_party_category_handler.o
 .text.party_get_device_name
                0x0000000000000000       0x28 emodules/drv_mcu_uart/core/handlers/protocol/party/mcu_party_category_handler.o
 .text.party_get_audio_source
                0x0000000000000000       0x10 emodules/drv_mcu_uart/core/handlers/protocol/party/mcu_party_category_handler.o
 .text.party_get_connection_status
                0x0000000000000000       0x10 emodules/drv_mcu_uart/core/handlers/protocol/party/mcu_party_category_handler.o
 .text.party_get_error_status
                0x0000000000000000       0x10 emodules/drv_mcu_uart/core/handlers/protocol/party/mcu_party_category_handler.o
 .text.party_get_scan_status
                0x0000000000000000       0x10 emodules/drv_mcu_uart/core/handlers/protocol/party/mcu_party_category_handler.o
 .text.party_get_discovered_devices
                0x0000000000000000       0x6e emodules/drv_mcu_uart/core/handlers/protocol/party/mcu_party_category_handler.o
 .text          0x0000000000000000        0x0 emodules/drv_mcu_uart/core/handlers/protocol/usb/mcu_usb_category_handler.o
 .data          0x0000000000000000        0x0 emodules/drv_mcu_uart/core/handlers/protocol/usb/mcu_usb_category_handler.o
 .bss           0x0000000000000000        0x0 emodules/drv_mcu_uart/core/handlers/protocol/usb/mcu_usb_category_handler.o
 .text.usb_disk_get_state
                0x0000000000000000       0x30 emodules/drv_mcu_uart/core/handlers/protocol/usb/mcu_usb_category_handler.o
 .text.usb_ipod_get_state
                0x0000000000000000       0x30 emodules/drv_mcu_uart/core/handlers/protocol/usb/mcu_usb_category_handler.o
 .text.usb_handler_init
                0x0000000000000000       0xa0 emodules/drv_mcu_uart/core/handlers/protocol/usb/mcu_usb_category_handler.o
 .text.usb_handler_deinit
                0x0000000000000000       0x40 emodules/drv_mcu_uart/core/handlers/protocol/usb/mcu_usb_category_handler.o
 .text.usb_get_handler_stats
                0x0000000000000000       0x2c emodules/drv_mcu_uart/core/handlers/protocol/usb/mcu_usb_category_handler.o
 .text.usb_reset_handler_stats
                0x0000000000000000       0x12 emodules/drv_mcu_uart/core/handlers/protocol/usb/mcu_usb_category_handler.o
 .text.usb_print_handler_status
                0x0000000000000000        0x2 emodules/drv_mcu_uart/core/handlers/protocol/usb/mcu_usb_category_handler.o
 .text          0x0000000000000000        0x0 emodules/drv_mcu_uart/core/handlers/protocol/settings/mcu_settings_category_handler.o
 .data          0x0000000000000000        0x0 emodules/drv_mcu_uart/core/handlers/protocol/settings/mcu_settings_category_handler.o
 .bss           0x0000000000000000        0x0 emodules/drv_mcu_uart/core/handlers/protocol/settings/mcu_settings_category_handler.o
 .text.settings_get_status
                0x0000000000000000       0x40 emodules/drv_mcu_uart/core/handlers/protocol/settings/mcu_settings_category_handler.o
 .text          0x0000000000000000        0x0 ./elibrary/bin//libsyscall.a(syscall.o)
 .data          0x0000000000000000        0x0 ./elibrary/bin//libsyscall.a(syscall.o)
 .bss           0x0000000000000000        0x0 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esCFG_Exit_Ex
                0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esCFG_GetGPIOSecData
                0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esCFG_GetGPIOSecData_Ex
                0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esCFG_GetGPIOSecKeyCount
                0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esCFG_GetGPIOSecKeyCount_Ex
                0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esCFG_GetKeyValue
                0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esCFG_GetKeyValue_Ex
                0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esCFG_GetSecCount
                0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esCFG_GetSecCount_Ex
                0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esCFG_GetSecKeyCount
                0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esCFG_GetSecKeyCount_Ex
                0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esCFG_Init_Ex
                0x0000000000000000       0x16 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esCHS_Char2Uni
                0x0000000000000000       0x1c ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esCHS_GetChLowerTbl
                0x0000000000000000       0x1c ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esCHS_GetChUpperTbl
                0x0000000000000000       0x1c ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esCHS_Uni2Char
                0x0000000000000000       0x1c ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esCLK_CloseMclk
                0x0000000000000000       0x1c ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esCLK_GetMclkDiv
                0x0000000000000000       0x1c ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esCLK_GetMclkSrc
                0x0000000000000000       0x1c ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esCLK_GetRound_Rate
                0x0000000000000000       0x20 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esCLK_GetSrcFreq
                0x0000000000000000       0x1c ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esCLK_MclkAssert
                0x0000000000000000       0x1c ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esCLK_MclkDeassert
                0x0000000000000000       0x1c ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esCLK_MclkGetRstStatus
                0x0000000000000000       0x1c ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esCLK_MclkOnOff
                0x0000000000000000       0x20 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esCLK_MclkRegCb
                0x0000000000000000       0x1c ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esCLK_MclkReset
                0x0000000000000000       0x1c ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esCLK_MclkUnregCb
                0x0000000000000000       0x1c ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esCLK_ModInfo
                0x0000000000000000       0x16 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esCLK_OpenMclk
                0x0000000000000000       0x1c ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esCLK_SetMclkDiv
                0x0000000000000000       0x20 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esCLK_SetMclkSrc
                0x0000000000000000       0x20 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esCLK_SetSrcFreq
                0x0000000000000000       0x20 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esCLK_SysInfo
                0x0000000000000000       0x16 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esDEV_Close
                0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esDEV_DevReg
                0x0000000000000000       0x16 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esDEV_DevUnreg
                0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esDEV_Insmod
                0x0000000000000000       0x1c ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esDEV_Ioctl
                0x0000000000000000       0x1c ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esDEV_Lock
                0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esDEV_Open
                0x0000000000000000       0x1a ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esDEV_Plugin
                0x0000000000000000       0x1c ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esDEV_Plugout
                0x0000000000000000       0x1c ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esDEV_Read
                0x0000000000000000       0x20 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esDEV_Unimod
                0x0000000000000000       0x1c ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esDEV_Unlock
                0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esDEV_Write
                0x0000000000000000       0x20 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esDMA_ChangeMode
                0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esDMA_DisableINT
                0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esDMA_EnableINT
                0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esDMA_Information
                0x0000000000000000       0x16 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esDMA_QueryDst
                0x0000000000000000       0x16 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esDMA_QueryRestCount
                0x0000000000000000       0x16 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esDMA_QuerySrc
                0x0000000000000000       0x16 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esDMA_QueryStat
                0x0000000000000000       0x1a ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esDMA_RegDmaHdler
                0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esDMA_Release
                0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esDMA_Request
                0x0000000000000000       0x16 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esDMA_Restart
                0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esDMA_Setting
                0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esDMA_Start
                0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esDMA_Stop
                0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esDMA_UnregDmaHdler
                0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esEXEC_PCreate
                0x0000000000000000       0x1e ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esEXEC_PDel
                0x0000000000000000       0x1a ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esEXEC_PDelReq
                0x0000000000000000       0x1a ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esEXEC_Run
                0x0000000000000000       0x1e ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esFSYS_clearpartupdateflag
                0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esFSYS_closedir
                0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esFSYS_fd2file
                0x0000000000000000       0x16 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esFSYS_ferrclr
                0x0000000000000000       0x16 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esFSYS_ferror
                0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esFSYS_file2fd
                0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esFSYS_fioctrl
                0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esFSYS_format
                0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esFSYS_fsdbg
                0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esFSYS_fseek
                0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esFSYS_fseekex
                0x0000000000000000       0x16 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esFSYS_fsreg
                0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esFSYS_fstat
                0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esFSYS_fsunreg
                0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esFSYS_fsync
                0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esFSYS_ftell
                0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esFSYS_ftellex
                0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esFSYS_ftruncate
                0x0000000000000000       0x1c ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esFSYS_getfscharset
                0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esFSYS_mkdir
                0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esFSYS_mntfs
                0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esFSYS_mntparts
                0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esFSYS_open
                0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esFSYS_opendir
                0x0000000000000000       0x16 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esFSYS_partfslck
                0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esFSYS_partfsunlck
                0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esFSYS_pclose
                0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esFSYS_pdreg
                0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esFSYS_pdunreg
                0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esFSYS_perrclr
                0x0000000000000000       0x16 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esFSYS_perror
                0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esFSYS_pioctrl
                0x0000000000000000       0x1c ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esFSYS_popen
                0x0000000000000000       0x16 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esFSYS_pread
                0x0000000000000000       0x20 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esFSYS_premove
                0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esFSYS_prename
                0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esFSYS_pwrite
                0x0000000000000000       0x20 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esFSYS_querypartupdateflag
                0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esFSYS_readdir
                0x0000000000000000       0x16 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esFSYS_remove
                0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esFSYS_rename
                0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esFSYS_rewinddir
                0x0000000000000000       0x16 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esFSYS_rmdir
                0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esFSYS_setfs
                0x0000000000000000       0x1c ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esFSYS_statfs
                0x0000000000000000       0x1c ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esFSYS_statpt
                0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esFSYS_umntfs
                0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esFSYS_umntparts
                0x0000000000000000       0x1c ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esHID_hiddevreg
                0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esHID_hiddevunreg
                0x0000000000000000       0x1c ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esHID_SendMsg
                0x0000000000000000       0x1c ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esINPUT_GetLdevID
                0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esINPUT_LdevCtl
                0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esINPUT_LdevFeedback
                0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esINPUT_LdevGrab
                0x0000000000000000       0x16 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esINPUT_LdevRelease
                0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esINT_DisableINT
                0x0000000000000000       0x1c ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esINT_EnableINT
                0x0000000000000000       0x1c ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esINT_InsFIR
                0x0000000000000000       0x1c ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esINT_InsISR
                0x0000000000000000       0x1c ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esINT_SetIRQPrio
                0x0000000000000000       0x20 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esINT_UniFIR
                0x0000000000000000       0x1c ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esINT_UniISR
                0x0000000000000000       0x1c ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esKRNL_CallBack
                0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esKRNL_DumpStack
                0x0000000000000000       0x16 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esKRNL_FlagAccept
                0x0000000000000000       0x1c ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esKRNL_FlagCreate
                0x0000000000000000       0x1a ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esKRNL_FlagDel
                0x0000000000000000       0x16 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esKRNL_FlagNameGet
                0x0000000000000000       0x1a ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esKRNL_FlagNameSet
                0x0000000000000000       0x16 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esKRNL_FlagPend
                0x0000000000000000       0x20 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esKRNL_FlagPendGetFlagsRdy
                0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esKRNL_FlagPost
                0x0000000000000000       0x1c ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esKRNL_FlagQuery
                0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esKRNL_GetCallBack
                0x0000000000000000       0x16 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esKRNL_GetTIDCur
                0x0000000000000000       0x16 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esKRNL_InterruptDisable
                0x0000000000000000       0x16 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esKRNL_InterruptEnable
                0x0000000000000000       0x16 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esKRNL_MboxAccept
                0x0000000000000000       0x16 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esKRNL_MboxCreate
                0x0000000000000000       0x1a ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esKRNL_MboxDel
                0x0000000000000000       0x16 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esKRNL_MboxPend
                0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esKRNL_MboxPost
                0x0000000000000000       0x1a ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esKRNL_MboxPostOpt
                0x0000000000000000       0x1e ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esKRNL_MboxQuery
                0x0000000000000000       0x1a ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esKRNL_MemLeakChk
                0x0000000000000000       0x1a ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esKRNL_MutexCreate
                0x0000000000000000       0x16 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esKRNL_MutexDel
                0x0000000000000000       0x16 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esKRNL_MutexPend
                0x0000000000000000       0x16 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esKRNL_MutexPost
                0x0000000000000000       0x1a ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esKRNL_QAccept
                0x0000000000000000       0x16 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esKRNL_QCreate
                0x0000000000000000       0x16 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esKRNL_QDel
                0x0000000000000000       0x16 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esKRNL_QFlush
                0x0000000000000000       0x1a ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esKRNL_QPend
                0x0000000000000000       0x16 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esKRNL_QPost
                0x0000000000000000       0x1a ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esKRNL_QPostFront
                0x0000000000000000       0x1a ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esKRNL_QPostOpt
                0x0000000000000000       0x1a ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esKRNL_QQuery
                0x0000000000000000       0x1a ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esKRNL_SemAccept
                0x0000000000000000       0x1a ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esKRNL_SemPend
                0x0000000000000000       0x16 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esKRNL_SemPost
                0x0000000000000000       0x1a ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esKRNL_SemQuery
                0x0000000000000000       0x1a ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esKRNL_SemSet
                0x0000000000000000       0x16 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esKRNL_SktAccept
                0x0000000000000000       0x16 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esKRNL_SktCreate
                0x0000000000000000       0x22 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esKRNL_SktDel
                0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esKRNL_SktPend
                0x0000000000000000       0x1a ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esKRNL_SktPost
                0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esKRNL_TaskPrefEn
                0x0000000000000000       0x1a ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esKRNL_TaskQuery
                0x0000000000000000       0x1e ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esKRNL_TaskResume
                0x0000000000000000       0x1e ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esKRNL_TaskSuspend
                0x0000000000000000       0x1e ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esKRNL_Time
                0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esKRNL_TimeDlyResume
                0x0000000000000000       0x1e ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esKRNL_TimeSet
                0x0000000000000000       0x1a ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esKRNL_TmrCreate
                0x0000000000000000       0x1a ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esKRNL_TmrDel
                0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esKRNL_TmrError
                0x0000000000000000       0x1a ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esKRNL_TmrRemainGet
                0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esKRNL_TmrStart
                0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esKRNL_TmrStateGet
                0x0000000000000000       0x1a ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esKRNL_TmrStop
                0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esKRNL_Version
                0x0000000000000000       0x1a ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esKRNL_Ioctrl
                0x0000000000000000       0x16 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esKSRV_ClearWatchDog
                0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esKSRV_DisableWatchDog
                0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esKSRV_DisableWatchDogSrv
                0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esKSRV_EnableWatchDog
                0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esKSRV_EnableWatchDogSrv
                0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esKSRV_GetAddPara
                0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esKSRV_GetDramCfgPara
                0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esKSRV_GetHighMsg
                0x0000000000000000       0x16 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esKSRV_GetLowMsg
                0x0000000000000000       0x16 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esKSRV_GetMsg
                0x0000000000000000       0x16 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esKSRV_GetPara
                0x0000000000000000       0x1c ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esKSRV_GetSocID
                0x0000000000000000       0x1a ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esKSRV_GetTargetPara
                0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esKSRV_GetVersion
                0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esKSRV_Save_Mixture_Hld
                0x0000000000000000       0x16 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esKSRV_Get_Mixture_Hld
                0x0000000000000000       0x16 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esKSRV_Save_Raw_Decoder_Hld
                0x0000000000000000       0x16 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esKSRV_Get_Raw_Decoder_Hld
                0x0000000000000000       0x16 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esKSRV_memcpy
                0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esKSRV_memset
                0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esKSRV_PowerOff
                0x0000000000000000       0x16 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esKSRV_Reset
                0x0000000000000000       0x16 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esKSRV_Save_Display_Hld
                0x0000000000000000       0x16 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esKSRV_SendMsgEx
                0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esKSRV_SysInfo
                0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esMEMS_Calloc
                0x0000000000000000       0x1e ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esMEMS_CleanDCache
                0x0000000000000000       0x16 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esMEMS_CleanDCacheRegion
                0x0000000000000000       0x1a ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esMEMS_CleanFlushCache
                0x0000000000000000       0x16 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esMEMS_CleanFlushCacheRegion
                0x0000000000000000       0x1a ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esMEMS_CleanFlushDCache
                0x0000000000000000       0x16 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esMEMS_CleanFlushDCacheRegion
                0x0000000000000000       0x1a ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esMEMS_CleanInvalidateCacheAll
                0x0000000000000000       0x16 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esMEMS_FlushCache
                0x0000000000000000       0x16 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esMEMS_FlushCacheRegion
                0x0000000000000000       0x1a ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esMEMS_FlushDCache
                0x0000000000000000       0x16 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esMEMS_FlushDCacheRegion
                0x0000000000000000       0x1a ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esMEMS_FlushICache
                0x0000000000000000       0x16 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esMEMS_FlushICacheRegion
                0x0000000000000000       0x1a ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esMEMS_FreeMemSize
                0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esMEMS_GetIoVAByPA
                0x0000000000000000       0x1c ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esMEMS_HeapCreate
                0x0000000000000000       0x1c ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esMEMS_HeapDel
                0x0000000000000000       0x16 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esMEMS_Info
                0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esMEMS_Realloc
                0x0000000000000000       0x1a ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esMEMS_TotalMemSize
                0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esMEMS_VA2PA
                0x0000000000000000       0x16 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esMEMS_VMalloc
                0x0000000000000000       0x16 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esMEMS_VMCreate
                0x0000000000000000       0x1c ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esMEMS_VMDelete
                0x0000000000000000       0x1a ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esMEMS_VMfree
                0x0000000000000000       0x16 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esMEM_BWDisable
                0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esMEM_BWEnable
                0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esMEM_BWGet
                0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esMEM_DramSuspend
                0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esMEM_DramWakeup
                0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esMEM_MasterGet
                0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esMEM_MasterSet
                0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esMEM_RegDramAccess
                0x0000000000000000       0x16 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esMEM_ReleaseDramUsrMode
                0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esMEM_RequestDramUsrMode
                0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esMEM_SramRelBlk
                0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esMEM_SramReqBlk
                0x0000000000000000       0x16 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esMEM_SramSwitchBlk
                0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esMEM_UnRegDramAccess
                0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esMODS_MClose
                0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esMODS_MInstall
                0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esMODS_MOpen
                0x0000000000000000       0x16 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esMODS_MRead
                0x0000000000000000       0x20 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esMODS_MUninstall
                0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esMODS_MWrite
                0x0000000000000000       0x20 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esMSTUB_GetFuncEntry
                0x0000000000000000       0x1e ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esMSTUB_RegFuncTbl
                0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esMSTUB_UnRegFuncTbl
                0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esPINS_ClearPending
                0x0000000000000000       0x16 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esPINS_DisbaleInt
                0x0000000000000000       0x16 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esPINS_EnbaleInt
                0x0000000000000000       0x16 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esPINS_GetPinGrpStat
                0x0000000000000000       0x1a ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esPINS_GetPinStat
                0x0000000000000000       0x16 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esPINS_QueryInt
                0x0000000000000000       0x16 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esPINS_RegIntHdler
                0x0000000000000000       0x16 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esPINS_SetIntMode
                0x0000000000000000       0x1a ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esPINS_SetPinDrive
                0x0000000000000000       0x1a ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esPINS_SetPinPull
                0x0000000000000000       0x1a ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esPINS_SetPinStat
                0x0000000000000000       0x16 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esPINS_UnregIntHdler
                0x0000000000000000       0x16 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esPWRMAN_EnterStandby
                0x0000000000000000       0x1a ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esPWRMAN_GetStandbyPara
                0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esPWRMAN_LockCpuFreq
                0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esPWRMAN_RegDevice
                0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esPWRMAN_RelPwrmanMode
                0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esPWRMAN_ReqPwrmanMode
                0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esPWRMAN_SetStandbyMode
                0x0000000000000000       0x1c ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esPWRMAN_UnlockCpuFreq
                0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esPWRMAN_UnregDevice
                0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esPWRMAN_UsrEventNotify
                0x0000000000000000       0x16 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esRESM_RClose
                0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esRESM_ROpen
                0x0000000000000000       0x16 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esRESM_RRead
                0x0000000000000000       0x20 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esRESM_RSeek
                0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esSIOS_getchar
                0x0000000000000000       0x16 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esSIOS_gets
                0x0000000000000000       0x14 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esSIOS_putarg
                0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esSIOS_setbaud
                0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esSVC_RegCloseNode
                0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esSVC_RegClrError
                0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esSVC_RegCreateKey
                0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esSVC_RegCreatePath
                0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esSVC_RegCreateSet
                0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esSVC_RegDeleteKey
                0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esSVC_RegDeleteNode
                0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esSVC_RegDeletePath
                0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esSVC_RegDeleteSet
                0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esSVC_RegGetError
                0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esSVC_RegGetFirstKey
                0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esSVC_RegGetFirstSet
                0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esSVC_RegGetKeyCount
                0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esSVC_RegGetKeyValue
                0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esSVC_RegGetNextKey
                0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esSVC_RegGetNextSet
                0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esSVC_RegGetRootPath
                0x0000000000000000       0x16 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esSVC_RegGetSetCount
                0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esSVC_RegIni2Reg
                0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esSVC_RegOpenNode
                0x0000000000000000       0x16 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esSVC_RegReg2Ini
                0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esSVC_RegSetKeyValue
                0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esSVC_RegSetRootPath
                0x0000000000000000       0x16 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esSVC_ResourceRel
                0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esSVC_ResourceReq
                0x0000000000000000       0x22 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esTIME_ContiCntr
                0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esTIME_GetDate
                0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esTIME_GetTime
                0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esTIME_PauseCntr
                0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esTIME_QuerryAlarm
                0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esTIME_QuerryCntr
                0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esTIME_QuerryCntrStat
                0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esTIME_QuerryTimer
                0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esTIME_ReleaseAlarm
                0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esTIME_ReleaseCntr
                0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esTIME_ReleaseTimer
                0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esTIME_RequestAlarm
                0x0000000000000000       0x1a ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esTIME_RequestCntr
                0x0000000000000000       0x16 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esTIME_RequestTimer
                0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esTIME_SetCntrPrescale
                0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esTIME_SetCntrValue
                0x0000000000000000       0x1c ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esTIME_SetDate
                0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esTIME_SetTime
                0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esTIME_StartAlarm
                0x0000000000000000       0x1c ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esTIME_StartCntr
                0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esTIME_StartTimer
                0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esTIME_StopAlarm
                0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esTIME_StopCntr
                0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esTIME_StopTimer
                0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.backtrace
                0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.select   0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.mmap     0x0000000000000000       0x16 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.munmap   0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.poll     0x0000000000000000       0x1c ./elibrary/bin//libsyscall.a(syscall.o)
 .text.pipe     0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall.o)
 .text          0x0000000000000000        0x0 ./elibrary/bin//libsyscall.a(syscall_fops.o)
 .data          0x0000000000000000        0x0 ./elibrary/bin//libsyscall.a(syscall_fops.o)
 .bss           0x0000000000000000        0x0 ./elibrary/bin//libsyscall.a(syscall_fops.o)
 .text.dup      0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall_fops.o)
 .text.dup2     0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall_fops.o)
 .text.closedir
                0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall_fops.o)
 .text.fioctrl  0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall_fops.o)
 .text._rename_r
                0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall_fops.o)
 .text._stat_r  0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall_fops.o)
 .text._mkdir_r
                0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall_fops.o)
 .text.fsync    0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall_fops.o)
 .text._unlink_r
                0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall_fops.o)
 .text._gettimeofday_r
                0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall_fops.o)
 .text.settimeofday
                0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall_fops.o)
 .text._isatty  0x0000000000000000       0x16 ./elibrary/bin//libsyscall.a(syscall_fops.o)
 .text.__libc_init_array
                0x0000000000000000       0x16 ./elibrary/bin//libsyscall.a(syscall_fops.o)
 .text._system  0x0000000000000000       0x16 ./elibrary/bin//libsyscall.a(syscall_fops.o)
 .text.__malloc_lock
                0x0000000000000000        0x2 ./elibrary/bin//libsyscall.a(syscall_fops.o)
 .text.__malloc_unlock
                0x0000000000000000        0x2 ./elibrary/bin//libsyscall.a(syscall_fops.o)
 .text.ftruncate
                0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall_fops.o)
 .text.mkdir    0x0000000000000000       0x1c ./elibrary/bin//libsyscall.a(syscall_fops.o)
 .text.opendir  0x0000000000000000       0x16 ./elibrary/bin//libsyscall.a(syscall_fops.o)
 .text.readdir  0x0000000000000000       0x16 ./elibrary/bin//libsyscall.a(syscall_fops.o)
 .text.remove   0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall_fops.o)
 .text.rewinddir
                0x0000000000000000       0x16 ./elibrary/bin//libsyscall.a(syscall_fops.o)
 .text.rmdir    0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall_fops.o)
 .text.statfs   0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall_fops.o)
 .text.access   0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall_fops.o)
 .text          0x0000000000000000        0x0 ./elibrary/bin//libsyscall.a(syscall.o)
 .data          0x0000000000000000        0x0 ./elibrary/bin//libsyscall.a(syscall.o)
 .bss           0x0000000000000000        0x0 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.pthread_attr_destroy
                0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.pthread_attr_init
                0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.pthread_attr_setdetachstate
                0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.pthread_attr_getdetachstate
                0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.pthread_attr_setschedpolicy
                0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.pthread_attr_getschedpolicy
                0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.pthread_attr_setschedparam
                0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.pthread_attr_getschedparam
                0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.pthread_attr_setstacksize
                0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.pthread_attr_getstacksize
                0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.pthread_attr_setstackaddr
                0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.pthread_attr_getstackaddr
                0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.pthread_attr_setstack
                0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.pthread_attr_getstack
                0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.pthread_attr_setguardsize
                0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.pthread_attr_getguardsize
                0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.pthread_attr_setscope
                0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.pthread_attr_getscope
                0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.pthread_system_init
                0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.pthread_create
                0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.pthread_detach
                0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.pthread_join
                0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.pthread_exit
                0x0000000000000000       0x16 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.pthread_once
                0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.pthread_cleanup_pop
                0x0000000000000000       0x16 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.pthread_cleanup_push
                0x0000000000000000       0x16 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.pthread_cancel
                0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.pthread_self
                0x0000000000000000       0x16 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.pthread_testcancel
                0x0000000000000000       0x16 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.pthread_setcancelstate
                0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.pthread_setcanceltype
                0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.pthread_atfork
                0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.pthread_kill
                0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.pthread_mutex_init
                0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.pthread_mutex_destroy
                0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.pthread_mutex_lock
                0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.pthread_mutex_unlock
                0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.pthread_mutex_trylock
                0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.pthread_mutexattr_init
                0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.pthread_mutexattr_destroy
                0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.pthread_mutexattr_gettype
                0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.pthread_mutexattr_settype
                0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.pthread_mutexattr_setpshared
                0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.pthread_mutexattr_getpshared
                0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.pthread_condattr_destroy
                0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.pthread_condattr_init
                0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.pthread_condattr_getclock
                0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.pthread_condattr_setclock
                0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.pthread_cond_init
                0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.pthread_cond_destroy
                0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.pthread_cond_broadcast
                0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.pthread_cond_signal
                0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.pthread_cond_wait
                0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.pthread_cond_timedwait
                0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.pthread_rwlockattr_init
                0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.pthread_rwlockattr_destroy
                0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.pthread_rwlockattr_getpshared
                0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.pthread_rwlockattr_setpshared
                0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.pthread_rwlock_init
                0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.pthread_rwlock_destroy
                0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.pthread_rwlock_rdlock
                0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.pthread_rwlock_tryrdlock
                0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.pthread_rwlock_timedrdlock
                0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.pthread_rwlock_timedwrlock
                0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.pthread_rwlock_unlock
                0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.pthread_rwlock_wrlock
                0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.pthread_rwlock_trywrlock
                0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.pthread_spin_init
                0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.pthread_spin_destroy
                0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.pthread_spin_lock
                0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.pthread_spin_trylock
                0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.pthread_spin_unlock
                0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.pthread_barrierattr_destroy
                0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.pthread_barrierattr_init
                0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.pthread_barrierattr_getpshared
                0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.pthread_barrierattr_setpshared
                0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.pthread_barrier_destroy
                0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.pthread_barrier_init
                0x0000000000000000       0x1c ./elibrary/bin//libsyscall.a(syscall.o)
 .text.pthread_barrier_wait
                0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.pthread_getspecific
                0x0000000000000000       0x16 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.pthread_setspecific
                0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.pthread_key_create
                0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.pthread_key_delete
                0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.pthread_setname_np
                0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.sem_close
                0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.sem_destroy
                0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.sem_getvalue
                0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.sem_init
                0x0000000000000000       0x1c ./elibrary/bin//libsyscall.a(syscall.o)
 .text.sem_open
                0x0000000000000000       0x22 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.sem_post
                0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.sem_timedwait
                0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.sem_trywait
                0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.sem_unlink
                0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.sem_wait
                0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.clock_settime
                0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.clock_gettime
                0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.clock_getres
                0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.msleep   0x0000000000000000       0x1c ./elibrary/bin//libsyscall.a(syscall.o)
 .text.sleep    0x0000000000000000       0x1c ./elibrary/bin//libsyscall.a(syscall.o)
 .text          0x0000000000000000        0x0 ./elibrary/bin//libminic.a(elibs_stdio.o)
 .data          0x0000000000000000        0x0 ./elibrary/bin//libminic.a(elibs_stdio.o)
 .bss           0x0000000000000000        0x0 ./elibrary/bin//libminic.a(elibs_stdio.o)
 .text.eLIBs_remove
                0x0000000000000000        0x8 ./elibrary/bin//libminic.a(elibs_stdio.o)
 .text.eLIBs_rename
                0x0000000000000000        0x8 ./elibrary/bin//libminic.a(elibs_stdio.o)
 .text.eLIBs_fsync
                0x0000000000000000        0x8 ./elibrary/bin//libminic.a(elibs_stdio.o)
 .text.eLIBs_ftruncate
                0x0000000000000000        0x8 ./elibrary/bin//libminic.a(elibs_stdio.o)
 .text.eLIBs_fseek
                0x0000000000000000       0x16 ./elibrary/bin//libminic.a(elibs_stdio.o)
 .text.eLIBs_fllseek
                0x0000000000000000       0x1e ./elibrary/bin//libminic.a(elibs_stdio.o)
 .text.eLIBs_ftell
                0x0000000000000000        0x8 ./elibrary/bin//libminic.a(elibs_stdio.o)
 .text.eLIBs_flltell
                0x0000000000000000       0x26 ./elibrary/bin//libminic.a(elibs_stdio.o)
 .text.eLIBs_fioctrl
                0x0000000000000000        0x8 ./elibrary/bin//libminic.a(elibs_stdio.o)
 .text.eLIBs_ferror
                0x0000000000000000        0x8 ./elibrary/bin//libminic.a(elibs_stdio.o)
 .text.eLIBs_ferrclr
                0x0000000000000000        0x8 ./elibrary/bin//libminic.a(elibs_stdio.o)
 .text.eLIBs_opendir
                0x0000000000000000        0x8 ./elibrary/bin//libminic.a(elibs_stdio.o)
 .text.eLIBs_closedir
                0x0000000000000000        0x8 ./elibrary/bin//libminic.a(elibs_stdio.o)
 .text.eLIBs_readdir
                0x0000000000000000        0x8 ./elibrary/bin//libminic.a(elibs_stdio.o)
 .text.eLIBs_rewinddir
                0x0000000000000000        0x8 ./elibrary/bin//libminic.a(elibs_stdio.o)
 .text.eLIBs_mkdir
                0x0000000000000000        0x8 ./elibrary/bin//libminic.a(elibs_stdio.o)
 .text.eLIBs_rmdir
                0x0000000000000000        0x8 ./elibrary/bin//libminic.a(elibs_stdio.o)
 .text.eLIBs_DirEnt2Attr
                0x0000000000000000        0x8 ./elibrary/bin//libminic.a(elibs_stdio.o)
 .text.eLIBs_DirEnt2Name
                0x0000000000000000       0x2e ./elibrary/bin//libminic.a(elibs_stdio.o)
 .text.eLIBs_DirEnt2Size
                0x0000000000000000        0x6 ./elibrary/bin//libminic.a(elibs_stdio.o)
 .text.eLIBs_DirEnt2Time
                0x0000000000000000        0x4 ./elibrary/bin//libminic.a(elibs_stdio.o)
 .text.eLIBs_GetNumFiles
                0x0000000000000000       0x3e ./elibrary/bin//libminic.a(elibs_stdio.o)
 .text.eLIBs_GetNumSubItems
                0x0000000000000000       0x4e ./elibrary/bin//libminic.a(elibs_stdio.o)
 .text.eLIBs_GetFileAttributes
                0x0000000000000000       0x4c ./elibrary/bin//libminic.a(elibs_stdio.o)
 .text.eLIBs_SetFileAttributes
                0x0000000000000000        0x4 ./elibrary/bin//libminic.a(elibs_stdio.o)
 .text.eLIBs_GetDirSize
                0x0000000000000000      0x108 ./elibrary/bin//libminic.a(elibs_stdio.o)
 .text.eLIBs_GetFileSize
                0x0000000000000000       0x40 ./elibrary/bin//libminic.a(elibs_stdio.o)
 .text.eLIBs_format
                0x0000000000000000        0x8 ./elibrary/bin//libminic.a(elibs_stdio.o)
 .text.eLIBs_GetVolName
                0x0000000000000000       0x80 ./elibrary/bin//libminic.a(elibs_stdio.o)
 .text.eLIBs_GetVolNameLen
                0x0000000000000000       0x50 ./elibrary/bin//libminic.a(elibs_stdio.o)
 .text.eLIBs_GetVolNameCharset
                0x0000000000000000       0x52 ./elibrary/bin//libminic.a(elibs_stdio.o)
 .text.eLIBs_GetVolTSpace
                0x0000000000000000       0x30 ./elibrary/bin//libminic.a(elibs_stdio.o)
 .text.eLIBs_GetVolFSpace
                0x0000000000000000       0x30 ./elibrary/bin//libminic.a(elibs_stdio.o)
 .text.eLIBs_GetVolClustSize
                0x0000000000000000       0x26 ./elibrary/bin//libminic.a(elibs_stdio.o)
 .text.eLIBs_GetFSName
                0x0000000000000000       0x80 ./elibrary/bin//libminic.a(elibs_stdio.o)
 .text.eLIBs_GetFSNameLen
                0x0000000000000000       0x50 ./elibrary/bin//libminic.a(elibs_stdio.o)
 .text.eLIBs_GetFSAttribute
                0x0000000000000000       0x36 ./elibrary/bin//libminic.a(elibs_stdio.o)
 .text.eLIBs_GetPTName
                0x0000000000000000       0x62 ./elibrary/bin//libminic.a(elibs_stdio.o)
 .text.eLIBs_IsPartFormated
                0x0000000000000000       0x9c ./elibrary/bin//libminic.a(elibs_stdio.o)
 .text.eLIBs_disklock
                0x0000000000000000        0x8 ./elibrary/bin//libminic.a(elibs_stdio.o)
 .text.eLIBs_diskunlock
                0x0000000000000000        0x8 ./elibrary/bin//libminic.a(elibs_stdio.o)
 .text.eLIBs_GetDiskArray
                0x0000000000000000       0x62 ./elibrary/bin//libminic.a(elibs_stdio.o)
 .text.eLIBs_feof
                0x0000000000000000       0x1e ./elibrary/bin//libminic.a(elibs_stdio.o)
 .text.eLIBs_fgetc
                0x0000000000000000       0x2c ./elibrary/bin//libminic.a(elibs_stdio.o)
 .text.eLIBs_fgets
                0x0000000000000000       0x6c ./elibrary/bin//libminic.a(elibs_stdio.o)
 .text.eLIBs_fputc
                0x0000000000000000       0x32 ./elibrary/bin//libminic.a(elibs_stdio.o)
 .text.eLIBs_fputs
                0x0000000000000000       0x42 ./elibrary/bin//libminic.a(elibs_stdio.o)
 .text.eLIBs_GetFileATime
                0x0000000000000000       0x50 ./elibrary/bin//libminic.a(elibs_stdio.o)
 .text.eLIBs_GetFileMTime
                0x0000000000000000       0x50 ./elibrary/bin//libminic.a(elibs_stdio.o)
 .text.eLIBs_GetFileCTime
                0x0000000000000000       0x50 ./elibrary/bin//libminic.a(elibs_stdio.o)
 .text.eLIBs_GetDirATime
                0x0000000000000000       0x48 ./elibrary/bin//libminic.a(elibs_stdio.o)
 .text.eLIBs_GetDirMTime
                0x0000000000000000       0x48 ./elibrary/bin//libminic.a(elibs_stdio.o)
 .text.eLIBs_GetDirCTime
                0x0000000000000000       0x48 ./elibrary/bin//libminic.a(elibs_stdio.o)
 .text.eLIBs_GetFSCharset
                0x0000000000000000       0x10 ./elibrary/bin//libminic.a(elibs_stdio.o)
 .text.eLIBs_file2fd
                0x0000000000000000        0x8 ./elibrary/bin//libminic.a(elibs_stdio.o)
 .text.eLIBs_fd2file
                0x0000000000000000        0x8 ./elibrary/bin//libminic.a(elibs_stdio.o)
 .text.eLIBs_open
                0x0000000000000000        0x8 ./elibrary/bin//libminic.a(elibs_stdio.o)
 .text.eLIBs_fstat
                0x0000000000000000        0x8 ./elibrary/bin//libminic.a(elibs_stdio.o)
 .text.eLIBs_statfs
                0x0000000000000000        0xc ./elibrary/bin//libminic.a(elibs_stdio.o)
 .text.eLIBs_GetDevName
                0x0000000000000000       0x6a ./elibrary/bin//libminic.a(elibs_stdio.o)
 .text.eLIBs_GetDevParts
                0x0000000000000000       0x88 ./elibrary/bin//libminic.a(elibs_stdio.o)
 .text.eLIBs_GetTime
                0x0000000000000000        0x8 ./elibrary/bin//libminic.a(elibs_stdio.o)
 .text.eLIBs_GetDate
                0x0000000000000000        0x8 ./elibrary/bin//libminic.a(elibs_stdio.o)
 .text.eLIBs_setfs
                0x0000000000000000       0x14 ./elibrary/bin//libminic.a(elibs_stdio.o)
 .bss.dir_level.6470
                0x0000000000000000        0x4 ./elibrary/bin//libminic.a(elibs_stdio.o)
 .rodata.eLIBs_GetDirSize.str1.8
                0x0000000000000000        0x2 ./elibrary/bin//libminic.a(elibs_stdio.o)
 .rodata.eLIBs_GetFileAttributes.str1.8
                0x0000000000000000        0x3 ./elibrary/bin//libminic.a(elibs_stdio.o)
 .rodata.eLIBs_GetVolNameCharset.str1.8
                0x0000000000000000        0x5 ./elibrary/bin//libminic.a(elibs_stdio.o)
 .text          0x0000000000000000        0x0 ./elibrary/bin//libminic.a(elibs_stdlib.o)
 .data          0x0000000000000000        0x0 ./elibrary/bin//libminic.a(elibs_stdlib.o)
 .bss           0x0000000000000000        0x0 ./elibrary/bin//libminic.a(elibs_stdlib.o)
 .text.eLIBs_int2str_dec
                0x0000000000000000        0xc ./elibrary/bin//libminic.a(elibs_stdlib.o)
 .text.eLIBs_uint2str_dec
                0x0000000000000000        0xc ./elibrary/bin//libminic.a(elibs_stdlib.o)
 .text.eLIBs_int2str_hex
                0x0000000000000000        0xc ./elibrary/bin//libminic.a(elibs_stdlib.o)
 .text.eLIBs_atoi
                0x0000000000000000        0xc ./elibrary/bin//libminic.a(elibs_stdlib.o)
 .text.eLIBs_toupper
                0x0000000000000000        0xc ./elibrary/bin//libminic.a(elibs_stdlib.o)
 .text.eLIBs_isspace
                0x0000000000000000        0xc ./elibrary/bin//libminic.a(elibs_stdlib.o)
 .text.eLIBs_strtol
                0x0000000000000000        0xc ./elibrary/bin//libminic.a(elibs_stdlib.o)
 .text.eLIBs_realloc
                0x0000000000000000       0x10 ./elibrary/bin//libminic.a(elibs_stdlib.o)
 .text.eLIBs_atof
                0x0000000000000000       0x18 ./elibrary/bin//libminic.a(elibs_stdlib.o)
 .text.eLIBs_sscanf
                0x0000000000000000       0x24 ./elibrary/bin//libminic.a(elibs_stdlib.o)
 .text._exit    0x0000000000000000        0x2 ./elibrary/bin//libminic.a(elibs_stdlib.o)
 .text._write   0x0000000000000000        0x2 ./elibrary/bin//libminic.a(elibs_stdlib.o)
 .text._kill    0x0000000000000000        0x2 ./elibrary/bin//libminic.a(elibs_stdlib.o)
 .text._getpid  0x0000000000000000        0x2 ./elibrary/bin//libminic.a(elibs_stdlib.o)
 .text._sbrk    0x0000000000000000        0x2 ./elibrary/bin//libminic.a(elibs_stdlib.o)
 .text          0x0000000000000000        0x0 ./elibrary/bin//libminic.a(elibs_string.o)
 .data          0x0000000000000000        0x0 ./elibrary/bin//libminic.a(elibs_string.o)
 .bss           0x0000000000000000        0x0 ./elibrary/bin//libminic.a(elibs_string.o)
 .text.eLIBs_strnlen
                0x0000000000000000        0xc ./elibrary/bin//libminic.a(elibs_string.o)
 .text.eLIBs_strncpy
                0x0000000000000000        0xc ./elibrary/bin//libminic.a(elibs_string.o)
 .text.eLIBs_strcat
                0x0000000000000000        0xc ./elibrary/bin//libminic.a(elibs_string.o)
 .text.eLIBs_strncat
                0x0000000000000000        0xc ./elibrary/bin//libminic.a(elibs_string.o)
 .text.eLIBs_stricmp
                0x0000000000000000        0xc ./elibrary/bin//libminic.a(elibs_string.o)
 .text.eLIBs_strncmp
                0x0000000000000000        0xc ./elibrary/bin//libminic.a(elibs_string.o)
 .text.eLIBs_strnicmp
                0x0000000000000000        0xc ./elibrary/bin//libminic.a(elibs_string.o)
 .text.eLIBs_strchr
                0x0000000000000000        0xc ./elibrary/bin//libminic.a(elibs_string.o)
 .text.eLIBs_strnchr
                0x0000000000000000        0xc ./elibrary/bin//libminic.a(elibs_string.o)
 .text.eLIBs_strchrlast
                0x0000000000000000        0xc ./elibrary/bin//libminic.a(elibs_string.o)
 .text.eLIBs_strstr
                0x0000000000000000        0xc ./elibrary/bin//libminic.a(elibs_string.o)
 .text.eLIBs_memclr
                0x0000000000000000       0x10 ./elibrary/bin//libminic.a(elibs_string.o)
 .text.eLIBs_memmove
                0x0000000000000000       0x3c ./elibrary/bin//libminic.a(elibs_string.o)
 .text          0x0000000000000000        0x0 ./elibrary/bin//libminic.a(elibs_sprintf.o)
 .data          0x0000000000000000        0x0 ./elibrary/bin//libminic.a(elibs_sprintf.o)
 .bss           0x0000000000000000        0x0 ./elibrary/bin//libminic.a(elibs_sprintf.o)
 .text.eLIBs_snprintf
                0x0000000000000000       0x20 ./elibrary/bin//libminic.a(elibs_sprintf.o)
 .text.eLIBs_scnprintf
                0x0000000000000000       0x2e ./elibrary/bin//libminic.a(elibs_sprintf.o)
 .text.eLIBs_vsprintf
                0x0000000000000000        0xe ./elibrary/bin//libminic.a(elibs_sprintf.o)
 .text.eLIBs_sprintf
                0x0000000000000000       0x26 ./elibrary/bin//libminic.a(elibs_sprintf.o)
 .data          0x0000000000000000        0x0 /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/../../../../riscv64-unknown-elf/lib/rv64imafdcxtheadc/lp64d/libc.a(lib_a-memcpy.o)
 .bss           0x0000000000000000        0x0 /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/../../../../riscv64-unknown-elf/lib/rv64imafdcxtheadc/lp64d/libc.a(lib_a-memcpy.o)
 .data          0x0000000000000000        0x0 /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/../../../../riscv64-unknown-elf/lib/rv64imafdcxtheadc/lp64d/libc.a(lib_a-memset.o)
 .bss           0x0000000000000000        0x0 /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/../../../../riscv64-unknown-elf/lib/rv64imafdcxtheadc/lp64d/libc.a(lib_a-memset.o)
 .data          0x0000000000000000        0x0 /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/../../../../riscv64-unknown-elf/lib/rv64imafdcxtheadc/lp64d/libc.a(lib_a-printf.o)
 .bss           0x0000000000000000        0x0 /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/../../../../riscv64-unknown-elf/lib/rv64imafdcxtheadc/lp64d/libc.a(lib_a-printf.o)
 .data          0x0000000000000000        0x0 /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/../../../../riscv64-unknown-elf/lib/rv64imafdcxtheadc/lp64d/libc.a(lib_a-sysclose.o)
 .bss           0x0000000000000000        0x0 /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/../../../../riscv64-unknown-elf/lib/rv64imafdcxtheadc/lp64d/libc.a(lib_a-sysclose.o)
 .data          0x0000000000000000        0x0 /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/../../../../riscv64-unknown-elf/lib/rv64imafdcxtheadc/lp64d/libc.a(lib_a-sysopen.o)
 .bss           0x0000000000000000        0x0 /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/../../../../riscv64-unknown-elf/lib/rv64imafdcxtheadc/lp64d/libc.a(lib_a-sysopen.o)
 .data          0x0000000000000000        0x0 /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/../../../../riscv64-unknown-elf/lib/rv64imafdcxtheadc/lp64d/libc.a(lib_a-vfprintf.o)
 .bss           0x0000000000000000        0x0 /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/../../../../riscv64-unknown-elf/lib/rv64imafdcxtheadc/lp64d/libc.a(lib_a-vfprintf.o)
 .data          0x0000000000000000        0x0 /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/../../../../riscv64-unknown-elf/lib/rv64imafdcxtheadc/lp64d/libc.a(lib_a-wsetup.o)
 .bss           0x0000000000000000        0x0 /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/../../../../riscv64-unknown-elf/lib/rv64imafdcxtheadc/lp64d/libc.a(lib_a-wsetup.o)
 .data          0x0000000000000000        0x0 /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/../../../../riscv64-unknown-elf/lib/rv64imafdcxtheadc/lp64d/libc.a(lib_a-fflush.o)
 .bss           0x0000000000000000        0x0 /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/../../../../riscv64-unknown-elf/lib/rv64imafdcxtheadc/lp64d/libc.a(lib_a-fflush.o)
 .data          0x0000000000000000        0x0 /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/../../../../riscv64-unknown-elf/lib/rv64imafdcxtheadc/lp64d/libc.a(lib_a-findfp.o)
 .bss           0x0000000000000000        0x0 /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/../../../../riscv64-unknown-elf/lib/rv64imafdcxtheadc/lp64d/libc.a(lib_a-findfp.o)
 .data          0x0000000000000000        0x0 /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/../../../../riscv64-unknown-elf/lib/rv64imafdcxtheadc/lp64d/libc.a(lib_a-fwalk.o)
 .bss           0x0000000000000000        0x0 /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/../../../../riscv64-unknown-elf/lib/rv64imafdcxtheadc/lp64d/libc.a(lib_a-fwalk.o)
 .text          0x0000000000000000        0x0 /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/../../../../riscv64-unknown-elf/lib/rv64imafdcxtheadc/lp64d/libc.a(lib_a-impure.o)
 .bss           0x0000000000000000        0x0 /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/../../../../riscv64-unknown-elf/lib/rv64imafdcxtheadc/lp64d/libc.a(lib_a-impure.o)
 .data          0x0000000000000000        0x0 /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/../../../../riscv64-unknown-elf/lib/rv64imafdcxtheadc/lp64d/libc.a(lib_a-ldtoa.o)
 .bss           0x0000000000000000        0x0 /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/../../../../riscv64-unknown-elf/lib/rv64imafdcxtheadc/lp64d/libc.a(lib_a-ldtoa.o)
 .data          0x0000000000000000        0x0 /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/../../../../riscv64-unknown-elf/lib/rv64imafdcxtheadc/lp64d/libc.a(lib_a-localeconv.o)
 .bss           0x0000000000000000        0x0 /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/../../../../riscv64-unknown-elf/lib/rv64imafdcxtheadc/lp64d/libc.a(lib_a-localeconv.o)
 .bss           0x0000000000000000        0x8 /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/../../../../riscv64-unknown-elf/lib/rv64imafdcxtheadc/lp64d/libc.a(lib_a-locale.o)
 .data          0x0000000000000000        0x0 /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/../../../../riscv64-unknown-elf/lib/rv64imafdcxtheadc/lp64d/libc.a(lib_a-makebuf.o)
 .bss           0x0000000000000000        0x0 /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/../../../../riscv64-unknown-elf/lib/rv64imafdcxtheadc/lp64d/libc.a(lib_a-makebuf.o)
 .data          0x0000000000000000        0x0 /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/../../../../riscv64-unknown-elf/lib/rv64imafdcxtheadc/lp64d/libc.a(lib_a-mbtowc_r.o)
 .bss           0x0000000000000000        0x0 /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/../../../../riscv64-unknown-elf/lib/rv64imafdcxtheadc/lp64d/libc.a(lib_a-mbtowc_r.o)
 .data          0x0000000000000000        0x0 /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/../../../../riscv64-unknown-elf/lib/rv64imafdcxtheadc/lp64d/libc.a(lib_a-memchr.o)
 .bss           0x0000000000000000        0x0 /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/../../../../riscv64-unknown-elf/lib/rv64imafdcxtheadc/lp64d/libc.a(lib_a-memchr.o)
 .data          0x0000000000000000        0x0 /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/../../../../riscv64-unknown-elf/lib/rv64imafdcxtheadc/lp64d/libc.a(lib_a-mprec.o)
 .bss           0x0000000000000000        0x0 /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/../../../../riscv64-unknown-elf/lib/rv64imafdcxtheadc/lp64d/libc.a(lib_a-mprec.o)
 .data          0x0000000000000000        0x0 /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/../../../../riscv64-unknown-elf/lib/rv64imafdcxtheadc/lp64d/libc.a(lib_a-s_frexp.o)
 .bss           0x0000000000000000        0x0 /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/../../../../riscv64-unknown-elf/lib/rv64imafdcxtheadc/lp64d/libc.a(lib_a-s_frexp.o)
 .data          0x0000000000000000        0x0 /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/../../../../riscv64-unknown-elf/lib/rv64imafdcxtheadc/lp64d/libc.a(lib_a-sprintf.o)
 .bss           0x0000000000000000        0x0 /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/../../../../riscv64-unknown-elf/lib/rv64imafdcxtheadc/lp64d/libc.a(lib_a-sprintf.o)
 .data          0x0000000000000000        0x0 /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/../../../../riscv64-unknown-elf/lib/rv64imafdcxtheadc/lp64d/libc.a(lib_a-stdio.o)
 .bss           0x0000000000000000        0x0 /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/../../../../riscv64-unknown-elf/lib/rv64imafdcxtheadc/lp64d/libc.a(lib_a-stdio.o)
 .data          0x0000000000000000        0x0 /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/../../../../riscv64-unknown-elf/lib/rv64imafdcxtheadc/lp64d/libc.a(lib_a-strcmp.o)
 .bss           0x0000000000000000        0x0 /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/../../../../riscv64-unknown-elf/lib/rv64imafdcxtheadc/lp64d/libc.a(lib_a-strcmp.o)
 .data          0x0000000000000000        0x0 /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/../../../../riscv64-unknown-elf/lib/rv64imafdcxtheadc/lp64d/libc.a(lib_a-strcpy.o)
 .bss           0x0000000000000000        0x0 /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/../../../../riscv64-unknown-elf/lib/rv64imafdcxtheadc/lp64d/libc.a(lib_a-strcpy.o)
 .data          0x0000000000000000        0x0 /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/../../../../riscv64-unknown-elf/lib/rv64imafdcxtheadc/lp64d/libc.a(lib_a-strlen.o)
 .bss           0x0000000000000000        0x0 /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/../../../../riscv64-unknown-elf/lib/rv64imafdcxtheadc/lp64d/libc.a(lib_a-strlen.o)
 .data          0x0000000000000000        0x0 /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/../../../../riscv64-unknown-elf/lib/rv64imafdcxtheadc/lp64d/libc.a(lib_a-strncpy.o)
 .bss           0x0000000000000000        0x0 /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/../../../../riscv64-unknown-elf/lib/rv64imafdcxtheadc/lp64d/libc.a(lib_a-strncpy.o)
 .data          0x0000000000000000        0x0 /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/../../../../riscv64-unknown-elf/lib/rv64imafdcxtheadc/lp64d/libc.a(lib_a-svfprintf.o)
 .bss           0x0000000000000000        0x0 /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/../../../../riscv64-unknown-elf/lib/rv64imafdcxtheadc/lp64d/libc.a(lib_a-svfprintf.o)
 .data          0x0000000000000000        0x0 /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/../../../../riscv64-unknown-elf/lib/rv64imafdcxtheadc/lp64d/libc.a(lib_a-vfiprintf.o)
 .bss           0x0000000000000000        0x0 /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/../../../../riscv64-unknown-elf/lib/rv64imafdcxtheadc/lp64d/libc.a(lib_a-vfiprintf.o)
 .data          0x0000000000000000        0x0 /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/../../../../riscv64-unknown-elf/lib/rv64imafdcxtheadc/lp64d/libc.a(lib_a-wctomb_r.o)
 .bss           0x0000000000000000        0x0 /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/../../../../riscv64-unknown-elf/lib/rv64imafdcxtheadc/lp64d/libc.a(lib_a-wctomb_r.o)
 .text          0x0000000000000000        0x0 /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/../../../../riscv64-unknown-elf/lib/rv64imafdcxtheadc/lp64d/libc.a(lib_a-ctype_.o)
 .data          0x0000000000000000        0x0 /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/../../../../riscv64-unknown-elf/lib/rv64imafdcxtheadc/lp64d/libc.a(lib_a-ctype_.o)
 .bss           0x0000000000000000        0x0 /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/../../../../riscv64-unknown-elf/lib/rv64imafdcxtheadc/lp64d/libc.a(lib_a-ctype_.o)
 .data          0x0000000000000000        0x0 /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/../../../../riscv64-unknown-elf/lib/rv64imafdcxtheadc/lp64d/libc.a(lib_a-fclose.o)
 .bss           0x0000000000000000        0x0 /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/../../../../riscv64-unknown-elf/lib/rv64imafdcxtheadc/lp64d/libc.a(lib_a-fclose.o)
 .data          0x0000000000000000        0x0 /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/../../../../riscv64-unknown-elf/lib/rv64imafdcxtheadc/lp64d/libc.a(lib_a-fputwc.o)
 .bss           0x0000000000000000        0x0 /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/../../../../riscv64-unknown-elf/lib/rv64imafdcxtheadc/lp64d/libc.a(lib_a-fputwc.o)
 .data          0x0000000000000000        0x0 /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/../../../../riscv64-unknown-elf/lib/rv64imafdcxtheadc/lp64d/libc.a(lib_a-fvwrite.o)
 .bss           0x0000000000000000        0x0 /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/../../../../riscv64-unknown-elf/lib/rv64imafdcxtheadc/lp64d/libc.a(lib_a-fvwrite.o)
 .data          0x0000000000000000        0x0 /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/../../../../riscv64-unknown-elf/lib/rv64imafdcxtheadc/lp64d/libc.a(lib_a-memmove-stub.o)
 .bss           0x0000000000000000        0x0 /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/../../../../riscv64-unknown-elf/lib/rv64imafdcxtheadc/lp64d/libc.a(lib_a-memmove-stub.o)
 .data          0x0000000000000000        0x0 /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/../../../../riscv64-unknown-elf/lib/rv64imafdcxtheadc/lp64d/libc.a(lib_a-svfiprintf.o)
 .bss           0x0000000000000000        0x0 /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/../../../../riscv64-unknown-elf/lib/rv64imafdcxtheadc/lp64d/libc.a(lib_a-svfiprintf.o)
 .data          0x0000000000000000        0x0 /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/../../../../riscv64-unknown-elf/lib/rv64imafdcxtheadc/lp64d/libc.a(lib_a-wbuf.o)
 .bss           0x0000000000000000        0x0 /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/../../../../riscv64-unknown-elf/lib/rv64imafdcxtheadc/lp64d/libc.a(lib_a-wbuf.o)
 .data          0x0000000000000000        0x0 /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/../../../../riscv64-unknown-elf/lib/rv64imafdcxtheadc/lp64d/libc.a(lib_a-wcrtomb.o)
 .bss           0x0000000000000000        0x0 /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/../../../../riscv64-unknown-elf/lib/rv64imafdcxtheadc/lp64d/libc.a(lib_a-wcrtomb.o)
 .data          0x0000000000000000        0x0 /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/rv64imafdcxtheadc/lp64d/libgcc.a(eqtf2.o)
 .bss           0x0000000000000000        0x0 /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/rv64imafdcxtheadc/lp64d/libgcc.a(eqtf2.o)
 .data          0x0000000000000000        0x0 /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/rv64imafdcxtheadc/lp64d/libgcc.a(getf2.o)
 .bss           0x0000000000000000        0x0 /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/rv64imafdcxtheadc/lp64d/libgcc.a(getf2.o)
 .data          0x0000000000000000        0x0 /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/rv64imafdcxtheadc/lp64d/libgcc.a(letf2.o)
 .bss           0x0000000000000000        0x0 /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/rv64imafdcxtheadc/lp64d/libgcc.a(letf2.o)
 .data          0x0000000000000000        0x0 /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/rv64imafdcxtheadc/lp64d/libgcc.a(multf3.o)
 .bss           0x0000000000000000        0x0 /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/rv64imafdcxtheadc/lp64d/libgcc.a(multf3.o)
 .data          0x0000000000000000        0x0 /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/rv64imafdcxtheadc/lp64d/libgcc.a(subtf3.o)
 .bss           0x0000000000000000        0x0 /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/rv64imafdcxtheadc/lp64d/libgcc.a(subtf3.o)
 .data          0x0000000000000000        0x0 /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/rv64imafdcxtheadc/lp64d/libgcc.a(fixtfsi.o)
 .bss           0x0000000000000000        0x0 /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/rv64imafdcxtheadc/lp64d/libgcc.a(fixtfsi.o)
 .data          0x0000000000000000        0x0 /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/rv64imafdcxtheadc/lp64d/libgcc.a(floatsitf.o)
 .bss           0x0000000000000000        0x0 /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/rv64imafdcxtheadc/lp64d/libgcc.a(floatsitf.o)
 .data          0x0000000000000000        0x0 /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/rv64imafdcxtheadc/lp64d/libgcc.a(extenddftf2.o)
 .bss           0x0000000000000000        0x0 /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/rv64imafdcxtheadc/lp64d/libgcc.a(extenddftf2.o)
 .data          0x0000000000000000        0x0 /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/rv64imafdcxtheadc/lp64d/libgcc.a(trunctfdf2.o)
 .bss           0x0000000000000000        0x0 /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/rv64imafdcxtheadc/lp64d/libgcc.a(trunctfdf2.o)
 .data          0x0000000000000000        0x0 /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/rv64imafdcxtheadc/lp64d/libgcc.a(_clzsi2.o)
 .bss           0x0000000000000000        0x0 /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/rv64imafdcxtheadc/lp64d/libgcc.a(_clzsi2.o)
 .text          0x0000000000000000        0x0 /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/rv64imafdcxtheadc/lp64d/libgcc.a(_clz.o)
 .data          0x0000000000000000        0x0 /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/rv64imafdcxtheadc/lp64d/libgcc.a(_clz.o)
 .bss           0x0000000000000000        0x0 /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/rv64imafdcxtheadc/lp64d/libgcc.a(_clz.o)

Memory Configuration

Name             Origin             Length             Attributes
dram0_0_seg      0x00000000c2000000 0x0000000004000000 xrw
dram0_1_seg      0x00000000e0000000 0x0000000010000000 xrw
dram0_2_seg      0x00000000e8900000 0x0000000000080000 xrw
magic_info_seg   0x00000000ffff0000 0x0000000000000100 r
*default*        0x0000000000000000 0xffffffffffffffff

Linker script and memory map

LOAD emodules/drv_mcu_uart/dev_mcu.o
LOAD emodules/drv_mcu_uart/drv_mcu.o
LOAD emodules/drv_mcu_uart/magic.o
LOAD emodules/drv_mcu_uart/core/mcu_interrupt_process.o
LOAD emodules/drv_mcu_uart/core/mcu_aux_init_para.o
LOAD emodules/drv_mcu_uart/core/mcu_bypass_cmd_proc.o
LOAD emodules/drv_mcu_uart/core/mcu_update_proc.o
LOAD emodules/drv_mcu_uart/core/mcu_cmd_dispatcher.o
LOAD emodules/drv_mcu_uart/core/handlers/system/mcu_system_handler.o
LOAD emodules/drv_mcu_uart/core/handlers/media/mcu_tuner_handler.o
LOAD emodules/drv_mcu_uart/core/handlers/media/mcu_usb_handler.o
LOAD emodules/drv_mcu_uart/core/handlers/bluetooth/mcu_bt_handler.o
LOAD emodules/drv_mcu_uart/core/handlers/sxm/mcu_sxm_handler.o
LOAD emodules/drv_mcu_uart/core/handlers/settings/mcu_settings_handler.o
LOAD emodules/drv_mcu_uart/core/handlers/audio/mcu_audio_handler.o
LOAD emodules/drv_mcu_uart/core/handlers/upgrade/mcu_upgrade_handler.o
LOAD emodules/drv_mcu_uart/core/handlers/bypass/mcu_bypass_handler.o
LOAD emodules/drv_mcu_uart/core/handlers/misc/mcu_misc_handler.o
LOAD emodules/drv_mcu_uart/core/handlers/protocol/mcu_new_protocol_handler.o
LOAD emodules/drv_mcu_uart/core/handlers/protocol/system/mcu_system_category_handler.o
LOAD emodules/drv_mcu_uart/core/handlers/protocol/audio/mcu_audio_category_handler.o
LOAD emodules/drv_mcu_uart/core/handlers/protocol/tuner/mcu_tuner_category_handler.o
LOAD emodules/drv_mcu_uart/core/handlers/protocol/party/mcu_party_category_handler.o
LOAD emodules/drv_mcu_uart/core/handlers/protocol/usb/mcu_usb_category_handler.o
LOAD emodules/drv_mcu_uart/core/handlers/protocol/settings/mcu_settings_category_handler.o
START GROUP
START GROUP
LOAD ./elibrary/bin//libsyscall.a
LOAD ./elibrary/bin//libminic.a
LOAD ./elibrary/bin//libpub0.a
LOAD ./elibrary/bin//libcharenc.a
LOAD ./elibrary/bin//libcharset.a
END GROUP
START GROUP
LOAD ./elibrary/bin//libcheck_app.a
END GROUP
LOAD /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/../../../../riscv64-unknown-elf/lib/rv64imafdcxtheadc/lp64d/libstdc++.a
LOAD /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/../../../../riscv64-unknown-elf/lib/rv64imafdcxtheadc/lp64d/libc.a
LOAD /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/rv64imafdcxtheadc/lp64d/libgcc.a
LOAD /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/../../../../riscv64-unknown-elf/lib/rv64imafdcxtheadc/lp64d/libm.a
END GROUP
                0x00000000e8900000                __DRAM0_MOD_BASE = ORIGIN (dram0_2_seg)
                0x00000000ffff0000                __DRAM0_INF_BASE = ORIGIN (magic_info_seg)

.mod.text       0x00000000e8900000    0x1bf88
 *(.text)
 .text          0x00000000e8900000       0xd0 /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/../../../../riscv64-unknown-elf/lib/rv64imafdcxtheadc/lp64d/libc.a(lib_a-memcpy.o)
                0x00000000e8900000                memcpy
 .text          0x00000000e89000d0       0xaa /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/../../../../riscv64-unknown-elf/lib/rv64imafdcxtheadc/lp64d/libc.a(lib_a-memset.o)
                0x00000000e89000d0                memset
 *fill*         0x00000000e890017a        0x6 
 .text          0x00000000e8900180       0x68 /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/../../../../riscv64-unknown-elf/lib/rv64imafdcxtheadc/lp64d/libc.a(lib_a-printf.o)
                0x00000000e8900180                _printf_r
                0x00000000e89001b0                printf
 *fill*         0x00000000e89001e8        0x8 
 .text          0x00000000e89001f0       0x1e /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/../../../../riscv64-unknown-elf/lib/rv64imafdcxtheadc/lp64d/libc.a(lib_a-sysclose.o)
                0x00000000e89001f0                close
 *fill*         0x00000000e890020e        0x2 
 .text          0x00000000e8900210       0x3e /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/../../../../riscv64-unknown-elf/lib/rv64imafdcxtheadc/lp64d/libc.a(lib_a-sysopen.o)
                0x00000000e8900210                open
 *fill*         0x00000000e890024e        0x2 
 .text          0x00000000e8900250     0x1c02 /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/../../../../riscv64-unknown-elf/lib/rv64imafdcxtheadc/lp64d/libc.a(lib_a-vfprintf.o)
                0x00000000e8900250                _vfprintf_r
                0x00000000e8901d90                vfprintf
 *fill*         0x00000000e8901e52        0xe 
 .text          0x00000000e8901e60      0x104 /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/../../../../riscv64-unknown-elf/lib/rv64imafdcxtheadc/lp64d/libc.a(lib_a-wsetup.o)
                0x00000000e8901e60                __swsetup_r
 *fill*         0x00000000e8901f64        0xc 
 .text          0x00000000e8901f70      0x218 /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/../../../../riscv64-unknown-elf/lib/rv64imafdcxtheadc/lp64d/libc.a(lib_a-fflush.o)
                0x00000000e8901f70                __sflush_r
                0x00000000e8902110                _fflush_r
                0x00000000e8902150                fflush
 *fill*         0x00000000e8902188        0x8 
 .text          0x00000000e8902190      0x334 /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/../../../../riscv64-unknown-elf/lib/rv64imafdcxtheadc/lp64d/libc.a(lib_a-findfp.o)
                0x00000000e89021a0                _cleanup_r
                0x00000000e89022f0                __sfmoreglue
                0x00000000e8902340                __sfp
                0x00000000e8902410                _cleanup
                0x00000000e8902430                __sinit
                0x00000000e8902440                __sfp_lock_acquire
                0x00000000e8902450                __sfp_lock_release
                0x00000000e8902460                __sinit_lock_acquire
                0x00000000e8902470                __sinit_lock_release
                0x00000000e8902480                __fp_lock_all
                0x00000000e89024a0                __fp_unlock_all
 *fill*         0x00000000e89024c4        0xc 
 .text          0x00000000e89024d0      0x130 /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/../../../../riscv64-unknown-elf/lib/rv64imafdcxtheadc/lp64d/libc.a(lib_a-fwalk.o)
                0x00000000e89024d0                _fwalk
                0x00000000e8902560                _fwalk_reent
 .text          0x00000000e8902600     0x218e /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/../../../../riscv64-unknown-elf/lib/rv64imafdcxtheadc/lp64d/libc.a(lib_a-ldtoa.o)
                0x00000000e89038e0                _ldtoa_r
                0x00000000e8904750                _ldcheck
 *fill*         0x00000000e890478e        0x2 
 .text          0x00000000e8904790       0x52 /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/../../../../riscv64-unknown-elf/lib/rv64imafdcxtheadc/lp64d/libc.a(lib_a-localeconv.o)
                0x00000000e8904790                __localeconv_l
                0x00000000e89047a0                _localeconv_r
                0x00000000e89047c0                localeconv
 *fill*         0x00000000e89047e2        0xe 
 .text          0x00000000e89047f0       0xd0 /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/../../../../riscv64-unknown-elf/lib/rv64imafdcxtheadc/lp64d/libc.a(lib_a-locale.o)
                0x00000000e89047f0                _setlocale_r
                0x00000000e8904850                __locale_mb_cur_max
                0x00000000e8904870                __locale_ctype_ptr_l
                0x00000000e8904880                __locale_ctype_ptr
                0x00000000e89048a0                setlocale
 .text          0x00000000e89048c0      0x15a /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/../../../../riscv64-unknown-elf/lib/rv64imafdcxtheadc/lp64d/libc.a(lib_a-makebuf.o)
                0x00000000e89048c0                __swhatbuf_r
                0x00000000e8904950                __smakebuf_r
 *fill*         0x00000000e8904a1a        0x6 
 .text          0x00000000e8904a20       0x56 /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/../../../../riscv64-unknown-elf/lib/rv64imafdcxtheadc/lp64d/libc.a(lib_a-mbtowc_r.o)
                0x00000000e8904a20                _mbtowc_r
                0x00000000e8904a40                __ascii_mbtowc
 *fill*         0x00000000e8904a76        0xa 
 .text          0x00000000e8904a80       0xba /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/../../../../riscv64-unknown-elf/lib/rv64imafdcxtheadc/lp64d/libc.a(lib_a-memchr.o)
                0x00000000e8904a80                memchr
 *fill*         0x00000000e8904b3a        0x6 
 .text          0x00000000e8904b40      0xbda /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/../../../../riscv64-unknown-elf/lib/rv64imafdcxtheadc/lp64d/libc.a(lib_a-mprec.o)
                0x00000000e8904b40                _Balloc
                0x00000000e8904bb0                _Bfree
                0x00000000e8904bd0                __multadd
                0x00000000e8904c80                __s2b
                0x00000000e8904d50                __hi0bits
                0x00000000e8904da0                __lo0bits
                0x00000000e8904e20                __i2b
                0x00000000e8904e40                __multiply
                0x00000000e8904fb0                __pow5mult
                0x00000000e8905080                __lshift
                0x00000000e8905180                __mcmp
                0x00000000e89051c0                __mdiff
                0x00000000e8905310                __ulp
                0x00000000e8905370                __b2d
                0x00000000e8905490                __d2b
                0x00000000e8905580                __ratio
                0x00000000e8905620                _mprec_log10
                0x00000000e8905660                __copybits
                0x00000000e89056c0                __any_on
 *fill*         0x00000000e890571a        0x6 
 .text          0x00000000e8905720       0x72 /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/../../../../riscv64-unknown-elf/lib/rv64imafdcxtheadc/lp64d/libc.a(lib_a-s_frexp.o)
                0x00000000e8905720                frexp
 *fill*         0x00000000e8905792        0xe 
 .text          0x00000000e89057a0       0xa8 /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/../../../../riscv64-unknown-elf/lib/rv64imafdcxtheadc/lp64d/libc.a(lib_a-sprintf.o)
                0x00000000e89057a0                _sprintf_r
                0x00000000e89057f0                sprintf
 *fill*         0x00000000e8905848        0x8 
 .text          0x00000000e8905850      0x10e /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/../../../../riscv64-unknown-elf/lib/rv64imafdcxtheadc/lp64d/libc.a(lib_a-stdio.o)
                0x00000000e8905850                __sread
                0x00000000e8905890                __seofread
                0x00000000e89058a0                __swrite
                0x00000000e8905900                __sseek
                0x00000000e8905950                __sclose
 *fill*         0x00000000e890595e        0x2 
 .text          0x00000000e8905960       0x86 /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/../../../../riscv64-unknown-elf/lib/rv64imafdcxtheadc/lp64d/libc.a(lib_a-strcmp.o)
                0x00000000e8905960                strcmp
 *fill*         0x00000000e89059e6        0x2 
 .text          0x00000000e89059e8       0xb0 /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/../../../../riscv64-unknown-elf/lib/rv64imafdcxtheadc/lp64d/libc.a(lib_a-strcpy.o)
                0x00000000e89059e8                strcpy
 .text          0x00000000e8905a98       0xa0 /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/../../../../riscv64-unknown-elf/lib/rv64imafdcxtheadc/lp64d/libc.a(lib_a-strlen.o)
                0x00000000e8905a98                strlen
 *fill*         0x00000000e8905b38        0x8 
 .text          0x00000000e8905b40       0xa8 /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/../../../../riscv64-unknown-elf/lib/rv64imafdcxtheadc/lp64d/libc.a(lib_a-strncpy.o)
                0x00000000e8905b40                strncpy
 *fill*         0x00000000e8905be8        0x8 
 .text          0x00000000e8905bf0     0x1ab8 /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/../../../../riscv64-unknown-elf/lib/rv64imafdcxtheadc/lp64d/libc.a(lib_a-svfprintf.o)
                0x00000000e8905bf0                _svfprintf_r
 *fill*         0x00000000e89076a8        0x8 
 .text          0x00000000e89076b0      0xe44 /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/../../../../riscv64-unknown-elf/lib/rv64imafdcxtheadc/lp64d/libc.a(lib_a-vfiprintf.o)
                0x00000000e8907750                __sprint_r
                0x00000000e8907760                _vfiprintf_r
                0x00000000e8908440                vfiprintf
 *fill*         0x00000000e89084f4        0xc 
 .text          0x00000000e8908500       0x56 /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/../../../../riscv64-unknown-elf/lib/rv64imafdcxtheadc/lp64d/libc.a(lib_a-wctomb_r.o)
                0x00000000e8908500                _wctomb_r
                0x00000000e8908520                __ascii_wctomb
 *fill*         0x00000000e8908556        0xa 
 .text          0x00000000e8908560       0xc0 /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/../../../../riscv64-unknown-elf/lib/rv64imafdcxtheadc/lp64d/libc.a(lib_a-fclose.o)
                0x00000000e8908560                _fclose_r
                0x00000000e8908610                fclose
 .text          0x00000000e8908620      0x166 /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/../../../../riscv64-unknown-elf/lib/rv64imafdcxtheadc/lp64d/libc.a(lib_a-fputwc.o)
                0x00000000e8908620                __fputwc
                0x00000000e89086f0                _fputwc_r
                0x00000000e8908720                fputwc
 *fill*         0x00000000e8908786        0xa 
 .text          0x00000000e8908790      0x360 /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/../../../../riscv64-unknown-elf/lib/rv64imafdcxtheadc/lp64d/libc.a(lib_a-fvwrite.o)
                0x00000000e8908790                __sfvwrite_r
 .text          0x00000000e8908af0       0xd2 /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/../../../../riscv64-unknown-elf/lib/rv64imafdcxtheadc/lp64d/libc.a(lib_a-memmove-stub.o)
                0x00000000e8908af0                memmove
 *fill*         0x00000000e8908bc2        0xe 
 .text          0x00000000e8908bd0      0xc7e /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/../../../../riscv64-unknown-elf/lib/rv64imafdcxtheadc/lp64d/libc.a(lib_a-svfiprintf.o)
                0x00000000e8908bd0                __ssprint_r
                0x00000000e8908d40                _svfiprintf_r
 *fill*         0x00000000e890984e        0x2 
 .text          0x00000000e8909850      0x11c /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/../../../../riscv64-unknown-elf/lib/rv64imafdcxtheadc/lp64d/libc.a(lib_a-wbuf.o)
                0x00000000e8909850                __swbuf_r
                0x00000000e8909950                __swbuf
 *fill*         0x00000000e890996c        0x4 
 .text          0x00000000e8909970       0xc6 /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/../../../../riscv64-unknown-elf/lib/rv64imafdcxtheadc/lp64d/libc.a(lib_a-wcrtomb.o)
                0x00000000e8909970                _wcrtomb_r
                0x00000000e89099d0                wcrtomb
 .text          0x00000000e8909a36       0x94 /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/rv64imafdcxtheadc/lp64d/libgcc.a(eqtf2.o)
                0x00000000e8909a36                __eqtf2
                0x00000000e8909a36                __netf2
 .text          0x00000000e8909aca       0xa4 /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/rv64imafdcxtheadc/lp64d/libgcc.a(getf2.o)
                0x00000000e8909aca                __getf2
                0x00000000e8909aca                __gttf2
 .text          0x00000000e8909b6e       0xa4 /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/rv64imafdcxtheadc/lp64d/libgcc.a(letf2.o)
                0x00000000e8909b6e                __letf2
                0x00000000e8909b6e                __lttf2
 .text          0x00000000e8909c12      0x5de /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/rv64imafdcxtheadc/lp64d/libgcc.a(multf3.o)
                0x00000000e8909c12                __multf3
 .text          0x00000000e890a1f0      0x81e /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/rv64imafdcxtheadc/lp64d/libgcc.a(subtf3.o)
                0x00000000e890a1f0                __subtf3
 .text          0x00000000e890aa0e       0x90 /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/rv64imafdcxtheadc/lp64d/libgcc.a(fixtfsi.o)
                0x00000000e890aa0e                __fixtfsi
 .text          0x00000000e890aa9e       0x60 /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/rv64imafdcxtheadc/lp64d/libgcc.a(floatsitf.o)
                0x00000000e890aa9e                __floatsitf
 .text          0x00000000e890aafe       0xc0 /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/rv64imafdcxtheadc/lp64d/libgcc.a(extenddftf2.o)
                0x00000000e890aafe                __extenddftf2
 .text          0x00000000e890abbe      0x228 /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/rv64imafdcxtheadc/lp64d/libgcc.a(trunctfdf2.o)
                0x00000000e890abbe                __trunctfdf2
 .text          0x00000000e890ade6       0x2e /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/rv64imafdcxtheadc/lp64d/libgcc.a(_clzsi2.o)
                0x00000000e890ade6                __clzdi2
 *(.text.*)
 .text.mcu_key_open
                0x00000000e890ae14       0x12 emodules/drv_mcu_uart/dev_mcu.o
 .text.mcu_key_close
                0x00000000e890ae26       0x12 emodules/drv_mcu_uart/dev_mcu.o
 .text.mcu_key_read
                0x00000000e890ae38        0x6 emodules/drv_mcu_uart/dev_mcu.o
 .text.mcu_key_ioctrl
                0x00000000e890ae3e        0x4 emodules/drv_mcu_uart/dev_mcu.o
 .text.hal_mcu_init
                0x00000000e890ae42        0x4 emodules/drv_mcu_uart/dev_mcu.o
                0x00000000e890ae42                hal_mcu_init
 .text.sunxi_mcu_init
                0x00000000e890ae46        0x4 emodules/drv_mcu_uart/dev_mcu.o
 .text.sunxi_mcu_open
                0x00000000e890ae4a       0x20 emodules/drv_mcu_uart/dev_mcu.o
 .text.sunxi_mcu_close
                0x00000000e890ae6a       0x20 emodules/drv_mcu_uart/dev_mcu.o
 .text.sunxi_mcu_write
                0x00000000e890ae8a        0x4 emodules/drv_mcu_uart/dev_mcu.o
 .text.sunxi_mcu_control
                0x00000000e890ae8e       0x26 emodules/drv_mcu_uart/dev_mcu.o
 .text.sunxi_mcu_read
                0x00000000e890aeb4        0x4 emodules/drv_mcu_uart/dev_mcu.o
 .text.mcu_key_write
                0x00000000e890aeb8        0x6 emodules/drv_mcu_uart/dev_mcu.o
 .text.hal_mcu_uninit
                0x00000000e890aebe        0x4 emodules/drv_mcu_uart/dev_mcu.o
                0x00000000e890aebe                hal_mcu_uninit
 .text.McuKeyDevInit
                0x00000000e890aec2       0xbe emodules/drv_mcu_uart/dev_mcu.o
                0x00000000e890aec2                McuKeyDevInit
 .text.McuKeyDevExit
                0x00000000e890af80       0x2e emodules/drv_mcu_uart/dev_mcu.o
                0x00000000e890af80                McuKeyDevExit
 .text.hal_mcu_control
                0x00000000e890afae       0x8c emodules/drv_mcu_uart/dev_mcu.o
                0x00000000e890afae                hal_mcu_control
 .text.sunxi_driver_mcu_init
                0x00000000e890b03a       0x74 emodules/drv_mcu_uart/dev_mcu.o
                0x00000000e890b03a                sunxi_driver_mcu_init
 .text.sunxi_driver_mcu_uninit
                0x00000000e890b0ae       0x1a emodules/drv_mcu_uart/dev_mcu.o
                0x00000000e890b0ae                sunxi_driver_mcu_uninit
 .text.DRV_MCU_MInit
                0x00000000e890b0c8       0x22 emodules/drv_mcu_uart/drv_mcu.o
                0x00000000e890b0c8                DRV_MCU_MInit
 .text.DRV_MCU_MExit
                0x00000000e890b0ea       0x20 emodules/drv_mcu_uart/drv_mcu.o
                0x00000000e890b0ea                DRV_MCU_MExit
 .text.DRV_MCU_MOpen
                0x00000000e890b10a        0xe emodules/drv_mcu_uart/drv_mcu.o
                0x00000000e890b10a                DRV_MCU_MOpen
 .text.DRV_MCU_MClose
                0x00000000e890b118        0x4 emodules/drv_mcu_uart/drv_mcu.o
                0x00000000e890b118                DRV_MCU_MClose
 .text.DRV_MCU_MRead
                0x00000000e890b11c        0x6 emodules/drv_mcu_uart/drv_mcu.o
                0x00000000e890b11c                DRV_MCU_MRead
 .text.DRV_MCU_MWrite
                0x00000000e890b122        0x6 emodules/drv_mcu_uart/drv_mcu.o
                0x00000000e890b122                DRV_MCU_MWrite
 .text.DRV_MCU_MIoctrl
                0x00000000e890b128       0x6a emodules/drv_mcu_uart/drv_mcu.o
                0x00000000e890b128                DRV_MCU_MIoctrl
 .text.push_cmd_to_queue
                0x00000000e890b192       0x46 emodules/drv_mcu_uart/core/mcu_interrupt_process.o
 .text.uninit_uart_buff_queue
                0x00000000e890b1d8       0x60 emodules/drv_mcu_uart/core/mcu_interrupt_process.o
 .text.uninit_uart_send_buffer
                0x00000000e890b238       0x48 emodules/drv_mcu_uart/core/mcu_interrupt_process.o
 .text.extract_sub_cmd_data
                0x00000000e890b280       0xdc emodules/drv_mcu_uart/core/mcu_interrupt_process.o
 .text.uart3_tx_enable.part.2
                0x00000000e890b35c       0x20 emodules/drv_mcu_uart/core/mcu_interrupt_process.o
 .text.check_iap_exist
                0x00000000e890b37c       0x3c emodules/drv_mcu_uart/core/mcu_interrupt_process.o
                0x00000000e890b37c                check_iap_exist
 .text.mcu_get_paramter_hdle
                0x00000000e890b3b8        0xc emodules/drv_mcu_uart/core/mcu_interrupt_process.o
                0x00000000e890b3b8                mcu_get_paramter_hdle
 .text.wait_debounce
                0x00000000e890b3c4       0x10 emodules/drv_mcu_uart/core/mcu_interrupt_process.o
                0x00000000e890b3c4                wait_debounce
 .text.init_uart3_tx_disable
                0x00000000e890b3d4       0x38 emodules/drv_mcu_uart/core/mcu_interrupt_process.o
                0x00000000e890b3d4                init_uart3_tx_disable
 .text.uart3_tx_enable
                0x00000000e890b40c       0x50 emodules/drv_mcu_uart/core/mcu_interrupt_process.o
                0x00000000e890b40c                uart3_tx_enable
 .text.CE_init_io
                0x00000000e890b45c       0x3e emodules/drv_mcu_uart/core/mcu_interrupt_process.o
                0x00000000e890b45c                CE_init_io
 .text.CE2INPUT
                0x00000000e890b49a       0x18 emodules/drv_mcu_uart/core/mcu_interrupt_process.o
                0x00000000e890b49a                CE2INPUT
 .text.CE2OUTPUT
                0x00000000e890b4b2       0x18 emodules/drv_mcu_uart/core/mcu_interrupt_process.o
                0x00000000e890b4b2                CE2OUTPUT
 .text.SetCE_HL
                0x00000000e890b4ca       0x34 emodules/drv_mcu_uart/core/mcu_interrupt_process.o
                0x00000000e890b4ca                SetCE_HL
 .text.GetCE_HL
                0x00000000e890b4fe       0x32 emodules/drv_mcu_uart/core/mcu_interrupt_process.o
                0x00000000e890b4fe                GetCE_HL
 .text.EnableCE
                0x00000000e890b530       0x58 emodules/drv_mcu_uart/core/mcu_interrupt_process.o
                0x00000000e890b530                EnableCE
 .text.DisableCE
                0x00000000e890b588       0x38 emodules/drv_mcu_uart/core/mcu_interrupt_process.o
                0x00000000e890b588                DisableCE
 .text.mcu_set_lcd_brightness
                0x00000000e890b5c0       0x4a emodules/drv_mcu_uart/core/mcu_interrupt_process.o
                0x00000000e890b5c0                mcu_set_lcd_brightness
 .text.uart_check_usb_exist
                0x00000000e890b60a       0x50 emodules/drv_mcu_uart/core/mcu_interrupt_process.o
                0x00000000e890b60a                uart_check_usb_exist
 .text.write_sequel_buffer
                0x00000000e890b65a       0xb8 emodules/drv_mcu_uart/core/mcu_interrupt_process.o
                0x00000000e890b65a                write_sequel_buffer
 .text.read_sequel_buffer
                0x00000000e890b712       0x76 emodules/drv_mcu_uart/core/mcu_interrupt_process.o
                0x00000000e890b712                read_sequel_buffer
 .text.update_sequel_buffer_RdPtr
                0x00000000e890b788       0x50 emodules/drv_mcu_uart/core/mcu_interrupt_process.o
                0x00000000e890b788                update_sequel_buffer_RdPtr
 .text.uart_SubCmd_send2main
                0x00000000e890b7d8       0xc4 emodules/drv_mcu_uart/core/mcu_interrupt_process.o
                0x00000000e890b7d8                uart_SubCmd_send2main
 .text.uart_send_data_ex
                0x00000000e890b89c       0x5e emodules/drv_mcu_uart/core/mcu_interrupt_process.o
                0x00000000e890b89c                uart_send_data_ex
 .text.uart_send_cmd
                0x00000000e890b8fa      0x220 emodules/drv_mcu_uart/core/mcu_interrupt_process.o
                0x00000000e890b8fa                uart_send_cmd
 .text.uart_send_cmd_data
                0x00000000e890bb1a     0x1298 emodules/drv_mcu_uart/core/mcu_interrupt_process.o
                0x00000000e890bb1a                uart_send_cmd_data
 .text.uart_sub_send_cmd_data
                0x00000000e890cdb2        0x4 emodules/drv_mcu_uart/core/mcu_interrupt_process.o
                0x00000000e890cdb2                uart_sub_send_cmd_data
 .text.uart_ack_arrived_sequel_proc
                0x00000000e890cdb6       0xce emodules/drv_mcu_uart/core/mcu_interrupt_process.o
                0x00000000e890cdb6                uart_ack_arrived_sequel_proc
 .text.omit_string_tail_space
                0x00000000e890ce84       0x22 emodules/drv_mcu_uart/core/mcu_interrupt_process.o
                0x00000000e890ce84                omit_string_tail_space
 .text.mcu_push_cmd_to_queue
                0x00000000e890cea6        0x4 emodules/drv_mcu_uart/core/mcu_interrupt_process.o
                0x00000000e890cea6                mcu_push_cmd_to_queue
 .text.sxm_crc32_initialize
                0x00000000e890ceaa       0x40 emodules/drv_mcu_uart/core/mcu_interrupt_process.o
                0x00000000e890ceaa                sxm_crc32_initialize
 .text.sxm_crc32_calculate
                0x00000000e890ceea       0x34 emodules/drv_mcu_uart/core/mcu_interrupt_process.o
                0x00000000e890ceea                sxm_crc32_calculate
 .text.reset_artwork_pkg
                0x00000000e890cf1e       0x10 emodules/drv_mcu_uart/core/mcu_interrupt_process.o
                0x00000000e890cf1e                reset_artwork_pkg
 .text.reset_artwork_buf
                0x00000000e890cf2e       0x3e emodules/drv_mcu_uart/core/mcu_interrupt_process.o
                0x00000000e890cf2e                reset_artwork_buf
 .text.mcu_key_cmd_put_key_encoder
                0x00000000e890cf6c       0x4c emodules/drv_mcu_uart/core/mcu_interrupt_process.o
                0x00000000e890cf6c                mcu_key_cmd_put_key_encoder
 .text.reset_remote_pkg_head
                0x00000000e890cfb8       0x10 emodules/drv_mcu_uart/core/mcu_interrupt_process.o
                0x00000000e890cfb8                reset_remote_pkg_head
 .text.check_remote_device_match
                0x00000000e890cfc8       0x4a emodules/drv_mcu_uart/core/mcu_interrupt_process.o
                0x00000000e890cfc8                check_remote_device_match
 .text.reset_uart_sequel_buf
                0x00000000e890d012       0x16 emodules/drv_mcu_uart/core/mcu_interrupt_process.o
                0x00000000e890d012                reset_uart_sequel_buf
 .text.reset_uart_sub_get_sequel_buf
                0x00000000e890d028       0x14 emodules/drv_mcu_uart/core/mcu_interrupt_process.o
                0x00000000e890d028                reset_uart_sub_get_sequel_buf
 .text.mcu_bus_thread
                0x00000000e890d03c     0x1b0e emodules/drv_mcu_uart/core/mcu_interrupt_process.o
 .text.mcu_message_process_init
                0x00000000e890eb4a      0xaec emodules/drv_mcu_uart/core/mcu_interrupt_process.o
                0x00000000e890eb4a                mcu_message_process_init
 .text.mcu_message_process_uninit
                0x00000000e890f636       0xdc emodules/drv_mcu_uart/core/mcu_interrupt_process.o
                0x00000000e890f636                mcu_message_process_uninit
 .text.hu_get_remote_mount
                0x00000000e890f712       0xf8 emodules/drv_mcu_uart/core/mcu_bypass_cmd_proc.o
                0x00000000e890f712                hu_get_remote_mount
 .text.bypass_cmd_proc
                0x00000000e890f80a      0xab2 emodules/drv_mcu_uart/core/mcu_bypass_cmd_proc.o
                0x00000000e890f80a                bypass_cmd_proc
 .text.remote_pass2hu_mount
                0x00000000e89102bc       0x4a emodules/drv_mcu_uart/core/mcu_bypass_cmd_proc.o
                0x00000000e89102bc                remote_pass2hu_mount
 .text.reset_update_data_send
                0x00000000e8910306       0x28 emodules/drv_mcu_uart/core/mcu_update_proc.o
                0x00000000e8910306                reset_update_data_send
 .text.uart_main_send_update_data
                0x00000000e891032e       0x5a emodules/drv_mcu_uart/core/mcu_update_proc.o
                0x00000000e891032e                uart_main_send_update_data
 .text.prepare_update_data_send
                0x00000000e8910388       0xd4 emodules/drv_mcu_uart/core/mcu_update_proc.o
                0x00000000e8910388                prepare_update_data_send
 .text.make_update_data_send_page
                0x00000000e891045c      0x134 emodules/drv_mcu_uart/core/mcu_update_proc.o
                0x00000000e891045c                make_update_data_send_page
 .text.hu_set_remote_update_status
                0x00000000e8910590       0x86 emodules/drv_mcu_uart/core/mcu_update_proc.o
                0x00000000e8910590                hu_set_remote_update_status
 .text.is_all_remote_update_success
                0x00000000e8910616       0x1e emodules/drv_mcu_uart/core/mcu_update_proc.o
                0x00000000e8910616                is_all_remote_update_success
 .text.mcu_cmd_dispatcher_exit
                0x00000000e8910634       0x3c emodules/drv_mcu_uart/core/mcu_cmd_dispatcher.o
                0x00000000e8910634                mcu_cmd_dispatcher_exit
 .text.mcu_cmd_dispatch
                0x00000000e8910670       0xfc emodules/drv_mcu_uart/core/mcu_cmd_dispatcher.o
                0x00000000e8910670                mcu_cmd_dispatch
 .text.mcu_cmd_register_handler
                0x00000000e891076c       0x5a emodules/drv_mcu_uart/core/mcu_cmd_dispatcher.o
                0x00000000e891076c                mcu_cmd_register_handler
 .text.mcu_cmd_dispatcher_init
                0x00000000e89107c6      0x2b8 emodules/drv_mcu_uart/core/mcu_cmd_dispatcher.o
                0x00000000e89107c6                mcu_cmd_dispatcher_init
 .text.mcu_system_cmd_handler
                0x00000000e8910a7e     0x2df0 emodules/drv_mcu_uart/core/handlers/system/mcu_system_handler.o
                0x00000000e8910a7e                mcu_system_cmd_handler
 .text.mcu_tuner_cmd_handler
                0x00000000e891386e      0x8a2 emodules/drv_mcu_uart/core/handlers/media/mcu_tuner_handler.o
                0x00000000e891386e                mcu_tuner_cmd_handler
 .text.mcu_usb_cmd_handler
                0x00000000e8914110     0x1182 emodules/drv_mcu_uart/core/handlers/media/mcu_usb_handler.o
                0x00000000e8914110                mcu_usb_cmd_handler
 .text.mcu_bt_cmd_handler
                0x00000000e8915292      0x224 emodules/drv_mcu_uart/core/handlers/bluetooth/mcu_bt_handler.o
                0x00000000e8915292                mcu_bt_cmd_handler
 .text.mcu_sxm_cmd_handler
                0x00000000e89154b6     0x18e8 emodules/drv_mcu_uart/core/handlers/sxm/mcu_sxm_handler.o
                0x00000000e89154b6                mcu_sxm_cmd_handler
 .text.mcu_settings_cmd_handler
                0x00000000e8916d9e      0xd9c emodules/drv_mcu_uart/core/handlers/settings/mcu_settings_handler.o
                0x00000000e8916d9e                mcu_settings_cmd_handler
 .text.mcu_audio_cmd_handler
                0x00000000e8917b3a      0x110 emodules/drv_mcu_uart/core/handlers/audio/mcu_audio_handler.o
                0x00000000e8917b3a                mcu_audio_cmd_handler
 .text.mcu_upgrade_cmd_handler
                0x00000000e8917c4a      0x23e emodules/drv_mcu_uart/core/handlers/upgrade/mcu_upgrade_handler.o
                0x00000000e8917c4a                mcu_upgrade_cmd_handler
 .text.mcu_bypass_cmd_handler
                0x00000000e8917e88       0x30 emodules/drv_mcu_uart/core/handlers/bypass/mcu_bypass_handler.o
                0x00000000e8917e88                mcu_bypass_cmd_handler
 .text.mcu_misc_cmd_handler
                0x00000000e8917eb8      0x1e4 emodules/drv_mcu_uart/core/handlers/misc/mcu_misc_handler.o
                0x00000000e8917eb8                mcu_misc_cmd_handler
 .text.mcu_new_protocol_cmd_handler
                0x00000000e891809c      0x138 emodules/drv_mcu_uart/core/handlers/protocol/mcu_new_protocol_handler.o
                0x00000000e891809c                mcu_new_protocol_cmd_handler
 .text.mcu_new_protocol_handler_register
                0x00000000e89181d4       0x54 emodules/drv_mcu_uart/core/handlers/protocol/mcu_new_protocol_handler.o
                0x00000000e89181d4                mcu_new_protocol_handler_register
 .text.mcu_new_protocol_register_category_handler
                0x00000000e8918228       0x8e emodules/drv_mcu_uart/core/handlers/protocol/mcu_new_protocol_handler.o
                0x00000000e8918228                mcu_new_protocol_register_category_handler
 .text.mcu_new_push_cmdpkt_to_send_queue
                0x00000000e89182b6       0xcc emodules/drv_mcu_uart/core/handlers/protocol/mcu_new_protocol_handler.o
                0x00000000e89182b6                mcu_new_push_cmdpkt_to_send_queue
 .text.mcu_new_protocol_send_category_data
                0x00000000e8918382       0x72 emodules/drv_mcu_uart/core/handlers/protocol/mcu_new_protocol_handler.o
                0x00000000e8918382                mcu_new_protocol_send_category_data
 .text.mcu_new_protocol_get_category_data
                0x00000000e89183f4       0x78 emodules/drv_mcu_uart/core/handlers/protocol/mcu_new_protocol_handler.o
                0x00000000e89183f4                mcu_new_protocol_get_category_data
 .text.mcu_new_get_init_data
                0x00000000e891846c       0xc0 emodules/drv_mcu_uart/core/handlers/protocol/mcu_new_protocol_handler.o
                0x00000000e891846c                mcu_new_get_init_data
 .text.mcu_new_get_source_init_data
                0x00000000e891852c       0x40 emodules/drv_mcu_uart/core/handlers/protocol/mcu_new_protocol_handler.o
                0x00000000e891852c                mcu_new_get_source_init_data
 .text.system_main_handler
                0x00000000e891856c      0x62e emodules/drv_mcu_uart/core/handlers/protocol/system/mcu_system_category_handler.o
                0x00000000e891856c                system_main_handler
 .text.mcu_system_category_handler_register
                0x00000000e8918b9a       0x3e emodules/drv_mcu_uart/core/handlers/protocol/system/mcu_system_category_handler.o
                0x00000000e8918b9a                mcu_system_category_handler_register
 .text.handle_equalizer_list_data
                0x00000000e8918bd8       0xa4 emodules/drv_mcu_uart/core/handlers/protocol/audio/mcu_audio_category_handler.o
 .text.handle_crossover_list_data
                0x00000000e8918c7c       0x8a emodules/drv_mcu_uart/core/handlers/protocol/audio/mcu_audio_category_handler.o
 .text.handle_simple_audio_data
                0x00000000e8918d06      0x120 emodules/drv_mcu_uart/core/handlers/protocol/audio/mcu_audio_category_handler.o
 .text.mcu_audio_category_handler_register
                0x00000000e8918e26       0x40 emodules/drv_mcu_uart/core/handlers/protocol/audio/mcu_audio_category_handler.o
                0x00000000e8918e26                mcu_audio_category_handler_register
 .text.audio_restore_default_settings
                0x00000000e8918e66      0x12c emodules/drv_mcu_uart/core/handlers/protocol/audio/mcu_audio_category_handler.o
                0x00000000e8918e66                audio_restore_default_settings
 .text.audio_main_handler
                0x00000000e8918f92      0x8e8 emodules/drv_mcu_uart/core/handlers/protocol/audio/mcu_audio_category_handler.o
                0x00000000e8918f92                audio_main_handler
 .text.fm_main_handler
                0x00000000e891987a      0x732 emodules/drv_mcu_uart/core/handlers/protocol/tuner/mcu_tuner_category_handler.o
                0x00000000e891987a                fm_main_handler
 .text.wb_main_handler
                0x00000000e8919fac      0x314 emodules/drv_mcu_uart/core/handlers/protocol/tuner/mcu_tuner_category_handler.o
                0x00000000e8919fac                wb_main_handler
 .text.am_main_handler
                0x00000000e891a2c0      0x3f2 emodules/drv_mcu_uart/core/handlers/protocol/tuner/mcu_tuner_category_handler.o
                0x00000000e891a2c0                am_main_handler
 .text.mcu_tuner_category_handler_register
                0x00000000e891a6b2       0xd6 emodules/drv_mcu_uart/core/handlers/protocol/tuner/mcu_tuner_category_handler.o
                0x00000000e891a6b2                mcu_tuner_category_handler_register
 .text.party_main_handler
                0x00000000e891a788      0x650 emodules/drv_mcu_uart/core/handlers/protocol/party/mcu_party_category_handler.o
                0x00000000e891a788                party_main_handler
 .text.party_init_status
                0x00000000e891add8       0x26 emodules/drv_mcu_uart/core/handlers/protocol/party/mcu_party_category_handler.o
                0x00000000e891add8                party_init_status
 .text.mcu_party_category_handler_register
                0x00000000e891adfe       0x4e emodules/drv_mcu_uart/core/handlers/protocol/party/mcu_party_category_handler.o
                0x00000000e891adfe                mcu_party_category_handler_register
 .text.usb_disk_main_handler
                0x00000000e891ae4c      0x20e emodules/drv_mcu_uart/core/handlers/protocol/usb/mcu_usb_category_handler.o
                0x00000000e891ae4c                usb_disk_main_handler
 .text.usb_ipod_main_handler
                0x00000000e891b05a      0x210 emodules/drv_mcu_uart/core/handlers/protocol/usb/mcu_usb_category_handler.o
                0x00000000e891b05a                usb_ipod_main_handler
 .text.mcu_usb_disk_category_handler_register
                0x00000000e891b26a       0x4e emodules/drv_mcu_uart/core/handlers/protocol/usb/mcu_usb_category_handler.o
                0x00000000e891b26a                mcu_usb_disk_category_handler_register
 .text.mcu_usb_ipod_category_handler_register
                0x00000000e891b2b8       0x4e emodules/drv_mcu_uart/core/handlers/protocol/usb/mcu_usb_category_handler.o
                0x00000000e891b2b8                mcu_usb_ipod_category_handler_register
 .text.settings_init_status
                0x00000000e891b306       0x42 emodules/drv_mcu_uart/core/handlers/protocol/settings/mcu_settings_category_handler.o
 .text.handle_device_rename_data
                0x00000000e891b348       0x94 emodules/drv_mcu_uart/core/handlers/protocol/settings/mcu_settings_category_handler.o
 .text.handle_switch_data.constprop.3
                0x00000000e891b3dc       0xe4 emodules/drv_mcu_uart/core/handlers/protocol/settings/mcu_settings_category_handler.o
 .text.settings_main_handler
                0x00000000e891b4c0      0x4e0 emodules/drv_mcu_uart/core/handlers/protocol/settings/mcu_settings_category_handler.o
                0x00000000e891b4c0                settings_main_handler
 .text.mcu_settings_category_handler_register
                0x00000000e891b9a0       0x32 emodules/drv_mcu_uart/core/handlers/protocol/settings/mcu_settings_category_handler.o
                0x00000000e891b9a0                mcu_settings_category_handler_register
 .text.rt_device_register
                0x00000000e891b9d2       0x16 ./elibrary/bin//libsyscall.a(syscall.o)
                0x00000000e891b9d2                rt_device_register
 .text.rt_device_unregister
                0x00000000e891b9e8       0x16 ./elibrary/bin//libsyscall.a(syscall.o)
                0x00000000e891b9e8                rt_device_unregister
 .text.esFSYS_fclose
                0x00000000e891b9fe       0x18 ./elibrary/bin//libsyscall.a(syscall.o)
                0x00000000e891b9fe                esFSYS_fclose
 .text.esFSYS_fopen
                0x00000000e891ba16       0x16 ./elibrary/bin//libsyscall.a(syscall.o)
                0x00000000e891ba16                esFSYS_fopen
 .text.esFSYS_fread
                0x00000000e891ba2c       0x20 ./elibrary/bin//libsyscall.a(syscall.o)
                0x00000000e891ba2c                esFSYS_fread
 .text.esFSYS_fwrite
                0x00000000e891ba4c       0x20 ./elibrary/bin//libsyscall.a(syscall.o)
                0x00000000e891ba4c                esFSYS_fwrite
 .text.esINPUT_RegDev
                0x00000000e891ba6c       0x18 ./elibrary/bin//libsyscall.a(syscall.o)
                0x00000000e891ba6c                esINPUT_RegDev
 .text.esINPUT_SendEvent
                0x00000000e891ba84       0x20 ./elibrary/bin//libsyscall.a(syscall.o)
                0x00000000e891ba84                esINPUT_SendEvent
 .text.esINPUT_UnregDev
                0x00000000e891baa4       0x18 ./elibrary/bin//libsyscall.a(syscall.o)
                0x00000000e891baa4                esINPUT_UnregDev
 .text.esKRNL_SchedLock
                0x00000000e891babc       0x16 ./elibrary/bin//libsyscall.a(syscall.o)
                0x00000000e891babc                esKRNL_SchedLock
 .text.esKRNL_SchedUnlock
                0x00000000e891bad2       0x16 ./elibrary/bin//libsyscall.a(syscall.o)
                0x00000000e891bad2                esKRNL_SchedUnlock
 .text.esKRNL_SemCreate
                0x00000000e891bae8       0x16 ./elibrary/bin//libsyscall.a(syscall.o)
                0x00000000e891bae8                esKRNL_SemCreate
 .text.esKRNL_SemDel
                0x00000000e891bafe       0x16 ./elibrary/bin//libsyscall.a(syscall.o)
                0x00000000e891bafe                esKRNL_SemDel
 .text.esKRNL_TaskNameSet
                0x00000000e891bb14       0x1a ./elibrary/bin//libsyscall.a(syscall.o)
                0x00000000e891bb14                esKRNL_TaskNameSet
 .text.esKRNL_TCreate
                0x00000000e891bb2e       0x1c ./elibrary/bin//libsyscall.a(syscall.o)
                0x00000000e891bb2e                esKRNL_TCreate
 .text.esKRNL_TDel
                0x00000000e891bb4a       0x1e ./elibrary/bin//libsyscall.a(syscall.o)
                0x00000000e891bb4a                esKRNL_TDel
 .text.esKRNL_TDelReq
                0x00000000e891bb68       0x1e ./elibrary/bin//libsyscall.a(syscall.o)
                0x00000000e891bb68                esKRNL_TDelReq
 .text.esKRNL_TimeDly
                0x00000000e891bb86       0x16 ./elibrary/bin//libsyscall.a(syscall.o)
                0x00000000e891bb86                esKRNL_TimeDly
 .text.esKRNL_TimeGet
                0x00000000e891bb9c       0x18 ./elibrary/bin//libsyscall.a(syscall.o)
                0x00000000e891bb9c                esKRNL_TimeGet
 .text.esKSRV_Get_Display_Hld
                0x00000000e891bbb4       0x16 ./elibrary/bin//libsyscall.a(syscall.o)
                0x00000000e891bbb4                esKSRV_Get_Display_Hld
 .text.esKSRV_Random
                0x00000000e891bbca       0x1c ./elibrary/bin//libsyscall.a(syscall.o)
                0x00000000e891bbca                esKSRV_Random
 .text.esKSRV_SendMsg
                0x00000000e891bbe6       0x20 ./elibrary/bin//libsyscall.a(syscall.o)
                0x00000000e891bbe6                esKSRV_SendMsg
 .text.esMEMS_Balloc
                0x00000000e891bc06       0x16 ./elibrary/bin//libsyscall.a(syscall.o)
                0x00000000e891bc06                esMEMS_Balloc
 .text.esMEMS_Bfree
                0x00000000e891bc1c       0x16 ./elibrary/bin//libsyscall.a(syscall.o)
                0x00000000e891bc1c                esMEMS_Bfree
 .text.esMEMS_Malloc
                0x00000000e891bc32       0x1a ./elibrary/bin//libsyscall.a(syscall.o)
                0x00000000e891bc32                esMEMS_Malloc
 .text.esMEMS_Mfree
                0x00000000e891bc4c       0x16 ./elibrary/bin//libsyscall.a(syscall.o)
                0x00000000e891bc4c                esMEMS_Mfree
 .text.esMEMS_Palloc
                0x00000000e891bc62       0x1e ./elibrary/bin//libsyscall.a(syscall.o)
                0x00000000e891bc62                esMEMS_Palloc
 .text.esMEMS_Pfree
                0x00000000e891bc80       0x1a ./elibrary/bin//libsyscall.a(syscall.o)
                0x00000000e891bc80                esMEMS_Pfree
 .text.esMODS_MIoctrl
                0x00000000e891bc9a       0x1a ./elibrary/bin//libsyscall.a(syscall.o)
                0x00000000e891bc9a                esMODS_MIoctrl
 .text.esPINS_PinGrpRel
                0x00000000e891bcb4       0x16 ./elibrary/bin//libsyscall.a(syscall.o)
                0x00000000e891bcb4                esPINS_PinGrpRel
 .text.esPINS_PinGrpReq
                0x00000000e891bcca       0x16 ./elibrary/bin//libsyscall.a(syscall.o)
                0x00000000e891bcca                esPINS_PinGrpReq
 .text.esPINS_ReadPinData
                0x00000000e891bce0       0x16 ./elibrary/bin//libsyscall.a(syscall.o)
                0x00000000e891bce0                esPINS_ReadPinData
 .text.esPINS_SetPinIO
                0x00000000e891bcf6       0x16 ./elibrary/bin//libsyscall.a(syscall.o)
                0x00000000e891bcf6                esPINS_SetPinIO
 .text.esPINS_WritePinData
                0x00000000e891bd0c       0x1a ./elibrary/bin//libsyscall.a(syscall.o)
                0x00000000e891bd0c                esPINS_WritePinData
 .text.esSIOS_putstr
                0x00000000e891bd26       0x14 ./elibrary/bin//libsyscall.a(syscall.o)
                0x00000000e891bd26                esSIOS_putstr
 .text._close_r
                0x00000000e891bd3a       0x18 ./elibrary/bin//libsyscall.a(syscall_fops.o)
                0x00000000e891bd3a                _close_r
 .text._fstat_r
                0x00000000e891bd52       0x18 ./elibrary/bin//libsyscall.a(syscall_fops.o)
                0x00000000e891bd52                _fstat_r
 .text._lseek_r
                0x00000000e891bd6a       0x18 ./elibrary/bin//libsyscall.a(syscall_fops.o)
                0x00000000e891bd6a                _lseek_r
 .text._open_r  0x00000000e891bd82       0x18 ./elibrary/bin//libsyscall.a(syscall_fops.o)
                0x00000000e891bd82                _open_r
 .text._read_r  0x00000000e891bd9a       0x16 ./elibrary/bin//libsyscall.a(syscall_fops.o)
                0x00000000e891bd9a                _read_r
 .text._write_r
                0x00000000e891bdb0       0x16 ./elibrary/bin//libsyscall.a(syscall_fops.o)
                0x00000000e891bdb0                _write_r
 .text._malloc_r
                0x00000000e891bdc6       0x16 ./elibrary/bin//libsyscall.a(syscall_fops.o)
                0x00000000e891bdc6                _malloc_r
 .text._realloc_r
                0x00000000e891bddc       0x16 ./elibrary/bin//libsyscall.a(syscall_fops.o)
                0x00000000e891bddc                _realloc_r
 .text._calloc_r
                0x00000000e891bdf2       0x16 ./elibrary/bin//libsyscall.a(syscall_fops.o)
                0x00000000e891bdf2                _calloc_r
 .text._free_r  0x00000000e891be08       0x16 ./elibrary/bin//libsyscall.a(syscall_fops.o)
                0x00000000e891be08                _free_r
 .text._isatty_r
                0x00000000e891be1e       0x16 ./elibrary/bin//libsyscall.a(syscall_fops.o)
                0x00000000e891be1e                _isatty_r
 .text.ioctl    0x00000000e891be34       0x28 ./elibrary/bin//libsyscall.a(syscall_fops.o)
                0x00000000e891be34                ioctl
 .text.usleep   0x00000000e891be5c       0x18 ./elibrary/bin//libsyscall.a(syscall.o)
                0x00000000e891be5c                usleep
 .text.eLIBs_fopen
                0x00000000e891be74        0x4 ./elibrary/bin//libminic.a(elibs_stdio.o)
                0x00000000e891be74                eLIBs_fopen
 .text.eLIBs_fclose
                0x00000000e891be78        0x4 ./elibrary/bin//libminic.a(elibs_stdio.o)
                0x00000000e891be78                eLIBs_fclose
 .text.eLIBs_fread
                0x00000000e891be7c        0x4 ./elibrary/bin//libminic.a(elibs_stdio.o)
                0x00000000e891be7c                eLIBs_fread
 .text.eLIBs_fwrite
                0x00000000e891be80        0x4 ./elibrary/bin//libminic.a(elibs_stdio.o)
                0x00000000e891be80                eLIBs_fwrite
 .text.eLIBs_vprintf
                0x00000000e891be84       0x4c ./elibrary/bin//libminic.a(elibs_stdio.o)
                0x00000000e891be84                eLIBs_vprintf
 .text.eLIBs_printf
                0x00000000e891bed0       0x20 ./elibrary/bin//libminic.a(elibs_stdio.o)
                0x00000000e891bed0                eLIBs_printf
                0x00000000e891bed0                printk
 .text.eLIBs_ramdom
                0x00000000e891bef0        0x4 ./elibrary/bin//libminic.a(elibs_stdlib.o)
                0x00000000e891bef0                eLIBs_ramdom
 .text.eLIBs_malloc
                0x00000000e891bef4        0xa ./elibrary/bin//libminic.a(elibs_stdlib.o)
                0x00000000e891bef4                eLIBs_malloc
 .text.eLIBs_free
                0x00000000e891befe        0xc ./elibrary/bin//libminic.a(elibs_stdlib.o)
                0x00000000e891befe                eLIBs_free
 .text.eLIBs_strlen
                0x00000000e891bf0a        0xc ./elibrary/bin//libminic.a(elibs_string.o)
                0x00000000e891bf0a                eLIBs_strlen
 .text.eLIBs_strcpy
                0x00000000e891bf16        0xc ./elibrary/bin//libminic.a(elibs_string.o)
                0x00000000e891bf16                eLIBs_strcpy
 .text.eLIBs_strcmp
                0x00000000e891bf22        0xc ./elibrary/bin//libminic.a(elibs_string.o)
                0x00000000e891bf22                eLIBs_strcmp
 .text.eLIBs_memset
                0x00000000e891bf2e        0xc ./elibrary/bin//libminic.a(elibs_string.o)
                0x00000000e891bf2e                eLIBs_memset
 .text.eLIBs_memcpy
                0x00000000e891bf3a        0xc ./elibrary/bin//libminic.a(elibs_string.o)
                0x00000000e891bf3a                eLIBs_memcpy
 .text.eLIBs_memcmp
                0x00000000e891bf46       0x18 ./elibrary/bin//libminic.a(elibs_string.o)
                0x00000000e891bf46                eLIBs_memcmp
 .text.eLIBs_vsnprintf
                0x00000000e891bf5e        0xc ./elibrary/bin//libminic.a(elibs_sprintf.o)
                0x00000000e891bf5e                eLIBs_vsnprintf
 .text.eLIBs_vscnprintf
                0x00000000e891bf6a       0x1c ./elibrary/bin//libminic.a(elibs_sprintf.o)
                0x00000000e891bf6a                eLIBs_vscnprintf
 *(.stub)
 *(.gnu.warning)
 *(.gnu.linkonce.t*)
                0x00000000e891bf88                . = ALIGN (0x4)
 *fill*         0x00000000e891bf86        0x2 

.mod.rodata     0x00000000e891bf88     0x1ae8
 *(.rodata)
 *fill*         0x00000000e891bf88        0x0 
 .rodata        0x00000000e891bf88       0x18 emodules/drv_mcu_uart/core/handlers/protocol/system/mcu_system_category_handler.o
 .rodata        0x00000000e891bfa0       0x18 emodules/drv_mcu_uart/core/handlers/protocol/audio/mcu_audio_category_handler.o
 .rodata        0x00000000e891bfb8       0x48 emodules/drv_mcu_uart/core/handlers/protocol/tuner/mcu_tuner_category_handler.o
 .rodata        0x00000000e891c000       0x18 emodules/drv_mcu_uart/core/handlers/protocol/party/mcu_party_category_handler.o
 .rodata        0x00000000e891c018       0x30 emodules/drv_mcu_uart/core/handlers/protocol/usb/mcu_usb_category_handler.o
 .rodata        0x00000000e891c048       0x18 emodules/drv_mcu_uart/core/handlers/protocol/settings/mcu_settings_category_handler.o
 .rodata        0x00000000e891c060      0x190 /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/../../../../riscv64-unknown-elf/lib/rv64imafdcxtheadc/lp64d/libc.a(lib_a-vfprintf.o)
 .rodata        0x00000000e891c1f0        0x8 /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/../../../../riscv64-unknown-elf/lib/rv64imafdcxtheadc/lp64d/libc.a(lib_a-impure.o)
                0x00000000e891c1f0                _global_impure_ptr
 .rodata        0x00000000e891c1f8      0x262 /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/../../../../riscv64-unknown-elf/lib/rv64imafdcxtheadc/lp64d/libc.a(lib_a-ldtoa.o)
 *fill*         0x00000000e891c45a        0x6 
 .rodata        0x00000000e891c460      0x128 /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/../../../../riscv64-unknown-elf/lib/rv64imafdcxtheadc/lp64d/libc.a(lib_a-mprec.o)
                0x00000000e891c470                __mprec_tens
                0x00000000e891c538                __mprec_tinytens
                0x00000000e891c560                __mprec_bigtens
 .rodata        0x00000000e891c588      0x190 /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/../../../../riscv64-unknown-elf/lib/rv64imafdcxtheadc/lp64d/libc.a(lib_a-svfprintf.o)
 .rodata        0x00000000e891c718      0x190 /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/../../../../riscv64-unknown-elf/lib/rv64imafdcxtheadc/lp64d/libc.a(lib_a-vfiprintf.o)
 .rodata        0x00000000e891c8a8      0x101 /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/../../../../riscv64-unknown-elf/lib/rv64imafdcxtheadc/lp64d/libc.a(lib_a-ctype_.o)
                0x00000000e891c8a8                _ctype_
 *fill*         0x00000000e891c9a9        0x7 
 .rodata        0x00000000e891c9b0      0x190 /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/../../../../riscv64-unknown-elf/lib/rv64imafdcxtheadc/lp64d/libc.a(lib_a-svfiprintf.o)
 .rodata        0x00000000e891cb40       0x3c /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/rv64imafdcxtheadc/lp64d/libgcc.a(multf3.o)
 *fill*         0x00000000e891cb7c        0x4 
 .rodata        0x00000000e891cb80      0x100 /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/rv64imafdcxtheadc/lp64d/libgcc.a(_clz.o)
                0x00000000e891cb80                __clz_tab
 *(.rodata.*)
 .rodata.hal_mcu_control
                0x00000000e891cc80       0x28 emodules/drv_mcu_uart/dev_mcu.o
 .rodata.McuKeyDevCodes
                0x00000000e891cca8       0xb4 emodules/drv_mcu_uart/dev_mcu.o
 *fill*         0x00000000e891cd5c        0x4 
 .rodata.McuKeyDevInit.str1.8
                0x00000000e891cd60        0xf emodules/drv_mcu_uart/dev_mcu.o
 *fill*         0x00000000e891cd6f        0x1 
 .rodata.sunxi_driver_mcu_init.str1.8
                0x00000000e891cd70        0x4 emodules/drv_mcu_uart/dev_mcu.o
 *fill*         0x00000000e891cd74        0x4 
 .rodata.sunxi_hal_mcu_driver
                0x00000000e891cd78       0x18 emodules/drv_mcu_uart/dev_mcu.o
 .rodata.mcu_bus_thread
                0x00000000e891cd90      0x440 emodules/drv_mcu_uart/core/mcu_interrupt_process.o
 .rodata.check_iap_exist.str1.8
                0x00000000e891d1d0        0xa emodules/drv_mcu_uart/core/mcu_interrupt_process.o
 *fill*         0x00000000e891d1da        0x6 
 .rodata.mcu_bus_thread.str1.8
                0x00000000e891d1e0       0x16 emodules/drv_mcu_uart/core/mcu_interrupt_process.o
 *fill*         0x00000000e891d1f6        0x2 
 .rodata.mcu_message_process_init.cst8
                0x00000000e891d1f8       0x38 emodules/drv_mcu_uart/core/mcu_interrupt_process.o
 .rodata.mcu_message_process_init.str1.8
                0x00000000e891d230       0xef emodules/drv_mcu_uart/core/mcu_interrupt_process.o
                                         0xf7 (size before relaxing)
 *fill*         0x00000000e891d31f        0x1 
 .rodata.uart_send_cmd_data.str1.8
                0x00000000e891d320       0x61 emodules/drv_mcu_uart/core/mcu_interrupt_process.o
 *fill*         0x00000000e891d381        0x7 
 .rodata.bypass_cmd_proc.str1.8
                0x00000000e891d388       0xd1 emodules/drv_mcu_uart/core/mcu_bypass_cmd_proc.o
 *fill*         0x00000000e891d459        0x7 
 .rodata.hu_get_remote_mount.str1.8
                0x00000000e891d460       0x42 emodules/drv_mcu_uart/core/mcu_bypass_cmd_proc.o
 *fill*         0x00000000e891d4a2        0x6 
 .rodata.mcu_cmd_dispatcher_init.str1.8
                0x00000000e891d4a8       0xa7 emodules/drv_mcu_uart/core/mcu_cmd_dispatcher.o
 *fill*         0x00000000e891d54f        0x1 
 .rodata.mcu_cmd_register_handler.str1.8
                0x00000000e891d550        0x8 emodules/drv_mcu_uart/core/mcu_cmd_dispatcher.o
 .rodata.mcu_system_cmd_handler
                0x00000000e891d558       0x14 emodules/drv_mcu_uart/core/handlers/system/mcu_system_handler.o
 *fill*         0x00000000e891d56c        0x4 
 .rodata.CSWTCH.613
                0x00000000e891d570       0x28 emodules/drv_mcu_uart/core/handlers/system/mcu_system_handler.o
 .rodata.mcu_system_cmd_handler.str1.8
                0x00000000e891d598       0x2b emodules/drv_mcu_uart/core/handlers/system/mcu_system_handler.o
 *fill*         0x00000000e891d5c3        0x1 
 .rodata.mcu_tuner_cmd_handler
                0x00000000e891d5c4       0xa0 emodules/drv_mcu_uart/core/handlers/media/mcu_tuner_handler.o
 *fill*         0x00000000e891d664        0x4 
 .rodata.mcu_usb_cmd_handler.str1.8
                0x00000000e891d668       0x15 emodules/drv_mcu_uart/core/handlers/media/mcu_usb_handler.o
                                         0x29 (size before relaxing)
 *fill*         0x00000000e891d67d        0x3 
 .rodata.mcu_sxm_cmd_handler
                0x00000000e891d680      0x104 emodules/drv_mcu_uart/core/handlers/sxm/mcu_sxm_handler.o
 *fill*         0x00000000e891d784        0x4 
 .rodata.mcu_sxm_cmd_handler.str1.8
                0x00000000e891d788        0xe emodules/drv_mcu_uart/core/handlers/sxm/mcu_sxm_handler.o
 *fill*         0x00000000e891d796        0x2 
 .rodata.mcu_misc_cmd_handler
                0x00000000e891d798       0x28 emodules/drv_mcu_uart/core/handlers/misc/mcu_misc_handler.o
 .rodata.mcu_new_protocol_handler_register.str1.8
                0x00000000e891d7c0        0xe emodules/drv_mcu_uart/core/handlers/protocol/mcu_new_protocol_handler.o
 *fill*         0x00000000e891d7ce        0x2 
 .rodata.str1.8
                0x00000000e891d7d0        0xf emodules/drv_mcu_uart/core/handlers/protocol/system/mcu_system_category_handler.o
 *fill*         0x00000000e891d7df        0x1 
 .rodata.handle_simple_audio_data.str1.8
                0x00000000e891d7e0       0x9c emodules/drv_mcu_uart/core/handlers/protocol/audio/mcu_audio_category_handler.o
 *fill*         0x00000000e891d87c        0x4 
 .rodata.str1.8
                0x00000000e891d880        0xe emodules/drv_mcu_uart/core/handlers/protocol/audio/mcu_audio_category_handler.o
 *fill*         0x00000000e891d88e        0x2 
 .rodata.str1.8
                0x00000000e891d890       0x2f emodules/drv_mcu_uart/core/handlers/protocol/tuner/mcu_tuner_category_handler.o
 *fill*         0x00000000e891d8bf        0x1 
 .rodata.party_init_status.str1.8
                0x00000000e891d8c0        0x7 emodules/drv_mcu_uart/core/handlers/protocol/party/mcu_party_category_handler.o
 *fill*         0x00000000e891d8c7        0x1 
 .rodata.str1.8
                0x00000000e891d8c8       0x11 emodules/drv_mcu_uart/core/handlers/protocol/party/mcu_party_category_handler.o
 *fill*         0x00000000e891d8d9        0x7 
 .rodata.str1.8
                0x00000000e891d8e0       0x29 emodules/drv_mcu_uart/core/handlers/protocol/usb/mcu_usb_category_handler.o
 *fill*         0x00000000e891d909        0x7 
 .rodata.handle_switch_data.constprop.3.str1.8
                0x00000000e891d910       0x20 emodules/drv_mcu_uart/core/handlers/protocol/settings/mcu_settings_category_handler.o
 .rodata.settings_main_handler.str1.8
                0x00000000e891d930       0x10 emodules/drv_mcu_uart/core/handlers/protocol/settings/mcu_settings_category_handler.o
 .rodata.str1.8
                0x00000000e891d940       0x11 emodules/drv_mcu_uart/core/handlers/protocol/settings/mcu_settings_category_handler.o
 *fill*         0x00000000e891d951        0xf 
 .rodata.cst16  0x00000000e891d960       0x30 /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/../../../../riscv64-unknown-elf/lib/rv64imafdcxtheadc/lp64d/libc.a(lib_a-vfprintf.o)
 .rodata.str1.8
                0x00000000e891d990       0x5a /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/../../../../riscv64-unknown-elf/lib/rv64imafdcxtheadc/lp64d/libc.a(lib_a-vfprintf.o)
 *fill*         0x00000000e891d9ea        0x6 
 .rodata.str1.8
                0x00000000e891d9f0       0x34 /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/../../../../riscv64-unknown-elf/lib/rv64imafdcxtheadc/lp64d/libc.a(lib_a-ldtoa.o)
 *fill*         0x00000000e891da24        0x4 
 .rodata.str1.8
                0x00000000e891da28       0x18 /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/../../../../riscv64-unknown-elf/lib/rv64imafdcxtheadc/lp64d/libc.a(lib_a-locale.o)
                                         0x1a (size before relaxing)
 .rodata.cst8   0x00000000e891da40       0x10 /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/../../../../riscv64-unknown-elf/lib/rv64imafdcxtheadc/lp64d/libc.a(lib_a-memchr.o)
 .rodata.cst8   0x00000000e891da50       0x10 /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/../../../../riscv64-unknown-elf/lib/rv64imafdcxtheadc/lp64d/libc.a(lib_a-mprec.o)
 .rodata.cst8   0x00000000e891da60        0x8 /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/../../../../riscv64-unknown-elf/lib/rv64imafdcxtheadc/lp64d/libc.a(lib_a-s_frexp.o)
 .rodata.cst8   0x00000000e891da68        0x8 /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/../../../../riscv64-unknown-elf/lib/rv64imafdcxtheadc/lp64d/libc.a(lib_a-strcpy.o)
 .rodata.cst8   0x00000000e891da70        0x8 /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/../../../../riscv64-unknown-elf/lib/rv64imafdcxtheadc/lp64d/libc.a(lib_a-strlen.o)
 .rodata.cst8   0x00000000e891da70       0x10 /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/../../../../riscv64-unknown-elf/lib/rv64imafdcxtheadc/lp64d/libc.a(lib_a-strncpy.o)
 .rodata.cst16  0x00000000e891da70       0x30 /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/../../../../riscv64-unknown-elf/lib/rv64imafdcxtheadc/lp64d/libc.a(lib_a-svfprintf.o)
 .rodata.str1.8
                0x00000000e891da70       0x5a /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/../../../../riscv64-unknown-elf/lib/rv64imafdcxtheadc/lp64d/libc.a(lib_a-svfprintf.o)
 .rodata.str1.8
                0x00000000e891da70       0x37 /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/../../../../riscv64-unknown-elf/lib/rv64imafdcxtheadc/lp64d/libc.a(lib_a-vfiprintf.o)
 .rodata.str1.8
                0x00000000e891da70       0x37 /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/../../../../riscv64-unknown-elf/lib/rv64imafdcxtheadc/lp64d/libc.a(lib_a-svfiprintf.o)
 *(.gnu.linkonce.r*)
                0x00000000e891dacc                . = ALIGN (0x4)

.mod.data       0x00000000e891da70      0xa5c
 *(.data)
 .data          0x00000000e891da70      0x750 /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/../../../../riscv64-unknown-elf/lib/rv64imafdcxtheadc/lp64d/libc.a(lib_a-impure.o)
                0x00000000e891da70                _impure_ptr
 .data          0x00000000e891e1c0      0x1a8 /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/../../../../riscv64-unknown-elf/lib/rv64imafdcxtheadc/lp64d/libc.a(lib_a-locale.o)
                0x00000000e891e1c0                __global_locale
 *(.data.*)
 .data.mcu_key_op
                0x00000000e891e368       0x28 emodules/drv_mcu_uart/dev_mcu.o
                0x00000000e891e368                mcu_key_op
 .data.Init_Eq_Gain
                0x00000000e891e390       0x82 emodules/drv_mcu_uart/core/mcu_aux_init_para.o
                0x00000000e891e390                Init_Eq_Gain
 *fill*         0x00000000e891e412        0x6 
 .data.init_position_para
                0x00000000e891e418       0xb0 emodules/drv_mcu_uart/core/mcu_aux_init_para.o
                0x00000000e891e418                init_position_para
 .data.g_first_source_flag
                0x00000000e891e4c8        0x1 emodules/drv_mcu_uart/core/handlers/protocol/mcu_new_protocol_handler.o
                0x00000000e891e4c8                g_first_source_flag
 *(.gnu.linkonce.d.*)
 *(.sdata)
 *(.sdata.*)
 *(.gnu.linkonce.s.*)
 *(.sdata2)
 *(.sdata2.*)
 *(.gnu.linkonce.s2.*)
                0x00000000e891e4cc                . = ALIGN (0x4)
 *fill*         0x00000000e891e4c9        0x3 

.mod.bss        0x00000000e891e4cc      0xd8c
                0x00000000e891e4cc                __bss_start = ABSOLUTE (.)
 *(.bss)
 *(.bss.*)
 *fill*         0x00000000e891e4cc        0x4 
 .bss.mcu       0x00000000e891e4d0       0xb8 emodules/drv_mcu_uart/dev_mcu.o
                0x00000000e891e4d0                mcu
 .bss.mcu_key_dev
                0x00000000e891e588        0x1 emodules/drv_mcu_uart/dev_mcu.o
 *fill*         0x00000000e891e589        0x7 
 .bss.mcu_drv   0x00000000e891e590       0x10 emodules/drv_mcu_uart/drv_mcu.o
                0x00000000e891e590                mcu_drv
 .bss.mcu_lock  0x00000000e891e5a0        0x8 emodules/drv_mcu_uart/drv_mcu.o
                0x00000000e891e5a0                mcu_lock
 .bss.ArtworkBuf
                0x00000000e891e5a8        0x8 emodules/drv_mcu_uart/core/mcu_interrupt_process.o
                0x00000000e891e5a8                ArtworkBuf
 .bss.ArtworkPkg
                0x00000000e891e5b0       0x18 emodules/drv_mcu_uart/core/mcu_interrupt_process.o
                0x00000000e891e5b0                ArtworkPkg
 .bss.RemotePkgHead
                0x00000000e891e5c8        0x8 emodules/drv_mcu_uart/core/mcu_interrupt_process.o
                0x00000000e891e5c8                RemotePkgHead
 .bss.all_app_para
                0x00000000e891e5d0        0x8 emodules/drv_mcu_uart/core/mcu_interrupt_process.o
                0x00000000e891e5d0                all_app_para
 .bss.cmd_queue
                0x00000000e891e5d8        0x8 emodules/drv_mcu_uart/core/mcu_interrupt_process.o
                0x00000000e891e5d8                cmd_queue
 .bss.crc32_table
                0x00000000e891e5e0      0x400 emodules/drv_mcu_uart/core/mcu_interrupt_process.o
 .bss.get_brower_cmd_flag
                0x00000000e891e9e0        0x1 emodules/drv_mcu_uart/core/mcu_interrupt_process.o
                0x00000000e891e9e0                get_brower_cmd_flag
 *fill*         0x00000000e891e9e1        0x3 
 .bss.get_cmd_no
                0x00000000e891e9e4        0x4 emodules/drv_mcu_uart/core/mcu_interrupt_process.o
                0x00000000e891e9e4                get_cmd_no
 .bss.get_data  0x00000000e891e9e8        0x8 emodules/drv_mcu_uart/core/mcu_interrupt_process.o
                0x00000000e891e9e8                get_data
 .bss.hTX3De    0x00000000e891e9f0        0x8 emodules/drv_mcu_uart/core/mcu_interrupt_process.o
                0x00000000e891e9f0                hTX3De
 .bss.message_process_ctrl
                0x00000000e891e9f8      0x190 emodules/drv_mcu_uart/core/mcu_interrupt_process.o
                0x00000000e891e9f8                message_process_ctrl
 .bss.pAU       0x00000000e891eb88       0x20 emodules/drv_mcu_uart/core/mcu_interrupt_process.o
                0x00000000e891eb88                pAU
 .bss.send_artwork_cnt
                0x00000000e891eba8        0x4 emodules/drv_mcu_uart/core/mcu_interrupt_process.o
                0x00000000e891eba8                send_artwork_cnt
 *fill*         0x00000000e891ebac        0x4 
 .bss.sequel    0x00000000e891ebb0        0x8 emodules/drv_mcu_uart/core/mcu_interrupt_process.o
                0x00000000e891ebb0                sequel
 .bss.sxm_browser_lock
                0x00000000e891ebb8        0x1 emodules/drv_mcu_uart/core/mcu_interrupt_process.o
                0x00000000e891ebb8                sxm_browser_lock
 *fill*         0x00000000e891ebb9        0x7 
 .bss.uart_sequel
                0x00000000e891ebc0        0x8 emodules/drv_mcu_uart/core/mcu_interrupt_process.o
                0x00000000e891ebc0                uart_sequel
 .bss.g_cmd_handlers
                0x00000000e891ebc8      0x300 emodules/drv_mcu_uart/core/mcu_cmd_dispatcher.o
 .bss.g_cmd_stats
                0x00000000e891eec8       0x10 emodules/drv_mcu_uart/core/mcu_cmd_dispatcher.o
 .bss.g_dispatcher_initialized
                0x00000000e891eed8        0x1 emodules/drv_mcu_uart/core/mcu_cmd_dispatcher.o
 *fill*         0x00000000e891eed9        0x3 
 .bss.g_handler_count
                0x00000000e891eedc        0x4 emodules/drv_mcu_uart/core/mcu_cmd_dispatcher.o
 .bss.key_value.14806
                0x00000000e891eee0        0x4 emodules/drv_mcu_uart/core/handlers/system/mcu_system_handler.o
 .bss.g_bypass_cmd_count
                0x00000000e891eee4        0x4 emodules/drv_mcu_uart/core/handlers/bypass/mcu_bypass_handler.o
 .bss.g_category_handler_count
                0x00000000e891eee8        0x4 emodules/drv_mcu_uart/core/handlers/protocol/mcu_new_protocol_handler.o
 *fill*         0x00000000e891eeec        0x4 
 .bss.g_category_handlers
                0x00000000e891eef0      0x300 emodules/drv_mcu_uart/core/handlers/protocol/mcu_new_protocol_handler.o
 .bss.g_init_data_get_flag
                0x00000000e891f1f0        0x1 emodules/drv_mcu_uart/core/handlers/protocol/mcu_new_protocol_handler.o
                0x00000000e891f1f0                g_init_data_get_flag
 *fill*         0x00000000e891f1f1        0x3 
 .bss.g_new_protocol_cmd_count
                0x00000000e891f1f4        0x4 emodules/drv_mcu_uart/core/handlers/protocol/mcu_new_protocol_handler.o
 .bss.g_new_protocol_error_count
                0x00000000e891f1f8        0x4 emodules/drv_mcu_uart/core/handlers/protocol/mcu_new_protocol_handler.o
 *fill*         0x00000000e891f1fc        0x4 
 .bss.ptr       0x00000000e891f200        0x8 emodules/drv_mcu_uart/core/handlers/protocol/system/mcu_system_category_handler.o
 .bss.aPtr      0x00000000e891f208        0x8 emodules/drv_mcu_uart/core/handlers/protocol/audio/mcu_audio_category_handler.o
 .bss.g_am_status
                0x00000000e891f210        0x8 emodules/drv_mcu_uart/core/handlers/protocol/tuner/mcu_tuner_category_handler.o
 .bss.g_fm_status
                0x00000000e891f218        0x8 emodules/drv_mcu_uart/core/handlers/protocol/tuner/mcu_tuner_category_handler.o
 .bss.g_wb_status
                0x00000000e891f220        0x8 emodules/drv_mcu_uart/core/handlers/protocol/tuner/mcu_tuner_category_handler.o
 .bss.g_party_status
                0x00000000e891f228        0x8 emodules/drv_mcu_uart/core/handlers/protocol/party/mcu_party_category_handler.o
 .bss.g_usb_category_counts
                0x00000000e891f230        0x8 emodules/drv_mcu_uart/core/handlers/protocol/usb/mcu_usb_category_handler.o
 .bss.g_usb_disk_state
                0x00000000e891f238        0x8 emodules/drv_mcu_uart/core/handlers/protocol/usb/mcu_usb_category_handler.o
 .bss.g_usb_error_counts
                0x00000000e891f240        0x4 emodules/drv_mcu_uart/core/handlers/protocol/usb/mcu_usb_category_handler.o
 *fill*         0x00000000e891f244        0x4 
 .bss.g_usb_ipod_state
                0x00000000e891f248        0x8 emodules/drv_mcu_uart/core/handlers/protocol/usb/mcu_usb_category_handler.o
 .bss.g_settings_status
                0x00000000e891f250        0x8 emodules/drv_mcu_uart/core/handlers/protocol/settings/mcu_settings_category_handler.o
 *(.gnu.linkonce.b.*)
 *(.sbss)
 *(.sbss.*)
 *(.gnu.linkonce.sb.*)
 *(.sbss2)
 *(.sbss2.*)
 *(.gnu.linkonce.sb2.*)
 *(COMMON)
 *(.scommon)
                0x00000000e891f258                . = ALIGN (0x4)
                0x00000000e891f258                __bss_end = ABSOLUTE (.)
                0x00000000e891f258                _end = ABSOLUTE (.)

MAGIC           0x00000000ffff0000       0x50
 *.o(.magic)
 .magic         0x00000000ffff0000       0x50 emodules/drv_mcu_uart/magic.o
                0x00000000ffff0000                modinfo

.note
 *(.note)

.stab
 *(.stab)

.stabstr
 *(.stabstr)

.stab.excl
 *(.stab.excl)

.stab.exclstr
 *(.stab.exclstr)

.stab.index
 *(.stab.index)

.stab.indexstr
 *(.stab.indexstr)

.mdebug
 *(.mdebug)

.reginfo
 *(.reginfo)

.comment        0x0000000000000000       0x32
 *(.comment)
 .comment       0x0000000000000000       0x32 emodules/drv_mcu_uart/dev_mcu.o
                                         0x33 (size before relaxing)
 .comment       0x0000000000000032       0x33 emodules/drv_mcu_uart/drv_mcu.o
 .comment       0x0000000000000032       0x33 emodules/drv_mcu_uart/magic.o
 .comment       0x0000000000000032       0x33 emodules/drv_mcu_uart/core/mcu_interrupt_process.o
 .comment       0x0000000000000032       0x33 emodules/drv_mcu_uart/core/mcu_aux_init_para.o
 .comment       0x0000000000000032       0x33 emodules/drv_mcu_uart/core/mcu_bypass_cmd_proc.o
 .comment       0x0000000000000032       0x33 emodules/drv_mcu_uart/core/mcu_update_proc.o
 .comment       0x0000000000000032       0x33 emodules/drv_mcu_uart/core/mcu_cmd_dispatcher.o
 .comment       0x0000000000000032       0x33 emodules/drv_mcu_uart/core/handlers/system/mcu_system_handler.o
 .comment       0x0000000000000032       0x33 emodules/drv_mcu_uart/core/handlers/media/mcu_tuner_handler.o
 .comment       0x0000000000000032       0x33 emodules/drv_mcu_uart/core/handlers/media/mcu_usb_handler.o
 .comment       0x0000000000000032       0x33 emodules/drv_mcu_uart/core/handlers/bluetooth/mcu_bt_handler.o
 .comment       0x0000000000000032       0x33 emodules/drv_mcu_uart/core/handlers/sxm/mcu_sxm_handler.o
 .comment       0x0000000000000032       0x33 emodules/drv_mcu_uart/core/handlers/settings/mcu_settings_handler.o
 .comment       0x0000000000000032       0x33 emodules/drv_mcu_uart/core/handlers/audio/mcu_audio_handler.o
 .comment       0x0000000000000032       0x33 emodules/drv_mcu_uart/core/handlers/upgrade/mcu_upgrade_handler.o
 .comment       0x0000000000000032       0x33 emodules/drv_mcu_uart/core/handlers/bypass/mcu_bypass_handler.o
 .comment       0x0000000000000032       0x33 emodules/drv_mcu_uart/core/handlers/misc/mcu_misc_handler.o
 .comment       0x0000000000000032       0x33 emodules/drv_mcu_uart/core/handlers/protocol/mcu_new_protocol_handler.o
 .comment       0x0000000000000032       0x33 emodules/drv_mcu_uart/core/handlers/protocol/system/mcu_system_category_handler.o
 .comment       0x0000000000000032       0x33 emodules/drv_mcu_uart/core/handlers/protocol/audio/mcu_audio_category_handler.o
 .comment       0x0000000000000032       0x33 emodules/drv_mcu_uart/core/handlers/protocol/tuner/mcu_tuner_category_handler.o
 .comment       0x0000000000000032       0x33 emodules/drv_mcu_uart/core/handlers/protocol/party/mcu_party_category_handler.o
 .comment       0x0000000000000032       0x33 emodules/drv_mcu_uart/core/handlers/protocol/usb/mcu_usb_category_handler.o
 .comment       0x0000000000000032       0x33 emodules/drv_mcu_uart/core/handlers/protocol/settings/mcu_settings_category_handler.o
 .comment       0x0000000000000032       0x33 ./elibrary/bin//libsyscall.a(syscall.o)
 .comment       0x0000000000000032       0x33 ./elibrary/bin//libsyscall.a(syscall_fops.o)
 .comment       0x0000000000000032       0x33 ./elibrary/bin//libsyscall.a(syscall.o)
 .comment       0x0000000000000032       0x33 ./elibrary/bin//libminic.a(elibs_stdio.o)
 .comment       0x0000000000000032       0x33 ./elibrary/bin//libminic.a(elibs_stdlib.o)
 .comment       0x0000000000000032       0x33 ./elibrary/bin//libminic.a(elibs_string.o)
 .comment       0x0000000000000032       0x33 ./elibrary/bin//libminic.a(elibs_sprintf.o)
 .comment       0x0000000000000032       0x33 /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/../../../../riscv64-unknown-elf/lib/rv64imafdcxtheadc/lp64d/libc.a(lib_a-memcpy.o)
 .comment       0x0000000000000032       0x33 /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/../../../../riscv64-unknown-elf/lib/rv64imafdcxtheadc/lp64d/libc.a(lib_a-printf.o)
 .comment       0x0000000000000032       0x33 /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/../../../../riscv64-unknown-elf/lib/rv64imafdcxtheadc/lp64d/libc.a(lib_a-sysclose.o)
 .comment       0x0000000000000032       0x33 /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/../../../../riscv64-unknown-elf/lib/rv64imafdcxtheadc/lp64d/libc.a(lib_a-sysopen.o)
 .comment       0x0000000000000032       0x33 /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/../../../../riscv64-unknown-elf/lib/rv64imafdcxtheadc/lp64d/libc.a(lib_a-vfprintf.o)
 .comment       0x0000000000000032       0x33 /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/../../../../riscv64-unknown-elf/lib/rv64imafdcxtheadc/lp64d/libc.a(lib_a-wsetup.o)
 .comment       0x0000000000000032       0x33 /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/../../../../riscv64-unknown-elf/lib/rv64imafdcxtheadc/lp64d/libc.a(lib_a-fflush.o)
 .comment       0x0000000000000032       0x33 /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/../../../../riscv64-unknown-elf/lib/rv64imafdcxtheadc/lp64d/libc.a(lib_a-findfp.o)
 .comment       0x0000000000000032       0x33 /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/../../../../riscv64-unknown-elf/lib/rv64imafdcxtheadc/lp64d/libc.a(lib_a-fwalk.o)
 .comment       0x0000000000000032       0x33 /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/../../../../riscv64-unknown-elf/lib/rv64imafdcxtheadc/lp64d/libc.a(lib_a-impure.o)
 .comment       0x0000000000000032       0x33 /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/../../../../riscv64-unknown-elf/lib/rv64imafdcxtheadc/lp64d/libc.a(lib_a-ldtoa.o)
 .comment       0x0000000000000032       0x33 /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/../../../../riscv64-unknown-elf/lib/rv64imafdcxtheadc/lp64d/libc.a(lib_a-localeconv.o)
 .comment       0x0000000000000032       0x33 /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/../../../../riscv64-unknown-elf/lib/rv64imafdcxtheadc/lp64d/libc.a(lib_a-locale.o)
 .comment       0x0000000000000032       0x33 /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/../../../../riscv64-unknown-elf/lib/rv64imafdcxtheadc/lp64d/libc.a(lib_a-makebuf.o)
 .comment       0x0000000000000032       0x33 /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/../../../../riscv64-unknown-elf/lib/rv64imafdcxtheadc/lp64d/libc.a(lib_a-mbtowc_r.o)
 .comment       0x0000000000000032       0x33 /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/../../../../riscv64-unknown-elf/lib/rv64imafdcxtheadc/lp64d/libc.a(lib_a-memchr.o)
 .comment       0x0000000000000032       0x33 /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/../../../../riscv64-unknown-elf/lib/rv64imafdcxtheadc/lp64d/libc.a(lib_a-mprec.o)
 .comment       0x0000000000000032       0x33 /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/../../../../riscv64-unknown-elf/lib/rv64imafdcxtheadc/lp64d/libc.a(lib_a-s_frexp.o)
 .comment       0x0000000000000032       0x33 /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/../../../../riscv64-unknown-elf/lib/rv64imafdcxtheadc/lp64d/libc.a(lib_a-sprintf.o)
 .comment       0x0000000000000032       0x33 /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/../../../../riscv64-unknown-elf/lib/rv64imafdcxtheadc/lp64d/libc.a(lib_a-stdio.o)
 .comment       0x0000000000000032       0x33 /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/../../../../riscv64-unknown-elf/lib/rv64imafdcxtheadc/lp64d/libc.a(lib_a-strcpy.o)
 .comment       0x0000000000000032       0x33 /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/../../../../riscv64-unknown-elf/lib/rv64imafdcxtheadc/lp64d/libc.a(lib_a-strlen.o)
 .comment       0x0000000000000032       0x33 /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/../../../../riscv64-unknown-elf/lib/rv64imafdcxtheadc/lp64d/libc.a(lib_a-strncpy.o)
 .comment       0x0000000000000032       0x33 /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/../../../../riscv64-unknown-elf/lib/rv64imafdcxtheadc/lp64d/libc.a(lib_a-svfprintf.o)
 .comment       0x0000000000000032       0x33 /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/../../../../riscv64-unknown-elf/lib/rv64imafdcxtheadc/lp64d/libc.a(lib_a-vfiprintf.o)
 .comment       0x0000000000000032       0x33 /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/../../../../riscv64-unknown-elf/lib/rv64imafdcxtheadc/lp64d/libc.a(lib_a-wctomb_r.o)
 .comment       0x0000000000000032       0x33 /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/../../../../riscv64-unknown-elf/lib/rv64imafdcxtheadc/lp64d/libc.a(lib_a-ctype_.o)
 .comment       0x0000000000000032       0x33 /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/../../../../riscv64-unknown-elf/lib/rv64imafdcxtheadc/lp64d/libc.a(lib_a-fclose.o)
 .comment       0x0000000000000032       0x33 /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/../../../../riscv64-unknown-elf/lib/rv64imafdcxtheadc/lp64d/libc.a(lib_a-fputwc.o)
 .comment       0x0000000000000032       0x33 /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/../../../../riscv64-unknown-elf/lib/rv64imafdcxtheadc/lp64d/libc.a(lib_a-fvwrite.o)
 .comment       0x0000000000000032       0x33 /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/../../../../riscv64-unknown-elf/lib/rv64imafdcxtheadc/lp64d/libc.a(lib_a-memmove-stub.o)
 .comment       0x0000000000000032       0x33 /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/../../../../riscv64-unknown-elf/lib/rv64imafdcxtheadc/lp64d/libc.a(lib_a-svfiprintf.o)
 .comment       0x0000000000000032       0x33 /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/../../../../riscv64-unknown-elf/lib/rv64imafdcxtheadc/lp64d/libc.a(lib_a-wbuf.o)
 .comment       0x0000000000000032       0x33 /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/../../../../riscv64-unknown-elf/lib/rv64imafdcxtheadc/lp64d/libc.a(lib_a-wcrtomb.o)
 .comment       0x0000000000000032       0x33 /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/rv64imafdcxtheadc/lp64d/libgcc.a(eqtf2.o)
 .comment       0x0000000000000032       0x33 /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/rv64imafdcxtheadc/lp64d/libgcc.a(getf2.o)
 .comment       0x0000000000000032       0x33 /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/rv64imafdcxtheadc/lp64d/libgcc.a(letf2.o)
 .comment       0x0000000000000032       0x33 /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/rv64imafdcxtheadc/lp64d/libgcc.a(multf3.o)
 .comment       0x0000000000000032       0x33 /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/rv64imafdcxtheadc/lp64d/libgcc.a(subtf3.o)
 .comment       0x0000000000000032       0x33 /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/rv64imafdcxtheadc/lp64d/libgcc.a(fixtfsi.o)
 .comment       0x0000000000000032       0x33 /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/rv64imafdcxtheadc/lp64d/libgcc.a(floatsitf.o)
 .comment       0x0000000000000032       0x33 /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/rv64imafdcxtheadc/lp64d/libgcc.a(extenddftf2.o)
 .comment       0x0000000000000032       0x33 /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/rv64imafdcxtheadc/lp64d/libgcc.a(trunctfdf2.o)
 .comment       0x0000000000000032       0x33 /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/rv64imafdcxtheadc/lp64d/libgcc.a(_clzsi2.o)
 .comment       0x0000000000000032       0x33 /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/rv64imafdcxtheadc/lp64d/libgcc.a(_clz.o)

.ARM.attributes
 *(.ARM.attributes)

.riscv.attributes
                0x0000000000000000       0x48
 *(.riscv.attributes)
 .riscv.attributes
                0x0000000000000000       0x3d emodules/drv_mcu_uart/dev_mcu.o
 .riscv.attributes
                0x000000000000003d       0x3d emodules/drv_mcu_uart/drv_mcu.o
 .riscv.attributes
                0x000000000000007a       0x3d emodules/drv_mcu_uart/magic.o
 .riscv.attributes
                0x00000000000000b7       0x3d emodules/drv_mcu_uart/core/mcu_interrupt_process.o
 .riscv.attributes
                0x00000000000000f4       0x3d emodules/drv_mcu_uart/core/mcu_aux_init_para.o
 .riscv.attributes
                0x0000000000000131       0x3d emodules/drv_mcu_uart/core/mcu_bypass_cmd_proc.o
 .riscv.attributes
                0x000000000000016e       0x3d emodules/drv_mcu_uart/core/mcu_update_proc.o
 .riscv.attributes
                0x00000000000001ab       0x3d emodules/drv_mcu_uart/core/mcu_cmd_dispatcher.o
 .riscv.attributes
                0x00000000000001e8       0x3d emodules/drv_mcu_uart/core/handlers/system/mcu_system_handler.o
 .riscv.attributes
                0x0000000000000225       0x3d emodules/drv_mcu_uart/core/handlers/media/mcu_tuner_handler.o
 .riscv.attributes
                0x0000000000000262       0x3d emodules/drv_mcu_uart/core/handlers/media/mcu_usb_handler.o
 .riscv.attributes
                0x000000000000029f       0x3d emodules/drv_mcu_uart/core/handlers/bluetooth/mcu_bt_handler.o
 .riscv.attributes
                0x00000000000002dc       0x3d emodules/drv_mcu_uart/core/handlers/sxm/mcu_sxm_handler.o
 .riscv.attributes
                0x0000000000000319       0x3d emodules/drv_mcu_uart/core/handlers/settings/mcu_settings_handler.o
 .riscv.attributes
                0x0000000000000356       0x3d emodules/drv_mcu_uart/core/handlers/audio/mcu_audio_handler.o
 .riscv.attributes
                0x0000000000000393       0x3d emodules/drv_mcu_uart/core/handlers/upgrade/mcu_upgrade_handler.o
 .riscv.attributes
                0x00000000000003d0       0x3d emodules/drv_mcu_uart/core/handlers/bypass/mcu_bypass_handler.o
 .riscv.attributes
                0x000000000000040d       0x3d emodules/drv_mcu_uart/core/handlers/misc/mcu_misc_handler.o
 .riscv.attributes
                0x000000000000044a       0x3d emodules/drv_mcu_uart/core/handlers/protocol/mcu_new_protocol_handler.o
 .riscv.attributes
                0x0000000000000487       0x3d emodules/drv_mcu_uart/core/handlers/protocol/system/mcu_system_category_handler.o
 .riscv.attributes
                0x00000000000004c4       0x3d emodules/drv_mcu_uart/core/handlers/protocol/audio/mcu_audio_category_handler.o
 .riscv.attributes
                0x0000000000000501       0x3d emodules/drv_mcu_uart/core/handlers/protocol/tuner/mcu_tuner_category_handler.o
 .riscv.attributes
                0x000000000000053e       0x3d emodules/drv_mcu_uart/core/handlers/protocol/party/mcu_party_category_handler.o
 .riscv.attributes
                0x000000000000057b       0x3d emodules/drv_mcu_uart/core/handlers/protocol/usb/mcu_usb_category_handler.o
 .riscv.attributes
                0x00000000000005b8       0x3d emodules/drv_mcu_uart/core/handlers/protocol/settings/mcu_settings_category_handler.o
 .riscv.attributes
                0x00000000000005f5       0x3d ./elibrary/bin//libsyscall.a(syscall.o)
 .riscv.attributes
                0x0000000000000632       0x3d ./elibrary/bin//libsyscall.a(syscall_fops.o)
 .riscv.attributes
                0x000000000000066f       0x3d ./elibrary/bin//libsyscall.a(syscall.o)
 .riscv.attributes
                0x00000000000006ac       0x3d ./elibrary/bin//libminic.a(elibs_stdio.o)
 .riscv.attributes
                0x00000000000006e9       0x3d ./elibrary/bin//libminic.a(elibs_stdlib.o)
 .riscv.attributes
                0x0000000000000726       0x3d ./elibrary/bin//libminic.a(elibs_string.o)
 .riscv.attributes
                0x0000000000000763       0x3d ./elibrary/bin//libminic.a(elibs_sprintf.o)
 .riscv.attributes
                0x00000000000007a0       0x3e /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/../../../../riscv64-unknown-elf/lib/rv64imafdcxtheadc/lp64d/libc.a(lib_a-memcpy.o)
 .riscv.attributes
                0x00000000000007de       0x3e /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/../../../../riscv64-unknown-elf/lib/rv64imafdcxtheadc/lp64d/libc.a(lib_a-memset.o)
 .riscv.attributes
                0x000000000000081c       0x3e /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/../../../../riscv64-unknown-elf/lib/rv64imafdcxtheadc/lp64d/libc.a(lib_a-printf.o)
 .riscv.attributes
                0x000000000000085a       0x3e /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/../../../../riscv64-unknown-elf/lib/rv64imafdcxtheadc/lp64d/libc.a(lib_a-sysclose.o)
 .riscv.attributes
                0x0000000000000898       0x3e /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/../../../../riscv64-unknown-elf/lib/rv64imafdcxtheadc/lp64d/libc.a(lib_a-sysopen.o)
 .riscv.attributes
                0x00000000000008d6       0x3e /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/../../../../riscv64-unknown-elf/lib/rv64imafdcxtheadc/lp64d/libc.a(lib_a-vfprintf.o)
 .riscv.attributes
                0x0000000000000914       0x3e /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/../../../../riscv64-unknown-elf/lib/rv64imafdcxtheadc/lp64d/libc.a(lib_a-wsetup.o)
 .riscv.attributes
                0x0000000000000952       0x3e /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/../../../../riscv64-unknown-elf/lib/rv64imafdcxtheadc/lp64d/libc.a(lib_a-fflush.o)
 .riscv.attributes
                0x0000000000000990       0x3e /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/../../../../riscv64-unknown-elf/lib/rv64imafdcxtheadc/lp64d/libc.a(lib_a-findfp.o)
 .riscv.attributes
                0x00000000000009ce       0x3e /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/../../../../riscv64-unknown-elf/lib/rv64imafdcxtheadc/lp64d/libc.a(lib_a-fwalk.o)
 .riscv.attributes
                0x0000000000000a0c       0x3e /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/../../../../riscv64-unknown-elf/lib/rv64imafdcxtheadc/lp64d/libc.a(lib_a-impure.o)
 .riscv.attributes
                0x0000000000000a4a       0x3e /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/../../../../riscv64-unknown-elf/lib/rv64imafdcxtheadc/lp64d/libc.a(lib_a-ldtoa.o)
 .riscv.attributes
                0x0000000000000a88       0x3e /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/../../../../riscv64-unknown-elf/lib/rv64imafdcxtheadc/lp64d/libc.a(lib_a-localeconv.o)
 .riscv.attributes
                0x0000000000000ac6       0x3e /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/../../../../riscv64-unknown-elf/lib/rv64imafdcxtheadc/lp64d/libc.a(lib_a-locale.o)
 .riscv.attributes
                0x0000000000000b04       0x3e /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/../../../../riscv64-unknown-elf/lib/rv64imafdcxtheadc/lp64d/libc.a(lib_a-makebuf.o)
 .riscv.attributes
                0x0000000000000b42       0x3e /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/../../../../riscv64-unknown-elf/lib/rv64imafdcxtheadc/lp64d/libc.a(lib_a-mbtowc_r.o)
 .riscv.attributes
                0x0000000000000b80       0x3e /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/../../../../riscv64-unknown-elf/lib/rv64imafdcxtheadc/lp64d/libc.a(lib_a-memchr.o)
 .riscv.attributes
                0x0000000000000bbe       0x3e /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/../../../../riscv64-unknown-elf/lib/rv64imafdcxtheadc/lp64d/libc.a(lib_a-mprec.o)
 .riscv.attributes
                0x0000000000000bfc       0x3e /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/../../../../riscv64-unknown-elf/lib/rv64imafdcxtheadc/lp64d/libc.a(lib_a-s_frexp.o)
 .riscv.attributes
                0x0000000000000c3a       0x3e /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/../../../../riscv64-unknown-elf/lib/rv64imafdcxtheadc/lp64d/libc.a(lib_a-sprintf.o)
 .riscv.attributes
                0x0000000000000c78       0x3e /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/../../../../riscv64-unknown-elf/lib/rv64imafdcxtheadc/lp64d/libc.a(lib_a-stdio.o)
 .riscv.attributes
                0x0000000000000cb6       0x3e /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/../../../../riscv64-unknown-elf/lib/rv64imafdcxtheadc/lp64d/libc.a(lib_a-strcmp.o)
 .riscv.attributes
                0x0000000000000cf4       0x3e /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/../../../../riscv64-unknown-elf/lib/rv64imafdcxtheadc/lp64d/libc.a(lib_a-strcpy.o)
 .riscv.attributes
                0x0000000000000d32       0x3e /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/../../../../riscv64-unknown-elf/lib/rv64imafdcxtheadc/lp64d/libc.a(lib_a-strlen.o)
 .riscv.attributes
                0x0000000000000d70       0x3e /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/../../../../riscv64-unknown-elf/lib/rv64imafdcxtheadc/lp64d/libc.a(lib_a-strncpy.o)
 .riscv.attributes
                0x0000000000000dae       0x3e /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/../../../../riscv64-unknown-elf/lib/rv64imafdcxtheadc/lp64d/libc.a(lib_a-svfprintf.o)
 .riscv.attributes
                0x0000000000000dec       0x3e /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/../../../../riscv64-unknown-elf/lib/rv64imafdcxtheadc/lp64d/libc.a(lib_a-vfiprintf.o)
 .riscv.attributes
                0x0000000000000e2a       0x3e /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/../../../../riscv64-unknown-elf/lib/rv64imafdcxtheadc/lp64d/libc.a(lib_a-wctomb_r.o)
 .riscv.attributes
                0x0000000000000e68       0x3e /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/../../../../riscv64-unknown-elf/lib/rv64imafdcxtheadc/lp64d/libc.a(lib_a-ctype_.o)
 .riscv.attributes
                0x0000000000000ea6       0x3e /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/../../../../riscv64-unknown-elf/lib/rv64imafdcxtheadc/lp64d/libc.a(lib_a-fclose.o)
 .riscv.attributes
                0x0000000000000ee4       0x3e /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/../../../../riscv64-unknown-elf/lib/rv64imafdcxtheadc/lp64d/libc.a(lib_a-fputwc.o)
 .riscv.attributes
                0x0000000000000f22       0x3e /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/../../../../riscv64-unknown-elf/lib/rv64imafdcxtheadc/lp64d/libc.a(lib_a-fvwrite.o)
 .riscv.attributes
                0x0000000000000f60       0x3e /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/../../../../riscv64-unknown-elf/lib/rv64imafdcxtheadc/lp64d/libc.a(lib_a-memmove-stub.o)
 .riscv.attributes
                0x0000000000000f9e       0x3e /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/../../../../riscv64-unknown-elf/lib/rv64imafdcxtheadc/lp64d/libc.a(lib_a-svfiprintf.o)
 .riscv.attributes
                0x0000000000000fdc       0x3e /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/../../../../riscv64-unknown-elf/lib/rv64imafdcxtheadc/lp64d/libc.a(lib_a-wbuf.o)
 .riscv.attributes
                0x000000000000101a       0x3e /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/../../../../riscv64-unknown-elf/lib/rv64imafdcxtheadc/lp64d/libc.a(lib_a-wcrtomb.o)
 .riscv.attributes
                0x0000000000001058       0x3e /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/rv64imafdcxtheadc/lp64d/libgcc.a(eqtf2.o)
 .riscv.attributes
                0x0000000000001096       0x3e /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/rv64imafdcxtheadc/lp64d/libgcc.a(getf2.o)
 .riscv.attributes
                0x00000000000010d4       0x3e /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/rv64imafdcxtheadc/lp64d/libgcc.a(letf2.o)
 .riscv.attributes
                0x0000000000001112       0x3e /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/rv64imafdcxtheadc/lp64d/libgcc.a(multf3.o)
 .riscv.attributes
                0x0000000000001150       0x3e /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/rv64imafdcxtheadc/lp64d/libgcc.a(subtf3.o)
 .riscv.attributes
                0x000000000000118e       0x3e /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/rv64imafdcxtheadc/lp64d/libgcc.a(fixtfsi.o)
 .riscv.attributes
                0x00000000000011cc       0x3e /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/rv64imafdcxtheadc/lp64d/libgcc.a(floatsitf.o)
 .riscv.attributes
                0x000000000000120a       0x3e /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/rv64imafdcxtheadc/lp64d/libgcc.a(extenddftf2.o)
 .riscv.attributes
                0x0000000000001248       0x3e /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/rv64imafdcxtheadc/lp64d/libgcc.a(trunctfdf2.o)
 .riscv.attributes
                0x0000000000001286       0x3e /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/rv64imafdcxtheadc/lp64d/libgcc.a(_clzsi2.o)
 .riscv.attributes
                0x00000000000012c4       0x3e /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/rv64imafdcxtheadc/lp64d/libgcc.a(_clz.o)

/DISCARD/
 *(.note.GNU-stack)
 *(.ARM.exidx* .gnu.linkonce.armexidx.*)
OUTPUT(emodules/drv_mcu_uart/mcu.o elf64-littleriscv)

.debug_info     0x0000000000000000   0x108476
 .debug_info    0x0000000000000000     0x2ac6 emodules/drv_mcu_uart/dev_mcu.o
 .debug_info    0x0000000000002ac6     0x218d emodules/drv_mcu_uart/drv_mcu.o
 .debug_info    0x0000000000004c53     0x143c emodules/drv_mcu_uart/magic.o
 .debug_info    0x000000000000608f     0xf54d emodules/drv_mcu_uart/core/mcu_interrupt_process.o
 .debug_info    0x00000000000155dc     0x9b66 emodules/drv_mcu_uart/core/mcu_aux_init_para.o
 .debug_info    0x000000000001f142     0xb837 emodules/drv_mcu_uart/core/mcu_bypass_cmd_proc.o
 .debug_info    0x000000000002a979     0x9bc6 emodules/drv_mcu_uart/core/mcu_update_proc.o
 .debug_info    0x000000000003453f     0x9ec9 emodules/drv_mcu_uart/core/mcu_cmd_dispatcher.o
 .debug_info    0x000000000003e408     0xab82 emodules/drv_mcu_uart/core/handlers/system/mcu_system_handler.o
 .debug_info    0x0000000000048f8a     0xa67b emodules/drv_mcu_uart/core/handlers/media/mcu_tuner_handler.o
 .debug_info    0x0000000000053605     0xaad2 emodules/drv_mcu_uart/core/handlers/media/mcu_usb_handler.o
 .debug_info    0x000000000005e0d7     0xa745 emodules/drv_mcu_uart/core/handlers/bluetooth/mcu_bt_handler.o
 .debug_info    0x000000000006881c     0xa9d7 emodules/drv_mcu_uart/core/handlers/sxm/mcu_sxm_handler.o
 .debug_info    0x00000000000731f3     0xa60c emodules/drv_mcu_uart/core/handlers/settings/mcu_settings_handler.o
 .debug_info    0x000000000007d7ff     0xa63a emodules/drv_mcu_uart/core/handlers/audio/mcu_audio_handler.o
 .debug_info    0x0000000000087e39     0xa161 emodules/drv_mcu_uart/core/handlers/upgrade/mcu_upgrade_handler.o
 .debug_info    0x0000000000091f9a     0x19c9 emodules/drv_mcu_uart/core/handlers/bypass/mcu_bypass_handler.o
 .debug_info    0x0000000000093963     0xa8f5 emodules/drv_mcu_uart/core/handlers/misc/mcu_misc_handler.o
 .debug_info    0x000000000009e258     0x24f2 emodules/drv_mcu_uart/core/handlers/protocol/mcu_new_protocol_handler.o
 .debug_info    0x00000000000a074a     0xb2d0 emodules/drv_mcu_uart/core/handlers/protocol/system/mcu_system_category_handler.o
 .debug_info    0x00000000000aba1a     0xbf26 emodules/drv_mcu_uart/core/handlers/protocol/audio/mcu_audio_category_handler.o
 .debug_info    0x00000000000b7940     0xc214 emodules/drv_mcu_uart/core/handlers/protocol/tuner/mcu_tuner_category_handler.o
 .debug_info    0x00000000000c3b54     0xaf88 emodules/drv_mcu_uart/core/handlers/protocol/party/mcu_party_category_handler.o
 .debug_info    0x00000000000ceadc     0xac51 emodules/drv_mcu_uart/core/handlers/protocol/usb/mcu_usb_category_handler.o
 .debug_info    0x00000000000d972d     0xa51d emodules/drv_mcu_uart/core/handlers/protocol/settings/mcu_settings_category_handler.o
 .debug_info    0x00000000000e3c4a    0x12bf0 ./elibrary/bin//libsyscall.a(syscall.o)
 .debug_info    0x00000000000f683a     0x2f5e ./elibrary/bin//libsyscall.a(syscall_fops.o)
 .debug_info    0x00000000000f9798     0x5cd8 ./elibrary/bin//libsyscall.a(syscall.o)
 .debug_info    0x00000000000ff470     0x2b1c ./elibrary/bin//libminic.a(elibs_stdio.o)
 .debug_info    0x0000000000101f8c     0x1691 ./elibrary/bin//libminic.a(elibs_stdlib.o)
 .debug_info    0x000000000010361d      0xb2d ./elibrary/bin//libminic.a(elibs_string.o)
 .debug_info    0x000000000010414a      0x7d5 ./elibrary/bin//libminic.a(elibs_sprintf.o)
 .debug_info    0x000000000010491f      0x2d9 /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/rv64imafdcxtheadc/lp64d/libgcc.a(eqtf2.o)
 .debug_info    0x0000000000104bf8      0x2cb /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/rv64imafdcxtheadc/lp64d/libgcc.a(getf2.o)
 .debug_info    0x0000000000104ec3      0x2cb /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/rv64imafdcxtheadc/lp64d/libgcc.a(letf2.o)
 .debug_info    0x000000000010518e      0x8a7 /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/rv64imafdcxtheadc/lp64d/libgcc.a(multf3.o)
 .debug_info    0x0000000000105a35      0x68f /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/rv64imafdcxtheadc/lp64d/libgcc.a(subtf3.o)
 .debug_info    0x00000000001060c4      0x262 /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/rv64imafdcxtheadc/lp64d/libgcc.a(fixtfsi.o)
 .debug_info    0x0000000000106326      0x317 /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/rv64imafdcxtheadc/lp64d/libgcc.a(floatsitf.o)
 .debug_info    0x000000000010663d      0x302 /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/rv64imafdcxtheadc/lp64d/libgcc.a(extenddftf2.o)
 .debug_info    0x000000000010693f      0x340 /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/rv64imafdcxtheadc/lp64d/libgcc.a(trunctfdf2.o)
 .debug_info    0x0000000000106c7f      0xc38 /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/rv64imafdcxtheadc/lp64d/libgcc.a(_clzsi2.o)
 .debug_info    0x00000000001078b7      0xbbf /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/rv64imafdcxtheadc/lp64d/libgcc.a(_clz.o)

.debug_abbrev   0x0000000000000000     0x8833
 .debug_abbrev  0x0000000000000000      0x543 emodules/drv_mcu_uart/dev_mcu.o
 .debug_abbrev  0x0000000000000543      0x33c emodules/drv_mcu_uart/drv_mcu.o
 .debug_abbrev  0x000000000000087f      0x225 emodules/drv_mcu_uart/magic.o
 .debug_abbrev  0x0000000000000aa4      0x74b emodules/drv_mcu_uart/core/mcu_interrupt_process.o
 .debug_abbrev  0x00000000000011ef      0x2d1 emodules/drv_mcu_uart/core/mcu_aux_init_para.o
 .debug_abbrev  0x00000000000014c0      0x410 emodules/drv_mcu_uart/core/mcu_bypass_cmd_proc.o
 .debug_abbrev  0x00000000000018d0      0x372 emodules/drv_mcu_uart/core/mcu_update_proc.o
 .debug_abbrev  0x0000000000001c42      0x462 emodules/drv_mcu_uart/core/mcu_cmd_dispatcher.o
 .debug_abbrev  0x00000000000020a4      0x3b4 emodules/drv_mcu_uart/core/handlers/system/mcu_system_handler.o
 .debug_abbrev  0x0000000000002458      0x378 emodules/drv_mcu_uart/core/handlers/media/mcu_tuner_handler.o
 .debug_abbrev  0x00000000000027d0      0x3a5 emodules/drv_mcu_uart/core/handlers/media/mcu_usb_handler.o
 .debug_abbrev  0x0000000000002b75      0x378 emodules/drv_mcu_uart/core/handlers/bluetooth/mcu_bt_handler.o
 .debug_abbrev  0x0000000000002eed      0x3d6 emodules/drv_mcu_uart/core/handlers/sxm/mcu_sxm_handler.o
 .debug_abbrev  0x00000000000032c3      0x35e emodules/drv_mcu_uart/core/handlers/settings/mcu_settings_handler.o
 .debug_abbrev  0x0000000000003621      0x35e emodules/drv_mcu_uart/core/handlers/audio/mcu_audio_handler.o
 .debug_abbrev  0x000000000000397f      0x33c emodules/drv_mcu_uart/core/handlers/upgrade/mcu_upgrade_handler.o
 .debug_abbrev  0x0000000000003cbb      0x29a emodules/drv_mcu_uart/core/handlers/bypass/mcu_bypass_handler.o
 .debug_abbrev  0x0000000000003f55      0x356 emodules/drv_mcu_uart/core/handlers/misc/mcu_misc_handler.o
 .debug_abbrev  0x00000000000042ab      0x452 emodules/drv_mcu_uart/core/handlers/protocol/mcu_new_protocol_handler.o
 .debug_abbrev  0x00000000000046fd      0x464 emodules/drv_mcu_uart/core/handlers/protocol/system/mcu_system_category_handler.o
 .debug_abbrev  0x0000000000004b61      0x4e6 emodules/drv_mcu_uart/core/handlers/protocol/audio/mcu_audio_category_handler.o
 .debug_abbrev  0x0000000000005047      0x425 emodules/drv_mcu_uart/core/handlers/protocol/tuner/mcu_tuner_category_handler.o
 .debug_abbrev  0x000000000000546c      0x42d emodules/drv_mcu_uart/core/handlers/protocol/party/mcu_party_category_handler.o
 .debug_abbrev  0x0000000000005899      0x46d emodules/drv_mcu_uart/core/handlers/protocol/usb/mcu_usb_category_handler.o
 .debug_abbrev  0x0000000000005d06      0x411 emodules/drv_mcu_uart/core/handlers/protocol/settings/mcu_settings_category_handler.o
 .debug_abbrev  0x0000000000006117      0x3a2 ./elibrary/bin//libsyscall.a(syscall.o)
 .debug_abbrev  0x00000000000064b9      0x3ec ./elibrary/bin//libsyscall.a(syscall_fops.o)
 .debug_abbrev  0x00000000000068a5      0x34c ./elibrary/bin//libsyscall.a(syscall.o)
 .debug_abbrev  0x0000000000006bf1      0x3d0 ./elibrary/bin//libminic.a(elibs_stdio.o)
 .debug_abbrev  0x0000000000006fc1      0x2f3 ./elibrary/bin//libminic.a(elibs_stdlib.o)
 .debug_abbrev  0x00000000000072b4      0x155 ./elibrary/bin//libminic.a(elibs_string.o)
 .debug_abbrev  0x0000000000007409      0x14d ./elibrary/bin//libminic.a(elibs_sprintf.o)
 .debug_abbrev  0x0000000000007556      0x1b5 /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/rv64imafdcxtheadc/lp64d/libgcc.a(eqtf2.o)
 .debug_abbrev  0x000000000000770b      0x1bb /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/rv64imafdcxtheadc/lp64d/libgcc.a(getf2.o)
 .debug_abbrev  0x00000000000078c6      0x1bb /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/rv64imafdcxtheadc/lp64d/libgcc.a(letf2.o)
 .debug_abbrev  0x0000000000007a81      0x1a8 /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/rv64imafdcxtheadc/lp64d/libgcc.a(multf3.o)
 .debug_abbrev  0x0000000000007c29      0x19a /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/rv64imafdcxtheadc/lp64d/libgcc.a(subtf3.o)
 .debug_abbrev  0x0000000000007dc3      0x183 /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/rv64imafdcxtheadc/lp64d/libgcc.a(fixtfsi.o)
 .debug_abbrev  0x0000000000007f46      0x19d /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/rv64imafdcxtheadc/lp64d/libgcc.a(floatsitf.o)
 .debug_abbrev  0x00000000000080e3      0x17d /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/rv64imafdcxtheadc/lp64d/libgcc.a(extenddftf2.o)
 .debug_abbrev  0x0000000000008260      0x179 /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/rv64imafdcxtheadc/lp64d/libgcc.a(trunctfdf2.o)
 .debug_abbrev  0x00000000000083d9      0x25a /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/rv64imafdcxtheadc/lp64d/libgcc.a(_clzsi2.o)
 .debug_abbrev  0x0000000000008633      0x200 /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/rv64imafdcxtheadc/lp64d/libgcc.a(_clz.o)

.debug_loc      0x0000000000000000    0x30824
 .debug_loc     0x0000000000000000      0x91a emodules/drv_mcu_uart/dev_mcu.o
 .debug_loc     0x000000000000091a      0x298 emodules/drv_mcu_uart/drv_mcu.o
 .debug_loc     0x0000000000000bb2     0x5f18 emodules/drv_mcu_uart/core/mcu_interrupt_process.o
 .debug_loc     0x0000000000006aca      0x771 emodules/drv_mcu_uart/core/mcu_bypass_cmd_proc.o
 .debug_loc     0x000000000000723b      0x2d4 emodules/drv_mcu_uart/core/mcu_update_proc.o
 .debug_loc     0x000000000000750f      0x447 emodules/drv_mcu_uart/core/mcu_cmd_dispatcher.o
 .debug_loc     0x0000000000007956     0x329d emodules/drv_mcu_uart/core/handlers/system/mcu_system_handler.o
 .debug_loc     0x000000000000abf3      0x5db emodules/drv_mcu_uart/core/handlers/media/mcu_tuner_handler.o
 .debug_loc     0x000000000000b1ce      0x8bd emodules/drv_mcu_uart/core/handlers/media/mcu_usb_handler.o
 .debug_loc     0x000000000000ba8b      0x358 emodules/drv_mcu_uart/core/handlers/bluetooth/mcu_bt_handler.o
 .debug_loc     0x000000000000bde3      0xb31 emodules/drv_mcu_uart/core/handlers/sxm/mcu_sxm_handler.o
 .debug_loc     0x000000000000c914      0xc92 emodules/drv_mcu_uart/core/handlers/settings/mcu_settings_handler.o
 .debug_loc     0x000000000000d5a6      0x181 emodules/drv_mcu_uart/core/handlers/audio/mcu_audio_handler.o
 .debug_loc     0x000000000000d727      0x209 emodules/drv_mcu_uart/core/handlers/upgrade/mcu_upgrade_handler.o
 .debug_loc     0x000000000000d930      0x121 emodules/drv_mcu_uart/core/handlers/bypass/mcu_bypass_handler.o
 .debug_loc     0x000000000000da51      0x3d6 emodules/drv_mcu_uart/core/handlers/misc/mcu_misc_handler.o
 .debug_loc     0x000000000000de27      0x949 emodules/drv_mcu_uart/core/handlers/protocol/mcu_new_protocol_handler.o
 .debug_loc     0x000000000000e770      0x685 emodules/drv_mcu_uart/core/handlers/protocol/system/mcu_system_category_handler.o
 .debug_loc     0x000000000000edf5     0x208c emodules/drv_mcu_uart/core/handlers/protocol/audio/mcu_audio_category_handler.o
 .debug_loc     0x0000000000010e81     0x2bdc emodules/drv_mcu_uart/core/handlers/protocol/tuner/mcu_tuner_category_handler.o
 .debug_loc     0x0000000000013a5d      0xc70 emodules/drv_mcu_uart/core/handlers/protocol/party/mcu_party_category_handler.o
 .debug_loc     0x00000000000146cd      0x5d2 emodules/drv_mcu_uart/core/handlers/protocol/usb/mcu_usb_category_handler.o
 .debug_loc     0x0000000000014c9f      0x704 emodules/drv_mcu_uart/core/handlers/protocol/settings/mcu_settings_category_handler.o
 .debug_loc     0x00000000000153a3     0xda5d ./elibrary/bin//libsyscall.a(syscall.o)
 .debug_loc     0x0000000000022e00     0x1325 ./elibrary/bin//libsyscall.a(syscall_fops.o)
 .debug_loc     0x0000000000024125     0x385a ./elibrary/bin//libsyscall.a(syscall.o)
 .debug_loc     0x000000000002797f     0x25c5 ./elibrary/bin//libminic.a(elibs_stdio.o)
 .debug_loc     0x0000000000029f44      0x38b ./elibrary/bin//libminic.a(elibs_stdlib.o)
 .debug_loc     0x000000000002a2cf      0x705 ./elibrary/bin//libminic.a(elibs_string.o)
 .debug_loc     0x000000000002a9d4      0x3f2 ./elibrary/bin//libminic.a(elibs_sprintf.o)
 .debug_loc     0x000000000002adc6      0x1eb /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/rv64imafdcxtheadc/lp64d/libgcc.a(eqtf2.o)
 .debug_loc     0x000000000002afb1      0x1c2 /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/rv64imafdcxtheadc/lp64d/libgcc.a(getf2.o)
 .debug_loc     0x000000000002b173      0x1c2 /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/rv64imafdcxtheadc/lp64d/libgcc.a(letf2.o)
 .debug_loc     0x000000000002b335     0x1d18 /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/rv64imafdcxtheadc/lp64d/libgcc.a(multf3.o)
 .debug_loc     0x000000000002d04d     0x23e2 /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/rv64imafdcxtheadc/lp64d/libgcc.a(subtf3.o)
 .debug_loc     0x000000000002f42f      0x334 /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/rv64imafdcxtheadc/lp64d/libgcc.a(fixtfsi.o)
 .debug_loc     0x000000000002f763      0x182 /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/rv64imafdcxtheadc/lp64d/libgcc.a(floatsitf.o)
 .debug_loc     0x000000000002f8e5      0x559 /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/rv64imafdcxtheadc/lp64d/libgcc.a(extenddftf2.o)
 .debug_loc     0x000000000002fe3e      0x93c /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/rv64imafdcxtheadc/lp64d/libgcc.a(trunctfdf2.o)
 .debug_loc     0x000000000003077a       0xaa /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/rv64imafdcxtheadc/lp64d/libgcc.a(_clzsi2.o)

.debug_aranges  0x0000000000000000     0x3b70
 .debug_aranges
                0x0000000000000000      0x110 emodules/drv_mcu_uart/dev_mcu.o
 .debug_aranges
                0x0000000000000110       0x80 emodules/drv_mcu_uart/drv_mcu.o
 .debug_aranges
                0x0000000000000190       0x20 emodules/drv_mcu_uart/magic.o
 .debug_aranges
                0x00000000000001b0      0x400 emodules/drv_mcu_uart/core/mcu_interrupt_process.o
 .debug_aranges
                0x00000000000005b0       0x20 emodules/drv_mcu_uart/core/mcu_aux_init_para.o
 .debug_aranges
                0x00000000000005d0       0x70 emodules/drv_mcu_uart/core/mcu_bypass_cmd_proc.o
 .debug_aranges
                0x0000000000000640       0x80 emodules/drv_mcu_uart/core/mcu_update_proc.o
 .debug_aranges
                0x00000000000006c0       0x90 emodules/drv_mcu_uart/core/mcu_cmd_dispatcher.o
 .debug_aranges
                0x0000000000000750       0x40 emodules/drv_mcu_uart/core/handlers/system/mcu_system_handler.o
 .debug_aranges
                0x0000000000000790       0x40 emodules/drv_mcu_uart/core/handlers/media/mcu_tuner_handler.o
 .debug_aranges
                0x00000000000007d0       0x40 emodules/drv_mcu_uart/core/handlers/media/mcu_usb_handler.o
 .debug_aranges
                0x0000000000000810       0x40 emodules/drv_mcu_uart/core/handlers/bluetooth/mcu_bt_handler.o
 .debug_aranges
                0x0000000000000850       0x40 emodules/drv_mcu_uart/core/handlers/sxm/mcu_sxm_handler.o
 .debug_aranges
                0x0000000000000890       0x40 emodules/drv_mcu_uart/core/handlers/settings/mcu_settings_handler.o
 .debug_aranges
                0x00000000000008d0       0x40 emodules/drv_mcu_uart/core/handlers/audio/mcu_audio_handler.o
 .debug_aranges
                0x0000000000000910       0x40 emodules/drv_mcu_uart/core/handlers/upgrade/mcu_upgrade_handler.o
 .debug_aranges
                0x0000000000000950       0x40 emodules/drv_mcu_uart/core/handlers/bypass/mcu_bypass_handler.o
 .debug_aranges
                0x0000000000000990       0x40 emodules/drv_mcu_uart/core/handlers/misc/mcu_misc_handler.o
 .debug_aranges
                0x00000000000009d0       0xe0 emodules/drv_mcu_uart/core/handlers/protocol/mcu_new_protocol_handler.o
 .debug_aranges
                0x0000000000000ab0      0x140 emodules/drv_mcu_uart/core/handlers/protocol/system/mcu_system_category_handler.o
 .debug_aranges
                0x0000000000000bf0      0x230 emodules/drv_mcu_uart/core/handlers/protocol/audio/mcu_audio_category_handler.o
 .debug_aranges
                0x0000000000000e20       0x90 emodules/drv_mcu_uart/core/handlers/protocol/tuner/mcu_tuner_category_handler.o
 .debug_aranges
                0x0000000000000eb0       0xe0 emodules/drv_mcu_uart/core/handlers/protocol/party/mcu_party_category_handler.o
 .debug_aranges
                0x0000000000000f90       0xd0 emodules/drv_mcu_uart/core/handlers/protocol/usb/mcu_usb_category_handler.o
 .debug_aranges
                0x0000000000001060       0x80 emodules/drv_mcu_uart/core/handlers/protocol/settings/mcu_settings_category_handler.o
 .debug_aranges
                0x00000000000010e0     0x1840 ./elibrary/bin//libsyscall.a(syscall.o)
 .debug_aranges
                0x0000000000002920      0x260 ./elibrary/bin//libsyscall.a(syscall_fops.o)
 .debug_aranges
                0x0000000000002b80      0x660 ./elibrary/bin//libsyscall.a(syscall.o)
 .debug_aranges
                0x00000000000031e0      0x480 ./elibrary/bin//libminic.a(elibs_stdio.o)
 .debug_aranges
                0x0000000000003660      0x140 ./elibrary/bin//libminic.a(elibs_stdlib.o)
 .debug_aranges
                0x00000000000037a0      0x150 ./elibrary/bin//libminic.a(elibs_string.o)
 .debug_aranges
                0x00000000000038f0       0x80 ./elibrary/bin//libminic.a(elibs_sprintf.o)
 .debug_aranges
                0x0000000000003970       0x30 /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/rv64imafdcxtheadc/lp64d/libgcc.a(eqtf2.o)
 .debug_aranges
                0x00000000000039a0       0x30 /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/rv64imafdcxtheadc/lp64d/libgcc.a(getf2.o)
 .debug_aranges
                0x00000000000039d0       0x30 /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/rv64imafdcxtheadc/lp64d/libgcc.a(letf2.o)
 .debug_aranges
                0x0000000000003a00       0x30 /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/rv64imafdcxtheadc/lp64d/libgcc.a(multf3.o)
 .debug_aranges
                0x0000000000003a30       0x30 /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/rv64imafdcxtheadc/lp64d/libgcc.a(subtf3.o)
 .debug_aranges
                0x0000000000003a60       0x30 /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/rv64imafdcxtheadc/lp64d/libgcc.a(fixtfsi.o)
 .debug_aranges
                0x0000000000003a90       0x30 /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/rv64imafdcxtheadc/lp64d/libgcc.a(floatsitf.o)
 .debug_aranges
                0x0000000000003ac0       0x30 /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/rv64imafdcxtheadc/lp64d/libgcc.a(extenddftf2.o)
 .debug_aranges
                0x0000000000003af0       0x30 /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/rv64imafdcxtheadc/lp64d/libgcc.a(trunctfdf2.o)
 .debug_aranges
                0x0000000000003b20       0x30 /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/rv64imafdcxtheadc/lp64d/libgcc.a(_clzsi2.o)
 .debug_aranges
                0x0000000000003b50       0x20 /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/rv64imafdcxtheadc/lp64d/libgcc.a(_clz.o)

.debug_line     0x0000000000000000    0x55aec
 .debug_line    0x0000000000000000      0xfff emodules/drv_mcu_uart/dev_mcu.o
 .debug_line    0x0000000000000fff      0x8e3 emodules/drv_mcu_uart/drv_mcu.o
 .debug_line    0x00000000000018e2      0x521 emodules/drv_mcu_uart/magic.o
 .debug_line    0x0000000000001e03     0xe04d emodules/drv_mcu_uart/core/mcu_interrupt_process.o
 .debug_line    0x000000000000fe50      0x6d7 emodules/drv_mcu_uart/core/mcu_aux_init_para.o
 .debug_line    0x0000000000010527     0x263e emodules/drv_mcu_uart/core/mcu_bypass_cmd_proc.o
 .debug_line    0x0000000000012b65      0xfc7 emodules/drv_mcu_uart/core/mcu_update_proc.o
 .debug_line    0x0000000000013b2c     0x10b1 emodules/drv_mcu_uart/core/mcu_cmd_dispatcher.o
 .debug_line    0x0000000000014bdd     0x707e emodules/drv_mcu_uart/core/handlers/system/mcu_system_handler.o
 .debug_line    0x000000000001bc5b     0x1c66 emodules/drv_mcu_uart/core/handlers/media/mcu_tuner_handler.o
 .debug_line    0x000000000001d8c1     0x306f emodules/drv_mcu_uart/core/handlers/media/mcu_usb_handler.o
 .debug_line    0x0000000000020930      0xc5d emodules/drv_mcu_uart/core/handlers/bluetooth/mcu_bt_handler.o
 .debug_line    0x000000000002158d     0x3e8c emodules/drv_mcu_uart/core/handlers/sxm/mcu_sxm_handler.o
 .debug_line    0x0000000000025419     0x26e1 emodules/drv_mcu_uart/core/handlers/settings/mcu_settings_handler.o
 .debug_line    0x0000000000027afa      0xa42 emodules/drv_mcu_uart/core/handlers/audio/mcu_audio_handler.o
 .debug_line    0x000000000002853c      0xc8c emodules/drv_mcu_uart/core/handlers/upgrade/mcu_upgrade_handler.o
 .debug_line    0x00000000000291c8      0x60e emodules/drv_mcu_uart/core/handlers/bypass/mcu_bypass_handler.o
 .debug_line    0x00000000000297d6      0xb14 emodules/drv_mcu_uart/core/handlers/misc/mcu_misc_handler.o
 .debug_line    0x000000000002a2ea     0x162f emodules/drv_mcu_uart/core/handlers/protocol/mcu_new_protocol_handler.o
 .debug_line    0x000000000002b919     0x20a2 emodules/drv_mcu_uart/core/handlers/protocol/system/mcu_system_category_handler.o
 .debug_line    0x000000000002d9bb     0x3c93 emodules/drv_mcu_uart/core/handlers/protocol/audio/mcu_audio_category_handler.o
 .debug_line    0x000000000003164e     0x3bb4 emodules/drv_mcu_uart/core/handlers/protocol/tuner/mcu_tuner_category_handler.o
 .debug_line    0x0000000000035202     0x1b16 emodules/drv_mcu_uart/core/handlers/protocol/party/mcu_party_category_handler.o
 .debug_line    0x0000000000036d18     0x1eb6 emodules/drv_mcu_uart/core/handlers/protocol/usb/mcu_usb_category_handler.o
 .debug_line    0x0000000000038bce     0x16d2 emodules/drv_mcu_uart/core/handlers/protocol/settings/mcu_settings_category_handler.o
 .debug_line    0x000000000003a2a0     0xe68f ./elibrary/bin//libsyscall.a(syscall.o)
 .debug_line    0x000000000004892f     0x1864 ./elibrary/bin//libsyscall.a(syscall_fops.o)
 .debug_line    0x000000000004a193     0x3d44 ./elibrary/bin//libsyscall.a(syscall.o)
 .debug_line    0x000000000004ded7     0x22a9 ./elibrary/bin//libminic.a(elibs_stdio.o)
 .debug_line    0x0000000000050180      0x6cb ./elibrary/bin//libminic.a(elibs_stdlib.o)
 .debug_line    0x000000000005084b      0x5f9 ./elibrary/bin//libminic.a(elibs_string.o)
 .debug_line    0x0000000000050e44      0x495 ./elibrary/bin//libminic.a(elibs_sprintf.o)
 .debug_line    0x00000000000512d9      0x35c /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/rv64imafdcxtheadc/lp64d/libgcc.a(eqtf2.o)
 .debug_line    0x0000000000051635      0x363 /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/rv64imafdcxtheadc/lp64d/libgcc.a(getf2.o)
 .debug_line    0x0000000000051998      0x363 /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/rv64imafdcxtheadc/lp64d/libgcc.a(letf2.o)
 .debug_line    0x0000000000051cfb     0x121b /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/rv64imafdcxtheadc/lp64d/libgcc.a(multf3.o)
 .debug_line    0x0000000000052f16     0x1521 /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/rv64imafdcxtheadc/lp64d/libgcc.a(subtf3.o)
 .debug_line    0x0000000000054437      0x2fa /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/rv64imafdcxtheadc/lp64d/libgcc.a(fixtfsi.o)
 .debug_line    0x0000000000054731      0x291 /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/rv64imafdcxtheadc/lp64d/libgcc.a(floatsitf.o)
 .debug_line    0x00000000000549c2      0x401 /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/rv64imafdcxtheadc/lp64d/libgcc.a(extenddftf2.o)
 .debug_line    0x0000000000054dc3      0x6ed /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/rv64imafdcxtheadc/lp64d/libgcc.a(trunctfdf2.o)
 .debug_line    0x00000000000554b0      0x369 /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/rv64imafdcxtheadc/lp64d/libgcc.a(_clzsi2.o)
 .debug_line    0x0000000000055819      0x2d3 /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/rv64imafdcxtheadc/lp64d/libgcc.a(_clz.o)

.debug_str      0x0000000000000000    0x18881
 .debug_str     0x0000000000000000     0x141a emodules/drv_mcu_uart/dev_mcu.o
                                       0x16c4 (size before relaxing)
 .debug_str     0x000000000000141a      0x814 emodules/drv_mcu_uart/drv_mcu.o
                                       0x1887 (size before relaxing)
 .debug_str     0x0000000000001c2e       0x64 emodules/drv_mcu_uart/magic.o
                                       0x11ea (size before relaxing)
 .debug_str     0x0000000000001c92     0xcbf2 emodules/drv_mcu_uart/core/mcu_interrupt_process.o
                                       0xf598 (size before relaxing)
 .debug_str     0x000000000000e884       0x2f emodules/drv_mcu_uart/core/mcu_aux_init_para.o
                                       0x7cb5 (size before relaxing)
 .debug_str     0x000000000000e8b3       0xf4 emodules/drv_mcu_uart/core/mcu_bypass_cmd_proc.o
                                       0xc341 (size before relaxing)
 .debug_str     0x000000000000e9a7       0xe7 emodules/drv_mcu_uart/core/mcu_update_proc.o
                                       0x7dcb (size before relaxing)
 .debug_str     0x000000000000ea8e      0x1d2 emodules/drv_mcu_uart/core/mcu_cmd_dispatcher.o
                                       0x7e1b (size before relaxing)
 .debug_str     0x000000000000ec60      0x2a5 emodules/drv_mcu_uart/core/handlers/system/mcu_system_handler.o
                                       0xb8ef (size before relaxing)
 .debug_str     0x000000000000ef05      0x12e emodules/drv_mcu_uart/core/handlers/media/mcu_tuner_handler.o
                                       0xacb7 (size before relaxing)
 .debug_str     0x000000000000f033       0x84 emodules/drv_mcu_uart/core/handlers/media/mcu_usb_handler.o
                                       0xb696 (size before relaxing)
 .debug_str     0x000000000000f0b7       0xa3 emodules/drv_mcu_uart/core/handlers/bluetooth/mcu_bt_handler.o
                                       0xaeac (size before relaxing)
 .debug_str     0x000000000000f15a      0x2fc emodules/drv_mcu_uart/core/handlers/sxm/mcu_sxm_handler.o
                                       0xb16a (size before relaxing)
 .debug_str     0x000000000000f456       0x7b emodules/drv_mcu_uart/core/handlers/settings/mcu_settings_handler.o
                                       0xac04 (size before relaxing)
 .debug_str     0x000000000000f4d1       0x6f emodules/drv_mcu_uart/core/handlers/audio/mcu_audio_handler.o
                                       0xac46 (size before relaxing)
 .debug_str     0x000000000000f540       0xd7 emodules/drv_mcu_uart/core/handlers/upgrade/mcu_upgrade_handler.o
                                       0x9933 (size before relaxing)
 .debug_str     0x000000000000f617       0x86 emodules/drv_mcu_uart/core/handlers/bypass/mcu_bypass_handler.o
                                       0x2db6 (size before relaxing)
 .debug_str     0x000000000000f69d      0x12d emodules/drv_mcu_uart/core/handlers/misc/mcu_misc_handler.o
                                       0xb776 (size before relaxing)
 .debug_str     0x000000000000f7ca      0xcf3 emodules/drv_mcu_uart/core/handlers/protocol/mcu_new_protocol_handler.o
                                       0x216d (size before relaxing)
 .debug_str     0x00000000000104bd      0x6b0 emodules/drv_mcu_uart/core/handlers/protocol/system/mcu_system_category_handler.o
                                       0xa143 (size before relaxing)
 .debug_str     0x0000000000010b6d      0x882 emodules/drv_mcu_uart/core/handlers/protocol/audio/mcu_audio_category_handler.o
                                       0xa486 (size before relaxing)
 .debug_str     0x00000000000113ef      0xae9 emodules/drv_mcu_uart/core/handlers/protocol/tuner/mcu_tuner_category_handler.o
                                       0xa3a2 (size before relaxing)
 .debug_str     0x0000000000011ed8      0x4f1 emodules/drv_mcu_uart/core/handlers/protocol/party/mcu_party_category_handler.o
                                       0xa126 (size before relaxing)
 .debug_str     0x00000000000123c9      0xb4e emodules/drv_mcu_uart/core/handlers/protocol/usb/mcu_usb_category_handler.o
                                       0x89cf (size before relaxing)
 .debug_str     0x0000000000012f17      0x59f emodules/drv_mcu_uart/core/handlers/protocol/settings/mcu_settings_category_handler.o
                                       0x8430 (size before relaxing)
 .debug_str     0x00000000000134b6     0x2b42 ./elibrary/bin//libsyscall.a(syscall.o)
                                       0x3c33 (size before relaxing)
 .debug_str     0x0000000000015ff8      0x1aa ./elibrary/bin//libsyscall.a(syscall_fops.o)
                                       0x1068 (size before relaxing)
 .debug_str     0x00000000000161a2      0xc61 ./elibrary/bin//libsyscall.a(syscall.o)
                                       0x1a68 (size before relaxing)
 .debug_str     0x0000000000016e03      0xa47 ./elibrary/bin//libminic.a(elibs_stdio.o)
                                       0x1624 (size before relaxing)
 .debug_str     0x000000000001784a      0x2f3 ./elibrary/bin//libminic.a(elibs_stdlib.o)
                                        0xd03 (size before relaxing)
 .debug_str     0x0000000000017b3d      0x17d ./elibrary/bin//libminic.a(elibs_string.o)
                                        0x6d8 (size before relaxing)
 .debug_str     0x0000000000017cba       0x81 ./elibrary/bin//libminic.a(elibs_sprintf.o)
                                        0x5d3 (size before relaxing)
 .debug_str     0x0000000000017d3b      0x21f /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/rv64imafdcxtheadc/lp64d/libgcc.a(eqtf2.o)
                                        0x29c (size before relaxing)
 .debug_str     0x0000000000017f5a       0x6c /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/rv64imafdcxtheadc/lp64d/libgcc.a(getf2.o)
                                        0x2c0 (size before relaxing)
 .debug_str     0x0000000000017fc6       0x48 /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/rv64imafdcxtheadc/lp64d/libgcc.a(letf2.o)
                                        0x2c0 (size before relaxing)
 .debug_str     0x000000000001800e      0x343 /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/rv64imafdcxtheadc/lp64d/libgcc.a(multf3.o)
                                        0x578 (size before relaxing)
 .debug_str     0x0000000000018351      0x130 /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/rv64imafdcxtheadc/lp64d/libgcc.a(subtf3.o)
                                        0x382 (size before relaxing)
 .debug_str     0x0000000000018481       0x5f /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/rv64imafdcxtheadc/lp64d/libgcc.a(fixtfsi.o)
                                        0x299 (size before relaxing)
 .debug_str     0x00000000000184e0       0x7d /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/rv64imafdcxtheadc/lp64d/libgcc.a(floatsitf.o)
                                        0x32f (size before relaxing)
 .debug_str     0x000000000001855d       0x8e /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/rv64imafdcxtheadc/lp64d/libgcc.a(extenddftf2.o)
                                        0x2be (size before relaxing)
 .debug_str     0x00000000000185eb       0x7a /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/rv64imafdcxtheadc/lp64d/libgcc.a(trunctfdf2.o)
                                        0x31c (size before relaxing)
 .debug_str     0x0000000000018665      0x21c /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/rv64imafdcxtheadc/lp64d/libgcc.a(_clzsi2.o)
                                        0x880 (size before relaxing)
 .debug_str     0x0000000000018881      0x863 /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/rv64imafdcxtheadc/lp64d/libgcc.a(_clz.o)

.debug_frame    0x0000000000000000     0x8640
 .debug_frame   0x0000000000000000      0x280 emodules/drv_mcu_uart/dev_mcu.o
 .debug_frame   0x0000000000000280       0xf0 emodules/drv_mcu_uart/drv_mcu.o
 .debug_frame   0x0000000000000370      0xa80 emodules/drv_mcu_uart/core/mcu_interrupt_process.o
 .debug_frame   0x0000000000000df0      0x120 emodules/drv_mcu_uart/core/mcu_bypass_cmd_proc.o
 .debug_frame   0x0000000000000f10       0xe8 emodules/drv_mcu_uart/core/mcu_update_proc.o
 .debug_frame   0x0000000000000ff8      0x110 emodules/drv_mcu_uart/core/mcu_cmd_dispatcher.o
 .debug_frame   0x0000000000001108       0x80 emodules/drv_mcu_uart/core/handlers/system/mcu_system_handler.o
 .debug_frame   0x0000000000001188       0x68 emodules/drv_mcu_uart/core/handlers/media/mcu_tuner_handler.o
 .debug_frame   0x00000000000011f0       0x88 emodules/drv_mcu_uart/core/handlers/media/mcu_usb_handler.o
 .debug_frame   0x0000000000001278       0x70 emodules/drv_mcu_uart/core/handlers/bluetooth/mcu_bt_handler.o
 .debug_frame   0x00000000000012e8       0x80 emodules/drv_mcu_uart/core/handlers/sxm/mcu_sxm_handler.o
 .debug_frame   0x0000000000001368       0x78 emodules/drv_mcu_uart/core/handlers/settings/mcu_settings_handler.o
 .debug_frame   0x00000000000013e0       0x58 emodules/drv_mcu_uart/core/handlers/audio/mcu_audio_handler.o
 .debug_frame   0x0000000000001438       0x60 emodules/drv_mcu_uart/core/handlers/upgrade/mcu_upgrade_handler.o
 .debug_frame   0x0000000000001498       0x40 emodules/drv_mcu_uart/core/handlers/bypass/mcu_bypass_handler.o
 .debug_frame   0x00000000000014d8       0x60 emodules/drv_mcu_uart/core/handlers/misc/mcu_misc_handler.o
 .debug_frame   0x0000000000001538      0x1f0 emodules/drv_mcu_uart/core/handlers/protocol/mcu_new_protocol_handler.o
 .debug_frame   0x0000000000001728      0x218 emodules/drv_mcu_uart/core/handlers/protocol/system/mcu_system_category_handler.o
 .debug_frame   0x0000000000001940      0x3f8 emodules/drv_mcu_uart/core/handlers/protocol/audio/mcu_audio_category_handler.o
 .debug_frame   0x0000000000001d38      0x1a0 emodules/drv_mcu_uart/core/handlers/protocol/tuner/mcu_tuner_category_handler.o
 .debug_frame   0x0000000000001ed8      0x1d0 emodules/drv_mcu_uart/core/handlers/protocol/party/mcu_party_category_handler.o
 .debug_frame   0x00000000000020a8      0x180 emodules/drv_mcu_uart/core/handlers/protocol/usb/mcu_usb_category_handler.o
 .debug_frame   0x0000000000002228      0x160 emodules/drv_mcu_uart/core/handlers/protocol/settings/mcu_settings_category_handler.o
 .debug_frame   0x0000000000002388     0x3c60 ./elibrary/bin//libsyscall.a(syscall.o)
 .debug_frame   0x0000000000005fe8      0x5b8 ./elibrary/bin//libsyscall.a(syscall_fops.o)
 .debug_frame   0x00000000000065a0      0xfb0 ./elibrary/bin//libsyscall.a(syscall.o)
 .debug_frame   0x0000000000007550      0xa28 ./elibrary/bin//libminic.a(elibs_stdio.o)
 .debug_frame   0x0000000000007f78      0x1e0 ./elibrary/bin//libminic.a(elibs_stdlib.o)
 .debug_frame   0x0000000000008158      0x1e8 ./elibrary/bin//libminic.a(elibs_string.o)
 .debug_frame   0x0000000000008340       0xe0 ./elibrary/bin//libminic.a(elibs_sprintf.o)
 .debug_frame   0x0000000000008420       0x28 /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/rv64imafdcxtheadc/lp64d/libgcc.a(eqtf2.o)
 .debug_frame   0x0000000000008448       0x28 /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/rv64imafdcxtheadc/lp64d/libgcc.a(getf2.o)
 .debug_frame   0x0000000000008470       0x28 /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/rv64imafdcxtheadc/lp64d/libgcc.a(letf2.o)
 .debug_frame   0x0000000000008498       0x60 /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/rv64imafdcxtheadc/lp64d/libgcc.a(multf3.o)
 .debug_frame   0x00000000000084f8       0x50 /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/rv64imafdcxtheadc/lp64d/libgcc.a(subtf3.o)
 .debug_frame   0x0000000000008548       0x28 /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/rv64imafdcxtheadc/lp64d/libgcc.a(fixtfsi.o)
 .debug_frame   0x0000000000008570       0x40 /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/rv64imafdcxtheadc/lp64d/libgcc.a(floatsitf.o)
 .debug_frame   0x00000000000085b0       0x40 /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/rv64imafdcxtheadc/lp64d/libgcc.a(extenddftf2.o)
 .debug_frame   0x00000000000085f0       0x28 /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/rv64imafdcxtheadc/lp64d/libgcc.a(trunctfdf2.o)
 .debug_frame   0x0000000000008618       0x28 /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/rv64imafdcxtheadc/lp64d/libgcc.a(_clzsi2.o)

.debug_ranges   0x0000000000000000     0x10a0
 .debug_ranges  0x0000000000000000       0xb0 /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/rv64imafdcxtheadc/lp64d/libgcc.a(eqtf2.o)
 .debug_ranges  0x00000000000000b0       0xc0 /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/rv64imafdcxtheadc/lp64d/libgcc.a(getf2.o)
 .debug_ranges  0x0000000000000170       0xc0 /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/rv64imafdcxtheadc/lp64d/libgcc.a(letf2.o)
 .debug_ranges  0x0000000000000230      0x610 /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/rv64imafdcxtheadc/lp64d/libgcc.a(multf3.o)
 .debug_ranges  0x0000000000000840      0x5e0 /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/rv64imafdcxtheadc/lp64d/libgcc.a(subtf3.o)
 .debug_ranges  0x0000000000000e20       0xa0 /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/rv64imafdcxtheadc/lp64d/libgcc.a(fixtfsi.o)
 .debug_ranges  0x0000000000000ec0       0x70 /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/rv64imafdcxtheadc/lp64d/libgcc.a(floatsitf.o)
 .debug_ranges  0x0000000000000f30       0x30 /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/rv64imafdcxtheadc/lp64d/libgcc.a(extenddftf2.o)
 .debug_ranges  0x0000000000000f60      0x110 /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/rv64imafdcxtheadc/lp64d/libgcc.a(trunctfdf2.o)
 .debug_ranges  0x0000000000001070       0x30 /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/rv64imafdcxtheadc/lp64d/libgcc.a(_clzsi2.o)

Cross Reference Table

Symbol                                            File
AlbumArtBuf                                       emodules/drv_mcu_uart/core/mcu_interrupt_process.o
ArtworkBuf                                        emodules/drv_mcu_uart/core/mcu_interrupt_process.o
                                                  emodules/drv_mcu_uart/core/handlers/media/mcu_usb_handler.o
                                                  emodules/drv_mcu_uart/core/mcu_bypass_cmd_proc.o
ArtworkPkg                                        emodules/drv_mcu_uart/core/mcu_interrupt_process.o
                                                  emodules/drv_mcu_uart/core/handlers/media/mcu_usb_handler.o
                                                  emodules/drv_mcu_uart/core/mcu_bypass_cmd_proc.o
CE2INPUT                                          emodules/drv_mcu_uart/core/mcu_interrupt_process.o
CE2OUTPUT                                         emodules/drv_mcu_uart/core/mcu_interrupt_process.o
CE_init_io                                        emodules/drv_mcu_uart/core/mcu_interrupt_process.o
DRV_MCU_MClose                                    emodules/drv_mcu_uart/drv_mcu.o
                                                  emodules/drv_mcu_uart/magic.o
DRV_MCU_MExit                                     emodules/drv_mcu_uart/drv_mcu.o
                                                  emodules/drv_mcu_uart/magic.o
DRV_MCU_MInit                                     emodules/drv_mcu_uart/drv_mcu.o
                                                  emodules/drv_mcu_uart/magic.o
DRV_MCU_MIoctrl                                   emodules/drv_mcu_uart/drv_mcu.o
                                                  emodules/drv_mcu_uart/magic.o
DRV_MCU_MOpen                                     emodules/drv_mcu_uart/drv_mcu.o
                                                  emodules/drv_mcu_uart/magic.o
DRV_MCU_MRead                                     emodules/drv_mcu_uart/drv_mcu.o
                                                  emodules/drv_mcu_uart/magic.o
DRV_MCU_MWrite                                    emodules/drv_mcu_uart/drv_mcu.o
                                                  emodules/drv_mcu_uart/magic.o
DisableCE                                         emodules/drv_mcu_uart/core/mcu_interrupt_process.o
EnableCE                                          emodules/drv_mcu_uart/core/mcu_interrupt_process.o
GetCE_HL                                          emodules/drv_mcu_uart/core/mcu_interrupt_process.o
Init_Eq_Gain                                      emodules/drv_mcu_uart/core/mcu_aux_init_para.o
                                                  emodules/drv_mcu_uart/core/mcu_interrupt_process.o
McuKeyDevExit                                     emodules/drv_mcu_uart/dev_mcu.o
McuKeyDevInit                                     emodules/drv_mcu_uart/dev_mcu.o
RemotePkgHead                                     emodules/drv_mcu_uart/core/mcu_interrupt_process.o
SetCE_HL                                          emodules/drv_mcu_uart/core/mcu_interrupt_process.o
_Balloc                                           /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/../../../../riscv64-unknown-elf/lib/rv64imafdcxtheadc/lp64d/libc.a(lib_a-mprec.o)
                                                  /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/../../../../riscv64-unknown-elf/lib/rv64imafdcxtheadc/lp64d/libc.a(lib_a-ldtoa.o)
_Bfree                                            /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/../../../../riscv64-unknown-elf/lib/rv64imafdcxtheadc/lp64d/libc.a(lib_a-mprec.o)
                                                  /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/../../../../riscv64-unknown-elf/lib/rv64imafdcxtheadc/lp64d/libc.a(lib_a-ldtoa.o)
_PathLocale                                       /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/../../../../riscv64-unknown-elf/lib/rv64imafdcxtheadc/lp64d/libc.a(lib_a-locale.o)
__any_on                                          /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/../../../../riscv64-unknown-elf/lib/rv64imafdcxtheadc/lp64d/libc.a(lib_a-mprec.o)
__ascii_mbtowc                                    /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/../../../../riscv64-unknown-elf/lib/rv64imafdcxtheadc/lp64d/libc.a(lib_a-mbtowc_r.o)
                                                  /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/../../../../riscv64-unknown-elf/lib/rv64imafdcxtheadc/lp64d/libc.a(lib_a-locale.o)
__ascii_wctomb                                    /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/../../../../riscv64-unknown-elf/lib/rv64imafdcxtheadc/lp64d/libc.a(lib_a-wctomb_r.o)
                                                  /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/../../../../riscv64-unknown-elf/lib/rv64imafdcxtheadc/lp64d/libc.a(lib_a-locale.o)
__b2d                                             /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/../../../../riscv64-unknown-elf/lib/rv64imafdcxtheadc/lp64d/libc.a(lib_a-mprec.o)
__clz_tab                                         /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/rv64imafdcxtheadc/lp64d/libgcc.a(_clz.o)
                                                  /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/rv64imafdcxtheadc/lp64d/libgcc.a(_clzsi2.o)
__clzdi2                                          /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/rv64imafdcxtheadc/lp64d/libgcc.a(_clzsi2.o)
                                                  /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/rv64imafdcxtheadc/lp64d/libgcc.a(extenddftf2.o)
                                                  /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/rv64imafdcxtheadc/lp64d/libgcc.a(floatsitf.o)
                                                  /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/rv64imafdcxtheadc/lp64d/libgcc.a(subtf3.o)
                                                  /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/rv64imafdcxtheadc/lp64d/libgcc.a(multf3.o)
__copybits                                        /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/../../../../riscv64-unknown-elf/lib/rv64imafdcxtheadc/lp64d/libc.a(lib_a-mprec.o)
__d2b                                             /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/../../../../riscv64-unknown-elf/lib/rv64imafdcxtheadc/lp64d/libc.a(lib_a-mprec.o)
__eqtf2                                           /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/rv64imafdcxtheadc/lp64d/libgcc.a(eqtf2.o)
                                                  /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/../../../../riscv64-unknown-elf/lib/rv64imafdcxtheadc/lp64d/libc.a(lib_a-svfprintf.o)
                                                  /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/../../../../riscv64-unknown-elf/lib/rv64imafdcxtheadc/lp64d/libc.a(lib_a-vfprintf.o)
__extenddftf2                                     /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/rv64imafdcxtheadc/lp64d/libgcc.a(extenddftf2.o)
                                                  /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/../../../../riscv64-unknown-elf/lib/rv64imafdcxtheadc/lp64d/libc.a(lib_a-svfprintf.o)
                                                  /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/../../../../riscv64-unknown-elf/lib/rv64imafdcxtheadc/lp64d/libc.a(lib_a-vfprintf.o)
__fixtfsi                                         /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/rv64imafdcxtheadc/lp64d/libgcc.a(fixtfsi.o)
                                                  /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/../../../../riscv64-unknown-elf/lib/rv64imafdcxtheadc/lp64d/libc.a(lib_a-svfprintf.o)
                                                  /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/../../../../riscv64-unknown-elf/lib/rv64imafdcxtheadc/lp64d/libc.a(lib_a-vfprintf.o)
__floatsitf                                       /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/rv64imafdcxtheadc/lp64d/libgcc.a(floatsitf.o)
                                                  /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/../../../../riscv64-unknown-elf/lib/rv64imafdcxtheadc/lp64d/libc.a(lib_a-svfprintf.o)
                                                  /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/../../../../riscv64-unknown-elf/lib/rv64imafdcxtheadc/lp64d/libc.a(lib_a-vfprintf.o)
__fp_lock_all                                     /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/../../../../riscv64-unknown-elf/lib/rv64imafdcxtheadc/lp64d/libc.a(lib_a-findfp.o)
__fp_unlock_all                                   /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/../../../../riscv64-unknown-elf/lib/rv64imafdcxtheadc/lp64d/libc.a(lib_a-findfp.o)
__fputwc                                          /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/../../../../riscv64-unknown-elf/lib/rv64imafdcxtheadc/lp64d/libc.a(lib_a-fputwc.o)
__getf2                                           /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/rv64imafdcxtheadc/lp64d/libgcc.a(getf2.o)
__global_locale                                   /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/../../../../riscv64-unknown-elf/lib/rv64imafdcxtheadc/lp64d/libc.a(lib_a-locale.o)
                                                  /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/../../../../riscv64-unknown-elf/lib/rv64imafdcxtheadc/lp64d/libc.a(lib_a-wcrtomb.o)
                                                  /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/../../../../riscv64-unknown-elf/lib/rv64imafdcxtheadc/lp64d/libc.a(lib_a-wctomb_r.o)
                                                  /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/../../../../riscv64-unknown-elf/lib/rv64imafdcxtheadc/lp64d/libc.a(lib_a-mbtowc_r.o)
                                                  /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/../../../../riscv64-unknown-elf/lib/rv64imafdcxtheadc/lp64d/libc.a(lib_a-localeconv.o)
__gttf2                                           /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/rv64imafdcxtheadc/lp64d/libgcc.a(getf2.o)
                                                  /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/../../../../riscv64-unknown-elf/lib/rv64imafdcxtheadc/lp64d/libc.a(lib_a-svfprintf.o)
                                                  /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/../../../../riscv64-unknown-elf/lib/rv64imafdcxtheadc/lp64d/libc.a(lib_a-vfprintf.o)
__hi0bits                                         /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/../../../../riscv64-unknown-elf/lib/rv64imafdcxtheadc/lp64d/libc.a(lib_a-mprec.o)
__i2b                                             /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/../../../../riscv64-unknown-elf/lib/rv64imafdcxtheadc/lp64d/libc.a(lib_a-mprec.o)
__letf2                                           /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/rv64imafdcxtheadc/lp64d/libgcc.a(letf2.o)
__libc_init_array                                 ./elibrary/bin//libsyscall.a(syscall_fops.o)
__lo0bits                                         /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/../../../../riscv64-unknown-elf/lib/rv64imafdcxtheadc/lp64d/libc.a(lib_a-mprec.o)
__locale_ctype_ptr                                /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/../../../../riscv64-unknown-elf/lib/rv64imafdcxtheadc/lp64d/libc.a(lib_a-locale.o)
__locale_ctype_ptr_l                              /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/../../../../riscv64-unknown-elf/lib/rv64imafdcxtheadc/lp64d/libc.a(lib_a-locale.o)
__locale_mb_cur_max                               /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/../../../../riscv64-unknown-elf/lib/rv64imafdcxtheadc/lp64d/libc.a(lib_a-locale.o)
                                                  /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/../../../../riscv64-unknown-elf/lib/rv64imafdcxtheadc/lp64d/libc.a(lib_a-fputwc.o)
__localeconv_l                                    /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/../../../../riscv64-unknown-elf/lib/rv64imafdcxtheadc/lp64d/libc.a(lib_a-localeconv.o)
__lshift                                          /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/../../../../riscv64-unknown-elf/lib/rv64imafdcxtheadc/lp64d/libc.a(lib_a-mprec.o)
__lttf2                                           /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/rv64imafdcxtheadc/lp64d/libgcc.a(letf2.o)
                                                  /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/../../../../riscv64-unknown-elf/lib/rv64imafdcxtheadc/lp64d/libc.a(lib_a-svfprintf.o)
                                                  /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/../../../../riscv64-unknown-elf/lib/rv64imafdcxtheadc/lp64d/libc.a(lib_a-vfprintf.o)
__malloc_lock                                     ./elibrary/bin//libsyscall.a(syscall_fops.o)
__malloc_unlock                                   ./elibrary/bin//libsyscall.a(syscall_fops.o)
__mcmp                                            /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/../../../../riscv64-unknown-elf/lib/rv64imafdcxtheadc/lp64d/libc.a(lib_a-mprec.o)
__mdiff                                           /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/../../../../riscv64-unknown-elf/lib/rv64imafdcxtheadc/lp64d/libc.a(lib_a-mprec.o)
__mprec_bigtens                                   /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/../../../../riscv64-unknown-elf/lib/rv64imafdcxtheadc/lp64d/libc.a(lib_a-mprec.o)
__mprec_tens                                      /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/../../../../riscv64-unknown-elf/lib/rv64imafdcxtheadc/lp64d/libc.a(lib_a-mprec.o)
__mprec_tinytens                                  /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/../../../../riscv64-unknown-elf/lib/rv64imafdcxtheadc/lp64d/libc.a(lib_a-mprec.o)
__multadd                                         /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/../../../../riscv64-unknown-elf/lib/rv64imafdcxtheadc/lp64d/libc.a(lib_a-mprec.o)
__multf3                                          /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/rv64imafdcxtheadc/lp64d/libgcc.a(multf3.o)
                                                  /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/../../../../riscv64-unknown-elf/lib/rv64imafdcxtheadc/lp64d/libc.a(lib_a-svfprintf.o)
                                                  /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/../../../../riscv64-unknown-elf/lib/rv64imafdcxtheadc/lp64d/libc.a(lib_a-vfprintf.o)
__multiply                                        /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/../../../../riscv64-unknown-elf/lib/rv64imafdcxtheadc/lp64d/libc.a(lib_a-mprec.o)
__netf2                                           /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/rv64imafdcxtheadc/lp64d/libgcc.a(eqtf2.o)
                                                  /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/../../../../riscv64-unknown-elf/lib/rv64imafdcxtheadc/lp64d/libc.a(lib_a-svfprintf.o)
                                                  /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/../../../../riscv64-unknown-elf/lib/rv64imafdcxtheadc/lp64d/libc.a(lib_a-vfprintf.o)
__pow5mult                                        /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/../../../../riscv64-unknown-elf/lib/rv64imafdcxtheadc/lp64d/libc.a(lib_a-mprec.o)
__ratio                                           /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/../../../../riscv64-unknown-elf/lib/rv64imafdcxtheadc/lp64d/libc.a(lib_a-mprec.o)
__s2b                                             /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/../../../../riscv64-unknown-elf/lib/rv64imafdcxtheadc/lp64d/libc.a(lib_a-mprec.o)
__sclose                                          /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/../../../../riscv64-unknown-elf/lib/rv64imafdcxtheadc/lp64d/libc.a(lib_a-stdio.o)
                                                  /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/../../../../riscv64-unknown-elf/lib/rv64imafdcxtheadc/lp64d/libc.a(lib_a-findfp.o)
__seofread                                        /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/../../../../riscv64-unknown-elf/lib/rv64imafdcxtheadc/lp64d/libc.a(lib_a-stdio.o)
__sflush_r                                        /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/../../../../riscv64-unknown-elf/lib/rv64imafdcxtheadc/lp64d/libc.a(lib_a-fflush.o)
                                                  /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/../../../../riscv64-unknown-elf/lib/rv64imafdcxtheadc/lp64d/libc.a(lib_a-fclose.o)
__sfmoreglue                                      /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/../../../../riscv64-unknown-elf/lib/rv64imafdcxtheadc/lp64d/libc.a(lib_a-findfp.o)
__sfp                                             /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/../../../../riscv64-unknown-elf/lib/rv64imafdcxtheadc/lp64d/libc.a(lib_a-findfp.o)
__sfp_lock_acquire                                /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/../../../../riscv64-unknown-elf/lib/rv64imafdcxtheadc/lp64d/libc.a(lib_a-findfp.o)
                                                  /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/../../../../riscv64-unknown-elf/lib/rv64imafdcxtheadc/lp64d/libc.a(lib_a-fclose.o)
__sfp_lock_release                                /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/../../../../riscv64-unknown-elf/lib/rv64imafdcxtheadc/lp64d/libc.a(lib_a-findfp.o)
                                                  /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/../../../../riscv64-unknown-elf/lib/rv64imafdcxtheadc/lp64d/libc.a(lib_a-fclose.o)
__sfvwrite_r                                      /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/../../../../riscv64-unknown-elf/lib/rv64imafdcxtheadc/lp64d/libc.a(lib_a-fvwrite.o)
                                                  /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/../../../../riscv64-unknown-elf/lib/rv64imafdcxtheadc/lp64d/libc.a(lib_a-vfiprintf.o)
__sinit                                           /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/../../../../riscv64-unknown-elf/lib/rv64imafdcxtheadc/lp64d/libc.a(lib_a-findfp.o)
                                                  /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/../../../../riscv64-unknown-elf/lib/rv64imafdcxtheadc/lp64d/libc.a(lib_a-wbuf.o)
                                                  /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/../../../../riscv64-unknown-elf/lib/rv64imafdcxtheadc/lp64d/libc.a(lib_a-fputwc.o)
                                                  /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/../../../../riscv64-unknown-elf/lib/rv64imafdcxtheadc/lp64d/libc.a(lib_a-fclose.o)
                                                  /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/../../../../riscv64-unknown-elf/lib/rv64imafdcxtheadc/lp64d/libc.a(lib_a-vfiprintf.o)
                                                  /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/../../../../riscv64-unknown-elf/lib/rv64imafdcxtheadc/lp64d/libc.a(lib_a-fflush.o)
                                                  /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/../../../../riscv64-unknown-elf/lib/rv64imafdcxtheadc/lp64d/libc.a(lib_a-wsetup.o)
                                                  /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/../../../../riscv64-unknown-elf/lib/rv64imafdcxtheadc/lp64d/libc.a(lib_a-vfprintf.o)
__sinit_lock_acquire                              /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/../../../../riscv64-unknown-elf/lib/rv64imafdcxtheadc/lp64d/libc.a(lib_a-findfp.o)
__sinit_lock_release                              /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/../../../../riscv64-unknown-elf/lib/rv64imafdcxtheadc/lp64d/libc.a(lib_a-findfp.o)
__smakebuf_r                                      /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/../../../../riscv64-unknown-elf/lib/rv64imafdcxtheadc/lp64d/libc.a(lib_a-makebuf.o)
                                                  /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/../../../../riscv64-unknown-elf/lib/rv64imafdcxtheadc/lp64d/libc.a(lib_a-wsetup.o)
__sprint_r                                        /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/../../../../riscv64-unknown-elf/lib/rv64imafdcxtheadc/lp64d/libc.a(lib_a-vfiprintf.o)
                                                  /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/../../../../riscv64-unknown-elf/lib/rv64imafdcxtheadc/lp64d/libc.a(lib_a-vfprintf.o)
__sread                                           /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/../../../../riscv64-unknown-elf/lib/rv64imafdcxtheadc/lp64d/libc.a(lib_a-stdio.o)
                                                  /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/../../../../riscv64-unknown-elf/lib/rv64imafdcxtheadc/lp64d/libc.a(lib_a-findfp.o)
__sseek                                           /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/../../../../riscv64-unknown-elf/lib/rv64imafdcxtheadc/lp64d/libc.a(lib_a-stdio.o)
                                                  /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/../../../../riscv64-unknown-elf/lib/rv64imafdcxtheadc/lp64d/libc.a(lib_a-findfp.o)
__ssprint_r                                       /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/../../../../riscv64-unknown-elf/lib/rv64imafdcxtheadc/lp64d/libc.a(lib_a-svfiprintf.o)
                                                  /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/../../../../riscv64-unknown-elf/lib/rv64imafdcxtheadc/lp64d/libc.a(lib_a-svfprintf.o)
__subtf3                                          /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/rv64imafdcxtheadc/lp64d/libgcc.a(subtf3.o)
                                                  /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/../../../../riscv64-unknown-elf/lib/rv64imafdcxtheadc/lp64d/libc.a(lib_a-svfprintf.o)
                                                  /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/../../../../riscv64-unknown-elf/lib/rv64imafdcxtheadc/lp64d/libc.a(lib_a-vfprintf.o)
__swbuf                                           /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/../../../../riscv64-unknown-elf/lib/rv64imafdcxtheadc/lp64d/libc.a(lib_a-wbuf.o)
__swbuf_r                                         /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/../../../../riscv64-unknown-elf/lib/rv64imafdcxtheadc/lp64d/libc.a(lib_a-wbuf.o)
                                                  /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/../../../../riscv64-unknown-elf/lib/rv64imafdcxtheadc/lp64d/libc.a(lib_a-fputwc.o)
__swhatbuf_r                                      /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/../../../../riscv64-unknown-elf/lib/rv64imafdcxtheadc/lp64d/libc.a(lib_a-makebuf.o)
__swrite                                          /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/../../../../riscv64-unknown-elf/lib/rv64imafdcxtheadc/lp64d/libc.a(lib_a-stdio.o)
                                                  /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/../../../../riscv64-unknown-elf/lib/rv64imafdcxtheadc/lp64d/libc.a(lib_a-findfp.o)
__swsetup_r                                       /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/../../../../riscv64-unknown-elf/lib/rv64imafdcxtheadc/lp64d/libc.a(lib_a-wsetup.o)
                                                  /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/../../../../riscv64-unknown-elf/lib/rv64imafdcxtheadc/lp64d/libc.a(lib_a-wbuf.o)
                                                  /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/../../../../riscv64-unknown-elf/lib/rv64imafdcxtheadc/lp64d/libc.a(lib_a-fvwrite.o)
                                                  /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/../../../../riscv64-unknown-elf/lib/rv64imafdcxtheadc/lp64d/libc.a(lib_a-vfiprintf.o)
                                                  /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/../../../../riscv64-unknown-elf/lib/rv64imafdcxtheadc/lp64d/libc.a(lib_a-vfprintf.o)
__trunctfdf2                                      /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/rv64imafdcxtheadc/lp64d/libgcc.a(trunctfdf2.o)
                                                  /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/../../../../riscv64-unknown-elf/lib/rv64imafdcxtheadc/lp64d/libc.a(lib_a-svfprintf.o)
                                                  /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/../../../../riscv64-unknown-elf/lib/rv64imafdcxtheadc/lp64d/libc.a(lib_a-vfprintf.o)
__ulp                                             /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/../../../../riscv64-unknown-elf/lib/rv64imafdcxtheadc/lp64d/libc.a(lib_a-mprec.o)
_calloc_r                                         ./elibrary/bin//libsyscall.a(syscall_fops.o)
                                                  /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/../../../../riscv64-unknown-elf/lib/rv64imafdcxtheadc/lp64d/libc.a(lib_a-mprec.o)
_cleanup                                          /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/../../../../riscv64-unknown-elf/lib/rv64imafdcxtheadc/lp64d/libc.a(lib_a-findfp.o)
_cleanup_r                                        /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/../../../../riscv64-unknown-elf/lib/rv64imafdcxtheadc/lp64d/libc.a(lib_a-findfp.o)
                                                  /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/../../../../riscv64-unknown-elf/lib/rv64imafdcxtheadc/lp64d/libc.a(lib_a-makebuf.o)
_close_r                                          ./elibrary/bin//libsyscall.a(syscall_fops.o)
                                                  /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/../../../../riscv64-unknown-elf/lib/rv64imafdcxtheadc/lp64d/libc.a(lib_a-stdio.o)
                                                  /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/../../../../riscv64-unknown-elf/lib/rv64imafdcxtheadc/lp64d/libc.a(lib_a-sysclose.o)
_ctype_                                           /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/../../../../riscv64-unknown-elf/lib/rv64imafdcxtheadc/lp64d/libc.a(lib_a-ctype_.o)
                                                  /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/../../../../riscv64-unknown-elf/lib/rv64imafdcxtheadc/lp64d/libc.a(lib_a-locale.o)
_exit                                             ./elibrary/bin//libminic.a(elibs_stdlib.o)
_fclose_r                                         /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/../../../../riscv64-unknown-elf/lib/rv64imafdcxtheadc/lp64d/libc.a(lib_a-fclose.o)
                                                  /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/../../../../riscv64-unknown-elf/lib/rv64imafdcxtheadc/lp64d/libc.a(lib_a-findfp.o)
_fflush_r                                         /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/../../../../riscv64-unknown-elf/lib/rv64imafdcxtheadc/lp64d/libc.a(lib_a-fflush.o)
                                                  /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/../../../../riscv64-unknown-elf/lib/rv64imafdcxtheadc/lp64d/libc.a(lib_a-wbuf.o)
                                                  /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/../../../../riscv64-unknown-elf/lib/rv64imafdcxtheadc/lp64d/libc.a(lib_a-fvwrite.o)
                                                  /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/../../../../riscv64-unknown-elf/lib/rv64imafdcxtheadc/lp64d/libc.a(lib_a-vfiprintf.o)
                                                  /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/../../../../riscv64-unknown-elf/lib/rv64imafdcxtheadc/lp64d/libc.a(lib_a-vfprintf.o)
_fputwc_r                                         /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/../../../../riscv64-unknown-elf/lib/rv64imafdcxtheadc/lp64d/libc.a(lib_a-fputwc.o)
                                                  /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/../../../../riscv64-unknown-elf/lib/rv64imafdcxtheadc/lp64d/libc.a(lib_a-vfiprintf.o)
_free_r                                           ./elibrary/bin//libsyscall.a(syscall_fops.o)
                                                  /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/../../../../riscv64-unknown-elf/lib/rv64imafdcxtheadc/lp64d/libc.a(lib_a-svfiprintf.o)
                                                  /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/../../../../riscv64-unknown-elf/lib/rv64imafdcxtheadc/lp64d/libc.a(lib_a-fvwrite.o)
                                                  /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/../../../../riscv64-unknown-elf/lib/rv64imafdcxtheadc/lp64d/libc.a(lib_a-fclose.o)
                                                  /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/../../../../riscv64-unknown-elf/lib/rv64imafdcxtheadc/lp64d/libc.a(lib_a-svfprintf.o)
                                                  /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/../../../../riscv64-unknown-elf/lib/rv64imafdcxtheadc/lp64d/libc.a(lib_a-fflush.o)
                                                  /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/../../../../riscv64-unknown-elf/lib/rv64imafdcxtheadc/lp64d/libc.a(lib_a-wsetup.o)
                                                  /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/../../../../riscv64-unknown-elf/lib/rv64imafdcxtheadc/lp64d/libc.a(lib_a-vfprintf.o)
_fstat_r                                          ./elibrary/bin//libsyscall.a(syscall_fops.o)
                                                  /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/../../../../riscv64-unknown-elf/lib/rv64imafdcxtheadc/lp64d/libc.a(lib_a-makebuf.o)
_fwalk                                            /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/../../../../riscv64-unknown-elf/lib/rv64imafdcxtheadc/lp64d/libc.a(lib_a-fwalk.o)
                                                  /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/../../../../riscv64-unknown-elf/lib/rv64imafdcxtheadc/lp64d/libc.a(lib_a-findfp.o)
_fwalk_reent                                      /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/../../../../riscv64-unknown-elf/lib/rv64imafdcxtheadc/lp64d/libc.a(lib_a-fwalk.o)
                                                  /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/../../../../riscv64-unknown-elf/lib/rv64imafdcxtheadc/lp64d/libc.a(lib_a-findfp.o)
                                                  /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/../../../../riscv64-unknown-elf/lib/rv64imafdcxtheadc/lp64d/libc.a(lib_a-fflush.o)
_getpid                                           ./elibrary/bin//libminic.a(elibs_stdlib.o)
_gettimeofday_r                                   ./elibrary/bin//libsyscall.a(syscall_fops.o)
_global_impure_ptr                                /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/../../../../riscv64-unknown-elf/lib/rv64imafdcxtheadc/lp64d/libc.a(lib_a-impure.o)
                                                  /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/../../../../riscv64-unknown-elf/lib/rv64imafdcxtheadc/lp64d/libc.a(lib_a-findfp.o)
                                                  /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/../../../../riscv64-unknown-elf/lib/rv64imafdcxtheadc/lp64d/libc.a(lib_a-fflush.o)
_impure_ptr                                       /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/../../../../riscv64-unknown-elf/lib/rv64imafdcxtheadc/lp64d/libc.a(lib_a-impure.o)
                                                  /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/../../../../riscv64-unknown-elf/lib/rv64imafdcxtheadc/lp64d/libc.a(lib_a-wcrtomb.o)
                                                  /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/../../../../riscv64-unknown-elf/lib/rv64imafdcxtheadc/lp64d/libc.a(lib_a-wbuf.o)
                                                  /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/../../../../riscv64-unknown-elf/lib/rv64imafdcxtheadc/lp64d/libc.a(lib_a-fputwc.o)
                                                  /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/../../../../riscv64-unknown-elf/lib/rv64imafdcxtheadc/lp64d/libc.a(lib_a-fclose.o)
                                                  /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/../../../../riscv64-unknown-elf/lib/rv64imafdcxtheadc/lp64d/libc.a(lib_a-wctomb_r.o)
                                                  /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/../../../../riscv64-unknown-elf/lib/rv64imafdcxtheadc/lp64d/libc.a(lib_a-vfiprintf.o)
                                                  /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/../../../../riscv64-unknown-elf/lib/rv64imafdcxtheadc/lp64d/libc.a(lib_a-sprintf.o)
                                                  /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/../../../../riscv64-unknown-elf/lib/rv64imafdcxtheadc/lp64d/libc.a(lib_a-mbtowc_r.o)
                                                  /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/../../../../riscv64-unknown-elf/lib/rv64imafdcxtheadc/lp64d/libc.a(lib_a-locale.o)
                                                  /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/../../../../riscv64-unknown-elf/lib/rv64imafdcxtheadc/lp64d/libc.a(lib_a-localeconv.o)
                                                  /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/../../../../riscv64-unknown-elf/lib/rv64imafdcxtheadc/lp64d/libc.a(lib_a-findfp.o)
                                                  /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/../../../../riscv64-unknown-elf/lib/rv64imafdcxtheadc/lp64d/libc.a(lib_a-fflush.o)
                                                  /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/../../../../riscv64-unknown-elf/lib/rv64imafdcxtheadc/lp64d/libc.a(lib_a-wsetup.o)
                                                  /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/../../../../riscv64-unknown-elf/lib/rv64imafdcxtheadc/lp64d/libc.a(lib_a-vfprintf.o)
                                                  /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/../../../../riscv64-unknown-elf/lib/rv64imafdcxtheadc/lp64d/libc.a(lib_a-sysopen.o)
                                                  /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/../../../../riscv64-unknown-elf/lib/rv64imafdcxtheadc/lp64d/libc.a(lib_a-sysclose.o)
                                                  /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/../../../../riscv64-unknown-elf/lib/rv64imafdcxtheadc/lp64d/libc.a(lib_a-printf.o)
_isatty                                           ./elibrary/bin//libsyscall.a(syscall_fops.o)
_isatty_r                                         ./elibrary/bin//libsyscall.a(syscall_fops.o)
                                                  /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/../../../../riscv64-unknown-elf/lib/rv64imafdcxtheadc/lp64d/libc.a(lib_a-makebuf.o)
_kill                                             ./elibrary/bin//libminic.a(elibs_stdlib.o)
_ldcheck                                          /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/../../../../riscv64-unknown-elf/lib/rv64imafdcxtheadc/lp64d/libc.a(lib_a-ldtoa.o)
                                                  /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/../../../../riscv64-unknown-elf/lib/rv64imafdcxtheadc/lp64d/libc.a(lib_a-svfprintf.o)
                                                  /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/../../../../riscv64-unknown-elf/lib/rv64imafdcxtheadc/lp64d/libc.a(lib_a-vfprintf.o)
_ldtoa_r                                          /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/../../../../riscv64-unknown-elf/lib/rv64imafdcxtheadc/lp64d/libc.a(lib_a-ldtoa.o)
                                                  /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/../../../../riscv64-unknown-elf/lib/rv64imafdcxtheadc/lp64d/libc.a(lib_a-svfprintf.o)
                                                  /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/../../../../riscv64-unknown-elf/lib/rv64imafdcxtheadc/lp64d/libc.a(lib_a-vfprintf.o)
_localeconv_r                                     /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/../../../../riscv64-unknown-elf/lib/rv64imafdcxtheadc/lp64d/libc.a(lib_a-localeconv.o)
                                                  /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/../../../../riscv64-unknown-elf/lib/rv64imafdcxtheadc/lp64d/libc.a(lib_a-svfiprintf.o)
                                                  /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/../../../../riscv64-unknown-elf/lib/rv64imafdcxtheadc/lp64d/libc.a(lib_a-vfiprintf.o)
                                                  /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/../../../../riscv64-unknown-elf/lib/rv64imafdcxtheadc/lp64d/libc.a(lib_a-svfprintf.o)
                                                  /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/../../../../riscv64-unknown-elf/lib/rv64imafdcxtheadc/lp64d/libc.a(lib_a-vfprintf.o)
_lseek_r                                          ./elibrary/bin//libsyscall.a(syscall_fops.o)
                                                  /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/../../../../riscv64-unknown-elf/lib/rv64imafdcxtheadc/lp64d/libc.a(lib_a-stdio.o)
_malloc_r                                         ./elibrary/bin//libsyscall.a(syscall_fops.o)
                                                  /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/../../../../riscv64-unknown-elf/lib/rv64imafdcxtheadc/lp64d/libc.a(lib_a-svfiprintf.o)
                                                  /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/../../../../riscv64-unknown-elf/lib/rv64imafdcxtheadc/lp64d/libc.a(lib_a-fvwrite.o)
                                                  /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/../../../../riscv64-unknown-elf/lib/rv64imafdcxtheadc/lp64d/libc.a(lib_a-svfprintf.o)
                                                  /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/../../../../riscv64-unknown-elf/lib/rv64imafdcxtheadc/lp64d/libc.a(lib_a-makebuf.o)
                                                  /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/../../../../riscv64-unknown-elf/lib/rv64imafdcxtheadc/lp64d/libc.a(lib_a-findfp.o)
                                                  /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/../../../../riscv64-unknown-elf/lib/rv64imafdcxtheadc/lp64d/libc.a(lib_a-vfprintf.o)
_mbtowc_r                                         /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/../../../../riscv64-unknown-elf/lib/rv64imafdcxtheadc/lp64d/libc.a(lib_a-mbtowc_r.o)
_mkdir_r                                          ./elibrary/bin//libsyscall.a(syscall_fops.o)
_mprec_log10                                      /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/../../../../riscv64-unknown-elf/lib/rv64imafdcxtheadc/lp64d/libc.a(lib_a-mprec.o)
_open_r                                           ./elibrary/bin//libsyscall.a(syscall_fops.o)
                                                  /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/../../../../riscv64-unknown-elf/lib/rv64imafdcxtheadc/lp64d/libc.a(lib_a-sysopen.o)
_printf_r                                         /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/../../../../riscv64-unknown-elf/lib/rv64imafdcxtheadc/lp64d/libc.a(lib_a-printf.o)
_read_r                                           ./elibrary/bin//libsyscall.a(syscall_fops.o)
                                                  /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/../../../../riscv64-unknown-elf/lib/rv64imafdcxtheadc/lp64d/libc.a(lib_a-stdio.o)
_realloc_r                                        ./elibrary/bin//libsyscall.a(syscall_fops.o)
                                                  /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/../../../../riscv64-unknown-elf/lib/rv64imafdcxtheadc/lp64d/libc.a(lib_a-svfiprintf.o)
                                                  /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/../../../../riscv64-unknown-elf/lib/rv64imafdcxtheadc/lp64d/libc.a(lib_a-fvwrite.o)
_rename_r                                         ./elibrary/bin//libsyscall.a(syscall_fops.o)
_sbrk                                             ./elibrary/bin//libminic.a(elibs_stdlib.o)
_setlocale_r                                      /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/../../../../riscv64-unknown-elf/lib/rv64imafdcxtheadc/lp64d/libc.a(lib_a-locale.o)
_sprintf_r                                        /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/../../../../riscv64-unknown-elf/lib/rv64imafdcxtheadc/lp64d/libc.a(lib_a-sprintf.o)
_stat_r                                           ./elibrary/bin//libsyscall.a(syscall_fops.o)
_svfiprintf_r                                     /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/../../../../riscv64-unknown-elf/lib/rv64imafdcxtheadc/lp64d/libc.a(lib_a-svfiprintf.o)
_svfprintf_r                                      /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/../../../../riscv64-unknown-elf/lib/rv64imafdcxtheadc/lp64d/libc.a(lib_a-svfprintf.o)
                                                  /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/../../../../riscv64-unknown-elf/lib/rv64imafdcxtheadc/lp64d/libc.a(lib_a-sprintf.o)
_system                                           ./elibrary/bin//libsyscall.a(syscall_fops.o)
_unlink_r                                         ./elibrary/bin//libsyscall.a(syscall_fops.o)
_vfiprintf_r                                      /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/../../../../riscv64-unknown-elf/lib/rv64imafdcxtheadc/lp64d/libc.a(lib_a-vfiprintf.o)
_vfprintf_r                                       /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/../../../../riscv64-unknown-elf/lib/rv64imafdcxtheadc/lp64d/libc.a(lib_a-vfprintf.o)
                                                  /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/../../../../riscv64-unknown-elf/lib/rv64imafdcxtheadc/lp64d/libc.a(lib_a-printf.o)
_wcrtomb_r                                        /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/../../../../riscv64-unknown-elf/lib/rv64imafdcxtheadc/lp64d/libc.a(lib_a-wcrtomb.o)
                                                  /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/../../../../riscv64-unknown-elf/lib/rv64imafdcxtheadc/lp64d/libc.a(lib_a-fputwc.o)
_wctomb_r                                         /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/../../../../riscv64-unknown-elf/lib/rv64imafdcxtheadc/lp64d/libc.a(lib_a-wctomb_r.o)
_write                                            ./elibrary/bin//libminic.a(elibs_stdlib.o)
_write_r                                          ./elibrary/bin//libsyscall.a(syscall_fops.o)
                                                  /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/../../../../riscv64-unknown-elf/lib/rv64imafdcxtheadc/lp64d/libc.a(lib_a-stdio.o)
access                                            ./elibrary/bin//libsyscall.a(syscall_fops.o)
all_app_para                                      emodules/drv_mcu_uart/core/mcu_interrupt_process.o
                                                  emodules/drv_mcu_uart/core/handlers/protocol/settings/mcu_settings_category_handler.o
                                                  emodules/drv_mcu_uart/core/handlers/protocol/usb/mcu_usb_category_handler.o
                                                  emodules/drv_mcu_uart/core/handlers/protocol/party/mcu_party_category_handler.o
                                                  emodules/drv_mcu_uart/core/handlers/protocol/tuner/mcu_tuner_category_handler.o
                                                  emodules/drv_mcu_uart/core/handlers/protocol/audio/mcu_audio_category_handler.o
                                                  emodules/drv_mcu_uart/core/handlers/protocol/system/mcu_system_category_handler.o
                                                  emodules/drv_mcu_uart/core/handlers/misc/mcu_misc_handler.o
                                                  emodules/drv_mcu_uart/core/handlers/upgrade/mcu_upgrade_handler.o
                                                  emodules/drv_mcu_uart/core/handlers/audio/mcu_audio_handler.o
                                                  emodules/drv_mcu_uart/core/handlers/settings/mcu_settings_handler.o
                                                  emodules/drv_mcu_uart/core/handlers/sxm/mcu_sxm_handler.o
                                                  emodules/drv_mcu_uart/core/handlers/bluetooth/mcu_bt_handler.o
                                                  emodules/drv_mcu_uart/core/handlers/media/mcu_usb_handler.o
                                                  emodules/drv_mcu_uart/core/handlers/media/mcu_tuner_handler.o
                                                  emodules/drv_mcu_uart/core/handlers/system/mcu_system_handler.o
                                                  emodules/drv_mcu_uart/core/mcu_cmd_dispatcher.o
                                                  emodules/drv_mcu_uart/core/mcu_update_proc.o
                                                  emodules/drv_mcu_uart/core/mcu_bypass_cmd_proc.o
am_get_status                                     emodules/drv_mcu_uart/core/handlers/protocol/tuner/mcu_tuner_category_handler.o
am_main_handler                                   emodules/drv_mcu_uart/core/handlers/protocol/tuner/mcu_tuner_category_handler.o
audio_get_balance                                 emodules/drv_mcu_uart/core/handlers/protocol/audio/mcu_audio_category_handler.o
audio_get_crossover                               emodules/drv_mcu_uart/core/handlers/protocol/audio/mcu_audio_category_handler.o
audio_get_eq_type_gains                           emodules/drv_mcu_uart/core/handlers/protocol/audio/mcu_audio_category_handler.o
audio_get_equalizer                               emodules/drv_mcu_uart/core/handlers/protocol/audio/mcu_audio_category_handler.o
audio_get_fade                                    emodules/drv_mcu_uart/core/handlers/protocol/audio/mcu_audio_category_handler.o
audio_get_main_volume                             emodules/drv_mcu_uart/core/handlers/protocol/audio/mcu_audio_category_handler.o
audio_get_mute_state                              emodules/drv_mcu_uart/core/handlers/protocol/audio/mcu_audio_category_handler.o
audio_get_source_gain                             emodules/drv_mcu_uart/core/handlers/protocol/audio/mcu_audio_category_handler.o
audio_get_status                                  emodules/drv_mcu_uart/core/handlers/protocol/audio/mcu_audio_category_handler.o
audio_get_subwoofer_volume                        emodules/drv_mcu_uart/core/handlers/protocol/audio/mcu_audio_category_handler.o
audio_get_zone_volume                             emodules/drv_mcu_uart/core/handlers/protocol/audio/mcu_audio_category_handler.o
audio_get_zones_enable                            emodules/drv_mcu_uart/core/handlers/protocol/audio/mcu_audio_category_handler.o
audio_main_handler                                emodules/drv_mcu_uart/core/handlers/protocol/audio/mcu_audio_category_handler.o
audio_reset_all_settings                          emodules/drv_mcu_uart/core/handlers/protocol/audio/mcu_audio_category_handler.o
audio_restore_default_settings                    emodules/drv_mcu_uart/core/handlers/protocol/audio/mcu_audio_category_handler.o
audio_set_balance                                 emodules/drv_mcu_uart/core/handlers/protocol/audio/mcu_audio_category_handler.o
audio_set_crossover                               emodules/drv_mcu_uart/core/handlers/protocol/audio/mcu_audio_category_handler.o
audio_set_eq_type_gains                           emodules/drv_mcu_uart/core/handlers/protocol/audio/mcu_audio_category_handler.o
audio_set_equalizer                               emodules/drv_mcu_uart/core/handlers/protocol/audio/mcu_audio_category_handler.o
audio_set_fade                                    emodules/drv_mcu_uart/core/handlers/protocol/audio/mcu_audio_category_handler.o
audio_set_main_volume                             emodules/drv_mcu_uart/core/handlers/protocol/audio/mcu_audio_category_handler.o
audio_set_mute_state                              emodules/drv_mcu_uart/core/handlers/protocol/audio/mcu_audio_category_handler.o
audio_set_source_gain                             emodules/drv_mcu_uart/core/handlers/protocol/audio/mcu_audio_category_handler.o
audio_set_subwoofer_phase                         emodules/drv_mcu_uart/core/handlers/protocol/audio/mcu_audio_category_handler.o
audio_set_subwoofer_switch                        emodules/drv_mcu_uart/core/handlers/protocol/audio/mcu_audio_category_handler.o
audio_set_subwoofer_volume                        emodules/drv_mcu_uart/core/handlers/protocol/audio/mcu_audio_category_handler.o
audio_set_zone_volume                             emodules/drv_mcu_uart/core/handlers/protocol/audio/mcu_audio_category_handler.o
audio_set_zones_enable                            emodules/drv_mcu_uart/core/handlers/protocol/audio/mcu_audio_category_handler.o
audio_switch_eq_type                              emodules/drv_mcu_uart/core/handlers/protocol/audio/mcu_audio_category_handler.o
backtrace                                         ./elibrary/bin//libsyscall.a(syscall.o)
bypass_cmd_proc                                   emodules/drv_mcu_uart/core/mcu_bypass_cmd_proc.o
                                                  emodules/drv_mcu_uart/core/handlers/bypass/mcu_bypass_handler.o
calc_crc16                                        emodules/drv_mcu_uart/core/mcu_interrupt_process.o
check_iap_exist                                   emodules/drv_mcu_uart/core/mcu_interrupt_process.o
                                                  emodules/drv_mcu_uart/core/handlers/media/mcu_usb_handler.o
check_memzero                                     emodules/drv_mcu_uart/core/mcu_interrupt_process.o
check_remote_device_match                         emodules/drv_mcu_uart/core/mcu_interrupt_process.o
clock_getres                                      ./elibrary/bin//libsyscall.a(syscall.o)
clock_gettime                                     ./elibrary/bin//libsyscall.a(syscall.o)
clock_settime                                     ./elibrary/bin//libsyscall.a(syscall.o)
close                                             /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/../../../../riscv64-unknown-elf/lib/rv64imafdcxtheadc/lp64d/libc.a(lib_a-sysclose.o)
                                                  emodules/drv_mcu_uart/core/mcu_interrupt_process.o
closedir                                          ./elibrary/bin//libsyscall.a(syscall_fops.o)
cmd_queue                                         emodules/drv_mcu_uart/core/mcu_interrupt_process.o
dup                                               ./elibrary/bin//libsyscall.a(syscall_fops.o)
dup2                                              ./elibrary/bin//libsyscall.a(syscall_fops.o)
eLIBs_DirEnt2Attr                                 ./elibrary/bin//libminic.a(elibs_stdio.o)
eLIBs_DirEnt2Name                                 ./elibrary/bin//libminic.a(elibs_stdio.o)
eLIBs_DirEnt2Size                                 ./elibrary/bin//libminic.a(elibs_stdio.o)
eLIBs_DirEnt2Time                                 ./elibrary/bin//libminic.a(elibs_stdio.o)
eLIBs_GetDate                                     ./elibrary/bin//libminic.a(elibs_stdio.o)
eLIBs_GetDevName                                  ./elibrary/bin//libminic.a(elibs_stdio.o)
eLIBs_GetDevParts                                 ./elibrary/bin//libminic.a(elibs_stdio.o)
eLIBs_GetDirATime                                 ./elibrary/bin//libminic.a(elibs_stdio.o)
eLIBs_GetDirCTime                                 ./elibrary/bin//libminic.a(elibs_stdio.o)
eLIBs_GetDirMTime                                 ./elibrary/bin//libminic.a(elibs_stdio.o)
eLIBs_GetDirSize                                  ./elibrary/bin//libminic.a(elibs_stdio.o)
eLIBs_GetDiskArray                                ./elibrary/bin//libminic.a(elibs_stdio.o)
eLIBs_GetFSAttribute                              ./elibrary/bin//libminic.a(elibs_stdio.o)
eLIBs_GetFSCharset                                ./elibrary/bin//libminic.a(elibs_stdio.o)
eLIBs_GetFSName                                   ./elibrary/bin//libminic.a(elibs_stdio.o)
eLIBs_GetFSNameLen                                ./elibrary/bin//libminic.a(elibs_stdio.o)
eLIBs_GetFileATime                                ./elibrary/bin//libminic.a(elibs_stdio.o)
eLIBs_GetFileAttributes                           ./elibrary/bin//libminic.a(elibs_stdio.o)
eLIBs_GetFileCTime                                ./elibrary/bin//libminic.a(elibs_stdio.o)
eLIBs_GetFileMTime                                ./elibrary/bin//libminic.a(elibs_stdio.o)
eLIBs_GetFileSize                                 ./elibrary/bin//libminic.a(elibs_stdio.o)
eLIBs_GetNumFiles                                 ./elibrary/bin//libminic.a(elibs_stdio.o)
eLIBs_GetNumSubItems                              ./elibrary/bin//libminic.a(elibs_stdio.o)
eLIBs_GetPTName                                   ./elibrary/bin//libminic.a(elibs_stdio.o)
eLIBs_GetTime                                     ./elibrary/bin//libminic.a(elibs_stdio.o)
eLIBs_GetVolClustSize                             ./elibrary/bin//libminic.a(elibs_stdio.o)
eLIBs_GetVolFSpace                                ./elibrary/bin//libminic.a(elibs_stdio.o)
eLIBs_GetVolName                                  ./elibrary/bin//libminic.a(elibs_stdio.o)
eLIBs_GetVolNameCharset                           ./elibrary/bin//libminic.a(elibs_stdio.o)
eLIBs_GetVolNameLen                               ./elibrary/bin//libminic.a(elibs_stdio.o)
eLIBs_GetVolTSpace                                ./elibrary/bin//libminic.a(elibs_stdio.o)
eLIBs_IsPartFormated                              ./elibrary/bin//libminic.a(elibs_stdio.o)
eLIBs_SetFileAttributes                           ./elibrary/bin//libminic.a(elibs_stdio.o)
eLIBs_atof                                        ./elibrary/bin//libminic.a(elibs_stdlib.o)
eLIBs_atoi                                        ./elibrary/bin//libminic.a(elibs_stdlib.o)
eLIBs_closedir                                    ./elibrary/bin//libminic.a(elibs_stdio.o)
eLIBs_disklock                                    ./elibrary/bin//libminic.a(elibs_stdio.o)
eLIBs_diskunlock                                  ./elibrary/bin//libminic.a(elibs_stdio.o)
eLIBs_fclose                                      ./elibrary/bin//libminic.a(elibs_stdio.o)
                                                  emodules/drv_mcu_uart/core/mcu_interrupt_process.o
eLIBs_fd2file                                     ./elibrary/bin//libminic.a(elibs_stdio.o)
eLIBs_feof                                        ./elibrary/bin//libminic.a(elibs_stdio.o)
eLIBs_ferrclr                                     ./elibrary/bin//libminic.a(elibs_stdio.o)
eLIBs_ferror                                      ./elibrary/bin//libminic.a(elibs_stdio.o)
eLIBs_fgetc                                       ./elibrary/bin//libminic.a(elibs_stdio.o)
eLIBs_fgets                                       ./elibrary/bin//libminic.a(elibs_stdio.o)
eLIBs_file2fd                                     ./elibrary/bin//libminic.a(elibs_stdio.o)
eLIBs_fioctrl                                     ./elibrary/bin//libminic.a(elibs_stdio.o)
eLIBs_fllseek                                     ./elibrary/bin//libminic.a(elibs_stdio.o)
eLIBs_flltell                                     ./elibrary/bin//libminic.a(elibs_stdio.o)
eLIBs_fopen                                       ./elibrary/bin//libminic.a(elibs_stdio.o)
                                                  emodules/drv_mcu_uart/core/mcu_interrupt_process.o
eLIBs_format                                      ./elibrary/bin//libminic.a(elibs_stdio.o)
eLIBs_fputc                                       ./elibrary/bin//libminic.a(elibs_stdio.o)
eLIBs_fputs                                       ./elibrary/bin//libminic.a(elibs_stdio.o)
eLIBs_fread                                       ./elibrary/bin//libminic.a(elibs_stdio.o)
                                                  emodules/drv_mcu_uart/core/mcu_interrupt_process.o
eLIBs_free                                        ./elibrary/bin//libminic.a(elibs_stdlib.o)
                                                  emodules/drv_mcu_uart/dev_mcu.o
eLIBs_fseek                                       ./elibrary/bin//libminic.a(elibs_stdio.o)
eLIBs_fstat                                       ./elibrary/bin//libminic.a(elibs_stdio.o)
eLIBs_fsync                                       ./elibrary/bin//libminic.a(elibs_stdio.o)
eLIBs_ftell                                       ./elibrary/bin//libminic.a(elibs_stdio.o)
eLIBs_ftruncate                                   ./elibrary/bin//libminic.a(elibs_stdio.o)
eLIBs_fwrite                                      ./elibrary/bin//libminic.a(elibs_stdio.o)
                                                  emodules/drv_mcu_uart/core/mcu_interrupt_process.o
eLIBs_int2str_dec                                 ./elibrary/bin//libminic.a(elibs_stdlib.o)
eLIBs_int2str_hex                                 ./elibrary/bin//libminic.a(elibs_stdlib.o)
eLIBs_isspace                                     ./elibrary/bin//libminic.a(elibs_stdlib.o)
eLIBs_malloc                                      ./elibrary/bin//libminic.a(elibs_stdlib.o)
                                                  emodules/drv_mcu_uart/dev_mcu.o
eLIBs_memclr                                      ./elibrary/bin//libminic.a(elibs_string.o)
eLIBs_memcmp                                      ./elibrary/bin//libminic.a(elibs_string.o)
                                                  emodules/drv_mcu_uart/core/handlers/media/mcu_usb_handler.o
eLIBs_memcpy                                      ./elibrary/bin//libminic.a(elibs_string.o)
                                                  ./elibrary/bin//libminic.a(elibs_stdio.o)
                                                  emodules/drv_mcu_uart/core/handlers/protocol/settings/mcu_settings_category_handler.o
                                                  emodules/drv_mcu_uart/core/handlers/protocol/usb/mcu_usb_category_handler.o
                                                  emodules/drv_mcu_uart/core/handlers/protocol/party/mcu_party_category_handler.o
                                                  emodules/drv_mcu_uart/core/handlers/protocol/tuner/mcu_tuner_category_handler.o
                                                  emodules/drv_mcu_uart/core/handlers/protocol/system/mcu_system_category_handler.o
                                                  emodules/drv_mcu_uart/core/handlers/protocol/mcu_new_protocol_handler.o
                                                  emodules/drv_mcu_uart/core/handlers/misc/mcu_misc_handler.o
                                                  emodules/drv_mcu_uart/core/handlers/settings/mcu_settings_handler.o
                                                  emodules/drv_mcu_uart/core/handlers/sxm/mcu_sxm_handler.o
                                                  emodules/drv_mcu_uart/core/handlers/bluetooth/mcu_bt_handler.o
                                                  emodules/drv_mcu_uart/core/handlers/media/mcu_usb_handler.o
                                                  emodules/drv_mcu_uart/core/handlers/media/mcu_tuner_handler.o
                                                  emodules/drv_mcu_uart/core/handlers/system/mcu_system_handler.o
                                                  emodules/drv_mcu_uart/core/mcu_cmd_dispatcher.o
                                                  emodules/drv_mcu_uart/core/mcu_bypass_cmd_proc.o
                                                  emodules/drv_mcu_uart/core/mcu_interrupt_process.o
eLIBs_memmove                                     ./elibrary/bin//libminic.a(elibs_string.o)
eLIBs_memset                                      ./elibrary/bin//libminic.a(elibs_string.o)
                                                  emodules/drv_mcu_uart/core/handlers/protocol/settings/mcu_settings_category_handler.o
                                                  emodules/drv_mcu_uart/core/handlers/protocol/usb/mcu_usb_category_handler.o
                                                  emodules/drv_mcu_uart/core/handlers/protocol/tuner/mcu_tuner_category_handler.o
                                                  emodules/drv_mcu_uart/core/handlers/settings/mcu_settings_handler.o
                                                  emodules/drv_mcu_uart/core/handlers/sxm/mcu_sxm_handler.o
                                                  emodules/drv_mcu_uart/core/handlers/bluetooth/mcu_bt_handler.o
                                                  emodules/drv_mcu_uart/core/handlers/media/mcu_usb_handler.o
                                                  emodules/drv_mcu_uart/core/handlers/system/mcu_system_handler.o
                                                  emodules/drv_mcu_uart/core/mcu_cmd_dispatcher.o
                                                  emodules/drv_mcu_uart/core/mcu_update_proc.o
                                                  emodules/drv_mcu_uart/core/mcu_bypass_cmd_proc.o
                                                  emodules/drv_mcu_uart/core/mcu_interrupt_process.o
                                                  emodules/drv_mcu_uart/dev_mcu.o
eLIBs_mkdir                                       ./elibrary/bin//libminic.a(elibs_stdio.o)
eLIBs_open                                        ./elibrary/bin//libminic.a(elibs_stdio.o)
eLIBs_opendir                                     ./elibrary/bin//libminic.a(elibs_stdio.o)
eLIBs_printf                                      ./elibrary/bin//libminic.a(elibs_stdio.o)
                                                  emodules/drv_mcu_uart/core/handlers/media/mcu_usb_handler.o
                                                  emodules/drv_mcu_uart/core/handlers/system/mcu_system_handler.o
                                                  emodules/drv_mcu_uart/core/mcu_bypass_cmd_proc.o
                                                  emodules/drv_mcu_uart/core/mcu_interrupt_process.o
eLIBs_ramdom                                      ./elibrary/bin//libminic.a(elibs_stdlib.o)
                                                  emodules/drv_mcu_uart/core/mcu_interrupt_process.o
eLIBs_readdir                                     ./elibrary/bin//libminic.a(elibs_stdio.o)
eLIBs_realloc                                     ./elibrary/bin//libminic.a(elibs_stdlib.o)
eLIBs_remove                                      ./elibrary/bin//libminic.a(elibs_stdio.o)
eLIBs_rename                                      ./elibrary/bin//libminic.a(elibs_stdio.o)
eLIBs_rewinddir                                   ./elibrary/bin//libminic.a(elibs_stdio.o)
eLIBs_rmdir                                       ./elibrary/bin//libminic.a(elibs_stdio.o)
eLIBs_scnprintf                                   ./elibrary/bin//libminic.a(elibs_sprintf.o)
eLIBs_setfs                                       ./elibrary/bin//libminic.a(elibs_stdio.o)
eLIBs_snprintf                                    ./elibrary/bin//libminic.a(elibs_sprintf.o)
eLIBs_sprintf                                     ./elibrary/bin//libminic.a(elibs_sprintf.o)
eLIBs_sscanf                                      ./elibrary/bin//libminic.a(elibs_stdlib.o)
eLIBs_statfs                                      ./elibrary/bin//libminic.a(elibs_stdio.o)
eLIBs_strcat                                      ./elibrary/bin//libminic.a(elibs_string.o)
                                                  ./elibrary/bin//libminic.a(elibs_stdio.o)
eLIBs_strchr                                      ./elibrary/bin//libminic.a(elibs_string.o)
eLIBs_strchrlast                                  ./elibrary/bin//libminic.a(elibs_string.o)
eLIBs_strcmp                                      ./elibrary/bin//libminic.a(elibs_string.o)
                                                  ./elibrary/bin//libminic.a(elibs_stdio.o)
                                                  emodules/drv_mcu_uart/core/handlers/protocol/settings/mcu_settings_category_handler.o
                                                  emodules/drv_mcu_uart/core/handlers/protocol/tuner/mcu_tuner_category_handler.o
eLIBs_strcpy                                      ./elibrary/bin//libminic.a(elibs_string.o)
                                                  ./elibrary/bin//libminic.a(elibs_stdio.o)
                                                  emodules/drv_mcu_uart/core/handlers/protocol/party/mcu_party_category_handler.o
                                                  emodules/drv_mcu_uart/core/handlers/protocol/audio/mcu_audio_category_handler.o
                                                  emodules/drv_mcu_uart/core/handlers/sxm/mcu_sxm_handler.o
                                                  emodules/drv_mcu_uart/core/mcu_bypass_cmd_proc.o
                                                  emodules/drv_mcu_uart/core/mcu_interrupt_process.o
eLIBs_stricmp                                     ./elibrary/bin//libminic.a(elibs_string.o)
                                                  ./elibrary/bin//libminic.a(elibs_stdio.o)
eLIBs_strlen                                      ./elibrary/bin//libminic.a(elibs_string.o)
                                                  ./elibrary/bin//libminic.a(elibs_stdio.o)
                                                  emodules/drv_mcu_uart/core/mcu_bypass_cmd_proc.o
                                                  emodules/drv_mcu_uart/core/mcu_interrupt_process.o
eLIBs_strncat                                     ./elibrary/bin//libminic.a(elibs_string.o)
eLIBs_strnchr                                     ./elibrary/bin//libminic.a(elibs_string.o)
eLIBs_strncmp                                     ./elibrary/bin//libminic.a(elibs_string.o)
eLIBs_strncpy                                     ./elibrary/bin//libminic.a(elibs_string.o)
                                                  ./elibrary/bin//libminic.a(elibs_stdio.o)
eLIBs_strnicmp                                    ./elibrary/bin//libminic.a(elibs_string.o)
eLIBs_strnlen                                     ./elibrary/bin//libminic.a(elibs_string.o)
eLIBs_strstr                                      ./elibrary/bin//libminic.a(elibs_string.o)
eLIBs_strtol                                      ./elibrary/bin//libminic.a(elibs_stdlib.o)
eLIBs_toupper                                     ./elibrary/bin//libminic.a(elibs_stdlib.o)
eLIBs_uint2str_dec                                ./elibrary/bin//libminic.a(elibs_stdlib.o)
eLIBs_vprintf                                     ./elibrary/bin//libminic.a(elibs_stdio.o)
eLIBs_vscnprintf                                  ./elibrary/bin//libminic.a(elibs_sprintf.o)
                                                  ./elibrary/bin//libminic.a(elibs_stdio.o)
eLIBs_vsnprintf                                   ./elibrary/bin//libminic.a(elibs_sprintf.o)
eLIBs_vsprintf                                    ./elibrary/bin//libminic.a(elibs_sprintf.o)
esCFG_Exit_Ex                                     ./elibrary/bin//libsyscall.a(syscall.o)
esCFG_GetGPIOSecData                              ./elibrary/bin//libsyscall.a(syscall.o)
esCFG_GetGPIOSecData_Ex                           ./elibrary/bin//libsyscall.a(syscall.o)
esCFG_GetGPIOSecKeyCount                          ./elibrary/bin//libsyscall.a(syscall.o)
esCFG_GetGPIOSecKeyCount_Ex                       ./elibrary/bin//libsyscall.a(syscall.o)
esCFG_GetKeyValue                                 ./elibrary/bin//libsyscall.a(syscall.o)
esCFG_GetKeyValue_Ex                              ./elibrary/bin//libsyscall.a(syscall.o)
esCFG_GetSecCount                                 ./elibrary/bin//libsyscall.a(syscall.o)
esCFG_GetSecCount_Ex                              ./elibrary/bin//libsyscall.a(syscall.o)
esCFG_GetSecKeyCount                              ./elibrary/bin//libsyscall.a(syscall.o)
esCFG_GetSecKeyCount_Ex                           ./elibrary/bin//libsyscall.a(syscall.o)
esCFG_Init_Ex                                     ./elibrary/bin//libsyscall.a(syscall.o)
esCHS_Char2Uni                                    ./elibrary/bin//libsyscall.a(syscall.o)
esCHS_GetChLowerTbl                               ./elibrary/bin//libsyscall.a(syscall.o)
esCHS_GetChUpperTbl                               ./elibrary/bin//libsyscall.a(syscall.o)
esCHS_Uni2Char                                    ./elibrary/bin//libsyscall.a(syscall.o)
esCLK_CloseMclk                                   ./elibrary/bin//libsyscall.a(syscall.o)
esCLK_GetMclkDiv                                  ./elibrary/bin//libsyscall.a(syscall.o)
esCLK_GetMclkSrc                                  ./elibrary/bin//libsyscall.a(syscall.o)
esCLK_GetRound_Rate                               ./elibrary/bin//libsyscall.a(syscall.o)
esCLK_GetSrcFreq                                  ./elibrary/bin//libsyscall.a(syscall.o)
esCLK_MclkAssert                                  ./elibrary/bin//libsyscall.a(syscall.o)
esCLK_MclkDeassert                                ./elibrary/bin//libsyscall.a(syscall.o)
esCLK_MclkGetRstStatus                            ./elibrary/bin//libsyscall.a(syscall.o)
esCLK_MclkOnOff                                   ./elibrary/bin//libsyscall.a(syscall.o)
esCLK_MclkRegCb                                   ./elibrary/bin//libsyscall.a(syscall.o)
esCLK_MclkReset                                   ./elibrary/bin//libsyscall.a(syscall.o)
esCLK_MclkUnregCb                                 ./elibrary/bin//libsyscall.a(syscall.o)
esCLK_ModInfo                                     ./elibrary/bin//libsyscall.a(syscall.o)
esCLK_OpenMclk                                    ./elibrary/bin//libsyscall.a(syscall.o)
esCLK_SetMclkDiv                                  ./elibrary/bin//libsyscall.a(syscall.o)
esCLK_SetMclkSrc                                  ./elibrary/bin//libsyscall.a(syscall.o)
esCLK_SetSrcFreq                                  ./elibrary/bin//libsyscall.a(syscall.o)
esCLK_SysInfo                                     ./elibrary/bin//libsyscall.a(syscall.o)
esDEV_Close                                       ./elibrary/bin//libsyscall.a(syscall.o)
esDEV_DevReg                                      ./elibrary/bin//libsyscall.a(syscall.o)
esDEV_DevUnreg                                    ./elibrary/bin//libsyscall.a(syscall.o)
esDEV_Insmod                                      ./elibrary/bin//libsyscall.a(syscall.o)
esDEV_Ioctl                                       ./elibrary/bin//libsyscall.a(syscall.o)
esDEV_Lock                                        ./elibrary/bin//libsyscall.a(syscall.o)
esDEV_Open                                        ./elibrary/bin//libsyscall.a(syscall.o)
esDEV_Plugin                                      ./elibrary/bin//libsyscall.a(syscall.o)
esDEV_Plugout                                     ./elibrary/bin//libsyscall.a(syscall.o)
esDEV_Read                                        ./elibrary/bin//libsyscall.a(syscall.o)
esDEV_Unimod                                      ./elibrary/bin//libsyscall.a(syscall.o)
esDEV_Unlock                                      ./elibrary/bin//libsyscall.a(syscall.o)
esDEV_Write                                       ./elibrary/bin//libsyscall.a(syscall.o)
esDMA_ChangeMode                                  ./elibrary/bin//libsyscall.a(syscall.o)
esDMA_DisableINT                                  ./elibrary/bin//libsyscall.a(syscall.o)
esDMA_EnableINT                                   ./elibrary/bin//libsyscall.a(syscall.o)
esDMA_Information                                 ./elibrary/bin//libsyscall.a(syscall.o)
esDMA_QueryDst                                    ./elibrary/bin//libsyscall.a(syscall.o)
esDMA_QueryRestCount                              ./elibrary/bin//libsyscall.a(syscall.o)
esDMA_QuerySrc                                    ./elibrary/bin//libsyscall.a(syscall.o)
esDMA_QueryStat                                   ./elibrary/bin//libsyscall.a(syscall.o)
esDMA_RegDmaHdler                                 ./elibrary/bin//libsyscall.a(syscall.o)
esDMA_Release                                     ./elibrary/bin//libsyscall.a(syscall.o)
esDMA_Request                                     ./elibrary/bin//libsyscall.a(syscall.o)
esDMA_Restart                                     ./elibrary/bin//libsyscall.a(syscall.o)
esDMA_Setting                                     ./elibrary/bin//libsyscall.a(syscall.o)
esDMA_Start                                       ./elibrary/bin//libsyscall.a(syscall.o)
esDMA_Stop                                        ./elibrary/bin//libsyscall.a(syscall.o)
esDMA_UnregDmaHdler                               ./elibrary/bin//libsyscall.a(syscall.o)
esEXEC_PCreate                                    ./elibrary/bin//libsyscall.a(syscall.o)
esEXEC_PDel                                       ./elibrary/bin//libsyscall.a(syscall.o)
esEXEC_PDelReq                                    ./elibrary/bin//libsyscall.a(syscall.o)
esEXEC_Run                                        ./elibrary/bin//libsyscall.a(syscall.o)
esFSYS_clearpartupdateflag                        ./elibrary/bin//libsyscall.a(syscall.o)
esFSYS_closedir                                   ./elibrary/bin//libsyscall.a(syscall.o)
                                                  ./elibrary/bin//libminic.a(elibs_stdio.o)
esFSYS_fclose                                     ./elibrary/bin//libsyscall.a(syscall.o)
                                                  ./elibrary/bin//libminic.a(elibs_stdio.o)
esFSYS_fd2file                                    ./elibrary/bin//libsyscall.a(syscall.o)
                                                  ./elibrary/bin//libminic.a(elibs_stdio.o)
esFSYS_ferrclr                                    ./elibrary/bin//libsyscall.a(syscall.o)
                                                  ./elibrary/bin//libminic.a(elibs_stdio.o)
esFSYS_ferror                                     ./elibrary/bin//libsyscall.a(syscall.o)
                                                  ./elibrary/bin//libminic.a(elibs_stdio.o)
esFSYS_file2fd                                    ./elibrary/bin//libsyscall.a(syscall.o)
                                                  ./elibrary/bin//libminic.a(elibs_stdio.o)
esFSYS_fioctrl                                    ./elibrary/bin//libsyscall.a(syscall.o)
                                                  ./elibrary/bin//libminic.a(elibs_stdio.o)
esFSYS_fopen                                      ./elibrary/bin//libsyscall.a(syscall.o)
                                                  ./elibrary/bin//libminic.a(elibs_stdio.o)
esFSYS_format                                     ./elibrary/bin//libsyscall.a(syscall.o)
                                                  ./elibrary/bin//libminic.a(elibs_stdio.o)
esFSYS_fread                                      ./elibrary/bin//libsyscall.a(syscall.o)
                                                  ./elibrary/bin//libminic.a(elibs_stdio.o)
esFSYS_fsdbg                                      ./elibrary/bin//libsyscall.a(syscall.o)
esFSYS_fseek                                      ./elibrary/bin//libsyscall.a(syscall.o)
                                                  ./elibrary/bin//libminic.a(elibs_stdio.o)
esFSYS_fseekex                                    ./elibrary/bin//libsyscall.a(syscall.o)
                                                  ./elibrary/bin//libminic.a(elibs_stdio.o)
esFSYS_fsreg                                      ./elibrary/bin//libsyscall.a(syscall.o)
esFSYS_fstat                                      ./elibrary/bin//libsyscall.a(syscall.o)
                                                  ./elibrary/bin//libminic.a(elibs_stdio.o)
esFSYS_fsunreg                                    ./elibrary/bin//libsyscall.a(syscall.o)
esFSYS_fsync                                      ./elibrary/bin//libsyscall.a(syscall.o)
                                                  ./elibrary/bin//libminic.a(elibs_stdio.o)
esFSYS_ftell                                      ./elibrary/bin//libsyscall.a(syscall.o)
                                                  ./elibrary/bin//libminic.a(elibs_stdio.o)
esFSYS_ftellex                                    ./elibrary/bin//libsyscall.a(syscall.o)
                                                  ./elibrary/bin//libminic.a(elibs_stdio.o)
esFSYS_ftruncate                                  ./elibrary/bin//libsyscall.a(syscall.o)
                                                  ./elibrary/bin//libminic.a(elibs_stdio.o)
esFSYS_fwrite                                     ./elibrary/bin//libsyscall.a(syscall.o)
                                                  ./elibrary/bin//libminic.a(elibs_stdio.o)
esFSYS_getfscharset                               ./elibrary/bin//libsyscall.a(syscall.o)
                                                  ./elibrary/bin//libminic.a(elibs_stdio.o)
esFSYS_mkdir                                      ./elibrary/bin//libsyscall.a(syscall.o)
                                                  ./elibrary/bin//libminic.a(elibs_stdio.o)
esFSYS_mntfs                                      ./elibrary/bin//libsyscall.a(syscall.o)
esFSYS_mntparts                                   ./elibrary/bin//libsyscall.a(syscall.o)
esFSYS_open                                       ./elibrary/bin//libsyscall.a(syscall.o)
                                                  ./elibrary/bin//libminic.a(elibs_stdio.o)
esFSYS_opendir                                    ./elibrary/bin//libsyscall.a(syscall.o)
                                                  ./elibrary/bin//libminic.a(elibs_stdio.o)
esFSYS_partfslck                                  ./elibrary/bin//libsyscall.a(syscall.o)
                                                  ./elibrary/bin//libminic.a(elibs_stdio.o)
esFSYS_partfsunlck                                ./elibrary/bin//libsyscall.a(syscall.o)
                                                  ./elibrary/bin//libminic.a(elibs_stdio.o)
esFSYS_pclose                                     ./elibrary/bin//libsyscall.a(syscall.o)
esFSYS_pdreg                                      ./elibrary/bin//libsyscall.a(syscall.o)
esFSYS_pdunreg                                    ./elibrary/bin//libsyscall.a(syscall.o)
esFSYS_perrclr                                    ./elibrary/bin//libsyscall.a(syscall.o)
esFSYS_perror                                     ./elibrary/bin//libsyscall.a(syscall.o)
esFSYS_pioctrl                                    ./elibrary/bin//libsyscall.a(syscall.o)
esFSYS_popen                                      ./elibrary/bin//libsyscall.a(syscall.o)
esFSYS_pread                                      ./elibrary/bin//libsyscall.a(syscall.o)
esFSYS_premove                                    ./elibrary/bin//libsyscall.a(syscall.o)
esFSYS_prename                                    ./elibrary/bin//libsyscall.a(syscall.o)
esFSYS_pwrite                                     ./elibrary/bin//libsyscall.a(syscall.o)
esFSYS_querypartupdateflag                        ./elibrary/bin//libsyscall.a(syscall.o)
esFSYS_readdir                                    ./elibrary/bin//libsyscall.a(syscall.o)
                                                  ./elibrary/bin//libminic.a(elibs_stdio.o)
esFSYS_remove                                     ./elibrary/bin//libsyscall.a(syscall.o)
                                                  ./elibrary/bin//libminic.a(elibs_stdio.o)
esFSYS_rename                                     ./elibrary/bin//libsyscall.a(syscall.o)
                                                  ./elibrary/bin//libminic.a(elibs_stdio.o)
esFSYS_rewinddir                                  ./elibrary/bin//libsyscall.a(syscall.o)
                                                  ./elibrary/bin//libminic.a(elibs_stdio.o)
esFSYS_rmdir                                      ./elibrary/bin//libsyscall.a(syscall.o)
                                                  ./elibrary/bin//libminic.a(elibs_stdio.o)
esFSYS_setfs                                      ./elibrary/bin//libsyscall.a(syscall.o)
                                                  ./elibrary/bin//libminic.a(elibs_stdio.o)
esFSYS_statfs                                     ./elibrary/bin//libsyscall.a(syscall.o)
                                                  ./elibrary/bin//libminic.a(elibs_stdio.o)
esFSYS_statpt                                     ./elibrary/bin//libsyscall.a(syscall.o)
                                                  ./elibrary/bin//libminic.a(elibs_stdio.o)
esFSYS_umntfs                                     ./elibrary/bin//libsyscall.a(syscall.o)
esFSYS_umntparts                                  ./elibrary/bin//libsyscall.a(syscall.o)
esHID_SendMsg                                     ./elibrary/bin//libsyscall.a(syscall.o)
esHID_hiddevreg                                   ./elibrary/bin//libsyscall.a(syscall.o)
esHID_hiddevunreg                                 ./elibrary/bin//libsyscall.a(syscall.o)
esINPUT_GetLdevID                                 ./elibrary/bin//libsyscall.a(syscall.o)
esINPUT_LdevCtl                                   ./elibrary/bin//libsyscall.a(syscall.o)
esINPUT_LdevFeedback                              ./elibrary/bin//libsyscall.a(syscall.o)
esINPUT_LdevGrab                                  ./elibrary/bin//libsyscall.a(syscall.o)
esINPUT_LdevRelease                               ./elibrary/bin//libsyscall.a(syscall.o)
esINPUT_RegDev                                    ./elibrary/bin//libsyscall.a(syscall.o)
                                                  emodules/drv_mcu_uart/dev_mcu.o
esINPUT_SendEvent                                 ./elibrary/bin//libsyscall.a(syscall.o)
                                                  emodules/drv_mcu_uart/core/mcu_interrupt_process.o
esINPUT_UnregDev                                  ./elibrary/bin//libsyscall.a(syscall.o)
                                                  emodules/drv_mcu_uart/dev_mcu.o
esINT_DisableINT                                  ./elibrary/bin//libsyscall.a(syscall.o)
esINT_EnableINT                                   ./elibrary/bin//libsyscall.a(syscall.o)
esINT_InsFIR                                      ./elibrary/bin//libsyscall.a(syscall.o)
esINT_InsISR                                      ./elibrary/bin//libsyscall.a(syscall.o)
esINT_SetIRQPrio                                  ./elibrary/bin//libsyscall.a(syscall.o)
esINT_UniFIR                                      ./elibrary/bin//libsyscall.a(syscall.o)
esINT_UniISR                                      ./elibrary/bin//libsyscall.a(syscall.o)
esKRNL_CallBack                                   ./elibrary/bin//libsyscall.a(syscall.o)
esKRNL_DumpStack                                  ./elibrary/bin//libsyscall.a(syscall.o)
esKRNL_FlagAccept                                 ./elibrary/bin//libsyscall.a(syscall.o)
esKRNL_FlagCreate                                 ./elibrary/bin//libsyscall.a(syscall.o)
esKRNL_FlagDel                                    ./elibrary/bin//libsyscall.a(syscall.o)
esKRNL_FlagNameGet                                ./elibrary/bin//libsyscall.a(syscall.o)
esKRNL_FlagNameSet                                ./elibrary/bin//libsyscall.a(syscall.o)
esKRNL_FlagPend                                   ./elibrary/bin//libsyscall.a(syscall.o)
esKRNL_FlagPendGetFlagsRdy                        ./elibrary/bin//libsyscall.a(syscall.o)
esKRNL_FlagPost                                   ./elibrary/bin//libsyscall.a(syscall.o)
esKRNL_FlagQuery                                  ./elibrary/bin//libsyscall.a(syscall.o)
esKRNL_GetCallBack                                ./elibrary/bin//libsyscall.a(syscall.o)
esKRNL_GetTIDCur                                  ./elibrary/bin//libsyscall.a(syscall.o)
esKRNL_InterruptDisable                           ./elibrary/bin//libsyscall.a(syscall.o)
esKRNL_InterruptEnable                            ./elibrary/bin//libsyscall.a(syscall.o)
esKRNL_Ioctrl                                     ./elibrary/bin//libsyscall.a(syscall.o)
esKRNL_MboxAccept                                 ./elibrary/bin//libsyscall.a(syscall.o)
esKRNL_MboxCreate                                 ./elibrary/bin//libsyscall.a(syscall.o)
esKRNL_MboxDel                                    ./elibrary/bin//libsyscall.a(syscall.o)
esKRNL_MboxPend                                   ./elibrary/bin//libsyscall.a(syscall.o)
esKRNL_MboxPost                                   ./elibrary/bin//libsyscall.a(syscall.o)
esKRNL_MboxPostOpt                                ./elibrary/bin//libsyscall.a(syscall.o)
esKRNL_MboxQuery                                  ./elibrary/bin//libsyscall.a(syscall.o)
esKRNL_MemLeakChk                                 ./elibrary/bin//libsyscall.a(syscall.o)
esKRNL_MutexCreate                                ./elibrary/bin//libsyscall.a(syscall.o)
esKRNL_MutexDel                                   ./elibrary/bin//libsyscall.a(syscall.o)
esKRNL_MutexPend                                  ./elibrary/bin//libsyscall.a(syscall.o)
esKRNL_MutexPost                                  ./elibrary/bin//libsyscall.a(syscall.o)
esKRNL_QAccept                                    ./elibrary/bin//libsyscall.a(syscall.o)
esKRNL_QCreate                                    ./elibrary/bin//libsyscall.a(syscall.o)
esKRNL_QDel                                       ./elibrary/bin//libsyscall.a(syscall.o)
esKRNL_QFlush                                     ./elibrary/bin//libsyscall.a(syscall.o)
esKRNL_QPend                                      ./elibrary/bin//libsyscall.a(syscall.o)
esKRNL_QPost                                      ./elibrary/bin//libsyscall.a(syscall.o)
esKRNL_QPostFront                                 ./elibrary/bin//libsyscall.a(syscall.o)
esKRNL_QPostOpt                                   ./elibrary/bin//libsyscall.a(syscall.o)
esKRNL_QQuery                                     ./elibrary/bin//libsyscall.a(syscall.o)
esKRNL_SchedLock                                  ./elibrary/bin//libsyscall.a(syscall.o)
                                                  emodules/drv_mcu_uart/core/mcu_interrupt_process.o
esKRNL_SchedUnlock                                ./elibrary/bin//libsyscall.a(syscall.o)
                                                  emodules/drv_mcu_uart/core/mcu_interrupt_process.o
esKRNL_SemAccept                                  ./elibrary/bin//libsyscall.a(syscall.o)
esKRNL_SemCreate                                  ./elibrary/bin//libsyscall.a(syscall.o)
                                                  emodules/drv_mcu_uart/drv_mcu.o
esKRNL_SemDel                                     ./elibrary/bin//libsyscall.a(syscall.o)
                                                  emodules/drv_mcu_uart/drv_mcu.o
esKRNL_SemPend                                    ./elibrary/bin//libsyscall.a(syscall.o)
esKRNL_SemPost                                    ./elibrary/bin//libsyscall.a(syscall.o)
esKRNL_SemQuery                                   ./elibrary/bin//libsyscall.a(syscall.o)
esKRNL_SemSet                                     ./elibrary/bin//libsyscall.a(syscall.o)
esKRNL_SktAccept                                  ./elibrary/bin//libsyscall.a(syscall.o)
esKRNL_SktCreate                                  ./elibrary/bin//libsyscall.a(syscall.o)
esKRNL_SktDel                                     ./elibrary/bin//libsyscall.a(syscall.o)
esKRNL_SktPend                                    ./elibrary/bin//libsyscall.a(syscall.o)
esKRNL_SktPost                                    ./elibrary/bin//libsyscall.a(syscall.o)
esKRNL_TCreate                                    ./elibrary/bin//libsyscall.a(syscall.o)
                                                  emodules/drv_mcu_uart/core/mcu_interrupt_process.o
esKRNL_TDel                                       ./elibrary/bin//libsyscall.a(syscall.o)
                                                  emodules/drv_mcu_uart/core/mcu_interrupt_process.o
esKRNL_TDelReq                                    ./elibrary/bin//libsyscall.a(syscall.o)
                                                  emodules/drv_mcu_uart/core/mcu_interrupt_process.o
esKRNL_TaskNameSet                                ./elibrary/bin//libsyscall.a(syscall.o)
                                                  emodules/drv_mcu_uart/core/mcu_interrupt_process.o
esKRNL_TaskPrefEn                                 ./elibrary/bin//libsyscall.a(syscall.o)
esKRNL_TaskQuery                                  ./elibrary/bin//libsyscall.a(syscall.o)
esKRNL_TaskResume                                 ./elibrary/bin//libsyscall.a(syscall.o)
esKRNL_TaskSuspend                                ./elibrary/bin//libsyscall.a(syscall.o)
esKRNL_Time                                       ./elibrary/bin//libsyscall.a(syscall.o)
esKRNL_TimeDly                                    ./elibrary/bin//libsyscall.a(syscall.o)
                                                  emodules/drv_mcu_uart/core/mcu_interrupt_process.o
esKRNL_TimeDlyResume                              ./elibrary/bin//libsyscall.a(syscall.o)
esKRNL_TimeGet                                    ./elibrary/bin//libsyscall.a(syscall.o)
                                                  emodules/drv_mcu_uart/core/mcu_update_proc.o
                                                  emodules/drv_mcu_uart/core/mcu_bypass_cmd_proc.o
                                                  emodules/drv_mcu_uart/core/mcu_interrupt_process.o
esKRNL_TimeSet                                    ./elibrary/bin//libsyscall.a(syscall.o)
esKRNL_TmrCreate                                  ./elibrary/bin//libsyscall.a(syscall.o)
esKRNL_TmrDel                                     ./elibrary/bin//libsyscall.a(syscall.o)
esKRNL_TmrError                                   ./elibrary/bin//libsyscall.a(syscall.o)
esKRNL_TmrRemainGet                               ./elibrary/bin//libsyscall.a(syscall.o)
esKRNL_TmrStart                                   ./elibrary/bin//libsyscall.a(syscall.o)
esKRNL_TmrStateGet                                ./elibrary/bin//libsyscall.a(syscall.o)
esKRNL_TmrStop                                    ./elibrary/bin//libsyscall.a(syscall.o)
esKRNL_Version                                    ./elibrary/bin//libsyscall.a(syscall.o)
esKSRV_ClearWatchDog                              ./elibrary/bin//libsyscall.a(syscall.o)
esKSRV_DisableWatchDog                            ./elibrary/bin//libsyscall.a(syscall.o)
esKSRV_DisableWatchDogSrv                         ./elibrary/bin//libsyscall.a(syscall.o)
esKSRV_EnableWatchDog                             ./elibrary/bin//libsyscall.a(syscall.o)
esKSRV_EnableWatchDogSrv                          ./elibrary/bin//libsyscall.a(syscall.o)
esKSRV_GetAddPara                                 ./elibrary/bin//libsyscall.a(syscall.o)
esKSRV_GetDramCfgPara                             ./elibrary/bin//libsyscall.a(syscall.o)
esKSRV_GetHighMsg                                 ./elibrary/bin//libsyscall.a(syscall.o)
esKSRV_GetLowMsg                                  ./elibrary/bin//libsyscall.a(syscall.o)
esKSRV_GetMsg                                     ./elibrary/bin//libsyscall.a(syscall.o)
esKSRV_GetPara                                    ./elibrary/bin//libsyscall.a(syscall.o)
esKSRV_GetSocID                                   ./elibrary/bin//libsyscall.a(syscall.o)
esKSRV_GetTargetPara                              ./elibrary/bin//libsyscall.a(syscall.o)
esKSRV_GetVersion                                 ./elibrary/bin//libsyscall.a(syscall.o)
esKSRV_Get_Display_Hld                            ./elibrary/bin//libsyscall.a(syscall.o)
                                                  emodules/drv_mcu_uart/core/handlers/misc/mcu_misc_handler.o
                                                  emodules/drv_mcu_uart/core/mcu_interrupt_process.o
esKSRV_Get_Mixture_Hld                            ./elibrary/bin//libsyscall.a(syscall.o)
esKSRV_Get_Raw_Decoder_Hld                        ./elibrary/bin//libsyscall.a(syscall.o)
esKSRV_PowerOff                                   ./elibrary/bin//libsyscall.a(syscall.o)
esKSRV_Random                                     ./elibrary/bin//libsyscall.a(syscall.o)
                                                  ./elibrary/bin//libminic.a(elibs_stdlib.o)
esKSRV_Reset                                      ./elibrary/bin//libsyscall.a(syscall.o)
esKSRV_Save_Display_Hld                           ./elibrary/bin//libsyscall.a(syscall.o)
esKSRV_Save_Mixture_Hld                           ./elibrary/bin//libsyscall.a(syscall.o)
esKSRV_Save_Raw_Decoder_Hld                       ./elibrary/bin//libsyscall.a(syscall.o)
esKSRV_SendMsg                                    ./elibrary/bin//libsyscall.a(syscall.o)
                                                  emodules/drv_mcu_uart/core/handlers/upgrade/mcu_upgrade_handler.o
                                                  emodules/drv_mcu_uart/core/handlers/system/mcu_system_handler.o
                                                  emodules/drv_mcu_uart/core/mcu_interrupt_process.o
esKSRV_SendMsgEx                                  ./elibrary/bin//libsyscall.a(syscall.o)
esKSRV_SysInfo                                    ./elibrary/bin//libsyscall.a(syscall.o)
esKSRV_memcpy                                     ./elibrary/bin//libsyscall.a(syscall.o)
esKSRV_memset                                     ./elibrary/bin//libsyscall.a(syscall.o)
esMEMS_Balloc                                     ./elibrary/bin//libsyscall.a(syscall.o)
                                                  emodules/drv_mcu_uart/core/mcu_interrupt_process.o
esMEMS_Bfree                                      ./elibrary/bin//libsyscall.a(syscall.o)
                                                  emodules/drv_mcu_uart/core/mcu_interrupt_process.o
esMEMS_Calloc                                     ./elibrary/bin//libsyscall.a(syscall.o)
esMEMS_CleanDCache                                ./elibrary/bin//libsyscall.a(syscall.o)
esMEMS_CleanDCacheRegion                          ./elibrary/bin//libsyscall.a(syscall.o)
esMEMS_CleanFlushCache                            ./elibrary/bin//libsyscall.a(syscall.o)
esMEMS_CleanFlushCacheRegion                      ./elibrary/bin//libsyscall.a(syscall.o)
esMEMS_CleanFlushDCache                           ./elibrary/bin//libsyscall.a(syscall.o)
esMEMS_CleanFlushDCacheRegion                     ./elibrary/bin//libsyscall.a(syscall.o)
esMEMS_CleanInvalidateCacheAll                    ./elibrary/bin//libsyscall.a(syscall.o)
esMEMS_FlushCache                                 ./elibrary/bin//libsyscall.a(syscall.o)
esMEMS_FlushCacheRegion                           ./elibrary/bin//libsyscall.a(syscall.o)
esMEMS_FlushDCache                                ./elibrary/bin//libsyscall.a(syscall.o)
esMEMS_FlushDCacheRegion                          ./elibrary/bin//libsyscall.a(syscall.o)
esMEMS_FlushICache                                ./elibrary/bin//libsyscall.a(syscall.o)
esMEMS_FlushICacheRegion                          ./elibrary/bin//libsyscall.a(syscall.o)
esMEMS_FreeMemSize                                ./elibrary/bin//libsyscall.a(syscall.o)
                                                  emodules/drv_mcu_uart/core/mcu_interrupt_process.o
esMEMS_GetIoVAByPA                                ./elibrary/bin//libsyscall.a(syscall.o)
esMEMS_HeapCreate                                 ./elibrary/bin//libsyscall.a(syscall.o)
esMEMS_HeapDel                                    ./elibrary/bin//libsyscall.a(syscall.o)
esMEMS_Info                                       ./elibrary/bin//libsyscall.a(syscall.o)
esMEMS_Malloc                                     ./elibrary/bin//libsyscall.a(syscall.o)
                                                  ./elibrary/bin//libminic.a(elibs_stdlib.o)
                                                  ./elibrary/bin//libminic.a(elibs_stdio.o)
                                                  emodules/drv_mcu_uart/core/mcu_interrupt_process.o
esMEMS_Mfree                                      ./elibrary/bin//libsyscall.a(syscall.o)
                                                  ./elibrary/bin//libminic.a(elibs_stdlib.o)
                                                  ./elibrary/bin//libminic.a(elibs_stdio.o)
                                                  emodules/drv_mcu_uart/core/mcu_interrupt_process.o
esMEMS_Palloc                                     ./elibrary/bin//libsyscall.a(syscall.o)
                                                  emodules/drv_mcu_uart/core/mcu_bypass_cmd_proc.o
esMEMS_Pfree                                      ./elibrary/bin//libsyscall.a(syscall.o)
                                                  emodules/drv_mcu_uart/core/mcu_bypass_cmd_proc.o
esMEMS_Realloc                                    ./elibrary/bin//libsyscall.a(syscall.o)
                                                  ./elibrary/bin//libminic.a(elibs_stdlib.o)
esMEMS_TotalMemSize                               ./elibrary/bin//libsyscall.a(syscall.o)
                                                  emodules/drv_mcu_uart/core/mcu_interrupt_process.o
esMEMS_VA2PA                                      ./elibrary/bin//libsyscall.a(syscall.o)
esMEMS_VMCreate                                   ./elibrary/bin//libsyscall.a(syscall.o)
esMEMS_VMDelete                                   ./elibrary/bin//libsyscall.a(syscall.o)
esMEMS_VMalloc                                    ./elibrary/bin//libsyscall.a(syscall.o)
esMEMS_VMfree                                     ./elibrary/bin//libsyscall.a(syscall.o)
esMEM_BWDisable                                   ./elibrary/bin//libsyscall.a(syscall.o)
esMEM_BWEnable                                    ./elibrary/bin//libsyscall.a(syscall.o)
esMEM_BWGet                                       ./elibrary/bin//libsyscall.a(syscall.o)
esMEM_DramSuspend                                 ./elibrary/bin//libsyscall.a(syscall.o)
esMEM_DramWakeup                                  ./elibrary/bin//libsyscall.a(syscall.o)
esMEM_MasterGet                                   ./elibrary/bin//libsyscall.a(syscall.o)
esMEM_MasterSet                                   ./elibrary/bin//libsyscall.a(syscall.o)
esMEM_RegDramAccess                               ./elibrary/bin//libsyscall.a(syscall.o)
esMEM_ReleaseDramUsrMode                          ./elibrary/bin//libsyscall.a(syscall.o)
esMEM_RequestDramUsrMode                          ./elibrary/bin//libsyscall.a(syscall.o)
esMEM_SramRelBlk                                  ./elibrary/bin//libsyscall.a(syscall.o)
esMEM_SramReqBlk                                  ./elibrary/bin//libsyscall.a(syscall.o)
esMEM_SramSwitchBlk                               ./elibrary/bin//libsyscall.a(syscall.o)
esMEM_UnRegDramAccess                             ./elibrary/bin//libsyscall.a(syscall.o)
esMODS_MClose                                     ./elibrary/bin//libsyscall.a(syscall.o)
esMODS_MInstall                                   ./elibrary/bin//libsyscall.a(syscall.o)
esMODS_MIoctrl                                    ./elibrary/bin//libsyscall.a(syscall.o)
                                                  emodules/drv_mcu_uart/core/handlers/misc/mcu_misc_handler.o
                                                  emodules/drv_mcu_uart/core/mcu_interrupt_process.o
esMODS_MOpen                                      ./elibrary/bin//libsyscall.a(syscall.o)
esMODS_MRead                                      ./elibrary/bin//libsyscall.a(syscall.o)
esMODS_MUninstall                                 ./elibrary/bin//libsyscall.a(syscall.o)
esMODS_MWrite                                     ./elibrary/bin//libsyscall.a(syscall.o)
esMSTUB_GetFuncEntry                              ./elibrary/bin//libsyscall.a(syscall.o)
esMSTUB_RegFuncTbl                                ./elibrary/bin//libsyscall.a(syscall.o)
esMSTUB_UnRegFuncTbl                              ./elibrary/bin//libsyscall.a(syscall.o)
esPINS_ClearPending                               ./elibrary/bin//libsyscall.a(syscall.o)
esPINS_DisbaleInt                                 ./elibrary/bin//libsyscall.a(syscall.o)
esPINS_EnbaleInt                                  ./elibrary/bin//libsyscall.a(syscall.o)
esPINS_GetPinGrpStat                              ./elibrary/bin//libsyscall.a(syscall.o)
esPINS_GetPinStat                                 ./elibrary/bin//libsyscall.a(syscall.o)
esPINS_PinGrpRel                                  ./elibrary/bin//libsyscall.a(syscall.o)
                                                  emodules/drv_mcu_uart/core/mcu_interrupt_process.o
esPINS_PinGrpReq                                  ./elibrary/bin//libsyscall.a(syscall.o)
                                                  emodules/drv_mcu_uart/core/mcu_interrupt_process.o
esPINS_QueryInt                                   ./elibrary/bin//libsyscall.a(syscall.o)
esPINS_ReadPinData                                ./elibrary/bin//libsyscall.a(syscall.o)
                                                  emodules/drv_mcu_uart/core/mcu_interrupt_process.o
esPINS_RegIntHdler                                ./elibrary/bin//libsyscall.a(syscall.o)
esPINS_SetIntMode                                 ./elibrary/bin//libsyscall.a(syscall.o)
esPINS_SetPinDrive                                ./elibrary/bin//libsyscall.a(syscall.o)
esPINS_SetPinIO                                   ./elibrary/bin//libsyscall.a(syscall.o)
                                                  emodules/drv_mcu_uart/core/mcu_interrupt_process.o
esPINS_SetPinPull                                 ./elibrary/bin//libsyscall.a(syscall.o)
esPINS_SetPinStat                                 ./elibrary/bin//libsyscall.a(syscall.o)
esPINS_UnregIntHdler                              ./elibrary/bin//libsyscall.a(syscall.o)
esPINS_WritePinData                               ./elibrary/bin//libsyscall.a(syscall.o)
                                                  emodules/drv_mcu_uart/core/mcu_interrupt_process.o
esPWRMAN_EnterStandby                             ./elibrary/bin//libsyscall.a(syscall.o)
esPWRMAN_GetStandbyPara                           ./elibrary/bin//libsyscall.a(syscall.o)
esPWRMAN_LockCpuFreq                              ./elibrary/bin//libsyscall.a(syscall.o)
esPWRMAN_RegDevice                                ./elibrary/bin//libsyscall.a(syscall.o)
esPWRMAN_RelPwrmanMode                            ./elibrary/bin//libsyscall.a(syscall.o)
esPWRMAN_ReqPwrmanMode                            ./elibrary/bin//libsyscall.a(syscall.o)
esPWRMAN_SetStandbyMode                           ./elibrary/bin//libsyscall.a(syscall.o)
esPWRMAN_UnlockCpuFreq                            ./elibrary/bin//libsyscall.a(syscall.o)
esPWRMAN_UnregDevice                              ./elibrary/bin//libsyscall.a(syscall.o)
esPWRMAN_UsrEventNotify                           ./elibrary/bin//libsyscall.a(syscall.o)
esRESM_RClose                                     ./elibrary/bin//libsyscall.a(syscall.o)
esRESM_ROpen                                      ./elibrary/bin//libsyscall.a(syscall.o)
esRESM_RRead                                      ./elibrary/bin//libsyscall.a(syscall.o)
esRESM_RSeek                                      ./elibrary/bin//libsyscall.a(syscall.o)
esSIOS_getchar                                    ./elibrary/bin//libsyscall.a(syscall.o)
esSIOS_gets                                       ./elibrary/bin//libsyscall.a(syscall.o)
esSIOS_putarg                                     ./elibrary/bin//libsyscall.a(syscall.o)
esSIOS_putstr                                     ./elibrary/bin//libsyscall.a(syscall.o)
                                                  ./elibrary/bin//libminic.a(elibs_stdio.o)
esSIOS_setbaud                                    ./elibrary/bin//libsyscall.a(syscall.o)
esSVC_RegCloseNode                                ./elibrary/bin//libsyscall.a(syscall.o)
esSVC_RegClrError                                 ./elibrary/bin//libsyscall.a(syscall.o)
esSVC_RegCreateKey                                ./elibrary/bin//libsyscall.a(syscall.o)
esSVC_RegCreatePath                               ./elibrary/bin//libsyscall.a(syscall.o)
esSVC_RegCreateSet                                ./elibrary/bin//libsyscall.a(syscall.o)
esSVC_RegDeleteKey                                ./elibrary/bin//libsyscall.a(syscall.o)
esSVC_RegDeleteNode                               ./elibrary/bin//libsyscall.a(syscall.o)
esSVC_RegDeletePath                               ./elibrary/bin//libsyscall.a(syscall.o)
esSVC_RegDeleteSet                                ./elibrary/bin//libsyscall.a(syscall.o)
esSVC_RegGetError                                 ./elibrary/bin//libsyscall.a(syscall.o)
esSVC_RegGetFirstKey                              ./elibrary/bin//libsyscall.a(syscall.o)
esSVC_RegGetFirstSet                              ./elibrary/bin//libsyscall.a(syscall.o)
esSVC_RegGetKeyCount                              ./elibrary/bin//libsyscall.a(syscall.o)
esSVC_RegGetKeyValue                              ./elibrary/bin//libsyscall.a(syscall.o)
esSVC_RegGetNextKey                               ./elibrary/bin//libsyscall.a(syscall.o)
esSVC_RegGetNextSet                               ./elibrary/bin//libsyscall.a(syscall.o)
esSVC_RegGetRootPath                              ./elibrary/bin//libsyscall.a(syscall.o)
esSVC_RegGetSetCount                              ./elibrary/bin//libsyscall.a(syscall.o)
esSVC_RegIni2Reg                                  ./elibrary/bin//libsyscall.a(syscall.o)
esSVC_RegOpenNode                                 ./elibrary/bin//libsyscall.a(syscall.o)
esSVC_RegReg2Ini                                  ./elibrary/bin//libsyscall.a(syscall.o)
esSVC_RegSetKeyValue                              ./elibrary/bin//libsyscall.a(syscall.o)
esSVC_RegSetRootPath                              ./elibrary/bin//libsyscall.a(syscall.o)
esSVC_ResourceRel                                 ./elibrary/bin//libsyscall.a(syscall.o)
esSVC_ResourceReq                                 ./elibrary/bin//libsyscall.a(syscall.o)
esTIME_ContiCntr                                  ./elibrary/bin//libsyscall.a(syscall.o)
esTIME_GetDate                                    ./elibrary/bin//libsyscall.a(syscall.o)
                                                  ./elibrary/bin//libminic.a(elibs_stdio.o)
esTIME_GetTime                                    ./elibrary/bin//libsyscall.a(syscall.o)
                                                  ./elibrary/bin//libminic.a(elibs_stdio.o)
esTIME_PauseCntr                                  ./elibrary/bin//libsyscall.a(syscall.o)
esTIME_QuerryAlarm                                ./elibrary/bin//libsyscall.a(syscall.o)
esTIME_QuerryCntr                                 ./elibrary/bin//libsyscall.a(syscall.o)
esTIME_QuerryCntrStat                             ./elibrary/bin//libsyscall.a(syscall.o)
esTIME_QuerryTimer                                ./elibrary/bin//libsyscall.a(syscall.o)
esTIME_ReleaseAlarm                               ./elibrary/bin//libsyscall.a(syscall.o)
esTIME_ReleaseCntr                                ./elibrary/bin//libsyscall.a(syscall.o)
esTIME_ReleaseTimer                               ./elibrary/bin//libsyscall.a(syscall.o)
esTIME_RequestAlarm                               ./elibrary/bin//libsyscall.a(syscall.o)
esTIME_RequestCntr                                ./elibrary/bin//libsyscall.a(syscall.o)
esTIME_RequestTimer                               ./elibrary/bin//libsyscall.a(syscall.o)
esTIME_SetCntrPrescale                            ./elibrary/bin//libsyscall.a(syscall.o)
esTIME_SetCntrValue                               ./elibrary/bin//libsyscall.a(syscall.o)
esTIME_SetDate                                    ./elibrary/bin//libsyscall.a(syscall.o)
esTIME_SetTime                                    ./elibrary/bin//libsyscall.a(syscall.o)
esTIME_StartAlarm                                 ./elibrary/bin//libsyscall.a(syscall.o)
esTIME_StartCntr                                  ./elibrary/bin//libsyscall.a(syscall.o)
esTIME_StartTimer                                 ./elibrary/bin//libsyscall.a(syscall.o)
esTIME_StopAlarm                                  ./elibrary/bin//libsyscall.a(syscall.o)
esTIME_StopCntr                                   ./elibrary/bin//libsyscall.a(syscall.o)
esTIME_StopTimer                                  ./elibrary/bin//libsyscall.a(syscall.o)
fclose                                            /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/../../../../riscv64-unknown-elf/lib/rv64imafdcxtheadc/lp64d/libc.a(lib_a-fclose.o)
fflush                                            /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/../../../../riscv64-unknown-elf/lib/rv64imafdcxtheadc/lp64d/libc.a(lib_a-fflush.o)
fioctrl                                           ./elibrary/bin//libsyscall.a(syscall_fops.o)
fm_get_status                                     emodules/drv_mcu_uart/core/handlers/protocol/tuner/mcu_tuner_category_handler.o
fm_main_handler                                   emodules/drv_mcu_uart/core/handlers/protocol/tuner/mcu_tuner_category_handler.o
fputwc                                            /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/../../../../riscv64-unknown-elf/lib/rv64imafdcxtheadc/lp64d/libc.a(lib_a-fputwc.o)
frexp                                             /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/../../../../riscv64-unknown-elf/lib/rv64imafdcxtheadc/lp64d/libc.a(lib_a-s_frexp.o)
                                                  /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/../../../../riscv64-unknown-elf/lib/rv64imafdcxtheadc/lp64d/libc.a(lib_a-svfprintf.o)
                                                  /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/../../../../riscv64-unknown-elf/lib/rv64imafdcxtheadc/lp64d/libc.a(lib_a-vfprintf.o)
fsync                                             ./elibrary/bin//libsyscall.a(syscall_fops.o)
ftruncate                                         ./elibrary/bin//libsyscall.a(syscall_fops.o)
g_first_source_flag                               emodules/drv_mcu_uart/core/handlers/protocol/mcu_new_protocol_handler.o
g_init_data_get_flag                              emodules/drv_mcu_uart/core/handlers/protocol/mcu_new_protocol_handler.o
get_brower_cmd_flag                               emodules/drv_mcu_uart/core/mcu_interrupt_process.o
get_cmd_err                                       emodules/drv_mcu_uart/core/mcu_interrupt_process.o
get_cmd_no                                        emodules/drv_mcu_uart/core/mcu_interrupt_process.o
get_data                                          emodules/drv_mcu_uart/core/mcu_interrupt_process.o
get_mcu_ack_pro                                   emodules/drv_mcu_uart/core/mcu_interrupt_process.o
hTX3De                                            emodules/drv_mcu_uart/core/mcu_interrupt_process.o
hal_mcu_control                                   emodules/drv_mcu_uart/dev_mcu.o
hal_mcu_init                                      emodules/drv_mcu_uart/dev_mcu.o
hal_mcu_uninit                                    emodules/drv_mcu_uart/dev_mcu.o
hu_get_remote_mount                               emodules/drv_mcu_uart/core/mcu_bypass_cmd_proc.o
hu_pass2remote_fw_version                         emodules/drv_mcu_uart/core/mcu_bypass_cmd_proc.o
hu_pass2remote_usb_status                         emodules/drv_mcu_uart/core/mcu_bypass_cmd_proc.o
hu_set_remote_update_status                       emodules/drv_mcu_uart/core/mcu_update_proc.o
                                                  emodules/drv_mcu_uart/core/mcu_bypass_cmd_proc.o
init_position_para                                emodules/drv_mcu_uart/core/mcu_aux_init_para.o
                                                  emodules/drv_mcu_uart/core/mcu_interrupt_process.o
init_uart3_tx_disable                             emodules/drv_mcu_uart/core/mcu_interrupt_process.o
ioctl                                             ./elibrary/bin//libsyscall.a(syscall_fops.o)
                                                  emodules/drv_mcu_uart/core/mcu_interrupt_process.o
isCPUbigEndian                                    emodules/drv_mcu_uart/core/handlers/protocol/mcu_new_protocol_handler.o
is_all_remote_update_success                      emodules/drv_mcu_uart/core/mcu_update_proc.o
                                                  emodules/drv_mcu_uart/core/mcu_bypass_cmd_proc.o
last_get_cmd_no                                   emodules/drv_mcu_uart/core/mcu_interrupt_process.o
last_sdtp_header                                  emodules/drv_mcu_uart/core/mcu_interrupt_process.o
localeconv                                        /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/../../../../riscv64-unknown-elf/lib/rv64imafdcxtheadc/lp64d/libc.a(lib_a-localeconv.o)
make_update_data_send_page                        emodules/drv_mcu_uart/core/mcu_update_proc.o
                                                  emodules/drv_mcu_uart/core/mcu_interrupt_process.o
mcu                                               emodules/drv_mcu_uart/dev_mcu.o
mcu_audio_category_handler_register               emodules/drv_mcu_uart/core/handlers/protocol/audio/mcu_audio_category_handler.o
                                                  emodules/drv_mcu_uart/core/handlers/protocol/mcu_new_protocol_handler.o
mcu_audio_cmd_handler                             emodules/drv_mcu_uart/core/handlers/audio/mcu_audio_handler.o
                                                  emodules/drv_mcu_uart/core/mcu_cmd_dispatcher.o
mcu_audio_handler_register                        emodules/drv_mcu_uart/core/handlers/audio/mcu_audio_handler.o
mcu_bt_cmd_handler                                emodules/drv_mcu_uart/core/handlers/bluetooth/mcu_bt_handler.o
                                                  emodules/drv_mcu_uart/core/mcu_cmd_dispatcher.o
mcu_bt_handler_register                           emodules/drv_mcu_uart/core/handlers/bluetooth/mcu_bt_handler.o
mcu_bypass_cmd_handler                            emodules/drv_mcu_uart/core/handlers/bypass/mcu_bypass_handler.o
                                                  emodules/drv_mcu_uart/core/mcu_cmd_dispatcher.o
mcu_bypass_handler_register                       emodules/drv_mcu_uart/core/handlers/bypass/mcu_bypass_handler.o
mcu_cmd_dispatch                                  emodules/drv_mcu_uart/core/mcu_cmd_dispatcher.o
                                                  emodules/drv_mcu_uart/core/mcu_interrupt_process.o
mcu_cmd_dispatcher_exit                           emodules/drv_mcu_uart/core/mcu_cmd_dispatcher.o
                                                  emodules/drv_mcu_uart/core/mcu_interrupt_process.o
mcu_cmd_dispatcher_init                           emodules/drv_mcu_uart/core/mcu_cmd_dispatcher.o
                                                  emodules/drv_mcu_uart/core/mcu_interrupt_process.o
mcu_cmd_get_stats                                 emodules/drv_mcu_uart/core/mcu_cmd_dispatcher.o
mcu_cmd_print_status                              emodules/drv_mcu_uart/core/mcu_cmd_dispatcher.o
mcu_cmd_register_handler                          emodules/drv_mcu_uart/core/mcu_cmd_dispatcher.o
                                                  emodules/drv_mcu_uart/core/handlers/protocol/mcu_new_protocol_handler.o
                                                  emodules/drv_mcu_uart/core/handlers/misc/mcu_misc_handler.o
                                                  emodules/drv_mcu_uart/core/handlers/bypass/mcu_bypass_handler.o
                                                  emodules/drv_mcu_uart/core/handlers/upgrade/mcu_upgrade_handler.o
                                                  emodules/drv_mcu_uart/core/handlers/audio/mcu_audio_handler.o
                                                  emodules/drv_mcu_uart/core/handlers/settings/mcu_settings_handler.o
                                                  emodules/drv_mcu_uart/core/handlers/sxm/mcu_sxm_handler.o
                                                  emodules/drv_mcu_uart/core/handlers/bluetooth/mcu_bt_handler.o
                                                  emodules/drv_mcu_uart/core/handlers/media/mcu_usb_handler.o
                                                  emodules/drv_mcu_uart/core/handlers/media/mcu_tuner_handler.o
                                                  emodules/drv_mcu_uart/core/handlers/system/mcu_system_handler.o
mcu_cmd_reset_stats                               emodules/drv_mcu_uart/core/mcu_cmd_dispatcher.o
mcu_drv                                           emodules/drv_mcu_uart/drv_mcu.o
mcu_get_paramter_hdle                             emodules/drv_mcu_uart/core/mcu_interrupt_process.o
                                                  emodules/drv_mcu_uart/dev_mcu.o
mcu_key_cmd_put_key_encoder                       emodules/drv_mcu_uart/core/mcu_interrupt_process.o
                                                  emodules/drv_mcu_uart/core/handlers/system/mcu_system_handler.o
mcu_key_op                                        emodules/drv_mcu_uart/dev_mcu.o
mcu_lock                                          emodules/drv_mcu_uart/drv_mcu.o
mcu_message_process_init                          emodules/drv_mcu_uart/core/mcu_interrupt_process.o
                                                  emodules/drv_mcu_uart/dev_mcu.o
mcu_message_process_uninit                        emodules/drv_mcu_uart/core/mcu_interrupt_process.o
                                                  emodules/drv_mcu_uart/dev_mcu.o
mcu_misc_cmd_handler                              emodules/drv_mcu_uart/core/handlers/misc/mcu_misc_handler.o
                                                  emodules/drv_mcu_uart/core/mcu_cmd_dispatcher.o
mcu_misc_handler_register                         emodules/drv_mcu_uart/core/handlers/misc/mcu_misc_handler.o
mcu_new_get_init_data                             emodules/drv_mcu_uart/core/handlers/protocol/mcu_new_protocol_handler.o
                                                  emodules/drv_mcu_uart/core/mcu_cmd_dispatcher.o
mcu_new_get_source_init_data                      emodules/drv_mcu_uart/core/handlers/protocol/mcu_new_protocol_handler.o
                                                  emodules/drv_mcu_uart/core/handlers/protocol/system/mcu_system_category_handler.o
mcu_new_protocol_cmd_handler                      emodules/drv_mcu_uart/core/handlers/protocol/mcu_new_protocol_handler.o
mcu_new_protocol_get_category_data                emodules/drv_mcu_uart/core/handlers/protocol/mcu_new_protocol_handler.o
mcu_new_protocol_get_stats                        emodules/drv_mcu_uart/core/handlers/protocol/mcu_new_protocol_handler.o
mcu_new_protocol_handler_register                 emodules/drv_mcu_uart/core/handlers/protocol/mcu_new_protocol_handler.o
                                                  emodules/drv_mcu_uart/core/mcu_cmd_dispatcher.o
mcu_new_protocol_print_status                     emodules/drv_mcu_uart/core/handlers/protocol/mcu_new_protocol_handler.o
mcu_new_protocol_register_category_handler        emodules/drv_mcu_uart/core/handlers/protocol/mcu_new_protocol_handler.o
                                                  emodules/drv_mcu_uart/core/handlers/protocol/settings/mcu_settings_category_handler.o
                                                  emodules/drv_mcu_uart/core/handlers/protocol/usb/mcu_usb_category_handler.o
                                                  emodules/drv_mcu_uart/core/handlers/protocol/party/mcu_party_category_handler.o
                                                  emodules/drv_mcu_uart/core/handlers/protocol/tuner/mcu_tuner_category_handler.o
                                                  emodules/drv_mcu_uart/core/handlers/protocol/audio/mcu_audio_category_handler.o
                                                  emodules/drv_mcu_uart/core/handlers/protocol/system/mcu_system_category_handler.o
mcu_new_protocol_reset_stats                      emodules/drv_mcu_uart/core/handlers/protocol/mcu_new_protocol_handler.o
mcu_new_protocol_send_category_data               emodules/drv_mcu_uart/core/handlers/protocol/mcu_new_protocol_handler.o
                                                  emodules/drv_mcu_uart/core/handlers/protocol/audio/mcu_audio_category_handler.o
                                                  emodules/drv_mcu_uart/core/handlers/protocol/system/mcu_system_category_handler.o
mcu_new_push_cmdpkt_to_send_queue                 emodules/drv_mcu_uart/core/handlers/protocol/mcu_new_protocol_handler.o
mcu_party_category_handler_register               emodules/drv_mcu_uart/core/handlers/protocol/party/mcu_party_category_handler.o
                                                  emodules/drv_mcu_uart/core/handlers/protocol/mcu_new_protocol_handler.o
mcu_print_memery                                  emodules/drv_mcu_uart/core/mcu_interrupt_process.o
mcu_push_cmd_to_queue                             emodules/drv_mcu_uart/core/mcu_interrupt_process.o
                                                  emodules/drv_mcu_uart/core/handlers/protocol/party/mcu_party_category_handler.o
                                                  emodules/drv_mcu_uart/core/handlers/protocol/tuner/mcu_tuner_category_handler.o
                                                  emodules/drv_mcu_uart/core/handlers/protocol/audio/mcu_audio_category_handler.o
                                                  emodules/drv_mcu_uart/core/handlers/protocol/system/mcu_system_category_handler.o
                                                  emodules/drv_mcu_uart/core/handlers/misc/mcu_misc_handler.o
                                                  emodules/drv_mcu_uart/core/handlers/audio/mcu_audio_handler.o
                                                  emodules/drv_mcu_uart/core/handlers/settings/mcu_settings_handler.o
                                                  emodules/drv_mcu_uart/core/handlers/sxm/mcu_sxm_handler.o
                                                  emodules/drv_mcu_uart/core/handlers/bluetooth/mcu_bt_handler.o
                                                  emodules/drv_mcu_uart/core/handlers/media/mcu_usb_handler.o
                                                  emodules/drv_mcu_uart/core/handlers/media/mcu_tuner_handler.o
                                                  emodules/drv_mcu_uart/core/handlers/system/mcu_system_handler.o
                                                  emodules/drv_mcu_uart/core/mcu_bypass_cmd_proc.o
mcu_set_lcd_brightness                            emodules/drv_mcu_uart/core/mcu_interrupt_process.o
                                                  emodules/drv_mcu_uart/core/handlers/system/mcu_system_handler.o
mcu_set_lcd_off                                   emodules/drv_mcu_uart/core/mcu_interrupt_process.o
mcu_settings_category_handler_register            emodules/drv_mcu_uart/core/handlers/protocol/settings/mcu_settings_category_handler.o
                                                  emodules/drv_mcu_uart/core/handlers/protocol/mcu_new_protocol_handler.o
mcu_settings_cmd_handler                          emodules/drv_mcu_uart/core/handlers/settings/mcu_settings_handler.o
                                                  emodules/drv_mcu_uart/core/mcu_cmd_dispatcher.o
mcu_settings_handler_register                     emodules/drv_mcu_uart/core/handlers/settings/mcu_settings_handler.o
mcu_sxm_cmd_handler                               emodules/drv_mcu_uart/core/handlers/sxm/mcu_sxm_handler.o
                                                  emodules/drv_mcu_uart/core/mcu_cmd_dispatcher.o
mcu_sxm_handler_register                          emodules/drv_mcu_uart/core/handlers/sxm/mcu_sxm_handler.o
mcu_system_category_handler_register              emodules/drv_mcu_uart/core/handlers/protocol/system/mcu_system_category_handler.o
                                                  emodules/drv_mcu_uart/core/handlers/protocol/mcu_new_protocol_handler.o
mcu_system_cmd_handler                            emodules/drv_mcu_uart/core/handlers/system/mcu_system_handler.o
                                                  emodules/drv_mcu_uart/core/mcu_cmd_dispatcher.o
mcu_system_handler_register                       emodules/drv_mcu_uart/core/handlers/system/mcu_system_handler.o
mcu_tuner_category_handler_register               emodules/drv_mcu_uart/core/handlers/protocol/tuner/mcu_tuner_category_handler.o
                                                  emodules/drv_mcu_uart/core/handlers/protocol/mcu_new_protocol_handler.o
mcu_tuner_cmd_handler                             emodules/drv_mcu_uart/core/handlers/media/mcu_tuner_handler.o
                                                  emodules/drv_mcu_uart/core/mcu_cmd_dispatcher.o
mcu_tuner_handler_register                        emodules/drv_mcu_uart/core/handlers/media/mcu_tuner_handler.o
mcu_upgrade_cmd_handler                           emodules/drv_mcu_uart/core/handlers/upgrade/mcu_upgrade_handler.o
                                                  emodules/drv_mcu_uart/core/mcu_cmd_dispatcher.o
mcu_upgrade_handler_register                      emodules/drv_mcu_uart/core/handlers/upgrade/mcu_upgrade_handler.o
mcu_usb_cmd_handler                               emodules/drv_mcu_uart/core/handlers/media/mcu_usb_handler.o
                                                  emodules/drv_mcu_uart/core/mcu_cmd_dispatcher.o
mcu_usb_disk_category_handler_register            emodules/drv_mcu_uart/core/handlers/protocol/usb/mcu_usb_category_handler.o
                                                  emodules/drv_mcu_uart/core/handlers/protocol/mcu_new_protocol_handler.o
mcu_usb_handler_register                          emodules/drv_mcu_uart/core/handlers/media/mcu_usb_handler.o
mcu_usb_ipod_category_handler_register            emodules/drv_mcu_uart/core/handlers/protocol/usb/mcu_usb_category_handler.o
                                                  emodules/drv_mcu_uart/core/handlers/protocol/mcu_new_protocol_handler.o
memchr                                            /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/../../../../riscv64-unknown-elf/lib/rv64imafdcxtheadc/lp64d/libc.a(lib_a-memchr.o)
                                                  /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/../../../../riscv64-unknown-elf/lib/rv64imafdcxtheadc/lp64d/libc.a(lib_a-svfiprintf.o)
                                                  /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/../../../../riscv64-unknown-elf/lib/rv64imafdcxtheadc/lp64d/libc.a(lib_a-fvwrite.o)
                                                  /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/../../../../riscv64-unknown-elf/lib/rv64imafdcxtheadc/lp64d/libc.a(lib_a-vfiprintf.o)
                                                  /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/../../../../riscv64-unknown-elf/lib/rv64imafdcxtheadc/lp64d/libc.a(lib_a-svfprintf.o)
                                                  /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/../../../../riscv64-unknown-elf/lib/rv64imafdcxtheadc/lp64d/libc.a(lib_a-vfprintf.o)
memcpy                                            /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/../../../../riscv64-unknown-elf/lib/rv64imafdcxtheadc/lp64d/libc.a(lib_a-memcpy.o)
                                                  /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/../../../../riscv64-unknown-elf/lib/rv64imafdcxtheadc/lp64d/libc.a(lib_a-svfiprintf.o)
                                                  /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/../../../../riscv64-unknown-elf/lib/rv64imafdcxtheadc/lp64d/libc.a(lib_a-fvwrite.o)
                                                  /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/../../../../riscv64-unknown-elf/lib/rv64imafdcxtheadc/lp64d/libc.a(lib_a-mprec.o)
                                                  emodules/drv_mcu_uart/core/handlers/protocol/audio/mcu_audio_category_handler.o
                                                  emodules/drv_mcu_uart/core/mcu_interrupt_process.o
memmove                                           /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/../../../../riscv64-unknown-elf/lib/rv64imafdcxtheadc/lp64d/libc.a(lib_a-memmove-stub.o)
                                                  /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/../../../../riscv64-unknown-elf/lib/rv64imafdcxtheadc/lp64d/libc.a(lib_a-svfiprintf.o)
                                                  /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/../../../../riscv64-unknown-elf/lib/rv64imafdcxtheadc/lp64d/libc.a(lib_a-fvwrite.o)
memset                                            /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/../../../../riscv64-unknown-elf/lib/rv64imafdcxtheadc/lp64d/libc.a(lib_a-memset.o)
                                                  /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/../../../../riscv64-unknown-elf/lib/rv64imafdcxtheadc/lp64d/libc.a(lib_a-findfp.o)
                                                  ./elibrary/bin//libminic.a(elibs_stdio.o)
                                                  emodules/drv_mcu_uart/core/handlers/protocol/mcu_new_protocol_handler.o
                                                  emodules/drv_mcu_uart/core/handlers/bluetooth/mcu_bt_handler.o
                                                  emodules/drv_mcu_uart/core/mcu_interrupt_process.o
message_process_ctrl                              emodules/drv_mcu_uart/core/mcu_interrupt_process.o
                                                  emodules/drv_mcu_uart/core/handlers/protocol/mcu_new_protocol_handler.o
                                                  emodules/drv_mcu_uart/dev_mcu.o
mkdir                                             ./elibrary/bin//libsyscall.a(syscall_fops.o)
mmap                                              ./elibrary/bin//libsyscall.a(syscall.o)
modinfo                                           emodules/drv_mcu_uart/magic.o
msleep                                            ./elibrary/bin//libsyscall.a(syscall.o)
munmap                                            ./elibrary/bin//libsyscall.a(syscall.o)
omit_string_tail_space                            emodules/drv_mcu_uart/core/mcu_interrupt_process.o
                                                  emodules/drv_mcu_uart/core/handlers/settings/mcu_settings_handler.o
                                                  emodules/drv_mcu_uart/core/handlers/system/mcu_system_handler.o
open                                              /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/../../../../riscv64-unknown-elf/lib/rv64imafdcxtheadc/lp64d/libc.a(lib_a-sysopen.o)
                                                  emodules/drv_mcu_uart/core/mcu_interrupt_process.o
opendir                                           ./elibrary/bin//libsyscall.a(syscall_fops.o)
pAU                                               emodules/drv_mcu_uart/core/mcu_interrupt_process.o
party_get_audio_source                            emodules/drv_mcu_uart/core/handlers/protocol/party/mcu_party_category_handler.o
party_get_connection_status                       emodules/drv_mcu_uart/core/handlers/protocol/party/mcu_party_category_handler.o
party_get_device_name                             emodules/drv_mcu_uart/core/handlers/protocol/party/mcu_party_category_handler.o
party_get_device_role                             emodules/drv_mcu_uart/core/handlers/protocol/party/mcu_party_category_handler.o
party_get_discovered_devices                      emodules/drv_mcu_uart/core/handlers/protocol/party/mcu_party_category_handler.o
party_get_error_status                            emodules/drv_mcu_uart/core/handlers/protocol/party/mcu_party_category_handler.o
party_get_mode_status                             emodules/drv_mcu_uart/core/handlers/protocol/party/mcu_party_category_handler.o
party_get_scan_status                             emodules/drv_mcu_uart/core/handlers/protocol/party/mcu_party_category_handler.o
party_get_status                                  emodules/drv_mcu_uart/core/handlers/protocol/party/mcu_party_category_handler.o
party_init_status                                 emodules/drv_mcu_uart/core/handlers/protocol/party/mcu_party_category_handler.o
party_main_handler                                emodules/drv_mcu_uart/core/handlers/protocol/party/mcu_party_category_handler.o
pipe                                              ./elibrary/bin//libsyscall.a(syscall.o)
poll                                              ./elibrary/bin//libsyscall.a(syscall.o)
pop_data_from_queue                               emodules/drv_mcu_uart/core/mcu_interrupt_process.o
prepare_update_data_send                          emodules/drv_mcu_uart/core/mcu_update_proc.o
                                                  emodules/drv_mcu_uart/core/mcu_bypass_cmd_proc.o
                                                  emodules/drv_mcu_uart/core/mcu_interrupt_process.o
printf                                            /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/../../../../riscv64-unknown-elf/lib/rv64imafdcxtheadc/lp64d/libc.a(lib_a-printf.o)
                                                  emodules/drv_mcu_uart/core/handlers/media/mcu_usb_handler.o
                                                  emodules/drv_mcu_uart/core/mcu_bypass_cmd_proc.o
                                                  emodules/drv_mcu_uart/core/mcu_interrupt_process.o
printk                                            ./elibrary/bin//libminic.a(elibs_stdio.o)
pthread_atfork                                    ./elibrary/bin//libsyscall.a(syscall.o)
pthread_attr_destroy                              ./elibrary/bin//libsyscall.a(syscall.o)
pthread_attr_getdetachstate                       ./elibrary/bin//libsyscall.a(syscall.o)
pthread_attr_getguardsize                         ./elibrary/bin//libsyscall.a(syscall.o)
pthread_attr_getschedparam                        ./elibrary/bin//libsyscall.a(syscall.o)
pthread_attr_getschedpolicy                       ./elibrary/bin//libsyscall.a(syscall.o)
pthread_attr_getscope                             ./elibrary/bin//libsyscall.a(syscall.o)
pthread_attr_getstack                             ./elibrary/bin//libsyscall.a(syscall.o)
pthread_attr_getstackaddr                         ./elibrary/bin//libsyscall.a(syscall.o)
pthread_attr_getstacksize                         ./elibrary/bin//libsyscall.a(syscall.o)
pthread_attr_init                                 ./elibrary/bin//libsyscall.a(syscall.o)
pthread_attr_setdetachstate                       ./elibrary/bin//libsyscall.a(syscall.o)
pthread_attr_setguardsize                         ./elibrary/bin//libsyscall.a(syscall.o)
pthread_attr_setschedparam                        ./elibrary/bin//libsyscall.a(syscall.o)
pthread_attr_setschedpolicy                       ./elibrary/bin//libsyscall.a(syscall.o)
pthread_attr_setscope                             ./elibrary/bin//libsyscall.a(syscall.o)
pthread_attr_setstack                             ./elibrary/bin//libsyscall.a(syscall.o)
pthread_attr_setstackaddr                         ./elibrary/bin//libsyscall.a(syscall.o)
pthread_attr_setstacksize                         ./elibrary/bin//libsyscall.a(syscall.o)
pthread_barrier_destroy                           ./elibrary/bin//libsyscall.a(syscall.o)
pthread_barrier_init                              ./elibrary/bin//libsyscall.a(syscall.o)
pthread_barrier_wait                              ./elibrary/bin//libsyscall.a(syscall.o)
pthread_barrierattr_destroy                       ./elibrary/bin//libsyscall.a(syscall.o)
pthread_barrierattr_getpshared                    ./elibrary/bin//libsyscall.a(syscall.o)
pthread_barrierattr_init                          ./elibrary/bin//libsyscall.a(syscall.o)
pthread_barrierattr_setpshared                    ./elibrary/bin//libsyscall.a(syscall.o)
pthread_cancel                                    ./elibrary/bin//libsyscall.a(syscall.o)
pthread_cleanup_pop                               ./elibrary/bin//libsyscall.a(syscall.o)
pthread_cleanup_push                              ./elibrary/bin//libsyscall.a(syscall.o)
pthread_cond_broadcast                            ./elibrary/bin//libsyscall.a(syscall.o)
pthread_cond_destroy                              ./elibrary/bin//libsyscall.a(syscall.o)
pthread_cond_init                                 ./elibrary/bin//libsyscall.a(syscall.o)
pthread_cond_signal                               ./elibrary/bin//libsyscall.a(syscall.o)
pthread_cond_timedwait                            ./elibrary/bin//libsyscall.a(syscall.o)
pthread_cond_wait                                 ./elibrary/bin//libsyscall.a(syscall.o)
pthread_condattr_destroy                          ./elibrary/bin//libsyscall.a(syscall.o)
pthread_condattr_getclock                         ./elibrary/bin//libsyscall.a(syscall.o)
pthread_condattr_init                             ./elibrary/bin//libsyscall.a(syscall.o)
pthread_condattr_setclock                         ./elibrary/bin//libsyscall.a(syscall.o)
pthread_create                                    ./elibrary/bin//libsyscall.a(syscall.o)
pthread_detach                                    ./elibrary/bin//libsyscall.a(syscall.o)
pthread_exit                                      ./elibrary/bin//libsyscall.a(syscall.o)
pthread_getspecific                               ./elibrary/bin//libsyscall.a(syscall.o)
pthread_join                                      ./elibrary/bin//libsyscall.a(syscall.o)
pthread_key_create                                ./elibrary/bin//libsyscall.a(syscall.o)
pthread_key_delete                                ./elibrary/bin//libsyscall.a(syscall.o)
pthread_kill                                      ./elibrary/bin//libsyscall.a(syscall.o)
pthread_mutex_destroy                             ./elibrary/bin//libsyscall.a(syscall.o)
pthread_mutex_init                                ./elibrary/bin//libsyscall.a(syscall.o)
pthread_mutex_lock                                ./elibrary/bin//libsyscall.a(syscall.o)
pthread_mutex_trylock                             ./elibrary/bin//libsyscall.a(syscall.o)
pthread_mutex_unlock                              ./elibrary/bin//libsyscall.a(syscall.o)
pthread_mutexattr_destroy                         ./elibrary/bin//libsyscall.a(syscall.o)
pthread_mutexattr_getpshared                      ./elibrary/bin//libsyscall.a(syscall.o)
pthread_mutexattr_gettype                         ./elibrary/bin//libsyscall.a(syscall.o)
pthread_mutexattr_init                            ./elibrary/bin//libsyscall.a(syscall.o)
pthread_mutexattr_setpshared                      ./elibrary/bin//libsyscall.a(syscall.o)
pthread_mutexattr_settype                         ./elibrary/bin//libsyscall.a(syscall.o)
pthread_once                                      ./elibrary/bin//libsyscall.a(syscall.o)
pthread_rwlock_destroy                            ./elibrary/bin//libsyscall.a(syscall.o)
pthread_rwlock_init                               ./elibrary/bin//libsyscall.a(syscall.o)
pthread_rwlock_rdlock                             ./elibrary/bin//libsyscall.a(syscall.o)
pthread_rwlock_timedrdlock                        ./elibrary/bin//libsyscall.a(syscall.o)
pthread_rwlock_timedwrlock                        ./elibrary/bin//libsyscall.a(syscall.o)
pthread_rwlock_tryrdlock                          ./elibrary/bin//libsyscall.a(syscall.o)
pthread_rwlock_trywrlock                          ./elibrary/bin//libsyscall.a(syscall.o)
pthread_rwlock_unlock                             ./elibrary/bin//libsyscall.a(syscall.o)
pthread_rwlock_wrlock                             ./elibrary/bin//libsyscall.a(syscall.o)
pthread_rwlockattr_destroy                        ./elibrary/bin//libsyscall.a(syscall.o)
pthread_rwlockattr_getpshared                     ./elibrary/bin//libsyscall.a(syscall.o)
pthread_rwlockattr_init                           ./elibrary/bin//libsyscall.a(syscall.o)
pthread_rwlockattr_setpshared                     ./elibrary/bin//libsyscall.a(syscall.o)
pthread_self                                      ./elibrary/bin//libsyscall.a(syscall.o)
pthread_setcancelstate                            ./elibrary/bin//libsyscall.a(syscall.o)
pthread_setcanceltype                             ./elibrary/bin//libsyscall.a(syscall.o)
pthread_setname_np                                ./elibrary/bin//libsyscall.a(syscall.o)
pthread_setspecific                               ./elibrary/bin//libsyscall.a(syscall.o)
pthread_spin_destroy                              ./elibrary/bin//libsyscall.a(syscall.o)
pthread_spin_init                                 ./elibrary/bin//libsyscall.a(syscall.o)
pthread_spin_lock                                 ./elibrary/bin//libsyscall.a(syscall.o)
pthread_spin_trylock                              ./elibrary/bin//libsyscall.a(syscall.o)
pthread_spin_unlock                               ./elibrary/bin//libsyscall.a(syscall.o)
pthread_system_init                               ./elibrary/bin//libsyscall.a(syscall.o)
pthread_testcancel                                ./elibrary/bin//libsyscall.a(syscall.o)
push_data_to_queue                                emodules/drv_mcu_uart/core/mcu_interrupt_process.o
read_sequel_buffer                                emodules/drv_mcu_uart/core/mcu_interrupt_process.o
readdir                                           ./elibrary/bin//libsyscall.a(syscall_fops.o)
remote_pass2hu_mount                              emodules/drv_mcu_uart/core/mcu_bypass_cmd_proc.o
                                                  emodules/drv_mcu_uart/core/mcu_interrupt_process.o
remove                                            ./elibrary/bin//libsyscall.a(syscall_fops.o)
reset_artwork_buf                                 emodules/drv_mcu_uart/core/mcu_interrupt_process.o
                                                  emodules/drv_mcu_uart/core/handlers/media/mcu_usb_handler.o
                                                  emodules/drv_mcu_uart/core/mcu_bypass_cmd_proc.o
reset_artwork_pkg                                 emodules/drv_mcu_uart/core/mcu_interrupt_process.o
                                                  emodules/drv_mcu_uart/core/handlers/media/mcu_usb_handler.o
                                                  emodules/drv_mcu_uart/core/mcu_bypass_cmd_proc.o
reset_remote_pkg_head                             emodules/drv_mcu_uart/core/mcu_interrupt_process.o
reset_uart_sequel_buf                             emodules/drv_mcu_uart/core/mcu_interrupt_process.o
reset_uart_sub_get_sequel_buf                     emodules/drv_mcu_uart/core/mcu_interrupt_process.o
reset_update_data_send                            emodules/drv_mcu_uart/core/mcu_update_proc.o
                                                  emodules/drv_mcu_uart/core/mcu_bypass_cmd_proc.o
rewinddir                                         ./elibrary/bin//libsyscall.a(syscall_fops.o)
rmdir                                             ./elibrary/bin//libsyscall.a(syscall_fops.o)
rt_device_register                                ./elibrary/bin//libsyscall.a(syscall.o)
                                                  emodules/drv_mcu_uart/dev_mcu.o
rt_device_unregister                              ./elibrary/bin//libsyscall.a(syscall.o)
                                                  emodules/drv_mcu_uart/dev_mcu.o
select                                            ./elibrary/bin//libsyscall.a(syscall.o)
sem_close                                         ./elibrary/bin//libsyscall.a(syscall.o)
sem_destroy                                       ./elibrary/bin//libsyscall.a(syscall.o)
sem_getvalue                                      ./elibrary/bin//libsyscall.a(syscall.o)
sem_init                                          ./elibrary/bin//libsyscall.a(syscall.o)
sem_open                                          ./elibrary/bin//libsyscall.a(syscall.o)
sem_post                                          ./elibrary/bin//libsyscall.a(syscall.o)
sem_timedwait                                     ./elibrary/bin//libsyscall.a(syscall.o)
sem_trywait                                       ./elibrary/bin//libsyscall.a(syscall.o)
sem_unlink                                        ./elibrary/bin//libsyscall.a(syscall.o)
sem_wait                                          ./elibrary/bin//libsyscall.a(syscall.o)
send_ack_to_mcu                                   emodules/drv_mcu_uart/core/mcu_interrupt_process.o
send_artwork_cnt                                  emodules/drv_mcu_uart/core/mcu_interrupt_process.o
send_data                                         emodules/drv_mcu_uart/core/mcu_interrupt_process.o
sequel                                            emodules/drv_mcu_uart/core/mcu_interrupt_process.o
setlocale                                         /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/../../../../riscv64-unknown-elf/lib/rv64imafdcxtheadc/lp64d/libc.a(lib_a-locale.o)
settimeofday                                      ./elibrary/bin//libsyscall.a(syscall_fops.o)
settings_get_status                               emodules/drv_mcu_uart/core/handlers/protocol/settings/mcu_settings_category_handler.o
settings_main_handler                             emodules/drv_mcu_uart/core/handlers/protocol/settings/mcu_settings_category_handler.o
sleep                                             ./elibrary/bin//libsyscall.a(syscall.o)
sprintf                                           /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/../../../../riscv64-unknown-elf/lib/rv64imafdcxtheadc/lp64d/libc.a(lib_a-sprintf.o)
                                                  /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/../../../../riscv64-unknown-elf/lib/rv64imafdcxtheadc/lp64d/libc.a(lib_a-ldtoa.o)
statfs                                            ./elibrary/bin//libsyscall.a(syscall_fops.o)
strcmp                                            /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/../../../../riscv64-unknown-elf/lib/rv64imafdcxtheadc/lp64d/libc.a(lib_a-strcmp.o)
                                                  /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/../../../../riscv64-unknown-elf/lib/rv64imafdcxtheadc/lp64d/libc.a(lib_a-locale.o)
strcpy                                            /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/../../../../riscv64-unknown-elf/lib/rv64imafdcxtheadc/lp64d/libc.a(lib_a-strcpy.o)
                                                  /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/../../../../riscv64-unknown-elf/lib/rv64imafdcxtheadc/lp64d/libc.a(lib_a-ldtoa.o)
strlen                                            /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/../../../../riscv64-unknown-elf/lib/rv64imafdcxtheadc/lp64d/libc.a(lib_a-strlen.o)
                                                  /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/../../../../riscv64-unknown-elf/lib/rv64imafdcxtheadc/lp64d/libc.a(lib_a-svfiprintf.o)
                                                  /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/../../../../riscv64-unknown-elf/lib/rv64imafdcxtheadc/lp64d/libc.a(lib_a-vfiprintf.o)
                                                  /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/../../../../riscv64-unknown-elf/lib/rv64imafdcxtheadc/lp64d/libc.a(lib_a-svfprintf.o)
                                                  /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/../../../../riscv64-unknown-elf/lib/rv64imafdcxtheadc/lp64d/libc.a(lib_a-vfprintf.o)
strncpy                                           /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/../../../../riscv64-unknown-elf/lib/rv64imafdcxtheadc/lp64d/libc.a(lib_a-strncpy.o)
                                                  /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/../../../../riscv64-unknown-elf/lib/rv64imafdcxtheadc/lp64d/libc.a(lib_a-svfiprintf.o)
                                                  /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/../../../../riscv64-unknown-elf/lib/rv64imafdcxtheadc/lp64d/libc.a(lib_a-vfiprintf.o)
                                                  /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/../../../../riscv64-unknown-elf/lib/rv64imafdcxtheadc/lp64d/libc.a(lib_a-svfprintf.o)
                                                  /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/../../../../riscv64-unknown-elf/lib/rv64imafdcxtheadc/lp64d/libc.a(lib_a-vfprintf.o)
sunxi_driver_mcu_init                             emodules/drv_mcu_uart/dev_mcu.o
                                                  emodules/drv_mcu_uart/drv_mcu.o
sunxi_driver_mcu_uninit                           emodules/drv_mcu_uart/dev_mcu.o
                                                  emodules/drv_mcu_uart/drv_mcu.o
sxm_baudot_table                                  emodules/drv_mcu_uart/core/mcu_interrupt_process.o
sxm_bitbuff_align                                 emodules/drv_mcu_uart/core/mcu_interrupt_process.o
sxm_bitbuff_baudot                                emodules/drv_mcu_uart/core/mcu_interrupt_process.o
sxm_bitbuff_read                                  emodules/drv_mcu_uart/core/mcu_interrupt_process.o
sxm_bitbuff_setup                                 emodules/drv_mcu_uart/core/mcu_interrupt_process.o
sxm_browser_lock                                  emodules/drv_mcu_uart/core/mcu_interrupt_process.o
                                                  emodules/drv_mcu_uart/core/handlers/sxm/mcu_sxm_handler.o
sxm_crc32_calculate                               emodules/drv_mcu_uart/core/mcu_interrupt_process.o
sxm_crc32_check                                   emodules/drv_mcu_uart/core/mcu_interrupt_process.o
sxm_crc32_initialize                              emodules/drv_mcu_uart/core/mcu_interrupt_process.o
sxm_crc32_part                                    emodules/drv_mcu_uart/core/mcu_interrupt_process.o
sxm_find_au_index                                 emodules/drv_mcu_uart/core/mcu_interrupt_process.o
sxm_pix_complete_au                               emodules/drv_mcu_uart/core/mcu_interrupt_process.o
system_get_current_model                          emodules/drv_mcu_uart/core/handlers/protocol/system/mcu_system_category_handler.o
system_get_current_source                         emodules/drv_mcu_uart/core/handlers/protocol/system/mcu_system_category_handler.o
system_get_current_theme                          emodules/drv_mcu_uart/core/handlers/protocol/system/mcu_system_category_handler.o
system_get_factory_info                           emodules/drv_mcu_uart/core/handlers/protocol/system/mcu_system_category_handler.o
system_get_version                                emodules/drv_mcu_uart/core/handlers/protocol/system/mcu_system_category_handler.o
system_main_handler                               emodules/drv_mcu_uart/core/handlers/protocol/system/mcu_system_category_handler.o
system_set_button_dimmer_off_level                emodules/drv_mcu_uart/core/handlers/protocol/system/mcu_system_category_handler.o
system_set_button_dimmer_on_level                 emodules/drv_mcu_uart/core/handlers/protocol/system/mcu_system_category_handler.o
system_set_dimmer_switch                          emodules/drv_mcu_uart/core/handlers/protocol/system/mcu_system_category_handler.o
system_set_language                               emodules/drv_mcu_uart/core/handlers/protocol/system/mcu_system_category_handler.o
system_set_model                                  emodules/drv_mcu_uart/core/handlers/protocol/system/mcu_system_category_handler.o
system_set_power_state                            emodules/drv_mcu_uart/core/handlers/protocol/system/mcu_system_category_handler.o
system_set_source                                 emodules/drv_mcu_uart/core/handlers/protocol/system/mcu_system_category_handler.o
system_set_source_enable                          emodules/drv_mcu_uart/core/handlers/protocol/system/mcu_system_category_handler.o
system_set_tft_dimmer_off_level                   emodules/drv_mcu_uart/core/handlers/protocol/system/mcu_system_category_handler.o
system_set_tft_dimmer_on_level                    emodules/drv_mcu_uart/core/handlers/protocol/system/mcu_system_category_handler.o
system_set_theme                                  emodules/drv_mcu_uart/core/handlers/protocol/system/mcu_system_category_handler.o
uart3_tx2gpio                                     emodules/drv_mcu_uart/core/mcu_interrupt_process.o
uart3_tx_enable                                   emodules/drv_mcu_uart/core/mcu_interrupt_process.o
uart_SubCmd_send2main                             emodules/drv_mcu_uart/core/mcu_interrupt_process.o
uart_ack_arrived_sequel_proc                      emodules/drv_mcu_uart/core/mcu_interrupt_process.o
                                                  emodules/drv_mcu_uart/core/handlers/misc/mcu_misc_handler.o
uart_check_usb_exist                              emodules/drv_mcu_uart/core/mcu_interrupt_process.o
                                                  emodules/drv_mcu_uart/core/handlers/media/mcu_usb_handler.o
uart_main_send_update_data                        emodules/drv_mcu_uart/core/mcu_update_proc.o
                                                  emodules/drv_mcu_uart/core/mcu_interrupt_process.o
uart_send_cmd                                     emodules/drv_mcu_uart/core/mcu_interrupt_process.o
                                                  emodules/drv_mcu_uart/core/handlers/system/mcu_system_handler.o
uart_send_cmd_data                                emodules/drv_mcu_uart/core/mcu_interrupt_process.o
                                                  emodules/drv_mcu_uart/core/handlers/upgrade/mcu_upgrade_handler.o
                                                  emodules/drv_mcu_uart/core/handlers/media/mcu_usb_handler.o
                                                  emodules/drv_mcu_uart/core/mcu_update_proc.o
                                                  emodules/drv_mcu_uart/core/mcu_bypass_cmd_proc.o
                                                  emodules/drv_mcu_uart/dev_mcu.o
uart_send_data                                    emodules/drv_mcu_uart/core/mcu_interrupt_process.o
uart_send_data_ex                                 emodules/drv_mcu_uart/core/mcu_interrupt_process.o
uart_sequel                                       emodules/drv_mcu_uart/core/mcu_interrupt_process.o
uart_sub_send_cmd_data                            emodules/drv_mcu_uart/core/mcu_interrupt_process.o
                                                  emodules/drv_mcu_uart/core/handlers/misc/mcu_misc_handler.o
uart_sub_send_data                                emodules/drv_mcu_uart/core/mcu_interrupt_process.o
update_sequel_buffer_RdPtr                        emodules/drv_mcu_uart/core/mcu_interrupt_process.o
usb_disk_get_state                                emodules/drv_mcu_uart/core/handlers/protocol/usb/mcu_usb_category_handler.o
usb_disk_main_handler                             emodules/drv_mcu_uart/core/handlers/protocol/usb/mcu_usb_category_handler.o
usb_get_handler_stats                             emodules/drv_mcu_uart/core/handlers/protocol/usb/mcu_usb_category_handler.o
usb_handler_deinit                                emodules/drv_mcu_uart/core/handlers/protocol/usb/mcu_usb_category_handler.o
usb_handler_init                                  emodules/drv_mcu_uart/core/handlers/protocol/usb/mcu_usb_category_handler.o
usb_ipod_get_state                                emodules/drv_mcu_uart/core/handlers/protocol/usb/mcu_usb_category_handler.o
usb_ipod_main_handler                             emodules/drv_mcu_uart/core/handlers/protocol/usb/mcu_usb_category_handler.o
usb_print_handler_status                          emodules/drv_mcu_uart/core/handlers/protocol/usb/mcu_usb_category_handler.o
usb_reset_handler_stats                           emodules/drv_mcu_uart/core/handlers/protocol/usb/mcu_usb_category_handler.o
usleep                                            ./elibrary/bin//libsyscall.a(syscall.o)
                                                  emodules/drv_mcu_uart/core/mcu_interrupt_process.o
vfiprintf                                         /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/../../../../riscv64-unknown-elf/lib/rv64imafdcxtheadc/lp64d/libc.a(lib_a-vfiprintf.o)
vfprintf                                          /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/../../../../riscv64-unknown-elf/lib/rv64imafdcxtheadc/lp64d/libc.a(lib_a-vfprintf.o)
wait_debounce                                     emodules/drv_mcu_uart/core/mcu_interrupt_process.o
wb_get_status                                     emodules/drv_mcu_uart/core/handlers/protocol/tuner/mcu_tuner_category_handler.o
wb_main_handler                                   emodules/drv_mcu_uart/core/handlers/protocol/tuner/mcu_tuner_category_handler.o
wcrtomb                                           /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/../../../../riscv64-unknown-elf/lib/rv64imafdcxtheadc/lp64d/libc.a(lib_a-wcrtomb.o)
write_sequel_buffer                               emodules/drv_mcu_uart/core/mcu_interrupt_process.o
                                                  emodules/drv_mcu_uart/core/handlers/protocol/mcu_new_protocol_handler.o
