Archive member included to satisfy reference by file (symbol)

./elibrary/bin//libsyscall.a(syscall.o)
                              emodules/fm/dev_fm.o (rt_device_register)
./elibrary/bin//libsyscall.a(syscall_fops.o)
                              emodules/fm/fm_module/si4760/Rds.o (ioctl)
./elibrary/bin//libsyscall.a(syscall.o)
                              emodules/fm/fm_module/si4760/Tu_Si_476X.o (usleep)
./elibrary/bin//libminic.a(elibs_stdio.o)
                              emodules/fm/fm_module/si4760/Tuner.o (eLIBs_printf)
./elibrary/bin//libminic.a(elibs_string.o)
                              ./elibrary/bin//libminic.a(elibs_stdio.o) (eLIBs_strlen)
./elibrary/bin//libminic.a(elibs_sprintf.o)
                              ./elibrary/bin//libminic.a(elibs_stdio.o) (eLIBs_vscnprintf)
/home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/../../../../riscv64-unknown-elf/lib/rv64imafdcxtheadc/lp64d/libc.a(lib_a-malloc.o)
                              emodules/fm/dev_fm.o (malloc)
/home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/../../../../riscv64-unknown-elf/lib/rv64imafdcxtheadc/lp64d/libc.a(lib_a-memcpy.o)
                              emodules/fm/fm_module/si4760/Tuner.o (memcpy)
/home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/../../../../riscv64-unknown-elf/lib/rv64imafdcxtheadc/lp64d/libc.a(lib_a-memset.o)
                              emodules/fm/fm_module/si4760/Rds.o (memset)
/home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/../../../../riscv64-unknown-elf/lib/rv64imafdcxtheadc/lp64d/libc.a(lib_a-sysclose.o)
                              emodules/fm/fm_module/si4760/Rds.o (close)
/home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/../../../../riscv64-unknown-elf/lib/rv64imafdcxtheadc/lp64d/libc.a(lib_a-sysopen.o)
                              emodules/fm/fm_module/si4760/Rds.o (open)
/home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/../../../../riscv64-unknown-elf/lib/rv64imafdcxtheadc/lp64d/libc.a(lib_a-syswrite.o)
                              emodules/fm/dev_fm.o (write)
/home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/../../../../riscv64-unknown-elf/lib/rv64imafdcxtheadc/lp64d/libc.a(lib_a-impure.o)
                              /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/../../../../riscv64-unknown-elf/lib/rv64imafdcxtheadc/lp64d/libc.a(lib_a-malloc.o) (_impure_ptr)

Discarded input sections

 .text          0x0000000000000000        0x0 emodules/fm/fm_module/si4760/Rds.o
 .data          0x0000000000000000        0x0 emodules/fm/fm_module/si4760/Rds.o
 .bss           0x0000000000000000        0x0 emodules/fm/fm_module/si4760/Rds.o
 .text.dab_fm_link_PI_table_printf
                0x0000000000000000        0x2 emodules/fm/fm_module/si4760/Rds.o
 .bss.Preset_Name
                0x0000000000000000       0xb4 emodules/fm/fm_module/si4760/Rds.o
 .bss.smeter_bak
                0x0000000000000000        0x1 emodules/fm/fm_module/si4760/Rds.o
 .bss.ta_seek_cnt
                0x0000000000000000        0x1 emodules/fm/fm_module/si4760/Rds.o
 .bss.ta_seek_net
                0x0000000000000000        0x1 emodules/fm/fm_module/si4760/Rds.o
 .text          0x0000000000000000        0x0 emodules/fm/fm_module/si4760/Rds_sub.o
 .data          0x0000000000000000        0x0 emodules/fm/fm_module/si4760/Rds_sub.o
 .bss           0x0000000000000000        0x0 emodules/fm/fm_module/si4760/Rds_sub.o
 .text.Network_get_af
                0x0000000000000000       0x2a emodules/fm/fm_module/si4760/Rds_sub.o
 .text.Network_af_store_ex
                0x0000000000000000      0x1c4 emodules/fm/fm_module/si4760/Rds_sub.o
 .text          0x0000000000000000        0x0 emodules/fm/fm_module/si4760/Tu_Si_476X.o
 .data          0x0000000000000000        0x0 emodules/fm/fm_module/si4760/Tu_Si_476X.o
 .bss           0x0000000000000000        0x0 emodules/fm/fm_module/si4760/Tu_Si_476X.o
 .text.si475x_get_property
                0x0000000000000000       0x2c emodules/fm/fm_module/si4760/Tu_Si_476X.o
 .text.getIntStatus
                0x0000000000000000       0x26 emodules/fm/fm_module/si4760/Tu_Si_476X.o
 .text.Si475x_Int_enable
                0x0000000000000000        0x2 emodules/fm/fm_module/si4760/Tu_Si_476X.o
 .text.Si475x_Int_disable
                0x0000000000000000        0x2 emodules/fm/fm_module/si4760/Tu_Si_476X.o
 .text.si475xFMRX_powerdown
                0x0000000000000000       0x1e emodules/fm/fm_module/si4760/Tu_Si_476X.o
 .text.si475xFMRX_front_end_cfg
                0x0000000000000000       0x26 emodules/fm/fm_module/si4760/Tu_Si_476X.o
 .bss.Tuner_AF_Confirm_Timers
                0x0000000000000000        0x1 emodules/fm/fm_module/si4760/Tu_Si_476X.o
 .text          0x0000000000000000        0x0 emodules/fm/fm_module/si4760/Tuner.o
 .data          0x0000000000000000        0x0 emodules/fm/fm_module/si4760/Tuner.o
 .bss           0x0000000000000000        0x0 emodules/fm/fm_module/si4760/Tuner.o
 .text.set_dab_fm_link_backup_freq
                0x0000000000000000        0xa emodules/fm/fm_module/si4760/Tuner.o
 .text.tuner_set_channel
                0x0000000000000000       0x5c emodules/fm/fm_module/si4760/Tuner.o
 .text.wait_us  0x0000000000000000        0x2 emodules/fm/fm_module/si4760/Tuner.o
 .text.wait_ms  0x0000000000000000        0x2 emodules/fm/fm_module/si4760/Tuner.o
 .text.Tune_AF_Seek_end
                0x0000000000000000        0x2 emodules/fm/fm_module/si4760/Tuner.o
 .text.set_mono_on_off
                0x0000000000000000       0xb8 emodules/fm/fm_module/si4760/Tuner.o
 .text.Display_PTY
                0x0000000000000000        0x2 emodules/fm/fm_module/si4760/Tuner.o
 .text.Cmd_PTY_Sel
                0x0000000000000000        0x2 emodules/fm/fm_module/si4760/Tuner.o
 .text.Cmd_Pset_Sel
                0x0000000000000000        0x2 emodules/fm/fm_module/si4760/Tuner.o
 .text.Tuner_Mode_Exit
                0x0000000000000000      0x1b0 emodules/fm/fm_module/si4760/Tuner.o
 .text.Tuner_Init_Proc
                0x0000000000000000        0x8 emodules/fm/fm_module/si4760/Tuner.o
 .text.Tuner_Mode_Proc
                0x0000000000000000        0x8 emodules/fm/fm_module/si4760/Tuner.o
 .text.Rds_QualityCheck
                0x0000000000000000        0x8 emodules/fm/fm_module/si4760/Tuner.o
 .text.Tuner_App_GET_SET_TU
                0x0000000000000000        0x2 emodules/fm/fm_module/si4760/Tuner.o
 .text.si4760_powerup
                0x0000000000000000       0x26 emodules/fm/fm_module/si4760/Tuner.o
 .text.si4760_get_partinfo
                0x0000000000000000       0x8c emodules/fm/fm_module/si4760/Tuner.o
 .bss.Store_scroll
                0x0000000000000000        0x1 emodules/fm/fm_module/si4760/Tuner.o
 .bss.Tune_Mute_Timer
                0x0000000000000000        0x1 emodules/fm/fm_module/si4760/Tuner.o
 .bss.Tune_Mute_Timer_af
                0x0000000000000000        0x1 emodules/fm/fm_module/si4760/Tuner.o
 .bss.Tune_Option_Band
                0x0000000000000000        0x1 emodules/fm/fm_module/si4760/Tuner.o
 .bss.Tune_Option_Lever
                0x0000000000000000        0x1 emodules/fm/fm_module/si4760/Tuner.o
 .bss.Tune_Option_OK
                0x0000000000000000        0x1 emodules/fm/fm_module/si4760/Tuner.o
 .bss.Tune_Option_State
                0x0000000000000000        0x1 emodules/fm/fm_module/si4760/Tuner.o
 .bss.Tune_Process_Timer_Af
                0x0000000000000000        0x1 emodules/fm/fm_module/si4760/Tuner.o
 .bss.Tuner_Init_mute_status
                0x0000000000000000        0x1 emodules/fm/fm_module/si4760/Tuner.o
 .bss.Tuner_Ver
                0x0000000000000000        0x1 emodules/fm/fm_module/si4760/Tuner.o
 .bss.af_1st_divisor
                0x0000000000000000        0x2 emodules/fm/fm_module/si4760/Tuner.o
 .text          0x0000000000000000        0x0 emodules/fm/dev_fm.o
 .data          0x0000000000000000        0x0 emodules/fm/dev_fm.o
 .bss           0x0000000000000000        0x0 emodules/fm/dev_fm.o
 .text.rec_task
                0x0000000000000000      0x134 emodules/fm/dev_fm.o
 .text.play_task
                0x0000000000000000      0x11c emodules/fm/dev_fm.o
 .text.monitor_task
                0x0000000000000000       0x94 emodules/fm/dev_fm.o
 .text.audio_start
                0x0000000000000000       0x5a emodules/fm/dev_fm.o
 .text.audio_stop
                0x0000000000000000       0xcc emodules/fm/dev_fm.o
 .bss.circle_buf
                0x0000000000000000       0x28 emodules/fm/dev_fm.o
 .bss.monitor_task_id
                0x0000000000000000        0x4 emodules/fm/dev_fm.o
 .bss.play_task_id
                0x0000000000000000        0x4 emodules/fm/dev_fm.o
 .bss.rec_task_id
                0x0000000000000000        0x4 emodules/fm/dev_fm.o
 .rodata.play_task.str1.8
                0x0000000000000000       0x11 emodules/fm/dev_fm.o
 .rodata.rec_task.str1.8
                0x0000000000000000       0x10 emodules/fm/dev_fm.o
 .text          0x0000000000000000        0x0 emodules/fm/drv_fm.o
 .data          0x0000000000000000        0x0 emodules/fm/drv_fm.o
 .bss           0x0000000000000000        0x0 emodules/fm/drv_fm.o
 .text          0x0000000000000000        0x0 emodules/fm/magic.o
 .data          0x0000000000000000        0x0 emodules/fm/magic.o
 .bss           0x0000000000000000        0x0 emodules/fm/magic.o
 .text          0x0000000000000000        0x0 ./elibrary/bin//libsyscall.a(syscall.o)
 .data          0x0000000000000000        0x0 ./elibrary/bin//libsyscall.a(syscall.o)
 .bss           0x0000000000000000        0x0 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esCFG_Exit_Ex
                0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esCFG_GetGPIOSecData
                0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esCFG_GetGPIOSecData_Ex
                0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esCFG_GetGPIOSecKeyCount
                0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esCFG_GetGPIOSecKeyCount_Ex
                0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esCFG_GetKeyValue
                0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esCFG_GetKeyValue_Ex
                0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esCFG_GetSecCount
                0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esCFG_GetSecCount_Ex
                0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esCFG_GetSecKeyCount
                0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esCFG_GetSecKeyCount_Ex
                0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esCFG_Init_Ex
                0x0000000000000000       0x16 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esCHS_Char2Uni
                0x0000000000000000       0x1c ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esCHS_GetChLowerTbl
                0x0000000000000000       0x1c ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esCHS_GetChUpperTbl
                0x0000000000000000       0x1c ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esCHS_Uni2Char
                0x0000000000000000       0x1c ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esCLK_CloseMclk
                0x0000000000000000       0x1c ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esCLK_GetMclkDiv
                0x0000000000000000       0x1c ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esCLK_GetMclkSrc
                0x0000000000000000       0x1c ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esCLK_GetRound_Rate
                0x0000000000000000       0x20 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esCLK_GetSrcFreq
                0x0000000000000000       0x1c ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esCLK_MclkAssert
                0x0000000000000000       0x1c ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esCLK_MclkDeassert
                0x0000000000000000       0x1c ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esCLK_MclkGetRstStatus
                0x0000000000000000       0x1c ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esCLK_MclkOnOff
                0x0000000000000000       0x20 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esCLK_MclkRegCb
                0x0000000000000000       0x1c ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esCLK_MclkReset
                0x0000000000000000       0x1c ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esCLK_MclkUnregCb
                0x0000000000000000       0x1c ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esCLK_ModInfo
                0x0000000000000000       0x16 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esCLK_OpenMclk
                0x0000000000000000       0x1c ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esCLK_SetMclkDiv
                0x0000000000000000       0x20 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esCLK_SetMclkSrc
                0x0000000000000000       0x20 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esCLK_SetSrcFreq
                0x0000000000000000       0x20 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esCLK_SysInfo
                0x0000000000000000       0x16 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esDEV_Close
                0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esDEV_DevReg
                0x0000000000000000       0x16 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esDEV_DevUnreg
                0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esDEV_Insmod
                0x0000000000000000       0x1c ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esDEV_Ioctl
                0x0000000000000000       0x1c ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esDEV_Lock
                0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esDEV_Open
                0x0000000000000000       0x1a ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esDEV_Plugin
                0x0000000000000000       0x1c ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esDEV_Plugout
                0x0000000000000000       0x1c ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esDEV_Read
                0x0000000000000000       0x20 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esDEV_Unimod
                0x0000000000000000       0x1c ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esDEV_Unlock
                0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esDEV_Write
                0x0000000000000000       0x20 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esDMA_ChangeMode
                0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esDMA_DisableINT
                0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esDMA_EnableINT
                0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esDMA_Information
                0x0000000000000000       0x16 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esDMA_QueryDst
                0x0000000000000000       0x16 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esDMA_QueryRestCount
                0x0000000000000000       0x16 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esDMA_QuerySrc
                0x0000000000000000       0x16 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esDMA_QueryStat
                0x0000000000000000       0x1a ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esDMA_RegDmaHdler
                0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esDMA_Release
                0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esDMA_Request
                0x0000000000000000       0x16 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esDMA_Restart
                0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esDMA_Setting
                0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esDMA_Start
                0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esDMA_Stop
                0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esDMA_UnregDmaHdler
                0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esEXEC_PCreate
                0x0000000000000000       0x1e ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esEXEC_PDel
                0x0000000000000000       0x1a ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esEXEC_PDelReq
                0x0000000000000000       0x1a ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esEXEC_Run
                0x0000000000000000       0x1e ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esFSYS_clearpartupdateflag
                0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esFSYS_closedir
                0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esFSYS_fclose
                0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esFSYS_fd2file
                0x0000000000000000       0x16 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esFSYS_ferrclr
                0x0000000000000000       0x16 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esFSYS_ferror
                0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esFSYS_file2fd
                0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esFSYS_fioctrl
                0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esFSYS_fopen
                0x0000000000000000       0x16 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esFSYS_format
                0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esFSYS_fread
                0x0000000000000000       0x20 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esFSYS_fsdbg
                0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esFSYS_fseek
                0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esFSYS_fseekex
                0x0000000000000000       0x16 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esFSYS_fsreg
                0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esFSYS_fstat
                0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esFSYS_fsunreg
                0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esFSYS_fsync
                0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esFSYS_ftell
                0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esFSYS_ftellex
                0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esFSYS_ftruncate
                0x0000000000000000       0x1c ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esFSYS_fwrite
                0x0000000000000000       0x20 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esFSYS_getfscharset
                0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esFSYS_mkdir
                0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esFSYS_mntfs
                0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esFSYS_mntparts
                0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esFSYS_open
                0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esFSYS_opendir
                0x0000000000000000       0x16 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esFSYS_partfslck
                0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esFSYS_partfsunlck
                0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esFSYS_pclose
                0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esFSYS_pdreg
                0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esFSYS_pdunreg
                0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esFSYS_perrclr
                0x0000000000000000       0x16 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esFSYS_perror
                0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esFSYS_pioctrl
                0x0000000000000000       0x1c ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esFSYS_popen
                0x0000000000000000       0x16 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esFSYS_pread
                0x0000000000000000       0x20 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esFSYS_premove
                0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esFSYS_prename
                0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esFSYS_pwrite
                0x0000000000000000       0x20 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esFSYS_querypartupdateflag
                0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esFSYS_readdir
                0x0000000000000000       0x16 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esFSYS_remove
                0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esFSYS_rename
                0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esFSYS_rewinddir
                0x0000000000000000       0x16 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esFSYS_rmdir
                0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esFSYS_setfs
                0x0000000000000000       0x1c ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esFSYS_statfs
                0x0000000000000000       0x1c ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esFSYS_statpt
                0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esFSYS_umntfs
                0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esFSYS_umntparts
                0x0000000000000000       0x1c ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esHID_hiddevreg
                0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esHID_hiddevunreg
                0x0000000000000000       0x1c ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esHID_SendMsg
                0x0000000000000000       0x1c ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esINPUT_GetLdevID
                0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esINPUT_LdevCtl
                0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esINPUT_LdevFeedback
                0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esINPUT_LdevGrab
                0x0000000000000000       0x16 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esINPUT_LdevRelease
                0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esINPUT_RegDev
                0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esINPUT_SendEvent
                0x0000000000000000       0x20 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esINPUT_UnregDev
                0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esINT_DisableINT
                0x0000000000000000       0x1c ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esINT_EnableINT
                0x0000000000000000       0x1c ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esINT_InsFIR
                0x0000000000000000       0x1c ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esINT_InsISR
                0x0000000000000000       0x1c ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esINT_SetIRQPrio
                0x0000000000000000       0x20 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esINT_UniFIR
                0x0000000000000000       0x1c ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esINT_UniISR
                0x0000000000000000       0x1c ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esKRNL_CallBack
                0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esKRNL_DumpStack
                0x0000000000000000       0x16 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esKRNL_FlagAccept
                0x0000000000000000       0x1c ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esKRNL_FlagCreate
                0x0000000000000000       0x1a ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esKRNL_FlagDel
                0x0000000000000000       0x16 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esKRNL_FlagNameGet
                0x0000000000000000       0x1a ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esKRNL_FlagNameSet
                0x0000000000000000       0x16 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esKRNL_FlagPend
                0x0000000000000000       0x20 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esKRNL_FlagPendGetFlagsRdy
                0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esKRNL_FlagPost
                0x0000000000000000       0x1c ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esKRNL_FlagQuery
                0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esKRNL_GetCallBack
                0x0000000000000000       0x16 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esKRNL_GetTIDCur
                0x0000000000000000       0x16 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esKRNL_InterruptDisable
                0x0000000000000000       0x16 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esKRNL_InterruptEnable
                0x0000000000000000       0x16 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esKRNL_MboxAccept
                0x0000000000000000       0x16 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esKRNL_MboxCreate
                0x0000000000000000       0x1a ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esKRNL_MboxDel
                0x0000000000000000       0x16 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esKRNL_MboxPend
                0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esKRNL_MboxPost
                0x0000000000000000       0x1a ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esKRNL_MboxPostOpt
                0x0000000000000000       0x1e ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esKRNL_MboxQuery
                0x0000000000000000       0x1a ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esKRNL_MemLeakChk
                0x0000000000000000       0x1a ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esKRNL_MutexCreate
                0x0000000000000000       0x16 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esKRNL_MutexDel
                0x0000000000000000       0x16 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esKRNL_MutexPend
                0x0000000000000000       0x16 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esKRNL_MutexPost
                0x0000000000000000       0x1a ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esKRNL_QAccept
                0x0000000000000000       0x16 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esKRNL_QCreate
                0x0000000000000000       0x16 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esKRNL_QDel
                0x0000000000000000       0x16 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esKRNL_QFlush
                0x0000000000000000       0x1a ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esKRNL_QPend
                0x0000000000000000       0x16 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esKRNL_QPost
                0x0000000000000000       0x1a ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esKRNL_QPostFront
                0x0000000000000000       0x1a ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esKRNL_QPostOpt
                0x0000000000000000       0x1a ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esKRNL_QQuery
                0x0000000000000000       0x1a ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esKRNL_SchedLock
                0x0000000000000000       0x16 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esKRNL_SchedUnlock
                0x0000000000000000       0x16 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esKRNL_SemAccept
                0x0000000000000000       0x1a ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esKRNL_SemQuery
                0x0000000000000000       0x1a ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esKRNL_SemSet
                0x0000000000000000       0x16 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esKRNL_SktAccept
                0x0000000000000000       0x16 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esKRNL_SktCreate
                0x0000000000000000       0x22 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esKRNL_SktDel
                0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esKRNL_SktPend
                0x0000000000000000       0x1a ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esKRNL_SktPost
                0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esKRNL_TaskPrefEn
                0x0000000000000000       0x1a ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esKRNL_TaskQuery
                0x0000000000000000       0x1e ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esKRNL_TaskResume
                0x0000000000000000       0x1e ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esKRNL_TaskNameSet
                0x0000000000000000       0x1a ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esKRNL_TaskSuspend
                0x0000000000000000       0x1e ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esKRNL_Time
                0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esKRNL_TimeDlyResume
                0x0000000000000000       0x1e ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esKRNL_TimeSet
                0x0000000000000000       0x1a ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esKRNL_TmrCreate
                0x0000000000000000       0x1a ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esKRNL_TmrDel
                0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esKRNL_TmrError
                0x0000000000000000       0x1a ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esKRNL_TmrRemainGet
                0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esKRNL_TmrStart
                0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esKRNL_TmrStateGet
                0x0000000000000000       0x1a ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esKRNL_TmrStop
                0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esKRNL_Version
                0x0000000000000000       0x1a ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esKRNL_Ioctrl
                0x0000000000000000       0x16 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esKSRV_ClearWatchDog
                0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esKSRV_DisableWatchDog
                0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esKSRV_DisableWatchDogSrv
                0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esKSRV_EnableWatchDog
                0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esKSRV_EnableWatchDogSrv
                0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esKSRV_GetAddPara
                0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esKSRV_GetDramCfgPara
                0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esKSRV_GetHighMsg
                0x0000000000000000       0x16 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esKSRV_GetLowMsg
                0x0000000000000000       0x16 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esKSRV_GetMsg
                0x0000000000000000       0x16 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esKSRV_GetPara
                0x0000000000000000       0x1c ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esKSRV_GetSocID
                0x0000000000000000       0x1a ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esKSRV_GetTargetPara
                0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esKSRV_GetVersion
                0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esKSRV_Get_Display_Hld
                0x0000000000000000       0x16 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esKSRV_Save_Mixture_Hld
                0x0000000000000000       0x16 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esKSRV_Get_Mixture_Hld
                0x0000000000000000       0x16 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esKSRV_Save_Raw_Decoder_Hld
                0x0000000000000000       0x16 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esKSRV_Get_Raw_Decoder_Hld
                0x0000000000000000       0x16 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esKSRV_memcpy
                0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esKSRV_memset
                0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esKSRV_PowerOff
                0x0000000000000000       0x16 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esKSRV_Random
                0x0000000000000000       0x1c ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esKSRV_Reset
                0x0000000000000000       0x16 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esKSRV_Save_Display_Hld
                0x0000000000000000       0x16 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esKSRV_SendMsgEx
                0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esKSRV_SysInfo
                0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esMEMS_Balloc
                0x0000000000000000       0x16 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esMEMS_Bfree
                0x0000000000000000       0x16 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esMEMS_Calloc
                0x0000000000000000       0x1e ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esMEMS_CleanDCache
                0x0000000000000000       0x16 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esMEMS_CleanDCacheRegion
                0x0000000000000000       0x1a ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esMEMS_CleanFlushCache
                0x0000000000000000       0x16 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esMEMS_CleanFlushCacheRegion
                0x0000000000000000       0x1a ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esMEMS_CleanFlushDCache
                0x0000000000000000       0x16 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esMEMS_CleanFlushDCacheRegion
                0x0000000000000000       0x1a ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esMEMS_CleanInvalidateCacheAll
                0x0000000000000000       0x16 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esMEMS_FlushCache
                0x0000000000000000       0x16 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esMEMS_FlushCacheRegion
                0x0000000000000000       0x1a ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esMEMS_FlushDCache
                0x0000000000000000       0x16 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esMEMS_FlushDCacheRegion
                0x0000000000000000       0x1a ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esMEMS_FlushICache
                0x0000000000000000       0x16 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esMEMS_FlushICacheRegion
                0x0000000000000000       0x1a ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esMEMS_FreeMemSize
                0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esMEMS_GetIoVAByPA
                0x0000000000000000       0x1c ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esMEMS_HeapCreate
                0x0000000000000000       0x1c ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esMEMS_HeapDel
                0x0000000000000000       0x16 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esMEMS_Info
                0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esMEMS_Palloc
                0x0000000000000000       0x1e ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esMEMS_Pfree
                0x0000000000000000       0x1a ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esMEMS_Realloc
                0x0000000000000000       0x1a ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esMEMS_TotalMemSize
                0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esMEMS_VA2PA
                0x0000000000000000       0x16 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esMEMS_VMalloc
                0x0000000000000000       0x16 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esMEMS_VMCreate
                0x0000000000000000       0x1c ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esMEMS_VMDelete
                0x0000000000000000       0x1a ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esMEMS_VMfree
                0x0000000000000000       0x16 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esMEM_BWDisable
                0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esMEM_BWEnable
                0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esMEM_BWGet
                0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esMEM_DramSuspend
                0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esMEM_DramWakeup
                0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esMEM_MasterGet
                0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esMEM_MasterSet
                0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esMEM_RegDramAccess
                0x0000000000000000       0x16 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esMEM_ReleaseDramUsrMode
                0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esMEM_RequestDramUsrMode
                0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esMEM_SramRelBlk
                0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esMEM_SramReqBlk
                0x0000000000000000       0x16 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esMEM_SramSwitchBlk
                0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esMEM_UnRegDramAccess
                0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esMODS_MClose
                0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esMODS_MInstall
                0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esMODS_MIoctrl
                0x0000000000000000       0x1a ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esMODS_MOpen
                0x0000000000000000       0x16 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esMODS_MRead
                0x0000000000000000       0x20 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esMODS_MUninstall
                0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esMODS_MWrite
                0x0000000000000000       0x20 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esMSTUB_GetFuncEntry
                0x0000000000000000       0x1e ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esMSTUB_RegFuncTbl
                0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esMSTUB_UnRegFuncTbl
                0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esPINS_ClearPending
                0x0000000000000000       0x16 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esPINS_DisbaleInt
                0x0000000000000000       0x16 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esPINS_EnbaleInt
                0x0000000000000000       0x16 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esPINS_GetPinGrpStat
                0x0000000000000000       0x1a ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esPINS_GetPinStat
                0x0000000000000000       0x16 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esPINS_PinGrpRel
                0x0000000000000000       0x16 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esPINS_PinGrpReq
                0x0000000000000000       0x16 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esPINS_QueryInt
                0x0000000000000000       0x16 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esPINS_ReadPinData
                0x0000000000000000       0x16 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esPINS_RegIntHdler
                0x0000000000000000       0x16 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esPINS_SetIntMode
                0x0000000000000000       0x1a ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esPINS_SetPinDrive
                0x0000000000000000       0x1a ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esPINS_SetPinIO
                0x0000000000000000       0x16 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esPINS_SetPinPull
                0x0000000000000000       0x1a ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esPINS_SetPinStat
                0x0000000000000000       0x16 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esPINS_UnregIntHdler
                0x0000000000000000       0x16 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esPINS_WritePinData
                0x0000000000000000       0x1a ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esPWRMAN_EnterStandby
                0x0000000000000000       0x1a ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esPWRMAN_GetStandbyPara
                0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esPWRMAN_LockCpuFreq
                0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esPWRMAN_RegDevice
                0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esPWRMAN_RelPwrmanMode
                0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esPWRMAN_ReqPwrmanMode
                0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esPWRMAN_SetStandbyMode
                0x0000000000000000       0x1c ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esPWRMAN_UnlockCpuFreq
                0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esPWRMAN_UnregDevice
                0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esPWRMAN_UsrEventNotify
                0x0000000000000000       0x16 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esRESM_RClose
                0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esRESM_ROpen
                0x0000000000000000       0x16 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esRESM_RRead
                0x0000000000000000       0x20 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esRESM_RSeek
                0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esSIOS_getchar
                0x0000000000000000       0x16 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esSIOS_gets
                0x0000000000000000       0x14 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esSIOS_putarg
                0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esSIOS_setbaud
                0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esSVC_RegCloseNode
                0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esSVC_RegClrError
                0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esSVC_RegCreateKey
                0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esSVC_RegCreatePath
                0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esSVC_RegCreateSet
                0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esSVC_RegDeleteKey
                0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esSVC_RegDeleteNode
                0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esSVC_RegDeletePath
                0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esSVC_RegDeleteSet
                0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esSVC_RegGetError
                0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esSVC_RegGetFirstKey
                0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esSVC_RegGetFirstSet
                0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esSVC_RegGetKeyCount
                0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esSVC_RegGetKeyValue
                0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esSVC_RegGetNextKey
                0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esSVC_RegGetNextSet
                0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esSVC_RegGetRootPath
                0x0000000000000000       0x16 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esSVC_RegGetSetCount
                0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esSVC_RegIni2Reg
                0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esSVC_RegOpenNode
                0x0000000000000000       0x16 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esSVC_RegReg2Ini
                0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esSVC_RegSetKeyValue
                0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esSVC_RegSetRootPath
                0x0000000000000000       0x16 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esSVC_ResourceRel
                0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esSVC_ResourceReq
                0x0000000000000000       0x22 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esTIME_ContiCntr
                0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esTIME_GetDate
                0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esTIME_GetTime
                0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esTIME_PauseCntr
                0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esTIME_QuerryAlarm
                0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esTIME_QuerryCntr
                0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esTIME_QuerryCntrStat
                0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esTIME_QuerryTimer
                0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esTIME_ReleaseAlarm
                0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esTIME_ReleaseCntr
                0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esTIME_ReleaseTimer
                0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esTIME_RequestAlarm
                0x0000000000000000       0x1a ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esTIME_RequestCntr
                0x0000000000000000       0x16 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esTIME_RequestTimer
                0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esTIME_SetCntrPrescale
                0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esTIME_SetCntrValue
                0x0000000000000000       0x1c ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esTIME_SetDate
                0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esTIME_SetTime
                0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esTIME_StartAlarm
                0x0000000000000000       0x1c ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esTIME_StartCntr
                0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esTIME_StartTimer
                0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esTIME_StopAlarm
                0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esTIME_StopCntr
                0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esTIME_StopTimer
                0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.backtrace
                0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.select   0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.mmap     0x0000000000000000       0x16 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.munmap   0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.poll     0x0000000000000000       0x1c ./elibrary/bin//libsyscall.a(syscall.o)
 .text.pipe     0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall.o)
 .text          0x0000000000000000        0x0 ./elibrary/bin//libsyscall.a(syscall_fops.o)
 .data          0x0000000000000000        0x0 ./elibrary/bin//libsyscall.a(syscall_fops.o)
 .bss           0x0000000000000000        0x0 ./elibrary/bin//libsyscall.a(syscall_fops.o)
 .text.dup      0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall_fops.o)
 .text.dup2     0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall_fops.o)
 .text.closedir
                0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall_fops.o)
 .text.fioctrl  0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall_fops.o)
 .text._fstat_r
                0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall_fops.o)
 .text._lseek_r
                0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall_fops.o)
 .text._rename_r
                0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall_fops.o)
 .text._stat_r  0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall_fops.o)
 .text._mkdir_r
                0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall_fops.o)
 .text.fsync    0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall_fops.o)
 .text._unlink_r
                0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall_fops.o)
 .text._read_r  0x0000000000000000       0x16 ./elibrary/bin//libsyscall.a(syscall_fops.o)
 .text._write_r
                0x0000000000000000       0x16 ./elibrary/bin//libsyscall.a(syscall_fops.o)
 .text._gettimeofday_r
                0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall_fops.o)
 .text.settimeofday
                0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall_fops.o)
 .text._malloc_r
                0x0000000000000000       0x16 ./elibrary/bin//libsyscall.a(syscall_fops.o)
 .text._realloc_r
                0x0000000000000000       0x16 ./elibrary/bin//libsyscall.a(syscall_fops.o)
 .text._calloc_r
                0x0000000000000000       0x16 ./elibrary/bin//libsyscall.a(syscall_fops.o)
 .text._free_r  0x0000000000000000       0x16 ./elibrary/bin//libsyscall.a(syscall_fops.o)
 .text._isatty_r
                0x0000000000000000       0x16 ./elibrary/bin//libsyscall.a(syscall_fops.o)
 .text._isatty  0x0000000000000000       0x16 ./elibrary/bin//libsyscall.a(syscall_fops.o)
 .text.__libc_init_array
                0x0000000000000000       0x16 ./elibrary/bin//libsyscall.a(syscall_fops.o)
 .text._system  0x0000000000000000       0x16 ./elibrary/bin//libsyscall.a(syscall_fops.o)
 .text.__malloc_lock
                0x0000000000000000        0x2 ./elibrary/bin//libsyscall.a(syscall_fops.o)
 .text.__malloc_unlock
                0x0000000000000000        0x2 ./elibrary/bin//libsyscall.a(syscall_fops.o)
 .text.ftruncate
                0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall_fops.o)
 .text.mkdir    0x0000000000000000       0x1c ./elibrary/bin//libsyscall.a(syscall_fops.o)
 .text.opendir  0x0000000000000000       0x16 ./elibrary/bin//libsyscall.a(syscall_fops.o)
 .text.readdir  0x0000000000000000       0x16 ./elibrary/bin//libsyscall.a(syscall_fops.o)
 .text.remove   0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall_fops.o)
 .text.rewinddir
                0x0000000000000000       0x16 ./elibrary/bin//libsyscall.a(syscall_fops.o)
 .text.rmdir    0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall_fops.o)
 .text.statfs   0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall_fops.o)
 .text.access   0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall_fops.o)
 .text          0x0000000000000000        0x0 ./elibrary/bin//libsyscall.a(syscall.o)
 .data          0x0000000000000000        0x0 ./elibrary/bin//libsyscall.a(syscall.o)
 .bss           0x0000000000000000        0x0 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.pthread_attr_destroy
                0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.pthread_attr_init
                0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.pthread_attr_setdetachstate
                0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.pthread_attr_getdetachstate
                0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.pthread_attr_setschedpolicy
                0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.pthread_attr_getschedpolicy
                0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.pthread_attr_setschedparam
                0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.pthread_attr_getschedparam
                0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.pthread_attr_setstacksize
                0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.pthread_attr_getstacksize
                0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.pthread_attr_setstackaddr
                0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.pthread_attr_getstackaddr
                0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.pthread_attr_setstack
                0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.pthread_attr_getstack
                0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.pthread_attr_setguardsize
                0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.pthread_attr_getguardsize
                0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.pthread_attr_setscope
                0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.pthread_attr_getscope
                0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.pthread_system_init
                0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.pthread_create
                0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.pthread_detach
                0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.pthread_join
                0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.pthread_exit
                0x0000000000000000       0x16 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.pthread_once
                0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.pthread_cleanup_pop
                0x0000000000000000       0x16 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.pthread_cleanup_push
                0x0000000000000000       0x16 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.pthread_cancel
                0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.pthread_self
                0x0000000000000000       0x16 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.pthread_testcancel
                0x0000000000000000       0x16 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.pthread_setcancelstate
                0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.pthread_setcanceltype
                0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.pthread_atfork
                0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.pthread_kill
                0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.pthread_mutex_init
                0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.pthread_mutex_destroy
                0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.pthread_mutex_lock
                0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.pthread_mutex_unlock
                0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.pthread_mutex_trylock
                0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.pthread_mutexattr_init
                0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.pthread_mutexattr_destroy
                0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.pthread_mutexattr_gettype
                0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.pthread_mutexattr_settype
                0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.pthread_mutexattr_setpshared
                0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.pthread_mutexattr_getpshared
                0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.pthread_condattr_destroy
                0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.pthread_condattr_init
                0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.pthread_condattr_getclock
                0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.pthread_condattr_setclock
                0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.pthread_cond_init
                0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.pthread_cond_destroy
                0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.pthread_cond_broadcast
                0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.pthread_cond_signal
                0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.pthread_cond_wait
                0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.pthread_cond_timedwait
                0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.pthread_rwlockattr_init
                0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.pthread_rwlockattr_destroy
                0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.pthread_rwlockattr_getpshared
                0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.pthread_rwlockattr_setpshared
                0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.pthread_rwlock_init
                0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.pthread_rwlock_destroy
                0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.pthread_rwlock_rdlock
                0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.pthread_rwlock_tryrdlock
                0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.pthread_rwlock_timedrdlock
                0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.pthread_rwlock_timedwrlock
                0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.pthread_rwlock_unlock
                0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.pthread_rwlock_wrlock
                0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.pthread_rwlock_trywrlock
                0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.pthread_spin_init
                0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.pthread_spin_destroy
                0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.pthread_spin_lock
                0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.pthread_spin_trylock
                0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.pthread_spin_unlock
                0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.pthread_barrierattr_destroy
                0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.pthread_barrierattr_init
                0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.pthread_barrierattr_getpshared
                0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.pthread_barrierattr_setpshared
                0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.pthread_barrier_destroy
                0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.pthread_barrier_init
                0x0000000000000000       0x1c ./elibrary/bin//libsyscall.a(syscall.o)
 .text.pthread_barrier_wait
                0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.pthread_getspecific
                0x0000000000000000       0x16 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.pthread_setspecific
                0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.pthread_key_create
                0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.pthread_key_delete
                0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.pthread_setname_np
                0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.sem_close
                0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.sem_destroy
                0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.sem_getvalue
                0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.sem_init
                0x0000000000000000       0x1c ./elibrary/bin//libsyscall.a(syscall.o)
 .text.sem_open
                0x0000000000000000       0x22 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.sem_post
                0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.sem_timedwait
                0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.sem_trywait
                0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.sem_unlink
                0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.sem_wait
                0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.clock_settime
                0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.clock_gettime
                0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.clock_getres
                0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.msleep   0x0000000000000000       0x1c ./elibrary/bin//libsyscall.a(syscall.o)
 .text.sleep    0x0000000000000000       0x1c ./elibrary/bin//libsyscall.a(syscall.o)
 .text          0x0000000000000000        0x0 ./elibrary/bin//libminic.a(elibs_stdio.o)
 .data          0x0000000000000000        0x0 ./elibrary/bin//libminic.a(elibs_stdio.o)
 .bss           0x0000000000000000        0x0 ./elibrary/bin//libminic.a(elibs_stdio.o)
 .text.eLIBs_remove
                0x0000000000000000        0x8 ./elibrary/bin//libminic.a(elibs_stdio.o)
 .text.eLIBs_rename
                0x0000000000000000        0x8 ./elibrary/bin//libminic.a(elibs_stdio.o)
 .text.eLIBs_fopen
                0x0000000000000000        0x8 ./elibrary/bin//libminic.a(elibs_stdio.o)
 .text.eLIBs_fclose
                0x0000000000000000        0x8 ./elibrary/bin//libminic.a(elibs_stdio.o)
 .text.eLIBs_fsync
                0x0000000000000000        0x8 ./elibrary/bin//libminic.a(elibs_stdio.o)
 .text.eLIBs_fread
                0x0000000000000000        0x8 ./elibrary/bin//libminic.a(elibs_stdio.o)
 .text.eLIBs_fwrite
                0x0000000000000000        0x8 ./elibrary/bin//libminic.a(elibs_stdio.o)
 .text.eLIBs_ftruncate
                0x0000000000000000        0x8 ./elibrary/bin//libminic.a(elibs_stdio.o)
 .text.eLIBs_fseek
                0x0000000000000000       0x16 ./elibrary/bin//libminic.a(elibs_stdio.o)
 .text.eLIBs_fllseek
                0x0000000000000000       0x1e ./elibrary/bin//libminic.a(elibs_stdio.o)
 .text.eLIBs_ftell
                0x0000000000000000        0x8 ./elibrary/bin//libminic.a(elibs_stdio.o)
 .text.eLIBs_flltell
                0x0000000000000000       0x26 ./elibrary/bin//libminic.a(elibs_stdio.o)
 .text.eLIBs_fioctrl
                0x0000000000000000        0x8 ./elibrary/bin//libminic.a(elibs_stdio.o)
 .text.eLIBs_ferror
                0x0000000000000000        0x8 ./elibrary/bin//libminic.a(elibs_stdio.o)
 .text.eLIBs_ferrclr
                0x0000000000000000        0x8 ./elibrary/bin//libminic.a(elibs_stdio.o)
 .text.eLIBs_opendir
                0x0000000000000000        0x8 ./elibrary/bin//libminic.a(elibs_stdio.o)
 .text.eLIBs_closedir
                0x0000000000000000        0x8 ./elibrary/bin//libminic.a(elibs_stdio.o)
 .text.eLIBs_readdir
                0x0000000000000000        0x8 ./elibrary/bin//libminic.a(elibs_stdio.o)
 .text.eLIBs_rewinddir
                0x0000000000000000        0x8 ./elibrary/bin//libminic.a(elibs_stdio.o)
 .text.eLIBs_mkdir
                0x0000000000000000        0x8 ./elibrary/bin//libminic.a(elibs_stdio.o)
 .text.eLIBs_rmdir
                0x0000000000000000        0x8 ./elibrary/bin//libminic.a(elibs_stdio.o)
 .text.eLIBs_DirEnt2Attr
                0x0000000000000000        0x8 ./elibrary/bin//libminic.a(elibs_stdio.o)
 .text.eLIBs_DirEnt2Name
                0x0000000000000000       0x2e ./elibrary/bin//libminic.a(elibs_stdio.o)
 .text.eLIBs_DirEnt2Size
                0x0000000000000000        0x6 ./elibrary/bin//libminic.a(elibs_stdio.o)
 .text.eLIBs_DirEnt2Time
                0x0000000000000000        0x4 ./elibrary/bin//libminic.a(elibs_stdio.o)
 .text.eLIBs_GetNumFiles
                0x0000000000000000       0x3e ./elibrary/bin//libminic.a(elibs_stdio.o)
 .text.eLIBs_GetNumSubItems
                0x0000000000000000       0x4e ./elibrary/bin//libminic.a(elibs_stdio.o)
 .text.eLIBs_GetFileAttributes
                0x0000000000000000       0x4c ./elibrary/bin//libminic.a(elibs_stdio.o)
 .text.eLIBs_SetFileAttributes
                0x0000000000000000        0x4 ./elibrary/bin//libminic.a(elibs_stdio.o)
 .text.eLIBs_GetDirSize
                0x0000000000000000      0x108 ./elibrary/bin//libminic.a(elibs_stdio.o)
 .text.eLIBs_GetFileSize
                0x0000000000000000       0x40 ./elibrary/bin//libminic.a(elibs_stdio.o)
 .text.eLIBs_format
                0x0000000000000000        0x8 ./elibrary/bin//libminic.a(elibs_stdio.o)
 .text.eLIBs_GetVolName
                0x0000000000000000       0x80 ./elibrary/bin//libminic.a(elibs_stdio.o)
 .text.eLIBs_GetVolNameLen
                0x0000000000000000       0x50 ./elibrary/bin//libminic.a(elibs_stdio.o)
 .text.eLIBs_GetVolNameCharset
                0x0000000000000000       0x52 ./elibrary/bin//libminic.a(elibs_stdio.o)
 .text.eLIBs_GetVolTSpace
                0x0000000000000000       0x30 ./elibrary/bin//libminic.a(elibs_stdio.o)
 .text.eLIBs_GetVolFSpace
                0x0000000000000000       0x30 ./elibrary/bin//libminic.a(elibs_stdio.o)
 .text.eLIBs_GetVolClustSize
                0x0000000000000000       0x26 ./elibrary/bin//libminic.a(elibs_stdio.o)
 .text.eLIBs_GetFSName
                0x0000000000000000       0x80 ./elibrary/bin//libminic.a(elibs_stdio.o)
 .text.eLIBs_GetFSNameLen
                0x0000000000000000       0x50 ./elibrary/bin//libminic.a(elibs_stdio.o)
 .text.eLIBs_GetFSAttribute
                0x0000000000000000       0x36 ./elibrary/bin//libminic.a(elibs_stdio.o)
 .text.eLIBs_GetPTName
                0x0000000000000000       0x62 ./elibrary/bin//libminic.a(elibs_stdio.o)
 .text.eLIBs_IsPartFormated
                0x0000000000000000       0x9c ./elibrary/bin//libminic.a(elibs_stdio.o)
 .text.eLIBs_disklock
                0x0000000000000000        0x8 ./elibrary/bin//libminic.a(elibs_stdio.o)
 .text.eLIBs_diskunlock
                0x0000000000000000        0x8 ./elibrary/bin//libminic.a(elibs_stdio.o)
 .text.eLIBs_GetDiskArray
                0x0000000000000000       0x62 ./elibrary/bin//libminic.a(elibs_stdio.o)
 .text.eLIBs_feof
                0x0000000000000000       0x1e ./elibrary/bin//libminic.a(elibs_stdio.o)
 .text.eLIBs_fgetc
                0x0000000000000000       0x2c ./elibrary/bin//libminic.a(elibs_stdio.o)
 .text.eLIBs_fgets
                0x0000000000000000       0x6c ./elibrary/bin//libminic.a(elibs_stdio.o)
 .text.eLIBs_fputc
                0x0000000000000000       0x32 ./elibrary/bin//libminic.a(elibs_stdio.o)
 .text.eLIBs_fputs
                0x0000000000000000       0x42 ./elibrary/bin//libminic.a(elibs_stdio.o)
 .text.eLIBs_GetFileATime
                0x0000000000000000       0x50 ./elibrary/bin//libminic.a(elibs_stdio.o)
 .text.eLIBs_GetFileMTime
                0x0000000000000000       0x50 ./elibrary/bin//libminic.a(elibs_stdio.o)
 .text.eLIBs_GetFileCTime
                0x0000000000000000       0x50 ./elibrary/bin//libminic.a(elibs_stdio.o)
 .text.eLIBs_GetDirATime
                0x0000000000000000       0x48 ./elibrary/bin//libminic.a(elibs_stdio.o)
 .text.eLIBs_GetDirMTime
                0x0000000000000000       0x48 ./elibrary/bin//libminic.a(elibs_stdio.o)
 .text.eLIBs_GetDirCTime
                0x0000000000000000       0x48 ./elibrary/bin//libminic.a(elibs_stdio.o)
 .text.eLIBs_GetFSCharset
                0x0000000000000000       0x10 ./elibrary/bin//libminic.a(elibs_stdio.o)
 .text.eLIBs_file2fd
                0x0000000000000000        0x8 ./elibrary/bin//libminic.a(elibs_stdio.o)
 .text.eLIBs_fd2file
                0x0000000000000000        0x8 ./elibrary/bin//libminic.a(elibs_stdio.o)
 .text.eLIBs_open
                0x0000000000000000        0x8 ./elibrary/bin//libminic.a(elibs_stdio.o)
 .text.eLIBs_fstat
                0x0000000000000000        0x8 ./elibrary/bin//libminic.a(elibs_stdio.o)
 .text.eLIBs_statfs
                0x0000000000000000        0xc ./elibrary/bin//libminic.a(elibs_stdio.o)
 .text.eLIBs_GetDevName
                0x0000000000000000       0x6a ./elibrary/bin//libminic.a(elibs_stdio.o)
 .text.eLIBs_GetDevParts
                0x0000000000000000       0x88 ./elibrary/bin//libminic.a(elibs_stdio.o)
 .text.eLIBs_GetTime
                0x0000000000000000        0x8 ./elibrary/bin//libminic.a(elibs_stdio.o)
 .text.eLIBs_GetDate
                0x0000000000000000        0x8 ./elibrary/bin//libminic.a(elibs_stdio.o)
 .text.eLIBs_setfs
                0x0000000000000000       0x14 ./elibrary/bin//libminic.a(elibs_stdio.o)
 .bss.dir_level.6470
                0x0000000000000000        0x4 ./elibrary/bin//libminic.a(elibs_stdio.o)
 .rodata.eLIBs_GetDirSize.str1.8
                0x0000000000000000        0x2 ./elibrary/bin//libminic.a(elibs_stdio.o)
 .rodata.eLIBs_GetFileAttributes.str1.8
                0x0000000000000000        0x3 ./elibrary/bin//libminic.a(elibs_stdio.o)
 .rodata.eLIBs_GetVolNameCharset.str1.8
                0x0000000000000000        0x5 ./elibrary/bin//libminic.a(elibs_stdio.o)
 .text          0x0000000000000000        0x0 ./elibrary/bin//libminic.a(elibs_string.o)
 .data          0x0000000000000000        0x0 ./elibrary/bin//libminic.a(elibs_string.o)
 .bss           0x0000000000000000        0x0 ./elibrary/bin//libminic.a(elibs_string.o)
 .text.eLIBs_strlen
                0x0000000000000000        0xc ./elibrary/bin//libminic.a(elibs_string.o)
 .text.eLIBs_strnlen
                0x0000000000000000        0xc ./elibrary/bin//libminic.a(elibs_string.o)
 .text.eLIBs_strcpy
                0x0000000000000000        0xc ./elibrary/bin//libminic.a(elibs_string.o)
 .text.eLIBs_strncpy
                0x0000000000000000        0xc ./elibrary/bin//libminic.a(elibs_string.o)
 .text.eLIBs_strcat
                0x0000000000000000        0xc ./elibrary/bin//libminic.a(elibs_string.o)
 .text.eLIBs_strncat
                0x0000000000000000        0xc ./elibrary/bin//libminic.a(elibs_string.o)
 .text.eLIBs_strcmp
                0x0000000000000000        0xc ./elibrary/bin//libminic.a(elibs_string.o)
 .text.eLIBs_stricmp
                0x0000000000000000        0xc ./elibrary/bin//libminic.a(elibs_string.o)
 .text.eLIBs_strncmp
                0x0000000000000000        0xc ./elibrary/bin//libminic.a(elibs_string.o)
 .text.eLIBs_strnicmp
                0x0000000000000000        0xc ./elibrary/bin//libminic.a(elibs_string.o)
 .text.eLIBs_strchr
                0x0000000000000000        0xc ./elibrary/bin//libminic.a(elibs_string.o)
 .text.eLIBs_strnchr
                0x0000000000000000        0xc ./elibrary/bin//libminic.a(elibs_string.o)
 .text.eLIBs_strchrlast
                0x0000000000000000        0xc ./elibrary/bin//libminic.a(elibs_string.o)
 .text.eLIBs_strstr
                0x0000000000000000        0xc ./elibrary/bin//libminic.a(elibs_string.o)
 .text.eLIBs_memclr
                0x0000000000000000       0x10 ./elibrary/bin//libminic.a(elibs_string.o)
 .text.eLIBs_memcpy
                0x0000000000000000        0xc ./elibrary/bin//libminic.a(elibs_string.o)
 .text.eLIBs_memcmp
                0x0000000000000000       0x18 ./elibrary/bin//libminic.a(elibs_string.o)
 .text.eLIBs_memmove
                0x0000000000000000       0x3c ./elibrary/bin//libminic.a(elibs_string.o)
 .text          0x0000000000000000        0x0 ./elibrary/bin//libminic.a(elibs_sprintf.o)
 .data          0x0000000000000000        0x0 ./elibrary/bin//libminic.a(elibs_sprintf.o)
 .bss           0x0000000000000000        0x0 ./elibrary/bin//libminic.a(elibs_sprintf.o)
 .text.eLIBs_snprintf
                0x0000000000000000       0x20 ./elibrary/bin//libminic.a(elibs_sprintf.o)
 .text.eLIBs_scnprintf
                0x0000000000000000       0x2e ./elibrary/bin//libminic.a(elibs_sprintf.o)
 .text.eLIBs_vsprintf
                0x0000000000000000        0xe ./elibrary/bin//libminic.a(elibs_sprintf.o)
 .text.eLIBs_sprintf
                0x0000000000000000       0x26 ./elibrary/bin//libminic.a(elibs_sprintf.o)
 .text          0x0000000000000000       0x50 /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/../../../../riscv64-unknown-elf/lib/rv64imafdcxtheadc/lp64d/libc.a(lib_a-malloc.o)
 .data          0x0000000000000000        0x0 /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/../../../../riscv64-unknown-elf/lib/rv64imafdcxtheadc/lp64d/libc.a(lib_a-malloc.o)
 .bss           0x0000000000000000        0x0 /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/../../../../riscv64-unknown-elf/lib/rv64imafdcxtheadc/lp64d/libc.a(lib_a-malloc.o)
 .comment       0x0000000000000000       0x33 /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/../../../../riscv64-unknown-elf/lib/rv64imafdcxtheadc/lp64d/libc.a(lib_a-malloc.o)
 .riscv.attributes
                0x0000000000000000       0x3e /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/../../../../riscv64-unknown-elf/lib/rv64imafdcxtheadc/lp64d/libc.a(lib_a-malloc.o)
 .data          0x0000000000000000        0x0 /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/../../../../riscv64-unknown-elf/lib/rv64imafdcxtheadc/lp64d/libc.a(lib_a-memcpy.o)
 .bss           0x0000000000000000        0x0 /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/../../../../riscv64-unknown-elf/lib/rv64imafdcxtheadc/lp64d/libc.a(lib_a-memcpy.o)
 .data          0x0000000000000000        0x0 /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/../../../../riscv64-unknown-elf/lib/rv64imafdcxtheadc/lp64d/libc.a(lib_a-memset.o)
 .bss           0x0000000000000000        0x0 /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/../../../../riscv64-unknown-elf/lib/rv64imafdcxtheadc/lp64d/libc.a(lib_a-memset.o)
 .data          0x0000000000000000        0x0 /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/../../../../riscv64-unknown-elf/lib/rv64imafdcxtheadc/lp64d/libc.a(lib_a-sysclose.o)
 .bss           0x0000000000000000        0x0 /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/../../../../riscv64-unknown-elf/lib/rv64imafdcxtheadc/lp64d/libc.a(lib_a-sysclose.o)
 .data          0x0000000000000000        0x0 /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/../../../../riscv64-unknown-elf/lib/rv64imafdcxtheadc/lp64d/libc.a(lib_a-sysopen.o)
 .bss           0x0000000000000000        0x0 /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/../../../../riscv64-unknown-elf/lib/rv64imafdcxtheadc/lp64d/libc.a(lib_a-sysopen.o)
 .text          0x0000000000000000       0x30 /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/../../../../riscv64-unknown-elf/lib/rv64imafdcxtheadc/lp64d/libc.a(lib_a-syswrite.o)
 .data          0x0000000000000000        0x0 /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/../../../../riscv64-unknown-elf/lib/rv64imafdcxtheadc/lp64d/libc.a(lib_a-syswrite.o)
 .bss           0x0000000000000000        0x0 /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/../../../../riscv64-unknown-elf/lib/rv64imafdcxtheadc/lp64d/libc.a(lib_a-syswrite.o)
 .comment       0x0000000000000000       0x33 /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/../../../../riscv64-unknown-elf/lib/rv64imafdcxtheadc/lp64d/libc.a(lib_a-syswrite.o)
 .riscv.attributes
                0x0000000000000000       0x3e /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/../../../../riscv64-unknown-elf/lib/rv64imafdcxtheadc/lp64d/libc.a(lib_a-syswrite.o)
 .text          0x0000000000000000        0x0 /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/../../../../riscv64-unknown-elf/lib/rv64imafdcxtheadc/lp64d/libc.a(lib_a-impure.o)
 .bss           0x0000000000000000        0x0 /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/../../../../riscv64-unknown-elf/lib/rv64imafdcxtheadc/lp64d/libc.a(lib_a-impure.o)
 .rodata        0x0000000000000000        0x8 /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/../../../../riscv64-unknown-elf/lib/rv64imafdcxtheadc/lp64d/libc.a(lib_a-impure.o)

Memory Configuration

Name             Origin             Length             Attributes
dram0_0_seg      0x00000000c2000000 0x0000000004000000 xrw
dram0_1_seg      0x00000000e0000000 0x0000000010000000 xrw
dram0_2_seg      0x00000000e0700000 0x0000000000010000 xrw
magic_info_seg   0x00000000ffff0000 0x0000000000000100 r
*default*        0x0000000000000000 0xffffffffffffffff

Linker script and memory map

LOAD emodules/fm/fm_module/si4760/Rds.o
LOAD emodules/fm/fm_module/si4760/Rds_sub.o
LOAD emodules/fm/fm_module/si4760/Tu_Si_476X.o
LOAD emodules/fm/fm_module/si4760/Tuner.o
LOAD emodules/fm/dev_fm.o
LOAD emodules/fm/drv_fm.o
LOAD emodules/fm/magic.o
START GROUP
START GROUP
LOAD ./elibrary/bin//libsyscall.a
LOAD ./elibrary/bin//libminic.a
LOAD ./elibrary/bin//libpub0.a
END GROUP
START GROUP
LOAD ./elibrary/bin//libcheck_app.a
END GROUP
LOAD /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/../../../../riscv64-unknown-elf/lib/rv64imafdcxtheadc/lp64d/libstdc++.a
LOAD /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/../../../../riscv64-unknown-elf/lib/rv64imafdcxtheadc/lp64d/libc.a
LOAD /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/rv64imafdcxtheadc/lp64d/libgcc.a
LOAD /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/../../../../riscv64-unknown-elf/lib/rv64imafdcxtheadc/lp64d/libm.a
END GROUP
                0x00000000e0700000                __DRAM0_MOD_BASE = ORIGIN (dram0_2_seg)
                0x00000000ffff0000                __DRAM0_INF_BASE = ORIGIN (magic_info_seg)

.mod.text       0x00000000e0700000     0x7f94
 *(.text)
 .text          0x00000000e0700000       0xd0 /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/../../../../riscv64-unknown-elf/lib/rv64imafdcxtheadc/lp64d/libc.a(lib_a-memcpy.o)
                0x00000000e0700000                memcpy
 .text          0x00000000e07000d0       0xaa /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/../../../../riscv64-unknown-elf/lib/rv64imafdcxtheadc/lp64d/libc.a(lib_a-memset.o)
                0x00000000e07000d0                memset
 *fill*         0x00000000e070017a        0x6 
 .text          0x00000000e0700180       0x1e /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/../../../../riscv64-unknown-elf/lib/rv64imafdcxtheadc/lp64d/libc.a(lib_a-sysclose.o)
                0x00000000e0700180                close
 *fill*         0x00000000e070019e        0x2 
 .text          0x00000000e07001a0       0x3e /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/../../../../riscv64-unknown-elf/lib/rv64imafdcxtheadc/lp64d/libc.a(lib_a-sysopen.o)
                0x00000000e07001a0                open
 *(.text.*)
 .text.rds_update_mcu_time
                0x00000000e07001de       0x78 emodules/fm/fm_module/si4760/Rds.o
 .text.rds_save_af_list_1
                0x00000000e0700256       0x4e emodules/fm/fm_module/si4760/Rds.o
                0x00000000e0700256                rds_save_af_list_1
 .text.rds_save_af_list_2
                0x00000000e07002a4       0x4a emodules/fm/fm_module/si4760/Rds.o
                0x00000000e07002a4                rds_save_af_list_2
 .text.rds_save_af_list_function
                0x00000000e07002ee       0x26 emodules/fm/fm_module/si4760/Rds.o
                0x00000000e07002ee                rds_save_af_list_function
 .text.Si_Init_RDS
                0x00000000e0700314        0x4 emodules/fm/fm_module/si4760/Rds.o
                0x00000000e0700314                Si_Init_RDS
 .text.Si_Disable_RDS
                0x00000000e0700318        0x4 emodules/fm/fm_module/si4760/Rds.o
                0x00000000e0700318                Si_Disable_RDS
 .text.Meth_Clear
                0x00000000e070031c       0x12 emodules/fm/fm_module/si4760/Rds.o
                0x00000000e070031c                Meth_Clear
 .text.PI_Clear
                0x00000000e070032e       0x40 emodules/fm/fm_module/si4760/Rds.o
                0x00000000e070032e                PI_Clear
 .text.PSBuf_Clear
                0x00000000e070036e       0x3c emodules/fm/fm_module/si4760/Rds.o
                0x00000000e070036e                PSBuf_Clear
 .text.PS_Clear
                0x00000000e07003aa       0x1e emodules/fm/fm_module/si4760/Rds.o
                0x00000000e07003aa                PS_Clear
 .text.TPTA_Clear
                0x00000000e07003c8       0x28 emodules/fm/fm_module/si4760/Rds.o
                0x00000000e07003c8                TPTA_Clear
 .text.PTY_Clear
                0x00000000e07003f0       0x2a emodules/fm/fm_module/si4760/Rds.o
                0x00000000e07003f0                PTY_Clear
 .text.RDS_Reset
                0x00000000e070041a       0x5c emodules/fm/fm_module/si4760/Rds.o
                0x00000000e070041a                RDS_Reset
 .text.update_alt_freq_5X
                0x00000000e0700476      0x15e emodules/fm/fm_module/si4760/Rds.o
                0x00000000e0700476                update_alt_freq_5X
 .text.add_dab_to_fm_link_pi
                0x00000000e07005d4       0x60 emodules/fm/fm_module/si4760/Rds.o
                0x00000000e07005d4                add_dab_to_fm_link_pi
 .text.rds_search_dab_pi_for_rds_link
                0x00000000e0700634       0xac emodules/fm/fm_module/si4760/Rds.o
                0x00000000e0700634                rds_search_dab_pi_for_rds_link
 .text.update_pi_5X
                0x00000000e07006e0      0x12e emodules/fm/fm_module/si4760/Rds.o
                0x00000000e07006e0                update_pi_5X
 .text.update_ps_5X
                0x00000000e070080e      0x190 emodules/fm/fm_module/si4760/Rds.o
                0x00000000e070080e                update_ps_5X
 .text.update_pty_5X
                0x00000000e070099e      0x11c emodules/fm/fm_module/si4760/Rds.o
                0x00000000e070099e                update_pty_5X
 .text.TA_Read_5X
                0x00000000e0700aba       0x88 emodules/fm/fm_module/si4760/Rds.o
                0x00000000e0700aba                TA_Read_5X
 .text.update_clock_5X
                0x00000000e0700b42      0x160 emodules/fm/fm_module/si4760/Rds.o
                0x00000000e0700b42                update_clock_5X
 .text.RDS_data_process_5X
                0x00000000e0700ca2      0x1fa emodules/fm/fm_module/si4760/Rds.o
                0x00000000e0700ca2                RDS_data_process_5X
 .text.Read475x_RdsStatus
                0x00000000e0700e9c      0x208 emodules/fm/fm_module/si4760/Rds.o
                0x00000000e0700e9c                Read475x_RdsStatus
 .text.Network_pi_cmp
                0x00000000e07010a4       0x20 emodules/fm/fm_module/si4760/Rds_sub.o
                0x00000000e07010a4                Network_pi_cmp
 .text.Network_netpi
                0x00000000e07010c4        0xa emodules/fm/fm_module/si4760/Rds_sub.o
                0x00000000e07010c4                Network_netpi
 .text.Network_af_count
                0x00000000e07010ce       0x30 emodules/fm/fm_module/si4760/Rds_sub.o
                0x00000000e07010ce                Network_af_count
 .text.Network_set_sequence
                0x00000000e07010fe      0x1bc emodules/fm/fm_module/si4760/Rds_sub.o
                0x00000000e07010fe                Network_set_sequence
 .text.Network_lookup_af
                0x00000000e07012ba       0x32 emodules/fm/fm_module/si4760/Rds_sub.o
                0x00000000e07012ba                Network_lookup_af
 .text.Network_lookup_pi
                0x00000000e07012ec       0x56 emodules/fm/fm_module/si4760/Rds_sub.o
                0x00000000e07012ec                Network_lookup_pi
 .text.Network_get_frq
                0x00000000e0701342       0x22 emodules/fm/fm_module/si4760/Rds_sub.o
                0x00000000e0701342                Network_get_frq
 .text.Network_clear
                0x00000000e0701364      0x138 emodules/fm/fm_module/si4760/Rds_sub.o
                0x00000000e0701364                Network_clear
 .text.Network_af_store
                0x00000000e070149c      0x22c emodules/fm/fm_module/si4760/Rds_sub.o
                0x00000000e070149c                Network_af_store
 .text.Network_get_ctx
                0x00000000e07016c8       0x38 emodules/fm/fm_module/si4760/Rds_sub.o
                0x00000000e07016c8                Network_get_ctx
 .text.Network_resume
                0x00000000e0701700       0x5c emodules/fm/fm_module/si4760/Rds_sub.o
                0x00000000e0701700                Network_resume
 .text.ReplaceFreq
                0x00000000e070175c       0x40 emodules/fm/fm_module/si4760/Rds_sub.o
                0x00000000e070175c                ReplaceFreq
 .text.Caculate_SMeter
                0x00000000e070179c       0xb8 emodules/fm/fm_module/si4760/Rds_sub.o
                0x00000000e070179c                Caculate_SMeter
 .text.si4750_init_func_info
                0x00000000e0701854       0x44 emodules/fm/fm_module/si4760/Tu_Si_476X.o
                0x00000000e0701854                si4750_init_func_info
 .text.si475x_readStatus
                0x00000000e0701898       0x20 emodules/fm/fm_module/si4760/Tu_Si_476X.o
                0x00000000e0701898                si475x_readStatus
 .text.si475x_waitForCTS
                0x00000000e07018b8       0xb2 emodules/fm/fm_module/si4760/Tu_Si_476X.o
                0x00000000e07018b8                si475x_waitForCTS
 .text.si475x_command
                0x00000000e070196a       0x5a emodules/fm/fm_module/si4760/Tu_Si_476X.o
                0x00000000e070196a                si475x_command
 .text.si475x_get_Version
                0x00000000e07019c4       0x94 emodules/fm/fm_module/si4760/Tu_Si_476X.o
                0x00000000e07019c4                si475x_get_Version
 .text.si475x_set_property
                0x00000000e0701a58       0x36 emodules/fm/fm_module/si4760/Tu_Si_476X.o
                0x00000000e0701a58                si475x_set_property
 .text.Si475x_Disable_RDS
                0x00000000e0701a8e       0x2a emodules/fm/fm_module/si4760/Tu_Si_476X.o
                0x00000000e0701a8e                Si475x_Disable_RDS
 .text.Si475x_Init_RDS
                0x00000000e0701ab8       0x2a emodules/fm/fm_module/si4760/Tu_Si_476X.o
                0x00000000e0701ab8                Si475x_Init_RDS
 .text.si475x6x_get_PartInfo
                0x00000000e0701ae2       0x1a emodules/fm/fm_module/si4760/Tu_Si_476X.o
                0x00000000e0701ae2                si475x6x_get_PartInfo
 .text.si475x6x_loadPatch
                0x00000000e0701afc       0x9c emodules/fm/fm_module/si4760/Tu_Si_476X.o
                0x00000000e0701afc                si475x6x_loadPatch
 .text.si475x6x_FORCECTS
                0x00000000e0701b98       0x24 emodules/fm/fm_module/si4760/Tu_Si_476X.o
                0x00000000e0701b98                si475x6x_FORCECTS
 .text.si475xFMRX_powerup
                0x00000000e0701bbc       0x66 emodules/fm/fm_module/si4760/Tu_Si_476X.o
                0x00000000e0701bbc                si475xFMRX_powerup
 .text.si475xFMRX_hardware_cfg
                0x00000000e0701c22       0x1e emodules/fm/fm_module/si4760/Tu_Si_476X.o
                0x00000000e0701c22                si475xFMRX_hardware_cfg
 .text.si475xFMRX_output_config
                0x00000000e0701c40       0x1c emodules/fm/fm_module/si4760/Tu_Si_476X.o
                0x00000000e0701c40                si475xFMRX_output_config
 .text.si475xFMRX_DigitAudioPinSet
                0x00000000e0701c5c       0x22 emodules/fm/fm_module/si4760/Tu_Si_476X.o
                0x00000000e0701c5c                si475xFMRX_DigitAudioPinSet
 .text.Pll_Initial_5X
                0x00000000e0701c7e      0x748 emodules/fm/fm_module/si4760/Tu_Si_476X.o
                0x00000000e0701c7e                Pll_Initial_5X
 .text.Si475X_ReadSeekStatus
                0x00000000e07023c6      0x1ce emodules/fm/fm_module/si4760/Tu_Si_476X.o
                0x00000000e07023c6                Si475X_ReadSeekStatus
 .text.Si475X_ReadAcfStatus
                0x00000000e0702594       0x36 emodules/fm/fm_module/si4760/Tu_Si_476X.o
                0x00000000e0702594                Si475X_ReadAcfStatus
 .text.Si475X_Tune_Freq
                0x00000000e07025ca       0xb4 emodules/fm/fm_module/si4760/Tu_Si_476X.o
                0x00000000e07025ca                Si475X_Tune_Freq
 .text.Tuner_Init_Proc_5X
                0x00000000e070267e      0x2b8 emodules/fm/fm_module/si4760/Tu_Si_476X.o
                0x00000000e070267e                Tuner_Init_Proc_5X
 .text.Tuner_Mode_Proc_5X
                0x00000000e0702936     0x17d2 emodules/fm/fm_module/si4760/Tu_Si_476X.o
                0x00000000e0702936                Tuner_Mode_Proc_5X
 .text.Rds_QualityCheck_5X
                0x00000000e0704108      0x2d6 emodules/fm/fm_module/si4760/Tu_Si_476X.o
                0x00000000e0704108                Rds_QualityCheck_5X
 .text.si476x_bus_thread
                0x00000000e07043de      0x180 emodules/fm/fm_module/si4760/Tuner.o
                0x00000000e07043de                si476x_bus_thread
 .text.get_dab_fm_link_backup_freq
                0x00000000e070455e        0xa emodules/fm/fm_module/si4760/Tuner.o
                0x00000000e070455e                get_dab_fm_link_backup_freq
 .text.clear_dab_fm_link_current_info
                0x00000000e0704568        0xa emodules/fm/fm_module/si4760/Tuner.o
                0x00000000e0704568                clear_dab_fm_link_current_info
 .text.set_dab_fm_link_current_info
                0x00000000e0704572        0xa emodules/fm/fm_module/si4760/Tuner.o
                0x00000000e0704572                set_dab_fm_link_current_info
 .text.set_dab_rds_link_info
                0x00000000e070457c       0x24 emodules/fm/fm_module/si4760/Tuner.o
                0x00000000e070457c                set_dab_rds_link_info
 .text.tuner_set_mute
                0x00000000e07045a0       0x54 emodules/fm/fm_module/si4760/Tuner.o
                0x00000000e07045a0                tuner_set_mute
 .text._tuner_set_mcu_io
                0x00000000e07045f4       0xc6 emodules/fm/fm_module/si4760/Tuner.o
                0x00000000e07045f4                _tuner_set_mcu_io
 .text._tuner_mute
                0x00000000e07046ba       0x3a emodules/fm/fm_module/si4760/Tuner.o
                0x00000000e07046ba                _tuner_mute
 .text._tuner_no_mute
                0x00000000e07046f4       0x68 emodules/fm/fm_module/si4760/Tuner.o
                0x00000000e07046f4                _tuner_no_mute
 .text.tuner_open_dev
                0x00000000e070475c       0x32 emodules/fm/fm_module/si4760/Tuner.o
                0x00000000e070475c                tuner_open_dev
 .text.tuner_close_dev
                0x00000000e070478e       0x2a emodules/fm/fm_module/si4760/Tuner.o
                0x00000000e070478e                tuner_close_dev
 .text.get_fm_para
                0x00000000e07047b8       0x1e emodules/fm/fm_module/si4760/Tuner.o
                0x00000000e07047b8                get_fm_para
 .text.get_init_para
                0x00000000e07047d6       0x1e emodules/fm/fm_module/si4760/Tuner.o
                0x00000000e07047d6                get_init_para
 .text.set_init_area
                0x00000000e07047f4       0x10 emodules/fm/fm_module/si4760/Tuner.o
                0x00000000e07047f4                set_init_area
 .text.get_init_area
                0x00000000e0704804       0x14 emodules/fm/fm_module/si4760/Tuner.o
                0x00000000e0704804                get_init_area
 .text.Tuner_IIC_Read
                0x00000000e0704818       0xca emodules/fm/fm_module/si4760/Tuner.o
                0x00000000e0704818                Tuner_IIC_Read
 .text.Tuner_IIC_Write
                0x00000000e07048e2       0xc0 emodules/fm/fm_module/si4760/Tuner.o
                0x00000000e07048e2                Tuner_IIC_Write
 .text.tuner_save_data
                0x00000000e07049a2       0x42 emodules/fm/fm_module/si4760/Tuner.o
                0x00000000e07049a2                tuner_save_data
 .text.tuner_save_system_data
                0x00000000e07049e4       0x5a emodules/fm/fm_module/si4760/Tuner.o
                0x00000000e07049e4                tuner_save_system_data
 .text.Get_Tuner_Init_Status
                0x00000000e0704a3e       0x10 emodules/fm/fm_module/si4760/Tuner.o
                0x00000000e0704a3e                Get_Tuner_Init_Status
 .text.Getfrequency
                0x00000000e0704a4e       0x4c emodules/fm/fm_module/si4760/Tuner.o
                0x00000000e0704a4e                Getfrequency
 .text.Get_band_inf
                0x00000000e0704a9a      0x18c emodules/fm/fm_module/si4760/Tuner.o
                0x00000000e0704a9a                Get_band_inf
 .text.Init_Rds_para
                0x00000000e0704c26       0x4a emodules/fm/fm_module/si4760/Tuner.o
                0x00000000e0704c26                Init_Rds_para
 .text.Tuner_Set_memory
                0x00000000e0704c70      0x178 emodules/fm/fm_module/si4760/Tuner.o
                0x00000000e0704c70                Tuner_Set_memory
 .text.Tuner_Read_memory
                0x00000000e0704de8      0x14c emodules/fm/fm_module/si4760/Tuner.o
                0x00000000e0704de8                Tuner_Read_memory
 .text.init_setting_fm_para
                0x00000000e0704f34       0x40 emodules/fm/fm_module/si4760/Tuner.o
                0x00000000e0704f34                init_setting_fm_para
 .text.Init_Tuner
                0x00000000e0704f74      0x442 emodules/fm/fm_module/si4760/Tuner.o
                0x00000000e0704f74                Init_Tuner
 .text.Si473X_mute
                0x00000000e07053b6        0xa emodules/fm/fm_module/si4760/Tuner.o
                0x00000000e07053b6                Si473X_mute
 .text.auto_store_start
                0x00000000e07053c0      0x120 emodules/fm/fm_module/si4760/Tuner.o
                0x00000000e07053c0                auto_store_start
 .text.set_search_stop_flag
                0x00000000e07054e0       0x18 emodules/fm/fm_module/si4760/Tuner.o
                0x00000000e07054e0                set_search_stop_flag
 .text.set_search_stop
                0x00000000e07054f8       0xb8 emodules/fm/fm_module/si4760/Tuner.o
                0x00000000e07054f8                set_search_stop
 .text.set_fast_high_cut_on_off
                0x00000000e07055b0       0x4c emodules/fm/fm_module/si4760/Tuner.o
                0x00000000e07055b0                set_fast_high_cut_on_off
 .text.set_high_cut_on_off
                0x00000000e07055fc      0x160 emodules/fm/fm_module/si4760/Tuner.o
                0x00000000e07055fc                set_high_cut_on_off
 .text.set_soft_mute_on_off
                0x00000000e070575c       0x6e emodules/fm/fm_module/si4760/Tuner.o
                0x00000000e070575c                set_soft_mute_on_off
 .text.set_multi_path_on_off
                0x00000000e07057ca       0xf2 emodules/fm/fm_module/si4760/Tuner.o
                0x00000000e07057ca                set_multi_path_on_off
 .text.set_rds_on_off
                0x00000000e07058bc       0xea emodules/fm/fm_module/si4760/Tuner.o
                0x00000000e07058bc                set_rds_on_off
 .text.set_rds_af_on_off
                0x00000000e07059a6       0x36 emodules/fm/fm_module/si4760/Tuner.o
                0x00000000e07059a6                set_rds_af_on_off
 .text.set_rds_ta_on_off
                0x00000000e07059dc       0x36 emodules/fm/fm_module/si4760/Tuner.o
                0x00000000e07059dc                set_rds_ta_on_off
 .text.set_rds_ct_on_off
                0x00000000e0705a12       0x38 emodules/fm/fm_module/si4760/Tuner.o
                0x00000000e0705a12                set_rds_ct_on_off
 .text.set_local_seek_on_off
                0x00000000e0705a4a       0x3e emodules/fm/fm_module/si4760/Tuner.o
                0x00000000e0705a4a                set_local_seek_on_off
 .text.autostore_save
                0x00000000e0705a88      0x2ae emodules/fm/fm_module/si4760/Tuner.o
                0x00000000e0705a88                autostore_save
 .text.autostore_end
                0x00000000e0705d36      0x25a emodules/fm/fm_module/si4760/Tuner.o
                0x00000000e0705d36                autostore_end
 .text.Search_PresetNum
                0x00000000e0705f90       0x58 emodules/fm/fm_module/si4760/Tuner.o
                0x00000000e0705f90                Search_PresetNum
 .text.Tune_Up_fStep
                0x00000000e0705fe8       0x40 emodules/fm/fm_module/si4760/Tuner.o
                0x00000000e0705fe8                Tune_Up_fStep
 .text.Tune_Down_fStep
                0x00000000e0706028       0x3e emodules/fm/fm_module/si4760/Tuner.o
                0x00000000e0706028                Tune_Down_fStep
 .text.key_autostore
                0x00000000e0706066       0xaa emodules/fm/fm_module/si4760/Tuner.o
                0x00000000e0706066                key_autostore
 .text.Tuner_set_mute
                0x00000000e0706110        0x8 emodules/fm/fm_module/si4760/Tuner.o
                0x00000000e0706110                Tuner_set_mute
 .text.tuner_store_preset
                0x00000000e0706118       0xa8 emodules/fm/fm_module/si4760/Tuner.o
                0x00000000e0706118                tuner_store_preset
 .text.tuner_select_preset
                0x00000000e07061c0      0x2d4 emodules/fm/fm_module/si4760/Tuner.o
                0x00000000e07061c0                tuner_select_preset
 .text.Cmd_Function_Band
                0x00000000e0706494      0x3c6 emodules/fm/fm_module/si4760/Tuner.o
                0x00000000e0706494                Cmd_Function_Band
 .text.Si47XX_Tune_Freq
                0x00000000e070685a        0x4 emodules/fm/fm_module/si4760/Tuner.o
                0x00000000e070685a                Si47XX_Tune_Freq
 .text.Tuner_key_Tune_Freq
                0x00000000e070685e       0xde emodules/fm/fm_module/si4760/Tuner.o
                0x00000000e070685e                Tuner_key_Tune_Freq
 .text.Tuner_func_set_area
                0x00000000e070693c       0x86 emodules/fm/fm_module/si4760/Tuner.o
                0x00000000e070693c                Tuner_func_set_area
 .text.Tuner_key_Menu_Seek
                0x00000000e07069c2      0x114 emodules/fm/fm_module/si4760/Tuner.o
                0x00000000e07069c2                Tuner_key_Menu_Seek
 .text.Tuner_key_pty_Seek
                0x00000000e0706ad6      0x198 emodules/fm/fm_module/si4760/Tuner.o
                0x00000000e0706ad6                Tuner_key_pty_Seek
 .text.Tuner_key_nextprev
                0x00000000e0706c6e      0x154 emodules/fm/fm_module/si4760/Tuner.o
                0x00000000e0706c6e                Tuner_key_nextprev
 .text.Tuner_HU_SET_App
                0x00000000e0706dc2        0x2 emodules/fm/fm_module/si4760/Tuner.o
                0x00000000e0706dc2                Tuner_HU_SET_App
 .text.Tuner_Key_Process
                0x00000000e0706dc4       0x3e emodules/fm/fm_module/si4760/Tuner.o
                0x00000000e0706dc4                Tuner_Key_Process
 .text.si4760_set_reset_io
                0x00000000e0706e02       0x68 emodules/fm/fm_module/si4760/Tuner.o
                0x00000000e0706e02                si4760_set_reset_io
 .text.si4760_powerup_command
                0x00000000e0706e6a       0x74 emodules/fm/fm_module/si4760/Tuner.o
                0x00000000e0706e6a                si4760_powerup_command
 .text.si4760_powerdown
                0x00000000e0706ede       0x14 emodules/fm/fm_module/si4760/Tuner.o
                0x00000000e0706ede                si4760_powerdown
 .text.fm_send_cmd2dab
                0x00000000e0706ef2       0x40 emodules/fm/fm_module/si4760/Tuner.o
                0x00000000e0706ef2                fm_send_cmd2dab
 .text.tuner_get_Tu_Divisor_by_freq
                0x00000000e0706f32       0x76 emodules/fm/fm_module/si4760/Tuner.o
                0x00000000e0706f32                tuner_get_Tu_Divisor_by_freq
 .text.tuner_scan_search_pi_for_link
                0x00000000e0706fa8       0xd0 emodules/fm/fm_module/si4760/Tuner.o
                0x00000000e0706fa8                tuner_scan_search_pi_for_link
 .text.tuner_break_search_pi_for_link
                0x00000000e0707078      0x16c emodules/fm/fm_module/si4760/Tuner.o
                0x00000000e0707078                tuner_break_search_pi_for_link
 .text.tuner_set_channel_for_dab_link
                0x00000000e07071e4       0xa2 emodules/fm/fm_module/si4760/Tuner.o
                0x00000000e07071e4                tuner_set_channel_for_dab_link
 .text.Tuner_status
                0x00000000e0707286        0xe emodules/fm/fm_module/si4760/Tuner.o
                0x00000000e0707286                Tuner_status
 .text.Tuner_switch_fm_band_for_dab_link
                0x00000000e0707294       0x20 emodules/fm/fm_module/si4760/Tuner.o
                0x00000000e0707294                Tuner_switch_fm_band_for_dab_link
 .text.Tuner_get_dab_link_rds_status
                0x00000000e07072b4       0x10 emodules/fm/fm_module/si4760/Tuner.o
                0x00000000e07072b4                Tuner_get_dab_link_rds_status
 .text.Tuner_switch_output_channel_for_dab_link
                0x00000000e07072c4       0xf4 emodules/fm/fm_module/si4760/Tuner.o
                0x00000000e07072c4                Tuner_switch_output_channel_for_dab_link
 .text.Tuner_start_search_dab_pi_for_rds_link
                0x00000000e07073b8       0xca emodules/fm/fm_module/si4760/Tuner.o
                0x00000000e07073b8                Tuner_start_search_dab_pi_for_rds_link
 .text.si476x_thread_init
                0x00000000e0707482       0x2e emodules/fm/fm_module/si4760/Tuner.o
                0x00000000e0707482                si476x_thread_init
 .text.si476x_thread_uninit
                0x00000000e07074b0       0x42 emodules/fm/fm_module/si4760/Tuner.o
                0x00000000e07074b0                si476x_thread_uninit
 .text.fm_init  0x00000000e07074f2      0x27e emodules/fm/fm_module/si4760/Tuner.o
                0x00000000e07074f2                fm_init
 .text.fm_exit  0x00000000e0707770       0x18 emodules/fm/fm_module/si4760/Tuner.o
                0x00000000e0707770                fm_exit
 .text.hal_fm_init
                0x00000000e0707788        0x4 emodules/fm/dev_fm.o
                0x00000000e0707788                hal_fm_init
 .text.hal_fm_uninit
                0x00000000e070778c        0xe emodules/fm/dev_fm.o
                0x00000000e070778c                hal_fm_uninit
 .text.sunxi_fm_init
                0x00000000e070779a        0x4 emodules/fm/dev_fm.o
 .text.sunxi_fm_open
                0x00000000e070779e       0x20 emodules/fm/dev_fm.o
 .text.sunxi_fm_close
                0x00000000e07077be       0x20 emodules/fm/dev_fm.o
 .text.sunxi_fm_write
                0x00000000e07077de        0x4 emodules/fm/dev_fm.o
 .text.sunxi_fm_control
                0x00000000e07077e2       0x26 emodules/fm/dev_fm.o
 .text.hal_fm_control
                0x00000000e0707808      0x36e emodules/fm/dev_fm.o
                0x00000000e0707808                hal_fm_control
 .text.sunxi_fm_read
                0x00000000e0707b76        0x4 emodules/fm/dev_fm.o
 .text.sunxi_driver_fm_init
                0x00000000e0707b7a       0x74 emodules/fm/dev_fm.o
                0x00000000e0707b7a                sunxi_driver_fm_init
 .text.sunxi_driver_fm_uninit
                0x00000000e0707bee       0x1a emodules/fm/dev_fm.o
                0x00000000e0707bee                sunxi_driver_fm_uninit
 .text.DRV_FM_MInit
                0x00000000e0707c08       0x22 emodules/fm/drv_fm.o
                0x00000000e0707c08                DRV_FM_MInit
 .text.DRV_FM_MExit
                0x00000000e0707c2a       0x20 emodules/fm/drv_fm.o
                0x00000000e0707c2a                DRV_FM_MExit
 .text.DRV_FM_MOpen
                0x00000000e0707c4a        0xe emodules/fm/drv_fm.o
                0x00000000e0707c4a                DRV_FM_MOpen
 .text.DRV_FM_MClose
                0x00000000e0707c58        0x4 emodules/fm/drv_fm.o
                0x00000000e0707c58                DRV_FM_MClose
 .text.DRV_FM_MRead
                0x00000000e0707c5c        0x6 emodules/fm/drv_fm.o
                0x00000000e0707c5c                DRV_FM_MRead
 .text.DRV_FM_MWrite
                0x00000000e0707c62        0x6 emodules/fm/drv_fm.o
                0x00000000e0707c62                DRV_FM_MWrite
 .text.DRV_FM_MIoctrl
                0x00000000e0707c68       0xa8 emodules/fm/drv_fm.o
                0x00000000e0707c68                DRV_FM_MIoctrl
 .text.rt_device_register
                0x00000000e0707d10       0x16 ./elibrary/bin//libsyscall.a(syscall.o)
                0x00000000e0707d10                rt_device_register
 .text.rt_device_unregister
                0x00000000e0707d26       0x16 ./elibrary/bin//libsyscall.a(syscall.o)
                0x00000000e0707d26                rt_device_unregister
 .text.esKRNL_SemCreate
                0x00000000e0707d3c       0x16 ./elibrary/bin//libsyscall.a(syscall.o)
                0x00000000e0707d3c                esKRNL_SemCreate
 .text.esKRNL_SemDel
                0x00000000e0707d52       0x16 ./elibrary/bin//libsyscall.a(syscall.o)
                0x00000000e0707d52                esKRNL_SemDel
 .text.esKRNL_SemPend
                0x00000000e0707d68       0x16 ./elibrary/bin//libsyscall.a(syscall.o)
                0x00000000e0707d68                esKRNL_SemPend
 .text.esKRNL_SemPost
                0x00000000e0707d7e       0x1a ./elibrary/bin//libsyscall.a(syscall.o)
                0x00000000e0707d7e                esKRNL_SemPost
 .text.esKRNL_TCreate
                0x00000000e0707d98       0x1c ./elibrary/bin//libsyscall.a(syscall.o)
                0x00000000e0707d98                esKRNL_TCreate
 .text.esKRNL_TDel
                0x00000000e0707db4       0x1e ./elibrary/bin//libsyscall.a(syscall.o)
                0x00000000e0707db4                esKRNL_TDel
 .text.esKRNL_TDelReq
                0x00000000e0707dd2       0x1e ./elibrary/bin//libsyscall.a(syscall.o)
                0x00000000e0707dd2                esKRNL_TDelReq
 .text.esKRNL_TimeDly
                0x00000000e0707df0       0x16 ./elibrary/bin//libsyscall.a(syscall.o)
                0x00000000e0707df0                esKRNL_TimeDly
 .text.esKRNL_TimeGet
                0x00000000e0707e06       0x18 ./elibrary/bin//libsyscall.a(syscall.o)
                0x00000000e0707e06                esKRNL_TimeGet
 .text.esKSRV_SendMsg
                0x00000000e0707e1e       0x20 ./elibrary/bin//libsyscall.a(syscall.o)
                0x00000000e0707e1e                esKSRV_SendMsg
 .text.esMEMS_Malloc
                0x00000000e0707e3e       0x1a ./elibrary/bin//libsyscall.a(syscall.o)
                0x00000000e0707e3e                esMEMS_Malloc
 .text.esMEMS_Mfree
                0x00000000e0707e58       0x16 ./elibrary/bin//libsyscall.a(syscall.o)
                0x00000000e0707e58                esMEMS_Mfree
 .text.esSIOS_putstr
                0x00000000e0707e6e       0x14 ./elibrary/bin//libsyscall.a(syscall.o)
                0x00000000e0707e6e                esSIOS_putstr
 .text._close_r
                0x00000000e0707e82       0x18 ./elibrary/bin//libsyscall.a(syscall_fops.o)
                0x00000000e0707e82                _close_r
 .text._open_r  0x00000000e0707e9a       0x18 ./elibrary/bin//libsyscall.a(syscall_fops.o)
                0x00000000e0707e9a                _open_r
 .text.ioctl    0x00000000e0707eb2       0x28 ./elibrary/bin//libsyscall.a(syscall_fops.o)
                0x00000000e0707eb2                ioctl
 .text.usleep   0x00000000e0707eda       0x18 ./elibrary/bin//libsyscall.a(syscall.o)
                0x00000000e0707eda                usleep
 .text.eLIBs_vprintf
                0x00000000e0707ef2       0x4c ./elibrary/bin//libminic.a(elibs_stdio.o)
                0x00000000e0707ef2                eLIBs_vprintf
 .text.eLIBs_printf
                0x00000000e0707f3e       0x20 ./elibrary/bin//libminic.a(elibs_stdio.o)
                0x00000000e0707f3e                eLIBs_printf
                0x00000000e0707f3e                printk
 .text.eLIBs_memset
                0x00000000e0707f5e        0xc ./elibrary/bin//libminic.a(elibs_string.o)
                0x00000000e0707f5e                eLIBs_memset
 .text.eLIBs_vsnprintf
                0x00000000e0707f6a        0xc ./elibrary/bin//libminic.a(elibs_sprintf.o)
                0x00000000e0707f6a                eLIBs_vsnprintf
 .text.eLIBs_vscnprintf
                0x00000000e0707f76       0x1c ./elibrary/bin//libminic.a(elibs_sprintf.o)
                0x00000000e0707f76                eLIBs_vscnprintf
 *(.stub)
 *(.gnu.warning)
 *(.gnu.linkonce.t*)
                0x00000000e0707f94                . = ALIGN (0x4)
 *fill*         0x00000000e0707f92        0x2 

.mod.rodata     0x00000000e0707f94      0xc24
 *(.rodata)
 *(.rodata.*)
 *fill*         0x00000000e0707f94        0x4 
 .rodata.rds_update_mcu_time.str1.8
                0x00000000e0707f98        0x9 emodules/fm/fm_module/si4760/Rds.o
 *fill*         0x00000000e0707fa1        0x7 
 .rodata.update_clock_5X.cst8
                0x00000000e0707fa8       0x20 emodules/fm/fm_module/si4760/Rds.o
 .rodata.Tuner_Init_Proc_5X
                0x00000000e0707fc8       0x28 emodules/fm/fm_module/si4760/Tu_Si_476X.o
 .rodata.Tuner_Init_Proc_5X.str1.8
                0x00000000e0707ff0        0x9 emodules/fm/fm_module/si4760/Tu_Si_476X.o
 .rodata.si475x6x_am_patch
                0x00000000e0707ff0      0x320 emodules/fm/fm_module/si4760/Tu_Si_476X.o
                0x00000000e0707ff0                si475x6x_am_patch
 .rodata.si476x_fm_patch
                0x00000000e0708310      0x698 emodules/fm/fm_module/si4760/Tu_Si_476X.o
                0x00000000e0708310                si476x_fm_patch
 .rodata.Get_band_inf
                0x00000000e07089a8       0x2c emodules/fm/fm_module/si4760/Tuner.o
 .rodata.Tuner_Key_Process
                0x00000000e07089d4       0x1c emodules/fm/fm_module/si4760/Tuner.o
 .rodata.Init_Tuner.cst8
                0x00000000e07089f0       0x68 emodules/fm/fm_module/si4760/Tuner.o
 .rodata.Tuner_IIC_Read.str1.8
                0x00000000e0708a58       0x2f emodules/fm/fm_module/si4760/Tuner.o
 *fill*         0x00000000e0708a87        0x1 
 .rodata.Tuner_IIC_Write.str1.8
                0x00000000e0708a88       0x18 emodules/fm/fm_module/si4760/Tuner.o
 .rodata._tuner_set_mcu_io.str1.8
                0x00000000e0708aa0        0x9 emodules/fm/fm_module/si4760/Tuner.o
 .rodata.fm_send_cmd2dab.str1.8
                0x00000000e0708aa0        0x9 emodules/fm/fm_module/si4760/Tuner.o
 *fill*         0x00000000e0708aa9        0x7 
 .rodata.tuner_open_dev.str1.8
                0x00000000e0708ab0        0xb emodules/fm/fm_module/si4760/Tuner.o
 *fill*         0x00000000e0708abb        0x5 
 .rodata.tuner_set_mute.str1.8
                0x00000000e0708ac0        0x9 emodules/fm/fm_module/si4760/Tuner.o
 *fill*         0x00000000e0708ac9        0x3 
 .rodata.hal_fm_control
                0x00000000e0708acc       0xc8 emodules/fm/dev_fm.o
 *fill*         0x00000000e0708b94        0x4 
 .rodata.sunxi_driver_fm_init.str1.8
                0x00000000e0708b98        0x8 emodules/fm/dev_fm.o
                                          0x3 (size before relaxing)
 .rodata.sunxi_hal_fm_driver
                0x00000000e0708ba0       0x18 emodules/fm/dev_fm.o
 .rodata.DRV_FM_MIoctrl.str1.8
                0x00000000e0708bb8        0x9 emodules/fm/drv_fm.o
 *(.gnu.linkonce.r*)
                0x00000000e0708bc4                . = ALIGN (0x4)

.mod.data       0x00000000e0708bb8      0x758
 *(.data)
 .data          0x00000000e0708bb8      0x750 /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/../../../../riscv64-unknown-elf/lib/rv64imafdcxtheadc/lp64d/libc.a(lib_a-impure.o)
                0x00000000e0708bb8                _impure_ptr
 *(.data.*)
 .data.fiic     0x00000000e0709308        0x4 emodules/fm/dev_fm.o
                0x00000000e0709308                fiic
 .data.tuner_mode
                0x00000000e070930c        0x1 emodules/fm/drv_fm.o
                0x00000000e070930c                tuner_mode
 *(.gnu.linkonce.d.*)
 *(.sdata)
 *(.sdata.*)
 *(.gnu.linkonce.s.*)
 *(.sdata2)
 *(.sdata2.*)
 *(.gnu.linkonce.s2.*)
                0x00000000e0709310                . = ALIGN (0x4)
 *fill*         0x00000000e070930d        0x3 

.mod.bss        0x00000000e0709310      0x650
                0x00000000e0709310                __bss_start = ABSOLUTE (.)
 *(.bss)
 *(.bss.*)
 .bss.Eonpi     0x00000000e0709310        0x2 emodules/fm/fm_module/si4760/Rds.o
                0x00000000e0709310                Eonpi
 .bss.af_cnt    0x00000000e0709312        0x1 emodules/fm/fm_module/si4760/Rds.o
                0x00000000e0709312                af_cnt
 *fill*         0x00000000e0709313        0x5 
 .bss.af_mask_timer
                0x00000000e0709318        0xc emodules/fm/fm_module/si4760/Rds.o
                0x00000000e0709318                af_mask_timer
 .bss.af_time   0x00000000e0709324        0x2 emodules/fm/fm_module/si4760/Rds.o
                0x00000000e0709324                af_time
 .bss.af_time2  0x00000000e0709326        0x2 emodules/fm/fm_module/si4760/Rds.o
                0x00000000e0709326                af_time2
 .bss.af_time3  0x00000000e0709328        0x2 emodules/fm/fm_module/si4760/Rds.o
                0x00000000e0709328                af_time3
 *fill*         0x00000000e070932a        0x6 
 .bss.bfpi      0x00000000e0709330        0x2 emodules/fm/fm_module/si4760/Rds.o
                0x00000000e0709330                bfpi
 *fill*         0x00000000e0709332        0x6 
 .bss.bfps      0x00000000e0709338        0x9 emodules/fm/fm_module/si4760/Rds.o
                0x00000000e0709338                bfps
 .bss.bfpty     0x00000000e0709341        0x1 emodules/fm/fm_module/si4760/Rds.o
                0x00000000e0709341                bfpty
 *fill*         0x00000000e0709342        0x6 
 .bss.bkpi      0x00000000e0709348        0x2 emodules/fm/fm_module/si4760/Rds.o
                0x00000000e0709348                bkpi
 .bss.curr_af   0x00000000e070934a        0x1 emodules/fm/fm_module/si4760/Rds.o
                0x00000000e070934a                curr_af
 .bss.curr_af_cnt
                0x00000000e070934b        0x1 emodules/fm/fm_module/si4760/Rds.o
                0x00000000e070934b                curr_af_cnt
 *fill*         0x00000000e070934c        0x4 
 .bss.curr_ctx  0x00000000e0709350        0x4 emodules/fm/fm_module/si4760/Rds.o
                0x00000000e0709350                curr_ctx
 *fill*         0x00000000e0709354        0x4 
 .bss.ltpi      0x00000000e0709358        0x2 emodules/fm/fm_module/si4760/Rds.o
                0x00000000e0709358                ltpi
 *fill*         0x00000000e070935a        0x6 
 .bss.ltps      0x00000000e0709360        0x9 emodules/fm/fm_module/si4760/Rds.o
                0x00000000e0709360                ltps
 .bss.ltpty     0x00000000e0709369        0x1 emodules/fm/fm_module/si4760/Rds.o
                0x00000000e0709369                ltpty
 .bss.mask_timer
                0x00000000e070936a        0x1 emodules/fm/fm_module/si4760/Rds.o
                0x00000000e070936a                mask_timer
 .bss.network_ptr
                0x00000000e070936b        0x1 emodules/fm/fm_module/si4760/Rds.o
                0x00000000e070936b                network_ptr
 *fill*         0x00000000e070936c        0x4 
 .bss.networks  0x00000000e0709370      0x208 emodules/fm/fm_module/si4760/Rds.o
                0x00000000e0709370                networks
 .bss.pi_bak    0x00000000e0709578        0x2 emodules/fm/fm_module/si4760/Rds.o
                0x00000000e0709578                pi_bak
 *fill*         0x00000000e070957a        0x6 
 .bss.preset_pi
                0x00000000e0709580       0x24 emodules/fm/fm_module/si4760/Rds.o
                0x00000000e0709580                preset_pi
 *fill*         0x00000000e07095a4        0x4 
 .bss.ps_flg    0x00000000e07095a8        0x4 emodules/fm/fm_module/si4760/Rds.o
                0x00000000e07095a8                ps_flg
 .bss.pty_mode  0x00000000e07095ac        0x1 emodules/fm/fm_module/si4760/Rds.o
                0x00000000e07095ac                pty_mode
 .bss.ta_cnt    0x00000000e07095ad        0x1 emodules/fm/fm_module/si4760/Rds.o
                0x00000000e07095ad                ta_cnt
 .bss.ta_timer  0x00000000e07095ae        0x1 emodules/fm/fm_module/si4760/Rds.o
                0x00000000e07095ae                ta_timer
 *fill*         0x00000000e07095af        0x1 
 .bss.tp_flg    0x00000000e07095b0        0x4 emodules/fm/fm_module/si4760/Rds.o
                0x00000000e07095b0                tp_flg
 .bss.increment.16117
                0x00000000e07095b4        0x2 emodules/fm/fm_module/si4760/Tu_Si_476X.o
 .bss.wait_pty_over
                0x00000000e07095b6        0x1 emodules/fm/fm_module/si4760/Tu_Si_476X.o
                0x00000000e07095b6                wait_pty_over
 .bss.Cur_Tuner_Band
                0x00000000e07095b7        0x1 emodules/fm/fm_module/si4760/Tuner.o
                0x00000000e07095b7                Cur_Tuner_Band
 .bss.I2c_status_err
                0x00000000e07095b8        0x1 emodules/fm/fm_module/si4760/Tuner.o
                0x00000000e07095b8                I2c_status_err
 .bss.Iic_Adress_error
                0x00000000e07095b9        0x1 emodules/fm/fm_module/si4760/Tuner.o
                0x00000000e07095b9                Iic_Adress_error
 .bss.Mem_Code  0x00000000e07095ba        0x1 emodules/fm/fm_module/si4760/Tuner.o
                0x00000000e07095ba                Mem_Code
 .bss.Tu_Band   0x00000000e07095bb        0x1 emodules/fm/fm_module/si4760/Tuner.o
                0x00000000e07095bb                Tu_Band
 *fill*         0x00000000e07095bc        0x4 
 .bss.Tu_Divisor
                0x00000000e07095c0        0x2 emodules/fm/fm_module/si4760/Tuner.o
                0x00000000e07095c0                Tu_Divisor
 .bss.Tu_Freq_Offset
                0x00000000e07095c2        0x1 emodules/fm/fm_module/si4760/Tuner.o
                0x00000000e07095c2                Tu_Freq_Offset
 .bss.Tu_No_Patch
                0x00000000e07095c3        0x1 emodules/fm/fm_module/si4760/Tuner.o
                0x00000000e07095c3                Tu_No_Patch
 *fill*         0x00000000e07095c4        0x4 
 .bss.Tu_Preset
                0x00000000e07095c8       0x5a emodules/fm/fm_module/si4760/Tuner.o
                0x00000000e07095c8                Tu_Preset
 *fill*         0x00000000e0709622        0x6 
 .bss.Tu_Rds_option
                0x00000000e0709628        0x4 emodules/fm/fm_module/si4760/Tuner.o
                0x00000000e0709628                Tu_Rds_option
 *fill*         0x00000000e070962c        0x4 
 .bss.Tu_Read_Buff
                0x00000000e0709630       0x12 emodules/fm/fm_module/si4760/Tuner.o
                0x00000000e0709630                Tu_Read_Buff
 .bss.Tu_Snr    0x00000000e0709642        0x1 emodules/fm/fm_module/si4760/Tuner.o
                0x00000000e0709642                Tu_Snr
 .bss.Tu_band_rds_fm
                0x00000000e0709643        0x1 emodules/fm/fm_module/si4760/Tuner.o
                0x00000000e0709643                Tu_band_rds_fm
 *fill*         0x00000000e0709644        0x4 
 .bss.Tu_blc_flg
                0x00000000e0709648        0x4 emodules/fm/fm_module/si4760/Tuner.o
                0x00000000e0709648                Tu_blc_flg
 .bss.Tu_divisor_step
                0x00000000e070964c        0x1 emodules/fm/fm_module/si4760/Tuner.o
                0x00000000e070964c                Tu_divisor_step
 *fill*         0x00000000e070964d        0x1 
 .bss.Tu_freq_max_con
                0x00000000e070964e        0x2 emodules/fm/fm_module/si4760/Tuner.o
                0x00000000e070964e                Tu_freq_max_con
 .bss.Tu_freq_min
                0x00000000e0709650        0x2 emodules/fm/fm_module/si4760/Tuner.o
                0x00000000e0709650                Tu_freq_min
 .bss.Tu_frequency
                0x00000000e0709652        0x2 emodules/fm/fm_module/si4760/Tuner.o
                0x00000000e0709652                Tu_frequency
 *fill*         0x00000000e0709654        0x4 
 .bss.Tu_last_divisor
                0x00000000e0709658        0x2 emodules/fm/fm_module/si4760/Tuner.o
                0x00000000e0709658                Tu_last_divisor
 .bss.Tu_preset_num
                0x00000000e070965a        0x1 emodules/fm/fm_module/si4760/Tuner.o
                0x00000000e070965a                Tu_preset_num
 *fill*         0x00000000e070965b        0x5 
 .bss.Tu_pro_freq
                0x00000000e0709660        0xc emodules/fm/fm_module/si4760/Tuner.o
                0x00000000e0709660                Tu_pro_freq
 *fill*         0x00000000e070966c        0x4 
 .bss.Tu_rds_flags
                0x00000000e0709670        0x4 emodules/fm/fm_module/si4760/Tuner.o
                0x00000000e0709670                Tu_rds_flags
 .bss.Tu_smeter
                0x00000000e0709674        0x1 emodules/fm/fm_module/si4760/Tuner.o
                0x00000000e0709674                Tu_smeter
 .bss.Tune_Half_Sec_Timer
                0x00000000e0709675        0x1 emodules/fm/fm_module/si4760/Tuner.o
                0x00000000e0709675                Tune_Half_Sec_Timer
 .bss.Tune_Process_Timer
                0x00000000e0709676        0x1 emodules/fm/fm_module/si4760/Tuner.o
                0x00000000e0709676                Tune_Process_Timer
 .bss.Tune_STC_OK
                0x00000000e0709677        0x1 emodules/fm/fm_module/si4760/Tuner.o
                0x00000000e0709677                Tune_STC_OK
 .bss.Tune_Step_Timer
                0x00000000e0709678        0x1 emodules/fm/fm_module/si4760/Tuner.o
                0x00000000e0709678                Tune_Step_Timer
 .bss.Tune_freq_int
                0x00000000e0709679        0x1 emodules/fm/fm_module/si4760/Tuner.o
                0x00000000e0709679                Tune_freq_int
 .bss.Tuner_ACK_error
                0x00000000e070967a        0x1 emodules/fm/fm_module/si4760/Tuner.o
                0x00000000e070967a                Tuner_ACK_error
 .bss.Tuner_Area
                0x00000000e070967b        0x1 emodules/fm/fm_module/si4760/Tuner.o
                0x00000000e070967b                Tuner_Area
 *fill*         0x00000000e070967c        0x4 
 .bss.Tuner_Flag0
                0x00000000e0709680        0x4 emodules/fm/fm_module/si4760/Tuner.o
                0x00000000e0709680                Tuner_Flag0
 *fill*         0x00000000e0709684        0x4 
 .bss.Tuner_Flag1
                0x00000000e0709688        0x4 emodules/fm/fm_module/si4760/Tuner.o
                0x00000000e0709688                Tuner_Flag1
 .bss.Tuner_Init_Status
                0x00000000e070968c        0x1 emodules/fm/fm_module/si4760/Tuner.o
                0x00000000e070968c                Tuner_Init_Status
 .bss.Tuner_Read_Band
                0x00000000e070968d        0x1 emodules/fm/fm_module/si4760/Tuner.o
                0x00000000e070968d                Tuner_Read_Band
 .bss.Tuner_Read_Band_cnt
                0x00000000e070968e        0x1 emodules/fm/fm_module/si4760/Tuner.o
                0x00000000e070968e                Tuner_Read_Band_cnt
 .bss.Tuner_Read_Band_timeout
                0x00000000e070968f        0x1 emodules/fm/fm_module/si4760/Tuner.o
                0x00000000e070968f                Tuner_Read_Band_timeout
 .bss.Tuner_Reset_Timer
                0x00000000e0709690        0x1 emodules/fm/fm_module/si4760/Tuner.o
                0x00000000e0709690                Tuner_Reset_Timer
 .bss.Tuner_Status
                0x00000000e0709691        0x1 emodules/fm/fm_module/si4760/Tuner.o
                0x00000000e0709691                Tuner_Status
 .bss.Tuner_auto_seek_count
                0x00000000e0709692        0x1 emodules/fm/fm_module/si4760/Tuner.o
                0x00000000e0709692                Tuner_auto_seek_count
 .bss.Tuner_auto_seek_invalid_count
                0x00000000e0709693        0x1 emodules/fm/fm_module/si4760/Tuner.o
                0x00000000e0709693                Tuner_auto_seek_invalid_count
 .bss.Tuner_auto_seek_valid_count
                0x00000000e0709694        0x1 emodules/fm/fm_module/si4760/Tuner.o
                0x00000000e0709694                Tuner_auto_seek_valid_count
 .bss.Tuner_communication_error
                0x00000000e0709695        0x1 emodules/fm/fm_module/si4760/Tuner.o
                0x00000000e0709695                Tuner_communication_error
 .bss.Tuner_iic_timeout
                0x00000000e0709696        0x1 emodules/fm/fm_module/si4760/Tuner.o
                0x00000000e0709696                Tuner_iic_timeout
 .bss.Tuner_init_again
                0x00000000e0709697        0x1 emodules/fm/fm_module/si4760/Tuner.o
                0x00000000e0709697                Tuner_init_again
 .bss.Tuner_init_error
                0x00000000e0709698        0x1 emodules/fm/fm_module/si4760/Tuner.o
                0x00000000e0709698                Tuner_init_error
 *fill*         0x00000000e0709699        0x7 
 .bss.all_app_para
                0x00000000e07096a0        0x8 emodules/fm/fm_module/si4760/Tuner.o
 .bss.bk_band   0x00000000e07096a8        0x1 emodules/fm/fm_module/si4760/Tuner.o
                0x00000000e07096a8                bk_band
 *fill*         0x00000000e07096a9        0x7 
 .bss.cmd       0x00000000e07096b0        0x8 emodules/fm/fm_module/si4760/Tuner.o
                0x00000000e07096b0                cmd
 .bss.current_dab_fm_linking_num
                0x00000000e07096b8        0x1 emodules/fm/fm_module/si4760/Tuner.o
                0x00000000e07096b8                current_dab_fm_linking_num
 *fill*         0x00000000e07096b9        0x7 
 .bss.current_link_info
                0x00000000e07096c0        0x4 emodules/fm/fm_module/si4760/Tuner.o
                0x00000000e07096c0                current_link_info
 .bss.dab_fm_link_backup_freq
                0x00000000e07096c4        0x2 emodules/fm/fm_module/si4760/Tuner.o
                0x00000000e07096c4                dab_fm_link_backup_freq
 .bss.dab_fm_pi_match_sucess
                0x00000000e07096c6        0x1 emodules/fm/fm_module/si4760/Tuner.o
                0x00000000e07096c6                dab_fm_pi_match_sucess
 .bss.dab_fm_scan_search_pi
                0x00000000e07096c7        0x1 emodules/fm/fm_module/si4760/Tuner.o
                0x00000000e07096c7                dab_fm_scan_search_pi
 .bss.dab_fm_scan_search_pi_match_flg
                0x00000000e07096c8        0x1 emodules/fm/fm_module/si4760/Tuner.o
                0x00000000e07096c8                dab_fm_scan_search_pi_match_flg
 .bss.dab_fm_start_link_sucess
                0x00000000e07096c9        0x1 emodules/fm/fm_module/si4760/Tuner.o
                0x00000000e07096c9                dab_fm_start_link_sucess
 *fill*         0x00000000e07096ca        0x6 
 .bss.dab_rds_linkings
                0x00000000e07096d0       0x80 emodules/fm/fm_module/si4760/Tuner.o
                0x00000000e07096d0                dab_rds_linkings
 .bss.goodcnt   0x00000000e0709750        0x1 emodules/fm/fm_module/si4760/Tuner.o
                0x00000000e0709750                goodcnt
 *fill*         0x00000000e0709751        0x7 
 .bss.link_pi_table
                0x00000000e0709758       0xc8 emodules/fm/fm_module/si4760/Tuner.o
                0x00000000e0709758                link_pi_table
 .bss.pro_pi    0x00000000e0709820        0x6 emodules/fm/fm_module/si4760/Tuner.o
                0x00000000e0709820                pro_pi
 *fill*         0x00000000e0709826        0x2 
 .bss.rsp       0x00000000e0709828        0xf emodules/fm/fm_module/si4760/Tuner.o
                0x00000000e0709828                rsp
 *fill*         0x00000000e0709837        0x1 
 .bss.si476x_aux_para
                0x00000000e0709838        0x8 emodules/fm/fm_module/si4760/Tuner.o
                0x00000000e0709838                si476x_aux_para
 .bss.si476x_bt_para
                0x00000000e0709840        0x8 emodules/fm/fm_module/si4760/Tuner.o
                0x00000000e0709840                si476x_bt_para
 .bss.si476x_bus_tid
                0x00000000e0709848        0x4 emodules/fm/fm_module/si4760/Tuner.o
 *fill*         0x00000000e070984c        0x4 
 .bss.si476x_fm_para
                0x00000000e0709850        0x8 emodules/fm/fm_module/si4760/Tuner.o
                0x00000000e0709850                si476x_fm_para
 .bss.si476x_init_para
                0x00000000e0709858        0x8 emodules/fm/fm_module/si4760/Tuner.o
                0x00000000e0709858                si476x_init_para
 .bss.si476x_root_para
                0x00000000e0709860        0x8 emodules/fm/fm_module/si4760/Tuner.o
                0x00000000e0709860                si476x_root_para
 .bss.si476x_system_para
                0x00000000e0709868        0x8 emodules/fm/fm_module/si4760/Tuner.o
                0x00000000e0709868                si476x_system_para
 .bss.tuner_fiic
                0x00000000e0709870        0x4 emodules/fm/fm_module/si4760/Tuner.o
                0x00000000e0709870                tuner_fiic
 *fill*         0x00000000e0709874        0x4 
 .bss.fm        0x00000000e0709878       0xb8 emodules/fm/dev_fm.o
                0x00000000e0709878                fm
 .bss.fm_drv    0x00000000e0709930       0x28 emodules/fm/drv_fm.o
                0x00000000e0709930                fm_drv
 .bss.fm_lock   0x00000000e0709958        0x8 emodules/fm/drv_fm.o
                0x00000000e0709958                fm_lock
 *(.gnu.linkonce.b.*)
 *(.sbss)
 *(.sbss.*)
 *(.gnu.linkonce.sb.*)
 *(.sbss2)
 *(.sbss2.*)
 *(.gnu.linkonce.sb2.*)
 *(COMMON)
 *(.scommon)
                0x00000000e0709960                . = ALIGN (0x4)
                0x00000000e0709960                __bss_end = ABSOLUTE (.)
                0x00000000e0709960                _end = ABSOLUTE (.)

MAGIC           0x00000000ffff0000       0x50
 *.o(.magic)
 .magic         0x00000000ffff0000       0x50 emodules/fm/magic.o
                0x00000000ffff0000                modinfo

.note
 *(.note)

.stab
 *(.stab)

.stabstr
 *(.stabstr)

.stab.excl
 *(.stab.excl)

.stab.exclstr
 *(.stab.exclstr)

.stab.index
 *(.stab.index)

.stab.indexstr
 *(.stab.indexstr)

.mdebug
 *(.mdebug)

.reginfo
 *(.reginfo)

.comment        0x0000000000000000       0x32
 *(.comment)
 .comment       0x0000000000000000       0x32 emodules/fm/fm_module/si4760/Rds.o
                                         0x33 (size before relaxing)
 .comment       0x0000000000000032       0x33 emodules/fm/fm_module/si4760/Rds_sub.o
 .comment       0x0000000000000032       0x33 emodules/fm/fm_module/si4760/Tu_Si_476X.o
 .comment       0x0000000000000032       0x33 emodules/fm/fm_module/si4760/Tuner.o
 .comment       0x0000000000000032       0x33 emodules/fm/dev_fm.o
 .comment       0x0000000000000032       0x33 emodules/fm/drv_fm.o
 .comment       0x0000000000000032       0x33 emodules/fm/magic.o
 .comment       0x0000000000000032       0x33 ./elibrary/bin//libsyscall.a(syscall.o)
 .comment       0x0000000000000032       0x33 ./elibrary/bin//libsyscall.a(syscall_fops.o)
 .comment       0x0000000000000032       0x33 ./elibrary/bin//libsyscall.a(syscall.o)
 .comment       0x0000000000000032       0x33 ./elibrary/bin//libminic.a(elibs_stdio.o)
 .comment       0x0000000000000032       0x33 ./elibrary/bin//libminic.a(elibs_string.o)
 .comment       0x0000000000000032       0x33 ./elibrary/bin//libminic.a(elibs_sprintf.o)
 .comment       0x0000000000000032       0x33 /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/../../../../riscv64-unknown-elf/lib/rv64imafdcxtheadc/lp64d/libc.a(lib_a-memcpy.o)
 .comment       0x0000000000000032       0x33 /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/../../../../riscv64-unknown-elf/lib/rv64imafdcxtheadc/lp64d/libc.a(lib_a-sysclose.o)
 .comment       0x0000000000000032       0x33 /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/../../../../riscv64-unknown-elf/lib/rv64imafdcxtheadc/lp64d/libc.a(lib_a-sysopen.o)
 .comment       0x0000000000000032       0x33 /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/../../../../riscv64-unknown-elf/lib/rv64imafdcxtheadc/lp64d/libc.a(lib_a-impure.o)

.ARM.attributes
 *(.ARM.attributes)

.riscv.attributes
                0x0000000000000000       0x48
 *(.riscv.attributes)
 .riscv.attributes
                0x0000000000000000       0x3d emodules/fm/fm_module/si4760/Rds.o
 .riscv.attributes
                0x000000000000003d       0x3d emodules/fm/fm_module/si4760/Rds_sub.o
 .riscv.attributes
                0x000000000000007a       0x3d emodules/fm/fm_module/si4760/Tu_Si_476X.o
 .riscv.attributes
                0x00000000000000b7       0x3d emodules/fm/fm_module/si4760/Tuner.o
 .riscv.attributes
                0x00000000000000f4       0x3d emodules/fm/dev_fm.o
 .riscv.attributes
                0x0000000000000131       0x3d emodules/fm/drv_fm.o
 .riscv.attributes
                0x000000000000016e       0x3d emodules/fm/magic.o
 .riscv.attributes
                0x00000000000001ab       0x3d ./elibrary/bin//libsyscall.a(syscall.o)
 .riscv.attributes
                0x00000000000001e8       0x3d ./elibrary/bin//libsyscall.a(syscall_fops.o)
 .riscv.attributes
                0x0000000000000225       0x3d ./elibrary/bin//libsyscall.a(syscall.o)
 .riscv.attributes
                0x0000000000000262       0x3d ./elibrary/bin//libminic.a(elibs_stdio.o)
 .riscv.attributes
                0x000000000000029f       0x3d ./elibrary/bin//libminic.a(elibs_string.o)
 .riscv.attributes
                0x00000000000002dc       0x3d ./elibrary/bin//libminic.a(elibs_sprintf.o)
 .riscv.attributes
                0x0000000000000319       0x3e /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/../../../../riscv64-unknown-elf/lib/rv64imafdcxtheadc/lp64d/libc.a(lib_a-memcpy.o)
 .riscv.attributes
                0x0000000000000357       0x3e /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/../../../../riscv64-unknown-elf/lib/rv64imafdcxtheadc/lp64d/libc.a(lib_a-memset.o)
 .riscv.attributes
                0x0000000000000395       0x3e /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/../../../../riscv64-unknown-elf/lib/rv64imafdcxtheadc/lp64d/libc.a(lib_a-sysclose.o)
 .riscv.attributes
                0x00000000000003d3       0x3e /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/../../../../riscv64-unknown-elf/lib/rv64imafdcxtheadc/lp64d/libc.a(lib_a-sysopen.o)
 .riscv.attributes
                0x0000000000000411       0x3e /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/../../../../riscv64-unknown-elf/lib/rv64imafdcxtheadc/lp64d/libc.a(lib_a-impure.o)

/DISCARD/
 *(.note.GNU-stack)
 *(.ARM.exidx* .gnu.linkonce.armexidx.*)
OUTPUT(emodules/fm/fm.o elf64-littleriscv)

.debug_info     0x0000000000000000    0x46fcd
 .debug_info    0x0000000000000000     0x5fa1 emodules/fm/fm_module/si4760/Rds.o
 .debug_info    0x0000000000005fa1     0x5949 emodules/fm/fm_module/si4760/Rds_sub.o
 .debug_info    0x000000000000b8ea     0x73a3 emodules/fm/fm_module/si4760/Tu_Si_476X.o
 .debug_info    0x0000000000012c8d     0xd523 emodules/fm/fm_module/si4760/Tuner.o
 .debug_info    0x00000000000201b0     0x3dc9 emodules/fm/dev_fm.o
 .debug_info    0x0000000000023f79     0x23f6 emodules/fm/drv_fm.o
 .debug_info    0x000000000002636f     0x161a emodules/fm/magic.o
 .debug_info    0x0000000000027989    0x12bf0 ./elibrary/bin//libsyscall.a(syscall.o)
 .debug_info    0x000000000003a579     0x2f5e ./elibrary/bin//libsyscall.a(syscall_fops.o)
 .debug_info    0x000000000003d4d7     0x5cd8 ./elibrary/bin//libsyscall.a(syscall.o)
 .debug_info    0x00000000000431af     0x2b1c ./elibrary/bin//libminic.a(elibs_stdio.o)
 .debug_info    0x0000000000045ccb      0xb2d ./elibrary/bin//libminic.a(elibs_string.o)
 .debug_info    0x00000000000467f8      0x7d5 ./elibrary/bin//libminic.a(elibs_sprintf.o)

.debug_abbrev   0x0000000000000000     0x309b
 .debug_abbrev  0x0000000000000000      0x50e emodules/fm/fm_module/si4760/Rds.o
 .debug_abbrev  0x000000000000050e      0x459 emodules/fm/fm_module/si4760/Rds_sub.o
 .debug_abbrev  0x0000000000000967      0x47c emodules/fm/fm_module/si4760/Tu_Si_476X.o
 .debug_abbrev  0x0000000000000de3      0x693 emodules/fm/fm_module/si4760/Tuner.o
 .debug_abbrev  0x0000000000001476      0x512 emodules/fm/dev_fm.o
 .debug_abbrev  0x0000000000001988      0x3a2 emodules/fm/drv_fm.o
 .debug_abbrev  0x0000000000001d2a      0x225 emodules/fm/magic.o
 .debug_abbrev  0x0000000000001f4f      0x3a2 ./elibrary/bin//libsyscall.a(syscall.o)
 .debug_abbrev  0x00000000000022f1      0x3ec ./elibrary/bin//libsyscall.a(syscall_fops.o)
 .debug_abbrev  0x00000000000026dd      0x34c ./elibrary/bin//libsyscall.a(syscall.o)
 .debug_abbrev  0x0000000000002a29      0x3d0 ./elibrary/bin//libminic.a(elibs_stdio.o)
 .debug_abbrev  0x0000000000002df9      0x155 ./elibrary/bin//libminic.a(elibs_string.o)
 .debug_abbrev  0x0000000000002f4e      0x14d ./elibrary/bin//libminic.a(elibs_sprintf.o)

.debug_loc      0x0000000000000000    0x1a426
 .debug_loc     0x0000000000000000      0x7d4 emodules/fm/fm_module/si4760/Rds.o
 .debug_loc     0x00000000000007d4      0x7a2 emodules/fm/fm_module/si4760/Rds_sub.o
 .debug_loc     0x0000000000000f76      0xc38 emodules/fm/fm_module/si4760/Tu_Si_476X.o
 .debug_loc     0x0000000000001bae     0x2498 emodules/fm/fm_module/si4760/Tuner.o
 .debug_loc     0x0000000000004046      0xa8d emodules/fm/dev_fm.o
 .debug_loc     0x0000000000004ad3      0x2bb emodules/fm/drv_fm.o
 .debug_loc     0x0000000000004d8e     0xda5d ./elibrary/bin//libsyscall.a(syscall.o)
 .debug_loc     0x00000000000127eb     0x1325 ./elibrary/bin//libsyscall.a(syscall_fops.o)
 .debug_loc     0x0000000000013b10     0x385a ./elibrary/bin//libsyscall.a(syscall.o)
 .debug_loc     0x000000000001736a     0x25c5 ./elibrary/bin//libminic.a(elibs_stdio.o)
 .debug_loc     0x000000000001992f      0x705 ./elibrary/bin//libminic.a(elibs_string.o)
 .debug_loc     0x000000000001a034      0x3f2 ./elibrary/bin//libminic.a(elibs_sprintf.o)

.debug_aranges  0x0000000000000000     0x3310
 .debug_aranges
                0x0000000000000000      0x1a0 emodules/fm/fm_module/si4760/Rds.o
 .debug_aranges
                0x00000000000001a0      0x110 emodules/fm/fm_module/si4760/Rds_sub.o
 .debug_aranges
                0x00000000000002b0      0x1d0 emodules/fm/fm_module/si4760/Tu_Si_476X.o
 .debug_aranges
                0x0000000000000480      0x590 emodules/fm/fm_module/si4760/Tuner.o
 .debug_aranges
                0x0000000000000a10      0x110 emodules/fm/dev_fm.o
 .debug_aranges
                0x0000000000000b20       0x80 emodules/fm/drv_fm.o
 .debug_aranges
                0x0000000000000ba0       0x20 emodules/fm/magic.o
 .debug_aranges
                0x0000000000000bc0     0x1840 ./elibrary/bin//libsyscall.a(syscall.o)
 .debug_aranges
                0x0000000000002400      0x260 ./elibrary/bin//libsyscall.a(syscall_fops.o)
 .debug_aranges
                0x0000000000002660      0x660 ./elibrary/bin//libsyscall.a(syscall.o)
 .debug_aranges
                0x0000000000002cc0      0x480 ./elibrary/bin//libminic.a(elibs_stdio.o)
 .debug_aranges
                0x0000000000003140      0x150 ./elibrary/bin//libminic.a(elibs_string.o)
 .debug_aranges
                0x0000000000003290       0x80 ./elibrary/bin//libminic.a(elibs_sprintf.o)

.debug_line     0x0000000000000000    0x29de8
 .debug_line    0x0000000000000000     0x2580 emodules/fm/fm_module/si4760/Rds.o
 .debug_line    0x0000000000002580     0x1c15 emodules/fm/fm_module/si4760/Rds_sub.o
 .debug_line    0x0000000000004195     0x5079 emodules/fm/fm_module/si4760/Tu_Si_476X.o
 .debug_line    0x000000000000920e     0x77ff emodules/fm/fm_module/si4760/Tuner.o
 .debug_line    0x0000000000010a0d     0x1b3a emodules/fm/dev_fm.o
 .debug_line    0x0000000000012547      0x957 emodules/fm/drv_fm.o
 .debug_line    0x0000000000012e9e      0x5dc emodules/fm/magic.o
 .debug_line    0x000000000001347a     0xe68f ./elibrary/bin//libsyscall.a(syscall.o)
 .debug_line    0x0000000000021b09     0x1864 ./elibrary/bin//libsyscall.a(syscall_fops.o)
 .debug_line    0x000000000002336d     0x3d44 ./elibrary/bin//libsyscall.a(syscall.o)
 .debug_line    0x00000000000270b1     0x22a9 ./elibrary/bin//libminic.a(elibs_stdio.o)
 .debug_line    0x000000000002935a      0x5f9 ./elibrary/bin//libminic.a(elibs_string.o)
 .debug_line    0x0000000000029953      0x495 ./elibrary/bin//libminic.a(elibs_sprintf.o)

.debug_str      0x0000000000000000    0x108d9
 .debug_str     0x0000000000000000     0x42c8 emodules/fm/fm_module/si4760/Rds.o
                                       0x4790 (size before relaxing)
 .debug_str     0x00000000000042c8      0x125 emodules/fm/fm_module/si4760/Rds_sub.o
                                       0x3d42 (size before relaxing)
 .debug_str     0x00000000000043ed     0x154d emodules/fm/fm_module/si4760/Tu_Si_476X.o
                                       0x538c (size before relaxing)
 .debug_str     0x000000000000593a     0x45b3 emodules/fm/fm_module/si4760/Tuner.o
                                       0xa1ec (size before relaxing)
 .debug_str     0x0000000000009eed     0x1a95 emodules/fm/dev_fm.o
                                       0x2fda (size before relaxing)
 .debug_str     0x000000000000b982      0x644 emodules/fm/drv_fm.o
                                       0x1a45 (size before relaxing)
 .debug_str     0x000000000000bfc6       0x5a emodules/fm/magic.o
                                       0x1289 (size before relaxing)
 .debug_str     0x000000000000c020     0x2c43 ./elibrary/bin//libsyscall.a(syscall.o)
                                       0x3c33 (size before relaxing)
 .debug_str     0x000000000000ec63      0x1aa ./elibrary/bin//libsyscall.a(syscall_fops.o)
                                       0x1068 (size before relaxing)
 .debug_str     0x000000000000ee0d      0xc6c ./elibrary/bin//libsyscall.a(syscall.o)
                                       0x1a68 (size before relaxing)
 .debug_str     0x000000000000fa79      0xa65 ./elibrary/bin//libminic.a(elibs_stdio.o)
                                       0x1624 (size before relaxing)
 .debug_str     0x00000000000104de      0x37a ./elibrary/bin//libminic.a(elibs_string.o)
                                        0x6d8 (size before relaxing)
 .debug_str     0x0000000000010858       0x81 ./elibrary/bin//libminic.a(elibs_sprintf.o)
                                        0x5d3 (size before relaxing)

.debug_frame    0x0000000000000000     0x7d10
 .debug_frame   0x0000000000000000      0x3f8 emodules/fm/fm_module/si4760/Rds.o
 .debug_frame   0x00000000000003f8      0x248 emodules/fm/fm_module/si4760/Rds_sub.o
 .debug_frame   0x0000000000000640      0x5b0 emodules/fm/fm_module/si4760/Tu_Si_476X.o
 .debug_frame   0x0000000000000bf0      0xed8 emodules/fm/fm_module/si4760/Tuner.o
 .debug_frame   0x0000000000001ac8      0x298 emodules/fm/dev_fm.o
 .debug_frame   0x0000000000001d60       0xf8 emodules/fm/drv_fm.o
 .debug_frame   0x0000000000001e58     0x3c60 ./elibrary/bin//libsyscall.a(syscall.o)
 .debug_frame   0x0000000000005ab8      0x5b8 ./elibrary/bin//libsyscall.a(syscall_fops.o)
 .debug_frame   0x0000000000006070      0xfb0 ./elibrary/bin//libsyscall.a(syscall.o)
 .debug_frame   0x0000000000007020      0xa28 ./elibrary/bin//libminic.a(elibs_stdio.o)
 .debug_frame   0x0000000000007a48      0x1e8 ./elibrary/bin//libminic.a(elibs_string.o)
 .debug_frame   0x0000000000007c30       0xe0 ./elibrary/bin//libminic.a(elibs_sprintf.o)

Cross Reference Table

Symbol                                            File
Caculate_SMeter                                   emodules/fm/fm_module/si4760/Rds_sub.o
                                                  emodules/fm/fm_module/si4760/Tu_Si_476X.o
CircleBufCreate                                   emodules/fm/dev_fm.o
CircleBufDestroy                                  emodules/fm/dev_fm.o
CircleBufQuerySize                                emodules/fm/dev_fm.o
CircleBufRead                                     emodules/fm/dev_fm.o
CircleBufWrite                                    emodules/fm/dev_fm.o
Cmd_Function_Band                                 emodules/fm/fm_module/si4760/Tuner.o
                                                  emodules/fm/dev_fm.o
Cmd_PTY_Sel                                       emodules/fm/fm_module/si4760/Tuner.o
Cmd_Pset_Sel                                      emodules/fm/fm_module/si4760/Tuner.o
Cur_Tuner_Band                                    emodules/fm/fm_module/si4760/Tuner.o
                                                  emodules/fm/fm_module/si4760/Tu_Si_476X.o
DRV_FM_MClose                                     emodules/fm/drv_fm.o
                                                  emodules/fm/magic.o
DRV_FM_MExit                                      emodules/fm/drv_fm.o
                                                  emodules/fm/magic.o
DRV_FM_MInit                                      emodules/fm/drv_fm.o
                                                  emodules/fm/magic.o
DRV_FM_MIoctrl                                    emodules/fm/drv_fm.o
                                                  emodules/fm/magic.o
DRV_FM_MOpen                                      emodules/fm/drv_fm.o
                                                  emodules/fm/magic.o
DRV_FM_MRead                                      emodules/fm/drv_fm.o
                                                  emodules/fm/magic.o
DRV_FM_MWrite                                     emodules/fm/drv_fm.o
                                                  emodules/fm/magic.o
Display_PTY                                       emodules/fm/fm_module/si4760/Tuner.o
Eonpi                                             emodules/fm/fm_module/si4760/Rds.o
                                                  emodules/fm/fm_module/si4760/Tuner.o
                                                  emodules/fm/fm_module/si4760/Tu_Si_476X.o
Get_Tuner_Init_Status                             emodules/fm/fm_module/si4760/Tuner.o
                                                  emodules/fm/dev_fm.o
Get_band_inf                                      emodules/fm/fm_module/si4760/Tuner.o
Getfrequency                                      emodules/fm/fm_module/si4760/Tuner.o
                                                  emodules/fm/fm_module/si4760/Tu_Si_476X.o
I2c_status_err                                    emodules/fm/fm_module/si4760/Tuner.o
                                                  emodules/fm/fm_module/si4760/Tu_Si_476X.o
Iic_Adress_error                                  emodules/fm/fm_module/si4760/Tuner.o
                                                  emodules/fm/fm_module/si4760/Tu_Si_476X.o
Init_Rds_para                                     emodules/fm/fm_module/si4760/Tuner.o
Init_Tuner                                        emodules/fm/fm_module/si4760/Tuner.o
Mem_Code                                          emodules/fm/fm_module/si4760/Tuner.o
                                                  emodules/fm/fm_module/si4760/Tu_Si_476X.o
Meth_Clear                                        emodules/fm/fm_module/si4760/Rds.o
Network_af_count                                  emodules/fm/fm_module/si4760/Rds_sub.o
Network_af_store                                  emodules/fm/fm_module/si4760/Rds_sub.o
                                                  emodules/fm/fm_module/si4760/Rds.o
Network_af_store_ex                               emodules/fm/fm_module/si4760/Rds_sub.o
Network_clear                                     emodules/fm/fm_module/si4760/Rds_sub.o
                                                  emodules/fm/fm_module/si4760/Tuner.o
                                                  emodules/fm/fm_module/si4760/Tu_Si_476X.o
                                                  emodules/fm/fm_module/si4760/Rds.o
Network_get_af                                    emodules/fm/fm_module/si4760/Rds_sub.o
Network_get_ctx                                   emodules/fm/fm_module/si4760/Rds_sub.o
                                                  emodules/fm/fm_module/si4760/Tuner.o
                                                  emodules/fm/fm_module/si4760/Tu_Si_476X.o
Network_get_frq                                   emodules/fm/fm_module/si4760/Rds_sub.o
                                                  emodules/fm/fm_module/si4760/Tu_Si_476X.o
Network_lookup_af                                 emodules/fm/fm_module/si4760/Rds_sub.o
Network_lookup_pi                                 emodules/fm/fm_module/si4760/Rds_sub.o
                                                  emodules/fm/fm_module/si4760/Tuner.o
Network_netpi                                     emodules/fm/fm_module/si4760/Rds_sub.o
                                                  emodules/fm/fm_module/si4760/Tuner.o
                                                  emodules/fm/fm_module/si4760/Tu_Si_476X.o
                                                  emodules/fm/fm_module/si4760/Rds.o
Network_pi_cmp                                    emodules/fm/fm_module/si4760/Rds_sub.o
                                                  emodules/fm/fm_module/si4760/Tuner.o
Network_resume                                    emodules/fm/fm_module/si4760/Rds_sub.o
                                                  emodules/fm/fm_module/si4760/Tuner.o
                                                  emodules/fm/fm_module/si4760/Tu_Si_476X.o
Network_set_sequence                              emodules/fm/fm_module/si4760/Rds_sub.o
                                                  emodules/fm/fm_module/si4760/Tu_Si_476X.o
PI_Clear                                          emodules/fm/fm_module/si4760/Rds.o
                                                  emodules/fm/fm_module/si4760/Tu_Si_476X.o
PSBuf_Clear                                       emodules/fm/fm_module/si4760/Rds.o
PS_Clear                                          emodules/fm/fm_module/si4760/Rds.o
PTY_Clear                                         emodules/fm/fm_module/si4760/Rds.o
Pll_Initial_5X                                    emodules/fm/fm_module/si4760/Tu_Si_476X.o
Preset_Name                                       emodules/fm/fm_module/si4760/Rds.o
RDS_Reset                                         emodules/fm/fm_module/si4760/Rds.o
                                                  emodules/fm/fm_module/si4760/Tuner.o
                                                  emodules/fm/fm_module/si4760/Tu_Si_476X.o
RDS_data_process_5X                               emodules/fm/fm_module/si4760/Rds.o
Rds_QualityCheck                                  emodules/fm/fm_module/si4760/Tuner.o
Rds_QualityCheck_5X                               emodules/fm/fm_module/si4760/Tu_Si_476X.o
                                                  emodules/fm/fm_module/si4760/Tuner.o
Read475x_RdsStatus                                emodules/fm/fm_module/si4760/Rds.o
                                                  emodules/fm/fm_module/si4760/Tuner.o
ReplaceFreq                                       emodules/fm/fm_module/si4760/Rds_sub.o
                                                  emodules/fm/fm_module/si4760/Tu_Si_476X.o
Search_PresetNum                                  emodules/fm/fm_module/si4760/Tuner.o
                                                  emodules/fm/fm_module/si4760/Tu_Si_476X.o
Si473X_mute                                       emodules/fm/fm_module/si4760/Tuner.o
                                                  emodules/fm/fm_module/si4760/Tu_Si_476X.o
Si475X_ReadAcfStatus                              emodules/fm/fm_module/si4760/Tu_Si_476X.o
                                                  emodules/fm/fm_module/si4760/Tuner.o
Si475X_ReadSeekStatus                             emodules/fm/fm_module/si4760/Tu_Si_476X.o
Si475X_Tune_Freq                                  emodules/fm/fm_module/si4760/Tu_Si_476X.o
                                                  emodules/fm/fm_module/si4760/Tuner.o
Si475x_Disable_RDS                                emodules/fm/fm_module/si4760/Tu_Si_476X.o
                                                  emodules/fm/fm_module/si4760/Rds.o
Si475x_Init_RDS                                   emodules/fm/fm_module/si4760/Tu_Si_476X.o
                                                  emodules/fm/fm_module/si4760/Rds.o
Si475x_Int_disable                                emodules/fm/fm_module/si4760/Tu_Si_476X.o
Si475x_Int_enable                                 emodules/fm/fm_module/si4760/Tu_Si_476X.o
Si47XX_Tune_Freq                                  emodules/fm/fm_module/si4760/Tuner.o
                                                  emodules/fm/fm_module/si4760/Tu_Si_476X.o
Si_Disable_RDS                                    emodules/fm/fm_module/si4760/Rds.o
                                                  emodules/fm/fm_module/si4760/Tuner.o
Si_Init_RDS                                       emodules/fm/fm_module/si4760/Rds.o
                                                  emodules/fm/fm_module/si4760/Tuner.o
Store_scroll                                      emodules/fm/fm_module/si4760/Tuner.o
TA_Read_5X                                        emodules/fm/fm_module/si4760/Rds.o
TPTA_Clear                                        emodules/fm/fm_module/si4760/Rds.o
                                                  emodules/fm/fm_module/si4760/Tu_Si_476X.o
Tu_Band                                           emodules/fm/fm_module/si4760/Tuner.o
                                                  emodules/fm/fm_module/si4760/Tu_Si_476X.o
                                                  emodules/fm/fm_module/si4760/Rds_sub.o
                                                  emodules/fm/fm_module/si4760/Rds.o
Tu_Divisor                                        emodules/fm/fm_module/si4760/Tuner.o
                                                  emodules/fm/fm_module/si4760/Tu_Si_476X.o
                                                  emodules/fm/fm_module/si4760/Rds_sub.o
Tu_Freq_Offset                                    emodules/fm/fm_module/si4760/Tuner.o
                                                  emodules/fm/fm_module/si4760/Tu_Si_476X.o
Tu_No_Patch                                       emodules/fm/fm_module/si4760/Tuner.o
                                                  emodules/fm/fm_module/si4760/Tu_Si_476X.o
Tu_Preset                                         emodules/fm/fm_module/si4760/Tuner.o
                                                  emodules/fm/fm_module/si4760/Tu_Si_476X.o
                                                  emodules/fm/fm_module/si4760/Rds_sub.o
Tu_Rds_option                                     emodules/fm/fm_module/si4760/Tuner.o
                                                  emodules/fm/fm_module/si4760/Tu_Si_476X.o
                                                  emodules/fm/fm_module/si4760/Rds_sub.o
                                                  emodules/fm/fm_module/si4760/Rds.o
Tu_Read_Buff                                      emodules/fm/fm_module/si4760/Tuner.o
                                                  emodules/fm/fm_module/si4760/Tu_Si_476X.o
                                                  emodules/fm/fm_module/si4760/Rds.o
Tu_Snr                                            emodules/fm/fm_module/si4760/Tuner.o
                                                  emodules/fm/fm_module/si4760/Tu_Si_476X.o
                                                  emodules/fm/fm_module/si4760/Rds_sub.o
                                                  emodules/fm/fm_module/si4760/Rds.o
Tu_band_rds_fm                                    emodules/fm/fm_module/si4760/Tuner.o
                                                  emodules/fm/fm_module/si4760/Tu_Si_476X.o
                                                  emodules/fm/fm_module/si4760/Rds_sub.o
Tu_blc_flg                                        emodules/fm/fm_module/si4760/Tuner.o
                                                  emodules/fm/fm_module/si4760/Tu_Si_476X.o
                                                  emodules/fm/fm_module/si4760/Rds_sub.o
                                                  emodules/fm/fm_module/si4760/Rds.o
Tu_divisor_step                                   emodules/fm/fm_module/si4760/Tuner.o
                                                  emodules/fm/fm_module/si4760/Tu_Si_476X.o
                                                  emodules/fm/fm_module/si4760/Rds_sub.o
Tu_freq_max_con                                   emodules/fm/fm_module/si4760/Tuner.o
                                                  emodules/fm/fm_module/si4760/Tu_Si_476X.o
Tu_freq_min                                       emodules/fm/fm_module/si4760/Tuner.o
                                                  emodules/fm/fm_module/si4760/Tu_Si_476X.o
Tu_frequency                                      emodules/fm/fm_module/si4760/Tuner.o
                                                  emodules/fm/fm_module/si4760/Tu_Si_476X.o
                                                  emodules/fm/fm_module/si4760/Rds.o
Tu_last_divisor                                   emodules/fm/fm_module/si4760/Tuner.o
                                                  emodules/fm/fm_module/si4760/Tu_Si_476X.o
Tu_preset_num                                     emodules/fm/fm_module/si4760/Tuner.o
                                                  emodules/fm/fm_module/si4760/Tu_Si_476X.o
                                                  emodules/fm/fm_module/si4760/Rds_sub.o
                                                  emodules/fm/fm_module/si4760/Rds.o
Tu_pro_freq                                       emodules/fm/fm_module/si4760/Tuner.o
                                                  emodules/fm/fm_module/si4760/Tu_Si_476X.o
Tu_rds_flags                                      emodules/fm/fm_module/si4760/Tuner.o
                                                  emodules/fm/fm_module/si4760/Tu_Si_476X.o
                                                  emodules/fm/fm_module/si4760/Rds.o
Tu_smeter                                         emodules/fm/fm_module/si4760/Tuner.o
                                                  emodules/fm/fm_module/si4760/Tu_Si_476X.o
                                                  emodules/fm/fm_module/si4760/Rds_sub.o
                                                  emodules/fm/fm_module/si4760/Rds.o
Tune_AF_Seek_end                                  emodules/fm/fm_module/si4760/Tuner.o
Tune_Down_fStep                                   emodules/fm/fm_module/si4760/Tuner.o
                                                  emodules/fm/fm_module/si4760/Tu_Si_476X.o
Tune_Half_Sec_Timer                               emodules/fm/fm_module/si4760/Tuner.o
Tune_Mute_Timer                                   emodules/fm/fm_module/si4760/Tuner.o
Tune_Mute_Timer_af                                emodules/fm/fm_module/si4760/Tuner.o
Tune_Option_Band                                  emodules/fm/fm_module/si4760/Tuner.o
Tune_Option_Lever                                 emodules/fm/fm_module/si4760/Tuner.o
Tune_Option_OK                                    emodules/fm/fm_module/si4760/Tuner.o
Tune_Option_State                                 emodules/fm/fm_module/si4760/Tuner.o
Tune_Process_Timer                                emodules/fm/fm_module/si4760/Tuner.o
                                                  emodules/fm/fm_module/si4760/Tu_Si_476X.o
Tune_Process_Timer_Af                             emodules/fm/fm_module/si4760/Tuner.o
Tune_STC_OK                                       emodules/fm/fm_module/si4760/Tuner.o
                                                  emodules/fm/fm_module/si4760/Tu_Si_476X.o
Tune_Step_Timer                                   emodules/fm/fm_module/si4760/Tuner.o
Tune_Up_fStep                                     emodules/fm/fm_module/si4760/Tuner.o
                                                  emodules/fm/fm_module/si4760/Tu_Si_476X.o
Tune_freq_int                                     emodules/fm/fm_module/si4760/Tuner.o
                                                  emodules/fm/fm_module/si4760/Tu_Si_476X.o
Tuner_ACK_error                                   emodules/fm/fm_module/si4760/Tuner.o
                                                  emodules/fm/fm_module/si4760/Tu_Si_476X.o
Tuner_AF_Confirm_Timers                           emodules/fm/fm_module/si4760/Tu_Si_476X.o
Tuner_App_GET_SET_TU                              emodules/fm/fm_module/si4760/Tuner.o
Tuner_Area                                        emodules/fm/fm_module/si4760/Tuner.o
                                                  emodules/fm/fm_module/si4760/Tu_Si_476X.o
Tuner_Flag0                                       emodules/fm/fm_module/si4760/Tuner.o
                                                  emodules/fm/fm_module/si4760/Tu_Si_476X.o
                                                  emodules/fm/fm_module/si4760/Rds.o
Tuner_Flag1                                       emodules/fm/fm_module/si4760/Tuner.o
                                                  emodules/fm/fm_module/si4760/Tu_Si_476X.o
Tuner_HU_SET_App                                  emodules/fm/fm_module/si4760/Tuner.o
                                                  emodules/fm/fm_module/si4760/Tu_Si_476X.o
Tuner_IIC_Read                                    emodules/fm/fm_module/si4760/Tuner.o
                                                  emodules/fm/fm_module/si4760/Tu_Si_476X.o
Tuner_IIC_Write                                   emodules/fm/fm_module/si4760/Tuner.o
                                                  emodules/fm/fm_module/si4760/Tu_Si_476X.o
Tuner_Init_Proc                                   emodules/fm/fm_module/si4760/Tuner.o
Tuner_Init_Proc_5X                                emodules/fm/fm_module/si4760/Tu_Si_476X.o
                                                  emodules/fm/fm_module/si4760/Tuner.o
Tuner_Init_Status                                 emodules/fm/fm_module/si4760/Tuner.o
                                                  emodules/fm/fm_module/si4760/Tu_Si_476X.o
                                                  emodules/fm/fm_module/si4760/Rds.o
Tuner_Init_mute_status                            emodules/fm/fm_module/si4760/Tuner.o
Tuner_Key_Process                                 emodules/fm/fm_module/si4760/Tuner.o
                                                  emodules/fm/dev_fm.o
Tuner_Mode_Exit                                   emodules/fm/fm_module/si4760/Tuner.o
Tuner_Mode_Proc                                   emodules/fm/fm_module/si4760/Tuner.o
Tuner_Mode_Proc_5X                                emodules/fm/fm_module/si4760/Tu_Si_476X.o
                                                  emodules/fm/fm_module/si4760/Tuner.o
Tuner_Read_Band                                   emodules/fm/fm_module/si4760/Tuner.o
                                                  emodules/fm/fm_module/si4760/Tu_Si_476X.o
Tuner_Read_Band_cnt                               emodules/fm/fm_module/si4760/Tuner.o
                                                  emodules/fm/fm_module/si4760/Tu_Si_476X.o
Tuner_Read_Band_timeout                           emodules/fm/fm_module/si4760/Tuner.o
                                                  emodules/fm/fm_module/si4760/Tu_Si_476X.o
Tuner_Read_memory                                 emodules/fm/fm_module/si4760/Tuner.o
Tuner_Reset_Timer                                 emodules/fm/fm_module/si4760/Tuner.o
                                                  emodules/fm/fm_module/si4760/Tu_Si_476X.o
Tuner_Set_memory                                  emodules/fm/fm_module/si4760/Tuner.o
                                                  emodules/fm/fm_module/si4760/Tu_Si_476X.o
Tuner_Status                                      emodules/fm/fm_module/si4760/Tuner.o
                                                  emodules/fm/fm_module/si4760/Tu_Si_476X.o
                                                  emodules/fm/fm_module/si4760/Rds.o
Tuner_Ver                                         emodules/fm/fm_module/si4760/Tuner.o
Tuner_auto_seek_count                             emodules/fm/fm_module/si4760/Tuner.o
                                                  emodules/fm/fm_module/si4760/Tu_Si_476X.o
Tuner_auto_seek_invalid_count                     emodules/fm/fm_module/si4760/Tuner.o
                                                  emodules/fm/fm_module/si4760/Tu_Si_476X.o
Tuner_auto_seek_valid_count                       emodules/fm/fm_module/si4760/Tuner.o
                                                  emodules/fm/fm_module/si4760/Tu_Si_476X.o
Tuner_communication_error                         emodules/fm/fm_module/si4760/Tuner.o
Tuner_func_set_area                               emodules/fm/fm_module/si4760/Tuner.o
                                                  emodules/fm/dev_fm.o
Tuner_get_dab_link_rds_status                     emodules/fm/fm_module/si4760/Tuner.o
                                                  emodules/fm/dev_fm.o
Tuner_iic_timeout                                 emodules/fm/fm_module/si4760/Tuner.o
                                                  emodules/fm/fm_module/si4760/Tu_Si_476X.o
Tuner_init_again                                  emodules/fm/fm_module/si4760/Tuner.o
Tuner_init_error                                  emodules/fm/fm_module/si4760/Tuner.o
                                                  emodules/fm/fm_module/si4760/Tu_Si_476X.o
Tuner_key_Menu_Seek                               emodules/fm/fm_module/si4760/Tuner.o
Tuner_key_Tune_Freq                               emodules/fm/fm_module/si4760/Tuner.o
Tuner_key_nextprev                                emodules/fm/fm_module/si4760/Tuner.o
Tuner_key_pty_Seek                                emodules/fm/fm_module/si4760/Tuner.o
                                                  emodules/fm/dev_fm.o
Tuner_set_mute                                    emodules/fm/fm_module/si4760/Tuner.o
                                                  emodules/fm/dev_fm.o
Tuner_start_search_dab_pi_for_rds_link            emodules/fm/fm_module/si4760/Tuner.o
                                                  emodules/fm/dev_fm.o
Tuner_status                                      emodules/fm/fm_module/si4760/Tuner.o
                                                  emodules/fm/dev_fm.o
Tuner_switch_fm_band_for_dab_link                 emodules/fm/fm_module/si4760/Tuner.o
                                                  emodules/fm/dev_fm.o
Tuner_switch_output_channel_for_dab_link          emodules/fm/fm_module/si4760/Tuner.o
                                                  emodules/fm/dev_fm.o
__libc_init_array                                 ./elibrary/bin//libsyscall.a(syscall_fops.o)
__malloc_lock                                     ./elibrary/bin//libsyscall.a(syscall_fops.o)
__malloc_unlock                                   ./elibrary/bin//libsyscall.a(syscall_fops.o)
_calloc_r                                         ./elibrary/bin//libsyscall.a(syscall_fops.o)
_close_r                                          ./elibrary/bin//libsyscall.a(syscall_fops.o)
                                                  /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/../../../../riscv64-unknown-elf/lib/rv64imafdcxtheadc/lp64d/libc.a(lib_a-sysclose.o)
_free_r                                           ./elibrary/bin//libsyscall.a(syscall_fops.o)
                                                  /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/../../../../riscv64-unknown-elf/lib/rv64imafdcxtheadc/lp64d/libc.a(lib_a-malloc.o)
_fstat_r                                          ./elibrary/bin//libsyscall.a(syscall_fops.o)
_gettimeofday_r                                   ./elibrary/bin//libsyscall.a(syscall_fops.o)
_global_impure_ptr                                /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/../../../../riscv64-unknown-elf/lib/rv64imafdcxtheadc/lp64d/libc.a(lib_a-impure.o)
_impure_ptr                                       /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/../../../../riscv64-unknown-elf/lib/rv64imafdcxtheadc/lp64d/libc.a(lib_a-impure.o)
                                                  /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/../../../../riscv64-unknown-elf/lib/rv64imafdcxtheadc/lp64d/libc.a(lib_a-syswrite.o)
                                                  /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/../../../../riscv64-unknown-elf/lib/rv64imafdcxtheadc/lp64d/libc.a(lib_a-sysopen.o)
                                                  /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/../../../../riscv64-unknown-elf/lib/rv64imafdcxtheadc/lp64d/libc.a(lib_a-sysclose.o)
                                                  /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/../../../../riscv64-unknown-elf/lib/rv64imafdcxtheadc/lp64d/libc.a(lib_a-malloc.o)
_isatty                                           ./elibrary/bin//libsyscall.a(syscall_fops.o)
_isatty_r                                         ./elibrary/bin//libsyscall.a(syscall_fops.o)
_lseek_r                                          ./elibrary/bin//libsyscall.a(syscall_fops.o)
_malloc_r                                         ./elibrary/bin//libsyscall.a(syscall_fops.o)
                                                  /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/../../../../riscv64-unknown-elf/lib/rv64imafdcxtheadc/lp64d/libc.a(lib_a-malloc.o)
_mkdir_r                                          ./elibrary/bin//libsyscall.a(syscall_fops.o)
_open_r                                           ./elibrary/bin//libsyscall.a(syscall_fops.o)
                                                  /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/../../../../riscv64-unknown-elf/lib/rv64imafdcxtheadc/lp64d/libc.a(lib_a-sysopen.o)
_read_r                                           ./elibrary/bin//libsyscall.a(syscall_fops.o)
_realloc_r                                        ./elibrary/bin//libsyscall.a(syscall_fops.o)
_rename_r                                         ./elibrary/bin//libsyscall.a(syscall_fops.o)
_stat_r                                           ./elibrary/bin//libsyscall.a(syscall_fops.o)
_system                                           ./elibrary/bin//libsyscall.a(syscall_fops.o)
_tuner_mute                                       emodules/fm/fm_module/si4760/Tuner.o
_tuner_no_mute                                    emodules/fm/fm_module/si4760/Tuner.o
                                                  emodules/fm/fm_module/si4760/Tu_Si_476X.o
_tuner_set_mcu_io                                 emodules/fm/fm_module/si4760/Tuner.o
_unlink_r                                         ./elibrary/bin//libsyscall.a(syscall_fops.o)
_write_r                                          ./elibrary/bin//libsyscall.a(syscall_fops.o)
                                                  /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/../../../../riscv64-unknown-elf/lib/rv64imafdcxtheadc/lp64d/libc.a(lib_a-syswrite.o)
access                                            ./elibrary/bin//libsyscall.a(syscall_fops.o)
add_dab_to_fm_link_pi                             emodules/fm/fm_module/si4760/Rds.o
af_1st_divisor                                    emodules/fm/fm_module/si4760/Tuner.o
af_cnt                                            emodules/fm/fm_module/si4760/Rds.o
                                                  emodules/fm/fm_module/si4760/Tuner.o
                                                  emodules/fm/fm_module/si4760/Tu_Si_476X.o
af_mask_timer                                     emodules/fm/fm_module/si4760/Rds.o
                                                  emodules/fm/fm_module/si4760/Tu_Si_476X.o
                                                  emodules/fm/fm_module/si4760/Rds_sub.o
af_time                                           emodules/fm/fm_module/si4760/Rds.o
                                                  emodules/fm/fm_module/si4760/Tuner.o
                                                  emodules/fm/fm_module/si4760/Tu_Si_476X.o
af_time2                                          emodules/fm/fm_module/si4760/Rds.o
                                                  emodules/fm/fm_module/si4760/Tuner.o
                                                  emodules/fm/fm_module/si4760/Tu_Si_476X.o
af_time3                                          emodules/fm/fm_module/si4760/Rds.o
                                                  emodules/fm/fm_module/si4760/Tuner.o
                                                  emodules/fm/fm_module/si4760/Tu_Si_476X.o
audio_start                                       emodules/fm/dev_fm.o
audio_stop                                        emodules/fm/dev_fm.o
auto_store_start                                  emodules/fm/fm_module/si4760/Tuner.o
                                                  emodules/fm/fm_module/si4760/Tu_Si_476X.o
autostore_end                                     emodules/fm/fm_module/si4760/Tuner.o
                                                  emodules/fm/fm_module/si4760/Tu_Si_476X.o
autostore_save                                    emodules/fm/fm_module/si4760/Tuner.o
                                                  emodules/fm/fm_module/si4760/Tu_Si_476X.o
backtrace                                         ./elibrary/bin//libsyscall.a(syscall.o)
bfpi                                              emodules/fm/fm_module/si4760/Rds.o
bfps                                              emodules/fm/fm_module/si4760/Rds.o
bfpty                                             emodules/fm/fm_module/si4760/Rds.o
                                                  emodules/fm/fm_module/si4760/Tu_Si_476X.o
bk_band                                           emodules/fm/fm_module/si4760/Tuner.o
bkpi                                              emodules/fm/fm_module/si4760/Rds.o
                                                  emodules/fm/fm_module/si4760/Tuner.o
                                                  emodules/fm/fm_module/si4760/Tu_Si_476X.o
                                                  emodules/fm/fm_module/si4760/Rds_sub.o
circle_buf                                        emodules/fm/dev_fm.o
clear_dab_fm_link_current_info                    emodules/fm/fm_module/si4760/Tuner.o
clock_getres                                      ./elibrary/bin//libsyscall.a(syscall.o)
clock_gettime                                     ./elibrary/bin//libsyscall.a(syscall.o)
clock_settime                                     ./elibrary/bin//libsyscall.a(syscall.o)
close                                             /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/../../../../riscv64-unknown-elf/lib/rv64imafdcxtheadc/lp64d/libc.a(lib_a-sysclose.o)
                                                  emodules/fm/drv_fm.o
                                                  emodules/fm/dev_fm.o
                                                  emodules/fm/fm_module/si4760/Tuner.o
                                                  emodules/fm/fm_module/si4760/Tu_Si_476X.o
                                                  emodules/fm/fm_module/si4760/Rds.o
closedir                                          ./elibrary/bin//libsyscall.a(syscall_fops.o)
cmd                                               emodules/fm/fm_module/si4760/Tuner.o
curr_af                                           emodules/fm/fm_module/si4760/Rds.o
curr_af_cnt                                       emodules/fm/fm_module/si4760/Rds.o
                                                  emodules/fm/fm_module/si4760/Rds_sub.o
curr_ctx                                          emodules/fm/fm_module/si4760/Rds.o
                                                  emodules/fm/fm_module/si4760/Tuner.o
                                                  emodules/fm/fm_module/si4760/Tu_Si_476X.o
                                                  emodules/fm/fm_module/si4760/Rds_sub.o
current_dab_fm_linking_num                        emodules/fm/fm_module/si4760/Tuner.o
                                                  emodules/fm/fm_module/si4760/Rds.o
current_link_info                                 emodules/fm/fm_module/si4760/Tuner.o
dab_fm_link_PI_table_printf                       emodules/fm/fm_module/si4760/Rds.o
dab_fm_link_backup_freq                           emodules/fm/fm_module/si4760/Tuner.o
dab_fm_pi_match_sucess                            emodules/fm/fm_module/si4760/Tuner.o
                                                  emodules/fm/fm_module/si4760/Rds.o
dab_fm_scan_search_pi                             emodules/fm/fm_module/si4760/Tuner.o
                                                  emodules/fm/fm_module/si4760/Tu_Si_476X.o
                                                  emodules/fm/fm_module/si4760/Rds.o
dab_fm_scan_search_pi_match_flg                   emodules/fm/fm_module/si4760/Tuner.o
                                                  emodules/fm/fm_module/si4760/Tu_Si_476X.o
                                                  emodules/fm/fm_module/si4760/Rds.o
dab_fm_start_link_sucess                          emodules/fm/fm_module/si4760/Tuner.o
                                                  emodules/fm/fm_module/si4760/Tu_Si_476X.o
dab_rds_linkings                                  emodules/fm/fm_module/si4760/Tuner.o
                                                  emodules/fm/fm_module/si4760/Rds.o
dup                                               ./elibrary/bin//libsyscall.a(syscall_fops.o)
dup2                                              ./elibrary/bin//libsyscall.a(syscall_fops.o)
eLIBs_DirEnt2Attr                                 ./elibrary/bin//libminic.a(elibs_stdio.o)
eLIBs_DirEnt2Name                                 ./elibrary/bin//libminic.a(elibs_stdio.o)
eLIBs_DirEnt2Size                                 ./elibrary/bin//libminic.a(elibs_stdio.o)
eLIBs_DirEnt2Time                                 ./elibrary/bin//libminic.a(elibs_stdio.o)
eLIBs_GetDate                                     ./elibrary/bin//libminic.a(elibs_stdio.o)
eLIBs_GetDevName                                  ./elibrary/bin//libminic.a(elibs_stdio.o)
eLIBs_GetDevParts                                 ./elibrary/bin//libminic.a(elibs_stdio.o)
eLIBs_GetDirATime                                 ./elibrary/bin//libminic.a(elibs_stdio.o)
eLIBs_GetDirCTime                                 ./elibrary/bin//libminic.a(elibs_stdio.o)
eLIBs_GetDirMTime                                 ./elibrary/bin//libminic.a(elibs_stdio.o)
eLIBs_GetDirSize                                  ./elibrary/bin//libminic.a(elibs_stdio.o)
eLIBs_GetDiskArray                                ./elibrary/bin//libminic.a(elibs_stdio.o)
eLIBs_GetFSAttribute                              ./elibrary/bin//libminic.a(elibs_stdio.o)
eLIBs_GetFSCharset                                ./elibrary/bin//libminic.a(elibs_stdio.o)
eLIBs_GetFSName                                   ./elibrary/bin//libminic.a(elibs_stdio.o)
eLIBs_GetFSNameLen                                ./elibrary/bin//libminic.a(elibs_stdio.o)
eLIBs_GetFileATime                                ./elibrary/bin//libminic.a(elibs_stdio.o)
eLIBs_GetFileAttributes                           ./elibrary/bin//libminic.a(elibs_stdio.o)
eLIBs_GetFileCTime                                ./elibrary/bin//libminic.a(elibs_stdio.o)
eLIBs_GetFileMTime                                ./elibrary/bin//libminic.a(elibs_stdio.o)
eLIBs_GetFileSize                                 ./elibrary/bin//libminic.a(elibs_stdio.o)
eLIBs_GetNumFiles                                 ./elibrary/bin//libminic.a(elibs_stdio.o)
eLIBs_GetNumSubItems                              ./elibrary/bin//libminic.a(elibs_stdio.o)
eLIBs_GetPTName                                   ./elibrary/bin//libminic.a(elibs_stdio.o)
eLIBs_GetTime                                     ./elibrary/bin//libminic.a(elibs_stdio.o)
eLIBs_GetVolClustSize                             ./elibrary/bin//libminic.a(elibs_stdio.o)
eLIBs_GetVolFSpace                                ./elibrary/bin//libminic.a(elibs_stdio.o)
eLIBs_GetVolName                                  ./elibrary/bin//libminic.a(elibs_stdio.o)
eLIBs_GetVolNameCharset                           ./elibrary/bin//libminic.a(elibs_stdio.o)
eLIBs_GetVolNameLen                               ./elibrary/bin//libminic.a(elibs_stdio.o)
eLIBs_GetVolTSpace                                ./elibrary/bin//libminic.a(elibs_stdio.o)
eLIBs_IsPartFormated                              ./elibrary/bin//libminic.a(elibs_stdio.o)
eLIBs_SetFileAttributes                           ./elibrary/bin//libminic.a(elibs_stdio.o)
eLIBs_closedir                                    ./elibrary/bin//libminic.a(elibs_stdio.o)
eLIBs_disklock                                    ./elibrary/bin//libminic.a(elibs_stdio.o)
eLIBs_diskunlock                                  ./elibrary/bin//libminic.a(elibs_stdio.o)
eLIBs_fclose                                      ./elibrary/bin//libminic.a(elibs_stdio.o)
eLIBs_fd2file                                     ./elibrary/bin//libminic.a(elibs_stdio.o)
eLIBs_feof                                        ./elibrary/bin//libminic.a(elibs_stdio.o)
eLIBs_ferrclr                                     ./elibrary/bin//libminic.a(elibs_stdio.o)
eLIBs_ferror                                      ./elibrary/bin//libminic.a(elibs_stdio.o)
eLIBs_fgetc                                       ./elibrary/bin//libminic.a(elibs_stdio.o)
eLIBs_fgets                                       ./elibrary/bin//libminic.a(elibs_stdio.o)
eLIBs_file2fd                                     ./elibrary/bin//libminic.a(elibs_stdio.o)
eLIBs_fioctrl                                     ./elibrary/bin//libminic.a(elibs_stdio.o)
eLIBs_fllseek                                     ./elibrary/bin//libminic.a(elibs_stdio.o)
eLIBs_flltell                                     ./elibrary/bin//libminic.a(elibs_stdio.o)
eLIBs_fopen                                       ./elibrary/bin//libminic.a(elibs_stdio.o)
eLIBs_format                                      ./elibrary/bin//libminic.a(elibs_stdio.o)
eLIBs_fputc                                       ./elibrary/bin//libminic.a(elibs_stdio.o)
eLIBs_fputs                                       ./elibrary/bin//libminic.a(elibs_stdio.o)
eLIBs_fread                                       ./elibrary/bin//libminic.a(elibs_stdio.o)
eLIBs_fseek                                       ./elibrary/bin//libminic.a(elibs_stdio.o)
eLIBs_fstat                                       ./elibrary/bin//libminic.a(elibs_stdio.o)
eLIBs_fsync                                       ./elibrary/bin//libminic.a(elibs_stdio.o)
eLIBs_ftell                                       ./elibrary/bin//libminic.a(elibs_stdio.o)
eLIBs_ftruncate                                   ./elibrary/bin//libminic.a(elibs_stdio.o)
eLIBs_fwrite                                      ./elibrary/bin//libminic.a(elibs_stdio.o)
eLIBs_memclr                                      ./elibrary/bin//libminic.a(elibs_string.o)
eLIBs_memcmp                                      ./elibrary/bin//libminic.a(elibs_string.o)
eLIBs_memcpy                                      ./elibrary/bin//libminic.a(elibs_string.o)
                                                  ./elibrary/bin//libminic.a(elibs_stdio.o)
eLIBs_memmove                                     ./elibrary/bin//libminic.a(elibs_string.o)
eLIBs_memset                                      ./elibrary/bin//libminic.a(elibs_string.o)
                                                  emodules/fm/fm_module/si4760/Tuner.o
eLIBs_mkdir                                       ./elibrary/bin//libminic.a(elibs_stdio.o)
eLIBs_open                                        ./elibrary/bin//libminic.a(elibs_stdio.o)
eLIBs_opendir                                     ./elibrary/bin//libminic.a(elibs_stdio.o)
eLIBs_printf                                      ./elibrary/bin//libminic.a(elibs_stdio.o)
                                                  emodules/fm/fm_module/si4760/Tuner.o
eLIBs_readdir                                     ./elibrary/bin//libminic.a(elibs_stdio.o)
eLIBs_remove                                      ./elibrary/bin//libminic.a(elibs_stdio.o)
eLIBs_rename                                      ./elibrary/bin//libminic.a(elibs_stdio.o)
eLIBs_rewinddir                                   ./elibrary/bin//libminic.a(elibs_stdio.o)
eLIBs_rmdir                                       ./elibrary/bin//libminic.a(elibs_stdio.o)
eLIBs_scnprintf                                   ./elibrary/bin//libminic.a(elibs_sprintf.o)
eLIBs_setfs                                       ./elibrary/bin//libminic.a(elibs_stdio.o)
eLIBs_snprintf                                    ./elibrary/bin//libminic.a(elibs_sprintf.o)
eLIBs_sprintf                                     ./elibrary/bin//libminic.a(elibs_sprintf.o)
eLIBs_statfs                                      ./elibrary/bin//libminic.a(elibs_stdio.o)
eLIBs_strcat                                      ./elibrary/bin//libminic.a(elibs_string.o)
                                                  ./elibrary/bin//libminic.a(elibs_stdio.o)
eLIBs_strchr                                      ./elibrary/bin//libminic.a(elibs_string.o)
eLIBs_strchrlast                                  ./elibrary/bin//libminic.a(elibs_string.o)
eLIBs_strcmp                                      ./elibrary/bin//libminic.a(elibs_string.o)
                                                  ./elibrary/bin//libminic.a(elibs_stdio.o)
eLIBs_strcpy                                      ./elibrary/bin//libminic.a(elibs_string.o)
                                                  ./elibrary/bin//libminic.a(elibs_stdio.o)
eLIBs_stricmp                                     ./elibrary/bin//libminic.a(elibs_string.o)
                                                  ./elibrary/bin//libminic.a(elibs_stdio.o)
eLIBs_strlen                                      ./elibrary/bin//libminic.a(elibs_string.o)
                                                  ./elibrary/bin//libminic.a(elibs_stdio.o)
eLIBs_strncat                                     ./elibrary/bin//libminic.a(elibs_string.o)
eLIBs_strnchr                                     ./elibrary/bin//libminic.a(elibs_string.o)
eLIBs_strncmp                                     ./elibrary/bin//libminic.a(elibs_string.o)
eLIBs_strncpy                                     ./elibrary/bin//libminic.a(elibs_string.o)
                                                  ./elibrary/bin//libminic.a(elibs_stdio.o)
eLIBs_strnicmp                                    ./elibrary/bin//libminic.a(elibs_string.o)
eLIBs_strnlen                                     ./elibrary/bin//libminic.a(elibs_string.o)
eLIBs_strstr                                      ./elibrary/bin//libminic.a(elibs_string.o)
eLIBs_vprintf                                     ./elibrary/bin//libminic.a(elibs_stdio.o)
eLIBs_vscnprintf                                  ./elibrary/bin//libminic.a(elibs_sprintf.o)
                                                  ./elibrary/bin//libminic.a(elibs_stdio.o)
eLIBs_vsnprintf                                   ./elibrary/bin//libminic.a(elibs_sprintf.o)
eLIBs_vsprintf                                    ./elibrary/bin//libminic.a(elibs_sprintf.o)
esCFG_Exit_Ex                                     ./elibrary/bin//libsyscall.a(syscall.o)
esCFG_GetGPIOSecData                              ./elibrary/bin//libsyscall.a(syscall.o)
esCFG_GetGPIOSecData_Ex                           ./elibrary/bin//libsyscall.a(syscall.o)
esCFG_GetGPIOSecKeyCount                          ./elibrary/bin//libsyscall.a(syscall.o)
esCFG_GetGPIOSecKeyCount_Ex                       ./elibrary/bin//libsyscall.a(syscall.o)
esCFG_GetKeyValue                                 ./elibrary/bin//libsyscall.a(syscall.o)
esCFG_GetKeyValue_Ex                              ./elibrary/bin//libsyscall.a(syscall.o)
esCFG_GetSecCount                                 ./elibrary/bin//libsyscall.a(syscall.o)
esCFG_GetSecCount_Ex                              ./elibrary/bin//libsyscall.a(syscall.o)
esCFG_GetSecKeyCount                              ./elibrary/bin//libsyscall.a(syscall.o)
esCFG_GetSecKeyCount_Ex                           ./elibrary/bin//libsyscall.a(syscall.o)
esCFG_Init_Ex                                     ./elibrary/bin//libsyscall.a(syscall.o)
esCHS_Char2Uni                                    ./elibrary/bin//libsyscall.a(syscall.o)
esCHS_GetChLowerTbl                               ./elibrary/bin//libsyscall.a(syscall.o)
esCHS_GetChUpperTbl                               ./elibrary/bin//libsyscall.a(syscall.o)
esCHS_Uni2Char                                    ./elibrary/bin//libsyscall.a(syscall.o)
esCLK_CloseMclk                                   ./elibrary/bin//libsyscall.a(syscall.o)
esCLK_GetMclkDiv                                  ./elibrary/bin//libsyscall.a(syscall.o)
esCLK_GetMclkSrc                                  ./elibrary/bin//libsyscall.a(syscall.o)
esCLK_GetRound_Rate                               ./elibrary/bin//libsyscall.a(syscall.o)
esCLK_GetSrcFreq                                  ./elibrary/bin//libsyscall.a(syscall.o)
esCLK_MclkAssert                                  ./elibrary/bin//libsyscall.a(syscall.o)
esCLK_MclkDeassert                                ./elibrary/bin//libsyscall.a(syscall.o)
esCLK_MclkGetRstStatus                            ./elibrary/bin//libsyscall.a(syscall.o)
esCLK_MclkOnOff                                   ./elibrary/bin//libsyscall.a(syscall.o)
esCLK_MclkRegCb                                   ./elibrary/bin//libsyscall.a(syscall.o)
esCLK_MclkReset                                   ./elibrary/bin//libsyscall.a(syscall.o)
esCLK_MclkUnregCb                                 ./elibrary/bin//libsyscall.a(syscall.o)
esCLK_ModInfo                                     ./elibrary/bin//libsyscall.a(syscall.o)
esCLK_OpenMclk                                    ./elibrary/bin//libsyscall.a(syscall.o)
esCLK_SetMclkDiv                                  ./elibrary/bin//libsyscall.a(syscall.o)
esCLK_SetMclkSrc                                  ./elibrary/bin//libsyscall.a(syscall.o)
esCLK_SetSrcFreq                                  ./elibrary/bin//libsyscall.a(syscall.o)
esCLK_SysInfo                                     ./elibrary/bin//libsyscall.a(syscall.o)
esDEV_Close                                       ./elibrary/bin//libsyscall.a(syscall.o)
esDEV_DevReg                                      ./elibrary/bin//libsyscall.a(syscall.o)
esDEV_DevUnreg                                    ./elibrary/bin//libsyscall.a(syscall.o)
esDEV_Insmod                                      ./elibrary/bin//libsyscall.a(syscall.o)
esDEV_Ioctl                                       ./elibrary/bin//libsyscall.a(syscall.o)
esDEV_Lock                                        ./elibrary/bin//libsyscall.a(syscall.o)
esDEV_Open                                        ./elibrary/bin//libsyscall.a(syscall.o)
esDEV_Plugin                                      ./elibrary/bin//libsyscall.a(syscall.o)
esDEV_Plugout                                     ./elibrary/bin//libsyscall.a(syscall.o)
esDEV_Read                                        ./elibrary/bin//libsyscall.a(syscall.o)
esDEV_Unimod                                      ./elibrary/bin//libsyscall.a(syscall.o)
esDEV_Unlock                                      ./elibrary/bin//libsyscall.a(syscall.o)
esDEV_Write                                       ./elibrary/bin//libsyscall.a(syscall.o)
esDMA_ChangeMode                                  ./elibrary/bin//libsyscall.a(syscall.o)
esDMA_DisableINT                                  ./elibrary/bin//libsyscall.a(syscall.o)
esDMA_EnableINT                                   ./elibrary/bin//libsyscall.a(syscall.o)
esDMA_Information                                 ./elibrary/bin//libsyscall.a(syscall.o)
esDMA_QueryDst                                    ./elibrary/bin//libsyscall.a(syscall.o)
esDMA_QueryRestCount                              ./elibrary/bin//libsyscall.a(syscall.o)
esDMA_QuerySrc                                    ./elibrary/bin//libsyscall.a(syscall.o)
esDMA_QueryStat                                   ./elibrary/bin//libsyscall.a(syscall.o)
esDMA_RegDmaHdler                                 ./elibrary/bin//libsyscall.a(syscall.o)
esDMA_Release                                     ./elibrary/bin//libsyscall.a(syscall.o)
esDMA_Request                                     ./elibrary/bin//libsyscall.a(syscall.o)
esDMA_Restart                                     ./elibrary/bin//libsyscall.a(syscall.o)
esDMA_Setting                                     ./elibrary/bin//libsyscall.a(syscall.o)
esDMA_Start                                       ./elibrary/bin//libsyscall.a(syscall.o)
esDMA_Stop                                        ./elibrary/bin//libsyscall.a(syscall.o)
esDMA_UnregDmaHdler                               ./elibrary/bin//libsyscall.a(syscall.o)
esEXEC_PCreate                                    ./elibrary/bin//libsyscall.a(syscall.o)
esEXEC_PDel                                       ./elibrary/bin//libsyscall.a(syscall.o)
esEXEC_PDelReq                                    ./elibrary/bin//libsyscall.a(syscall.o)
esEXEC_Run                                        ./elibrary/bin//libsyscall.a(syscall.o)
esFSYS_clearpartupdateflag                        ./elibrary/bin//libsyscall.a(syscall.o)
esFSYS_closedir                                   ./elibrary/bin//libsyscall.a(syscall.o)
                                                  ./elibrary/bin//libminic.a(elibs_stdio.o)
esFSYS_fclose                                     ./elibrary/bin//libsyscall.a(syscall.o)
                                                  ./elibrary/bin//libminic.a(elibs_stdio.o)
esFSYS_fd2file                                    ./elibrary/bin//libsyscall.a(syscall.o)
                                                  ./elibrary/bin//libminic.a(elibs_stdio.o)
esFSYS_ferrclr                                    ./elibrary/bin//libsyscall.a(syscall.o)
                                                  ./elibrary/bin//libminic.a(elibs_stdio.o)
esFSYS_ferror                                     ./elibrary/bin//libsyscall.a(syscall.o)
                                                  ./elibrary/bin//libminic.a(elibs_stdio.o)
esFSYS_file2fd                                    ./elibrary/bin//libsyscall.a(syscall.o)
                                                  ./elibrary/bin//libminic.a(elibs_stdio.o)
esFSYS_fioctrl                                    ./elibrary/bin//libsyscall.a(syscall.o)
                                                  ./elibrary/bin//libminic.a(elibs_stdio.o)
esFSYS_fopen                                      ./elibrary/bin//libsyscall.a(syscall.o)
                                                  ./elibrary/bin//libminic.a(elibs_stdio.o)
esFSYS_format                                     ./elibrary/bin//libsyscall.a(syscall.o)
                                                  ./elibrary/bin//libminic.a(elibs_stdio.o)
esFSYS_fread                                      ./elibrary/bin//libsyscall.a(syscall.o)
                                                  ./elibrary/bin//libminic.a(elibs_stdio.o)
esFSYS_fsdbg                                      ./elibrary/bin//libsyscall.a(syscall.o)
esFSYS_fseek                                      ./elibrary/bin//libsyscall.a(syscall.o)
                                                  ./elibrary/bin//libminic.a(elibs_stdio.o)
esFSYS_fseekex                                    ./elibrary/bin//libsyscall.a(syscall.o)
                                                  ./elibrary/bin//libminic.a(elibs_stdio.o)
esFSYS_fsreg                                      ./elibrary/bin//libsyscall.a(syscall.o)
esFSYS_fstat                                      ./elibrary/bin//libsyscall.a(syscall.o)
                                                  ./elibrary/bin//libminic.a(elibs_stdio.o)
esFSYS_fsunreg                                    ./elibrary/bin//libsyscall.a(syscall.o)
esFSYS_fsync                                      ./elibrary/bin//libsyscall.a(syscall.o)
                                                  ./elibrary/bin//libminic.a(elibs_stdio.o)
esFSYS_ftell                                      ./elibrary/bin//libsyscall.a(syscall.o)
                                                  ./elibrary/bin//libminic.a(elibs_stdio.o)
esFSYS_ftellex                                    ./elibrary/bin//libsyscall.a(syscall.o)
                                                  ./elibrary/bin//libminic.a(elibs_stdio.o)
esFSYS_ftruncate                                  ./elibrary/bin//libsyscall.a(syscall.o)
                                                  ./elibrary/bin//libminic.a(elibs_stdio.o)
esFSYS_fwrite                                     ./elibrary/bin//libsyscall.a(syscall.o)
                                                  ./elibrary/bin//libminic.a(elibs_stdio.o)
esFSYS_getfscharset                               ./elibrary/bin//libsyscall.a(syscall.o)
                                                  ./elibrary/bin//libminic.a(elibs_stdio.o)
esFSYS_mkdir                                      ./elibrary/bin//libsyscall.a(syscall.o)
                                                  ./elibrary/bin//libminic.a(elibs_stdio.o)
esFSYS_mntfs                                      ./elibrary/bin//libsyscall.a(syscall.o)
esFSYS_mntparts                                   ./elibrary/bin//libsyscall.a(syscall.o)
esFSYS_open                                       ./elibrary/bin//libsyscall.a(syscall.o)
                                                  ./elibrary/bin//libminic.a(elibs_stdio.o)
esFSYS_opendir                                    ./elibrary/bin//libsyscall.a(syscall.o)
                                                  ./elibrary/bin//libminic.a(elibs_stdio.o)
esFSYS_partfslck                                  ./elibrary/bin//libsyscall.a(syscall.o)
                                                  ./elibrary/bin//libminic.a(elibs_stdio.o)
esFSYS_partfsunlck                                ./elibrary/bin//libsyscall.a(syscall.o)
                                                  ./elibrary/bin//libminic.a(elibs_stdio.o)
esFSYS_pclose                                     ./elibrary/bin//libsyscall.a(syscall.o)
esFSYS_pdreg                                      ./elibrary/bin//libsyscall.a(syscall.o)
esFSYS_pdunreg                                    ./elibrary/bin//libsyscall.a(syscall.o)
esFSYS_perrclr                                    ./elibrary/bin//libsyscall.a(syscall.o)
esFSYS_perror                                     ./elibrary/bin//libsyscall.a(syscall.o)
esFSYS_pioctrl                                    ./elibrary/bin//libsyscall.a(syscall.o)
esFSYS_popen                                      ./elibrary/bin//libsyscall.a(syscall.o)
esFSYS_pread                                      ./elibrary/bin//libsyscall.a(syscall.o)
esFSYS_premove                                    ./elibrary/bin//libsyscall.a(syscall.o)
esFSYS_prename                                    ./elibrary/bin//libsyscall.a(syscall.o)
esFSYS_pwrite                                     ./elibrary/bin//libsyscall.a(syscall.o)
esFSYS_querypartupdateflag                        ./elibrary/bin//libsyscall.a(syscall.o)
esFSYS_readdir                                    ./elibrary/bin//libsyscall.a(syscall.o)
                                                  ./elibrary/bin//libminic.a(elibs_stdio.o)
esFSYS_remove                                     ./elibrary/bin//libsyscall.a(syscall.o)
                                                  ./elibrary/bin//libminic.a(elibs_stdio.o)
esFSYS_rename                                     ./elibrary/bin//libsyscall.a(syscall.o)
                                                  ./elibrary/bin//libminic.a(elibs_stdio.o)
esFSYS_rewinddir                                  ./elibrary/bin//libsyscall.a(syscall.o)
                                                  ./elibrary/bin//libminic.a(elibs_stdio.o)
esFSYS_rmdir                                      ./elibrary/bin//libsyscall.a(syscall.o)
                                                  ./elibrary/bin//libminic.a(elibs_stdio.o)
esFSYS_setfs                                      ./elibrary/bin//libsyscall.a(syscall.o)
                                                  ./elibrary/bin//libminic.a(elibs_stdio.o)
esFSYS_statfs                                     ./elibrary/bin//libsyscall.a(syscall.o)
                                                  ./elibrary/bin//libminic.a(elibs_stdio.o)
esFSYS_statpt                                     ./elibrary/bin//libsyscall.a(syscall.o)
                                                  ./elibrary/bin//libminic.a(elibs_stdio.o)
esFSYS_umntfs                                     ./elibrary/bin//libsyscall.a(syscall.o)
esFSYS_umntparts                                  ./elibrary/bin//libsyscall.a(syscall.o)
esHID_SendMsg                                     ./elibrary/bin//libsyscall.a(syscall.o)
esHID_hiddevreg                                   ./elibrary/bin//libsyscall.a(syscall.o)
esHID_hiddevunreg                                 ./elibrary/bin//libsyscall.a(syscall.o)
esINPUT_GetLdevID                                 ./elibrary/bin//libsyscall.a(syscall.o)
esINPUT_LdevCtl                                   ./elibrary/bin//libsyscall.a(syscall.o)
esINPUT_LdevFeedback                              ./elibrary/bin//libsyscall.a(syscall.o)
esINPUT_LdevGrab                                  ./elibrary/bin//libsyscall.a(syscall.o)
esINPUT_LdevRelease                               ./elibrary/bin//libsyscall.a(syscall.o)
esINPUT_RegDev                                    ./elibrary/bin//libsyscall.a(syscall.o)
esINPUT_SendEvent                                 ./elibrary/bin//libsyscall.a(syscall.o)
esINPUT_UnregDev                                  ./elibrary/bin//libsyscall.a(syscall.o)
esINT_DisableINT                                  ./elibrary/bin//libsyscall.a(syscall.o)
esINT_EnableINT                                   ./elibrary/bin//libsyscall.a(syscall.o)
esINT_InsFIR                                      ./elibrary/bin//libsyscall.a(syscall.o)
esINT_InsISR                                      ./elibrary/bin//libsyscall.a(syscall.o)
esINT_SetIRQPrio                                  ./elibrary/bin//libsyscall.a(syscall.o)
esINT_UniFIR                                      ./elibrary/bin//libsyscall.a(syscall.o)
esINT_UniISR                                      ./elibrary/bin//libsyscall.a(syscall.o)
esKRNL_CallBack                                   ./elibrary/bin//libsyscall.a(syscall.o)
esKRNL_DumpStack                                  ./elibrary/bin//libsyscall.a(syscall.o)
esKRNL_FlagAccept                                 ./elibrary/bin//libsyscall.a(syscall.o)
esKRNL_FlagCreate                                 ./elibrary/bin//libsyscall.a(syscall.o)
esKRNL_FlagDel                                    ./elibrary/bin//libsyscall.a(syscall.o)
esKRNL_FlagNameGet                                ./elibrary/bin//libsyscall.a(syscall.o)
esKRNL_FlagNameSet                                ./elibrary/bin//libsyscall.a(syscall.o)
esKRNL_FlagPend                                   ./elibrary/bin//libsyscall.a(syscall.o)
esKRNL_FlagPendGetFlagsRdy                        ./elibrary/bin//libsyscall.a(syscall.o)
esKRNL_FlagPost                                   ./elibrary/bin//libsyscall.a(syscall.o)
esKRNL_FlagQuery                                  ./elibrary/bin//libsyscall.a(syscall.o)
esKRNL_GetCallBack                                ./elibrary/bin//libsyscall.a(syscall.o)
esKRNL_GetTIDCur                                  ./elibrary/bin//libsyscall.a(syscall.o)
esKRNL_InterruptDisable                           ./elibrary/bin//libsyscall.a(syscall.o)
esKRNL_InterruptEnable                            ./elibrary/bin//libsyscall.a(syscall.o)
esKRNL_Ioctrl                                     ./elibrary/bin//libsyscall.a(syscall.o)
esKRNL_MboxAccept                                 ./elibrary/bin//libsyscall.a(syscall.o)
esKRNL_MboxCreate                                 ./elibrary/bin//libsyscall.a(syscall.o)
esKRNL_MboxDel                                    ./elibrary/bin//libsyscall.a(syscall.o)
esKRNL_MboxPend                                   ./elibrary/bin//libsyscall.a(syscall.o)
esKRNL_MboxPost                                   ./elibrary/bin//libsyscall.a(syscall.o)
esKRNL_MboxPostOpt                                ./elibrary/bin//libsyscall.a(syscall.o)
esKRNL_MboxQuery                                  ./elibrary/bin//libsyscall.a(syscall.o)
esKRNL_MemLeakChk                                 ./elibrary/bin//libsyscall.a(syscall.o)
esKRNL_MutexCreate                                ./elibrary/bin//libsyscall.a(syscall.o)
esKRNL_MutexDel                                   ./elibrary/bin//libsyscall.a(syscall.o)
esKRNL_MutexPend                                  ./elibrary/bin//libsyscall.a(syscall.o)
esKRNL_MutexPost                                  ./elibrary/bin//libsyscall.a(syscall.o)
esKRNL_QAccept                                    ./elibrary/bin//libsyscall.a(syscall.o)
esKRNL_QCreate                                    ./elibrary/bin//libsyscall.a(syscall.o)
esKRNL_QDel                                       ./elibrary/bin//libsyscall.a(syscall.o)
esKRNL_QFlush                                     ./elibrary/bin//libsyscall.a(syscall.o)
esKRNL_QPend                                      ./elibrary/bin//libsyscall.a(syscall.o)
esKRNL_QPost                                      ./elibrary/bin//libsyscall.a(syscall.o)
esKRNL_QPostFront                                 ./elibrary/bin//libsyscall.a(syscall.o)
esKRNL_QPostOpt                                   ./elibrary/bin//libsyscall.a(syscall.o)
esKRNL_QQuery                                     ./elibrary/bin//libsyscall.a(syscall.o)
esKRNL_SchedLock                                  ./elibrary/bin//libsyscall.a(syscall.o)
esKRNL_SchedUnlock                                ./elibrary/bin//libsyscall.a(syscall.o)
esKRNL_SemAccept                                  ./elibrary/bin//libsyscall.a(syscall.o)
esKRNL_SemCreate                                  ./elibrary/bin//libsyscall.a(syscall.o)
                                                  emodules/fm/drv_fm.o
esKRNL_SemDel                                     ./elibrary/bin//libsyscall.a(syscall.o)
                                                  emodules/fm/drv_fm.o
esKRNL_SemPend                                    ./elibrary/bin//libsyscall.a(syscall.o)
                                                  emodules/fm/dev_fm.o
esKRNL_SemPost                                    ./elibrary/bin//libsyscall.a(syscall.o)
                                                  emodules/fm/dev_fm.o
esKRNL_SemQuery                                   ./elibrary/bin//libsyscall.a(syscall.o)
esKRNL_SemSet                                     ./elibrary/bin//libsyscall.a(syscall.o)
esKRNL_SktAccept                                  ./elibrary/bin//libsyscall.a(syscall.o)
esKRNL_SktCreate                                  ./elibrary/bin//libsyscall.a(syscall.o)
esKRNL_SktDel                                     ./elibrary/bin//libsyscall.a(syscall.o)
esKRNL_SktPend                                    ./elibrary/bin//libsyscall.a(syscall.o)
esKRNL_SktPost                                    ./elibrary/bin//libsyscall.a(syscall.o)
esKRNL_TCreate                                    ./elibrary/bin//libsyscall.a(syscall.o)
                                                  emodules/fm/dev_fm.o
                                                  emodules/fm/fm_module/si4760/Tuner.o
esKRNL_TDel                                       ./elibrary/bin//libsyscall.a(syscall.o)
                                                  emodules/fm/dev_fm.o
                                                  emodules/fm/fm_module/si4760/Tuner.o
esKRNL_TDelReq                                    ./elibrary/bin//libsyscall.a(syscall.o)
                                                  emodules/fm/dev_fm.o
                                                  emodules/fm/fm_module/si4760/Tuner.o
esKRNL_TaskNameSet                                ./elibrary/bin//libsyscall.a(syscall.o)
esKRNL_TaskPrefEn                                 ./elibrary/bin//libsyscall.a(syscall.o)
esKRNL_TaskQuery                                  ./elibrary/bin//libsyscall.a(syscall.o)
esKRNL_TaskResume                                 ./elibrary/bin//libsyscall.a(syscall.o)
esKRNL_TaskSuspend                                ./elibrary/bin//libsyscall.a(syscall.o)
esKRNL_Time                                       ./elibrary/bin//libsyscall.a(syscall.o)
esKRNL_TimeDly                                    ./elibrary/bin//libsyscall.a(syscall.o)
                                                  emodules/fm/dev_fm.o
                                                  emodules/fm/fm_module/si4760/Tuner.o
                                                  emodules/fm/fm_module/si4760/Tu_Si_476X.o
esKRNL_TimeDlyResume                              ./elibrary/bin//libsyscall.a(syscall.o)
                                                  emodules/fm/dev_fm.o
esKRNL_TimeGet                                    ./elibrary/bin//libsyscall.a(syscall.o)
                                                  emodules/fm/fm_module/si4760/Tuner.o
esKRNL_TimeSet                                    ./elibrary/bin//libsyscall.a(syscall.o)
esKRNL_TmrCreate                                  ./elibrary/bin//libsyscall.a(syscall.o)
esKRNL_TmrDel                                     ./elibrary/bin//libsyscall.a(syscall.o)
esKRNL_TmrError                                   ./elibrary/bin//libsyscall.a(syscall.o)
esKRNL_TmrRemainGet                               ./elibrary/bin//libsyscall.a(syscall.o)
esKRNL_TmrStart                                   ./elibrary/bin//libsyscall.a(syscall.o)
esKRNL_TmrStateGet                                ./elibrary/bin//libsyscall.a(syscall.o)
esKRNL_TmrStop                                    ./elibrary/bin//libsyscall.a(syscall.o)
esKRNL_Version                                    ./elibrary/bin//libsyscall.a(syscall.o)
esKSRV_ClearWatchDog                              ./elibrary/bin//libsyscall.a(syscall.o)
esKSRV_DisableWatchDog                            ./elibrary/bin//libsyscall.a(syscall.o)
esKSRV_DisableWatchDogSrv                         ./elibrary/bin//libsyscall.a(syscall.o)
esKSRV_EnableWatchDog                             ./elibrary/bin//libsyscall.a(syscall.o)
esKSRV_EnableWatchDogSrv                          ./elibrary/bin//libsyscall.a(syscall.o)
esKSRV_GetAddPara                                 ./elibrary/bin//libsyscall.a(syscall.o)
esKSRV_GetDramCfgPara                             ./elibrary/bin//libsyscall.a(syscall.o)
esKSRV_GetHighMsg                                 ./elibrary/bin//libsyscall.a(syscall.o)
esKSRV_GetLowMsg                                  ./elibrary/bin//libsyscall.a(syscall.o)
esKSRV_GetMsg                                     ./elibrary/bin//libsyscall.a(syscall.o)
esKSRV_GetPara                                    ./elibrary/bin//libsyscall.a(syscall.o)
esKSRV_GetSocID                                   ./elibrary/bin//libsyscall.a(syscall.o)
esKSRV_GetTargetPara                              ./elibrary/bin//libsyscall.a(syscall.o)
esKSRV_GetVersion                                 ./elibrary/bin//libsyscall.a(syscall.o)
esKSRV_Get_Display_Hld                            ./elibrary/bin//libsyscall.a(syscall.o)
esKSRV_Get_Mixture_Hld                            ./elibrary/bin//libsyscall.a(syscall.o)
esKSRV_Get_Raw_Decoder_Hld                        ./elibrary/bin//libsyscall.a(syscall.o)
esKSRV_PowerOff                                   ./elibrary/bin//libsyscall.a(syscall.o)
esKSRV_Random                                     ./elibrary/bin//libsyscall.a(syscall.o)
esKSRV_Reset                                      ./elibrary/bin//libsyscall.a(syscall.o)
esKSRV_Save_Display_Hld                           ./elibrary/bin//libsyscall.a(syscall.o)
esKSRV_Save_Mixture_Hld                           ./elibrary/bin//libsyscall.a(syscall.o)
esKSRV_Save_Raw_Decoder_Hld                       ./elibrary/bin//libsyscall.a(syscall.o)
esKSRV_SendMsg                                    ./elibrary/bin//libsyscall.a(syscall.o)
                                                  emodules/fm/fm_module/si4760/Tu_Si_476X.o
                                                  emodules/fm/fm_module/si4760/Rds.o
esKSRV_SendMsgEx                                  ./elibrary/bin//libsyscall.a(syscall.o)
esKSRV_SysInfo                                    ./elibrary/bin//libsyscall.a(syscall.o)
esKSRV_memcpy                                     ./elibrary/bin//libsyscall.a(syscall.o)
esKSRV_memset                                     ./elibrary/bin//libsyscall.a(syscall.o)
esMEMS_Balloc                                     ./elibrary/bin//libsyscall.a(syscall.o)
esMEMS_Bfree                                      ./elibrary/bin//libsyscall.a(syscall.o)
esMEMS_Calloc                                     ./elibrary/bin//libsyscall.a(syscall.o)
esMEMS_CleanDCache                                ./elibrary/bin//libsyscall.a(syscall.o)
esMEMS_CleanDCacheRegion                          ./elibrary/bin//libsyscall.a(syscall.o)
esMEMS_CleanFlushCache                            ./elibrary/bin//libsyscall.a(syscall.o)
esMEMS_CleanFlushCacheRegion                      ./elibrary/bin//libsyscall.a(syscall.o)
esMEMS_CleanFlushDCache                           ./elibrary/bin//libsyscall.a(syscall.o)
esMEMS_CleanFlushDCacheRegion                     ./elibrary/bin//libsyscall.a(syscall.o)
esMEMS_CleanInvalidateCacheAll                    ./elibrary/bin//libsyscall.a(syscall.o)
esMEMS_FlushCache                                 ./elibrary/bin//libsyscall.a(syscall.o)
esMEMS_FlushCacheRegion                           ./elibrary/bin//libsyscall.a(syscall.o)
esMEMS_FlushDCache                                ./elibrary/bin//libsyscall.a(syscall.o)
esMEMS_FlushDCacheRegion                          ./elibrary/bin//libsyscall.a(syscall.o)
esMEMS_FlushICache                                ./elibrary/bin//libsyscall.a(syscall.o)
esMEMS_FlushICacheRegion                          ./elibrary/bin//libsyscall.a(syscall.o)
esMEMS_FreeMemSize                                ./elibrary/bin//libsyscall.a(syscall.o)
esMEMS_GetIoVAByPA                                ./elibrary/bin//libsyscall.a(syscall.o)
esMEMS_HeapCreate                                 ./elibrary/bin//libsyscall.a(syscall.o)
esMEMS_HeapDel                                    ./elibrary/bin//libsyscall.a(syscall.o)
esMEMS_Info                                       ./elibrary/bin//libsyscall.a(syscall.o)
esMEMS_Malloc                                     ./elibrary/bin//libsyscall.a(syscall.o)
                                                  ./elibrary/bin//libminic.a(elibs_stdio.o)
                                                  emodules/fm/fm_module/si4760/Tuner.o
esMEMS_Mfree                                      ./elibrary/bin//libsyscall.a(syscall.o)
                                                  ./elibrary/bin//libminic.a(elibs_stdio.o)
esMEMS_Palloc                                     ./elibrary/bin//libsyscall.a(syscall.o)
esMEMS_Pfree                                      ./elibrary/bin//libsyscall.a(syscall.o)
esMEMS_Realloc                                    ./elibrary/bin//libsyscall.a(syscall.o)
esMEMS_TotalMemSize                               ./elibrary/bin//libsyscall.a(syscall.o)
esMEMS_VA2PA                                      ./elibrary/bin//libsyscall.a(syscall.o)
esMEMS_VMCreate                                   ./elibrary/bin//libsyscall.a(syscall.o)
esMEMS_VMDelete                                   ./elibrary/bin//libsyscall.a(syscall.o)
esMEMS_VMalloc                                    ./elibrary/bin//libsyscall.a(syscall.o)
esMEMS_VMfree                                     ./elibrary/bin//libsyscall.a(syscall.o)
esMEM_BWDisable                                   ./elibrary/bin//libsyscall.a(syscall.o)
esMEM_BWEnable                                    ./elibrary/bin//libsyscall.a(syscall.o)
esMEM_BWGet                                       ./elibrary/bin//libsyscall.a(syscall.o)
esMEM_DramSuspend                                 ./elibrary/bin//libsyscall.a(syscall.o)
esMEM_DramWakeup                                  ./elibrary/bin//libsyscall.a(syscall.o)
esMEM_MasterGet                                   ./elibrary/bin//libsyscall.a(syscall.o)
esMEM_MasterSet                                   ./elibrary/bin//libsyscall.a(syscall.o)
esMEM_RegDramAccess                               ./elibrary/bin//libsyscall.a(syscall.o)
esMEM_ReleaseDramUsrMode                          ./elibrary/bin//libsyscall.a(syscall.o)
esMEM_RequestDramUsrMode                          ./elibrary/bin//libsyscall.a(syscall.o)
esMEM_SramRelBlk                                  ./elibrary/bin//libsyscall.a(syscall.o)
esMEM_SramReqBlk                                  ./elibrary/bin//libsyscall.a(syscall.o)
esMEM_SramSwitchBlk                               ./elibrary/bin//libsyscall.a(syscall.o)
esMEM_UnRegDramAccess                             ./elibrary/bin//libsyscall.a(syscall.o)
esMODS_MClose                                     ./elibrary/bin//libsyscall.a(syscall.o)
esMODS_MInstall                                   ./elibrary/bin//libsyscall.a(syscall.o)
esMODS_MIoctrl                                    ./elibrary/bin//libsyscall.a(syscall.o)
esMODS_MOpen                                      ./elibrary/bin//libsyscall.a(syscall.o)
esMODS_MRead                                      ./elibrary/bin//libsyscall.a(syscall.o)
esMODS_MUninstall                                 ./elibrary/bin//libsyscall.a(syscall.o)
esMODS_MWrite                                     ./elibrary/bin//libsyscall.a(syscall.o)
esMSTUB_GetFuncEntry                              ./elibrary/bin//libsyscall.a(syscall.o)
esMSTUB_RegFuncTbl                                ./elibrary/bin//libsyscall.a(syscall.o)
esMSTUB_UnRegFuncTbl                              ./elibrary/bin//libsyscall.a(syscall.o)
esPINS_ClearPending                               ./elibrary/bin//libsyscall.a(syscall.o)
esPINS_DisbaleInt                                 ./elibrary/bin//libsyscall.a(syscall.o)
esPINS_EnbaleInt                                  ./elibrary/bin//libsyscall.a(syscall.o)
esPINS_GetPinGrpStat                              ./elibrary/bin//libsyscall.a(syscall.o)
esPINS_GetPinStat                                 ./elibrary/bin//libsyscall.a(syscall.o)
esPINS_PinGrpRel                                  ./elibrary/bin//libsyscall.a(syscall.o)
esPINS_PinGrpReq                                  ./elibrary/bin//libsyscall.a(syscall.o)
esPINS_QueryInt                                   ./elibrary/bin//libsyscall.a(syscall.o)
esPINS_ReadPinData                                ./elibrary/bin//libsyscall.a(syscall.o)
esPINS_RegIntHdler                                ./elibrary/bin//libsyscall.a(syscall.o)
esPINS_SetIntMode                                 ./elibrary/bin//libsyscall.a(syscall.o)
esPINS_SetPinDrive                                ./elibrary/bin//libsyscall.a(syscall.o)
esPINS_SetPinIO                                   ./elibrary/bin//libsyscall.a(syscall.o)
esPINS_SetPinPull                                 ./elibrary/bin//libsyscall.a(syscall.o)
esPINS_SetPinStat                                 ./elibrary/bin//libsyscall.a(syscall.o)
esPINS_UnregIntHdler                              ./elibrary/bin//libsyscall.a(syscall.o)
esPINS_WritePinData                               ./elibrary/bin//libsyscall.a(syscall.o)
esPWRMAN_EnterStandby                             ./elibrary/bin//libsyscall.a(syscall.o)
esPWRMAN_GetStandbyPara                           ./elibrary/bin//libsyscall.a(syscall.o)
esPWRMAN_LockCpuFreq                              ./elibrary/bin//libsyscall.a(syscall.o)
esPWRMAN_RegDevice                                ./elibrary/bin//libsyscall.a(syscall.o)
esPWRMAN_RelPwrmanMode                            ./elibrary/bin//libsyscall.a(syscall.o)
esPWRMAN_ReqPwrmanMode                            ./elibrary/bin//libsyscall.a(syscall.o)
esPWRMAN_SetStandbyMode                           ./elibrary/bin//libsyscall.a(syscall.o)
esPWRMAN_UnlockCpuFreq                            ./elibrary/bin//libsyscall.a(syscall.o)
esPWRMAN_UnregDevice                              ./elibrary/bin//libsyscall.a(syscall.o)
esPWRMAN_UsrEventNotify                           ./elibrary/bin//libsyscall.a(syscall.o)
esRESM_RClose                                     ./elibrary/bin//libsyscall.a(syscall.o)
esRESM_ROpen                                      ./elibrary/bin//libsyscall.a(syscall.o)
esRESM_RRead                                      ./elibrary/bin//libsyscall.a(syscall.o)
esRESM_RSeek                                      ./elibrary/bin//libsyscall.a(syscall.o)
esSIOS_getchar                                    ./elibrary/bin//libsyscall.a(syscall.o)
esSIOS_gets                                       ./elibrary/bin//libsyscall.a(syscall.o)
esSIOS_putarg                                     ./elibrary/bin//libsyscall.a(syscall.o)
esSIOS_putstr                                     ./elibrary/bin//libsyscall.a(syscall.o)
                                                  ./elibrary/bin//libminic.a(elibs_stdio.o)
esSIOS_setbaud                                    ./elibrary/bin//libsyscall.a(syscall.o)
esSVC_RegCloseNode                                ./elibrary/bin//libsyscall.a(syscall.o)
esSVC_RegClrError                                 ./elibrary/bin//libsyscall.a(syscall.o)
esSVC_RegCreateKey                                ./elibrary/bin//libsyscall.a(syscall.o)
esSVC_RegCreatePath                               ./elibrary/bin//libsyscall.a(syscall.o)
esSVC_RegCreateSet                                ./elibrary/bin//libsyscall.a(syscall.o)
esSVC_RegDeleteKey                                ./elibrary/bin//libsyscall.a(syscall.o)
esSVC_RegDeleteNode                               ./elibrary/bin//libsyscall.a(syscall.o)
esSVC_RegDeletePath                               ./elibrary/bin//libsyscall.a(syscall.o)
esSVC_RegDeleteSet                                ./elibrary/bin//libsyscall.a(syscall.o)
esSVC_RegGetError                                 ./elibrary/bin//libsyscall.a(syscall.o)
esSVC_RegGetFirstKey                              ./elibrary/bin//libsyscall.a(syscall.o)
esSVC_RegGetFirstSet                              ./elibrary/bin//libsyscall.a(syscall.o)
esSVC_RegGetKeyCount                              ./elibrary/bin//libsyscall.a(syscall.o)
esSVC_RegGetKeyValue                              ./elibrary/bin//libsyscall.a(syscall.o)
esSVC_RegGetNextKey                               ./elibrary/bin//libsyscall.a(syscall.o)
esSVC_RegGetNextSet                               ./elibrary/bin//libsyscall.a(syscall.o)
esSVC_RegGetRootPath                              ./elibrary/bin//libsyscall.a(syscall.o)
esSVC_RegGetSetCount                              ./elibrary/bin//libsyscall.a(syscall.o)
esSVC_RegIni2Reg                                  ./elibrary/bin//libsyscall.a(syscall.o)
esSVC_RegOpenNode                                 ./elibrary/bin//libsyscall.a(syscall.o)
esSVC_RegReg2Ini                                  ./elibrary/bin//libsyscall.a(syscall.o)
esSVC_RegSetKeyValue                              ./elibrary/bin//libsyscall.a(syscall.o)
esSVC_RegSetRootPath                              ./elibrary/bin//libsyscall.a(syscall.o)
esSVC_ResourceRel                                 ./elibrary/bin//libsyscall.a(syscall.o)
esSVC_ResourceReq                                 ./elibrary/bin//libsyscall.a(syscall.o)
esTIME_ContiCntr                                  ./elibrary/bin//libsyscall.a(syscall.o)
esTIME_GetDate                                    ./elibrary/bin//libsyscall.a(syscall.o)
                                                  ./elibrary/bin//libminic.a(elibs_stdio.o)
esTIME_GetTime                                    ./elibrary/bin//libsyscall.a(syscall.o)
                                                  ./elibrary/bin//libminic.a(elibs_stdio.o)
esTIME_PauseCntr                                  ./elibrary/bin//libsyscall.a(syscall.o)
esTIME_QuerryAlarm                                ./elibrary/bin//libsyscall.a(syscall.o)
esTIME_QuerryCntr                                 ./elibrary/bin//libsyscall.a(syscall.o)
esTIME_QuerryCntrStat                             ./elibrary/bin//libsyscall.a(syscall.o)
esTIME_QuerryTimer                                ./elibrary/bin//libsyscall.a(syscall.o)
esTIME_ReleaseAlarm                               ./elibrary/bin//libsyscall.a(syscall.o)
esTIME_ReleaseCntr                                ./elibrary/bin//libsyscall.a(syscall.o)
esTIME_ReleaseTimer                               ./elibrary/bin//libsyscall.a(syscall.o)
esTIME_RequestAlarm                               ./elibrary/bin//libsyscall.a(syscall.o)
esTIME_RequestCntr                                ./elibrary/bin//libsyscall.a(syscall.o)
esTIME_RequestTimer                               ./elibrary/bin//libsyscall.a(syscall.o)
esTIME_SetCntrPrescale                            ./elibrary/bin//libsyscall.a(syscall.o)
esTIME_SetCntrValue                               ./elibrary/bin//libsyscall.a(syscall.o)
esTIME_SetDate                                    ./elibrary/bin//libsyscall.a(syscall.o)
esTIME_SetTime                                    ./elibrary/bin//libsyscall.a(syscall.o)
esTIME_StartAlarm                                 ./elibrary/bin//libsyscall.a(syscall.o)
esTIME_StartCntr                                  ./elibrary/bin//libsyscall.a(syscall.o)
esTIME_StartTimer                                 ./elibrary/bin//libsyscall.a(syscall.o)
esTIME_StopAlarm                                  ./elibrary/bin//libsyscall.a(syscall.o)
esTIME_StopCntr                                   ./elibrary/bin//libsyscall.a(syscall.o)
esTIME_StopTimer                                  ./elibrary/bin//libsyscall.a(syscall.o)
fiic                                              emodules/fm/dev_fm.o
fioctrl                                           ./elibrary/bin//libsyscall.a(syscall_fops.o)
fm                                                emodules/fm/dev_fm.o
fm_drv                                            emodules/fm/drv_fm.o
fm_exit                                           emodules/fm/fm_module/si4760/Tuner.o
                                                  emodules/fm/dev_fm.o
fm_init                                           emodules/fm/fm_module/si4760/Tuner.o
                                                  emodules/fm/dev_fm.o
fm_lock                                           emodules/fm/drv_fm.o
                                                  emodules/fm/dev_fm.o
fm_send_cmd2dab                                   emodules/fm/fm_module/si4760/Tuner.o
                                                  emodules/fm/fm_module/si4760/Rds.o
free                                              /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/../../../../riscv64-unknown-elf/lib/rv64imafdcxtheadc/lp64d/libc.a(lib_a-malloc.o)
                                                  emodules/fm/dev_fm.o
fsync                                             ./elibrary/bin//libsyscall.a(syscall_fops.o)
ftruncate                                         ./elibrary/bin//libsyscall.a(syscall_fops.o)
getIntStatus                                      emodules/fm/fm_module/si4760/Tu_Si_476X.o
get_dab_fm_link_backup_freq                       emodules/fm/fm_module/si4760/Tuner.o
                                                  emodules/fm/fm_module/si4760/Tu_Si_476X.o
                                                  emodules/fm/fm_module/si4760/Rds.o
get_fm_para                                       emodules/fm/fm_module/si4760/Tuner.o
                                                  emodules/fm/dev_fm.o
get_init_area                                     emodules/fm/fm_module/si4760/Tuner.o
                                                  emodules/fm/dev_fm.o
get_init_para                                     emodules/fm/fm_module/si4760/Tuner.o
                                                  emodules/fm/dev_fm.o
goodcnt                                           emodules/fm/fm_module/si4760/Tuner.o
                                                  emodules/fm/fm_module/si4760/Tu_Si_476X.o
                                                  emodules/fm/fm_module/si4760/Rds_sub.o
hal_fm_control                                    emodules/fm/dev_fm.o
hal_fm_init                                       emodules/fm/dev_fm.o
hal_fm_uninit                                     emodules/fm/dev_fm.o
init_setting_fm_para                              emodules/fm/fm_module/si4760/Tuner.o
                                                  emodules/fm/dev_fm.o
ioctl                                             ./elibrary/bin//libsyscall.a(syscall_fops.o)
                                                  emodules/fm/drv_fm.o
                                                  emodules/fm/dev_fm.o
                                                  emodules/fm/fm_module/si4760/Tuner.o
                                                  emodules/fm/fm_module/si4760/Tu_Si_476X.o
                                                  emodules/fm/fm_module/si4760/Rds.o
key_autostore                                     emodules/fm/fm_module/si4760/Tuner.o
link_pi_table                                     emodules/fm/fm_module/si4760/Tuner.o
                                                  emodules/fm/fm_module/si4760/Rds.o
ltpi                                              emodules/fm/fm_module/si4760/Rds.o
                                                  emodules/fm/fm_module/si4760/Tuner.o
                                                  emodules/fm/fm_module/si4760/Tu_Si_476X.o
ltps                                              emodules/fm/fm_module/si4760/Rds.o
ltpty                                             emodules/fm/fm_module/si4760/Rds.o
malloc                                            /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/../../../../riscv64-unknown-elf/lib/rv64imafdcxtheadc/lp64d/libc.a(lib_a-malloc.o)
                                                  emodules/fm/dev_fm.o
mask_timer                                        emodules/fm/fm_module/si4760/Rds.o
                                                  emodules/fm/fm_module/si4760/Tu_Si_476X.o
memcpy                                            /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/../../../../riscv64-unknown-elf/lib/rv64imafdcxtheadc/lp64d/libc.a(lib_a-memcpy.o)
                                                  emodules/fm/fm_module/si4760/Tuner.o
memset                                            /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/../../../../riscv64-unknown-elf/lib/rv64imafdcxtheadc/lp64d/libc.a(lib_a-memset.o)
                                                  ./elibrary/bin//libminic.a(elibs_stdio.o)
                                                  emodules/fm/fm_module/si4760/Tuner.o
                                                  emodules/fm/fm_module/si4760/Rds.o
mkdir                                             ./elibrary/bin//libsyscall.a(syscall_fops.o)
mmap                                              ./elibrary/bin//libsyscall.a(syscall.o)
modinfo                                           emodules/fm/magic.o
monitor_task                                      emodules/fm/dev_fm.o
msleep                                            ./elibrary/bin//libsyscall.a(syscall.o)
munmap                                            ./elibrary/bin//libsyscall.a(syscall.o)
network_ptr                                       emodules/fm/fm_module/si4760/Rds.o
                                                  emodules/fm/fm_module/si4760/Tuner.o
                                                  emodules/fm/fm_module/si4760/Rds_sub.o
networks                                          emodules/fm/fm_module/si4760/Rds.o
                                                  emodules/fm/fm_module/si4760/Tuner.o
                                                  emodules/fm/fm_module/si4760/Tu_Si_476X.o
                                                  emodules/fm/fm_module/si4760/Rds_sub.o
open                                              /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/../../../../riscv64-unknown-elf/lib/rv64imafdcxtheadc/lp64d/libc.a(lib_a-sysopen.o)
                                                  emodules/fm/drv_fm.o
                                                  emodules/fm/dev_fm.o
                                                  emodules/fm/fm_module/si4760/Tuner.o
                                                  emodules/fm/fm_module/si4760/Tu_Si_476X.o
                                                  emodules/fm/fm_module/si4760/Rds.o
opendir                                           ./elibrary/bin//libsyscall.a(syscall_fops.o)
pi_bak                                            emodules/fm/fm_module/si4760/Rds.o
                                                  emodules/fm/fm_module/si4760/Tuner.o
                                                  emodules/fm/fm_module/si4760/Tu_Si_476X.o
pipe                                              ./elibrary/bin//libsyscall.a(syscall.o)
poll                                              ./elibrary/bin//libsyscall.a(syscall.o)
preset_pi                                         emodules/fm/fm_module/si4760/Rds.o
                                                  emodules/fm/fm_module/si4760/Tuner.o
                                                  emodules/fm/fm_module/si4760/Tu_Si_476X.o
                                                  emodules/fm/fm_module/si4760/Rds_sub.o
printk                                            ./elibrary/bin//libminic.a(elibs_stdio.o)
pro_pi                                            emodules/fm/fm_module/si4760/Tuner.o
ps_flg                                            emodules/fm/fm_module/si4760/Rds.o
pthread_atfork                                    ./elibrary/bin//libsyscall.a(syscall.o)
pthread_attr_destroy                              ./elibrary/bin//libsyscall.a(syscall.o)
pthread_attr_getdetachstate                       ./elibrary/bin//libsyscall.a(syscall.o)
pthread_attr_getguardsize                         ./elibrary/bin//libsyscall.a(syscall.o)
pthread_attr_getschedparam                        ./elibrary/bin//libsyscall.a(syscall.o)
pthread_attr_getschedpolicy                       ./elibrary/bin//libsyscall.a(syscall.o)
pthread_attr_getscope                             ./elibrary/bin//libsyscall.a(syscall.o)
pthread_attr_getstack                             ./elibrary/bin//libsyscall.a(syscall.o)
pthread_attr_getstackaddr                         ./elibrary/bin//libsyscall.a(syscall.o)
pthread_attr_getstacksize                         ./elibrary/bin//libsyscall.a(syscall.o)
pthread_attr_init                                 ./elibrary/bin//libsyscall.a(syscall.o)
pthread_attr_setdetachstate                       ./elibrary/bin//libsyscall.a(syscall.o)
pthread_attr_setguardsize                         ./elibrary/bin//libsyscall.a(syscall.o)
pthread_attr_setschedparam                        ./elibrary/bin//libsyscall.a(syscall.o)
pthread_attr_setschedpolicy                       ./elibrary/bin//libsyscall.a(syscall.o)
pthread_attr_setscope                             ./elibrary/bin//libsyscall.a(syscall.o)
pthread_attr_setstack                             ./elibrary/bin//libsyscall.a(syscall.o)
pthread_attr_setstackaddr                         ./elibrary/bin//libsyscall.a(syscall.o)
pthread_attr_setstacksize                         ./elibrary/bin//libsyscall.a(syscall.o)
pthread_barrier_destroy                           ./elibrary/bin//libsyscall.a(syscall.o)
pthread_barrier_init                              ./elibrary/bin//libsyscall.a(syscall.o)
pthread_barrier_wait                              ./elibrary/bin//libsyscall.a(syscall.o)
pthread_barrierattr_destroy                       ./elibrary/bin//libsyscall.a(syscall.o)
pthread_barrierattr_getpshared                    ./elibrary/bin//libsyscall.a(syscall.o)
pthread_barrierattr_init                          ./elibrary/bin//libsyscall.a(syscall.o)
pthread_barrierattr_setpshared                    ./elibrary/bin//libsyscall.a(syscall.o)
pthread_cancel                                    ./elibrary/bin//libsyscall.a(syscall.o)
pthread_cleanup_pop                               ./elibrary/bin//libsyscall.a(syscall.o)
pthread_cleanup_push                              ./elibrary/bin//libsyscall.a(syscall.o)
pthread_cond_broadcast                            ./elibrary/bin//libsyscall.a(syscall.o)
pthread_cond_destroy                              ./elibrary/bin//libsyscall.a(syscall.o)
pthread_cond_init                                 ./elibrary/bin//libsyscall.a(syscall.o)
pthread_cond_signal                               ./elibrary/bin//libsyscall.a(syscall.o)
pthread_cond_timedwait                            ./elibrary/bin//libsyscall.a(syscall.o)
pthread_cond_wait                                 ./elibrary/bin//libsyscall.a(syscall.o)
pthread_condattr_destroy                          ./elibrary/bin//libsyscall.a(syscall.o)
pthread_condattr_getclock                         ./elibrary/bin//libsyscall.a(syscall.o)
pthread_condattr_init                             ./elibrary/bin//libsyscall.a(syscall.o)
pthread_condattr_setclock                         ./elibrary/bin//libsyscall.a(syscall.o)
pthread_create                                    ./elibrary/bin//libsyscall.a(syscall.o)
pthread_detach                                    ./elibrary/bin//libsyscall.a(syscall.o)
pthread_exit                                      ./elibrary/bin//libsyscall.a(syscall.o)
pthread_getspecific                               ./elibrary/bin//libsyscall.a(syscall.o)
pthread_join                                      ./elibrary/bin//libsyscall.a(syscall.o)
pthread_key_create                                ./elibrary/bin//libsyscall.a(syscall.o)
pthread_key_delete                                ./elibrary/bin//libsyscall.a(syscall.o)
pthread_kill                                      ./elibrary/bin//libsyscall.a(syscall.o)
pthread_mutex_destroy                             ./elibrary/bin//libsyscall.a(syscall.o)
pthread_mutex_init                                ./elibrary/bin//libsyscall.a(syscall.o)
pthread_mutex_lock                                ./elibrary/bin//libsyscall.a(syscall.o)
pthread_mutex_trylock                             ./elibrary/bin//libsyscall.a(syscall.o)
pthread_mutex_unlock                              ./elibrary/bin//libsyscall.a(syscall.o)
pthread_mutexattr_destroy                         ./elibrary/bin//libsyscall.a(syscall.o)
pthread_mutexattr_getpshared                      ./elibrary/bin//libsyscall.a(syscall.o)
pthread_mutexattr_gettype                         ./elibrary/bin//libsyscall.a(syscall.o)
pthread_mutexattr_init                            ./elibrary/bin//libsyscall.a(syscall.o)
pthread_mutexattr_setpshared                      ./elibrary/bin//libsyscall.a(syscall.o)
pthread_mutexattr_settype                         ./elibrary/bin//libsyscall.a(syscall.o)
pthread_once                                      ./elibrary/bin//libsyscall.a(syscall.o)
pthread_rwlock_destroy                            ./elibrary/bin//libsyscall.a(syscall.o)
pthread_rwlock_init                               ./elibrary/bin//libsyscall.a(syscall.o)
pthread_rwlock_rdlock                             ./elibrary/bin//libsyscall.a(syscall.o)
pthread_rwlock_timedrdlock                        ./elibrary/bin//libsyscall.a(syscall.o)
pthread_rwlock_timedwrlock                        ./elibrary/bin//libsyscall.a(syscall.o)
pthread_rwlock_tryrdlock                          ./elibrary/bin//libsyscall.a(syscall.o)
pthread_rwlock_trywrlock                          ./elibrary/bin//libsyscall.a(syscall.o)
pthread_rwlock_unlock                             ./elibrary/bin//libsyscall.a(syscall.o)
pthread_rwlock_wrlock                             ./elibrary/bin//libsyscall.a(syscall.o)
pthread_rwlockattr_destroy                        ./elibrary/bin//libsyscall.a(syscall.o)
pthread_rwlockattr_getpshared                     ./elibrary/bin//libsyscall.a(syscall.o)
pthread_rwlockattr_init                           ./elibrary/bin//libsyscall.a(syscall.o)
pthread_rwlockattr_setpshared                     ./elibrary/bin//libsyscall.a(syscall.o)
pthread_self                                      ./elibrary/bin//libsyscall.a(syscall.o)
pthread_setcancelstate                            ./elibrary/bin//libsyscall.a(syscall.o)
pthread_setcanceltype                             ./elibrary/bin//libsyscall.a(syscall.o)
pthread_setname_np                                ./elibrary/bin//libsyscall.a(syscall.o)
pthread_setspecific                               ./elibrary/bin//libsyscall.a(syscall.o)
pthread_spin_destroy                              ./elibrary/bin//libsyscall.a(syscall.o)
pthread_spin_init                                 ./elibrary/bin//libsyscall.a(syscall.o)
pthread_spin_lock                                 ./elibrary/bin//libsyscall.a(syscall.o)
pthread_spin_trylock                              ./elibrary/bin//libsyscall.a(syscall.o)
pthread_spin_unlock                               ./elibrary/bin//libsyscall.a(syscall.o)
pthread_system_init                               ./elibrary/bin//libsyscall.a(syscall.o)
pthread_testcancel                                ./elibrary/bin//libsyscall.a(syscall.o)
pty_mode                                          emodules/fm/fm_module/si4760/Rds.o
                                                  emodules/fm/fm_module/si4760/Tuner.o
                                                  emodules/fm/fm_module/si4760/Tu_Si_476X.o
rds_save_af_list_1                                emodules/fm/fm_module/si4760/Rds.o
rds_save_af_list_2                                emodules/fm/fm_module/si4760/Rds.o
rds_save_af_list_function                         emodules/fm/fm_module/si4760/Rds.o
                                                  emodules/fm/fm_module/si4760/Rds_sub.o
rds_search_dab_pi_for_rds_link                    emodules/fm/fm_module/si4760/Rds.o
readdir                                           ./elibrary/bin//libsyscall.a(syscall_fops.o)
remove                                            ./elibrary/bin//libsyscall.a(syscall_fops.o)
rewinddir                                         ./elibrary/bin//libsyscall.a(syscall_fops.o)
rmdir                                             ./elibrary/bin//libsyscall.a(syscall_fops.o)
rsp                                               emodules/fm/fm_module/si4760/Tuner.o
rt_device_register                                ./elibrary/bin//libsyscall.a(syscall.o)
                                                  emodules/fm/dev_fm.o
rt_device_unregister                              ./elibrary/bin//libsyscall.a(syscall.o)
                                                  emodules/fm/dev_fm.o
select                                            ./elibrary/bin//libsyscall.a(syscall.o)
sem_close                                         ./elibrary/bin//libsyscall.a(syscall.o)
sem_destroy                                       ./elibrary/bin//libsyscall.a(syscall.o)
sem_getvalue                                      ./elibrary/bin//libsyscall.a(syscall.o)
sem_init                                          ./elibrary/bin//libsyscall.a(syscall.o)
sem_open                                          ./elibrary/bin//libsyscall.a(syscall.o)
sem_post                                          ./elibrary/bin//libsyscall.a(syscall.o)
sem_timedwait                                     ./elibrary/bin//libsyscall.a(syscall.o)
sem_trywait                                       ./elibrary/bin//libsyscall.a(syscall.o)
sem_unlink                                        ./elibrary/bin//libsyscall.a(syscall.o)
sem_wait                                          ./elibrary/bin//libsyscall.a(syscall.o)
set_dab_fm_link_backup_freq                       emodules/fm/fm_module/si4760/Tuner.o
set_dab_fm_link_current_info                      emodules/fm/fm_module/si4760/Tuner.o
                                                  emodules/fm/fm_module/si4760/Rds.o
set_dab_rds_link_info                             emodules/fm/fm_module/si4760/Tuner.o
                                                  emodules/fm/dev_fm.o
set_fast_high_cut_on_off                          emodules/fm/fm_module/si4760/Tuner.o
                                                  emodules/fm/dev_fm.o
set_high_cut_on_off                               emodules/fm/fm_module/si4760/Tuner.o
                                                  emodules/fm/dev_fm.o
set_init_area                                     emodules/fm/fm_module/si4760/Tuner.o
                                                  emodules/fm/dev_fm.o
set_local_seek_on_off                             emodules/fm/fm_module/si4760/Tuner.o
                                                  emodules/fm/dev_fm.o
set_mono_on_off                                   emodules/fm/fm_module/si4760/Tuner.o
set_multi_path_on_off                             emodules/fm/fm_module/si4760/Tuner.o
                                                  emodules/fm/dev_fm.o
set_rds_af_on_off                                 emodules/fm/fm_module/si4760/Tuner.o
                                                  emodules/fm/dev_fm.o
set_rds_ct_on_off                                 emodules/fm/fm_module/si4760/Tuner.o
                                                  emodules/fm/dev_fm.o
set_rds_on_off                                    emodules/fm/fm_module/si4760/Tuner.o
                                                  emodules/fm/dev_fm.o
set_rds_ta_on_off                                 emodules/fm/fm_module/si4760/Tuner.o
                                                  emodules/fm/dev_fm.o
set_search_stop                                   emodules/fm/fm_module/si4760/Tuner.o
                                                  emodules/fm/dev_fm.o
set_search_stop_flag                              emodules/fm/fm_module/si4760/Tuner.o
                                                  emodules/fm/dev_fm.o
set_soft_mute_on_off                              emodules/fm/fm_module/si4760/Tuner.o
                                                  emodules/fm/dev_fm.o
settimeofday                                      ./elibrary/bin//libsyscall.a(syscall_fops.o)
si4750_init_func_info                             emodules/fm/fm_module/si4760/Tu_Si_476X.o
                                                  emodules/fm/fm_module/si4760/Tuner.o
si475x6x_FORCECTS                                 emodules/fm/fm_module/si4760/Tu_Si_476X.o
si475x6x_am_patch                                 emodules/fm/fm_module/si4760/Tu_Si_476X.o
si475x6x_get_PartInfo                             emodules/fm/fm_module/si4760/Tu_Si_476X.o
si475x6x_loadPatch                                emodules/fm/fm_module/si4760/Tu_Si_476X.o
si475xFMRX_DigitAudioPinSet                       emodules/fm/fm_module/si4760/Tu_Si_476X.o
si475xFMRX_front_end_cfg                          emodules/fm/fm_module/si4760/Tu_Si_476X.o
si475xFMRX_hardware_cfg                           emodules/fm/fm_module/si4760/Tu_Si_476X.o
si475xFMRX_output_config                          emodules/fm/fm_module/si4760/Tu_Si_476X.o
si475xFMRX_powerdown                              emodules/fm/fm_module/si4760/Tu_Si_476X.o
si475xFMRX_powerup                                emodules/fm/fm_module/si4760/Tu_Si_476X.o
si475x_command                                    emodules/fm/fm_module/si4760/Tu_Si_476X.o
                                                  emodules/fm/fm_module/si4760/Rds.o
si475x_get_Version                                emodules/fm/fm_module/si4760/Tu_Si_476X.o
                                                  emodules/fm/fm_module/si4760/Tuner.o
si475x_get_property                               emodules/fm/fm_module/si4760/Tu_Si_476X.o
si475x_readStatus                                 emodules/fm/fm_module/si4760/Tu_Si_476X.o
si475x_set_property                               emodules/fm/fm_module/si4760/Tu_Si_476X.o
                                                  emodules/fm/fm_module/si4760/Tuner.o
si475x_waitForCTS                                 emodules/fm/fm_module/si4760/Tu_Si_476X.o
si4760_get_partinfo                               emodules/fm/fm_module/si4760/Tuner.o
si4760_powerdown                                  emodules/fm/fm_module/si4760/Tuner.o
si4760_powerup                                    emodules/fm/fm_module/si4760/Tuner.o
si4760_powerup_command                            emodules/fm/fm_module/si4760/Tuner.o
si4760_set_reset_io                               emodules/fm/fm_module/si4760/Tuner.o
                                                  emodules/fm/fm_module/si4760/Tu_Si_476X.o
si476x_aux_para                                   emodules/fm/fm_module/si4760/Tuner.o
si476x_bt_para                                    emodules/fm/fm_module/si4760/Tuner.o
si476x_bus_thread                                 emodules/fm/fm_module/si4760/Tuner.o
si476x_fm_para                                    emodules/fm/fm_module/si4760/Tuner.o
                                                  emodules/fm/fm_module/si4760/Tu_Si_476X.o
                                                  emodules/fm/fm_module/si4760/Rds_sub.o
                                                  emodules/fm/fm_module/si4760/Rds.o
si476x_fm_patch                                   emodules/fm/fm_module/si4760/Tu_Si_476X.o
si476x_init_para                                  emodules/fm/fm_module/si4760/Tuner.o
                                                  emodules/fm/fm_module/si4760/Tu_Si_476X.o
                                                  emodules/fm/fm_module/si4760/Rds.o
si476x_root_para                                  emodules/fm/fm_module/si4760/Tuner.o
si476x_system_para                                emodules/fm/fm_module/si4760/Tuner.o
                                                  emodules/fm/fm_module/si4760/Rds.o
si476x_thread_init                                emodules/fm/fm_module/si4760/Tuner.o
si476x_thread_uninit                              emodules/fm/fm_module/si4760/Tuner.o
sleep                                             ./elibrary/bin//libsyscall.a(syscall.o)
smeter_bak                                        emodules/fm/fm_module/si4760/Rds.o
statfs                                            ./elibrary/bin//libsyscall.a(syscall_fops.o)
sunxi_driver_fm_init                              emodules/fm/dev_fm.o
                                                  emodules/fm/drv_fm.o
sunxi_driver_fm_uninit                            emodules/fm/dev_fm.o
                                                  emodules/fm/drv_fm.o
ta_cnt                                            emodules/fm/fm_module/si4760/Rds.o
                                                  emodules/fm/fm_module/si4760/Tuner.o
                                                  emodules/fm/fm_module/si4760/Tu_Si_476X.o
ta_seek_cnt                                       emodules/fm/fm_module/si4760/Rds.o
ta_seek_net                                       emodules/fm/fm_module/si4760/Rds.o
ta_timer                                          emodules/fm/fm_module/si4760/Rds.o
tp_flg                                            emodules/fm/fm_module/si4760/Rds.o
                                                  emodules/fm/fm_module/si4760/Tu_Si_476X.o
tuner_break_search_pi_for_link                    emodules/fm/fm_module/si4760/Tuner.o
                                                  emodules/fm/dev_fm.o
tuner_close_dev                                   emodules/fm/fm_module/si4760/Tuner.o
tuner_fiic                                        emodules/fm/fm_module/si4760/Tuner.o
tuner_get_Tu_Divisor_by_freq                      emodules/fm/fm_module/si4760/Tuner.o
                                                  emodules/fm/fm_module/si4760/Tu_Si_476X.o
tuner_mode                                        emodules/fm/drv_fm.o
                                                  emodules/fm/dev_fm.o
tuner_open_dev                                    emodules/fm/fm_module/si4760/Tuner.o
tuner_save_data                                   emodules/fm/fm_module/si4760/Tuner.o
                                                  emodules/fm/fm_module/si4760/Tu_Si_476X.o
                                                  emodules/fm/fm_module/si4760/Rds.o
tuner_save_system_data                            emodules/fm/fm_module/si4760/Tuner.o
                                                  emodules/fm/fm_module/si4760/Tu_Si_476X.o
                                                  emodules/fm/fm_module/si4760/Rds.o
tuner_scan_search_pi_for_link                     emodules/fm/fm_module/si4760/Tuner.o
tuner_select_preset                               emodules/fm/fm_module/si4760/Tuner.o
                                                  emodules/fm/dev_fm.o
tuner_set_channel                                 emodules/fm/fm_module/si4760/Tuner.o
tuner_set_channel_for_dab_link                    emodules/fm/fm_module/si4760/Tuner.o
tuner_set_mute                                    emodules/fm/fm_module/si4760/Tuner.o
tuner_store_preset                                emodules/fm/fm_module/si4760/Tuner.o
                                                  emodules/fm/dev_fm.o
update_alt_freq_5X                                emodules/fm/fm_module/si4760/Rds.o
update_clock_5X                                   emodules/fm/fm_module/si4760/Rds.o
update_pi_5X                                      emodules/fm/fm_module/si4760/Rds.o
update_ps_5X                                      emodules/fm/fm_module/si4760/Rds.o
update_pty_5X                                     emodules/fm/fm_module/si4760/Rds.o
usleep                                            ./elibrary/bin//libsyscall.a(syscall.o)
                                                  emodules/fm/fm_module/si4760/Tu_Si_476X.o
wait_ms                                           emodules/fm/fm_module/si4760/Tuner.o
wait_pty_over                                     emodules/fm/fm_module/si4760/Tu_Si_476X.o
wait_us                                           emodules/fm/fm_module/si4760/Tuner.o
write                                             /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/../../../../riscv64-unknown-elf/lib/rv64imafdcxtheadc/lp64d/libc.a(lib_a-syswrite.o)
                                                  emodules/fm/dev_fm.o
