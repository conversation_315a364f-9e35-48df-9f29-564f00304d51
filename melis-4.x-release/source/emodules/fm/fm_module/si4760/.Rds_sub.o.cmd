cmd_emodules/fm/fm_module/si4760/Rds_sub.o := /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/source/../toolchain/riscv64-elf-x86_64-20201104//bin/riscv64-unknown-elf-gcc -Wp,-MD,emodules/fm/fm_module/si4760/.Rds_sub.o.d  -isystem /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/include  -Iinclude -Iinclude/melis -Iinclude/melis/common -Iinclude/melis/eboot -Iinclude/melis/eboard -Iinclude/melis/ekernel -Iinclude/melis/ekernel/arch -Iinclude/melis/ekernel/csp -Iinclude/melis/ekernel/drivers -Iinclude/melis/ekernel/filesystem -Iinclude/melis/ekernel/pthread -Iinclude/melis/elibrary -Iinclude/melis/elibrary/libc -Iinclude/melis/elibrary/libc/mediainfo -Iinclude/melis/elibrary/libc/misc -Iinclude/melis/elibrary/libc/misc/pub0 -Iinclude/melis/emodules -Iinclude/melis/ipc -Iinclude/melis/misc -Iinclude/melis/of -Iinclude/melis/sys -Iinclude/melis/video -Iekernel/components/thirdparty/finsh_cli -Iekernel/core/rt-thread/include -Iekernel/components/thirdparty/dfs/include -Iekernel/drivers/rtos-hal/hal/source -Iekernel/drivers/include/osal -Iinclude/generated -Iinclude/generated/uapi/melis -I./include/melis/elibrary -include ./include/melis/kconfig.h -Iinclude/melis/ekernel/arch/riscv -Iinclude/melis/arch/riscv -DARCH_CPU_64BIT -mcmodel=medany -mabi=lp64d -march=rv64gcxthead -falign-functions=4 -mcmodel=medany -mabi=lp64d -march=rv64gcxthead -Wall -Wundef -Wstrict-prototypes -Wno-trigraphs -fno-strict-aliasing -fno-common -Werror-implicit-function-declaration -Wno-format-security -fno-builtin-printf -D_SYS__PTHREADTYPES_H_ -fno-delete-null-pointer-checks -ffunction-sections -fdata-sections -fzero-initialized-in-bss -Wmaybe-uninitialized -Os -g -gdwarf-2 -gstrict-dwarf -Wstrict-prototypes -Wundef -Wno-unused-function -Wno-unused-variable -fshort-enums -Wsizeof-pointer-memaccess --param=allow-store-data-races=0 -Wframe-larger-than=8192 -fno-stack-protector -Wno-unused-but-set-variable -fomit-frame-pointer -fno-var-tracking-assignments -g -Wdeclaration-after-statement -Wno-pointer-sign -fno-strict-overflow -fconserve-stack -Werror=implicit-int -Werror=strict-prototypes -DCC_HAVE_ASM_GOTO -Wno-declaration-after-statement -mcmodel=medany -mabi=lp64d -march=rv64gcxthead -Wno-unused-label -Wextra -Wno-unused-parameter -Wno-old-style-declaration -Wno-sign-compare -I./include/melis/kernel -I./include/melis/libs/libc -I./include/melis/board -I./include/melis/emodules -I./include/melis/ekernel/drivers -I./emodules/fm/fm_module/si4760/ -I./emodules/fm/ -I./ekernel/drivers/include/hal -I./ekernel/drivers/include/drv -I./ekernel/core/rt-thread/include  -DMODULE  -D"KBUILD_STR(s)=\#s" -D"KBUILD_BASENAME=KBUILD_STR(Rds_sub)"  -D"KBUILD_MODNAME=KBUILD_STR(fm)" -c -o emodules/fm/fm_module/si4760/.tmp_Rds_sub.o emodules/fm/fm_module/si4760/Rds_sub.c

source_emodules/fm/fm_module/si4760/Rds_sub.o := emodules/fm/fm_module/si4760/Rds_sub.c

deps_emodules/fm/fm_module/si4760/Rds_sub.o := \
  include/melis/kconfig.h \
    $(wildcard include/config/h.h) \
  emodules/fm/fm_module/si4760/Tuner.h \
  emodules/fm/fm_module/si4760/../../drv_fm_i.h \
  include/melis/emodules/mod_defs.h \
  include/melis/typedef.h \
    $(wildcard include/config/drivers/usb/gadget/qvh.h) \
  /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/lib/gcc/riscv64-unknown-elf/8.4.0/include/stddef.h \
  /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/lib/gcc/riscv64-unknown-elf/8.4.0/include/stdint.h \
  /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/riscv64-unknown-elf/include/stdint.h \
  /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/riscv64-unknown-elf/include/machine/_default_types.h \
  /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/riscv64-unknown-elf/include/sys/features.h \
  /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/riscv64-unknown-elf/include/_newlib_version.h \
  /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/riscv64-unknown-elf/include/sys/_intsup.h \
  /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/riscv64-unknown-elf/include/sys/_stdint.h \
  /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/lib/gcc/riscv64-unknown-elf/8.4.0/include/stdarg.h \
  /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/lib/gcc/riscv64-unknown-elf/8.4.0/include-fixed/limits.h \
  /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/lib/gcc/riscv64-unknown-elf/8.4.0/include-fixed/syslimits.h \
  /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/riscv64-unknown-elf/include/limits.h \
  /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/riscv64-unknown-elf/include/newlib.h \
  /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/riscv64-unknown-elf/include/sys/cdefs.h \
  include/melis/ekernel/kapi.h \
    $(wildcard include/config/melis/legacy/driver/man.h) \
    $(wildcard include/config/t.h) \
    $(wildcard include/config/kernel/use/sbi.h) \
  /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/riscv64-unknown-elf/include/errno.h \
  /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/riscv64-unknown-elf/include/sys/errno.h \
  /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/riscv64-unknown-elf/include/sys/reent.h \
  /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/riscv64-unknown-elf/include/_ansi.h \
  /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/riscv64-unknown-elf/include/sys/config.h \
    $(wildcard include/config/h//.h) \
  /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/riscv64-unknown-elf/include/machine/ieeefp.h \
  /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/riscv64-unknown-elf/include/sys/_types.h \
  /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/riscv64-unknown-elf/include/machine/_types.h \
  /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/riscv64-unknown-elf/include/sys/lock.h \
  include/melis/ekernel/kmsg.h \
    $(wildcard include/config/drivers/usb/gadget/ncm.h) \
    $(wildcard include/config/setting/update.h) \
  include/melis/eboot/boot.h \
  include/melis/ekernel/drivers/sys_charset.h \
  include/melis/ekernel/drivers/sys_clock.h \
  include/melis/ekernel/ktype.h \
  include/melis/ekernel/csp/csp_ccm_para.h \
    $(wildcard include/config/drivers/sunxi/clk.h) \
    $(wildcard include/config/drivers/sunxi/ccu.h) \
  include/melis/kconfig.h \
  include/melis/ekernel/drivers/sys_device.h \
    $(wildcard include/config/spinor/cmmb/data/offset.h) \
    $(wildcard include/config/spinor/cmmb/data/size.h) \
    $(wildcard include/config/spinor/apps/data/offset.h) \
    $(wildcard include/config/spinor/apps/data/size.h) \
    $(wildcard include/config/spinor/mcu/update/data/offset.h) \
    $(wildcard include/config/spinor/mcu/update/data/size.h) \
  include/melis/ekernel/drivers/sys_fsys.h \
    $(wildcard include/config/soc/sun20iw1.h) \
    $(wildcard include/config/soc/sun8iw20.h) \
    $(wildcard include/config/driver/spinor.h) \
  /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/lib/gcc/riscv64-unknown-elf/8.4.0/include/stdbool.h \
  include/melis/ekernel/drivers/sys_device.h \
  include/melis/ekernel/drivers/sys_hwsc.h \
  include/melis/ekernel/drivers/sys_input.h \
  include/melis/ekernel/drivers/sys_mems.h \
  include/melis/ekernel/csp/csp_dram_para.h \
    $(wildcard include/config/soc/sun3iw2.h) \
    $(wildcard include/config/soc/sun8i.h) \
    $(wildcard include/config/soc/sun20iw3.h) \
    $(wildcard include/config/soc/sun3iw1.h) \
  include/melis/ekernel/drivers/sys_pins.h \
  include/melis/script.h \
  include/melis/ekernel/drivers/sys_powerman.h \
  include/melis/ekernel/drivers/sys_svc.h \
  include/melis/ekernel/drivers/sys_time.h \
  include/melis/ekernel/csp/csp_dma_para.h \
    $(wildcard include/config/soc/sun8iw19.h) \
  include/melis/ekernel/csp/csp_int_para.h \
  include/melis/elibrary/libc/elibs_stdio.h \
  ekernel/core/rt-thread/include/rtthread.h \
  ekernel/core/rt-thread/include/rtconfig.h \
    $(wildcard include/config/rt/name/max.h) \
    $(wildcard include/config/rt/align/size.h) \
    $(wildcard include/config/modules.h) \
    $(wildcard include/config/rt/thread/priority/max.h) \
    $(wildcard include/config/hz.h) \
    $(wildcard include/config/rt/debug.h) \
    $(wildcard include/config/rt/debug/init/config.h) \
    $(wildcard include/config/rt/debug/thread/config.h) \
    $(wildcard include/config/rt/debug/scheduler/config.h) \
    $(wildcard include/config/rt/debug/ipc/config.h) \
    $(wildcard include/config/rt/debug/timer/config.h) \
    $(wildcard include/config/rt/debug/irq/config.h) \
    $(wildcard include/config/rt/debug/mem/config.h) \
    $(wildcard include/config/rt/debug/slab/config.h) \
    $(wildcard include/config/rt/debug/memheap/config.h) \
    $(wildcard include/config/rt/debug/module/config.h) \
    $(wildcard include/config/rt/using/overflow/check.h) \
    $(wildcard include/config/rt/using/hook.h) \
    $(wildcard include/config/rt/using/idle/hook.h) \
    $(wildcard include/config/rt/using/timer/soft.h) \
    $(wildcard include/config/rt/timer/thread/prio.h) \
    $(wildcard include/config/rt/timer/thread/stack/size.h) \
    $(wildcard include/config/rt/using/semaphore.h) \
    $(wildcard include/config/rt/using/mutex.h) \
    $(wildcard include/config/rt/using/event.h) \
    $(wildcard include/config/rt/using/mailbox.h) \
    $(wildcard include/config/rt/using/messagequeue.h) \
    $(wildcard include/config/rt/using/pipe.h) \
    $(wildcard include/config/rt/using/ringbuffer.h) \
    $(wildcard include/config/rt/using/waitqueue.h) \
    $(wildcard include/config/rt/using/workqueue.h) \
    $(wildcard include/config/rt/using/completion.h) \
    $(wildcard include/config/rt/using/signals.h) \
    $(wildcard include/config/rt/using/mempool.h) \
    $(wildcard include/config/rt/using/memheap.h) \
    $(wildcard include/config/rt/using/noheap.h) \
    $(wildcard include/config/rt/using/small/mem.h) \
    $(wildcard include/config/rt/using/slab.h) \
    $(wildcard include/config/rt/using/memheap/as/heap.h) \
    $(wildcard include/config/rt/using/memtrace.h) \
    $(wildcard include/config/rt/using/heap.h) \
    $(wildcard include/config/rt/using/device.h) \
    $(wildcard include/config/rt/using/interrupt/info.h) \
    $(wildcard include/config/rt/using/console.h) \
    $(wildcard include/config/rt/consolebuf/size.h) \
    $(wildcard include/config/rt/console/device/name.h) \
    $(wildcard include/config/rt/using/finsh.h) \
    $(wildcard include/config/finsh/using/symtab.h) \
    $(wildcard include/config/finsh/thread/name.h) \
    $(wildcard include/config/finsh/using/history.h) \
    $(wildcard include/config/finsh/history/lines.h) \
    $(wildcard include/config/finsh/using/description.h) \
    $(wildcard include/config/finsh/thread/priority.h) \
    $(wildcard include/config/finsh/thread/stack/size.h) \
    $(wildcard include/config/finsh/cmd/size.h) \
    $(wildcard include/config/finsh/using/msh.h) \
    $(wildcard include/config/finsh/using/msh/default.h) \
    $(wildcard include/config/finsh/arg/max.h) \
    $(wildcard include/config/rt/using/dfs.h) \
  ekernel/core/rt-thread/include/rtdebug.h \
  ekernel/core/rt-thread/include/rtdef.h \
    $(wildcard include/config/arch/riscv.h) \
    $(wildcard include/config/arch/riscv/vector.h) \
  include/melis/ekernel/arch/riscv/excep.h \
    $(wildcard include/config/fpu/double.h) \
  ekernel/core/rt-thread/include/rtservice.h \
  ekernel/core/rt-thread/include/rtm.h \
  ekernel/components/thirdparty/finsh_cli/finsh_api.h \
  ekernel/drivers/include/hal/aw_list.h \
    $(wildcard include/config/debug/list.h) \
  ekernel/drivers/include/hal/aw_common.h \
    $(wildcard include/config/kernel/freertos.h) \
  ekernel/drivers/include/hal/sunxi_hal_common.h \
    $(wildcard include/config/debug/backtrace.h) \
    $(wildcard include/config/components/amp.h) \
  /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/riscv64-unknown-elf/include/stdio.h \
  /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/riscv64-unknown-elf/include/_ansi.h \
  /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/riscv64-unknown-elf/include/sys/types.h \
  /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/riscv64-unknown-elf/include/machine/endian.h \
  /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/riscv64-unknown-elf/include/machine/_endian.h \
  /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/riscv64-unknown-elf/include/sys/select.h \
  /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/riscv64-unknown-elf/include/sys/_sigset.h \
  /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/riscv64-unknown-elf/include/sys/_timeval.h \
  /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/riscv64-unknown-elf/include/sys/timespec.h \
  /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/riscv64-unknown-elf/include/sys/_timespec.h \
  /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/riscv64-unknown-elf/include/sys/_pthreadtypes.h \
  /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/riscv64-unknown-elf/include/machine/types.h \
  /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/riscv64-unknown-elf/include/sys/stdio.h \
  include/melis/backtrace.h \
  include/melis/ekernel/arch/riscv/barrier.h \
    $(wildcard include/config/arch/riscv/exten/instruction/set.h) \
  include/melis/elibrary/libc/elibs_string.h \
  include/melis/emodules/mod_mcu.h \
  include/melis/emodules/mod_cedar.h \
    $(wildcard include/config/video/lyr/ck/enable.h) \
  include/melis/misc/fb.h \
  include/melis/emodules/mod_fm.h \
  include/melis/emodules/mod_dab.h \
  include/melis/emodules/mod_aux.h \
  include/melis/emodules/mod_bp1064.h \
  include/melis/emodules/mod_bt_moudel.h \
  include/melis/emodules/SaveRds.h \
  include/melis/emodules/mod_video_play.h \
    $(wildcard include/config/para.h) \
  include/melis/emodules/../../../emodules/drv_mcu_uart/core/uart_cmd_set.h \
  include/melis/emodules/../../../emodules/drv_mcu_uart/core/sxi_defs.h \
  include/melis/emodules/../../../emodules/drv_mcu_uart/core/handlers/protocol/mcu_new_protocol_handler.h \
  include/melis/emodules/../../../emodules/drv_mcu_uart/core/handlers/protocol/../../mcu_cmd_dispatcher.h \
  include/melis/emodules/../../../emodules/drv_mcu_uart/core/handlers/protocol/../../uart_cmd_set.h \
  include/melis/emodules/../../../emodules/drv_mcu_uart/core/handlers/protocol/system/mcu_system_category_handler.h \
  include/melis/emodules/../../../emodules/drv_mcu_uart/core/handlers/protocol/audio/mcu_audio_category_handler.h \
  include/melis/emodules/../../../emodules/drv_mcu_uart/core/handlers/protocol/tuner/mcu_tuner_category_handler.h \
  include/melis/emodules/../../../emodules/drv_mcu_uart/core/handlers/protocol/tuner/../mcu_protocol_sync.h \
  include/melis/emodules/../../../emodules/drv_mcu_uart/core/handlers/protocol/usb/mcu_usb_category_handler.h \
  include/melis/emodules/../../../emodules/drv_mcu_uart/core/handlers/protocol/party/mcu_party_category_handler.h \
  include/melis/emodules/../../../emodules/drv_mcu_uart/core/handlers/protocol/settings/mcu_settings_category_handler.h \
  include/melis/emodules/mod_dab.h \
  ekernel/components/thirdparty/dfs/include/dfs_posix.h \
  ekernel/components/thirdparty/dfs/include/dfs_file.h \
  ekernel/components/thirdparty/dfs/include/dfs.h \
  /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/riscv64-unknown-elf/include/stdlib.h \
  /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/riscv64-unknown-elf/include/machine/stdlib.h \
  /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/riscv64-unknown-elf/include/alloca.h \
  /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/riscv64-unknown-elf/include/string.h \
  /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/riscv64-unknown-elf/include/xlocale.h \
  /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/riscv64-unknown-elf/include/strings.h \
  /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/riscv64-unknown-elf/include/sys/string.h \
  /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/riscv64-unknown-elf/include/time.h \
  /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/riscv64-unknown-elf/include/machine/time.h \
  /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/riscv64-unknown-elf/include/sys/stat.h \
  /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/riscv64-unknown-elf/include/fcntl.h \
  /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/riscv64-unknown-elf/include/sys/fcntl.h \
  /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/riscv64-unknown-elf/include/sys/_default_fcntl.h \
  /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/riscv64-unknown-elf/include/sys/unistd.h \
  ekernel/components/thirdparty/dfs/include/dfs_fs.h \
  include/melis/emodules/mod_bp1064.h \
  include/melis/emodules/mod_bt_moudel.h \
  emodules/fm/fm_module/si4760/Rds_sub.h \
  emodules/fm/fm_module/si4760/Rds.h \
  include/melis/emodules/mod_fm.h \
  include/melis/log.h \
    $(wildcard include/config/log/default/level.h) \
    $(wildcard include/config/log/release.h) \
    $(wildcard include/config/dynamic/log/level/support.h) \
    $(wildcard include/config/disable/all/debuglog.h) \
  include/melis/emodules/mod_mcu.h \

emodules/fm/fm_module/si4760/Rds_sub.o: $(deps_emodules/fm/fm_module/si4760/Rds_sub.o)

$(deps_emodules/fm/fm_module/si4760/Rds_sub.o):
