Archive member included to satisfy reference by file (symbol)

./elibrary/bin//libsyscall.a(syscall.o)
                              emodules/mod_mixture/mod_mixture.o (esCFG_GetGPIOSecData)
./elibrary/bin//libsyscall.a(syscall_fops.o)
                              emodules/mod_mixture/mod_mixture.o (ioctl)
./elibrary/bin//libminic.a(elibs_sprintf.o)
                              emodules/mod_mixture/mod_mixture.o (eLIBs_sprintf)
./elibrary/bin//libminic.a(elibs_stdio.o)
                              emodules/mod_mixture/mod_mixture.o (eLIBs_fopen)
./elibrary/bin//libminic.a(elibs_stdlib.o)
                              emodules/mod_mixture/mod_mixture.o (eLIBs_atoi)
./elibrary/bin//libminic.a(elibs_string.o)
                              ./elibrary/bin//libminic.a(elibs_stdio.o) (eLIBs_strlen)
./elibrary/bin//liblzma.a(az100.o)
                              emodules/mod_mixture/mod_mixture.o (AZ100_IsCompress)
./elibrary/bin//liblzma.a(compress.o)
                              ./elibrary/bin//liblzma.a(az100.o) (AZ100_DataUncompress)
./elibrary/bin//liblzma.a(LzmaLib.o)
                              ./elibrary/bin//liblzma.a(compress.o) (LzmaCompress)
./elibrary/bin//liblzma.a(LzmaDec.o)
                              ./elibrary/bin//liblzma.a(LzmaLib.o) (LzmaDecode)
./elibrary/bin//liblzma.a(LzmaEnc.o)
                              ./elibrary/bin//liblzma.a(LzmaLib.o) (LzmaEncProps_Init)
./elibrary/bin//liblzma.a(LzFind.o)
                              ./elibrary/bin//liblzma.a(LzmaEnc.o) (MatchFinder_Construct)
/home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/../../../../riscv64-unknown-elf/lib/rv64imafdcxtheadc/lp64d/libc.a(lib_a-memcpy.o)
                              emodules/mod_mixture/mod_mixture.o (memcpy)
/home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/../../../../riscv64-unknown-elf/lib/rv64imafdcxtheadc/lp64d/libc.a(lib_a-memmove-stub.o)
                              ./elibrary/bin//liblzma.a(LzFind.o) (memmove)
/home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/../../../../riscv64-unknown-elf/lib/rv64imafdcxtheadc/lp64d/libc.a(lib_a-memset.o)
                              emodules/mod_mixture/mod_mixture.o (memset)
/home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/../../../../riscv64-unknown-elf/lib/rv64imafdcxtheadc/lp64d/libc.a(lib_a-sysclose.o)
                              emodules/mod_mixture/mod_mixture.o (close)
/home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/../../../../riscv64-unknown-elf/lib/rv64imafdcxtheadc/lp64d/libc.a(lib_a-sysopen.o)
                              emodules/mod_mixture/mod_mixture.o (open)
/home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/../../../../riscv64-unknown-elf/lib/rv64imafdcxtheadc/lp64d/libc.a(lib_a-syswrite.o)
                              emodules/mod_mixture/startup_check/startup_check.o (write)
/home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/../../../../riscv64-unknown-elf/lib/rv64imafdcxtheadc/lp64d/libc.a(lib_a-impure.o)
                              /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/../../../../riscv64-unknown-elf/lib/rv64imafdcxtheadc/lp64d/libc.a(lib_a-sysclose.o) (_impure_ptr)

Discarded input sections

 .text          0x0000000000000000        0x0 emodules/mod_mixture/mod_mixture.o
 .data          0x0000000000000000        0x0 emodules/mod_mixture/mod_mixture.o
 .bss           0x0000000000000000        0x0 emodules/mod_mixture/mod_mixture.o
 .text.read_picture_index
                0x0000000000000000       0x64 emodules/mod_mixture/mod_mixture.o
 .text.ReadDataFromFile
                0x0000000000000000       0x50 emodules/mod_mixture/mod_mixture.o
 .text.close_jpg_animation
                0x0000000000000000        0x8 emodules/mod_mixture/mod_mixture.o
 .text.plogo_data_uninit
                0x0000000000000000        0x4 emodules/mod_mixture/mod_mixture.o
 .text.get_backcar_status
                0x0000000000000000        0xa emodules/mod_mixture/mod_mixture.o
 .text.get_logo_scroll_tid
                0x0000000000000000       0x10 emodules/mod_mixture/mod_mixture.o
 .text.logo_crolling_thread_uninit
                0x0000000000000000       0x4a emodules/mod_mixture/mod_mixture.o
 .bss.disp_backcar_dvr_event_status
                0x0000000000000000        0x4 emodules/mod_mixture/mod_mixture.o
 .bss.disp_backcar_fd
                0x0000000000000000        0x4 emodules/mod_mixture/mod_mixture.o
 .bss.dot_end_x
                0x0000000000000000        0x4 emodules/mod_mixture/mod_mixture.o
 .bss.dot_start_y
                0x0000000000000000        0x4 emodules/mod_mixture/mod_mixture.o
 .data.scrolling_percent
                0x0000000000000000        0x1 emodules/mod_mixture/mod_mixture.o
 .text          0x0000000000000000        0x0 emodules/mod_mixture/magic.o
 .data          0x0000000000000000        0x0 emodules/mod_mixture/magic.o
 .bss           0x0000000000000000        0x0 emodules/mod_mixture/magic.o
 .text          0x0000000000000000        0x0 emodules/mod_mixture/startup_check/startup_check.o
 .data          0x0000000000000000        0x0 emodules/mod_mixture/startup_check/startup_check.o
 .bss           0x0000000000000000        0x0 emodules/mod_mixture/startup_check/startup_check.o
 .text.amp_output_ctl
                0x0000000000000000       0x62 emodules/mod_mixture/startup_check/startup_check.o
 .text.check_if_lowpower
                0x0000000000000000      0x214 emodules/mod_mixture/startup_check/startup_check.o
 .rodata.amp_output_ctl.str1.8
                0x0000000000000000       0x13 emodules/mod_mixture/startup_check/startup_check.o
 .rodata.check_if_lowpower.str1.8
                0x0000000000000000       0x51 emodules/mod_mixture/startup_check/startup_check.o
 .debug_info    0x0000000000000000     0x1883 emodules/mod_mixture/startup_check/startup_check.o
 .debug_abbrev  0x0000000000000000      0x2f6 emodules/mod_mixture/startup_check/startup_check.o
 .debug_loc     0x0000000000000000      0x24f emodules/mod_mixture/startup_check/startup_check.o
 .debug_aranges
                0x0000000000000000       0x40 emodules/mod_mixture/startup_check/startup_check.o
 .debug_line    0x0000000000000000      0x991 emodules/mod_mixture/startup_check/startup_check.o
 .debug_str     0x0000000000000000     0x1a2a emodules/mod_mixture/startup_check/startup_check.o
 .comment       0x0000000000000000       0x33 emodules/mod_mixture/startup_check/startup_check.o
 .debug_frame   0x0000000000000000       0x80 emodules/mod_mixture/startup_check/startup_check.o
 .riscv.attributes
                0x0000000000000000       0x3d emodules/mod_mixture/startup_check/startup_check.o
 .text          0x0000000000000000        0x0 emodules/mod_mixture/bmp_parser/bmp.o
 .data          0x0000000000000000        0x0 emodules/mod_mixture/bmp_parser/bmp.o
 .bss           0x0000000000000000        0x0 emodules/mod_mixture/bmp_parser/bmp.o
 .text.open_ram_bmp
                0x0000000000000000      0x108 emodules/mod_mixture/bmp_parser/bmp.o
 .text.close_ram_bmp
                0x0000000000000000       0x42 emodules/mod_mixture/bmp_parser/bmp.o
 .text.read_ram_pixel
                0x0000000000000000       0x96 emodules/mod_mixture/bmp_parser/bmp.o
 .text.get_ram_matrix
                0x0000000000000000       0xb2 emodules/mod_mixture/bmp_parser/bmp.o
 .text.read_pixel
                0x0000000000000000       0x96 emodules/mod_mixture/bmp_parser/bmp.o
 .bss.g_ram_pbmp_buf
                0x0000000000000000        0x8 emodules/mod_mixture/bmp_parser/bmp.o
 .text          0x0000000000000000        0x0 emodules/mod_mixture/bmp_parser/Parse_Picture.o
 .data          0x0000000000000000        0x0 emodules/mod_mixture/bmp_parser/Parse_Picture.o
 .bss           0x0000000000000000        0x0 emodules/mod_mixture/bmp_parser/Parse_Picture.o
 .text.Parse_Pic_BMP_ByBuffer
                0x0000000000000000       0x40 emodules/mod_mixture/bmp_parser/Parse_Picture.o
 .text.Parse_Pic_BMP_ByRam
                0x0000000000000000       0xd0 emodules/mod_mixture/bmp_parser/Parse_Picture.o
 .text          0x0000000000000000        0x0 emodules/mod_mixture/backcarcheck/dis_bmp.o
 .data          0x0000000000000000        0x0 emodules/mod_mixture/backcarcheck/dis_bmp.o
 .bss           0x0000000000000000        0x0 emodules/mod_mixture/backcarcheck/dis_bmp.o
 .text.dis_read_pixel
                0x0000000000000000       0x9e emodules/mod_mixture/backcarcheck/dis_bmp.o
 .text          0x0000000000000000        0x0 emodules/mod_mixture/backcarcheck/dis_checkio.o
 .data          0x0000000000000000        0x0 emodules/mod_mixture/backcarcheck/dis_checkio.o
 .bss           0x0000000000000000        0x0 emodules/mod_mixture/backcarcheck/dis_checkio.o
 .text.video_set_enhance
                0x0000000000000000       0x3a emodules/mod_mixture/backcarcheck/dis_checkio.o
 .text.video_get_luma
                0x0000000000000000       0x2e emodules/mod_mixture/backcarcheck/dis_checkio.o
 .text.video_get_contrast
                0x0000000000000000       0x2e emodules/mod_mixture/backcarcheck/dis_checkio.o
 .text.video_get_saturation
                0x0000000000000000       0x2e emodules/mod_mixture/backcarcheck/dis_checkio.o
 .text.video_rotate_angle
                0x0000000000000000       0x28 emodules/mod_mixture/backcarcheck/dis_checkio.o
 .text          0x0000000000000000        0x0 emodules/mod_mixture/backcarcheck/dis_Parse_Picture.o
 .data          0x0000000000000000        0x0 emodules/mod_mixture/backcarcheck/dis_Parse_Picture.o
 .bss           0x0000000000000000        0x0 emodules/mod_mixture/backcarcheck/dis_Parse_Picture.o
 .text.dis_Parse_Pic_BMP_ByBuffer
                0x0000000000000000       0x44 emodules/mod_mixture/backcarcheck/dis_Parse_Picture.o
 .text          0x0000000000000000        0x0 emodules/mod_mixture/backcarcheck/dis_show_picture.o
 .data          0x0000000000000000        0x0 emodules/mod_mixture/backcarcheck/dis_show_picture.o
 .bss           0x0000000000000000        0x0 emodules/mod_mixture/backcarcheck/dis_show_picture.o
 .text.__show_backcar_str_pic
                0x0000000000000000      0x14c emodules/mod_mixture/backcarcheck/dis_show_picture.o
 .text.__close_backcar_str_pic
                0x0000000000000000       0x96 emodules/mod_mixture/backcarcheck/dis_show_picture.o
 .bss.backcar_str_layer
                0x0000000000000000        0x8 emodules/mod_mixture/backcarcheck/dis_show_picture.o
 .bss.backcar_str_picture
                0x0000000000000000        0x8 emodules/mod_mixture/backcarcheck/dis_show_picture.o
 .rodata.__show_backcar_str_pic.str1.8
                0x0000000000000000       0x17 emodules/mod_mixture/backcarcheck/dis_show_picture.o
 .text          0x0000000000000000        0x0 ./elibrary/bin//libsyscall.a(syscall.o)
 .data          0x0000000000000000        0x0 ./elibrary/bin//libsyscall.a(syscall.o)
 .bss           0x0000000000000000        0x0 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esCFG_Exit_Ex
                0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esCFG_GetGPIOSecData_Ex
                0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esCFG_GetGPIOSecKeyCount
                0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esCFG_GetGPIOSecKeyCount_Ex
                0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esCFG_GetKeyValue_Ex
                0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esCFG_GetSecCount
                0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esCFG_GetSecCount_Ex
                0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esCFG_GetSecKeyCount
                0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esCFG_GetSecKeyCount_Ex
                0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esCFG_Init_Ex
                0x0000000000000000       0x16 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esCHS_Char2Uni
                0x0000000000000000       0x1c ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esCHS_GetChLowerTbl
                0x0000000000000000       0x1c ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esCHS_GetChUpperTbl
                0x0000000000000000       0x1c ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esCHS_Uni2Char
                0x0000000000000000       0x1c ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esCLK_CloseMclk
                0x0000000000000000       0x1c ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esCLK_GetMclkDiv
                0x0000000000000000       0x1c ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esCLK_GetMclkSrc
                0x0000000000000000       0x1c ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esCLK_GetRound_Rate
                0x0000000000000000       0x20 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esCLK_GetSrcFreq
                0x0000000000000000       0x1c ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esCLK_MclkAssert
                0x0000000000000000       0x1c ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esCLK_MclkDeassert
                0x0000000000000000       0x1c ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esCLK_MclkGetRstStatus
                0x0000000000000000       0x1c ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esCLK_MclkOnOff
                0x0000000000000000       0x20 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esCLK_MclkRegCb
                0x0000000000000000       0x1c ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esCLK_MclkReset
                0x0000000000000000       0x1c ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esCLK_MclkUnregCb
                0x0000000000000000       0x1c ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esCLK_ModInfo
                0x0000000000000000       0x16 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esCLK_OpenMclk
                0x0000000000000000       0x1c ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esCLK_SetMclkDiv
                0x0000000000000000       0x20 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esCLK_SetMclkSrc
                0x0000000000000000       0x20 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esCLK_SetSrcFreq
                0x0000000000000000       0x20 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esCLK_SysInfo
                0x0000000000000000       0x16 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esDEV_Close
                0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esDEV_DevReg
                0x0000000000000000       0x16 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esDEV_DevUnreg
                0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esDEV_Insmod
                0x0000000000000000       0x1c ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esDEV_Ioctl
                0x0000000000000000       0x1c ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esDEV_Lock
                0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esDEV_Open
                0x0000000000000000       0x1a ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esDEV_Plugin
                0x0000000000000000       0x1c ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esDEV_Plugout
                0x0000000000000000       0x1c ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esDEV_Read
                0x0000000000000000       0x20 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esDEV_Unimod
                0x0000000000000000       0x1c ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esDEV_Unlock
                0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esDEV_Write
                0x0000000000000000       0x20 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.rt_device_register
                0x0000000000000000       0x16 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.rt_device_unregister
                0x0000000000000000       0x16 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esDMA_ChangeMode
                0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esDMA_DisableINT
                0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esDMA_EnableINT
                0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esDMA_Information
                0x0000000000000000       0x16 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esDMA_QueryDst
                0x0000000000000000       0x16 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esDMA_QueryRestCount
                0x0000000000000000       0x16 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esDMA_QuerySrc
                0x0000000000000000       0x16 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esDMA_QueryStat
                0x0000000000000000       0x1a ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esDMA_RegDmaHdler
                0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esDMA_Release
                0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esDMA_Request
                0x0000000000000000       0x16 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esDMA_Restart
                0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esDMA_Setting
                0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esDMA_Start
                0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esDMA_Stop
                0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esDMA_UnregDmaHdler
                0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esEXEC_PCreate
                0x0000000000000000       0x1e ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esEXEC_PDel
                0x0000000000000000       0x1a ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esEXEC_PDelReq
                0x0000000000000000       0x1a ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esEXEC_Run
                0x0000000000000000       0x1e ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esFSYS_clearpartupdateflag
                0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esFSYS_fd2file
                0x0000000000000000       0x16 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esFSYS_ferrclr
                0x0000000000000000       0x16 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esFSYS_ferror
                0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esFSYS_file2fd
                0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esFSYS_fioctrl
                0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esFSYS_format
                0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esFSYS_fsdbg
                0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esFSYS_fsreg
                0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esFSYS_fstat
                0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esFSYS_fsunreg
                0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esFSYS_fsync
                0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esFSYS_ftruncate
                0x0000000000000000       0x1c ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esFSYS_fwrite
                0x0000000000000000       0x20 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esFSYS_getfscharset
                0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esFSYS_mkdir
                0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esFSYS_mntfs
                0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esFSYS_mntparts
                0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esFSYS_open
                0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esFSYS_partfslck
                0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esFSYS_partfsunlck
                0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esFSYS_pclose
                0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esFSYS_pdreg
                0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esFSYS_pdunreg
                0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esFSYS_perrclr
                0x0000000000000000       0x16 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esFSYS_perror
                0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esFSYS_pioctrl
                0x0000000000000000       0x1c ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esFSYS_popen
                0x0000000000000000       0x16 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esFSYS_pread
                0x0000000000000000       0x20 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esFSYS_premove
                0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esFSYS_prename
                0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esFSYS_pwrite
                0x0000000000000000       0x20 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esFSYS_querypartupdateflag
                0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esFSYS_readdir
                0x0000000000000000       0x16 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esFSYS_remove
                0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esFSYS_rename
                0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esFSYS_rewinddir
                0x0000000000000000       0x16 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esFSYS_rmdir
                0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esFSYS_setfs
                0x0000000000000000       0x1c ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esFSYS_statfs
                0x0000000000000000       0x1c ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esFSYS_umntfs
                0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esFSYS_umntparts
                0x0000000000000000       0x1c ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esHID_hiddevreg
                0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esHID_hiddevunreg
                0x0000000000000000       0x1c ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esHID_SendMsg
                0x0000000000000000       0x1c ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esINPUT_GetLdevID
                0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esINPUT_LdevCtl
                0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esINPUT_LdevFeedback
                0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esINPUT_LdevGrab
                0x0000000000000000       0x16 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esINPUT_LdevRelease
                0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esINPUT_RegDev
                0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esINPUT_SendEvent
                0x0000000000000000       0x20 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esINPUT_UnregDev
                0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esINT_DisableINT
                0x0000000000000000       0x1c ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esINT_EnableINT
                0x0000000000000000       0x1c ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esINT_InsFIR
                0x0000000000000000       0x1c ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esINT_InsISR
                0x0000000000000000       0x1c ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esINT_SetIRQPrio
                0x0000000000000000       0x20 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esINT_UniFIR
                0x0000000000000000       0x1c ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esINT_UniISR
                0x0000000000000000       0x1c ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esKRNL_CallBack
                0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esKRNL_DumpStack
                0x0000000000000000       0x16 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esKRNL_FlagAccept
                0x0000000000000000       0x1c ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esKRNL_FlagCreate
                0x0000000000000000       0x1a ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esKRNL_FlagDel
                0x0000000000000000       0x16 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esKRNL_FlagNameGet
                0x0000000000000000       0x1a ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esKRNL_FlagNameSet
                0x0000000000000000       0x16 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esKRNL_FlagPend
                0x0000000000000000       0x20 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esKRNL_FlagPendGetFlagsRdy
                0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esKRNL_FlagPost
                0x0000000000000000       0x1c ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esKRNL_FlagQuery
                0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esKRNL_GetTIDCur
                0x0000000000000000       0x16 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esKRNL_InterruptDisable
                0x0000000000000000       0x16 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esKRNL_InterruptEnable
                0x0000000000000000       0x16 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esKRNL_MboxAccept
                0x0000000000000000       0x16 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esKRNL_MboxCreate
                0x0000000000000000       0x1a ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esKRNL_MboxDel
                0x0000000000000000       0x16 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esKRNL_MboxPend
                0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esKRNL_MboxPost
                0x0000000000000000       0x1a ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esKRNL_MboxPostOpt
                0x0000000000000000       0x1e ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esKRNL_MboxQuery
                0x0000000000000000       0x1a ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esKRNL_MemLeakChk
                0x0000000000000000       0x1a ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esKRNL_MutexCreate
                0x0000000000000000       0x16 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esKRNL_MutexDel
                0x0000000000000000       0x16 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esKRNL_MutexPend
                0x0000000000000000       0x16 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esKRNL_MutexPost
                0x0000000000000000       0x1a ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esKRNL_QAccept
                0x0000000000000000       0x16 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esKRNL_QCreate
                0x0000000000000000       0x16 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esKRNL_QDel
                0x0000000000000000       0x16 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esKRNL_QFlush
                0x0000000000000000       0x1a ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esKRNL_QPend
                0x0000000000000000       0x16 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esKRNL_QPost
                0x0000000000000000       0x1a ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esKRNL_QPostFront
                0x0000000000000000       0x1a ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esKRNL_QPostOpt
                0x0000000000000000       0x1a ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esKRNL_QQuery
                0x0000000000000000       0x1a ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esKRNL_SchedLock
                0x0000000000000000       0x16 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esKRNL_SchedUnlock
                0x0000000000000000       0x16 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esKRNL_SemAccept
                0x0000000000000000       0x1a ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esKRNL_SemDel
                0x0000000000000000       0x16 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esKRNL_SemQuery
                0x0000000000000000       0x1a ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esKRNL_SemSet
                0x0000000000000000       0x16 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esKRNL_SktAccept
                0x0000000000000000       0x16 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esKRNL_SktCreate
                0x0000000000000000       0x22 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esKRNL_SktDel
                0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esKRNL_SktPend
                0x0000000000000000       0x1a ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esKRNL_SktPost
                0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esKRNL_TaskPrefEn
                0x0000000000000000       0x1a ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esKRNL_TaskQuery
                0x0000000000000000       0x1e ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esKRNL_TaskResume
                0x0000000000000000       0x1e ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esKRNL_TaskNameSet
                0x0000000000000000       0x1a ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esKRNL_TaskSuspend
                0x0000000000000000       0x1e ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esKRNL_Time
                0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esKRNL_TimeDlyResume
                0x0000000000000000       0x1e ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esKRNL_TimeGet
                0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esKRNL_TimeSet
                0x0000000000000000       0x1a ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esKRNL_TmrCreate
                0x0000000000000000       0x1a ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esKRNL_TmrDel
                0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esKRNL_TmrError
                0x0000000000000000       0x1a ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esKRNL_TmrRemainGet
                0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esKRNL_TmrStart
                0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esKRNL_TmrStateGet
                0x0000000000000000       0x1a ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esKRNL_TmrStop
                0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esKRNL_Version
                0x0000000000000000       0x1a ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esKRNL_Ioctrl
                0x0000000000000000       0x16 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esKSRV_ClearWatchDog
                0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esKSRV_DisableWatchDog
                0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esKSRV_DisableWatchDogSrv
                0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esKSRV_EnableWatchDog
                0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esKSRV_EnableWatchDogSrv
                0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esKSRV_GetAddPara
                0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esKSRV_GetDramCfgPara
                0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esKSRV_GetHighMsg
                0x0000000000000000       0x16 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esKSRV_GetLowMsg
                0x0000000000000000       0x16 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esKSRV_GetMsg
                0x0000000000000000       0x16 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esKSRV_GetPara
                0x0000000000000000       0x1c ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esKSRV_GetSocID
                0x0000000000000000       0x1a ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esKSRV_GetTargetPara
                0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esKSRV_GetVersion
                0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esKSRV_Save_Mixture_Hld
                0x0000000000000000       0x16 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esKSRV_Get_Mixture_Hld
                0x0000000000000000       0x16 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esKSRV_Save_Raw_Decoder_Hld
                0x0000000000000000       0x16 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esKSRV_Get_Raw_Decoder_Hld
                0x0000000000000000       0x16 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esKSRV_memcpy
                0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esKSRV_memset
                0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esKSRV_PowerOff
                0x0000000000000000       0x16 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esKSRV_Random
                0x0000000000000000       0x1c ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esKSRV_Reset
                0x0000000000000000       0x16 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esKSRV_Save_Display_Hld
                0x0000000000000000       0x16 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esKSRV_SendMsgEx
                0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esKSRV_SysInfo
                0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esMEMS_Calloc
                0x0000000000000000       0x1e ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esMEMS_CleanDCache
                0x0000000000000000       0x16 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esMEMS_CleanDCacheRegion
                0x0000000000000000       0x1a ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esMEMS_CleanFlushCache
                0x0000000000000000       0x16 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esMEMS_CleanFlushCacheRegion
                0x0000000000000000       0x1a ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esMEMS_CleanFlushDCache
                0x0000000000000000       0x16 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esMEMS_CleanInvalidateCacheAll
                0x0000000000000000       0x16 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esMEMS_FlushCache
                0x0000000000000000       0x16 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esMEMS_FlushCacheRegion
                0x0000000000000000       0x1a ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esMEMS_FlushDCache
                0x0000000000000000       0x16 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esMEMS_FlushDCacheRegion
                0x0000000000000000       0x1a ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esMEMS_FlushICache
                0x0000000000000000       0x16 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esMEMS_FlushICacheRegion
                0x0000000000000000       0x1a ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esMEMS_FreeMemSize
                0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esMEMS_GetIoVAByPA
                0x0000000000000000       0x1c ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esMEMS_HeapCreate
                0x0000000000000000       0x1c ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esMEMS_HeapDel
                0x0000000000000000       0x16 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esMEMS_Info
                0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esMEMS_Realloc
                0x0000000000000000       0x1a ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esMEMS_TotalMemSize
                0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esMEMS_VA2PA
                0x0000000000000000       0x16 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esMEMS_VMalloc
                0x0000000000000000       0x16 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esMEMS_VMCreate
                0x0000000000000000       0x1c ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esMEMS_VMDelete
                0x0000000000000000       0x1a ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esMEMS_VMfree
                0x0000000000000000       0x16 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esMEM_BWDisable
                0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esMEM_BWEnable
                0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esMEM_BWGet
                0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esMEM_DramSuspend
                0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esMEM_DramWakeup
                0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esMEM_MasterGet
                0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esMEM_MasterSet
                0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esMEM_RegDramAccess
                0x0000000000000000       0x16 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esMEM_ReleaseDramUsrMode
                0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esMEM_RequestDramUsrMode
                0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esMEM_SramRelBlk
                0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esMEM_SramReqBlk
                0x0000000000000000       0x16 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esMEM_SramSwitchBlk
                0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esMEM_UnRegDramAccess
                0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esMODS_MRead
                0x0000000000000000       0x20 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esMODS_MWrite
                0x0000000000000000       0x20 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esMSTUB_GetFuncEntry
                0x0000000000000000       0x1e ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esMSTUB_RegFuncTbl
                0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esMSTUB_UnRegFuncTbl
                0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esPINS_ClearPending
                0x0000000000000000       0x16 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esPINS_DisbaleInt
                0x0000000000000000       0x16 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esPINS_EnbaleInt
                0x0000000000000000       0x16 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esPINS_GetPinGrpStat
                0x0000000000000000       0x1a ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esPINS_GetPinStat
                0x0000000000000000       0x16 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esPINS_QueryInt
                0x0000000000000000       0x16 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esPINS_RegIntHdler
                0x0000000000000000       0x16 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esPINS_SetIntMode
                0x0000000000000000       0x1a ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esPINS_SetPinDrive
                0x0000000000000000       0x1a ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esPINS_SetPinStat
                0x0000000000000000       0x16 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esPINS_UnregIntHdler
                0x0000000000000000       0x16 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esPINS_WritePinData
                0x0000000000000000       0x1a ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esPWRMAN_EnterStandby
                0x0000000000000000       0x1a ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esPWRMAN_GetStandbyPara
                0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esPWRMAN_LockCpuFreq
                0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esPWRMAN_RegDevice
                0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esPWRMAN_RelPwrmanMode
                0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esPWRMAN_ReqPwrmanMode
                0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esPWRMAN_SetStandbyMode
                0x0000000000000000       0x1c ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esPWRMAN_UnlockCpuFreq
                0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esPWRMAN_UnregDevice
                0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esPWRMAN_UsrEventNotify
                0x0000000000000000       0x16 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esRESM_RClose
                0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esRESM_ROpen
                0x0000000000000000       0x16 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esRESM_RRead
                0x0000000000000000       0x20 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esRESM_RSeek
                0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esSIOS_getchar
                0x0000000000000000       0x16 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esSIOS_gets
                0x0000000000000000       0x14 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esSIOS_putarg
                0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esSIOS_setbaud
                0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esSVC_RegCloseNode
                0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esSVC_RegClrError
                0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esSVC_RegCreateKey
                0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esSVC_RegCreatePath
                0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esSVC_RegCreateSet
                0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esSVC_RegDeleteKey
                0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esSVC_RegDeleteNode
                0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esSVC_RegDeletePath
                0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esSVC_RegDeleteSet
                0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esSVC_RegGetError
                0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esSVC_RegGetFirstKey
                0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esSVC_RegGetFirstSet
                0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esSVC_RegGetKeyCount
                0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esSVC_RegGetKeyValue
                0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esSVC_RegGetNextKey
                0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esSVC_RegGetNextSet
                0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esSVC_RegGetRootPath
                0x0000000000000000       0x16 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esSVC_RegGetSetCount
                0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esSVC_RegIni2Reg
                0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esSVC_RegOpenNode
                0x0000000000000000       0x16 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esSVC_RegReg2Ini
                0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esSVC_RegSetKeyValue
                0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esSVC_RegSetRootPath
                0x0000000000000000       0x16 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esSVC_ResourceRel
                0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esSVC_ResourceReq
                0x0000000000000000       0x22 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esTIME_ContiCntr
                0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esTIME_GetDate
                0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esTIME_GetTime
                0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esTIME_PauseCntr
                0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esTIME_QuerryAlarm
                0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esTIME_QuerryCntr
                0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esTIME_QuerryCntrStat
                0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esTIME_QuerryTimer
                0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esTIME_ReleaseAlarm
                0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esTIME_ReleaseCntr
                0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esTIME_ReleaseTimer
                0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esTIME_RequestAlarm
                0x0000000000000000       0x1a ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esTIME_RequestCntr
                0x0000000000000000       0x16 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esTIME_RequestTimer
                0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esTIME_SetCntrPrescale
                0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esTIME_SetCntrValue
                0x0000000000000000       0x1c ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esTIME_SetDate
                0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esTIME_SetTime
                0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esTIME_StartAlarm
                0x0000000000000000       0x1c ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esTIME_StartCntr
                0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esTIME_StartTimer
                0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esTIME_StopAlarm
                0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esTIME_StopCntr
                0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esTIME_StopTimer
                0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.backtrace
                0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.select   0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.mmap     0x0000000000000000       0x16 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.munmap   0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.poll     0x0000000000000000       0x1c ./elibrary/bin//libsyscall.a(syscall.o)
 .text.pipe     0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall.o)
 .text          0x0000000000000000        0x0 ./elibrary/bin//libsyscall.a(syscall_fops.o)
 .data          0x0000000000000000        0x0 ./elibrary/bin//libsyscall.a(syscall_fops.o)
 .bss           0x0000000000000000        0x0 ./elibrary/bin//libsyscall.a(syscall_fops.o)
 .text.dup      0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall_fops.o)
 .text.dup2     0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall_fops.o)
 .text.closedir
                0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall_fops.o)
 .text.fioctrl  0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall_fops.o)
 .text._fstat_r
                0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall_fops.o)
 .text._lseek_r
                0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall_fops.o)
 .text._rename_r
                0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall_fops.o)
 .text._stat_r  0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall_fops.o)
 .text._mkdir_r
                0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall_fops.o)
 .text.fsync    0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall_fops.o)
 .text._unlink_r
                0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall_fops.o)
 .text._read_r  0x0000000000000000       0x16 ./elibrary/bin//libsyscall.a(syscall_fops.o)
 .text._write_r
                0x0000000000000000       0x16 ./elibrary/bin//libsyscall.a(syscall_fops.o)
 .text._gettimeofday_r
                0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall_fops.o)
 .text.settimeofday
                0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall_fops.o)
 .text._malloc_r
                0x0000000000000000       0x16 ./elibrary/bin//libsyscall.a(syscall_fops.o)
 .text._realloc_r
                0x0000000000000000       0x16 ./elibrary/bin//libsyscall.a(syscall_fops.o)
 .text._calloc_r
                0x0000000000000000       0x16 ./elibrary/bin//libsyscall.a(syscall_fops.o)
 .text._free_r  0x0000000000000000       0x16 ./elibrary/bin//libsyscall.a(syscall_fops.o)
 .text._isatty_r
                0x0000000000000000       0x16 ./elibrary/bin//libsyscall.a(syscall_fops.o)
 .text._isatty  0x0000000000000000       0x16 ./elibrary/bin//libsyscall.a(syscall_fops.o)
 .text.__libc_init_array
                0x0000000000000000       0x16 ./elibrary/bin//libsyscall.a(syscall_fops.o)
 .text._system  0x0000000000000000       0x16 ./elibrary/bin//libsyscall.a(syscall_fops.o)
 .text.__malloc_lock
                0x0000000000000000        0x2 ./elibrary/bin//libsyscall.a(syscall_fops.o)
 .text.__malloc_unlock
                0x0000000000000000        0x2 ./elibrary/bin//libsyscall.a(syscall_fops.o)
 .text.ftruncate
                0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall_fops.o)
 .text.mkdir    0x0000000000000000       0x1c ./elibrary/bin//libsyscall.a(syscall_fops.o)
 .text.opendir  0x0000000000000000       0x16 ./elibrary/bin//libsyscall.a(syscall_fops.o)
 .text.readdir  0x0000000000000000       0x16 ./elibrary/bin//libsyscall.a(syscall_fops.o)
 .text.remove   0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall_fops.o)
 .text.rewinddir
                0x0000000000000000       0x16 ./elibrary/bin//libsyscall.a(syscall_fops.o)
 .text.rmdir    0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall_fops.o)
 .text.statfs   0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall_fops.o)
 .text.access   0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall_fops.o)
 .text          0x0000000000000000        0x0 ./elibrary/bin//libminic.a(elibs_sprintf.o)
 .data          0x0000000000000000        0x0 ./elibrary/bin//libminic.a(elibs_sprintf.o)
 .bss           0x0000000000000000        0x0 ./elibrary/bin//libminic.a(elibs_sprintf.o)
 .text.eLIBs_snprintf
                0x0000000000000000       0x20 ./elibrary/bin//libminic.a(elibs_sprintf.o)
 .text.eLIBs_scnprintf
                0x0000000000000000       0x2e ./elibrary/bin//libminic.a(elibs_sprintf.o)
 .text.eLIBs_vsprintf
                0x0000000000000000        0xe ./elibrary/bin//libminic.a(elibs_sprintf.o)
 .text          0x0000000000000000        0x0 ./elibrary/bin//libminic.a(elibs_stdio.o)
 .data          0x0000000000000000        0x0 ./elibrary/bin//libminic.a(elibs_stdio.o)
 .bss           0x0000000000000000        0x0 ./elibrary/bin//libminic.a(elibs_stdio.o)
 .text.eLIBs_remove
                0x0000000000000000        0x8 ./elibrary/bin//libminic.a(elibs_stdio.o)
 .text.eLIBs_rename
                0x0000000000000000        0x8 ./elibrary/bin//libminic.a(elibs_stdio.o)
 .text.eLIBs_fsync
                0x0000000000000000        0x8 ./elibrary/bin//libminic.a(elibs_stdio.o)
 .text.eLIBs_fwrite
                0x0000000000000000        0x8 ./elibrary/bin//libminic.a(elibs_stdio.o)
 .text.eLIBs_ftruncate
                0x0000000000000000        0x8 ./elibrary/bin//libminic.a(elibs_stdio.o)
 .text.eLIBs_fioctrl
                0x0000000000000000        0x8 ./elibrary/bin//libminic.a(elibs_stdio.o)
 .text.eLIBs_ferror
                0x0000000000000000        0x8 ./elibrary/bin//libminic.a(elibs_stdio.o)
 .text.eLIBs_ferrclr
                0x0000000000000000        0x8 ./elibrary/bin//libminic.a(elibs_stdio.o)
 .text.eLIBs_opendir
                0x0000000000000000        0x8 ./elibrary/bin//libminic.a(elibs_stdio.o)
 .text.eLIBs_readdir
                0x0000000000000000        0x8 ./elibrary/bin//libminic.a(elibs_stdio.o)
 .text.eLIBs_rewinddir
                0x0000000000000000        0x8 ./elibrary/bin//libminic.a(elibs_stdio.o)
 .text.eLIBs_mkdir
                0x0000000000000000        0x8 ./elibrary/bin//libminic.a(elibs_stdio.o)
 .text.eLIBs_rmdir
                0x0000000000000000        0x8 ./elibrary/bin//libminic.a(elibs_stdio.o)
 .text.eLIBs_DirEnt2Attr
                0x0000000000000000        0x8 ./elibrary/bin//libminic.a(elibs_stdio.o)
 .text.eLIBs_DirEnt2Name
                0x0000000000000000       0x2e ./elibrary/bin//libminic.a(elibs_stdio.o)
 .text.eLIBs_DirEnt2Size
                0x0000000000000000        0x6 ./elibrary/bin//libminic.a(elibs_stdio.o)
 .text.eLIBs_DirEnt2Time
                0x0000000000000000        0x4 ./elibrary/bin//libminic.a(elibs_stdio.o)
 .text.eLIBs_GetNumFiles
                0x0000000000000000       0x3e ./elibrary/bin//libminic.a(elibs_stdio.o)
 .text.eLIBs_GetNumSubItems
                0x0000000000000000       0x4e ./elibrary/bin//libminic.a(elibs_stdio.o)
 .text.eLIBs_GetFileAttributes
                0x0000000000000000       0x4c ./elibrary/bin//libminic.a(elibs_stdio.o)
 .text.eLIBs_SetFileAttributes
                0x0000000000000000        0x4 ./elibrary/bin//libminic.a(elibs_stdio.o)
 .text.eLIBs_GetDirSize
                0x0000000000000000      0x108 ./elibrary/bin//libminic.a(elibs_stdio.o)
 .text.eLIBs_GetFileSize
                0x0000000000000000       0x40 ./elibrary/bin//libminic.a(elibs_stdio.o)
 .text.eLIBs_format
                0x0000000000000000        0x8 ./elibrary/bin//libminic.a(elibs_stdio.o)
 .text.eLIBs_GetVolName
                0x0000000000000000       0x80 ./elibrary/bin//libminic.a(elibs_stdio.o)
 .text.eLIBs_GetVolNameLen
                0x0000000000000000       0x50 ./elibrary/bin//libminic.a(elibs_stdio.o)
 .text.eLIBs_GetVolNameCharset
                0x0000000000000000       0x52 ./elibrary/bin//libminic.a(elibs_stdio.o)
 .text.eLIBs_GetVolTSpace
                0x0000000000000000       0x30 ./elibrary/bin//libminic.a(elibs_stdio.o)
 .text.eLIBs_GetVolFSpace
                0x0000000000000000       0x30 ./elibrary/bin//libminic.a(elibs_stdio.o)
 .text.eLIBs_GetVolClustSize
                0x0000000000000000       0x26 ./elibrary/bin//libminic.a(elibs_stdio.o)
 .text.eLIBs_GetFSName
                0x0000000000000000       0x80 ./elibrary/bin//libminic.a(elibs_stdio.o)
 .text.eLIBs_GetFSNameLen
                0x0000000000000000       0x50 ./elibrary/bin//libminic.a(elibs_stdio.o)
 .text.eLIBs_GetFSAttribute
                0x0000000000000000       0x36 ./elibrary/bin//libminic.a(elibs_stdio.o)
 .text.eLIBs_GetPTName
                0x0000000000000000       0x62 ./elibrary/bin//libminic.a(elibs_stdio.o)
 .text.eLIBs_disklock
                0x0000000000000000        0x8 ./elibrary/bin//libminic.a(elibs_stdio.o)
 .text.eLIBs_diskunlock
                0x0000000000000000        0x8 ./elibrary/bin//libminic.a(elibs_stdio.o)
 .text.eLIBs_GetDiskArray
                0x0000000000000000       0x62 ./elibrary/bin//libminic.a(elibs_stdio.o)
 .text.eLIBs_feof
                0x0000000000000000       0x1e ./elibrary/bin//libminic.a(elibs_stdio.o)
 .text.eLIBs_fgetc
                0x0000000000000000       0x2c ./elibrary/bin//libminic.a(elibs_stdio.o)
 .text.eLIBs_fgets
                0x0000000000000000       0x6c ./elibrary/bin//libminic.a(elibs_stdio.o)
 .text.eLIBs_fputc
                0x0000000000000000       0x32 ./elibrary/bin//libminic.a(elibs_stdio.o)
 .text.eLIBs_fputs
                0x0000000000000000       0x42 ./elibrary/bin//libminic.a(elibs_stdio.o)
 .text.eLIBs_GetFileATime
                0x0000000000000000       0x50 ./elibrary/bin//libminic.a(elibs_stdio.o)
 .text.eLIBs_GetFileMTime
                0x0000000000000000       0x50 ./elibrary/bin//libminic.a(elibs_stdio.o)
 .text.eLIBs_GetFileCTime
                0x0000000000000000       0x50 ./elibrary/bin//libminic.a(elibs_stdio.o)
 .text.eLIBs_GetDirATime
                0x0000000000000000       0x48 ./elibrary/bin//libminic.a(elibs_stdio.o)
 .text.eLIBs_GetDirMTime
                0x0000000000000000       0x48 ./elibrary/bin//libminic.a(elibs_stdio.o)
 .text.eLIBs_GetDirCTime
                0x0000000000000000       0x48 ./elibrary/bin//libminic.a(elibs_stdio.o)
 .text.eLIBs_GetFSCharset
                0x0000000000000000       0x10 ./elibrary/bin//libminic.a(elibs_stdio.o)
 .text.eLIBs_file2fd
                0x0000000000000000        0x8 ./elibrary/bin//libminic.a(elibs_stdio.o)
 .text.eLIBs_fd2file
                0x0000000000000000        0x8 ./elibrary/bin//libminic.a(elibs_stdio.o)
 .text.eLIBs_open
                0x0000000000000000        0x8 ./elibrary/bin//libminic.a(elibs_stdio.o)
 .text.eLIBs_fstat
                0x0000000000000000        0x8 ./elibrary/bin//libminic.a(elibs_stdio.o)
 .text.eLIBs_statfs
                0x0000000000000000        0xc ./elibrary/bin//libminic.a(elibs_stdio.o)
 .text.eLIBs_GetDevName
                0x0000000000000000       0x6a ./elibrary/bin//libminic.a(elibs_stdio.o)
 .text.eLIBs_GetDevParts
                0x0000000000000000       0x88 ./elibrary/bin//libminic.a(elibs_stdio.o)
 .text.eLIBs_GetTime
                0x0000000000000000        0x8 ./elibrary/bin//libminic.a(elibs_stdio.o)
 .text.eLIBs_GetDate
                0x0000000000000000        0x8 ./elibrary/bin//libminic.a(elibs_stdio.o)
 .text.eLIBs_setfs
                0x0000000000000000       0x14 ./elibrary/bin//libminic.a(elibs_stdio.o)
 .bss.dir_level.6470
                0x0000000000000000        0x4 ./elibrary/bin//libminic.a(elibs_stdio.o)
 .rodata.eLIBs_GetDirSize.str1.8
                0x0000000000000000        0x2 ./elibrary/bin//libminic.a(elibs_stdio.o)
 .rodata.eLIBs_GetFileAttributes.str1.8
                0x0000000000000000        0x3 ./elibrary/bin//libminic.a(elibs_stdio.o)
 .rodata.eLIBs_GetVolNameCharset.str1.8
                0x0000000000000000        0x5 ./elibrary/bin//libminic.a(elibs_stdio.o)
 .text          0x0000000000000000        0x0 ./elibrary/bin//libminic.a(elibs_stdlib.o)
 .data          0x0000000000000000        0x0 ./elibrary/bin//libminic.a(elibs_stdlib.o)
 .bss           0x0000000000000000        0x0 ./elibrary/bin//libminic.a(elibs_stdlib.o)
 .text.eLIBs_int2str_dec
                0x0000000000000000        0xc ./elibrary/bin//libminic.a(elibs_stdlib.o)
 .text.eLIBs_uint2str_dec
                0x0000000000000000        0xc ./elibrary/bin//libminic.a(elibs_stdlib.o)
 .text.eLIBs_int2str_hex
                0x0000000000000000        0xc ./elibrary/bin//libminic.a(elibs_stdlib.o)
 .text.eLIBs_atoi
                0x0000000000000000        0xc ./elibrary/bin//libminic.a(elibs_stdlib.o)
 .text.eLIBs_toupper
                0x0000000000000000        0xc ./elibrary/bin//libminic.a(elibs_stdlib.o)
 .text.eLIBs_isspace
                0x0000000000000000        0xc ./elibrary/bin//libminic.a(elibs_stdlib.o)
 .text.eLIBs_strtol
                0x0000000000000000        0xc ./elibrary/bin//libminic.a(elibs_stdlib.o)
 .text.eLIBs_ramdom
                0x0000000000000000        0x8 ./elibrary/bin//libminic.a(elibs_stdlib.o)
 .text.eLIBs_realloc
                0x0000000000000000       0x10 ./elibrary/bin//libminic.a(elibs_stdlib.o)
 .text.eLIBs_atof
                0x0000000000000000       0x18 ./elibrary/bin//libminic.a(elibs_stdlib.o)
 .text.eLIBs_sscanf
                0x0000000000000000       0x24 ./elibrary/bin//libminic.a(elibs_stdlib.o)
 .text._exit    0x0000000000000000        0x2 ./elibrary/bin//libminic.a(elibs_stdlib.o)
 .text._write   0x0000000000000000        0x2 ./elibrary/bin//libminic.a(elibs_stdlib.o)
 .text._kill    0x0000000000000000        0x2 ./elibrary/bin//libminic.a(elibs_stdlib.o)
 .text._getpid  0x0000000000000000        0x2 ./elibrary/bin//libminic.a(elibs_stdlib.o)
 .text._sbrk    0x0000000000000000        0x2 ./elibrary/bin//libminic.a(elibs_stdlib.o)
 .text          0x0000000000000000        0x0 ./elibrary/bin//libminic.a(elibs_string.o)
 .data          0x0000000000000000        0x0 ./elibrary/bin//libminic.a(elibs_string.o)
 .bss           0x0000000000000000        0x0 ./elibrary/bin//libminic.a(elibs_string.o)
 .text.eLIBs_strlen
                0x0000000000000000        0xc ./elibrary/bin//libminic.a(elibs_string.o)
 .text.eLIBs_strnlen
                0x0000000000000000        0xc ./elibrary/bin//libminic.a(elibs_string.o)
 .text.eLIBs_strcat
                0x0000000000000000        0xc ./elibrary/bin//libminic.a(elibs_string.o)
 .text.eLIBs_strncat
                0x0000000000000000        0xc ./elibrary/bin//libminic.a(elibs_string.o)
 .text.eLIBs_strcmp
                0x0000000000000000        0xc ./elibrary/bin//libminic.a(elibs_string.o)
 .text.eLIBs_stricmp
                0x0000000000000000        0xc ./elibrary/bin//libminic.a(elibs_string.o)
 .text.eLIBs_strnicmp
                0x0000000000000000        0xc ./elibrary/bin//libminic.a(elibs_string.o)
 .text.eLIBs_strchr
                0x0000000000000000        0xc ./elibrary/bin//libminic.a(elibs_string.o)
 .text.eLIBs_strnchr
                0x0000000000000000        0xc ./elibrary/bin//libminic.a(elibs_string.o)
 .text.eLIBs_strchrlast
                0x0000000000000000        0xc ./elibrary/bin//libminic.a(elibs_string.o)
 .text.eLIBs_strstr
                0x0000000000000000        0xc ./elibrary/bin//libminic.a(elibs_string.o)
 .text.eLIBs_memclr
                0x0000000000000000       0x10 ./elibrary/bin//libminic.a(elibs_string.o)
 .text.eLIBs_memcmp
                0x0000000000000000       0x18 ./elibrary/bin//libminic.a(elibs_string.o)
 .text.eLIBs_memmove
                0x0000000000000000       0x3c ./elibrary/bin//libminic.a(elibs_string.o)
 .text          0x0000000000000000        0x0 ./elibrary/bin//liblzma.a(az100.o)
 .data          0x0000000000000000        0x0 ./elibrary/bin//liblzma.a(az100.o)
 .bss           0x0000000000000000        0x0 ./elibrary/bin//liblzma.a(az100.o)
 .text          0x0000000000000000        0x0 ./elibrary/bin//liblzma.a(compress.o)
 .data          0x0000000000000000        0x0 ./elibrary/bin//liblzma.a(compress.o)
 .bss           0x0000000000000000        0x0 ./elibrary/bin//liblzma.a(compress.o)
 .text.AZ100_Compress
                0x0000000000000000       0x2e ./elibrary/bin//liblzma.a(compress.o)
 .text          0x0000000000000000        0x0 ./elibrary/bin//liblzma.a(LzmaLib.o)
 .data          0x0000000000000000        0x0 ./elibrary/bin//liblzma.a(LzmaLib.o)
 .bss           0x0000000000000000        0x0 ./elibrary/bin//liblzma.a(LzmaLib.o)
 .text.LzmaCompress
                0x0000000000000000       0x68 ./elibrary/bin//liblzma.a(LzmaLib.o)
 .text          0x0000000000000000        0x0 ./elibrary/bin//liblzma.a(LzmaDec.o)
 .data          0x0000000000000000        0x0 ./elibrary/bin//liblzma.a(LzmaDec.o)
 .bss           0x0000000000000000        0x0 ./elibrary/bin//liblzma.a(LzmaDec.o)
 .text.LzmaDec_InitDicAndState
                0x0000000000000000       0x1e ./elibrary/bin//liblzma.a(LzmaDec.o)
 .text.LzmaDec_DecodeToBuf
                0x0000000000000000       0xd6 ./elibrary/bin//liblzma.a(LzmaDec.o)
 .text.LzmaDec_Free
                0x0000000000000000       0x2a ./elibrary/bin//liblzma.a(LzmaDec.o)
 .text.LzmaDec_Allocate
                0x0000000000000000       0x80 ./elibrary/bin//liblzma.a(LzmaDec.o)
 .text          0x0000000000000000        0x0 ./elibrary/bin//liblzma.a(LzmaEnc.o)
 .data          0x0000000000000000        0x0 ./elibrary/bin//liblzma.a(LzmaEnc.o)
 .bss           0x0000000000000000        0x0 ./elibrary/bin//liblzma.a(LzmaEnc.o)
 .text.LitEnc_GetPrice
                0x0000000000000000       0x34 ./elibrary/bin//liblzma.a(LzmaEnc.o)
 .text.LitEnc_GetPriceMatched
                0x0000000000000000       0x58 ./elibrary/bin//liblzma.a(LzmaEnc.o)
 .text.RcTree_GetPrice
                0x0000000000000000       0x3a ./elibrary/bin//liblzma.a(LzmaEnc.o)
 .text.RcTree_ReverseGetPrice
                0x0000000000000000       0x3a ./elibrary/bin//liblzma.a(LzmaEnc.o)
 .text.LenEnc_Init
                0x0000000000000000       0x40 ./elibrary/bin//liblzma.a(LzmaEnc.o)
 .text.LenEnc_SetPrices
                0x0000000000000000      0x104 ./elibrary/bin//liblzma.a(LzmaEnc.o)
 .text.ReadMatchDistances
                0x0000000000000000       0xd6 ./elibrary/bin//liblzma.a(LzmaEnc.o)
 .text.FillAlignPrices
                0x0000000000000000       0x68 ./elibrary/bin//liblzma.a(LzmaEnc.o)
 .text.FillDistancesPrices
                0x0000000000000000      0x176 ./elibrary/bin//liblzma.a(LzmaEnc.o)
 .text.MyWrite  0x0000000000000000       0x3c ./elibrary/bin//liblzma.a(LzmaEnc.o)
 .text.RangeEnc_FlushStream.part.1
                0x0000000000000000       0x34 ./elibrary/bin//liblzma.a(LzmaEnc.o)
 .text.RangeEnc_ShiftLow
                0x0000000000000000       0x70 ./elibrary/bin//liblzma.a(LzmaEnc.o)
 .text.RangeEnc_EncodeDirectBits
                0x0000000000000000       0x56 ./elibrary/bin//liblzma.a(LzmaEnc.o)
 .text.RangeEnc_EncodeBit
                0x0000000000000000       0x56 ./elibrary/bin//liblzma.a(LzmaEnc.o)
 .text.LitEnc_Encode
                0x0000000000000000       0x42 ./elibrary/bin//liblzma.a(LzmaEnc.o)
 .text.RcTree_Encode
                0x0000000000000000       0x56 ./elibrary/bin//liblzma.a(LzmaEnc.o)
 .text.RcTree_ReverseEncode
                0x0000000000000000       0x5c ./elibrary/bin//liblzma.a(LzmaEnc.o)
 .text.LenEnc_Encode
                0x0000000000000000       0x9e ./elibrary/bin//liblzma.a(LzmaEnc.o)
 .text.MovePos  0x0000000000000000       0x1c ./elibrary/bin//liblzma.a(LzmaEnc.o)
 .text.GetPureRepPrice
                0x0000000000000000       0xaa ./elibrary/bin//liblzma.a(LzmaEnc.o)
 .text.CheckErrors
                0x0000000000000000       0x40 ./elibrary/bin//liblzma.a(LzmaEnc.o)
 .text.Flush    0x0000000000000000      0x1fe ./elibrary/bin//liblzma.a(LzmaEnc.o)
 .text.LzmaEnc_CodeOneBlock
                0x0000000000000000     0x1bb2 ./elibrary/bin//liblzma.a(LzmaEnc.o)
 .text.LzmaEnc_Encode2
                0x0000000000000000       0x5e ./elibrary/bin//liblzma.a(LzmaEnc.o)
 .text.LzmaEncProps_Init
                0x0000000000000000       0x18 ./elibrary/bin//liblzma.a(LzmaEnc.o)
 .text.LzmaEncProps_Normalize
                0x0000000000000000       0xb2 ./elibrary/bin//liblzma.a(LzmaEnc.o)
 .text.LzmaEncProps_GetDictSize
                0x0000000000000000       0x26 ./elibrary/bin//liblzma.a(LzmaEnc.o)
 .text.LzmaEnc_FastPosInit
                0x0000000000000000       0x34 ./elibrary/bin//liblzma.a(LzmaEnc.o)
 .text.LzmaEnc_SaveState
                0x0000000000000000      0x1f2 ./elibrary/bin//liblzma.a(LzmaEnc.o)
 .text.LzmaEnc_RestoreState
                0x0000000000000000      0x1f0 ./elibrary/bin//liblzma.a(LzmaEnc.o)
 .text.LzmaEnc_SetProps
                0x0000000000000000       0xd2 ./elibrary/bin//liblzma.a(LzmaEnc.o)
 .text.LzmaEnc_InitPriceTables
                0x0000000000000000       0x40 ./elibrary/bin//liblzma.a(LzmaEnc.o)
 .text.LzmaEnc_Construct
                0x0000000000000000       0x76 ./elibrary/bin//liblzma.a(LzmaEnc.o)
 .text.LzmaEnc_Create
                0x0000000000000000       0x28 ./elibrary/bin//liblzma.a(LzmaEnc.o)
 .text.LzmaEnc_FreeLits
                0x0000000000000000       0x44 ./elibrary/bin//liblzma.a(LzmaEnc.o)
 .text.LzmaEnc_Destruct
                0x0000000000000000       0x44 ./elibrary/bin//liblzma.a(LzmaEnc.o)
 .text.LzmaEnc_Destroy
                0x0000000000000000       0x26 ./elibrary/bin//liblzma.a(LzmaEnc.o)
 .text.LzmaEnc_Init
                0x0000000000000000      0x17e ./elibrary/bin//liblzma.a(LzmaEnc.o)
 .text.LzmaEnc_InitPrices
                0x0000000000000000      0x130 ./elibrary/bin//liblzma.a(LzmaEnc.o)
 .text.LzmaEnc_AllocAndInit
                0x0000000000000000      0x17c ./elibrary/bin//liblzma.a(LzmaEnc.o)
 .text.LzmaEnc_PrepareForLzma2
                0x0000000000000000       0x20 ./elibrary/bin//liblzma.a(LzmaEnc.o)
 .text.LzmaEnc_MemPrepare
                0x0000000000000000       0x26 ./elibrary/bin//liblzma.a(LzmaEnc.o)
 .text.LzmaEnc_Finish
                0x0000000000000000        0x2 ./elibrary/bin//liblzma.a(LzmaEnc.o)
 .text.LzmaEnc_GetNumAvailableBytes
                0x0000000000000000        0x8 ./elibrary/bin//liblzma.a(LzmaEnc.o)
 .text.LzmaEnc_GetCurBuf
                0x0000000000000000       0x22 ./elibrary/bin//liblzma.a(LzmaEnc.o)
 .text.LzmaEnc_CodeOneMemBlock
                0x0000000000000000       0xc4 ./elibrary/bin//liblzma.a(LzmaEnc.o)
 .text.LzmaEnc_Encode
                0x0000000000000000       0x4c ./elibrary/bin//liblzma.a(LzmaEnc.o)
 .text.LzmaEnc_WriteProperties
                0x0000000000000000       0x66 ./elibrary/bin//liblzma.a(LzmaEnc.o)
 .text.LzmaEnc_MemEncode
                0x0000000000000000       0x74 ./elibrary/bin//liblzma.a(LzmaEnc.o)
 .text.LzmaEncode
                0x0000000000000000       0xaa ./elibrary/bin//liblzma.a(LzmaEnc.o)
 .rodata.kLiteralNextStates
                0x0000000000000000       0x30 ./elibrary/bin//liblzma.a(LzmaEnc.o)
 .rodata.kMatchNextStates
                0x0000000000000000       0x30 ./elibrary/bin//liblzma.a(LzmaEnc.o)
 .rodata.kRepNextStates
                0x0000000000000000       0x30 ./elibrary/bin//liblzma.a(LzmaEnc.o)
 .rodata.kShortRepNextStates
                0x0000000000000000       0x30 ./elibrary/bin//liblzma.a(LzmaEnc.o)
 .debug_info    0x0000000000000000     0x4ec2 ./elibrary/bin//liblzma.a(LzmaEnc.o)
 .debug_abbrev  0x0000000000000000      0x4dc ./elibrary/bin//liblzma.a(LzmaEnc.o)
 .debug_loc     0x0000000000000000     0x537e ./elibrary/bin//liblzma.a(LzmaEnc.o)
 .debug_aranges
                0x0000000000000000      0x340 ./elibrary/bin//liblzma.a(LzmaEnc.o)
 .debug_line    0x0000000000000000     0x720b ./elibrary/bin//liblzma.a(LzmaEnc.o)
 .debug_str     0x0000000000000000     0x1554 ./elibrary/bin//liblzma.a(LzmaEnc.o)
 .comment       0x0000000000000000       0x33 ./elibrary/bin//liblzma.a(LzmaEnc.o)
 .debug_frame   0x0000000000000000      0x908 ./elibrary/bin//liblzma.a(LzmaEnc.o)
 .riscv.attributes
                0x0000000000000000       0x3d ./elibrary/bin//liblzma.a(LzmaEnc.o)
 .text          0x0000000000000000        0x0 ./elibrary/bin//liblzma.a(LzFind.o)
 .data          0x0000000000000000        0x0 ./elibrary/bin//liblzma.a(LzFind.o)
 .bss           0x0000000000000000        0x0 ./elibrary/bin//liblzma.a(LzFind.o)
 .text.MatchFinder_GetPointerToCurrentPos
                0x0000000000000000        0x4 ./elibrary/bin//liblzma.a(LzFind.o)
 .text.MatchFinder_GetIndexByte
                0x0000000000000000        0x8 ./elibrary/bin//liblzma.a(LzFind.o)
 .text.MatchFinder_GetNumAvailableBytes
                0x0000000000000000        0xa ./elibrary/bin//liblzma.a(LzFind.o)
 .text.MatchFinder_SetLimits
                0x0000000000000000       0x4c ./elibrary/bin//liblzma.a(LzFind.o)
 .text.Hc_GetMatchesSpec
                0x0000000000000000       0x8e ./elibrary/bin//liblzma.a(LzFind.o)
 .text.SkipMatchesSpec
                0x0000000000000000       0xd0 ./elibrary/bin//liblzma.a(LzFind.o)
 .text.MatchFinder_ReadBlock
                0x0000000000000000       0x86 ./elibrary/bin//liblzma.a(LzFind.o)
 .text.MatchFinder_Init
                0x0000000000000000       0x4c ./elibrary/bin//liblzma.a(LzFind.o)
 .text.MatchFinder_ReduceOffsets
                0x0000000000000000       0x14 ./elibrary/bin//liblzma.a(LzFind.o)
 .text.MatchFinder_MoveBlock
                0x0000000000000000       0x3c ./elibrary/bin//liblzma.a(LzFind.o)
 .text.MatchFinder_NeedMove
                0x0000000000000000       0x24 ./elibrary/bin//liblzma.a(LzFind.o)
 .text.MatchFinder_ReadIfRequired
                0x0000000000000000       0x1c ./elibrary/bin//liblzma.a(LzFind.o)
 .text.MatchFinder_Construct
                0x0000000000000000       0x50 ./elibrary/bin//liblzma.a(LzFind.o)
 .text.MatchFinder_Free
                0x0000000000000000       0x32 ./elibrary/bin//liblzma.a(LzFind.o)
 .text.MatchFinder_Create
                0x0000000000000000      0x184 ./elibrary/bin//liblzma.a(LzFind.o)
 .text.MatchFinder_Normalize3
                0x0000000000000000       0x22 ./elibrary/bin//liblzma.a(LzFind.o)
 .text.MatchFinder_CheckLimits
                0x0000000000000000       0x90 ./elibrary/bin//liblzma.a(LzFind.o)
 .text.MatchFinder_MovePos
                0x0000000000000000       0x26 ./elibrary/bin//liblzma.a(LzFind.o)
 .text.Bt4_MatchFinder_Skip
                0x0000000000000000       0xd6 ./elibrary/bin//liblzma.a(LzFind.o)
 .text.Bt3_MatchFinder_Skip
                0x0000000000000000       0xaa ./elibrary/bin//liblzma.a(LzFind.o)
 .text.Bt2_MatchFinder_Skip
                0x0000000000000000       0x7e ./elibrary/bin//liblzma.a(LzFind.o)
 .text.Hc4_MatchFinder_Skip
                0x0000000000000000       0xc6 ./elibrary/bin//liblzma.a(LzFind.o)
 .text.Hc4_MatchFinder_GetMatches
                0x0000000000000000      0x1a2 ./elibrary/bin//liblzma.a(LzFind.o)
 .text.GetMatchesSpec1
                0x0000000000000000      0x126 ./elibrary/bin//liblzma.a(LzFind.o)
 .text.Bt4_MatchFinder_GetMatches
                0x0000000000000000      0x1b2 ./elibrary/bin//liblzma.a(LzFind.o)
 .text.Bt3_MatchFinder_GetMatches
                0x0000000000000000      0x13c ./elibrary/bin//liblzma.a(LzFind.o)
 .text.Bt2_MatchFinder_GetMatches
                0x0000000000000000       0x88 ./elibrary/bin//liblzma.a(LzFind.o)
 .text.Bt3Zip_MatchFinder_GetMatches
                0x0000000000000000       0xa0 ./elibrary/bin//liblzma.a(LzFind.o)
 .text.Hc3Zip_MatchFinder_GetMatches
                0x0000000000000000       0xa0 ./elibrary/bin//liblzma.a(LzFind.o)
 .text.Bt3Zip_MatchFinder_Skip
                0x0000000000000000       0x96 ./elibrary/bin//liblzma.a(LzFind.o)
 .text.Hc3Zip_MatchFinder_Skip
                0x0000000000000000       0x8a ./elibrary/bin//liblzma.a(LzFind.o)
 .text.MatchFinder_CreateVTable
                0x0000000000000000       0x8c ./elibrary/bin//liblzma.a(LzFind.o)
 .debug_info    0x0000000000000000     0x23cf ./elibrary/bin//liblzma.a(LzFind.o)
 .debug_abbrev  0x0000000000000000      0x4db ./elibrary/bin//liblzma.a(LzFind.o)
 .debug_loc     0x0000000000000000     0x2494 ./elibrary/bin//liblzma.a(LzFind.o)
 .debug_aranges
                0x0000000000000000      0x220 ./elibrary/bin//liblzma.a(LzFind.o)
 .debug_line    0x0000000000000000     0x2b56 ./elibrary/bin//liblzma.a(LzFind.o)
 .debug_str     0x0000000000000000      0xe64 ./elibrary/bin//liblzma.a(LzFind.o)
 .comment       0x0000000000000000       0x33 ./elibrary/bin//liblzma.a(LzFind.o)
 .debug_frame   0x0000000000000000      0x550 ./elibrary/bin//liblzma.a(LzFind.o)
 .riscv.attributes
                0x0000000000000000       0x3d ./elibrary/bin//liblzma.a(LzFind.o)
 .data          0x0000000000000000        0x0 /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/../../../../riscv64-unknown-elf/lib/rv64imafdcxtheadc/lp64d/libc.a(lib_a-memcpy.o)
 .bss           0x0000000000000000        0x0 /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/../../../../riscv64-unknown-elf/lib/rv64imafdcxtheadc/lp64d/libc.a(lib_a-memcpy.o)
 .text          0x0000000000000000       0xd2 /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/../../../../riscv64-unknown-elf/lib/rv64imafdcxtheadc/lp64d/libc.a(lib_a-memmove-stub.o)
 .data          0x0000000000000000        0x0 /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/../../../../riscv64-unknown-elf/lib/rv64imafdcxtheadc/lp64d/libc.a(lib_a-memmove-stub.o)
 .bss           0x0000000000000000        0x0 /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/../../../../riscv64-unknown-elf/lib/rv64imafdcxtheadc/lp64d/libc.a(lib_a-memmove-stub.o)
 .comment       0x0000000000000000       0x33 /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/../../../../riscv64-unknown-elf/lib/rv64imafdcxtheadc/lp64d/libc.a(lib_a-memmove-stub.o)
 .riscv.attributes
                0x0000000000000000       0x3e /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/../../../../riscv64-unknown-elf/lib/rv64imafdcxtheadc/lp64d/libc.a(lib_a-memmove-stub.o)
 .data          0x0000000000000000        0x0 /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/../../../../riscv64-unknown-elf/lib/rv64imafdcxtheadc/lp64d/libc.a(lib_a-memset.o)
 .bss           0x0000000000000000        0x0 /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/../../../../riscv64-unknown-elf/lib/rv64imafdcxtheadc/lp64d/libc.a(lib_a-memset.o)
 .data          0x0000000000000000        0x0 /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/../../../../riscv64-unknown-elf/lib/rv64imafdcxtheadc/lp64d/libc.a(lib_a-sysclose.o)
 .bss           0x0000000000000000        0x0 /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/../../../../riscv64-unknown-elf/lib/rv64imafdcxtheadc/lp64d/libc.a(lib_a-sysclose.o)
 .data          0x0000000000000000        0x0 /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/../../../../riscv64-unknown-elf/lib/rv64imafdcxtheadc/lp64d/libc.a(lib_a-sysopen.o)
 .bss           0x0000000000000000        0x0 /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/../../../../riscv64-unknown-elf/lib/rv64imafdcxtheadc/lp64d/libc.a(lib_a-sysopen.o)
 .text          0x0000000000000000       0x30 /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/../../../../riscv64-unknown-elf/lib/rv64imafdcxtheadc/lp64d/libc.a(lib_a-syswrite.o)
 .data          0x0000000000000000        0x0 /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/../../../../riscv64-unknown-elf/lib/rv64imafdcxtheadc/lp64d/libc.a(lib_a-syswrite.o)
 .bss           0x0000000000000000        0x0 /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/../../../../riscv64-unknown-elf/lib/rv64imafdcxtheadc/lp64d/libc.a(lib_a-syswrite.o)
 .comment       0x0000000000000000       0x33 /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/../../../../riscv64-unknown-elf/lib/rv64imafdcxtheadc/lp64d/libc.a(lib_a-syswrite.o)
 .riscv.attributes
                0x0000000000000000       0x3e /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/../../../../riscv64-unknown-elf/lib/rv64imafdcxtheadc/lp64d/libc.a(lib_a-syswrite.o)
 .text          0x0000000000000000        0x0 /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/../../../../riscv64-unknown-elf/lib/rv64imafdcxtheadc/lp64d/libc.a(lib_a-impure.o)
 .bss           0x0000000000000000        0x0 /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/../../../../riscv64-unknown-elf/lib/rv64imafdcxtheadc/lp64d/libc.a(lib_a-impure.o)
 .rodata        0x0000000000000000        0x8 /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/../../../../riscv64-unknown-elf/lib/rv64imafdcxtheadc/lp64d/libc.a(lib_a-impure.o)

Memory Configuration

Name             Origin             Length             Attributes
dram0_0_seg      0x00000000c2000000 0x0000000004000000 xrw
dram0_1_seg      0x00000000e0000000 0x0000000010000000 xrw
dram0_2_seg      0x00000000e9b00000 0x00000000000f0000 xrw
magic_info_seg   0x00000000ffff0000 0x0000000000000100 r
*default*        0x0000000000000000 0xffffffffffffffff

Linker script and memory map

LOAD emodules/mod_mixture/mod_mixture.o
LOAD emodules/mod_mixture/magic.o
LOAD emodules/mod_mixture/startup_check/startup_check.o
LOAD emodules/mod_mixture/bmp_parser/bmp.o
LOAD emodules/mod_mixture/bmp_parser/Parse_Picture.o
LOAD emodules/mod_mixture/backcarcheck/dis_bmp.o
LOAD emodules/mod_mixture/backcarcheck/dis_checkio.o
LOAD emodules/mod_mixture/backcarcheck/dis_Parse_Picture.o
LOAD emodules/mod_mixture/backcarcheck/dis_show_picture.o
START GROUP
START GROUP
LOAD ./elibrary/bin//libsyscall.a
LOAD ./elibrary/bin//libminic.a
LOAD ./elibrary/bin//libpub0.a
LOAD ./elibrary/bin//liblzma.a
END GROUP
START GROUP
LOAD ./elibrary/bin//libcheck_app.a
END GROUP
LOAD /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/../../../../riscv64-unknown-elf/lib/rv64imafdcxtheadc/lp64d/libstdc++.a
LOAD /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/../../../../riscv64-unknown-elf/lib/rv64imafdcxtheadc/lp64d/libc.a
LOAD /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/rv64imafdcxtheadc/lp64d/libgcc.a
LOAD /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/../../../../riscv64-unknown-elf/lib/rv64imafdcxtheadc/lp64d/libm.a
END GROUP
                0x00000000e9b00000                __DRAM0_MOD_BASE = ORIGIN (dram0_2_seg)
                0x00000000ffff0000                __DRAM0_INF_BASE = ORIGIN (magic_info_seg)

.mod.text       0x00000000e9b00000     0x49dc
 *(.text)
 .text          0x00000000e9b00000       0xd0 /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/../../../../riscv64-unknown-elf/lib/rv64imafdcxtheadc/lp64d/libc.a(lib_a-memcpy.o)
                0x00000000e9b00000                memcpy
 .text          0x00000000e9b000d0       0xaa /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/../../../../riscv64-unknown-elf/lib/rv64imafdcxtheadc/lp64d/libc.a(lib_a-memset.o)
                0x00000000e9b000d0                memset
 *fill*         0x00000000e9b0017a        0x6 
 .text          0x00000000e9b00180       0x1e /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/../../../../riscv64-unknown-elf/lib/rv64imafdcxtheadc/lp64d/libc.a(lib_a-sysclose.o)
                0x00000000e9b00180                close
 *fill*         0x00000000e9b0019e        0x2 
 .text          0x00000000e9b001a0       0x3e /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/../../../../riscv64-unknown-elf/lib/rv64imafdcxtheadc/lp64d/libc.a(lib_a-sysopen.o)
                0x00000000e9b001a0                open
 *(.text.*)
 .text.CB_UserDef_LBC
                0x00000000e9b001de        0x4 emodules/mod_mixture/mod_mixture.o
 .text.CB_UserDef_Scaledown
                0x00000000e9b001e2       0x3c emodules/mod_mixture/mod_mixture.o
 .text.mcu_open
                0x00000000e9b0021e        0xe emodules/mod_mixture/mod_mixture.o
 .text.set_camera_in_power_up_before
                0x00000000e9b0022c       0x50 emodules/mod_mixture/mod_mixture.o
 .text.send_mcu_dvr_hide_icon
                0x00000000e9b0027c       0x4c emodules/mod_mixture/mod_mixture.o
 .text.send_mcu_dvr_switch_rear_lens
                0x00000000e9b002c8       0x4c emodules/mod_mixture/mod_mixture.o
 .text.send_mcu_dvr_switch_flont_rear_lens
                0x00000000e9b00314       0x4c emodules/mod_mixture/mod_mixture.o
 .text.send_mcu_dvr_show_icon
                0x00000000e9b00360       0x4c emodules/mod_mixture/mod_mixture.o
 .text.get_size_jpg.part.0
                0x00000000e9b003ac       0x7c emodules/mod_mixture/mod_mixture.o
 .text.show_jpg_logo
                0x00000000e9b00428      0x246 emodules/mod_mixture/mod_mixture.o
 .text.LoadDataFromFile
                0x00000000e9b0066e       0xee emodules/mod_mixture/mod_mixture.o
                0x00000000e9b0066e                LoadDataFromFile
 .text.__jpg_animation
                0x00000000e9b0075c      0x3f0 emodules/mod_mixture/mod_mixture.o
 .text.__animation_task
                0x00000000e9b00b4c       0x20 emodules/mod_mixture/mod_mixture.o
 .text.close_jpg_logo
                0x00000000e9b00b6c       0x88 emodules/mod_mixture/mod_mixture.o
                0x00000000e9b00b6c                close_jpg_logo
 .text.show_bmp_logo
                0x00000000e9b00bf4      0x34c emodules/mod_mixture/mod_mixture.o
                0x00000000e9b00bf4                show_bmp_logo
 .text.close_bmp_logo
                0x00000000e9b00f40      0x138 emodules/mod_mixture/mod_mixture.o
                0x00000000e9b00f40                close_bmp_logo
 .text.show_picture
                0x00000000e9b01078       0x3e emodules/mod_mixture/mod_mixture.o
                0x00000000e9b01078                show_picture
 .text.stop_picture
                0x00000000e9b010b6       0x7a emodules/mod_mixture/mod_mixture.o
                0x00000000e9b010b6                stop_picture
 .text.close_bmp_animation
                0x00000000e9b01130       0x84 emodules/mod_mixture/mod_mixture.o
                0x00000000e9b01130                close_bmp_animation
 .text.plogo_data_init
                0x00000000e9b011b4      0x232 emodules/mod_mixture/mod_mixture.o
                0x00000000e9b011b4                plogo_data_init
 .text.display_into_backcar_state
                0x00000000e9b013e6      0x102 emodules/mod_mixture/mod_mixture.o
                0x00000000e9b013e6                display_into_backcar_state
 .text.check_backcar_io
                0x00000000e9b014e8       0xda emodules/mod_mixture/mod_mixture.o
                0x00000000e9b014e8                check_backcar_io
 .text.display_init_backcar_state
                0x00000000e9b015c2       0x84 emodules/mod_mixture/mod_mixture.o
                0x00000000e9b015c2                display_init_backcar_state
 .text.display_uninit_backcar_state
                0x00000000e9b01646       0x92 emodules/mod_mixture/mod_mixture.o
                0x00000000e9b01646                display_uninit_backcar_state
 .text.logo_scrolling_thread
                0x00000000e9b016d8      0x3ba emodules/mod_mixture/mod_mixture.o
 .text.logo_crolling_thread_init
                0x00000000e9b01a92       0x46 emodules/mod_mixture/mod_mixture.o
                0x00000000e9b01a92                logo_crolling_thread_init
 .text.mix_cedar_close
                0x00000000e9b01ad8       0x46 emodules/mod_mixture/mod_mixture.o
                0x00000000e9b01ad8                mix_cedar_close
 .text.mix_request_video_layer
                0x00000000e9b01b1e      0x13e emodules/mod_mixture/mod_mixture.o
                0x00000000e9b01b1e                mix_request_video_layer
 .text.mix_release_video_layer
                0x00000000e9b01c5c       0x38 emodules/mod_mixture/mod_mixture.o
                0x00000000e9b01c5c                mix_release_video_layer
 .text.show_movie
                0x00000000e9b01c94      0x1bc emodules/mod_mixture/mod_mixture.o
                0x00000000e9b01c94                show_movie
 .text.get_movie_stop
                0x00000000e9b01e50       0x34 emodules/mod_mixture/mod_mixture.o
                0x00000000e9b01e50                get_movie_stop
 .text.close_movie
                0x00000000e9b01e84       0x58 emodules/mod_mixture/mod_mixture.o
                0x00000000e9b01e84                close_movie
 .text.Mod_Mixture_MInit
                0x00000000e9b01edc        0x4 emodules/mod_mixture/mod_mixture.o
                0x00000000e9b01edc                Mod_Mixture_MInit
 .text.Mod_Mixture_MExit
                0x00000000e9b01ee0        0x4 emodules/mod_mixture/mod_mixture.o
                0x00000000e9b01ee0                Mod_Mixture_MExit
 .text.Mod_Mixture_MOpen
                0x00000000e9b01ee4       0x62 emodules/mod_mixture/mod_mixture.o
                0x00000000e9b01ee4                Mod_Mixture_MOpen
 .text.Mod_Mixture_MClose
                0x00000000e9b01f46       0x36 emodules/mod_mixture/mod_mixture.o
                0x00000000e9b01f46                Mod_Mixture_MClose
 .text.Mod_Mixture_MRead
                0x00000000e9b01f7c        0x4 emodules/mod_mixture/mod_mixture.o
                0x00000000e9b01f7c                Mod_Mixture_MRead
 .text.Mod_Mixture_MWrite
                0x00000000e9b01f80        0x4 emodules/mod_mixture/mod_mixture.o
                0x00000000e9b01f80                Mod_Mixture_MWrite
 .text.Mod_Mixture_MIoctrl
                0x00000000e9b01f84      0x1f0 emodules/mod_mixture/mod_mixture.o
                0x00000000e9b01f84                Mod_Mixture_MIoctrl
 .text.open_bmp
                0x00000000e9b02174      0x1b8 emodules/mod_mixture/bmp_parser/bmp.o
                0x00000000e9b02174                open_bmp
 .text.close_bmp
                0x00000000e9b0232c       0x58 emodules/mod_mixture/bmp_parser/bmp.o
                0x00000000e9b0232c                close_bmp
 .text.get_matrix
                0x00000000e9b02384       0xaa emodules/mod_mixture/bmp_parser/bmp.o
                0x00000000e9b02384                get_matrix
 .text.get_bitcount
                0x00000000e9b0242e        0x6 emodules/mod_mixture/bmp_parser/bmp.o
                0x00000000e9b0242e                get_bitcount
 .text.get_width
                0x00000000e9b02434        0x4 emodules/mod_mixture/bmp_parser/bmp.o
                0x00000000e9b02434                get_width
 .text.get_height
                0x00000000e9b02438        0xe emodules/mod_mixture/bmp_parser/bmp.o
                0x00000000e9b02438                get_height
 .text.get_rowsize
                0x00000000e9b02446        0x4 emodules/mod_mixture/bmp_parser/bmp.o
                0x00000000e9b02446                get_rowsize
 .text.Parse_Pic_BMP_ByPath
                0x00000000e9b0244a       0x96 emodules/mod_mixture/bmp_parser/Parse_Picture.o
                0x00000000e9b0244a                Parse_Pic_BMP_ByPath
 .text.dis_open_bmp
                0x00000000e9b024e0      0x198 emodules/mod_mixture/backcarcheck/dis_bmp.o
                0x00000000e9b024e0                dis_open_bmp
 .text.dis_close_bmp
                0x00000000e9b02678       0x5a emodules/mod_mixture/backcarcheck/dis_bmp.o
                0x00000000e9b02678                dis_close_bmp
 .text.dis_get_matrix
                0x00000000e9b026d2       0xc6 emodules/mod_mixture/backcarcheck/dis_bmp.o
                0x00000000e9b026d2                dis_get_matrix
 .text.dis_get_bitcount
                0x00000000e9b02798        0xa emodules/mod_mixture/backcarcheck/dis_bmp.o
                0x00000000e9b02798                dis_get_bitcount
 .text.dis_get_width
                0x00000000e9b027a2        0x8 emodules/mod_mixture/backcarcheck/dis_bmp.o
                0x00000000e9b027a2                dis_get_width
 .text.dis_get_height
                0x00000000e9b027aa       0x12 emodules/mod_mixture/backcarcheck/dis_bmp.o
                0x00000000e9b027aa                dis_get_height
 .text.dis_get_rowsize
                0x00000000e9b027bc        0x8 emodules/mod_mixture/backcarcheck/dis_bmp.o
                0x00000000e9b027bc                dis_get_rowsize
 .text.video_init
                0x00000000e9b027c4       0x40 emodules/mod_mixture/backcarcheck/dis_checkio.o
                0x00000000e9b027c4                video_init
 .text.video_exit
                0x00000000e9b02804       0x3a emodules/mod_mixture/backcarcheck/dis_checkio.o
                0x00000000e9b02804                video_exit
 .text.video_get_status
                0x00000000e9b0283e       0x24 emodules/mod_mixture/backcarcheck/dis_checkio.o
                0x00000000e9b0283e                video_get_status
 .text.video_get_format
                0x00000000e9b02862       0x24 emodules/mod_mixture/backcarcheck/dis_checkio.o
                0x00000000e9b02862                video_get_format
 .text.video_set_luma
                0x00000000e9b02886       0x22 emodules/mod_mixture/backcarcheck/dis_checkio.o
                0x00000000e9b02886                video_set_luma
 .text.video_set_contrast
                0x00000000e9b028a8       0x22 emodules/mod_mixture/backcarcheck/dis_checkio.o
                0x00000000e9b028a8                video_set_contrast
 .text.video_set_saturation
                0x00000000e9b028ca       0x22 emodules/mod_mixture/backcarcheck/dis_checkio.o
                0x00000000e9b028ca                video_set_saturation
 .text.video_config_para
                0x00000000e9b028ec       0xa8 emodules/mod_mixture/backcarcheck/dis_checkio.o
                0x00000000e9b028ec                video_config_para
 .text.video_open
                0x00000000e9b02994       0xb2 emodules/mod_mixture/backcarcheck/dis_checkio.o
                0x00000000e9b02994                video_open
 .text.video_close
                0x00000000e9b02a46       0x24 emodules/mod_mixture/backcarcheck/dis_checkio.o
                0x00000000e9b02a46                video_close
 .text.dis_Parse_Pic_BMP_ByPath
                0x00000000e9b02a6a       0xa4 emodules/mod_mixture/backcarcheck/dis_Parse_Picture.o
                0x00000000e9b02a6a                dis_Parse_Pic_BMP_ByPath
 .text.__show_nosignal_pic
                0x00000000e9b02b0e      0x13a emodules/mod_mixture/backcarcheck/dis_show_picture.o
                0x00000000e9b02b0e                __show_nosignal_pic
 .text.__close_nosignal_pic
                0x00000000e9b02c48       0x8a emodules/mod_mixture/backcarcheck/dis_show_picture.o
                0x00000000e9b02c48                __close_nosignal_pic
 .text.esCFG_GetGPIOSecData
                0x00000000e9b02cd2       0x18 ./elibrary/bin//libsyscall.a(syscall.o)
                0x00000000e9b02cd2                esCFG_GetGPIOSecData
 .text.esCFG_GetKeyValue
                0x00000000e9b02cea       0x18 ./elibrary/bin//libsyscall.a(syscall.o)
                0x00000000e9b02cea                esCFG_GetKeyValue
 .text.esFSYS_closedir
                0x00000000e9b02d02       0x18 ./elibrary/bin//libsyscall.a(syscall.o)
                0x00000000e9b02d02                esFSYS_closedir
 .text.esFSYS_fclose
                0x00000000e9b02d1a       0x18 ./elibrary/bin//libsyscall.a(syscall.o)
                0x00000000e9b02d1a                esFSYS_fclose
 .text.esFSYS_fopen
                0x00000000e9b02d32       0x16 ./elibrary/bin//libsyscall.a(syscall.o)
                0x00000000e9b02d32                esFSYS_fopen
 .text.esFSYS_fread
                0x00000000e9b02d48       0x20 ./elibrary/bin//libsyscall.a(syscall.o)
                0x00000000e9b02d48                esFSYS_fread
 .text.esFSYS_fseek
                0x00000000e9b02d68       0x18 ./elibrary/bin//libsyscall.a(syscall.o)
                0x00000000e9b02d68                esFSYS_fseek
 .text.esFSYS_fseekex
                0x00000000e9b02d80       0x16 ./elibrary/bin//libsyscall.a(syscall.o)
                0x00000000e9b02d80                esFSYS_fseekex
 .text.esFSYS_ftell
                0x00000000e9b02d96       0x18 ./elibrary/bin//libsyscall.a(syscall.o)
                0x00000000e9b02d96                esFSYS_ftell
 .text.esFSYS_ftellex
                0x00000000e9b02dae       0x18 ./elibrary/bin//libsyscall.a(syscall.o)
                0x00000000e9b02dae                esFSYS_ftellex
 .text.esFSYS_opendir
                0x00000000e9b02dc6       0x16 ./elibrary/bin//libsyscall.a(syscall.o)
                0x00000000e9b02dc6                esFSYS_opendir
 .text.esFSYS_statpt
                0x00000000e9b02ddc       0x18 ./elibrary/bin//libsyscall.a(syscall.o)
                0x00000000e9b02ddc                esFSYS_statpt
 .text.esKRNL_GetCallBack
                0x00000000e9b02df4       0x16 ./elibrary/bin//libsyscall.a(syscall.o)
                0x00000000e9b02df4                esKRNL_GetCallBack
 .text.esKRNL_SemCreate
                0x00000000e9b02e0a       0x16 ./elibrary/bin//libsyscall.a(syscall.o)
                0x00000000e9b02e0a                esKRNL_SemCreate
 .text.esKRNL_SemPend
                0x00000000e9b02e20       0x16 ./elibrary/bin//libsyscall.a(syscall.o)
                0x00000000e9b02e20                esKRNL_SemPend
 .text.esKRNL_SemPost
                0x00000000e9b02e36       0x1a ./elibrary/bin//libsyscall.a(syscall.o)
                0x00000000e9b02e36                esKRNL_SemPost
 .text.esKRNL_TCreate
                0x00000000e9b02e50       0x1c ./elibrary/bin//libsyscall.a(syscall.o)
                0x00000000e9b02e50                esKRNL_TCreate
 .text.esKRNL_TDel
                0x00000000e9b02e6c       0x1e ./elibrary/bin//libsyscall.a(syscall.o)
                0x00000000e9b02e6c                esKRNL_TDel
 .text.esKRNL_TDelReq
                0x00000000e9b02e8a       0x1e ./elibrary/bin//libsyscall.a(syscall.o)
                0x00000000e9b02e8a                esKRNL_TDelReq
 .text.esKRNL_TimeDly
                0x00000000e9b02ea8       0x16 ./elibrary/bin//libsyscall.a(syscall.o)
                0x00000000e9b02ea8                esKRNL_TimeDly
 .text.esKSRV_Get_Display_Hld
                0x00000000e9b02ebe       0x16 ./elibrary/bin//libsyscall.a(syscall.o)
                0x00000000e9b02ebe                esKSRV_Get_Display_Hld
 .text.esKSRV_SendMsg
                0x00000000e9b02ed4       0x20 ./elibrary/bin//libsyscall.a(syscall.o)
                0x00000000e9b02ed4                esKSRV_SendMsg
 .text.esMEMS_Balloc
                0x00000000e9b02ef4       0x16 ./elibrary/bin//libsyscall.a(syscall.o)
                0x00000000e9b02ef4                esMEMS_Balloc
 .text.esMEMS_Bfree
                0x00000000e9b02f0a       0x16 ./elibrary/bin//libsyscall.a(syscall.o)
                0x00000000e9b02f0a                esMEMS_Bfree
 .text.esMEMS_CleanFlushDCacheRegion
                0x00000000e9b02f20       0x1a ./elibrary/bin//libsyscall.a(syscall.o)
                0x00000000e9b02f20                esMEMS_CleanFlushDCacheRegion
 .text.esMEMS_Malloc
                0x00000000e9b02f3a       0x1a ./elibrary/bin//libsyscall.a(syscall.o)
                0x00000000e9b02f3a                esMEMS_Malloc
 .text.esMEMS_Mfree
                0x00000000e9b02f54       0x16 ./elibrary/bin//libsyscall.a(syscall.o)
                0x00000000e9b02f54                esMEMS_Mfree
 .text.esMEMS_Palloc
                0x00000000e9b02f6a       0x1e ./elibrary/bin//libsyscall.a(syscall.o)
                0x00000000e9b02f6a                esMEMS_Palloc
 .text.esMEMS_Pfree
                0x00000000e9b02f88       0x1a ./elibrary/bin//libsyscall.a(syscall.o)
                0x00000000e9b02f88                esMEMS_Pfree
 .text.esMODS_MClose
                0x00000000e9b02fa2       0x18 ./elibrary/bin//libsyscall.a(syscall.o)
                0x00000000e9b02fa2                esMODS_MClose
 .text.esMODS_MInstall
                0x00000000e9b02fba       0x18 ./elibrary/bin//libsyscall.a(syscall.o)
                0x00000000e9b02fba                esMODS_MInstall
 .text.esMODS_MIoctrl
                0x00000000e9b02fd2       0x1a ./elibrary/bin//libsyscall.a(syscall.o)
                0x00000000e9b02fd2                esMODS_MIoctrl
 .text.esMODS_MOpen
                0x00000000e9b02fec       0x16 ./elibrary/bin//libsyscall.a(syscall.o)
                0x00000000e9b02fec                esMODS_MOpen
 .text.esMODS_MUninstall
                0x00000000e9b03002       0x18 ./elibrary/bin//libsyscall.a(syscall.o)
                0x00000000e9b03002                esMODS_MUninstall
 .text.esPINS_PinGrpRel
                0x00000000e9b0301a       0x16 ./elibrary/bin//libsyscall.a(syscall.o)
                0x00000000e9b0301a                esPINS_PinGrpRel
 .text.esPINS_PinGrpReq
                0x00000000e9b03030       0x16 ./elibrary/bin//libsyscall.a(syscall.o)
                0x00000000e9b03030                esPINS_PinGrpReq
 .text.esPINS_ReadPinData
                0x00000000e9b03046       0x16 ./elibrary/bin//libsyscall.a(syscall.o)
                0x00000000e9b03046                esPINS_ReadPinData
 .text.esPINS_SetPinIO
                0x00000000e9b0305c       0x16 ./elibrary/bin//libsyscall.a(syscall.o)
                0x00000000e9b0305c                esPINS_SetPinIO
 .text.esPINS_SetPinPull
                0x00000000e9b03072       0x1a ./elibrary/bin//libsyscall.a(syscall.o)
                0x00000000e9b03072                esPINS_SetPinPull
 .text.esSIOS_putstr
                0x00000000e9b0308c       0x14 ./elibrary/bin//libsyscall.a(syscall.o)
                0x00000000e9b0308c                esSIOS_putstr
 .text._close_r
                0x00000000e9b030a0       0x18 ./elibrary/bin//libsyscall.a(syscall_fops.o)
                0x00000000e9b030a0                _close_r
 .text._open_r  0x00000000e9b030b8       0x18 ./elibrary/bin//libsyscall.a(syscall_fops.o)
                0x00000000e9b030b8                _open_r
 .text.ioctl    0x00000000e9b030d0       0x28 ./elibrary/bin//libsyscall.a(syscall_fops.o)
                0x00000000e9b030d0                ioctl
 .text.eLIBs_vsnprintf
                0x00000000e9b030f8        0xc ./elibrary/bin//libminic.a(elibs_sprintf.o)
                0x00000000e9b030f8                eLIBs_vsnprintf
 .text.eLIBs_vscnprintf
                0x00000000e9b03104       0x1c ./elibrary/bin//libminic.a(elibs_sprintf.o)
                0x00000000e9b03104                eLIBs_vscnprintf
 .text.eLIBs_sprintf
                0x00000000e9b03120       0x22 ./elibrary/bin//libminic.a(elibs_sprintf.o)
                0x00000000e9b03120                eLIBs_sprintf
 .text.eLIBs_fopen
                0x00000000e9b03142        0x4 ./elibrary/bin//libminic.a(elibs_stdio.o)
                0x00000000e9b03142                eLIBs_fopen
 .text.eLIBs_fclose
                0x00000000e9b03146        0x4 ./elibrary/bin//libminic.a(elibs_stdio.o)
                0x00000000e9b03146                eLIBs_fclose
 .text.eLIBs_fread
                0x00000000e9b0314a        0x4 ./elibrary/bin//libminic.a(elibs_stdio.o)
                0x00000000e9b0314a                eLIBs_fread
 .text.eLIBs_fseek
                0x00000000e9b0314e       0x12 ./elibrary/bin//libminic.a(elibs_stdio.o)
                0x00000000e9b0314e                eLIBs_fseek
 .text.eLIBs_fllseek
                0x00000000e9b03160       0x1a ./elibrary/bin//libminic.a(elibs_stdio.o)
                0x00000000e9b03160                eLIBs_fllseek
 .text.eLIBs_ftell
                0x00000000e9b0317a        0x4 ./elibrary/bin//libminic.a(elibs_stdio.o)
                0x00000000e9b0317a                eLIBs_ftell
 .text.eLIBs_flltell
                0x00000000e9b0317e       0x22 ./elibrary/bin//libminic.a(elibs_stdio.o)
                0x00000000e9b0317e                eLIBs_flltell
 .text.eLIBs_closedir
                0x00000000e9b031a0        0x4 ./elibrary/bin//libminic.a(elibs_stdio.o)
                0x00000000e9b031a0                eLIBs_closedir
 .text.eLIBs_IsPartFormated
                0x00000000e9b031a4       0x80 ./elibrary/bin//libminic.a(elibs_stdio.o)
                0x00000000e9b031a4                eLIBs_IsPartFormated
 .text.eLIBs_vprintf
                0x00000000e9b03224       0x4c ./elibrary/bin//libminic.a(elibs_stdio.o)
                0x00000000e9b03224                eLIBs_vprintf
 .text.eLIBs_printf
                0x00000000e9b03270       0x20 ./elibrary/bin//libminic.a(elibs_stdio.o)
                0x00000000e9b03270                eLIBs_printf
                0x00000000e9b03270                printk
 .text.eLIBs_malloc
                0x00000000e9b03290        0xa ./elibrary/bin//libminic.a(elibs_stdlib.o)
                0x00000000e9b03290                eLIBs_malloc
 .text.eLIBs_free
                0x00000000e9b0329a        0xc ./elibrary/bin//libminic.a(elibs_stdlib.o)
                0x00000000e9b0329a                eLIBs_free
 .text.eLIBs_strcpy
                0x00000000e9b032a6        0xc ./elibrary/bin//libminic.a(elibs_string.o)
                0x00000000e9b032a6                eLIBs_strcpy
 .text.eLIBs_strncpy
                0x00000000e9b032b2        0xc ./elibrary/bin//libminic.a(elibs_string.o)
                0x00000000e9b032b2                eLIBs_strncpy
 .text.eLIBs_strncmp
                0x00000000e9b032be        0xc ./elibrary/bin//libminic.a(elibs_string.o)
                0x00000000e9b032be                eLIBs_strncmp
 .text.eLIBs_memset
                0x00000000e9b032ca        0xc ./elibrary/bin//libminic.a(elibs_string.o)
                0x00000000e9b032ca                eLIBs_memset
 .text.eLIBs_memcpy
                0x00000000e9b032d6        0xc ./elibrary/bin//libminic.a(elibs_string.o)
                0x00000000e9b032d6                eLIBs_memcpy
 .text.AZ100_IsCompress
                0x00000000e9b032e2       0x2e ./elibrary/bin//liblzma.a(az100.o)
                0x00000000e9b032e2                AZ100_IsCompress
 .text.AZ100_GetUncompressSize
                0x00000000e9b03310       0x52 ./elibrary/bin//liblzma.a(az100.o)
                0x00000000e9b03310                AZ100_GetUncompressSize
 .text.AZ100_Uncompress
                0x00000000e9b03362       0x24 ./elibrary/bin//liblzma.a(az100.o)
                0x00000000e9b03362                AZ100_Uncompress
 .text.AZ100_DataUncompress
                0x00000000e9b03386       0x26 ./elibrary/bin//liblzma.a(compress.o)
                0x00000000e9b03386                AZ100_DataUncompress
 .text.SzFree   0x00000000e9b033ac        0x6 ./elibrary/bin//liblzma.a(LzmaLib.o)
 .text.SzAlloc  0x00000000e9b033b2        0x6 ./elibrary/bin//liblzma.a(LzmaLib.o)
 .text.LzmaUncompress
                0x00000000e9b033b8       0x20 ./elibrary/bin//liblzma.a(LzmaLib.o)
                0x00000000e9b033b8                LzmaUncompress
 .text.LzmaDec_WriteRem
                0x00000000e9b033d8       0x80 ./elibrary/bin//liblzma.a(LzmaDec.o)
 .text.LzmaDec_DecodeReal2
                0x00000000e9b03458      0xc54 ./elibrary/bin//liblzma.a(LzmaDec.o)
 .text.LzmaDec_TryDummy
                0x00000000e9b040ac      0x500 ./elibrary/bin//liblzma.a(LzmaDec.o)
 .text.LzmaDec_Init
                0x00000000e9b045ac       0x1a ./elibrary/bin//liblzma.a(LzmaDec.o)
                0x00000000e9b045ac                LzmaDec_Init
 .text.LzmaDec_DecodeToDic
                0x00000000e9b045c6      0x26c ./elibrary/bin//liblzma.a(LzmaDec.o)
                0x00000000e9b045c6                LzmaDec_DecodeToDic
 .text.LzmaDec_FreeProbs
                0x00000000e9b04832       0x1c ./elibrary/bin//liblzma.a(LzmaDec.o)
                0x00000000e9b04832                LzmaDec_FreeProbs
 .text.LzmaDec_AllocateProbs2
                0x00000000e9b0484e       0x58 ./elibrary/bin//liblzma.a(LzmaDec.o)
 .text.LzmaProps_Decode
                0x00000000e9b048a6       0x5c ./elibrary/bin//liblzma.a(LzmaDec.o)
                0x00000000e9b048a6                LzmaProps_Decode
 .text.LzmaDec_AllocateProbs
                0x00000000e9b04902       0x30 ./elibrary/bin//liblzma.a(LzmaDec.o)
                0x00000000e9b04902                LzmaDec_AllocateProbs
 .text.LzmaDecode
                0x00000000e9b04932       0xaa ./elibrary/bin//liblzma.a(LzmaDec.o)
                0x00000000e9b04932                LzmaDecode
 *(.stub)
 *(.gnu.warning)
 *(.gnu.linkonce.t*)
                0x00000000e9b049dc                . = ALIGN (0x4)
 *fill*         0x00000000e9b049dc        0x0 

.mod.rodata     0x00000000e9b049dc      0x534
 *(.rodata)
 *(.rodata.*)
 .rodata.Mod_Mixture_MIoctrl
                0x00000000e9b049dc       0x24 emodules/mod_mixture/mod_mixture.o
 *fill*         0x00000000e9b04a00        0x0 
 .rodata.__jpg_animation.str1.8
                0x00000000e9b04a00       0x5c emodules/mod_mixture/mod_mixture.o
 *fill*         0x00000000e9b04a5c        0x4 
 .rodata.check_backcar_io.str1.8
                0x00000000e9b04a60       0x62 emodules/mod_mixture/mod_mixture.o
 *fill*         0x00000000e9b04ac2        0x6 
 .rodata.close_bmp_logo.str1.8
                0x00000000e9b04ac8       0x39 emodules/mod_mixture/mod_mixture.o
 *fill*         0x00000000e9b04b01        0x7 
 .rodata.display_init_backcar_state.str1.8
                0x00000000e9b04b08       0x12 emodules/mod_mixture/mod_mixture.o
 *fill*         0x00000000e9b04b1a        0x6 
 .rodata.mcu_open.str1.8
                0x00000000e9b04b20        0x9 emodules/mod_mixture/mod_mixture.o
 *fill*         0x00000000e9b04b29        0x7 
 .rodata.mix_request_video_layer.str1.8
                0x00000000e9b04b30       0x8b emodules/mod_mixture/mod_mixture.o
 *fill*         0x00000000e9b04bbb        0x5 
 .rodata.plogo_data_init.str1.8
                0x00000000e9b04bc0      0x14b emodules/mod_mixture/mod_mixture.o
 *fill*         0x00000000e9b04d0b        0x5 
 .rodata.read_picture_index.str1.8
                0x00000000e9b04d10       0x30 emodules/mod_mixture/mod_mixture.o
 .rodata.show_bmp_logo.str1.8
                0x00000000e9b04d40       0x3f emodules/mod_mixture/mod_mixture.o
 *fill*         0x00000000e9b04d7f        0x1 
 .rodata.show_jpg_logo.str1.8
                0x00000000e9b04d80       0x12 emodules/mod_mixture/mod_mixture.o
 *fill*         0x00000000e9b04d92        0x6 
 .rodata.show_movie.str1.8
                0x00000000e9b04d98       0xc9 emodules/mod_mixture/mod_mixture.o
 *fill*         0x00000000e9b04e61        0x7 
 .rodata.stop_picture.str1.8
                0x00000000e9b04e68       0x3d emodules/mod_mixture/mod_mixture.o
 .rodata.open_bmp.str1.8
                0x00000000e9b04ea5        0x3 emodules/mod_mixture/bmp_parser/bmp.o
 .rodata.dis_open_bmp.str1.8
                0x00000000e9b04ea5        0x3 emodules/mod_mixture/backcarcheck/dis_bmp.o
 *fill*         0x00000000e9b04ea5        0x3 
 .rodata.video_config_para.str1.8
                0x00000000e9b04ea8       0x27 emodules/mod_mixture/backcarcheck/dis_checkio.o
                                         0x37 (size before relaxing)
 *fill*         0x00000000e9b04ecf        0x1 
 .rodata.video_init.str1.8
                0x00000000e9b04ed0       0x16 emodules/mod_mixture/backcarcheck/dis_checkio.o
 *fill*         0x00000000e9b04ee6        0x2 
 .rodata.__show_nosignal_pic.str1.8
                0x00000000e9b04ee8       0x1c emodules/mod_mixture/backcarcheck/dis_show_picture.o
 *fill*         0x00000000e9b04f04        0x4 
 .rodata.AZ100_IsCompress.str1.8
                0x00000000e9b04f08        0x8 ./elibrary/bin//liblzma.a(az100.o)
                                          0x5 (size before relaxing)
 *(.gnu.linkonce.r*)
                0x00000000e9b04f10                . = ALIGN (0x4)

.mod.data       0x00000000e9b04f10      0x768
 *(.data)
 .data          0x00000000e9b04f10      0x750 /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/../../../../riscv64-unknown-elf/lib/rv64imafdcxtheadc/lp64d/libc.a(lib_a-impure.o)
                0x00000000e9b04f10                _impure_ptr
 *(.data.*)
 .data.backcar_check_io
                0x00000000e9b05660        0x1 emodules/mod_mixture/mod_mixture.o
                0x00000000e9b05660                backcar_check_io
 .data.last_backcar_check_io
                0x00000000e9b05661        0x1 emodules/mod_mixture/mod_mixture.o
                0x00000000e9b05661                last_backcar_check_io
 *fill*         0x00000000e9b05662        0x6 
 .data.g_Alloc  0x00000000e9b05668       0x10 ./elibrary/bin//liblzma.a(LzmaLib.o)
 *(.gnu.linkonce.d.*)
 *(.sdata)
 *(.sdata.*)
 *(.gnu.linkonce.s.*)
 *(.sdata2)
 *(.sdata2.*)
 *(.gnu.linkonce.s2.*)
                0x00000000e9b05678                . = ALIGN (0x4)

.mod.bss        0x00000000e9b05678      0x140
                0x00000000e9b05678                __bss_start = ABSOLUTE (.)
 *(.bss)
 *(.bss.*)
 .bss._disp_init_para
                0x00000000e9b05678        0x8 emodules/mod_mixture/mod_mixture.o
                0x00000000e9b05678                _disp_init_para
 .bss.backcar_det_time_cnt
                0x00000000e9b05680        0x1 emodules/mod_mixture/mod_mixture.o
                0x00000000e9b05680                backcar_det_time_cnt
 .bss.count.17216
                0x00000000e9b05681        0x1 emodules/mod_mixture/mod_mixture.o
 .bss.det_back_car_flag
                0x00000000e9b05682        0x1 emodules/mod_mixture/mod_mixture.o
                0x00000000e9b05682                det_back_car_flag
 .bss.disp_backcar_dev_open
                0x00000000e9b05683        0x1 emodules/mod_mixture/mod_mixture.o
                0x00000000e9b05683                disp_backcar_dev_open
 .bss.disp_backcar_into_backcar
                0x00000000e9b05684        0x4 emodules/mod_mixture/mod_mixture.o
                0x00000000e9b05684                disp_backcar_into_backcar
 .bss.disp_backcar_new_format
                0x00000000e9b05688        0x4 emodules/mod_mixture/mod_mixture.o
                0x00000000e9b05688                disp_backcar_new_format
 .bss.disp_backcar_old_format
                0x00000000e9b0568c        0x4 emodules/mod_mixture/mod_mixture.o
                0x00000000e9b0568c                disp_backcar_old_format
 .bss.disp_backcar_open_flag
                0x00000000e9b05690        0x4 emodules/mod_mixture/mod_mixture.o
                0x00000000e9b05690                disp_backcar_open_flag
 .bss.disp_backcar_out_to_backcar
                0x00000000e9b05694        0x4 emodules/mod_mixture/mod_mixture.o
                0x00000000e9b05694                disp_backcar_out_to_backcar
 .bss.disp_backcar_pic_used
                0x00000000e9b05698        0x4 emodules/mod_mixture/mod_mixture.o
                0x00000000e9b05698                disp_backcar_pic_used
 .bss.disp_backcar_show_flag
                0x00000000e9b0569c        0x4 emodules/mod_mixture/mod_mixture.o
                0x00000000e9b0569c                disp_backcar_show_flag
 .bss.disp_backcar_video_signal
                0x00000000e9b056a0        0x4 emodules/mod_mixture/mod_mixture.o
                0x00000000e9b056a0                disp_backcar_video_signal
 *fill*         0x00000000e9b056a4        0x4 
 .bss.disp_system_para
                0x00000000e9b056a8        0x8 emodules/mod_mixture/mod_mixture.o
                0x00000000e9b056a8                disp_system_para
 .bss.fheader   0x00000000e9b056b0       0x68 emodules/mod_mixture/mod_mixture.o
                0x00000000e9b056b0                fheader
 .bss.file_len  0x00000000e9b05718        0x4 emodules/mod_mixture/mod_mixture.o
                0x00000000e9b05718                file_len
 *fill*         0x00000000e9b0571c        0x4 
 .bss.fp        0x00000000e9b05720        0x8 emodules/mod_mixture/mod_mixture.o
 .bss.hvideo_lyr
                0x00000000e9b05728        0x8 emodules/mod_mixture/mod_mixture.o
                0x00000000e9b05728                hvideo_lyr
 .bss.is_det_backcar
                0x00000000e9b05730        0x1 emodules/mod_mixture/mod_mixture.o
                0x00000000e9b05730                is_det_backcar
 .bss.logo_is_running
                0x00000000e9b05731        0x1 emodules/mod_mixture/mod_mixture.o
                0x00000000e9b05731                logo_is_running
 .bss.mid_ced   0x00000000e9b05732        0x1 emodules/mod_mixture/mod_mixture.o
 *fill*         0x00000000e9b05733        0x5 
 .bss.mix_hced  0x00000000e9b05738        0x8 emodules/mod_mixture/mod_mixture.o
                0x00000000e9b05738                mix_hced
 .bss.mix_open_state
                0x00000000e9b05740        0x1 emodules/mod_mixture/mod_mixture.o
 *fill*         0x00000000e9b05741        0x7 
 .bss.mod_mixture_mp
                0x00000000e9b05748        0x2 emodules/mod_mixture/mod_mixture.o
                0x00000000e9b05748                mod_mixture_mp
 *fill*         0x00000000e9b0574a        0x6 
 .bss.plogo_data
                0x00000000e9b05750        0x8 emodules/mod_mixture/mod_mixture.o
                0x00000000e9b05750                plogo_data
 .bss.read_num  0x00000000e9b05758        0x4 emodules/mod_mixture/mod_mixture.o
                0x00000000e9b05758                read_num
 *fill*         0x00000000e9b0575c        0x4 
 .bss.save_logo_buf
                0x00000000e9b05760        0x8 emodules/mod_mixture/mod_mixture.o
                0x00000000e9b05760                save_logo_buf
 .bss.save_logo_part_mount
                0x00000000e9b05768        0x1 emodules/mod_mixture/mod_mixture.o
                0x00000000e9b05768                save_logo_part_mount
 *fill*         0x00000000e9b05769        0x3 
 .bss.save_logo_size
                0x00000000e9b0576c        0x4 emodules/mod_mixture/mod_mixture.o
                0x00000000e9b0576c                save_logo_size
 .bss.signal.17217
                0x00000000e9b05770        0x1 emodules/mod_mixture/mod_mixture.o
 *fill*         0x00000000e9b05771        0x3 
 .bss.src_height
                0x00000000e9b05774        0x4 emodules/mod_mixture/mod_mixture.o
                0x00000000e9b05774                src_height
 .bss.src_width
                0x00000000e9b05778        0x4 emodules/mod_mixture/mod_mixture.o
                0x00000000e9b05778                src_width
 .bss.g_bmp_size
                0x00000000e9b0577c        0x4 emodules/mod_mixture/bmp_parser/bmp.o
 .bss.g_pbmp_buf
                0x00000000e9b05780        0x8 emodules/mod_mixture/bmp_parser/bmp.o
 .bss.g_bmp_size
                0x00000000e9b05788        0x4 emodules/mod_mixture/backcarcheck/dis_bmp.o
 *fill*         0x00000000e9b0578c        0x4 
 .bss.g_pbmp_buf
                0x00000000e9b05790        0x8 emodules/mod_mixture/backcarcheck/dis_bmp.o
 .bss.mid_video_play
                0x00000000e9b05798        0x1 emodules/mod_mixture/backcarcheck/dis_checkio.o
 *fill*         0x00000000e9b05799        0x7 
 .bss.mod_video_play
                0x00000000e9b057a0        0x8 emodules/mod_mixture/backcarcheck/dis_checkio.o
 .bss.logo_layer
                0x00000000e9b057a8        0x8 emodules/mod_mixture/backcarcheck/dis_show_picture.o
 .bss.picture   0x00000000e9b057b0        0x8 emodules/mod_mixture/backcarcheck/dis_show_picture.o
 *(.gnu.linkonce.b.*)
 *(.sbss)
 *(.sbss.*)
 *(.gnu.linkonce.sb.*)
 *(.sbss2)
 *(.sbss2.*)
 *(.gnu.linkonce.sb2.*)
 *(COMMON)
 *(.scommon)
                0x00000000e9b057b8                . = ALIGN (0x4)
                0x00000000e9b057b8                __bss_end = ABSOLUTE (.)
                0x00000000e9b057b8                _end = ABSOLUTE (.)

MAGIC           0x00000000ffff0000       0x50
 *.o(.magic)
 .magic         0x00000000ffff0000       0x50 emodules/mod_mixture/magic.o
                0x00000000ffff0000                modinfo

.note
 *(.note)

.stab
 *(.stab)

.stabstr
 *(.stabstr)

.stab.excl
 *(.stab.excl)

.stab.exclstr
 *(.stab.exclstr)

.stab.index
 *(.stab.index)

.stab.indexstr
 *(.stab.indexstr)

.mdebug
 *(.mdebug)

.reginfo
 *(.reginfo)

.comment        0x0000000000000000       0x32
 *(.comment)
 .comment       0x0000000000000000       0x32 emodules/mod_mixture/mod_mixture.o
                                         0x33 (size before relaxing)
 .comment       0x0000000000000032       0x33 emodules/mod_mixture/magic.o
 .comment       0x0000000000000032       0x33 emodules/mod_mixture/bmp_parser/bmp.o
 .comment       0x0000000000000032       0x33 emodules/mod_mixture/bmp_parser/Parse_Picture.o
 .comment       0x0000000000000032       0x33 emodules/mod_mixture/backcarcheck/dis_bmp.o
 .comment       0x0000000000000032       0x33 emodules/mod_mixture/backcarcheck/dis_checkio.o
 .comment       0x0000000000000032       0x33 emodules/mod_mixture/backcarcheck/dis_Parse_Picture.o
 .comment       0x0000000000000032       0x33 emodules/mod_mixture/backcarcheck/dis_show_picture.o
 .comment       0x0000000000000032       0x33 ./elibrary/bin//libsyscall.a(syscall.o)
 .comment       0x0000000000000032       0x33 ./elibrary/bin//libsyscall.a(syscall_fops.o)
 .comment       0x0000000000000032       0x33 ./elibrary/bin//libminic.a(elibs_sprintf.o)
 .comment       0x0000000000000032       0x33 ./elibrary/bin//libminic.a(elibs_stdio.o)
 .comment       0x0000000000000032       0x33 ./elibrary/bin//libminic.a(elibs_stdlib.o)
 .comment       0x0000000000000032       0x33 ./elibrary/bin//libminic.a(elibs_string.o)
 .comment       0x0000000000000032       0x33 ./elibrary/bin//liblzma.a(az100.o)
 .comment       0x0000000000000032       0x33 ./elibrary/bin//liblzma.a(compress.o)
 .comment       0x0000000000000032       0x33 ./elibrary/bin//liblzma.a(LzmaLib.o)
 .comment       0x0000000000000032       0x33 ./elibrary/bin//liblzma.a(LzmaDec.o)
 .comment       0x0000000000000032       0x33 /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/../../../../riscv64-unknown-elf/lib/rv64imafdcxtheadc/lp64d/libc.a(lib_a-memcpy.o)
 .comment       0x0000000000000032       0x33 /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/../../../../riscv64-unknown-elf/lib/rv64imafdcxtheadc/lp64d/libc.a(lib_a-sysclose.o)
 .comment       0x0000000000000032       0x33 /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/../../../../riscv64-unknown-elf/lib/rv64imafdcxtheadc/lp64d/libc.a(lib_a-sysopen.o)
 .comment       0x0000000000000032       0x33 /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/../../../../riscv64-unknown-elf/lib/rv64imafdcxtheadc/lp64d/libc.a(lib_a-impure.o)

.ARM.attributes
 *(.ARM.attributes)

.riscv.attributes
                0x0000000000000000       0x48
 *(.riscv.attributes)
 .riscv.attributes
                0x0000000000000000       0x3d emodules/mod_mixture/mod_mixture.o
 .riscv.attributes
                0x000000000000003d       0x3d emodules/mod_mixture/magic.o
 .riscv.attributes
                0x000000000000007a       0x3d emodules/mod_mixture/bmp_parser/bmp.o
 .riscv.attributes
                0x00000000000000b7       0x3d emodules/mod_mixture/bmp_parser/Parse_Picture.o
 .riscv.attributes
                0x00000000000000f4       0x3d emodules/mod_mixture/backcarcheck/dis_bmp.o
 .riscv.attributes
                0x0000000000000131       0x3d emodules/mod_mixture/backcarcheck/dis_checkio.o
 .riscv.attributes
                0x000000000000016e       0x3d emodules/mod_mixture/backcarcheck/dis_Parse_Picture.o
 .riscv.attributes
                0x00000000000001ab       0x3d emodules/mod_mixture/backcarcheck/dis_show_picture.o
 .riscv.attributes
                0x00000000000001e8       0x3d ./elibrary/bin//libsyscall.a(syscall.o)
 .riscv.attributes
                0x0000000000000225       0x3d ./elibrary/bin//libsyscall.a(syscall_fops.o)
 .riscv.attributes
                0x0000000000000262       0x3d ./elibrary/bin//libminic.a(elibs_sprintf.o)
 .riscv.attributes
                0x000000000000029f       0x3d ./elibrary/bin//libminic.a(elibs_stdio.o)
 .riscv.attributes
                0x00000000000002dc       0x3d ./elibrary/bin//libminic.a(elibs_stdlib.o)
 .riscv.attributes
                0x0000000000000319       0x3d ./elibrary/bin//libminic.a(elibs_string.o)
 .riscv.attributes
                0x0000000000000356       0x3d ./elibrary/bin//liblzma.a(az100.o)
 .riscv.attributes
                0x0000000000000393       0x3d ./elibrary/bin//liblzma.a(compress.o)
 .riscv.attributes
                0x00000000000003d0       0x3d ./elibrary/bin//liblzma.a(LzmaLib.o)
 .riscv.attributes
                0x000000000000040d       0x3d ./elibrary/bin//liblzma.a(LzmaDec.o)
 .riscv.attributes
                0x000000000000044a       0x3e /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/../../../../riscv64-unknown-elf/lib/rv64imafdcxtheadc/lp64d/libc.a(lib_a-memcpy.o)
 .riscv.attributes
                0x0000000000000488       0x3e /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/../../../../riscv64-unknown-elf/lib/rv64imafdcxtheadc/lp64d/libc.a(lib_a-memset.o)
 .riscv.attributes
                0x00000000000004c6       0x3e /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/../../../../riscv64-unknown-elf/lib/rv64imafdcxtheadc/lp64d/libc.a(lib_a-sysclose.o)
 .riscv.attributes
                0x0000000000000504       0x3e /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/../../../../riscv64-unknown-elf/lib/rv64imafdcxtheadc/lp64d/libc.a(lib_a-sysopen.o)
 .riscv.attributes
                0x0000000000000542       0x3e /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/../../../../riscv64-unknown-elf/lib/rv64imafdcxtheadc/lp64d/libc.a(lib_a-impure.o)

/DISCARD/
 *(.note.GNU-stack)
 *(.ARM.exidx* .gnu.linkonce.armexidx.*)
OUTPUT(emodules/mod_mixture/mixture.o elf64-littleriscv)

.debug_info     0x0000000000000000    0x3479c
 .debug_info    0x0000000000000000     0xcc28 emodules/mod_mixture/mod_mixture.o
 .debug_info    0x000000000000cc28      0xfdc emodules/mod_mixture/magic.o
 .debug_info    0x000000000000dc04     0x17b3 emodules/mod_mixture/bmp_parser/bmp.o
 .debug_info    0x000000000000f3b7     0x1179 emodules/mod_mixture/bmp_parser/Parse_Picture.o
 .debug_info    0x0000000000010530     0x1843 emodules/mod_mixture/backcarcheck/dis_bmp.o
 .debug_info    0x0000000000011d73     0x18d9 emodules/mod_mixture/backcarcheck/dis_checkio.o
 .debug_info    0x000000000001364c     0x10bd emodules/mod_mixture/backcarcheck/dis_Parse_Picture.o
 .debug_info    0x0000000000014709     0x1d13 emodules/mod_mixture/backcarcheck/dis_show_picture.o
 .debug_info    0x000000000001641c    0x12bf0 ./elibrary/bin//libsyscall.a(syscall.o)
 .debug_info    0x000000000002900c     0x2f5e ./elibrary/bin//libsyscall.a(syscall_fops.o)
 .debug_info    0x000000000002bf6a      0x7d5 ./elibrary/bin//libminic.a(elibs_sprintf.o)
 .debug_info    0x000000000002c73f     0x2b1c ./elibrary/bin//libminic.a(elibs_stdio.o)
 .debug_info    0x000000000002f25b     0x1691 ./elibrary/bin//libminic.a(elibs_stdlib.o)
 .debug_info    0x00000000000308ec      0xb2d ./elibrary/bin//libminic.a(elibs_string.o)
 .debug_info    0x0000000000031419      0xf3c ./elibrary/bin//liblzma.a(az100.o)
 .debug_info    0x0000000000032355      0x1f3 ./elibrary/bin//liblzma.a(compress.o)
 .debug_info    0x0000000000032548      0x3dc ./elibrary/bin//liblzma.a(LzmaLib.o)
 .debug_info    0x0000000000032924     0x1e78 ./elibrary/bin//liblzma.a(LzmaDec.o)

.debug_abbrev   0x0000000000000000     0x3243
 .debug_abbrev  0x0000000000000000      0x645 emodules/mod_mixture/mod_mixture.o
 .debug_abbrev  0x0000000000000645      0x1db emodules/mod_mixture/magic.o
 .debug_abbrev  0x0000000000000820      0x357 emodules/mod_mixture/bmp_parser/bmp.o
 .debug_abbrev  0x0000000000000b77      0x241 emodules/mod_mixture/bmp_parser/Parse_Picture.o
 .debug_abbrev  0x0000000000000db8      0x372 emodules/mod_mixture/backcarcheck/dis_bmp.o
 .debug_abbrev  0x000000000000112a      0x2e3 emodules/mod_mixture/backcarcheck/dis_checkio.o
 .debug_abbrev  0x000000000000140d      0x241 emodules/mod_mixture/backcarcheck/dis_Parse_Picture.o
 .debug_abbrev  0x000000000000164e      0x2e8 emodules/mod_mixture/backcarcheck/dis_show_picture.o
 .debug_abbrev  0x0000000000001936      0x3a2 ./elibrary/bin//libsyscall.a(syscall.o)
 .debug_abbrev  0x0000000000001cd8      0x3ec ./elibrary/bin//libsyscall.a(syscall_fops.o)
 .debug_abbrev  0x00000000000020c4      0x14d ./elibrary/bin//libminic.a(elibs_sprintf.o)
 .debug_abbrev  0x0000000000002211      0x3d0 ./elibrary/bin//libminic.a(elibs_stdio.o)
 .debug_abbrev  0x00000000000025e1      0x2f3 ./elibrary/bin//libminic.a(elibs_stdlib.o)
 .debug_abbrev  0x00000000000028d4      0x155 ./elibrary/bin//libminic.a(elibs_string.o)
 .debug_abbrev  0x0000000000002a29      0x238 ./elibrary/bin//liblzma.a(az100.o)
 .debug_abbrev  0x0000000000002c61       0x88 ./elibrary/bin//liblzma.a(compress.o)
 .debug_abbrev  0x0000000000002ce9      0x13d ./elibrary/bin//liblzma.a(LzmaLib.o)
 .debug_abbrev  0x0000000000002e26      0x41d ./elibrary/bin//liblzma.a(LzmaDec.o)

.debug_loc      0x0000000000000000    0x1911f
 .debug_loc     0x0000000000000000     0x1fe6 emodules/mod_mixture/mod_mixture.o
 .debug_loc     0x0000000000001fe6      0xee7 emodules/mod_mixture/bmp_parser/bmp.o
 .debug_loc     0x0000000000002ecd      0x371 emodules/mod_mixture/bmp_parser/Parse_Picture.o
 .debug_loc     0x000000000000323e      0x8a2 emodules/mod_mixture/backcarcheck/dis_bmp.o
 .debug_loc     0x0000000000003ae0      0x73b emodules/mod_mixture/backcarcheck/dis_checkio.o
 .debug_loc     0x000000000000421b      0x158 emodules/mod_mixture/backcarcheck/dis_Parse_Picture.o
 .debug_loc     0x0000000000004373      0x284 emodules/mod_mixture/backcarcheck/dis_show_picture.o
 .debug_loc     0x00000000000045f7     0xda5d ./elibrary/bin//libsyscall.a(syscall.o)
 .debug_loc     0x0000000000012054     0x1325 ./elibrary/bin//libsyscall.a(syscall_fops.o)
 .debug_loc     0x0000000000013379      0x3f2 ./elibrary/bin//libminic.a(elibs_sprintf.o)
 .debug_loc     0x000000000001376b     0x25c5 ./elibrary/bin//libminic.a(elibs_stdio.o)
 .debug_loc     0x0000000000015d30      0x38b ./elibrary/bin//libminic.a(elibs_stdlib.o)
 .debug_loc     0x00000000000160bb      0x705 ./elibrary/bin//libminic.a(elibs_string.o)
 .debug_loc     0x00000000000167c0      0x296 ./elibrary/bin//liblzma.a(az100.o)
 .debug_loc     0x0000000000016a56      0x26b ./elibrary/bin//liblzma.a(compress.o)
 .debug_loc     0x0000000000016cc1      0x33b ./elibrary/bin//liblzma.a(LzmaLib.o)
 .debug_loc     0x0000000000016ffc     0x2123 ./elibrary/bin//liblzma.a(LzmaDec.o)

.debug_aranges  0x0000000000000000     0x2a80
 .debug_aranges
                0x0000000000000000      0x2c0 emodules/mod_mixture/mod_mixture.o
 .debug_aranges
                0x00000000000002c0       0x20 emodules/mod_mixture/magic.o
 .debug_aranges
                0x00000000000002e0       0xe0 emodules/mod_mixture/bmp_parser/bmp.o
 .debug_aranges
                0x00000000000003c0       0x50 emodules/mod_mixture/bmp_parser/Parse_Picture.o
 .debug_aranges
                0x0000000000000410       0xa0 emodules/mod_mixture/backcarcheck/dis_bmp.o
 .debug_aranges
                0x00000000000004b0      0x110 emodules/mod_mixture/backcarcheck/dis_checkio.o
 .debug_aranges
                0x00000000000005c0       0x40 emodules/mod_mixture/backcarcheck/dis_Parse_Picture.o
 .debug_aranges
                0x0000000000000600       0x60 emodules/mod_mixture/backcarcheck/dis_show_picture.o
 .debug_aranges
                0x0000000000000660     0x1840 ./elibrary/bin//libsyscall.a(syscall.o)
 .debug_aranges
                0x0000000000001ea0      0x260 ./elibrary/bin//libsyscall.a(syscall_fops.o)
 .debug_aranges
                0x0000000000002100       0x80 ./elibrary/bin//libminic.a(elibs_sprintf.o)
 .debug_aranges
                0x0000000000002180      0x480 ./elibrary/bin//libminic.a(elibs_stdio.o)
 .debug_aranges
                0x0000000000002600      0x140 ./elibrary/bin//libminic.a(elibs_stdlib.o)
 .debug_aranges
                0x0000000000002740      0x150 ./elibrary/bin//libminic.a(elibs_string.o)
 .debug_aranges
                0x0000000000002890       0x50 ./elibrary/bin//liblzma.a(az100.o)
 .debug_aranges
                0x00000000000028e0       0x40 ./elibrary/bin//liblzma.a(compress.o)
 .debug_aranges
                0x0000000000002920       0x60 ./elibrary/bin//liblzma.a(LzmaLib.o)
 .debug_aranges
                0x0000000000002980      0x100 ./elibrary/bin//liblzma.a(LzmaDec.o)

.debug_line     0x0000000000000000    0x22062
 .debug_line    0x0000000000000000     0x59e7 emodules/mod_mixture/mod_mixture.o
 .debug_line    0x00000000000059e7      0x2dc emodules/mod_mixture/magic.o
 .debug_line    0x0000000000005cc3     0x13ac emodules/mod_mixture/bmp_parser/bmp.o
 .debug_line    0x000000000000706f      0x769 emodules/mod_mixture/bmp_parser/Parse_Picture.o
 .debug_line    0x00000000000077d8      0xf59 emodules/mod_mixture/backcarcheck/dis_bmp.o
 .debug_line    0x0000000000008731      0xefa emodules/mod_mixture/backcarcheck/dis_checkio.o
 .debug_line    0x000000000000962b      0x5bd emodules/mod_mixture/backcarcheck/dis_Parse_Picture.o
 .debug_line    0x0000000000009be8     0x1058 emodules/mod_mixture/backcarcheck/dis_show_picture.o
 .debug_line    0x000000000000ac40     0xe68f ./elibrary/bin//libsyscall.a(syscall.o)
 .debug_line    0x00000000000192cf     0x1864 ./elibrary/bin//libsyscall.a(syscall_fops.o)
 .debug_line    0x000000000001ab33      0x495 ./elibrary/bin//libminic.a(elibs_sprintf.o)
 .debug_line    0x000000000001afc8     0x22a9 ./elibrary/bin//libminic.a(elibs_stdio.o)
 .debug_line    0x000000000001d271      0x6cb ./elibrary/bin//libminic.a(elibs_stdlib.o)
 .debug_line    0x000000000001d93c      0x5f9 ./elibrary/bin//libminic.a(elibs_string.o)
 .debug_line    0x000000000001df35      0x4e7 ./elibrary/bin//liblzma.a(az100.o)
 .debug_line    0x000000000001e41c      0x31c ./elibrary/bin//liblzma.a(compress.o)
 .debug_line    0x000000000001e738      0x280 ./elibrary/bin//liblzma.a(LzmaLib.o)
 .debug_line    0x000000000001e9b8     0x36aa ./elibrary/bin//liblzma.a(LzmaDec.o)

.debug_str      0x0000000000000000    0x101ea
 .debug_str     0x0000000000000000     0xafb0 emodules/mod_mixture/mod_mixture.o
                                       0xb7e8 (size before relaxing)
 .debug_str     0x000000000000afb0       0x63 emodules/mod_mixture/magic.o
                                        0xa4c (size before relaxing)
 .debug_str     0x000000000000b013      0x23a emodules/mod_mixture/bmp_parser/bmp.o
                                        0xcf6 (size before relaxing)
 .debug_str     0x000000000000b24d       0x84 emodules/mod_mixture/bmp_parser/Parse_Picture.o
                                        0xbe1 (size before relaxing)
 .debug_str     0x000000000000b2d1       0xa3 emodules/mod_mixture/backcarcheck/dis_bmp.o
                                       0x13e2 (size before relaxing)
 .debug_str     0x000000000000b374      0x5e1 emodules/mod_mixture/backcarcheck/dis_checkio.o
                                       0x17ea (size before relaxing)
 .debug_str     0x000000000000b955       0x6a emodules/mod_mixture/backcarcheck/dis_Parse_Picture.o
                                        0xbaf (size before relaxing)
 .debug_str     0x000000000000b9bf       0xb3 emodules/mod_mixture/backcarcheck/dis_show_picture.o
                                       0x291f (size before relaxing)
 .debug_str     0x000000000000ba72     0x2f33 ./elibrary/bin//libsyscall.a(syscall.o)
                                       0x3c33 (size before relaxing)
 .debug_str     0x000000000000e9a5      0x2c0 ./elibrary/bin//libsyscall.a(syscall_fops.o)
                                       0x1068 (size before relaxing)
 .debug_str     0x000000000000ec65      0x285 ./elibrary/bin//libminic.a(elibs_sprintf.o)
                                        0x5d3 (size before relaxing)
 .debug_str     0x000000000000eeea      0xa47 ./elibrary/bin//libminic.a(elibs_stdio.o)
                                       0x1624 (size before relaxing)
 .debug_str     0x000000000000f931       0xfc ./elibrary/bin//libminic.a(elibs_stdlib.o)
                                        0xd03 (size before relaxing)
 .debug_str     0x000000000000fa2d      0x17d ./elibrary/bin//libminic.a(elibs_string.o)
                                        0x6d8 (size before relaxing)
 .debug_str     0x000000000000fbaa       0x80 ./elibrary/bin//liblzma.a(az100.o)
                                        0xa4a (size before relaxing)
 .debug_str     0x000000000000fc2a       0x83 ./elibrary/bin//liblzma.a(compress.o)
                                        0x3bd (size before relaxing)
 .debug_str     0x000000000000fcad      0x19f ./elibrary/bin//liblzma.a(LzmaLib.o)
                                        0x4cb (size before relaxing)
 .debug_str     0x000000000000fe4c      0x39e ./elibrary/bin//liblzma.a(LzmaDec.o)
                                        0xbc3 (size before relaxing)

.debug_frame    0x0000000000000000     0x6578
 .debug_frame   0x0000000000000000      0x840 emodules/mod_mixture/mod_mixture.o
 .debug_frame   0x0000000000000840      0x238 emodules/mod_mixture/bmp_parser/bmp.o
 .debug_frame   0x0000000000000a78       0x90 emodules/mod_mixture/bmp_parser/Parse_Picture.o
 .debug_frame   0x0000000000000b08      0x168 emodules/mod_mixture/backcarcheck/dis_bmp.o
 .debug_frame   0x0000000000000c70      0x278 emodules/mod_mixture/backcarcheck/dis_checkio.o
 .debug_frame   0x0000000000000ee8       0x58 emodules/mod_mixture/backcarcheck/dis_Parse_Picture.o
 .debug_frame   0x0000000000000f40       0xf0 emodules/mod_mixture/backcarcheck/dis_show_picture.o
 .debug_frame   0x0000000000001030     0x3c60 ./elibrary/bin//libsyscall.a(syscall.o)
 .debug_frame   0x0000000000004c90      0x5b8 ./elibrary/bin//libsyscall.a(syscall_fops.o)
 .debug_frame   0x0000000000005248       0xe0 ./elibrary/bin//libminic.a(elibs_sprintf.o)
 .debug_frame   0x0000000000005328      0xa28 ./elibrary/bin//libminic.a(elibs_stdio.o)
 .debug_frame   0x0000000000005d50      0x1e0 ./elibrary/bin//libminic.a(elibs_stdlib.o)
 .debug_frame   0x0000000000005f30      0x1e8 ./elibrary/bin//libminic.a(elibs_string.o)
 .debug_frame   0x0000000000006118       0x80 ./elibrary/bin//liblzma.a(az100.o)
 .debug_frame   0x0000000000006198       0x68 ./elibrary/bin//liblzma.a(compress.o)
 .debug_frame   0x0000000000006200       0x98 ./elibrary/bin//liblzma.a(LzmaLib.o)
 .debug_frame   0x0000000000006298      0x2e0 ./elibrary/bin//liblzma.a(LzmaDec.o)

Cross Reference Table

Symbol                                            File
AZ100_Compress                                    ./elibrary/bin//liblzma.a(compress.o)
AZ100_DataUncompress                              ./elibrary/bin//liblzma.a(compress.o)
                                                  ./elibrary/bin//liblzma.a(az100.o)
AZ100_GetUncompressSize                           ./elibrary/bin//liblzma.a(az100.o)
                                                  emodules/mod_mixture/backcarcheck/dis_bmp.o
                                                  emodules/mod_mixture/bmp_parser/bmp.o
                                                  emodules/mod_mixture/mod_mixture.o
AZ100_IsCompress                                  ./elibrary/bin//liblzma.a(az100.o)
                                                  emodules/mod_mixture/backcarcheck/dis_bmp.o
                                                  emodules/mod_mixture/bmp_parser/bmp.o
                                                  emodules/mod_mixture/mod_mixture.o
AZ100_Uncompress                                  ./elibrary/bin//liblzma.a(az100.o)
                                                  emodules/mod_mixture/backcarcheck/dis_bmp.o
                                                  emodules/mod_mixture/bmp_parser/bmp.o
                                                  emodules/mod_mixture/mod_mixture.o
Bt3Zip_MatchFinder_GetMatches                     ./elibrary/bin//liblzma.a(LzFind.o)
Bt3Zip_MatchFinder_Skip                           ./elibrary/bin//liblzma.a(LzFind.o)
GetMatchesSpec1                                   ./elibrary/bin//liblzma.a(LzFind.o)
Hc3Zip_MatchFinder_GetMatches                     ./elibrary/bin//liblzma.a(LzFind.o)
Hc3Zip_MatchFinder_Skip                           ./elibrary/bin//liblzma.a(LzFind.o)
LoadDataFromFile                                  emodules/mod_mixture/mod_mixture.o
LzmaCompress                                      ./elibrary/bin//liblzma.a(LzmaLib.o)
                                                  ./elibrary/bin//liblzma.a(compress.o)
LzmaDec_Allocate                                  ./elibrary/bin//liblzma.a(LzmaDec.o)
LzmaDec_AllocateProbs                             ./elibrary/bin//liblzma.a(LzmaDec.o)
LzmaDec_DecodeToBuf                               ./elibrary/bin//liblzma.a(LzmaDec.o)
LzmaDec_DecodeToDic                               ./elibrary/bin//liblzma.a(LzmaDec.o)
LzmaDec_Free                                      ./elibrary/bin//liblzma.a(LzmaDec.o)
LzmaDec_FreeProbs                                 ./elibrary/bin//liblzma.a(LzmaDec.o)
LzmaDec_Init                                      ./elibrary/bin//liblzma.a(LzmaDec.o)
LzmaDec_InitDicAndState                           ./elibrary/bin//liblzma.a(LzmaDec.o)
LzmaDecode                                        ./elibrary/bin//liblzma.a(LzmaDec.o)
                                                  ./elibrary/bin//liblzma.a(LzmaLib.o)
LzmaEncProps_GetDictSize                          ./elibrary/bin//liblzma.a(LzmaEnc.o)
LzmaEncProps_Init                                 ./elibrary/bin//liblzma.a(LzmaEnc.o)
                                                  ./elibrary/bin//liblzma.a(LzmaLib.o)
LzmaEncProps_Normalize                            ./elibrary/bin//liblzma.a(LzmaEnc.o)
LzmaEnc_CodeOneMemBlock                           ./elibrary/bin//liblzma.a(LzmaEnc.o)
LzmaEnc_Construct                                 ./elibrary/bin//liblzma.a(LzmaEnc.o)
LzmaEnc_Create                                    ./elibrary/bin//liblzma.a(LzmaEnc.o)
LzmaEnc_Destroy                                   ./elibrary/bin//liblzma.a(LzmaEnc.o)
LzmaEnc_Destruct                                  ./elibrary/bin//liblzma.a(LzmaEnc.o)
LzmaEnc_Encode                                    ./elibrary/bin//liblzma.a(LzmaEnc.o)
LzmaEnc_FastPosInit                               ./elibrary/bin//liblzma.a(LzmaEnc.o)
LzmaEnc_Finish                                    ./elibrary/bin//liblzma.a(LzmaEnc.o)
LzmaEnc_FreeLits                                  ./elibrary/bin//liblzma.a(LzmaEnc.o)
LzmaEnc_GetCurBuf                                 ./elibrary/bin//liblzma.a(LzmaEnc.o)
LzmaEnc_GetNumAvailableBytes                      ./elibrary/bin//liblzma.a(LzmaEnc.o)
LzmaEnc_Init                                      ./elibrary/bin//liblzma.a(LzmaEnc.o)
LzmaEnc_InitPriceTables                           ./elibrary/bin//liblzma.a(LzmaEnc.o)
LzmaEnc_InitPrices                                ./elibrary/bin//liblzma.a(LzmaEnc.o)
LzmaEnc_MemEncode                                 ./elibrary/bin//liblzma.a(LzmaEnc.o)
LzmaEnc_MemPrepare                                ./elibrary/bin//liblzma.a(LzmaEnc.o)
LzmaEnc_PrepareForLzma2                           ./elibrary/bin//liblzma.a(LzmaEnc.o)
LzmaEnc_RestoreState                              ./elibrary/bin//liblzma.a(LzmaEnc.o)
LzmaEnc_SaveState                                 ./elibrary/bin//liblzma.a(LzmaEnc.o)
LzmaEnc_SetProps                                  ./elibrary/bin//liblzma.a(LzmaEnc.o)
LzmaEnc_WriteProperties                           ./elibrary/bin//liblzma.a(LzmaEnc.o)
LzmaEncode                                        ./elibrary/bin//liblzma.a(LzmaEnc.o)
                                                  ./elibrary/bin//liblzma.a(LzmaLib.o)
LzmaProps_Decode                                  ./elibrary/bin//liblzma.a(LzmaDec.o)
LzmaUncompress                                    ./elibrary/bin//liblzma.a(LzmaLib.o)
                                                  ./elibrary/bin//liblzma.a(compress.o)
MatchFinder_Construct                             ./elibrary/bin//liblzma.a(LzFind.o)
                                                  ./elibrary/bin//liblzma.a(LzmaEnc.o)
MatchFinder_Create                                ./elibrary/bin//liblzma.a(LzFind.o)
                                                  ./elibrary/bin//liblzma.a(LzmaEnc.o)
MatchFinder_CreateVTable                          ./elibrary/bin//liblzma.a(LzFind.o)
                                                  ./elibrary/bin//liblzma.a(LzmaEnc.o)
MatchFinder_Free                                  ./elibrary/bin//liblzma.a(LzFind.o)
                                                  ./elibrary/bin//liblzma.a(LzmaEnc.o)
MatchFinder_GetIndexByte                          ./elibrary/bin//liblzma.a(LzFind.o)
MatchFinder_GetNumAvailableBytes                  ./elibrary/bin//liblzma.a(LzFind.o)
MatchFinder_GetPointerToCurrentPos                ./elibrary/bin//liblzma.a(LzFind.o)
MatchFinder_Init                                  ./elibrary/bin//liblzma.a(LzFind.o)
MatchFinder_MoveBlock                             ./elibrary/bin//liblzma.a(LzFind.o)
MatchFinder_NeedMove                              ./elibrary/bin//liblzma.a(LzFind.o)
MatchFinder_Normalize3                            ./elibrary/bin//liblzma.a(LzFind.o)
MatchFinder_ReadIfRequired                        ./elibrary/bin//liblzma.a(LzFind.o)
MatchFinder_ReduceOffsets                         ./elibrary/bin//liblzma.a(LzFind.o)
Mod_Mixture_MClose                                emodules/mod_mixture/mod_mixture.o
                                                  emodules/mod_mixture/magic.o
Mod_Mixture_MExit                                 emodules/mod_mixture/mod_mixture.o
                                                  emodules/mod_mixture/magic.o
Mod_Mixture_MInit                                 emodules/mod_mixture/mod_mixture.o
                                                  emodules/mod_mixture/magic.o
Mod_Mixture_MIoctrl                               emodules/mod_mixture/mod_mixture.o
                                                  emodules/mod_mixture/magic.o
Mod_Mixture_MOpen                                 emodules/mod_mixture/mod_mixture.o
                                                  emodules/mod_mixture/magic.o
Mod_Mixture_MRead                                 emodules/mod_mixture/mod_mixture.o
                                                  emodules/mod_mixture/magic.o
Mod_Mixture_MWrite                                emodules/mod_mixture/mod_mixture.o
                                                  emodules/mod_mixture/magic.o
Parse_Pic_BMP_ByBuffer                            emodules/mod_mixture/bmp_parser/Parse_Picture.o
Parse_Pic_BMP_ByPath                              emodules/mod_mixture/bmp_parser/Parse_Picture.o
                                                  emodules/mod_mixture/mod_mixture.o
Parse_Pic_BMP_ByRam                               emodules/mod_mixture/bmp_parser/Parse_Picture.o
ReadDataFromFile                                  emodules/mod_mixture/mod_mixture.o
__close_backcar_str_pic                           emodules/mod_mixture/backcarcheck/dis_show_picture.o
__close_nosignal_pic                              emodules/mod_mixture/backcarcheck/dis_show_picture.o
                                                  emodules/mod_mixture/mod_mixture.o
__libc_init_array                                 ./elibrary/bin//libsyscall.a(syscall_fops.o)
__malloc_lock                                     ./elibrary/bin//libsyscall.a(syscall_fops.o)
__malloc_unlock                                   ./elibrary/bin//libsyscall.a(syscall_fops.o)
__show_backcar_str_pic                            emodules/mod_mixture/backcarcheck/dis_show_picture.o
__show_nosignal_pic                               emodules/mod_mixture/backcarcheck/dis_show_picture.o
                                                  emodules/mod_mixture/mod_mixture.o
_calloc_r                                         ./elibrary/bin//libsyscall.a(syscall_fops.o)
_close_r                                          ./elibrary/bin//libsyscall.a(syscall_fops.o)
                                                  /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/../../../../riscv64-unknown-elf/lib/rv64imafdcxtheadc/lp64d/libc.a(lib_a-sysclose.o)
_disp_init_para                                   emodules/mod_mixture/mod_mixture.o
_exit                                             ./elibrary/bin//libminic.a(elibs_stdlib.o)
_free_r                                           ./elibrary/bin//libsyscall.a(syscall_fops.o)
_fstat_r                                          ./elibrary/bin//libsyscall.a(syscall_fops.o)
_getpid                                           ./elibrary/bin//libminic.a(elibs_stdlib.o)
_gettimeofday_r                                   ./elibrary/bin//libsyscall.a(syscall_fops.o)
_global_impure_ptr                                /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/../../../../riscv64-unknown-elf/lib/rv64imafdcxtheadc/lp64d/libc.a(lib_a-impure.o)
_impure_ptr                                       /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/../../../../riscv64-unknown-elf/lib/rv64imafdcxtheadc/lp64d/libc.a(lib_a-impure.o)
                                                  /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/../../../../riscv64-unknown-elf/lib/rv64imafdcxtheadc/lp64d/libc.a(lib_a-syswrite.o)
                                                  /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/../../../../riscv64-unknown-elf/lib/rv64imafdcxtheadc/lp64d/libc.a(lib_a-sysopen.o)
                                                  /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/../../../../riscv64-unknown-elf/lib/rv64imafdcxtheadc/lp64d/libc.a(lib_a-sysclose.o)
_isatty                                           ./elibrary/bin//libsyscall.a(syscall_fops.o)
_isatty_r                                         ./elibrary/bin//libsyscall.a(syscall_fops.o)
_kill                                             ./elibrary/bin//libminic.a(elibs_stdlib.o)
_lseek_r                                          ./elibrary/bin//libsyscall.a(syscall_fops.o)
_malloc_r                                         ./elibrary/bin//libsyscall.a(syscall_fops.o)
_mkdir_r                                          ./elibrary/bin//libsyscall.a(syscall_fops.o)
_open_r                                           ./elibrary/bin//libsyscall.a(syscall_fops.o)
                                                  /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/../../../../riscv64-unknown-elf/lib/rv64imafdcxtheadc/lp64d/libc.a(lib_a-sysopen.o)
_read_r                                           ./elibrary/bin//libsyscall.a(syscall_fops.o)
_realloc_r                                        ./elibrary/bin//libsyscall.a(syscall_fops.o)
_rename_r                                         ./elibrary/bin//libsyscall.a(syscall_fops.o)
_sbrk                                             ./elibrary/bin//libminic.a(elibs_stdlib.o)
_stat_r                                           ./elibrary/bin//libsyscall.a(syscall_fops.o)
_system                                           ./elibrary/bin//libsyscall.a(syscall_fops.o)
_unlink_r                                         ./elibrary/bin//libsyscall.a(syscall_fops.o)
_write                                            ./elibrary/bin//libminic.a(elibs_stdlib.o)
_write_r                                          ./elibrary/bin//libsyscall.a(syscall_fops.o)
                                                  /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/../../../../riscv64-unknown-elf/lib/rv64imafdcxtheadc/lp64d/libc.a(lib_a-syswrite.o)
access                                            ./elibrary/bin//libsyscall.a(syscall_fops.o)
backcar_check_io                                  emodules/mod_mixture/mod_mixture.o
backcar_det_time_cnt                              emodules/mod_mixture/mod_mixture.o
backtrace                                         ./elibrary/bin//libsyscall.a(syscall.o)
check_backcar_io                                  emodules/mod_mixture/mod_mixture.o
check_if_lowpower                                 emodules/mod_mixture/startup_check/startup_check.o
close                                             /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/../../../../riscv64-unknown-elf/lib/rv64imafdcxtheadc/lp64d/libc.a(lib_a-sysclose.o)
                                                  emodules/mod_mixture/startup_check/startup_check.o
                                                  emodules/mod_mixture/mod_mixture.o
close_bmp                                         emodules/mod_mixture/bmp_parser/bmp.o
                                                  emodules/mod_mixture/bmp_parser/Parse_Picture.o
close_bmp_animation                               emodules/mod_mixture/mod_mixture.o
close_bmp_logo                                    emodules/mod_mixture/mod_mixture.o
close_jpg_animation                               emodules/mod_mixture/mod_mixture.o
close_jpg_logo                                    emodules/mod_mixture/mod_mixture.o
close_movie                                       emodules/mod_mixture/mod_mixture.o
close_ram_bmp                                     emodules/mod_mixture/bmp_parser/bmp.o
closedir                                          ./elibrary/bin//libsyscall.a(syscall_fops.o)
det_back_car_flag                                 emodules/mod_mixture/mod_mixture.o
dis_Parse_Pic_BMP_ByBuffer                        emodules/mod_mixture/backcarcheck/dis_Parse_Picture.o
dis_Parse_Pic_BMP_ByPath                          emodules/mod_mixture/backcarcheck/dis_Parse_Picture.o
                                                  emodules/mod_mixture/backcarcheck/dis_show_picture.o
dis_close_bmp                                     emodules/mod_mixture/backcarcheck/dis_bmp.o
                                                  emodules/mod_mixture/backcarcheck/dis_Parse_Picture.o
dis_get_bitcount                                  emodules/mod_mixture/backcarcheck/dis_bmp.o
                                                  emodules/mod_mixture/backcarcheck/dis_Parse_Picture.o
dis_get_height                                    emodules/mod_mixture/backcarcheck/dis_bmp.o
                                                  emodules/mod_mixture/backcarcheck/dis_Parse_Picture.o
dis_get_matrix                                    emodules/mod_mixture/backcarcheck/dis_bmp.o
                                                  emodules/mod_mixture/backcarcheck/dis_Parse_Picture.o
dis_get_rowsize                                   emodules/mod_mixture/backcarcheck/dis_bmp.o
                                                  emodules/mod_mixture/backcarcheck/dis_Parse_Picture.o
dis_get_width                                     emodules/mod_mixture/backcarcheck/dis_bmp.o
                                                  emodules/mod_mixture/backcarcheck/dis_Parse_Picture.o
dis_open_bmp                                      emodules/mod_mixture/backcarcheck/dis_bmp.o
                                                  emodules/mod_mixture/backcarcheck/dis_Parse_Picture.o
dis_read_pixel                                    emodules/mod_mixture/backcarcheck/dis_bmp.o
disp_backcar_dev_open                             emodules/mod_mixture/mod_mixture.o
disp_backcar_dvr_event_status                     emodules/mod_mixture/mod_mixture.o
disp_backcar_fd                                   emodules/mod_mixture/mod_mixture.o
disp_backcar_into_backcar                         emodules/mod_mixture/mod_mixture.o
disp_backcar_new_format                           emodules/mod_mixture/mod_mixture.o
disp_backcar_old_format                           emodules/mod_mixture/mod_mixture.o
disp_backcar_open_flag                            emodules/mod_mixture/mod_mixture.o
disp_backcar_out_to_backcar                       emodules/mod_mixture/mod_mixture.o
disp_backcar_pic_used                             emodules/mod_mixture/mod_mixture.o
disp_backcar_show_flag                            emodules/mod_mixture/mod_mixture.o
disp_backcar_video_signal                         emodules/mod_mixture/mod_mixture.o
disp_system_para                                  emodules/mod_mixture/mod_mixture.o
display_init_backcar_state                        emodules/mod_mixture/mod_mixture.o
display_into_backcar_state                        emodules/mod_mixture/mod_mixture.o
display_uninit_backcar_state                      emodules/mod_mixture/mod_mixture.o
dot_end_x                                         emodules/mod_mixture/mod_mixture.o
dot_start_y                                       emodules/mod_mixture/mod_mixture.o
dup                                               ./elibrary/bin//libsyscall.a(syscall_fops.o)
dup2                                              ./elibrary/bin//libsyscall.a(syscall_fops.o)
eLIBs_DirEnt2Attr                                 ./elibrary/bin//libminic.a(elibs_stdio.o)
eLIBs_DirEnt2Name                                 ./elibrary/bin//libminic.a(elibs_stdio.o)
eLIBs_DirEnt2Size                                 ./elibrary/bin//libminic.a(elibs_stdio.o)
eLIBs_DirEnt2Time                                 ./elibrary/bin//libminic.a(elibs_stdio.o)
eLIBs_GetDate                                     ./elibrary/bin//libminic.a(elibs_stdio.o)
eLIBs_GetDevName                                  ./elibrary/bin//libminic.a(elibs_stdio.o)
eLIBs_GetDevParts                                 ./elibrary/bin//libminic.a(elibs_stdio.o)
eLIBs_GetDirATime                                 ./elibrary/bin//libminic.a(elibs_stdio.o)
eLIBs_GetDirCTime                                 ./elibrary/bin//libminic.a(elibs_stdio.o)
eLIBs_GetDirMTime                                 ./elibrary/bin//libminic.a(elibs_stdio.o)
eLIBs_GetDirSize                                  ./elibrary/bin//libminic.a(elibs_stdio.o)
eLIBs_GetDiskArray                                ./elibrary/bin//libminic.a(elibs_stdio.o)
eLIBs_GetFSAttribute                              ./elibrary/bin//libminic.a(elibs_stdio.o)
eLIBs_GetFSCharset                                ./elibrary/bin//libminic.a(elibs_stdio.o)
eLIBs_GetFSName                                   ./elibrary/bin//libminic.a(elibs_stdio.o)
eLIBs_GetFSNameLen                                ./elibrary/bin//libminic.a(elibs_stdio.o)
eLIBs_GetFileATime                                ./elibrary/bin//libminic.a(elibs_stdio.o)
eLIBs_GetFileAttributes                           ./elibrary/bin//libminic.a(elibs_stdio.o)
eLIBs_GetFileCTime                                ./elibrary/bin//libminic.a(elibs_stdio.o)
eLIBs_GetFileMTime                                ./elibrary/bin//libminic.a(elibs_stdio.o)
eLIBs_GetFileSize                                 ./elibrary/bin//libminic.a(elibs_stdio.o)
eLIBs_GetNumFiles                                 ./elibrary/bin//libminic.a(elibs_stdio.o)
eLIBs_GetNumSubItems                              ./elibrary/bin//libminic.a(elibs_stdio.o)
eLIBs_GetPTName                                   ./elibrary/bin//libminic.a(elibs_stdio.o)
eLIBs_GetTime                                     ./elibrary/bin//libminic.a(elibs_stdio.o)
eLIBs_GetVolClustSize                             ./elibrary/bin//libminic.a(elibs_stdio.o)
eLIBs_GetVolFSpace                                ./elibrary/bin//libminic.a(elibs_stdio.o)
eLIBs_GetVolName                                  ./elibrary/bin//libminic.a(elibs_stdio.o)
eLIBs_GetVolNameCharset                           ./elibrary/bin//libminic.a(elibs_stdio.o)
eLIBs_GetVolNameLen                               ./elibrary/bin//libminic.a(elibs_stdio.o)
eLIBs_GetVolTSpace                                ./elibrary/bin//libminic.a(elibs_stdio.o)
eLIBs_IsPartFormated                              ./elibrary/bin//libminic.a(elibs_stdio.o)
                                                  emodules/mod_mixture/mod_mixture.o
eLIBs_SetFileAttributes                           ./elibrary/bin//libminic.a(elibs_stdio.o)
eLIBs_atof                                        ./elibrary/bin//libminic.a(elibs_stdlib.o)
eLIBs_atoi                                        ./elibrary/bin//libminic.a(elibs_stdlib.o)
                                                  emodules/mod_mixture/mod_mixture.o
eLIBs_closedir                                    ./elibrary/bin//libminic.a(elibs_stdio.o)
eLIBs_disklock                                    ./elibrary/bin//libminic.a(elibs_stdio.o)
eLIBs_diskunlock                                  ./elibrary/bin//libminic.a(elibs_stdio.o)
eLIBs_fclose                                      ./elibrary/bin//libminic.a(elibs_stdio.o)
                                                  emodules/mod_mixture/backcarcheck/dis_bmp.o
                                                  emodules/mod_mixture/bmp_parser/bmp.o
                                                  emodules/mod_mixture/startup_check/startup_check.o
                                                  emodules/mod_mixture/mod_mixture.o
eLIBs_fd2file                                     ./elibrary/bin//libminic.a(elibs_stdio.o)
eLIBs_feof                                        ./elibrary/bin//libminic.a(elibs_stdio.o)
eLIBs_ferrclr                                     ./elibrary/bin//libminic.a(elibs_stdio.o)
eLIBs_ferror                                      ./elibrary/bin//libminic.a(elibs_stdio.o)
eLIBs_fgetc                                       ./elibrary/bin//libminic.a(elibs_stdio.o)
eLIBs_fgets                                       ./elibrary/bin//libminic.a(elibs_stdio.o)
eLIBs_file2fd                                     ./elibrary/bin//libminic.a(elibs_stdio.o)
eLIBs_fioctrl                                     ./elibrary/bin//libminic.a(elibs_stdio.o)
eLIBs_fllseek                                     ./elibrary/bin//libminic.a(elibs_stdio.o)
                                                  emodules/mod_mixture/mod_mixture.o
eLIBs_flltell                                     ./elibrary/bin//libminic.a(elibs_stdio.o)
                                                  emodules/mod_mixture/mod_mixture.o
eLIBs_fopen                                       ./elibrary/bin//libminic.a(elibs_stdio.o)
                                                  emodules/mod_mixture/backcarcheck/dis_bmp.o
                                                  emodules/mod_mixture/bmp_parser/bmp.o
                                                  emodules/mod_mixture/startup_check/startup_check.o
                                                  emodules/mod_mixture/mod_mixture.o
eLIBs_format                                      ./elibrary/bin//libminic.a(elibs_stdio.o)
eLIBs_fputc                                       ./elibrary/bin//libminic.a(elibs_stdio.o)
eLIBs_fputs                                       ./elibrary/bin//libminic.a(elibs_stdio.o)
eLIBs_fread                                       ./elibrary/bin//libminic.a(elibs_stdio.o)
                                                  emodules/mod_mixture/backcarcheck/dis_bmp.o
                                                  emodules/mod_mixture/bmp_parser/bmp.o
                                                  emodules/mod_mixture/startup_check/startup_check.o
                                                  emodules/mod_mixture/mod_mixture.o
eLIBs_free                                        ./elibrary/bin//libminic.a(elibs_stdlib.o)
                                                  ./elibrary/bin//liblzma.a(LzmaLib.o)
                                                  emodules/mod_mixture/backcarcheck/dis_show_picture.o
                                                  emodules/mod_mixture/bmp_parser/bmp.o
                                                  emodules/mod_mixture/startup_check/startup_check.o
                                                  emodules/mod_mixture/mod_mixture.o
eLIBs_fseek                                       ./elibrary/bin//libminic.a(elibs_stdio.o)
                                                  emodules/mod_mixture/backcarcheck/dis_bmp.o
                                                  emodules/mod_mixture/bmp_parser/bmp.o
                                                  emodules/mod_mixture/startup_check/startup_check.o
                                                  emodules/mod_mixture/mod_mixture.o
eLIBs_fstat                                       ./elibrary/bin//libminic.a(elibs_stdio.o)
eLIBs_fsync                                       ./elibrary/bin//libminic.a(elibs_stdio.o)
eLIBs_ftell                                       ./elibrary/bin//libminic.a(elibs_stdio.o)
                                                  emodules/mod_mixture/backcarcheck/dis_bmp.o
                                                  emodules/mod_mixture/bmp_parser/bmp.o
                                                  emodules/mod_mixture/startup_check/startup_check.o
                                                  emodules/mod_mixture/mod_mixture.o
eLIBs_ftruncate                                   ./elibrary/bin//libminic.a(elibs_stdio.o)
eLIBs_fwrite                                      ./elibrary/bin//libminic.a(elibs_stdio.o)
eLIBs_int2str_dec                                 ./elibrary/bin//libminic.a(elibs_stdlib.o)
eLIBs_int2str_hex                                 ./elibrary/bin//libminic.a(elibs_stdlib.o)
eLIBs_isspace                                     ./elibrary/bin//libminic.a(elibs_stdlib.o)
eLIBs_malloc                                      ./elibrary/bin//libminic.a(elibs_stdlib.o)
                                                  ./elibrary/bin//liblzma.a(LzmaLib.o)
                                                  emodules/mod_mixture/backcarcheck/dis_show_picture.o
                                                  emodules/mod_mixture/bmp_parser/bmp.o
                                                  emodules/mod_mixture/startup_check/startup_check.o
                                                  emodules/mod_mixture/mod_mixture.o
eLIBs_memclr                                      ./elibrary/bin//libminic.a(elibs_string.o)
eLIBs_memcmp                                      ./elibrary/bin//libminic.a(elibs_string.o)
eLIBs_memcpy                                      ./elibrary/bin//libminic.a(elibs_string.o)
                                                  ./elibrary/bin//libminic.a(elibs_stdio.o)
                                                  emodules/mod_mixture/backcarcheck/dis_bmp.o
                                                  emodules/mod_mixture/bmp_parser/bmp.o
eLIBs_memmove                                     ./elibrary/bin//libminic.a(elibs_string.o)
eLIBs_memset                                      ./elibrary/bin//libminic.a(elibs_string.o)
                                                  emodules/mod_mixture/backcarcheck/dis_show_picture.o
                                                  emodules/mod_mixture/backcarcheck/dis_Parse_Picture.o
                                                  emodules/mod_mixture/backcarcheck/dis_bmp.o
                                                  emodules/mod_mixture/bmp_parser/Parse_Picture.o
                                                  emodules/mod_mixture/bmp_parser/bmp.o
                                                  emodules/mod_mixture/startup_check/startup_check.o
                                                  emodules/mod_mixture/mod_mixture.o
eLIBs_mkdir                                       ./elibrary/bin//libminic.a(elibs_stdio.o)
eLIBs_open                                        ./elibrary/bin//libminic.a(elibs_stdio.o)
eLIBs_opendir                                     ./elibrary/bin//libminic.a(elibs_stdio.o)
eLIBs_printf                                      ./elibrary/bin//libminic.a(elibs_stdio.o)
                                                  emodules/mod_mixture/mod_mixture.o
eLIBs_ramdom                                      ./elibrary/bin//libminic.a(elibs_stdlib.o)
eLIBs_readdir                                     ./elibrary/bin//libminic.a(elibs_stdio.o)
eLIBs_realloc                                     ./elibrary/bin//libminic.a(elibs_stdlib.o)
eLIBs_remove                                      ./elibrary/bin//libminic.a(elibs_stdio.o)
eLIBs_rename                                      ./elibrary/bin//libminic.a(elibs_stdio.o)
eLIBs_rewinddir                                   ./elibrary/bin//libminic.a(elibs_stdio.o)
eLIBs_rmdir                                       ./elibrary/bin//libminic.a(elibs_stdio.o)
eLIBs_scnprintf                                   ./elibrary/bin//libminic.a(elibs_sprintf.o)
eLIBs_setfs                                       ./elibrary/bin//libminic.a(elibs_stdio.o)
eLIBs_snprintf                                    ./elibrary/bin//libminic.a(elibs_sprintf.o)
eLIBs_sprintf                                     ./elibrary/bin//libminic.a(elibs_sprintf.o)
                                                  emodules/mod_mixture/mod_mixture.o
eLIBs_sscanf                                      ./elibrary/bin//libminic.a(elibs_stdlib.o)
eLIBs_statfs                                      ./elibrary/bin//libminic.a(elibs_stdio.o)
eLIBs_strcat                                      ./elibrary/bin//libminic.a(elibs_string.o)
                                                  ./elibrary/bin//libminic.a(elibs_stdio.o)
eLIBs_strchr                                      ./elibrary/bin//libminic.a(elibs_string.o)
eLIBs_strchrlast                                  ./elibrary/bin//libminic.a(elibs_string.o)
eLIBs_strcmp                                      ./elibrary/bin//libminic.a(elibs_string.o)
                                                  ./elibrary/bin//libminic.a(elibs_stdio.o)
eLIBs_strcpy                                      ./elibrary/bin//libminic.a(elibs_string.o)
                                                  ./elibrary/bin//libminic.a(elibs_stdio.o)
                                                  emodules/mod_mixture/mod_mixture.o
eLIBs_stricmp                                     ./elibrary/bin//libminic.a(elibs_string.o)
                                                  ./elibrary/bin//libminic.a(elibs_stdio.o)
eLIBs_strlen                                      ./elibrary/bin//libminic.a(elibs_string.o)
                                                  ./elibrary/bin//libminic.a(elibs_stdio.o)
eLIBs_strncat                                     ./elibrary/bin//libminic.a(elibs_string.o)
eLIBs_strnchr                                     ./elibrary/bin//libminic.a(elibs_string.o)
eLIBs_strncmp                                     ./elibrary/bin//libminic.a(elibs_string.o)
                                                  ./elibrary/bin//liblzma.a(az100.o)
eLIBs_strncpy                                     ./elibrary/bin//libminic.a(elibs_string.o)
                                                  ./elibrary/bin//libminic.a(elibs_stdio.o)
eLIBs_strnicmp                                    ./elibrary/bin//libminic.a(elibs_string.o)
eLIBs_strnlen                                     ./elibrary/bin//libminic.a(elibs_string.o)
eLIBs_strstr                                      ./elibrary/bin//libminic.a(elibs_string.o)
eLIBs_strtol                                      ./elibrary/bin//libminic.a(elibs_stdlib.o)
eLIBs_toupper                                     ./elibrary/bin//libminic.a(elibs_stdlib.o)
eLIBs_uint2str_dec                                ./elibrary/bin//libminic.a(elibs_stdlib.o)
eLIBs_vprintf                                     ./elibrary/bin//libminic.a(elibs_stdio.o)
eLIBs_vscnprintf                                  ./elibrary/bin//libminic.a(elibs_sprintf.o)
                                                  ./elibrary/bin//libminic.a(elibs_stdio.o)
eLIBs_vsnprintf                                   ./elibrary/bin//libminic.a(elibs_sprintf.o)
eLIBs_vsprintf                                    ./elibrary/bin//libminic.a(elibs_sprintf.o)
esCFG_Exit_Ex                                     ./elibrary/bin//libsyscall.a(syscall.o)
esCFG_GetGPIOSecData                              ./elibrary/bin//libsyscall.a(syscall.o)
                                                  emodules/mod_mixture/mod_mixture.o
esCFG_GetGPIOSecData_Ex                           ./elibrary/bin//libsyscall.a(syscall.o)
esCFG_GetGPIOSecKeyCount                          ./elibrary/bin//libsyscall.a(syscall.o)
esCFG_GetGPIOSecKeyCount_Ex                       ./elibrary/bin//libsyscall.a(syscall.o)
esCFG_GetKeyValue                                 ./elibrary/bin//libsyscall.a(syscall.o)
                                                  emodules/mod_mixture/backcarcheck/dis_checkio.o
                                                  emodules/mod_mixture/startup_check/startup_check.o
                                                  emodules/mod_mixture/mod_mixture.o
esCFG_GetKeyValue_Ex                              ./elibrary/bin//libsyscall.a(syscall.o)
esCFG_GetSecCount                                 ./elibrary/bin//libsyscall.a(syscall.o)
esCFG_GetSecCount_Ex                              ./elibrary/bin//libsyscall.a(syscall.o)
esCFG_GetSecKeyCount                              ./elibrary/bin//libsyscall.a(syscall.o)
esCFG_GetSecKeyCount_Ex                           ./elibrary/bin//libsyscall.a(syscall.o)
esCFG_Init_Ex                                     ./elibrary/bin//libsyscall.a(syscall.o)
esCHS_Char2Uni                                    ./elibrary/bin//libsyscall.a(syscall.o)
esCHS_GetChLowerTbl                               ./elibrary/bin//libsyscall.a(syscall.o)
esCHS_GetChUpperTbl                               ./elibrary/bin//libsyscall.a(syscall.o)
esCHS_Uni2Char                                    ./elibrary/bin//libsyscall.a(syscall.o)
esCLK_CloseMclk                                   ./elibrary/bin//libsyscall.a(syscall.o)
esCLK_GetMclkDiv                                  ./elibrary/bin//libsyscall.a(syscall.o)
esCLK_GetMclkSrc                                  ./elibrary/bin//libsyscall.a(syscall.o)
esCLK_GetRound_Rate                               ./elibrary/bin//libsyscall.a(syscall.o)
esCLK_GetSrcFreq                                  ./elibrary/bin//libsyscall.a(syscall.o)
esCLK_MclkAssert                                  ./elibrary/bin//libsyscall.a(syscall.o)
esCLK_MclkDeassert                                ./elibrary/bin//libsyscall.a(syscall.o)
esCLK_MclkGetRstStatus                            ./elibrary/bin//libsyscall.a(syscall.o)
esCLK_MclkOnOff                                   ./elibrary/bin//libsyscall.a(syscall.o)
esCLK_MclkRegCb                                   ./elibrary/bin//libsyscall.a(syscall.o)
esCLK_MclkReset                                   ./elibrary/bin//libsyscall.a(syscall.o)
esCLK_MclkUnregCb                                 ./elibrary/bin//libsyscall.a(syscall.o)
esCLK_ModInfo                                     ./elibrary/bin//libsyscall.a(syscall.o)
esCLK_OpenMclk                                    ./elibrary/bin//libsyscall.a(syscall.o)
esCLK_SetMclkDiv                                  ./elibrary/bin//libsyscall.a(syscall.o)
esCLK_SetMclkSrc                                  ./elibrary/bin//libsyscall.a(syscall.o)
esCLK_SetSrcFreq                                  ./elibrary/bin//libsyscall.a(syscall.o)
esCLK_SysInfo                                     ./elibrary/bin//libsyscall.a(syscall.o)
esDEV_Close                                       ./elibrary/bin//libsyscall.a(syscall.o)
esDEV_DevReg                                      ./elibrary/bin//libsyscall.a(syscall.o)
esDEV_DevUnreg                                    ./elibrary/bin//libsyscall.a(syscall.o)
esDEV_Insmod                                      ./elibrary/bin//libsyscall.a(syscall.o)
esDEV_Ioctl                                       ./elibrary/bin//libsyscall.a(syscall.o)
esDEV_Lock                                        ./elibrary/bin//libsyscall.a(syscall.o)
esDEV_Open                                        ./elibrary/bin//libsyscall.a(syscall.o)
esDEV_Plugin                                      ./elibrary/bin//libsyscall.a(syscall.o)
esDEV_Plugout                                     ./elibrary/bin//libsyscall.a(syscall.o)
esDEV_Read                                        ./elibrary/bin//libsyscall.a(syscall.o)
esDEV_Unimod                                      ./elibrary/bin//libsyscall.a(syscall.o)
esDEV_Unlock                                      ./elibrary/bin//libsyscall.a(syscall.o)
esDEV_Write                                       ./elibrary/bin//libsyscall.a(syscall.o)
esDMA_ChangeMode                                  ./elibrary/bin//libsyscall.a(syscall.o)
esDMA_DisableINT                                  ./elibrary/bin//libsyscall.a(syscall.o)
esDMA_EnableINT                                   ./elibrary/bin//libsyscall.a(syscall.o)
esDMA_Information                                 ./elibrary/bin//libsyscall.a(syscall.o)
esDMA_QueryDst                                    ./elibrary/bin//libsyscall.a(syscall.o)
esDMA_QueryRestCount                              ./elibrary/bin//libsyscall.a(syscall.o)
esDMA_QuerySrc                                    ./elibrary/bin//libsyscall.a(syscall.o)
esDMA_QueryStat                                   ./elibrary/bin//libsyscall.a(syscall.o)
esDMA_RegDmaHdler                                 ./elibrary/bin//libsyscall.a(syscall.o)
esDMA_Release                                     ./elibrary/bin//libsyscall.a(syscall.o)
esDMA_Request                                     ./elibrary/bin//libsyscall.a(syscall.o)
esDMA_Restart                                     ./elibrary/bin//libsyscall.a(syscall.o)
esDMA_Setting                                     ./elibrary/bin//libsyscall.a(syscall.o)
esDMA_Start                                       ./elibrary/bin//libsyscall.a(syscall.o)
esDMA_Stop                                        ./elibrary/bin//libsyscall.a(syscall.o)
esDMA_UnregDmaHdler                               ./elibrary/bin//libsyscall.a(syscall.o)
esEXEC_PCreate                                    ./elibrary/bin//libsyscall.a(syscall.o)
esEXEC_PDel                                       ./elibrary/bin//libsyscall.a(syscall.o)
esEXEC_PDelReq                                    ./elibrary/bin//libsyscall.a(syscall.o)
esEXEC_Run                                        ./elibrary/bin//libsyscall.a(syscall.o)
esFSYS_clearpartupdateflag                        ./elibrary/bin//libsyscall.a(syscall.o)
esFSYS_closedir                                   ./elibrary/bin//libsyscall.a(syscall.o)
                                                  ./elibrary/bin//libminic.a(elibs_stdio.o)
esFSYS_fclose                                     ./elibrary/bin//libsyscall.a(syscall.o)
                                                  ./elibrary/bin//libminic.a(elibs_stdio.o)
esFSYS_fd2file                                    ./elibrary/bin//libsyscall.a(syscall.o)
                                                  ./elibrary/bin//libminic.a(elibs_stdio.o)
esFSYS_ferrclr                                    ./elibrary/bin//libsyscall.a(syscall.o)
                                                  ./elibrary/bin//libminic.a(elibs_stdio.o)
esFSYS_ferror                                     ./elibrary/bin//libsyscall.a(syscall.o)
                                                  ./elibrary/bin//libminic.a(elibs_stdio.o)
esFSYS_file2fd                                    ./elibrary/bin//libsyscall.a(syscall.o)
                                                  ./elibrary/bin//libminic.a(elibs_stdio.o)
esFSYS_fioctrl                                    ./elibrary/bin//libsyscall.a(syscall.o)
                                                  ./elibrary/bin//libminic.a(elibs_stdio.o)
esFSYS_fopen                                      ./elibrary/bin//libsyscall.a(syscall.o)
                                                  ./elibrary/bin//libminic.a(elibs_stdio.o)
esFSYS_format                                     ./elibrary/bin//libsyscall.a(syscall.o)
                                                  ./elibrary/bin//libminic.a(elibs_stdio.o)
esFSYS_fread                                      ./elibrary/bin//libsyscall.a(syscall.o)
                                                  ./elibrary/bin//libminic.a(elibs_stdio.o)
esFSYS_fsdbg                                      ./elibrary/bin//libsyscall.a(syscall.o)
esFSYS_fseek                                      ./elibrary/bin//libsyscall.a(syscall.o)
                                                  ./elibrary/bin//libminic.a(elibs_stdio.o)
esFSYS_fseekex                                    ./elibrary/bin//libsyscall.a(syscall.o)
                                                  ./elibrary/bin//libminic.a(elibs_stdio.o)
esFSYS_fsreg                                      ./elibrary/bin//libsyscall.a(syscall.o)
esFSYS_fstat                                      ./elibrary/bin//libsyscall.a(syscall.o)
                                                  ./elibrary/bin//libminic.a(elibs_stdio.o)
esFSYS_fsunreg                                    ./elibrary/bin//libsyscall.a(syscall.o)
esFSYS_fsync                                      ./elibrary/bin//libsyscall.a(syscall.o)
                                                  ./elibrary/bin//libminic.a(elibs_stdio.o)
esFSYS_ftell                                      ./elibrary/bin//libsyscall.a(syscall.o)
                                                  ./elibrary/bin//libminic.a(elibs_stdio.o)
esFSYS_ftellex                                    ./elibrary/bin//libsyscall.a(syscall.o)
                                                  ./elibrary/bin//libminic.a(elibs_stdio.o)
esFSYS_ftruncate                                  ./elibrary/bin//libsyscall.a(syscall.o)
                                                  ./elibrary/bin//libminic.a(elibs_stdio.o)
esFSYS_fwrite                                     ./elibrary/bin//libsyscall.a(syscall.o)
                                                  ./elibrary/bin//libminic.a(elibs_stdio.o)
esFSYS_getfscharset                               ./elibrary/bin//libsyscall.a(syscall.o)
                                                  ./elibrary/bin//libminic.a(elibs_stdio.o)
esFSYS_mkdir                                      ./elibrary/bin//libsyscall.a(syscall.o)
                                                  ./elibrary/bin//libminic.a(elibs_stdio.o)
esFSYS_mntfs                                      ./elibrary/bin//libsyscall.a(syscall.o)
esFSYS_mntparts                                   ./elibrary/bin//libsyscall.a(syscall.o)
esFSYS_open                                       ./elibrary/bin//libsyscall.a(syscall.o)
                                                  ./elibrary/bin//libminic.a(elibs_stdio.o)
esFSYS_opendir                                    ./elibrary/bin//libsyscall.a(syscall.o)
                                                  ./elibrary/bin//libminic.a(elibs_stdio.o)
esFSYS_partfslck                                  ./elibrary/bin//libsyscall.a(syscall.o)
                                                  ./elibrary/bin//libminic.a(elibs_stdio.o)
esFSYS_partfsunlck                                ./elibrary/bin//libsyscall.a(syscall.o)
                                                  ./elibrary/bin//libminic.a(elibs_stdio.o)
esFSYS_pclose                                     ./elibrary/bin//libsyscall.a(syscall.o)
esFSYS_pdreg                                      ./elibrary/bin//libsyscall.a(syscall.o)
esFSYS_pdunreg                                    ./elibrary/bin//libsyscall.a(syscall.o)
esFSYS_perrclr                                    ./elibrary/bin//libsyscall.a(syscall.o)
esFSYS_perror                                     ./elibrary/bin//libsyscall.a(syscall.o)
esFSYS_pioctrl                                    ./elibrary/bin//libsyscall.a(syscall.o)
esFSYS_popen                                      ./elibrary/bin//libsyscall.a(syscall.o)
esFSYS_pread                                      ./elibrary/bin//libsyscall.a(syscall.o)
esFSYS_premove                                    ./elibrary/bin//libsyscall.a(syscall.o)
esFSYS_prename                                    ./elibrary/bin//libsyscall.a(syscall.o)
esFSYS_pwrite                                     ./elibrary/bin//libsyscall.a(syscall.o)
esFSYS_querypartupdateflag                        ./elibrary/bin//libsyscall.a(syscall.o)
esFSYS_readdir                                    ./elibrary/bin//libsyscall.a(syscall.o)
                                                  ./elibrary/bin//libminic.a(elibs_stdio.o)
esFSYS_remove                                     ./elibrary/bin//libsyscall.a(syscall.o)
                                                  ./elibrary/bin//libminic.a(elibs_stdio.o)
esFSYS_rename                                     ./elibrary/bin//libsyscall.a(syscall.o)
                                                  ./elibrary/bin//libminic.a(elibs_stdio.o)
esFSYS_rewinddir                                  ./elibrary/bin//libsyscall.a(syscall.o)
                                                  ./elibrary/bin//libminic.a(elibs_stdio.o)
esFSYS_rmdir                                      ./elibrary/bin//libsyscall.a(syscall.o)
                                                  ./elibrary/bin//libminic.a(elibs_stdio.o)
esFSYS_setfs                                      ./elibrary/bin//libsyscall.a(syscall.o)
                                                  ./elibrary/bin//libminic.a(elibs_stdio.o)
esFSYS_statfs                                     ./elibrary/bin//libsyscall.a(syscall.o)
                                                  ./elibrary/bin//libminic.a(elibs_stdio.o)
esFSYS_statpt                                     ./elibrary/bin//libsyscall.a(syscall.o)
                                                  ./elibrary/bin//libminic.a(elibs_stdio.o)
esFSYS_umntfs                                     ./elibrary/bin//libsyscall.a(syscall.o)
esFSYS_umntparts                                  ./elibrary/bin//libsyscall.a(syscall.o)
esHID_SendMsg                                     ./elibrary/bin//libsyscall.a(syscall.o)
esHID_hiddevreg                                   ./elibrary/bin//libsyscall.a(syscall.o)
esHID_hiddevunreg                                 ./elibrary/bin//libsyscall.a(syscall.o)
esINPUT_GetLdevID                                 ./elibrary/bin//libsyscall.a(syscall.o)
esINPUT_LdevCtl                                   ./elibrary/bin//libsyscall.a(syscall.o)
esINPUT_LdevFeedback                              ./elibrary/bin//libsyscall.a(syscall.o)
esINPUT_LdevGrab                                  ./elibrary/bin//libsyscall.a(syscall.o)
esINPUT_LdevRelease                               ./elibrary/bin//libsyscall.a(syscall.o)
esINPUT_RegDev                                    ./elibrary/bin//libsyscall.a(syscall.o)
esINPUT_SendEvent                                 ./elibrary/bin//libsyscall.a(syscall.o)
esINPUT_UnregDev                                  ./elibrary/bin//libsyscall.a(syscall.o)
esINT_DisableINT                                  ./elibrary/bin//libsyscall.a(syscall.o)
esINT_EnableINT                                   ./elibrary/bin//libsyscall.a(syscall.o)
esINT_InsFIR                                      ./elibrary/bin//libsyscall.a(syscall.o)
esINT_InsISR                                      ./elibrary/bin//libsyscall.a(syscall.o)
esINT_SetIRQPrio                                  ./elibrary/bin//libsyscall.a(syscall.o)
esINT_UniFIR                                      ./elibrary/bin//libsyscall.a(syscall.o)
esINT_UniISR                                      ./elibrary/bin//libsyscall.a(syscall.o)
esKRNL_CallBack                                   ./elibrary/bin//libsyscall.a(syscall.o)
esKRNL_DumpStack                                  ./elibrary/bin//libsyscall.a(syscall.o)
esKRNL_FlagAccept                                 ./elibrary/bin//libsyscall.a(syscall.o)
esKRNL_FlagCreate                                 ./elibrary/bin//libsyscall.a(syscall.o)
esKRNL_FlagDel                                    ./elibrary/bin//libsyscall.a(syscall.o)
esKRNL_FlagNameGet                                ./elibrary/bin//libsyscall.a(syscall.o)
esKRNL_FlagNameSet                                ./elibrary/bin//libsyscall.a(syscall.o)
esKRNL_FlagPend                                   ./elibrary/bin//libsyscall.a(syscall.o)
esKRNL_FlagPendGetFlagsRdy                        ./elibrary/bin//libsyscall.a(syscall.o)
esKRNL_FlagPost                                   ./elibrary/bin//libsyscall.a(syscall.o)
esKRNL_FlagQuery                                  ./elibrary/bin//libsyscall.a(syscall.o)
esKRNL_GetCallBack                                ./elibrary/bin//libsyscall.a(syscall.o)
                                                  emodules/mod_mixture/mod_mixture.o
esKRNL_GetTIDCur                                  ./elibrary/bin//libsyscall.a(syscall.o)
esKRNL_InterruptDisable                           ./elibrary/bin//libsyscall.a(syscall.o)
esKRNL_InterruptEnable                            ./elibrary/bin//libsyscall.a(syscall.o)
esKRNL_Ioctrl                                     ./elibrary/bin//libsyscall.a(syscall.o)
esKRNL_MboxAccept                                 ./elibrary/bin//libsyscall.a(syscall.o)
esKRNL_MboxCreate                                 ./elibrary/bin//libsyscall.a(syscall.o)
esKRNL_MboxDel                                    ./elibrary/bin//libsyscall.a(syscall.o)
esKRNL_MboxPend                                   ./elibrary/bin//libsyscall.a(syscall.o)
esKRNL_MboxPost                                   ./elibrary/bin//libsyscall.a(syscall.o)
esKRNL_MboxPostOpt                                ./elibrary/bin//libsyscall.a(syscall.o)
esKRNL_MboxQuery                                  ./elibrary/bin//libsyscall.a(syscall.o)
esKRNL_MemLeakChk                                 ./elibrary/bin//libsyscall.a(syscall.o)
esKRNL_MutexCreate                                ./elibrary/bin//libsyscall.a(syscall.o)
esKRNL_MutexDel                                   ./elibrary/bin//libsyscall.a(syscall.o)
esKRNL_MutexPend                                  ./elibrary/bin//libsyscall.a(syscall.o)
esKRNL_MutexPost                                  ./elibrary/bin//libsyscall.a(syscall.o)
esKRNL_QAccept                                    ./elibrary/bin//libsyscall.a(syscall.o)
esKRNL_QCreate                                    ./elibrary/bin//libsyscall.a(syscall.o)
esKRNL_QDel                                       ./elibrary/bin//libsyscall.a(syscall.o)
esKRNL_QFlush                                     ./elibrary/bin//libsyscall.a(syscall.o)
esKRNL_QPend                                      ./elibrary/bin//libsyscall.a(syscall.o)
esKRNL_QPost                                      ./elibrary/bin//libsyscall.a(syscall.o)
esKRNL_QPostFront                                 ./elibrary/bin//libsyscall.a(syscall.o)
esKRNL_QPostOpt                                   ./elibrary/bin//libsyscall.a(syscall.o)
esKRNL_QQuery                                     ./elibrary/bin//libsyscall.a(syscall.o)
esKRNL_SchedLock                                  ./elibrary/bin//libsyscall.a(syscall.o)
esKRNL_SchedUnlock                                ./elibrary/bin//libsyscall.a(syscall.o)
esKRNL_SemAccept                                  ./elibrary/bin//libsyscall.a(syscall.o)
esKRNL_SemCreate                                  ./elibrary/bin//libsyscall.a(syscall.o)
                                                  emodules/mod_mixture/mod_mixture.o
esKRNL_SemDel                                     ./elibrary/bin//libsyscall.a(syscall.o)
esKRNL_SemPend                                    ./elibrary/bin//libsyscall.a(syscall.o)
                                                  emodules/mod_mixture/mod_mixture.o
esKRNL_SemPost                                    ./elibrary/bin//libsyscall.a(syscall.o)
                                                  emodules/mod_mixture/mod_mixture.o
esKRNL_SemQuery                                   ./elibrary/bin//libsyscall.a(syscall.o)
esKRNL_SemSet                                     ./elibrary/bin//libsyscall.a(syscall.o)
esKRNL_SktAccept                                  ./elibrary/bin//libsyscall.a(syscall.o)
esKRNL_SktCreate                                  ./elibrary/bin//libsyscall.a(syscall.o)
esKRNL_SktDel                                     ./elibrary/bin//libsyscall.a(syscall.o)
esKRNL_SktPend                                    ./elibrary/bin//libsyscall.a(syscall.o)
esKRNL_SktPost                                    ./elibrary/bin//libsyscall.a(syscall.o)
esKRNL_TCreate                                    ./elibrary/bin//libsyscall.a(syscall.o)
                                                  emodules/mod_mixture/mod_mixture.o
esKRNL_TDel                                       ./elibrary/bin//libsyscall.a(syscall.o)
                                                  emodules/mod_mixture/mod_mixture.o
esKRNL_TDelReq                                    ./elibrary/bin//libsyscall.a(syscall.o)
                                                  emodules/mod_mixture/mod_mixture.o
esKRNL_TaskNameSet                                ./elibrary/bin//libsyscall.a(syscall.o)
esKRNL_TaskPrefEn                                 ./elibrary/bin//libsyscall.a(syscall.o)
esKRNL_TaskQuery                                  ./elibrary/bin//libsyscall.a(syscall.o)
esKRNL_TaskResume                                 ./elibrary/bin//libsyscall.a(syscall.o)
esKRNL_TaskSuspend                                ./elibrary/bin//libsyscall.a(syscall.o)
esKRNL_Time                                       ./elibrary/bin//libsyscall.a(syscall.o)
esKRNL_TimeDly                                    ./elibrary/bin//libsyscall.a(syscall.o)
                                                  emodules/mod_mixture/startup_check/startup_check.o
                                                  emodules/mod_mixture/mod_mixture.o
esKRNL_TimeDlyResume                              ./elibrary/bin//libsyscall.a(syscall.o)
esKRNL_TimeGet                                    ./elibrary/bin//libsyscall.a(syscall.o)
esKRNL_TimeSet                                    ./elibrary/bin//libsyscall.a(syscall.o)
esKRNL_TmrCreate                                  ./elibrary/bin//libsyscall.a(syscall.o)
esKRNL_TmrDel                                     ./elibrary/bin//libsyscall.a(syscall.o)
esKRNL_TmrError                                   ./elibrary/bin//libsyscall.a(syscall.o)
esKRNL_TmrRemainGet                               ./elibrary/bin//libsyscall.a(syscall.o)
esKRNL_TmrStart                                   ./elibrary/bin//libsyscall.a(syscall.o)
esKRNL_TmrStateGet                                ./elibrary/bin//libsyscall.a(syscall.o)
esKRNL_TmrStop                                    ./elibrary/bin//libsyscall.a(syscall.o)
esKRNL_Version                                    ./elibrary/bin//libsyscall.a(syscall.o)
esKSRV_ClearWatchDog                              ./elibrary/bin//libsyscall.a(syscall.o)
esKSRV_DisableWatchDog                            ./elibrary/bin//libsyscall.a(syscall.o)
esKSRV_DisableWatchDogSrv                         ./elibrary/bin//libsyscall.a(syscall.o)
esKSRV_EnableWatchDog                             ./elibrary/bin//libsyscall.a(syscall.o)
esKSRV_EnableWatchDogSrv                          ./elibrary/bin//libsyscall.a(syscall.o)
esKSRV_GetAddPara                                 ./elibrary/bin//libsyscall.a(syscall.o)
esKSRV_GetDramCfgPara                             ./elibrary/bin//libsyscall.a(syscall.o)
esKSRV_GetHighMsg                                 ./elibrary/bin//libsyscall.a(syscall.o)
esKSRV_GetLowMsg                                  ./elibrary/bin//libsyscall.a(syscall.o)
esKSRV_GetMsg                                     ./elibrary/bin//libsyscall.a(syscall.o)
esKSRV_GetPara                                    ./elibrary/bin//libsyscall.a(syscall.o)
esKSRV_GetSocID                                   ./elibrary/bin//libsyscall.a(syscall.o)
esKSRV_GetTargetPara                              ./elibrary/bin//libsyscall.a(syscall.o)
esKSRV_GetVersion                                 ./elibrary/bin//libsyscall.a(syscall.o)
esKSRV_Get_Display_Hld                            ./elibrary/bin//libsyscall.a(syscall.o)
                                                  emodules/mod_mixture/backcarcheck/dis_show_picture.o
                                                  emodules/mod_mixture/mod_mixture.o
esKSRV_Get_Mixture_Hld                            ./elibrary/bin//libsyscall.a(syscall.o)
esKSRV_Get_Raw_Decoder_Hld                        ./elibrary/bin//libsyscall.a(syscall.o)
esKSRV_PowerOff                                   ./elibrary/bin//libsyscall.a(syscall.o)
esKSRV_Random                                     ./elibrary/bin//libsyscall.a(syscall.o)
                                                  ./elibrary/bin//libminic.a(elibs_stdlib.o)
esKSRV_Reset                                      ./elibrary/bin//libsyscall.a(syscall.o)
esKSRV_Save_Display_Hld                           ./elibrary/bin//libsyscall.a(syscall.o)
esKSRV_Save_Mixture_Hld                           ./elibrary/bin//libsyscall.a(syscall.o)
esKSRV_Save_Raw_Decoder_Hld                       ./elibrary/bin//libsyscall.a(syscall.o)
esKSRV_SendMsg                                    ./elibrary/bin//libsyscall.a(syscall.o)
                                                  emodules/mod_mixture/mod_mixture.o
esKSRV_SendMsgEx                                  ./elibrary/bin//libsyscall.a(syscall.o)
esKSRV_SysInfo                                    ./elibrary/bin//libsyscall.a(syscall.o)
esKSRV_memcpy                                     ./elibrary/bin//libsyscall.a(syscall.o)
esKSRV_memset                                     ./elibrary/bin//libsyscall.a(syscall.o)
esMEMS_Balloc                                     ./elibrary/bin//libsyscall.a(syscall.o)
                                                  emodules/mod_mixture/bmp_parser/Parse_Picture.o
                                                  emodules/mod_mixture/mod_mixture.o
esMEMS_Bfree                                      ./elibrary/bin//libsyscall.a(syscall.o)
                                                  emodules/mod_mixture/backcarcheck/dis_show_picture.o
                                                  emodules/mod_mixture/mod_mixture.o
esMEMS_Calloc                                     ./elibrary/bin//libsyscall.a(syscall.o)
esMEMS_CleanDCache                                ./elibrary/bin//libsyscall.a(syscall.o)
esMEMS_CleanDCacheRegion                          ./elibrary/bin//libsyscall.a(syscall.o)
esMEMS_CleanFlushCache                            ./elibrary/bin//libsyscall.a(syscall.o)
esMEMS_CleanFlushCacheRegion                      ./elibrary/bin//libsyscall.a(syscall.o)
esMEMS_CleanFlushDCache                           ./elibrary/bin//libsyscall.a(syscall.o)
esMEMS_CleanFlushDCacheRegion                     ./elibrary/bin//libsyscall.a(syscall.o)
                                                  emodules/mod_mixture/mod_mixture.o
esMEMS_CleanInvalidateCacheAll                    ./elibrary/bin//libsyscall.a(syscall.o)
esMEMS_FlushCache                                 ./elibrary/bin//libsyscall.a(syscall.o)
esMEMS_FlushCacheRegion                           ./elibrary/bin//libsyscall.a(syscall.o)
esMEMS_FlushDCache                                ./elibrary/bin//libsyscall.a(syscall.o)
esMEMS_FlushDCacheRegion                          ./elibrary/bin//libsyscall.a(syscall.o)
esMEMS_FlushICache                                ./elibrary/bin//libsyscall.a(syscall.o)
esMEMS_FlushICacheRegion                          ./elibrary/bin//libsyscall.a(syscall.o)
esMEMS_FreeMemSize                                ./elibrary/bin//libsyscall.a(syscall.o)
esMEMS_GetIoVAByPA                                ./elibrary/bin//libsyscall.a(syscall.o)
esMEMS_HeapCreate                                 ./elibrary/bin//libsyscall.a(syscall.o)
esMEMS_HeapDel                                    ./elibrary/bin//libsyscall.a(syscall.o)
esMEMS_Info                                       ./elibrary/bin//libsyscall.a(syscall.o)
esMEMS_Malloc                                     ./elibrary/bin//libsyscall.a(syscall.o)
                                                  ./elibrary/bin//libminic.a(elibs_stdlib.o)
                                                  ./elibrary/bin//libminic.a(elibs_stdio.o)
                                                  emodules/mod_mixture/mod_mixture.o
esMEMS_Mfree                                      ./elibrary/bin//libsyscall.a(syscall.o)
                                                  ./elibrary/bin//libminic.a(elibs_stdlib.o)
                                                  ./elibrary/bin//libminic.a(elibs_stdio.o)
                                                  emodules/mod_mixture/mod_mixture.o
esMEMS_Palloc                                     ./elibrary/bin//libsyscall.a(syscall.o)
                                                  emodules/mod_mixture/backcarcheck/dis_Parse_Picture.o
                                                  emodules/mod_mixture/backcarcheck/dis_bmp.o
                                                  emodules/mod_mixture/bmp_parser/Parse_Picture.o
                                                  emodules/mod_mixture/bmp_parser/bmp.o
                                                  emodules/mod_mixture/mod_mixture.o
esMEMS_Pfree                                      ./elibrary/bin//libsyscall.a(syscall.o)
                                                  emodules/mod_mixture/backcarcheck/dis_show_picture.o
                                                  emodules/mod_mixture/backcarcheck/dis_bmp.o
                                                  emodules/mod_mixture/bmp_parser/bmp.o
                                                  emodules/mod_mixture/mod_mixture.o
esMEMS_Realloc                                    ./elibrary/bin//libsyscall.a(syscall.o)
                                                  ./elibrary/bin//libminic.a(elibs_stdlib.o)
esMEMS_TotalMemSize                               ./elibrary/bin//libsyscall.a(syscall.o)
esMEMS_VA2PA                                      ./elibrary/bin//libsyscall.a(syscall.o)
esMEMS_VMCreate                                   ./elibrary/bin//libsyscall.a(syscall.o)
esMEMS_VMDelete                                   ./elibrary/bin//libsyscall.a(syscall.o)
esMEMS_VMalloc                                    ./elibrary/bin//libsyscall.a(syscall.o)
esMEMS_VMfree                                     ./elibrary/bin//libsyscall.a(syscall.o)
esMEM_BWDisable                                   ./elibrary/bin//libsyscall.a(syscall.o)
esMEM_BWEnable                                    ./elibrary/bin//libsyscall.a(syscall.o)
esMEM_BWGet                                       ./elibrary/bin//libsyscall.a(syscall.o)
esMEM_DramSuspend                                 ./elibrary/bin//libsyscall.a(syscall.o)
esMEM_DramWakeup                                  ./elibrary/bin//libsyscall.a(syscall.o)
esMEM_MasterGet                                   ./elibrary/bin//libsyscall.a(syscall.o)
esMEM_MasterSet                                   ./elibrary/bin//libsyscall.a(syscall.o)
esMEM_RegDramAccess                               ./elibrary/bin//libsyscall.a(syscall.o)
esMEM_ReleaseDramUsrMode                          ./elibrary/bin//libsyscall.a(syscall.o)
esMEM_RequestDramUsrMode                          ./elibrary/bin//libsyscall.a(syscall.o)
esMEM_SramRelBlk                                  ./elibrary/bin//libsyscall.a(syscall.o)
esMEM_SramReqBlk                                  ./elibrary/bin//libsyscall.a(syscall.o)
esMEM_SramSwitchBlk                               ./elibrary/bin//libsyscall.a(syscall.o)
esMEM_UnRegDramAccess                             ./elibrary/bin//libsyscall.a(syscall.o)
esMODS_MClose                                     ./elibrary/bin//libsyscall.a(syscall.o)
                                                  emodules/mod_mixture/backcarcheck/dis_checkio.o
                                                  emodules/mod_mixture/mod_mixture.o
esMODS_MInstall                                   ./elibrary/bin//libsyscall.a(syscall.o)
                                                  emodules/mod_mixture/backcarcheck/dis_checkio.o
                                                  emodules/mod_mixture/mod_mixture.o
esMODS_MIoctrl                                    ./elibrary/bin//libsyscall.a(syscall.o)
                                                  emodules/mod_mixture/backcarcheck/dis_show_picture.o
                                                  emodules/mod_mixture/backcarcheck/dis_checkio.o
                                                  emodules/mod_mixture/mod_mixture.o
esMODS_MOpen                                      ./elibrary/bin//libsyscall.a(syscall.o)
                                                  emodules/mod_mixture/backcarcheck/dis_checkio.o
                                                  emodules/mod_mixture/mod_mixture.o
esMODS_MRead                                      ./elibrary/bin//libsyscall.a(syscall.o)
esMODS_MUninstall                                 ./elibrary/bin//libsyscall.a(syscall.o)
                                                  emodules/mod_mixture/backcarcheck/dis_checkio.o
                                                  emodules/mod_mixture/mod_mixture.o
esMODS_MWrite                                     ./elibrary/bin//libsyscall.a(syscall.o)
esMSTUB_GetFuncEntry                              ./elibrary/bin//libsyscall.a(syscall.o)
esMSTUB_RegFuncTbl                                ./elibrary/bin//libsyscall.a(syscall.o)
esMSTUB_UnRegFuncTbl                              ./elibrary/bin//libsyscall.a(syscall.o)
esPINS_ClearPending                               ./elibrary/bin//libsyscall.a(syscall.o)
esPINS_DisbaleInt                                 ./elibrary/bin//libsyscall.a(syscall.o)
esPINS_EnbaleInt                                  ./elibrary/bin//libsyscall.a(syscall.o)
esPINS_GetPinGrpStat                              ./elibrary/bin//libsyscall.a(syscall.o)
esPINS_GetPinStat                                 ./elibrary/bin//libsyscall.a(syscall.o)
esPINS_PinGrpRel                                  ./elibrary/bin//libsyscall.a(syscall.o)
                                                  emodules/mod_mixture/mod_mixture.o
esPINS_PinGrpReq                                  ./elibrary/bin//libsyscall.a(syscall.o)
                                                  emodules/mod_mixture/startup_check/startup_check.o
                                                  emodules/mod_mixture/mod_mixture.o
esPINS_QueryInt                                   ./elibrary/bin//libsyscall.a(syscall.o)
esPINS_ReadPinData                                ./elibrary/bin//libsyscall.a(syscall.o)
                                                  emodules/mod_mixture/mod_mixture.o
esPINS_RegIntHdler                                ./elibrary/bin//libsyscall.a(syscall.o)
esPINS_SetIntMode                                 ./elibrary/bin//libsyscall.a(syscall.o)
esPINS_SetPinDrive                                ./elibrary/bin//libsyscall.a(syscall.o)
esPINS_SetPinIO                                   ./elibrary/bin//libsyscall.a(syscall.o)
                                                  emodules/mod_mixture/mod_mixture.o
esPINS_SetPinPull                                 ./elibrary/bin//libsyscall.a(syscall.o)
                                                  emodules/mod_mixture/mod_mixture.o
esPINS_SetPinStat                                 ./elibrary/bin//libsyscall.a(syscall.o)
esPINS_UnregIntHdler                              ./elibrary/bin//libsyscall.a(syscall.o)
esPINS_WritePinData                               ./elibrary/bin//libsyscall.a(syscall.o)
                                                  emodules/mod_mixture/startup_check/startup_check.o
esPWRMAN_EnterStandby                             ./elibrary/bin//libsyscall.a(syscall.o)
esPWRMAN_GetStandbyPara                           ./elibrary/bin//libsyscall.a(syscall.o)
esPWRMAN_LockCpuFreq                              ./elibrary/bin//libsyscall.a(syscall.o)
esPWRMAN_RegDevice                                ./elibrary/bin//libsyscall.a(syscall.o)
esPWRMAN_RelPwrmanMode                            ./elibrary/bin//libsyscall.a(syscall.o)
esPWRMAN_ReqPwrmanMode                            ./elibrary/bin//libsyscall.a(syscall.o)
esPWRMAN_SetStandbyMode                           ./elibrary/bin//libsyscall.a(syscall.o)
esPWRMAN_UnlockCpuFreq                            ./elibrary/bin//libsyscall.a(syscall.o)
esPWRMAN_UnregDevice                              ./elibrary/bin//libsyscall.a(syscall.o)
esPWRMAN_UsrEventNotify                           ./elibrary/bin//libsyscall.a(syscall.o)
esRESM_RClose                                     ./elibrary/bin//libsyscall.a(syscall.o)
esRESM_ROpen                                      ./elibrary/bin//libsyscall.a(syscall.o)
esRESM_RRead                                      ./elibrary/bin//libsyscall.a(syscall.o)
esRESM_RSeek                                      ./elibrary/bin//libsyscall.a(syscall.o)
esSIOS_getchar                                    ./elibrary/bin//libsyscall.a(syscall.o)
esSIOS_gets                                       ./elibrary/bin//libsyscall.a(syscall.o)
esSIOS_putarg                                     ./elibrary/bin//libsyscall.a(syscall.o)
esSIOS_putstr                                     ./elibrary/bin//libsyscall.a(syscall.o)
                                                  ./elibrary/bin//libminic.a(elibs_stdio.o)
esSIOS_setbaud                                    ./elibrary/bin//libsyscall.a(syscall.o)
esSVC_RegCloseNode                                ./elibrary/bin//libsyscall.a(syscall.o)
esSVC_RegClrError                                 ./elibrary/bin//libsyscall.a(syscall.o)
esSVC_RegCreateKey                                ./elibrary/bin//libsyscall.a(syscall.o)
esSVC_RegCreatePath                               ./elibrary/bin//libsyscall.a(syscall.o)
esSVC_RegCreateSet                                ./elibrary/bin//libsyscall.a(syscall.o)
esSVC_RegDeleteKey                                ./elibrary/bin//libsyscall.a(syscall.o)
esSVC_RegDeleteNode                               ./elibrary/bin//libsyscall.a(syscall.o)
esSVC_RegDeletePath                               ./elibrary/bin//libsyscall.a(syscall.o)
esSVC_RegDeleteSet                                ./elibrary/bin//libsyscall.a(syscall.o)
esSVC_RegGetError                                 ./elibrary/bin//libsyscall.a(syscall.o)
esSVC_RegGetFirstKey                              ./elibrary/bin//libsyscall.a(syscall.o)
esSVC_RegGetFirstSet                              ./elibrary/bin//libsyscall.a(syscall.o)
esSVC_RegGetKeyCount                              ./elibrary/bin//libsyscall.a(syscall.o)
esSVC_RegGetKeyValue                              ./elibrary/bin//libsyscall.a(syscall.o)
esSVC_RegGetNextKey                               ./elibrary/bin//libsyscall.a(syscall.o)
esSVC_RegGetNextSet                               ./elibrary/bin//libsyscall.a(syscall.o)
esSVC_RegGetRootPath                              ./elibrary/bin//libsyscall.a(syscall.o)
esSVC_RegGetSetCount                              ./elibrary/bin//libsyscall.a(syscall.o)
esSVC_RegIni2Reg                                  ./elibrary/bin//libsyscall.a(syscall.o)
esSVC_RegOpenNode                                 ./elibrary/bin//libsyscall.a(syscall.o)
esSVC_RegReg2Ini                                  ./elibrary/bin//libsyscall.a(syscall.o)
esSVC_RegSetKeyValue                              ./elibrary/bin//libsyscall.a(syscall.o)
esSVC_RegSetRootPath                              ./elibrary/bin//libsyscall.a(syscall.o)
esSVC_ResourceRel                                 ./elibrary/bin//libsyscall.a(syscall.o)
esSVC_ResourceReq                                 ./elibrary/bin//libsyscall.a(syscall.o)
esTIME_ContiCntr                                  ./elibrary/bin//libsyscall.a(syscall.o)
esTIME_GetDate                                    ./elibrary/bin//libsyscall.a(syscall.o)
                                                  ./elibrary/bin//libminic.a(elibs_stdio.o)
esTIME_GetTime                                    ./elibrary/bin//libsyscall.a(syscall.o)
                                                  ./elibrary/bin//libminic.a(elibs_stdio.o)
esTIME_PauseCntr                                  ./elibrary/bin//libsyscall.a(syscall.o)
esTIME_QuerryAlarm                                ./elibrary/bin//libsyscall.a(syscall.o)
esTIME_QuerryCntr                                 ./elibrary/bin//libsyscall.a(syscall.o)
esTIME_QuerryCntrStat                             ./elibrary/bin//libsyscall.a(syscall.o)
esTIME_QuerryTimer                                ./elibrary/bin//libsyscall.a(syscall.o)
esTIME_ReleaseAlarm                               ./elibrary/bin//libsyscall.a(syscall.o)
esTIME_ReleaseCntr                                ./elibrary/bin//libsyscall.a(syscall.o)
esTIME_ReleaseTimer                               ./elibrary/bin//libsyscall.a(syscall.o)
esTIME_RequestAlarm                               ./elibrary/bin//libsyscall.a(syscall.o)
esTIME_RequestCntr                                ./elibrary/bin//libsyscall.a(syscall.o)
esTIME_RequestTimer                               ./elibrary/bin//libsyscall.a(syscall.o)
esTIME_SetCntrPrescale                            ./elibrary/bin//libsyscall.a(syscall.o)
esTIME_SetCntrValue                               ./elibrary/bin//libsyscall.a(syscall.o)
esTIME_SetDate                                    ./elibrary/bin//libsyscall.a(syscall.o)
esTIME_SetTime                                    ./elibrary/bin//libsyscall.a(syscall.o)
esTIME_StartAlarm                                 ./elibrary/bin//libsyscall.a(syscall.o)
esTIME_StartCntr                                  ./elibrary/bin//libsyscall.a(syscall.o)
esTIME_StartTimer                                 ./elibrary/bin//libsyscall.a(syscall.o)
esTIME_StopAlarm                                  ./elibrary/bin//libsyscall.a(syscall.o)
esTIME_StopCntr                                   ./elibrary/bin//libsyscall.a(syscall.o)
esTIME_StopTimer                                  ./elibrary/bin//libsyscall.a(syscall.o)
fheader                                           emodules/mod_mixture/mod_mixture.o
file_len                                          emodules/mod_mixture/mod_mixture.o
fioctrl                                           ./elibrary/bin//libsyscall.a(syscall_fops.o)
fsync                                             ./elibrary/bin//libsyscall.a(syscall_fops.o)
ftruncate                                         ./elibrary/bin//libsyscall.a(syscall_fops.o)
get_backcar_status                                emodules/mod_mixture/mod_mixture.o
get_bitcount                                      emodules/mod_mixture/bmp_parser/bmp.o
                                                  emodules/mod_mixture/bmp_parser/Parse_Picture.o
get_height                                        emodules/mod_mixture/bmp_parser/bmp.o
                                                  emodules/mod_mixture/bmp_parser/Parse_Picture.o
get_logo_scroll_tid                               emodules/mod_mixture/mod_mixture.o
get_matrix                                        emodules/mod_mixture/bmp_parser/bmp.o
                                                  emodules/mod_mixture/bmp_parser/Parse_Picture.o
get_movie_stop                                    emodules/mod_mixture/mod_mixture.o
get_ram_matrix                                    emodules/mod_mixture/bmp_parser/bmp.o
get_rowsize                                       emodules/mod_mixture/bmp_parser/bmp.o
                                                  emodules/mod_mixture/bmp_parser/Parse_Picture.o
get_width                                         emodules/mod_mixture/bmp_parser/bmp.o
                                                  emodules/mod_mixture/bmp_parser/Parse_Picture.o
hvideo_lyr                                        emodules/mod_mixture/mod_mixture.o
ioctl                                             ./elibrary/bin//libsyscall.a(syscall_fops.o)
                                                  emodules/mod_mixture/startup_check/startup_check.o
                                                  emodules/mod_mixture/mod_mixture.o
is_det_backcar                                    emodules/mod_mixture/mod_mixture.o
last_backcar_check_io                             emodules/mod_mixture/mod_mixture.o
logo_crolling_thread_init                         emodules/mod_mixture/mod_mixture.o
logo_crolling_thread_uninit                       emodules/mod_mixture/mod_mixture.o
logo_is_running                                   emodules/mod_mixture/mod_mixture.o
memcpy                                            /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/../../../../riscv64-unknown-elf/lib/rv64imafdcxtheadc/lp64d/libc.a(lib_a-memcpy.o)
                                                  ./elibrary/bin//liblzma.a(LzmaEnc.o)
                                                  ./elibrary/bin//liblzma.a(LzmaDec.o)
                                                  emodules/mod_mixture/mod_mixture.o
memmove                                           /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/../../../../riscv64-unknown-elf/lib/rv64imafdcxtheadc/lp64d/libc.a(lib_a-memmove-stub.o)
                                                  ./elibrary/bin//liblzma.a(LzFind.o)
memset                                            /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/../../../../riscv64-unknown-elf/lib/rv64imafdcxtheadc/lp64d/libc.a(lib_a-memset.o)
                                                  ./elibrary/bin//libminic.a(elibs_stdio.o)
                                                  emodules/mod_mixture/startup_check/startup_check.o
                                                  emodules/mod_mixture/mod_mixture.o
mix_cedar_close                                   emodules/mod_mixture/mod_mixture.o
mix_hced                                          emodules/mod_mixture/mod_mixture.o
mix_release_video_layer                           emodules/mod_mixture/mod_mixture.o
mix_request_video_layer                           emodules/mod_mixture/mod_mixture.o
mkdir                                             ./elibrary/bin//libsyscall.a(syscall_fops.o)
mmap                                              ./elibrary/bin//libsyscall.a(syscall.o)
mod_mixture_mp                                    emodules/mod_mixture/mod_mixture.o
modinfo                                           emodules/mod_mixture/magic.o
munmap                                            ./elibrary/bin//libsyscall.a(syscall.o)
open                                              /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/../../../../riscv64-unknown-elf/lib/rv64imafdcxtheadc/lp64d/libc.a(lib_a-sysopen.o)
                                                  emodules/mod_mixture/startup_check/startup_check.o
                                                  emodules/mod_mixture/mod_mixture.o
open_bmp                                          emodules/mod_mixture/bmp_parser/bmp.o
                                                  emodules/mod_mixture/bmp_parser/Parse_Picture.o
open_ram_bmp                                      emodules/mod_mixture/bmp_parser/bmp.o
                                                  emodules/mod_mixture/bmp_parser/Parse_Picture.o
opendir                                           ./elibrary/bin//libsyscall.a(syscall_fops.o)
pipe                                              ./elibrary/bin//libsyscall.a(syscall.o)
plogo_data                                        emodules/mod_mixture/mod_mixture.o
plogo_data_init                                   emodules/mod_mixture/mod_mixture.o
plogo_data_uninit                                 emodules/mod_mixture/mod_mixture.o
poll                                              ./elibrary/bin//libsyscall.a(syscall.o)
printk                                            ./elibrary/bin//libminic.a(elibs_stdio.o)
                                                  emodules/mod_mixture/mod_mixture.o
read_num                                          emodules/mod_mixture/mod_mixture.o
read_picture_index                                emodules/mod_mixture/mod_mixture.o
read_pixel                                        emodules/mod_mixture/bmp_parser/bmp.o
read_ram_pixel                                    emodules/mod_mixture/bmp_parser/bmp.o
readdir                                           ./elibrary/bin//libsyscall.a(syscall_fops.o)
remove                                            ./elibrary/bin//libsyscall.a(syscall_fops.o)
rewinddir                                         ./elibrary/bin//libsyscall.a(syscall_fops.o)
rmdir                                             ./elibrary/bin//libsyscall.a(syscall_fops.o)
rt_device_register                                ./elibrary/bin//libsyscall.a(syscall.o)
rt_device_unregister                              ./elibrary/bin//libsyscall.a(syscall.o)
save_logo_buf                                     emodules/mod_mixture/mod_mixture.o
save_logo_part_mount                              emodules/mod_mixture/mod_mixture.o
save_logo_size                                    emodules/mod_mixture/mod_mixture.o
scrolling_percent                                 emodules/mod_mixture/mod_mixture.o
select                                            ./elibrary/bin//libsyscall.a(syscall.o)
settimeofday                                      ./elibrary/bin//libsyscall.a(syscall_fops.o)
show_bmp_logo                                     emodules/mod_mixture/mod_mixture.o
show_movie                                        emodules/mod_mixture/mod_mixture.o
show_picture                                      emodules/mod_mixture/mod_mixture.o
src_height                                        emodules/mod_mixture/mod_mixture.o
src_width                                         emodules/mod_mixture/mod_mixture.o
statfs                                            ./elibrary/bin//libsyscall.a(syscall_fops.o)
stop_picture                                      emodules/mod_mixture/mod_mixture.o
video_close                                       emodules/mod_mixture/backcarcheck/dis_checkio.o
                                                  emodules/mod_mixture/mod_mixture.o
video_config_para                                 emodules/mod_mixture/backcarcheck/dis_checkio.o
                                                  emodules/mod_mixture/mod_mixture.o
video_exit                                        emodules/mod_mixture/backcarcheck/dis_checkio.o
                                                  emodules/mod_mixture/mod_mixture.o
video_get_contrast                                emodules/mod_mixture/backcarcheck/dis_checkio.o
video_get_format                                  emodules/mod_mixture/backcarcheck/dis_checkio.o
                                                  emodules/mod_mixture/mod_mixture.o
video_get_luma                                    emodules/mod_mixture/backcarcheck/dis_checkio.o
video_get_saturation                              emodules/mod_mixture/backcarcheck/dis_checkio.o
video_get_status                                  emodules/mod_mixture/backcarcheck/dis_checkio.o
                                                  emodules/mod_mixture/mod_mixture.o
video_init                                        emodules/mod_mixture/backcarcheck/dis_checkio.o
                                                  emodules/mod_mixture/mod_mixture.o
video_open                                        emodules/mod_mixture/backcarcheck/dis_checkio.o
                                                  emodules/mod_mixture/mod_mixture.o
video_rotate_angle                                emodules/mod_mixture/backcarcheck/dis_checkio.o
video_set_contrast                                emodules/mod_mixture/backcarcheck/dis_checkio.o
                                                  emodules/mod_mixture/mod_mixture.o
video_set_enhance                                 emodules/mod_mixture/backcarcheck/dis_checkio.o
video_set_luma                                    emodules/mod_mixture/backcarcheck/dis_checkio.o
                                                  emodules/mod_mixture/mod_mixture.o
video_set_saturation                              emodules/mod_mixture/backcarcheck/dis_checkio.o
                                                  emodules/mod_mixture/mod_mixture.o
write                                             /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/../../../../riscv64-unknown-elf/lib/rv64imafdcxtheadc/lp64d/libc.a(lib_a-syswrite.o)
                                                  emodules/mod_mixture/startup_check/startup_check.o
