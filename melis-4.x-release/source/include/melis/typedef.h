#ifndef __MELIS_TYPEDEF__
#define __MELIS_TYPEDEF__

#ifdef __cplusplus
extern "C" {
#endif

#include <stddef.h>
#include <stdint.h>
#include <stdarg.h>
#include <limits.h>

/************************************************************************************************************/
/***********************************************USER DEFINE**************************************************/
/************************************************************************************************************/
#define IIS_DEBUG 1
#define MAIN_SCREEN_TEST  0//peter20210331
//#define DMX125_TOUCH

#define JVC_KENWOOD  0// 1:JVC	0:KENWOOD
//#define DMX125_TOUCH

#define MIRROR_RAW_DEC 1
//#define VDECODE_MODE_AWRAW			//peter202101201
#define ADD_UDISK_PART				1//peter202101201
#define USE_SDK_VIDEO_MOD			1//peter20220526
#define USE_LVGL_GUI				1//peter20220526
#define ADD_MULTIPLE_LANGUAGE_MENU	1
#define ADD_CHINESES_LANGUAGE_MENU	1
#define NEED_CONSERVE_MEMORY		1//peter20220720
#define USE_KEYBOARD_DEV			1//peter20221213
#define USE_LOG_PRINT				0//peter20230822
#define EFUSE_GET_CHIPID			1
#define SAVE_LAST_VIDEO_CONFIG		1//peter20230301

#define USE_CUSTOM_MITSUBISHI 		//peter20230906
#define USB_RECOGNIZE_HUB

#define USE_FM_FUNC 				//peter20230906

//#define USE_PT2258_BP1064 			//peter20240511
#define AK7604_DEC_GAIN 			//Add By Deng With 20231012

#define USE_AMP_MUTE 				//peter20240829
#define USE_BT_POWER 				//peter20240829

#define USE_NEW_SMENU				//peter20240605
#ifdef USE_NEW_SMENU
#define USE_BT_SEARCH_FUN			//peter20240605
#endif
#define USE_NEW_MIRROR				//peter20240613

#define USE_IAP_HID					//peter20240712

#define USE_PARTY_MODE_SOURCE		//peter20250516

#define NEW_HOME_UI

//#define USE_AIRPLAY_MIRROR			//peter20241105

//#define USE_MENU_USB_NO_FOLDER		//peter20250117
#define USE_MENU_ANIM		            //peter20250422
#define USE_MENU_WARNING_BAR		    //peter20250527


#define USE_PHONE_MIRROR_TOP_BAR_SETUP				//song20241216
#define USE_WIRLESS_MIRROR_TOUCH_KEY	 			//song20241218
#define USE_IPHONE_TOUCH_KEY	 					//song20241219
#define USE_NEW_INSTALL_MIRROR_APP_WIN	 			//song20241220


#define USE_UDC_DMA

#define DEV_NET_THREAD

#define USE_AIC8800
#define USE_DHCPD
//#define USE_AP_P2P_GO_COEXIST //add by deng 20241108

//#define USE_DISP_LAYER_SYNC		//peter20240722
#define USE_DISP_SETTING_CHANGE		//peter20240722
#define USE_MIXTURE_SETTING_CHANGE	//peter20240722
//#define USE_PD_UPDATE				//peter20241122

#define USE_ESKSRV_RAW_DECODER		//peter20241213

#define GX2900_MODEL

#define AUX_AFTER_TUNRE_INIT //Lin 241227

#define MIPI_TFT
#define USE_AUX_FUNCTION

#define USE_GXPMX30_MODEL

#define USE_ALBUM_BG_FG

#ifdef MIPI_TFT
//#define USE_1024_600
//#define USE_1280_720
#ifdef USE_GXPMX30_MODEL
//#define USE_640_480
#endif

#ifdef USE_1280_720
#define TFT_LCD_W 1280
#define TFT_LCD_H 720
#elif defined(USE_640_480)
#define TFT_LCD_W 640
#define TFT_LCD_H 480
#else
#define TFT_LCD_W 1024
#define TFT_LCD_H 600
#endif

#if defined(USE_1024_600)
#define UI_SCREEN_W 1024
#define UI_SCREEN_H 600
#elif defined(USE_1280_720)
#define UI_SCREEN_W 1280
#define UI_SCREEN_H 720
#elif defined(USE_640_480)
#define UI_SCREEN_W 640
#define UI_SCREEN_H 480
#else
#define UI_SCREEN_W 800
#define UI_SCREEN_H 480
#endif
#endif

#define ABLUM_PICTURE_SET_BG_ENABLE

#define BROWER_PHONEBOOK_MEMORY_OPTIMIZE

#if USE_SDK_VIDEO_MOD
//#define VIDEO_CSI
#define VIDEO_TVD
#endif

#ifdef VIDEO_CSI
#define AHD_FOR_FRONT_REAR_CAMERA
#define USE_SENSOR_N5
#define USE_SENSOR_TP9951
#define USE_SENSOR_XS9950
#define USE_SENSOR_4_CHANNEL
//#define AHD_FORMAT_DISP_TEXT //peter20230724
//#define TEST_REG_PRINT
#define ADD_AHD_MODE
#endif
//#define AVIN_ADJUST_HUE//test


#if USE_LVGL_GUI
#define LV_USE_F133_MSG
#define LV_FIX_ANIM_BUG					1//peter20230428
#define LV_LIST_AUTO_FOCUSE				1//peter20230428
#define LV_F133_USE_ASYNC_THREAD		1//peter20230611
#define LV_BG_USE						0//peter20230724
#define LV_BG_LAYER_USE					0//peter20230724
#define LV_BG_VIDEO_IMG					0//deng20230808
//#define LV_ADD_ANIM_SCREEN			//peter20230914
#if LV_F133_USE_ASYNC_THREAD
//#define LV_USE_API					//peter20231220
#endif
//#define TEST_LVGL_APP
#endif
#if JVC_KENWOOD
#else
//#define KENWOOD_DMX_1025
#endif
//#define TY8500_TFT

//#define MITS70_TFT
#define GX2800_MODEL_TYPE


#ifdef GX2800_MODEL_TYPE

#define GX2800_NEW_UI_FLOW
//#define GX2820_TYPE
#define GX2900_MODEL_TYPE			//peter20220712
#define GX2900_1024_TOUCH			0//peter20231027
#define GX2900_NEW_TOUCH			0//peter20231027
#define GX2900_6_8_INCH_JB			0//peter20240103
#define GX2900_6_8_INCH_HC			0//peter20240103
#define GX2900_6_8_INCH_HC_JB		1//peter20240103
#define GX2900_6_8_INCH_JB_NEW		1//peter20240708
#endif

#define  CW1048_MODEL


//#define BP1064_MODEL
//#define AK7604_DSP

//#define BP1048_MODEL

#define PB7_MCLK

#define AUX_MAX_VOL  40



//#define SUPPORT_ECLINK_SMX_MIRROR

//#define USB_SWITCH_IO_AUTO

#if (defined(BP1064_MODEL) && !defined(AK7604_DSP))
#define BP1064_SUB_CHANGE_DAC_CHANNEL
#endif


#ifdef AK7604_DSP
#define DSP_DEFEND
#endif

#define IPOD_C800_BROSWER


#define DMX7018_SIMILARITY
//#ifdef GX2900_MODEL
//#if GX2900_NEW_TOUCH
//#define TOUCH_OFFSET		(56)
//#else
//#define TOUCH_OFFSET		(40)
//#endif
//#else
#define TOUCH_OFFSET		(0)
//#endif

#define INPUT_DEVICE_CHECK	

#define CONTROL_VOLUME_DEFAULT (20)

//parking line sel icon
#define 	SEL_ICON_WIDTH		64
#define 	SEL_ICON_HIGH		70


//#define HOUSE_TEST
//#define ABNORMAL_TEST

#define LAST_MEMORY_PATH_CMP_NOT_INCLUDE_PARTION
#define SMX_TF100_PRJ


//#define SOURCE_SWITCH_GUI_DISP_BUG	//add by Deng 2020-02-25 for GUI bug
//#define SOURCE_SWITCH_MODE	
#define GET_TUNER_INFO


#define USB_PLAYER_SUPPORT_M4A_FILE
#define USB_PLAYER_SUPPORT_FF_RR //for m4a


#define NEW_MCU_MODEL


#define FIX_C800_UPDATE_FLUSH_FLASH_BUG

#define MCU_UPGRADE_PKG_DATA_MAX	512

//upgrade
//upgrade dialog
#define UPGRADE_TIMEOUT	(10*(1000/10)) // 10s

#define FW_MCU_BT_UPGRADE_TOGETHER
#define RMT_UPDATE_USE_ONE_FILE

#ifdef GX2900_MODEL
#ifdef AK7604_DSP
#define FW_UPDATE_FILENAME        "GX2900_F133_AK7604_CW1048_.img"
#define FW_CMP_LEN		   	  (26+3)	
#else
#define FW_UPDATE_FILENAME        "PMX30_GUI_.img"
#define FW_CMP_LEN		   	  (10+3)	
#endif


#define BT_FW_UPDATE_CW1048_FILENAME  "CW1048_DSP_GX2900.mva"
#define BT_FW_CW1048_CMP_LEN		   (17+3)

#define BT_FW_UPDATE_FILENAME 	  "PMX30_DEC_.mva"
#define BT_FW_CMP_LEN		   (9+3)

#define MCU_FW_UPDATE_FILENAME 	  "PMX30_MCU_.smx"
#define MCU_FW_CMP_LEN		   (10+3)

#define CWBT_FW_UPDATE_FILENAME 	  "PMX30_LEA_.mvl"
#define CWBT_FW_CMP_LEN		   (10+3)

//for remote update
#define MCU_REMOTE_UPDATE_FILENAME		"PMXR20_MCU_.smx"
#define MCU_REMOTE_CMP_LEN	   (11+3)

#define FW_REMOTE_UPDATE_FILENAME		"PMXR20_GUI_.img"
#define FW_REMOTE_CMP_LEN		(11+3)

//for update find in scan
#define UPDATE_FW_FILE	0x01
#define UPDATE_MCU_FILE  0x02
#define UPDATE_BT_FILE	0x04 //for M3
#define UPDATE_CWBT_FILE 0x08 //for M3
#define UPDATE_ALL_FILE	0x0F

#ifdef USE_PD_UPDATE
#define PD_FW_UPDATE_FILENAME 	  "KeLX_1C_HUSB362BF_27W_APP_.bin"
#define PD_FW_CMP_LEN		   (26+3)
#endif
#else
#define FW_UPDATE_FILENAME        "GX2950_F133_CW1048_CW1048_.img"
#define FW_CMP_LEN		   	  (26+3)	


#define BT_FW_UPDATE_CW1048_FILENAME  "CW1048_DSP_GX2950.mva"
#define BT_FW_CW1048_CMP_LEN		   (17+3)

#define BT_FW_UPDATE_FILENAME 	  "GX2850.mva"
#define BT_FW_CMP_LEN		   (6+3)


#define MCU_FW_UPDATE_FILENAME 	  "GX2950_MCU_.smx"
#define MCU_FW_CMP_LEN		   (11+3)
#endif

#define FONT_SHADOW_ENABLE

#define DAB_SW_VERSION		"dab_radio_5_0_9"

//#define __EXPL_LISTBAR__

#define MAX_MUSIC_NUMBER	20000 //include folder/sub folder
#define MAX_MOVIE_NUMBER	5000 //include folder/sub folder
#define MAX_PHOTO_NUMBER	10000 //not include folder/sub folder

#define ANDROID_ADB_ENABLE

//#define ADJUST_DVR
#define ADB_CAPTURING_SCREEN_TIP

//#define AUTO_SWITCH_ADB

//#define TOUCH_TEST


//#define IPOD_STATUS_TEST
//#define BLUETOOTH_CONNECT_TEST

//#define BT_VOICE_TEST

//#define IPOD_BROWSER_TIMEOUT
//#define IPOD_PLAYLIST_TIMEOUT
//#define MFI_STATUS_TEST
//#define MFI_TAIWAN_MODE_TEST
#ifdef MFI_STATUS_TEST
#define TID_STATUS_TEST
#endif

#ifndef MFI_STATUS_TEST
#define BT_DOUBLE_LINK
#endif

#ifdef CW1048_MODEL
#ifndef MFI_STATUS_TEST
#define IPOD_PLAYLIST_TIMEOUT
#define IPOD_RESTART_USB_POWER

#endif

#define BT_MODEL_SELET

//#define BT_RESTART_UPDATE

#define BT_PHONEBOOK_SORT
#ifdef GX2800_NEW_UI_FLOW
#ifndef MFI_STATUS_TEST
#define BLE_SELECT

#endif
#ifdef BLE_SELECT
//#define BLE_CONNECT_TEST
#endif
#endif


#ifdef BLE_SELECT
#define DEVICE_NAME   "GX2900"
#endif

//#define BLE_TEST

#define IPOD_ERR_RESTART_POWER

#endif

//#define VOICE_TEST


//#define TEST_IPOD_BTAUDIO
//#define TEST_MIRROR_FM
//#define BT_CONNECT_TEST


#define PHONE_WAITTING_FUNC

#define BTIAP_BROSWER

//#define TEST_POWER_OFF
#ifdef TID_STATUS_TEST
#define AUTO_SWITCH_ADB_USB_IPOD
#define USB_SWITCH_IO_AUTO

#endif
//#define IPOD_ACC_TEST
//#define IPOD_TEST


#define CLOCK_MODE_ENABLE //mllee 130318
#define CLOCK_MODE_24H			0
#define CLOCK_MODE_12H			1

//#define DSP_MONOIN
//#define RLT8731_WIFI_MOUDEL
#ifdef GX2900_MODEL
#define RLT8731_AP_SSID	"GX2900"
#else
#define RLT8731_AP_SSID	"GX2950"
#endif

#define RLT8731_P2P_SSID 	"DIRECT-GX2900"

#define RLT8731_AP_PASSWD	"abcde12345"
#define RLT8731_AP_CHANNEL	149
//#define RLT8731_IPERF_CMD

#ifdef RLT8731_WIFI_MOUDEL
//#define WIFI_ONOFF_TEST

#define WIFI_ANDROID_MIRROR
//#define WIFI_LIST_DEMO
//#define WIFI_SCREEN_DISPLAY
#ifdef WIFI_SCREEN_DISPLAY
#define WIFI_SCREEN_STA
#endif
#endif

//#define UPDATE_BY_OTA  //song20240911


#define MIRROR_SPP_FUNC

#define INIT_SHOW_BACKCAR
#ifdef INIT_SHOW_BACKCAR
#define INIT_ONLY_SHOW_PARKINGLINE
#endif


//#define BACKCAR_TEST
//#define SYSTEM_TEST_BUGS
//#define FM_IPOD_TEST


//#define NO_FM3_AM2_BAND
#ifdef GX2800_MODEL_TYPE
#define PARKING_MODE_ENTER
#ifndef USE_CUSTOM_MITSUBISHI
#define DVR_ENTER
#define DEMO_FUNC
#endif
#endif


//#define USB_TEST //iPhone²»¶ÁºÍAPPO A3²»¶ÁÈí¼þ²âÊÔ

#define USB_3_0_SUPPORT
#define USB_CONNECT_CHECK_ENABLE

#define USB_BROSWER_ALBUM_ARTIST_SORT
#ifdef USB_BROSWER_ALBUM_ARTIST_SORT
#define USB_BROSWER_SAVE_INDEX_SORT
#define USB_BROSWER_SORT_AT_BACKGROUND
#endif
//#define AOA_ENABLE

#if 1//ndef GX2800_MODEL_TYPE
#define APP_UPGARDE_QRCODE
#endif

//#define MIRROR_TOUCH_TEST
#define MIRROR_SUPPORT_SET_PHONE_LANDSCAPE


#ifdef APP_UPGARDE_QRCODE
//#define APP_UPGARDE_NEW_FLOW
#define APP_UPGARDE_NEW_FLOW_RESTART
#define BRAND_NAME
#define IOS_APP_UPGRADE_ENABLE

#endif
//#define AOA_READ_WITH_USBH
//#define AOA_TEST
//#define AOA_TEST_SAVE_STREAM
//#define AOA_TEST_STREAM_WITH_LEN
#define AOA_HID_MUTITOUCH_SUPPORTED
//#define AOA_HID_MOUSE_SUPPORTED
#define AOA_BT_HID_SUPPORTED
//#define AOA_BT_NO_SUPPORT_AUTO_CONN//add by deng 20241228
//#define MIRROR_FULL_SCREEN_SUPPORTED
//#define IOS_BT_HID_SUPPORT
//#define IOS_MIRROR_SUPPORT
#define IOS_MIRROR_LOCK_SCREEN_TIPS	//add by deng 20201027
#ifdef IOS_MIRROR_LOCK_SCREEN_TIPS
#define IOS_MIRROR_LOCK_SCREEN_TIPS_COMPATI	//add by deng 20241228
#endif
//#define IOS_MIRROR_LOCK_DOWN_TIPS	//add by deng 20201027
//#define IOS_MIRROR_REPAIR_INFO_SAVE	//add by deng 20201110
//#define IOS_MIRROR_MFI	//add by deng 20201114
#define IOS_MIRROR_APP
#define IOS_MIRROR_AUDIO_HOLD
#define IOS_MIRROR_SHOW_LOADING
#define IOS_MIRROR_RESET_PORT
//#define IOS_MIRROR_ERROR_TIPS
#define IOS_MIRROR_MUXD_COMMAND_WAIT
#define IOS_MIRROR_AIR_PLAY_FLAG
#define IOS_MIRROR_TOUCH_SCREEN_FLAG

//#define IOS_MIRROR_TEST_BUG
//#define IOS_MIRROR_TEST_BUG_1
//

#define DENG_FIX_MIRROR_BUG_20231207 //for usb&wifi mirror

#ifdef GX2800_MODEL_TYPE
#define IOS_APP_WALLPAPER_ENABLE //´øMIRRORappµÄºê
#define TCP_MULTIPLEXER_PORT //´øMIRRORappµÄºê
#endif



#define IOS_MIRROR_APP_LUNCH


//#define IOS_MIRROR_APP_SAVE_UDID
#define IOS_MIRROR_APP_CURRENT_MODE
#define IOS_MIRROR_APP_USB_READ_TIMEOUT
//#define IOS_MIRROR_FEED_DATA_SAVE
#define IOS_MIRROR_SET_REPLAY_LAST_FRAME
#define IOS_CARPLAY		0

#if IOS_CARPLAY
#define WIRED_CARPLAY_IPADDR "fe80::8a00:33ff:fe77:69cc"//"fe80::3812::a7ff::fecd:85b3"
#define WIRED_CARPLAY_PORT 7000
#define WIRED_CARPLAY_DEVICE_ID "1C:50:1E:75:B5:DC"
#define WIRED_CARPLAY_PUBLIC_KEY "00002e00-c439-47f4-9057-4088c602e81c"//"d003a3f1-1372-4d98-8b9c-0cc91b7e382b"
#define WIRED_CARPLAY_SOURCE_VERSION "480.5"//from kAirPlaySourceVersion
#endif

#ifdef CONFIG_DRIVERS_USB_GADGET_QVH
#define USE_QVH_PHONE_DEV_FOR_FILESYSTEM
#endif
#define USE_HID_UP_FUN
#define USE_HID_MULTIPOINT_FUN
#ifdef USE_HID_MULTIPOINT_FUN
#define HID_MULTIPOINT_NUM 3//5
#endif

//#define AOA_WALLPAPER_ENTER

//for explroer listbar
//#define EXPLR_SQUARE_MODE_SHOW_LOADING //add by deng

#define HOME_WALLPAP_TYPE 0x01
#define PLAYWIN_WALLPAP_TYPE 0x02
#define RADIO_WALLPAP_TYPE 0x04
#ifdef IOS_MIRROR_APP_CURRENT_MODE
#define IPOD_WALLPAP_TYPE 0x08
#endif
#define MIRROR_WALLPAP_TYPE 0x10

#define ALL_WALLPAP_TYPE 0x8000

//#define AOA_BG_SIZE ((512-64)*1024)
#define AOA_BG_SIZE (256*1024)
#ifdef AOA_WALLPAPER_ENTER
#define APPFS_NO_BACKUP_BLOCK	//add by Deng
#define APPFS_NO_LOAD_PART		//add by Deng
#define FB_FILE_SAVE_AFTER_COMPERSS		//add by Deng

#ifdef FB_FILE_SAVE_AFTER_COMPERSS
#define FB_FILE_MAX_COMPRESS_PROPS_LEN    128
#define FB_FILE_MAX_EXPAND_RATIO          1

#define WALLPAPER_FB_FILE_MAX_EXPAND_RATIO          2

#endif
#ifdef APPFS_NO_BACKUP_BLOCK
#define SPLASH_WALLPAP_FILE_PATH 			"y:\\splash_wallpaper.bin"
#else
#define SPLASH_WALLPAP_FILE_PATH 			"z:\\splash_wallpaper.bin"
#endif
#endif


//#define USB_IPHETH_FUNCTION



//for explroer listbar
//#define EXPLR_SQUARE_MODE_SHOW_LOADING //add by deng



//for dab
#define DAB_PRESET_LIST_ENABLE
#define DAB_RELATED_SERVICE_POPUP_TIPS
//#define DAB_LINKING_TEST
//#define DAB_VERSIONS_SHOW
//#define DAB_PRINTF_RSSI
#define DAB_CHECK_RESET
//#define DAB_RESET_PRINTF
#define DAB_VERSIONS_SHOW
//#define DAB_INFO_SHOW
#define DAB_SOFT_MUTE

//#define TUNER_PRINT_ALL_INFO_WIN
#define TUNER_AUTO_SEEK_COUNT
#define TUNER_AUTO_SEEK_FREQ_OFFSET
#define TUNER_SAVE_RDS_AF_LIST

//#define TUNER_PI_SEEK


#define TUNER_FAST_CUT_PARA
//#define TUNER_AREA_SELECT
//movie
#define MOVIE_SPSC_SLEEP
//#define MOVIE_SPSC_SLEEP_LAYER_SUSPEND
#define VIDEO_DISPLAY_DIFF1
#define VIDEO_SUBSHOW_PROTECT
#define AVIN_DISPLAY_DIFF
//#define MIRROR_DISPLAY_DIFF

#define POWER_ON_ACC_NOISE_BUG
#define BOOT_SPEED

#define BG_TYPE_RGB
#define TTF_FONT_FOR_DEFAULT
#define TTF_FONT_MULTIPLE_LANGUAGES
#define BT_PHONE_VOL_CONTROL_ENABLE
#define BT_PHONE_SET_EQ2OFF_ENABLE
//#define DEFAULT_LANGUAGE		EPDK_LANGUAGE_ENM_ENGLISH
#define DEFAULT_OUTPUT			LION_DISP_LCD
//#define TUNER_RSSI_SNR_TEST
#define DIMMER_LCD_BACKLIGHT	LION_BRIGHT_LEVEL10

#define SLIDESHOW_DEMO
#define TV_OUTPUT_DIFF_SHOW_ENABLE

#define SWITCH_BAND_TIPS
//#define PRINTF_TO_USB
//#define MCU_DATA_PRINTF_TO_USB
#define MCU_C800_COMMUNICATION
//#define MCU_C800_COMMUNICATION_ERROR_RESET
#define MCU_C800_SET_IO_ACK

#define IPOD_BROWSER_SIZE   (8*1024)

#define FLASH_SAVE_FILE_PATH 			"e:\\flash_save.bin"
#define FLASH_SAVE_VERSION_INFO		 "REG3"
#define FLASH_SAVE_VERSION_INFO_LEN (strlen(FLASH_SAVE_VERSION_INFO))

#define SHOW_SAVED_LOGO_ENABLE
#define SPLASH_LOGO_FILE_PATH 			"z:\\splash_logo.bin"
#define FW_UPDATE_REMAIN_SAVED_LOGO_ENABLE

//#define FW_UPDATE_REMAIN_SAVED_PARKING_LINE_ENABLE
//#define MCU_FLASH_SAVE_ENABLE

//LED PWM 
#define KEY_LED_PWM5_ENABLE

//for cedar
#define APP_LICENSE_FROM_MOD_LICENSE



#ifdef BRAND_NAME
#define MODEL_BRAND_NAME  		"GRUNDIG"
#define MODEL_NAME_MAIN		"GX2900"
#endif



#if 0//def GX2800_MODEL_TYPE
#ifdef GX2900_MODEL_TYPE
#define BT_DEVICE_NAME     "GX2950"//"Mitsubishi"
#else
#ifdef GX2800_NEW_UI_FLOW
#define BT_DEVICE_NAME     "GX2820"//"Mitsubishi"
#else

#define BT_DEVICE_NAME     "GX2800"//"Mitsubishi"
#endif
#endif
#else
#ifdef GX2900_MODEL
#define BT_DEVICE_NAME     "GX2900"//"Mitsubishi"
#else
#define BT_DEVICE_NAME     "GX2950"//"Mitsubishi"
#endif
#endif
#if 0//def GX2800_MODEL_TYPE
#ifdef GX2900_MODEL_TYPE
#define MODEL_NAME		"GX2950"
#else
#ifdef GX2800_NEW_UI_FLOW
#define MODEL_NAME		"GX2820"
#else

#define MODEL_NAME     "GX2800"//"Mitsubishi"
#endif
#endif
#else
#ifdef GX2900_MODEL
#ifdef USE_GXPMX30_MODEL
#define MODEL_NAME		    "PMX30"
#define MODEL_NO1_NAME	    "PMX20"
#define MODEL_REMOTE_NAME	"PMX-R20"
#else
#define MODEL_NAME		"GX2900"
#endif
#else
#define MODEL_NAME_MITS69		"GX2950_MITS69"
#define MODEL_NAME_MITS70		"GX2950_MITS70"
#endif
#endif


#define GUI_SW_VERSION	 	"250816000132M"//Õæ°æ±¾ºÅ£¬½öÄÚ²¿¿É¼û
#define GUI_FALSE_SW_VERSION	 	"250816000132M"//¼Ù°æ±¾ºÅ£¬¿Í»§¿É¼û



#ifdef APP_UPGARDE_QRCODE


#if 1//def CW1048_MODEL
//#define UPGARDE_MASTER_CW1048_CONTROL_PREFIX				"GX2850_C800_CW1048_"
//#define UPGARDE_MASTER_CW1048_CONTROL_HARDWARE_VERSION		"GX2850_C800_V1_0002"
#if 0//def GX2800_MODEL_TYPE
#ifdef GX2900_MODEL_TYPE
#define UPGARDE_MASTER_CW1048_BT_PREFIX						"CW1048_GX2900_BT_"
#define UPGARDE_CW1048_BT_HARDWARE_VERSION					"GX2900_BT_800_V1_0002"
#else
#ifdef GX2800_NEW_UI_FLOW
#define UPGARDE_MASTER_CW1048_BT_PREFIX						"CW1048_GX2820_BT_"
#define UPGARDE_CW1048_BT_HARDWARE_VERSION					"GX2820_BT_800_V1_0002"
#else
#ifdef  GX2820_TYPE
#define UPGARDE_MASTER_CW1048_BT_PREFIX						"CW1048_GX2820_BT_"
#define UPGARDE_CW1048_BT_HARDWARE_VERSION					"GX2820_BT_800_V1_0002"
#else
#define UPGARDE_MASTER_CW1048_BT_PREFIX						"CW1048_GX2800_BT_"
#define UPGARDE_CW1048_BT_HARDWARE_VERSION					"GX2800_BT_800_V1_0002"

#endif
#endif
#endif
#else
#ifdef GX2900_MODEL
#define UPGARDE_MASTER_CW1048_BT_PREFIX						"CW1048_GX2900_BT_"
#define UPGARDE_CW1048_BT_HARDWARE_VERSION					"GX2900_BT_800_V1_0002"
#else
#define UPGARDE_MASTER_CW1048_BT_PREFIX						"CW1048_GX2950_BT_"
#define UPGARDE_CW1048_BT_HARDWARE_VERSION					"GX2950_BT_800_V1_0002"
#endif
#endif
#endif

#define UPGARDE_MASTER_CONTROL_TYPE					"MPG"
#if 0//def GX2800_MODEL_TYPE
#ifdef GX2900_MODEL_TYPE
#define UPGARDE_MASTER_CONTROL_PREFIX				"GX2900_F133_"
#define UPGARDE_MASTER_CONTROL_HARDWARE_VERSION		"GX2900_F133_V1_0001"
#else
#ifdef GX2800_NEW_UI_FLOW
#define UPGARDE_MASTER_CONTROL_PREFIX				"GX2820_F133_"
#define UPGARDE_MASTER_CONTROL_HARDWARE_VERSION		"GX2820_F133_V1_0001"
#else
#ifdef  GX2820_TYPE
#define UPGARDE_MASTER_CONTROL_PREFIX				"GX2820_F133_"
#define UPGARDE_MASTER_CONTROL_HARDWARE_VERSION		"GX2820_F133_V1_0001"
#else
#define UPGARDE_MASTER_CONTROL_PREFIX				"GX2800_F133_"
#define UPGARDE_MASTER_CONTROL_HARDWARE_VERSION		"GX2800_F133_V1_0001"
#endif
#endif
#endif
#else
#ifdef GX2900_MODEL
#define UPGARDE_MASTER_CONTROL_PREFIX				"GX2900_F133_"
#define UPGARDE_MASTER_CONTROL_HARDWARE_VERSION		"GX2900_F133_V1_0001"
#else
#define UPGARDE_MASTER_CONTROL_PREFIX				"GX2950_F133_"
#define UPGARDE_MASTER_CONTROL_HARDWARE_VERSION		"GX2950_F133_V1_0001"
#endif
#endif

#define UPGARDE_MCU_TYPE								"MCU"
#if 0//def GX2800_MODEL_TYPE
#ifdef GX2900_MODEL_TYPE
#define UPGARDE_MASTER_MCU_PREFIX					"GX2900_MCU_"
#define UPGARDE_MCU_HARDWARE_VERSION				"GX2900_MCU_800_V1_0001"
#else
#ifdef GX2800_NEW_UI_FLOW
#define UPGARDE_MASTER_MCU_PREFIX					"GX2820_MCU_"
#define UPGARDE_MCU_HARDWARE_VERSION				"GX2820_MCU_800_V1_0001"
#else
#ifdef  GX2820_TYPE
#define UPGARDE_MASTER_MCU_PREFIX					"GX2820_MCU_"
#define UPGARDE_MCU_HARDWARE_VERSION				"GX2820_MCU_800_V1_0001"
#else
#define UPGARDE_MASTER_MCU_PREFIX					"GX2800_MCU_"
#define UPGARDE_MCU_HARDWARE_VERSION				"GX2800_MCU_800_V1_0001"
#endif
#endif
#endif
#else
#ifdef GX2900_MODEL
#define UPGARDE_MASTER_MCU_PREFIX					"GX2900_MCU_"
#define UPGARDE_MCU_HARDWARE_VERSION				"GX2900_MCU_800_V1_0001"
#else
#define UPGARDE_MASTER_MCU_PREFIX					"GX2950_MCU_"
#define UPGARDE_MCU_HARDWARE_VERSION				"GX2950_MCU_800_V1_0001"
#endif
#endif

#define UPGARDE_BT_TYPE								"BT"
#if 0//def GX2800_MODEL_TYPE
#ifdef GX2900_MODEL_TYPE
#define UPGARDE_MASTER_BT_PREFIX						"GX2900_BT_"
#define UPGARDE_BT_HARDWARE_VERSION					"GX2900_BT_800_V1_0001"
#else
#ifdef GX2800_NEW_UI_FLOW
#define UPGARDE_MASTER_BT_PREFIX						"GX2820_BT_"
#define UPGARDE_BT_HARDWARE_VERSION					"GX2820_BT_800_V1_0001"
#else
#ifdef  GX2820_TYPE
#define UPGARDE_MASTER_BT_PREFIX						"GX2820_BT_"
#define UPGARDE_BT_HARDWARE_VERSION					"GX2820_BT_800_V1_0001"
#else
#define UPGARDE_MASTER_BT_PREFIX						"GX2800_BT_"
#define UPGARDE_BT_HARDWARE_VERSION					"GX2800_BT_800_V1_0001"
#endif
#endif
#endif
#else
#ifdef GX2900_MODEL
#define UPGARDE_MASTER_BT_PREFIX						"GX2900_BT_"
#define UPGARDE_BT_HARDWARE_VERSION					"GX2900_BT_800_V1_0001"
#else
#define UPGARDE_MASTER_BT_PREFIX						"GX2950_BT_"
#define UPGARDE_BT_HARDWARE_VERSION					"GX2950_BT_800_V1_0001"
#endif
#endif

/*³µ»úÈí¼þ°æ±¾ºÅÃüÃû¹æÔòÈçÏÂ:   1.°æ±¾ºÅÃüÃû:V1.5.0.6325:      
1ÊÇÖ÷°æ±¾      
5ÊÇ×Ó°æ±¾     
0ÊÇ½×¶Î°æ±¾      
6235¿ÉÒÔÊÇÈÕÆÚÓëÊý×Ö    */


#define MPEG_VERSION_TEST		"V202003270001"
#define MCU_VERSION_TEST		"V202005010003"
#define BT_VERSION_TEST		"V202006080023"

#endif

#define DAY_NIGHT_MODE_ENABLE

//for sxm service default img
#define SXM_SERVICE_DEFAULT_IMG
#define SXM_SERVICE_DEFAULT_IMG_FILE_PATH	"d:\\res\\sxm_baseline\\A0000V000.jpg" //"d:\\res\\sxm_baseline\\A0000V000.png" //
#define SXM_ALBUMART1_PATH	"\\album1.jpg"
#define SXM_ALBUMART2_PATH	"\\album2.jpg"
//#define SXM_DEMO_JPG_PATH						"d:\\res\\sxm_baseline\\xyz.JPG"//A0006V000.jpg"
//#define SXM_ALBUMART_IN_FILE 
#define SXM_BF_B_ENABLE
#define SXM_BROWSER_ITEM_BLANK_PATCH
#define SXM_BROWSER_ITEM_META_BLANK_PATCH
#define SXM_BROWSER_CAT_PATCH
#define SXM_BROWSER_WAIT_ENABLE
#define SXM_POPUP_ON_ALL_WIN_PATCH //mllee 160824 added
#define SXM_SEND_MCU_CHAN_NO_ENABLE //mllee 161031 added
#define SXM_CATEGORY_BROWSER_SHOW_CATEGORY_NAME_ENABLE //mllee 161210 added
#define SXM_PRESET_SHOW_NOT_AVAILABLE_ENABLE
#define SXM_POWER_ON_NO_SELECT_CHANNEL_PATCH
#define SXM_PSI_FOR_ALBUMART_ENABLE
#define SXM_CD_B_ENABLE
//#define SXM_METADATA_ADD2LIST_SUPPORT //mllee 170115 added for no need new METADATA insert to LIST

//head unit
#define FW_UPDATE_TYPE			0x01
#define MCU_UPDATE_TYPE			0x02
#define BT_UPDATE_TYPE			0x04
#define CWBT_UPDATE_TYPE		0x08
#define LEA_UPDATE_TYPE			0x08 //for broadcast bt
#define PD_UPDATE_TYPE			0x20

//remote
#define FW_REMOTE_UPDATE_TYPE	0x11
#define MCU_REMOTE_UPDATE_TYPE 0x12
#define ALL_UPDATE_TYPE			(FW_UPDATE_TYPE|MCU_UPDATE_TYPE|BT_UPDATE_TYPE|CWBT_UPDATE_TYPE)


//for remote
//Remote
#define REMOTE_ADDR_4BYTES

#define REMOTE_USB_ALBUMART_ENABLE
#define REMOTE_USB_ALBUMART_YUV422

//for HU update remote
#define RMT_UPDATE_RECIVE_BUFFER_SIZE		(11*1024*1024)//(13*1024*1024) //231214 mod  rootfs+ramfs max: 11075584//rootfs+ramfs max: 13172736


#define TFT_TEST_ENABLE
#define TFT_TEST_TRIGER_NAME	"tft_test.tger"
#define PHOTO_PLAY_TEST_TRIGER_NAME	"photo_test.tger"

/**********************************************************************************************************************/
/*****************************************************************end**************************************************/
/**********************************************************************************************************************/



#ifndef EBASE_TRUE
#define EBASE_TRUE      0
#endif

#ifndef EBASE_FALSE
#define EBASE_FALSE     (-1)
#endif

#ifndef EBSP_TRUE
#define EBSP_TRUE       EBASE_TRUE
#endif

#ifndef EBSP_FALSE
#define EBSP_FALSE      EBASE_FALSE
#endif

#ifndef EPDK_OK
#define EPDK_OK         0
#endif

#ifndef EPDK_FAIL
#define EPDK_FAIL       (-1)
#endif

#ifndef EPDK_TRUE
#define EPDK_TRUE       1
#endif

#ifndef EPDK_FALSE
#define EPDK_FALSE      0
#endif

#ifndef EPDK_DISABLED
#define EPDK_DISABLED   0
#endif

#ifndef EPDK_ENABLED
#define EPDK_ENABLED    1
#endif

#ifndef EPDK_NO
#define EPDK_NO         0
#endif

#ifndef EPDK_YES
#define EPDK_YES        1
#endif

#ifndef EPDK_OFF
#define EPDK_OFF        0
#endif

#ifndef EPDK_ON
#define EPDK_ON         1
#endif

#ifndef EPDK_CLR
#define EPDK_CLR        0
#endif

#ifndef EPDK_SET
#define EPDK_SET        1
#endif

#ifndef NULL
#define NULL            (void*)(0)
#endif

#ifndef DATA_TYPE_X___u64
#define DATA_TYPE_X___u64
typedef uint64_t    __u64;
#endif

#ifndef DATA_TYPE_X___u32
#define DATA_TYPE_X___u32
typedef uint32_t    __u32;
#endif

#ifndef DATA_TYPE_X___u16
#define DATA_TYPE_X___u16
typedef uint16_t    __u16;
#endif

#ifndef DATA_TYPE_X___u8
#define DATA_TYPE_X___u8
typedef uint8_t     __u8;
#endif

#ifndef DATA_TYPE_X___s64
#define DATA_TYPE_X___s64
typedef int64_t     __s64;
#endif

#ifndef DATA_TYPE_X___s32
#define DATA_TYPE_X___s32
typedef int32_t     __s32;
#endif

#ifndef DATA_TYPE_X___s16
#define DATA_TYPE_X___s16
typedef int16_t     __s16;
#endif

#ifndef DATA_TYPE_X___s8
#define DATA_TYPE_X___s8
typedef int8_t      __s8;
#endif

#ifndef DATA_TYPE_X_u64
#define DATA_TYPE_X_u64
typedef uint64_t    u64;
#endif

#ifndef DATA_TYPE_X_u32
#define DATA_TYPE_X_u32
typedef uint32_t    u32;
#endif

#ifndef DATA_TYPE_X_u16
#define DATA_TYPE_X_u16
typedef uint16_t    u16;
#endif

#ifndef DATA_TYPE_X_u8
#define DATA_TYPE_X_u8
typedef uint8_t     u8;
#endif

#ifndef DATA_TYPE_X_s64
#define DATA_TYPE_X_s64
typedef int64_t     s64;
#endif

#ifndef DATA_TYPE_X_s32
#define DATA_TYPE_X_s32
typedef int32_t     s32;
#endif

#ifndef DATA_TYPE_X_s16
#define DATA_TYPE_X_s16
typedef int16_t     s16;
#endif

#ifndef DATA_TYPE_X_s8
#define DATA_TYPE_X_s8
typedef int8_t      s8;
#endif


#ifndef DATA_TYPE_X___hdle
#define DATA_TYPE_X___hdle
typedef void       *__hdle;
#endif

#ifndef DATA_TYPE_X___bool
#define DATA_TYPE_X___bool
typedef signed char __bool;
#endif

#ifndef DATA_TYPE_X___size
#define DATA_TYPE_X___size
typedef unsigned int __size;
#endif

#ifndef DATA_TYPE_X___fp32
#define DATA_TYPE_X___fp32
typedef float        __fp32;
#endif

#ifndef DATA_TYPE_X___wchar_t
#define DATA_TYPE_X___wchar_t
typedef uint16_t   __wchar_t;
#endif

#ifndef DATA_TYPE_X_uint
#define DATA_TYPE_X_uint
typedef unsigned int uint;
#endif

#ifdef __cplusplus
}
#endif

#endif

