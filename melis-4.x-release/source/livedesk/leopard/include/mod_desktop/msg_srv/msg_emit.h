/*
**************************************************************************************************************
*											         ePDK
*						            the Easy Portable/Player Develop Kits
*									           desktop system
*
*						        	 (c) Copyright 2007-2010, ANDY, China
*											 All Rights Reserved
*
* File    	: msg_emit_e.h
* By      	: Andy.zhang
* Func		:
* Version	: v1.0
* ============================================================================================================
* 2009-7-24 10:56:33 andy.z<PERSON>  create this file, implements the fundemental interface;
**************************************************************************************************************
*/

#ifndef __MSG_EMIT_H__
#define __MSG_EMIT_H__

#include <gui_msg.h>

#define LOWORD(l)               ((__u16)(__u32)(l))
#define HIWORD(l)               ((__u16)((((__u32)(l)) >> 16) & 0xFFFF))
#define LOSWORD(l)              ((__s16)(__u32)(l))
#define HISWORD(l)              ((__s16)((((__u32)(l)) >> 16) & 0xFFFF))


typedef enum __GUI_MSG_TOUCHID
{
    GUI_MSG_TOUCH_DOWN = 0x80,
    GUI_MSG_TOUCH_UP,
    GUI_MSG_TOUCH_MOVE,
    GUI_MSG_TOUCH_ZOOMIN,
    GUI_MSG_TOUCH_ZOOMOUT,
    GUI_MSG_TOUCH_NCDOWN,
    GUI_MSG_TOUCH_NCUP,
    GUI_MSG_TOUCH_NCMOVE,
    GUI_MSG_TOUCH_OVERDOWN,
    GUI_MSG_TOUCH_OVERUP,
    GUI_MSG_TOUCH_OVERMOVE,
    GUI_MSG_TOUCH_OVERZOOMIN,
    GUI_MSG_TOUCH_OVERZOOMOUT,
    GUI_MSG_TOUCH_LONGDOWN,
    GUI_MSG_TOUCH_OVERLONGDOWN
} __gui_msg_touchid_t;

typedef enum __GUI_MSGID
{
    GUI_MSG_CREATE = 0,
    GUI_MSG_DESTROY,
    GUI_MSG_PAINT,                                /* draw */
    GUI_MSG_CLOSE,
    GUI_MSG_QUIT,
    GUI_MSG_KEY,
    GUI_MSG_TOUCH,
    GUI_MSG_COMMAND,
    GUI_MSG_ENABLE,
    GUI_MSG_STSTEM,                                 /*广播消息，注册控件消息等，(GUI系统保留)*/
    GUI_MSG_NOTIFY_CHILD_DELETED,
    GUI_MSG_SET_FOCUS,
    GUI_MSG_SET_UNFOCUS,
    GUI_MSG_INVALID,
    GUI_MSG_TOUCHACTIVE,
    GUI_MSG_TOUCHINACTIVE,
    GUI_MSG_INITDIALOG,
    GUI_MSG_NCPAINT,
    GUI_MSG_ERASEBKGND,
    GUI_MSG_TIMER,

    GUI_MSG_WIN_SLEEP = 100,
    GUI_MSG_WIN_WAKEUP,
    GUI_MSG_LYR_MOVE,

    GUI_MSG_KEYTONE,
	GUI_MSG_UI_UPDATE, //mllee 141008

    GUI_MSG_CONTROL = 0x1000,                       /*控件*/

    GUI_MSG_DESKTOP = 0x2000,

    GUI_MSG_USER_DEF = 0x3000,

    GUI_MSG_
} __gui_msgid_t;


#define GUI_MSG_USER   0x2000


// typedef enum __GUI_MSG_KEYID
// {
//     GUI_MSG_KEY_UP = 0x0,
//     GUI_MSG_KEY_DOWN,
//     GUI_MSG_KEY_ENTER,
//     GUI_MSG_KEY_RIGHT,
//     GUI_MSG_KEY_LEFT,
//     GUI_MSG_KEY_ESCAPE,
//     GUI_MSG_KEY_VADD,
//     GUI_MSG_KEY_VDEC,
//     GUI_MSG_KEY_RISE,
//     GUI_MSG_KEY_POWER,
//     GUI_MSG_KEY_SOURCE,
//     GUI_MSG_KEY_CLEAR,
//     GUI_MSG_KEY_DISPLAY,
//     GUI_MSG_KEY_NUM0,
//     GUI_MSG_KEY_NUM1,
//     GUI_MSG_KEY_NUM2,
//     GUI_MSG_KEY_NUM3,
//     GUI_MSG_KEY_NUM4,
//     GUI_MSG_KEY_NUM5,
//     GUI_MSG_KEY_NUM6,
//     GUI_MSG_KEY_NUM7,
//     GUI_MSG_KEY_NUM8,
//     GUI_MSG_KEY_NUM9,
//     GUI_MSG_KEY_REPEATE,
//     GUI_MSG_KEY_BLOCK,
//     GUI_MSG_KEY_PLAY_PAUSE,
//     GUI_MSG_KEY_TITLE,
//     GUI_MSG_KEY_MENU,
//     GUI_MSG_KEY_SETUP,
//     GUI_MSG_KEY_GOTO,
//     GUI_MSG_KEY_SEL,
//     GUI_MSG_KEY_SHIFT,
//     GUI_MSG_KEY_DISC,
//     GUI_MSG_KEY_ATT,
//     GUI_MSG_KEY_POWEROFF,
//     GUI_MSG_KEY_RST,
//     GUI_MSG_KEY_CANCLE,
//     GUI_MSG_KEY_ZOOM_UP,
//     GUI_MSG_KEY_ZOOM_DOWN,
//     GUI_MSG_KEY_HOLD,

//     GUI_MSG_KEY_LONGUP = 0x50,
//     GUI_MSG_KEY_LONGDOWN,
//     GUI_MSG_KEY_LONGENTER,
//     GUI_MSG_KEY_LONGRIGHT,
//     GUI_MSG_KEY_LONGLEFT,
//     GUI_MSG_KEY_LONGESCAPE,
//     GUI_MSG_KEY_LONGVADD,
//     GUI_MSG_KEY_LONGVDEC,
//     GUI_MSG_KEY_LONGRISE,
//     GUI_MSG_KEY_LONGPOWER,
//     GUI_MSG_KEY_LONGSOURCE,
//     GUI_MSG_KEY_LONGCLEAR,
//     GUI_MSG_KEY_LONGDISPLAY,
//     GUI_MSG_KEY_LONGNUM0,
//     GUI_MSG_KEY_LONGNUM1,
//     GUI_MSG_KEY_LONGNUM2,
//     GUI_MSG_KEY_LONGNUM3,
//     GUI_MSG_KEY_LONGNUM4,
//     GUI_MSG_KEY_LONGNUM5,
//     GUI_MSG_KEY_LONGNUM6,
//     GUI_MSG_KEY_LONGNUM7,
//     GUI_MSG_KEY_LONGNUM8,
//     GUI_MSG_KEY_LONGNUM9,
//     GUI_MSG_KEY_LONGREPEATE,
//     GUI_MSG_KEY_LONGBLOCK,
//     GUI_MSG_KEY_LONGPLAY_PAUSE,
//     GUI_MSG_KEY_LONGTITLE,
//     GUI_MSG_KEY_LONGMENU,
//     GUI_MSG_KEY_LONGSETUP,
//     GUI_MSG_KEY_LONGGOTO,
//     GUI_MSG_KEY_LONGSEL,
//     GUI_MSG_KEY_LONGSHIFT,
//     GUI_MSG_KEY_LONGDISC,
//     GUI_MSG_KEY_LONGATT,
//     GUI_MSG_KEY_LONGPOWEROFF,
//     GUI_MSG_KEY_LONGRST,
//     GUI_MSG_KEY_LONGCANCLE,
//     GUI_MSG_KEY_LONGZOOM_UP,
//     GUI_MSG_KEY_LONGZOOM_DOWN,
//     GUI_MSG_KEY_LONGHOLD,
// } __gui_msg_keyid_t;


typedef enum __GUI_MSG_KEYID
{
    GUI_MSG_KEY_UP = 0x0,
    GUI_MSG_KEY_DOWN,
    GUI_MSG_KEY_ENTER,
    GUI_MSG_KEY_RIGHT,
    GUI_MSG_KEY_LEFT,
    GUI_MSG_KEY_ESCAPE,
    GUI_MSG_KEY_VADD,
    GUI_MSG_KEY_VDEC,
    GUI_MSG_KEY_RISE,
    GUI_MSG_KEY_POWER,
    GUI_MSG_KEY_SOURCE, // 10
    GUI_MSG_KEY_ANSWER,
    GUI_MSG_KEY_DISPLAY,
    GUI_MSG_KEY_NUM0,
    GUI_MSG_KEY_NUM1,
    GUI_MSG_KEY_NUM2,
    GUI_MSG_KEY_NUM3,
    GUI_MSG_KEY_NUM4,
    GUI_MSG_KEY_NUM5,
    GUI_MSG_KEY_NUM6,
    GUI_MSG_KEY_NUM7, // 20
    GUI_MSG_KEY_NUM8,
    GUI_MSG_KEY_NUM9,
    GUI_MSG_KEY_REPEATE,
    GUI_MSG_KEY_HANDUP,
    GUI_MSG_KEY_PLAY_PAUSE,
#ifdef JVC_CHEVROLET_TYPE
    GUI_MSG_KEY_SWC_POWER,
 #else
   GUI_MSG_KEY_TITLE,
 #endif
    GUI_MSG_KEY_MENU,
    GUI_MSG_KEY_SETUP,
    GUI_MSG_KEY_VOICE,
    GUI_MSG_KEY_SEL, // 30
    GUI_MSG_KEY_SHIFT,
    GUI_MSG_KEY_BAND,
    GUI_MSG_KEY_AUTO_STORE,
    GUI_MSG_KEY_HOME,
    GUI_MSG_KEY_POWEROFF,
    GUI_MSG_KEY_RST,
    GUI_MSG_KEY_CANCLE,
    GUI_MSG_KEY_ZOOM_UP,
    GUI_MSG_KEY_ZOOM_DOWN,
    GUI_MSG_KEY_HOLD,//40
    GUI_MSG_KEY_FM,
    GUI_MSG_KEY_KARAOKE, //chengf  add
    GUI_MSG_KEY_VIDEO,
    GUI_MSG_KEY_AUDIO,
    GUI_MSG_KEY_EBOOK,
    GUI_MSG_KEY_PHOTO,
    GUI_MSG_KEY_TV,
    GUI_MSG_KEY_LCDOFF,
    GUI_MSG_KEY_STOP,
    GUI_MSG_KEY_MODE,//50
    GUI_MSG_KEY_MUTE,
    //chengf   add   begin  ============
    GUI_MSG_KEY_CLEAR,
    GUI_MSG_KEY_MIC_ONOFF,
    GUI_MSG_KEY_REC_ONOFF,
    GUI_MSG_KEY_MIC_VOL_ADD,
    GUI_MSG_KEY_MIC_VOL_DEC,
    GUI_MSG_KEY_ECHO_ONOFF,
    GUI_MSG_KEY_ECHO_SETTING,
    GUI_MSG_KEY_ACCOMP_ONOFF,
    GUI_MSG_KEY_ADD_LYRIC_TIME,//60
    GUI_MSG_KEY_DEC_LYRIC_TIME,
    GUI_MSG_KEY_EQ,
    GUI_MSG_KEY_TVOUT,
    GUI_MSG_KEY_SINGER,
    GUI_MSG_KEY_ALPHABET,
    GUI_MSG_KEY_DIGITAL,
    GUI_MSG_KEY_FAVOR,
    GUI_MSG_KEY_NEWSONG,
    GUI_MSG_KEY_SEL_LIST,
    GUI_MSG_KEY_DELETE,//70
    GUI_MSG_KEY_CUTSONG,
    GUI_MSG_KEY_AUX,
    GUI_MSG_KEY_PREV,
    GUI_MSG_KEY_NEXT,
    GUI_MSG_KEY_FF,
    GUI_MSG_KEY_RR,
    GUI_MSG_KEY_NTFS_PAL,
    GUI_MSG_KEY_SEL_TIME,
    //chengf   add   end    ============
  //  GUI_MSG_KEY_USB_TF,
    GUI_MSG_KEY_TACK,
    GUI_MSG_KEY_SCAN,//80
    GUI_MSG_KEY_AUDIO_RESET,
    GUI_MSG_KEY_LIST,

    GUI_MSG_KEY_LONGUP = 0x50,
    GUI_MSG_KEY_LONGDOWN,
    GUI_MSG_KEY_LONGENTER,
    GUI_MSG_KEY_LONGRIGHT,
    GUI_MSG_KEY_LONGLEFT,
    GUI_MSG_KEY_LONGESCAPE,
    GUI_MSG_KEY_LONGVADD,
    GUI_MSG_KEY_LONGVDEC,
    GUI_MSG_KEY_LONGRISE,
    GUI_MSG_KEY_LONGPOWER,
    GUI_MSG_KEY_LONGSOURCE,//90
    GUI_MSG_KEY_LONGANSWER,
    GUI_MSG_KEY_LONGDISPLAY,
    GUI_MSG_KEY_LONGNUM0,
    GUI_MSG_KEY_LONGNUM1,
    GUI_MSG_KEY_LONGNUM2,
    GUI_MSG_KEY_LONGNUM3,
    GUI_MSG_KEY_LONGNUM4,
    GUI_MSG_KEY_LONGNUM5,
    GUI_MSG_KEY_LONGNUM6,
    GUI_MSG_KEY_LONGNUM7,//100
    GUI_MSG_KEY_LONGNUM8,
    GUI_MSG_KEY_LONGNUM9,
    GUI_MSG_KEY_LONGREPEATE,
    GUI_MSG_KEY_LONGHANDUP,
    GUI_MSG_KEY_LONGPLAY_PAUSE,
#ifdef JVC_CHEVROLET_TYPE
    GUI_MSG_KEY_LONG_SWC_POWER,
#else
    GUI_MSG_KEY_LONGTITLE,
#endif
     GUI_MSG_KEY_LONGMENU,
    GUI_MSG_KEY_LONGSETUP,
    GUI_MSG_KEY_LONGVOICE,
    GUI_MSG_KEY_LONGSEL,
    GUI_MSG_KEY_LONGSHIFT,
    GUI_MSG_KEY_LONGBAND,
    GUI_MSG_KEY_LONGHOME,
    GUI_MSG_KEY_LONGPOWEROFF,
    GUI_MSG_KEY_LONGRST,
    GUI_MSG_KEY_LONGCANCLE,
    GUI_MSG_KEY_LONGZOOM_UP,
    GUI_MSG_KEY_LONGZOOM_DOWN,
    GUI_MSG_KEY_LONGHOLD,
    GUI_MSG_KEY_LONGFM,
    GUI_MSG_KEY_LONGKARAOKE, //chengf  add
    GUI_MSG_KEY_LONGVIDEO,
    GUI_MSG_KEY_LONGAUDIO,
    GUI_MSG_KEY_LONGEBOOK,
    GUI_MSG_KEY_LONGPHOTO,
    GUI_MSG_KEY_LONGTV,
    GUI_MSG_KEY_LONGLCDOFF,
    GUI_MSG_KEY_LONGSTOP,
    GUI_MSG_KEY_LONGMODE,
    GUI_MSG_KEY_LONGMUTE,

    //chengf   add   begin  ============
    GUI_MSG_KEY_LONGCLEAR,
    GUI_MSG_KEY_LONG_MIC_ONOFF,
    GUI_MSG_KEY_LONG_REC_ONOFF,
    GUI_MSG_KEY_LONG_MIC_VOL_ADD,
    GUI_MSG_KEY_LONG_MIC_VOL_DEC,
    GUI_MSG_KEY_LONG_ECHO_ONOFF,
    GUI_MSG_KEY_LONG_ECHO_SETTING,
    GUI_MSG_KEY_LONG_ACCOMP_ONOFF,
    GUI_MSG_KEY_LONG_ADD_LYRIC_TIME,
    GUI_MSG_KEY_LONG_DEC_LYRIC_TIME,
    GUI_MSG_KEY_LONG_KARAOKE_MUTE,
    GUI_MSG_KEY_LONGEQ,
    GUI_MSG_KEY_LONGTVOUT,
    GUI_MSG_KEY_LONG_SINGER,
    GUI_MSG_KEY_LONG_ALPHABET,
    GUI_MSG_KEY_LONG_DIGITAL,
    GUI_MSG_KEY_LONG_FAVOR,
    GUI_MSG_KEY_LONG_NEWSONG,
    GUI_MSG_KEY_LONG_SEL_LIST,
    GUI_MSG_KEY_LONG_DELETE,
    GUI_MSG_KEY_LONG_CUTSONG,
    GUI_MSG_KEY_LONGAUX,
    GUI_MSG_KEY_LONGPREV,
    GUI_MSG_KEY_LONGNEXT,
    GUI_MSG_KEY_LONGFF,
    GUI_MSG_KEY_LONGRR,
    GUI_MSG_KEY_LONG_NTFS_PAL,
    GUI_MSG_KEY_LONG_SEL_TIME,
    //chengf   add   begin  ============
  //  GUI_MSG_KEY_LONG_USB_TF,
    GUI_MSG_KEY_LONG_TACK,
    GUI_MSG_KEY_LONG_SCAN,
    GUI_MSG_KEY_LONG_AUDIO_RESET,
    GUI_MSG_KEY_LONG_LIST,
    GUI_MSG_KEY_LONG_RECORD,
    GUI_MSG_KEY_LONG_ECHO_VOL,
    GUI_MSG_KEY_TF_USB,
    GUI_MSG_KEY_MAX
} __gui_msg_keyid_t;


typedef struct __GUI_KEY_INFO
{
    __u32  key;                                     /* key value                                                    */
    __u32  presscnt;
} __gui_keyinfo_t;

#ifdef LV_USE_API
typedef enum __GUI_MSG_LVGL
{
	GUI_MSG_OBJ_CREATE,
	GUI_MSG_OBJ_ADD_FLAG,
	GUI_MSG_OBJ_CLEAR_FLAG,
	GUI_MSG_OBJ_ADD_STATE,
	GUI_MSG_OBJ_CLEAR_STATE,

	GUI_MSG_OBJ_SET_POS,
	GUI_MSG_OBJ_SET_X,
	GUI_MSG_OBJ_SET_Y,
	GUI_MSG_OBJ_REFE_SIZE,
	GUI_MSG_OBJ_SET_SIZE,
	GUI_MSG_OBJ_SET_WIDTH,
	GUI_MSG_OBJ_SET_HIGHT,
	GUI_MSG_OBJ_SET_CONTENT_WIDTH,
	GUI_MSG_OBJ_SET_CONTENT_HIGHT,
	GUI_MSG_OBJ_SET_LAYOUT,
	GUI_MSG_OBJ_MARK_LAYOUT_AS_DIRTY,
	GUI_MSG_OBJ_UPDATE_LAYOUT,
	GUI_MSG_OBJ_GET_X,
	GUI_MSG_OBJ_GET_Y,
	GUI_MSG_OBJ_GET_WIDTH,
	GUI_MSG_OBJ_GET_HIGHT,

	GUI_MSG_INDEV_GET_ACT,
	GUI_MSG_INDEV_GET_TYPE,
	GUI_MSG_INDEV_GET_POINT,
	GUI_MSG_INDEV_GET_VECT,
}__gui_msg_lvgl_t;
#endif

typedef enum __GUI_MSG_UIID
{
	//system
	GUI_MSG_UI_SYS_SOURCE = 0x01,
	GUI_MSG_UI_SYS_VOLUME,
	GUI_MSG_UI_SYS_MUTE,
	GUI_MSG_UI_SYS_DIMMER,
	GUI_MSG_UI_SYS_2ZONE,
	GUI_MSG_UI_SYS_2ZONE_SOURCE,
	GUI_MSG_UI_SYS_2ZONE_EXIT,
	GUI_MSG_UI_SYS_XBS,
	GUI_MSG_UI_SYS_SUBW_ONOFF,
	GUI_MSG_UI_SYS_SUBW_VOL,//10
	GUI_MSG_UI_SYS_12VOUT,
	GUI_MSG_UI_SYS_12VOUT_EXIT,
	GUI_MSG_UI_SYS_HOME,
	GUI_MSG_UI_SYS_HOME_EXIT,
	GUI_MSG_UI_SYS_UPGRADE,
	GUI_MSG_UI_SYS_UPGRADE_CANCEL,
	GUI_MSG_UI_SYS_EXIT,				//17
	//tuner
	GUI_MSG_UI_TUNER_PLAYWIN_OPEN, //= 0x10,
	GUI_MSG_UI_TUNER_PLAYWIN_FREQ_UPDATE,
	GUI_MSG_UI_TUNER_PLAYWIN_RDS_UPDATE,//20
	GUI_MSG_UI_TUNER_LISTWIN_OPEN,
	GUI_MSG_UI_TUNER_LISTWIN_UPDATE,
	GUI_MSG_UI_TUNER_PTYLISTWIN_OPEN,
	GUI_MSG_UI_TUNER_PTYLISTWIN_UPDATE,
	GUI_MSG_UI_TUNER_FUNCWIN_OPEN,
	GUI_MSG_UI_TUNER_FUNCWIN_UPDATE,
	GUI_MSG_UI_TUNER_SEEK_START,
	GUI_MSG_UI_TUNER_SEEK_END,
	GUI_MSG_UI_TUNER_MLISTWIN_UPDATE, //29
	//new tuner func for rockford
	GUI_MSG_UI_TUNER_FUNCBANDWIN_UPDATE,//30
	GUI_MSG_UI_TUNER_FUNCPRESETWIN_UPDATE,
	GUI_MSG_UI_TUNER_FUNCPRESETADDWIN_UPDATE,
	GUI_MSG_UI_TUNER_FUNCPRESETRMWIN_UPDATE,
	GUI_MSG_UI_TUNER_FUNCLOCALWIN_UPDATE,
	GUI_MSG_UI_TUNER_FUNC_RDS_WIN_UPDATE,
	GUI_MSG_UI_TUNER_FUNC_RDSONOFF_WIN_UPDATE,
	GUI_MSG_UI_TUNER_FUNC_REGION_WIN_UPDATE,
	//usb
	GUI_MSG_UI_USB_READING,  //38
	GUI_MSG_UI_USB_DEVICE,
	GUI_MSG_UI_USB_TRACK_INFO,//40
	GUI_MSG_UI_USB_PLAY_TIME,
	GUI_MSG_UI_USB_PLAY_STATE,
	GUI_MSG_UI_USB_PLAY_MODE,
	GUI_MSG_UI_USB_PLAY_TRACK,
	GUI_MSG_UI_USB_PLAY_ID3,
	GUI_MSG_UI_USB_PLAY_FOLDER,
	GUI_MSG_UI_USB_FUNC,
	GUI_MSG_UI_USB_FUNC_REPEAT,
	GUI_MSG_UI_USB_FUNC_RANDOM,
	GUI_MSG_UI_USB_FUNC_IPOD_MODE,//50
	GUI_MSG_UI_USB_FUNC_EXIT,
	//usb tid
	GUI_MSG_UI_USB_TID_NOTI,
	GUI_MSG_UI_USB_SWITCH_REVOCER,
	//browser
	GUI_MSG_UI_BROWSER_PAGE,
	GUI_MSG_UI_BROWSER_ITEM,
	GUI_MSG_UI_BROWSER_EXIT,
	//artwork pic
	GUI_MSG_UI_ARTWORK_READY,
	GUI_MSG_UI_ARTWORK_CLEAR,
	//bt
	GUI_MSG_UI_BT_STATUS,
	GUI_MSG_UI_BT_PAIR_CONFIRM_UPDATE,//60
	GUI_MSG_UI_BT_PAIR_CONFIRM_EXIT,
	//smenu
	GUI_MSG_UI_SMENU_MAIN,
	GUI_MSG_UI_SMENU_ZONE2_MAIN,
	GUI_MSG_UI_SMENU_AUDIO,
	GUI_MSG_UI_SMENU_BT,
	GUI_MSG_UI_SMENU_SYSTEM,
	GUI_MSG_UI_SMENU_EQ,
	GUI_MSG_UI_SMENU_ZONE2_EQ,
	GUI_MSG_UI_SMENU_VERSION,
	GUI_MSG_UI_SMENU_FACTORY_VERSION,//70
	GUI_MSG_UI_SMENU_DSP_INFO,
	GUI_MSG_UI_SMENU_EXIT,
	GUI_MSG_UI_SYS_RESET,
	GUI_MSG_UI_SYS_RESET_CANCEL,
	//smenu for rockford
	GUI_MSG_UI_SMENU_BRIGHTNESS,
	GUI_MSG_UI_SMENU_BALANCE,
	GUI_MSG_UI_SMENU_FADE,
	GUI_MSG_UI_SMENU_ZONE,
	//eq level2
	GUI_MSG_UI_SMENU_EQ_7BAND,
	GUI_MSG_UI_SMENU_EQ_CROSSOVER, //80
	GUI_MSG_UI_SMENU_EQ_PUNCH,
	GUI_MSG_UI_SMENU_EQ_PRESET,
	GUI_MSG_UI_SMENU_CROSSOVER_FREQ,
	GUI_MSG_UI_SMENU_CROSSOVER_SLOPE,
	GUI_MSG_UI_SMENU_ZONE_MAIN,
	GUI_MSG_UI_SMENU_ZONE_ZONE1,
	GUI_MSG_UI_SMENU_ZONE_ZONE2,
	GUI_MSG_UI_SMENU_ZONE_SOURCE,
	GUI_MSG_UI_SMENU_RENAME,
	GUI_MSG_UI_SMENU_BEEP,//90
	GUI_MSG_UI_SMENU_BT_PAIRING,
	GUI_MSG_UI_SMENU_BT_DEVICE_LIST,
	GUI_MSG_UI_SMENU_BT_DEVICE_MENU,
	GUI_MSG_UI_SMENU_BT_DISCOVERABLE,
	GUI_MSG_UI_SMENU_AUX,
	GUI_MSG_UI_SMENU_REAR_RCA,
	GUI_MSG_UI_SMENU_VOLTAGE_DET,
	GUI_MSG_UI_SMENU_TUNER_REGION,
	GUI_MSG_UI_SMENU_LANGUAGE,
	GUI_MSG_UI_SMENU_SOFTWARE,//100
	GUI_MSG_UI_SMENU_FACTORY,
	GUI_MSG_UI_SMENU_SOURCE_GAIN,
	GUI_MSG_UI_SMENU_INTERNAL_AMP,
	GUI_MSG_UI_SMENU_THEME_DAY_NIGHT,
	//PMX8 4zone
	GUI_MSG_UI_SMENU_4ZONE_MAIN,
	GUI_MSG_UI_SMENU_4ZONE_AUDIO1,
	GUI_MSG_UI_SMENU_4ZONE_AUDIO2,
	GUI_MSG_UI_SMENU_ZONES_ZONE_TYPE1,
	GUI_MSG_UI_SMENU_ZONES_ZONE_TYPE2,
	GUI_MSG_UI_SMENU_4ZONE_ZONE_SOURCE, //110
	GUI_MSG_UI_SMENU_4ZONE_EQ_CROSSOVER,
	GUI_MSG_UI_4ZONE_MAIN,
	GUI_MSG_UI_4ZONE_SOURCE,
	//can bus menu
	GUI_MSG_UI_SMENU_CANBUS_MAIN,
	GUI_MSG_UI_SMENU_CANBUS_STATUS,
	GUI_MSG_UI_SMENU_CANBUS_VERSION,
	GUI_MSG_UI_SMENU_CANBUS_CODE,
	GUI_MSG_UI_SMENU_CANBUS_INSTANCE,
	GUI_MSG_UI_SMENU_CANBUS_INS_VALUE,
	//remote zone learning
	GUI_MSG_UI_SMENU_ZONE_LEARNING, //120
	//BLE remote
	GUI_MSG_UI_SMENU_BLE_REMOTE_MAIN,
	GUI_MSG_UI_SMENU_BLE_REMOTE_INFO,
	GUI_MSG_UI_SMENU_BLE_REMOTE_SCAN,
	GUI_MSG_UI_SMENU_BLE_LOCAL_INFO,
	GUI_MSG_UI_BLE_CONNECT_STATUS,
	GUI_MSG_UI_SMENU_BLE_RESET,
	//voltage
	GUI_MSG_UI_VOLTAGE_DISPLAY_UPDATE,
	GUI_MSG_UI_VOLTAGE_DISPLAY_EXIT,
	GUI_MSG_UI_LOW_VOLTAGE_ALARM,
	GUI_MSG_UI_LOW_VOLTAGE_ALARM_EXIT, //130
	//speaker error
	GUI_MSG_UI_SPEAKER_ERROR_ALARM,
	GUI_MSG_UI_SPEAKER_ERROR_EXIT,
	//sub vol
	GUI_MSG_UI_SUB_VOL,

	//sxm
	GUI_MSG_UI_SXM_CONNECT_STATE,
	GUI_MSG_UI_SXM_CHANNEL_UPDATE,
	GUI_MSG_UI_SXM_CHANNEL_META_DATA_UPDATE,
	GUI_MSG_UI_SXM_REPLAY_CHANNEL_UPDATE,
	GUI_MSG_UI_SXM_REPLAY_UPDATE,
	GUI_MSG_UI_SXM_FAST_SEEK,
	GUI_MSG_UI_SXM_SUBSCRIPTION_UPDATE, //140
	GUI_MSG_UI_SXM_CHANNEL_UNLOCK,
	GUI_MSG_UI_SXM_FUNC_MAINWIN_UPDATE,
	GUI_MSG_UI_SXM_JUMP_TO_LIVE,
	GUI_MSG_UI_SXM_FUNC_BANDWIN_UPDATE,
	GUI_MSG_UI_SXM_FUNC_PRESETWIN_UPDATE,
	GUI_MSG_UI_SXM_FUNC_PRESETMANAGE_UPDATE,
	GUI_MSG_UI_SXM_FUNC_PRESETADDWIN_UPDATE,
	GUI_MSG_UI_SXM_FUNC_PRESETRMWIN_UPDATE,
	GUI_MSG_UI_SXM_FUNC_BROWSER_CHANNEL,
	GUI_MSG_UI_SXM_FUNC_BROWSER_ARTIST, //150
	GUI_MSG_UI_SXM_FUNC_BROWSER_SONG,
	GUI_MSG_UI_SXM_FUNC_BROWSER_INFO,
	GUI_MSG_UI_SXM_FUNC_BROWSER_CHANNEL_CAT,
	GUI_MSG_UI_SXM_FUNC_BROWSER_CAT,
	GUI_MSG_UI_SXM_FUNC_BROWSER_CAT_CHANNEL,
	GUI_MSG_UI_SXM_FUNC_BROWSER_CAT_ARTIST,
	GUI_MSG_UI_SXM_FUNC_BROWSER_CAT_SONG,
	GUI_MSG_UI_SXM_FUNC_BROWSER_CAT_INFO,
	GUI_MSG_UI_SXM_FUNC_TUNING_CHANNEL,
	GUI_MSG_UI_SXM_FUNC_PARENTAL_CONTROL, //160
	GUI_MSG_UI_SXM_FUNC_CHANNEL_LOCK_UPDATE,
	GUI_MSG_UI_SXM_FUNC_MY_ACCOUNT,
	GUI_MSG_UI_SXM_FUNC_FACTORY_DEFAULT,
	GUI_MSG_UI_SXM_FUNC_EXIT,
	//sxm factory menu
	GUI_MSG_UI_SXM_FACTORY_MENU_UPDATE,
	GUI_MSG_UI_SXM_FACTORY_MENU_EXIT,
	GUI_MSG_UI_SXM_FACTORY_DIAGNOSTIC_UPDATE,
	GUI_MSG_UI_SXM_FACTORY_DIAGNOSTIC_EXIT,
	GUI_MSG_UI_SXM_DELETE_CGS_DATA,
	GUI_MSG_UI_SXM_DELETE_ALBUMART_DATA, //170
	GUI_MSG_UI_SXM_ALBUMART_1_UPDATE,
	GUI_MSG_UI_SXM_ALBUMART_2_UPDATE,
	GUI_MSG_UI_SXM_CHAN_LIST_WAIT_DONE,

	//pandora
	GUI_MSG_UI_PANDORA_STATUS_UPDATE,
	GUI_MSG_UI_PANDORA_PLAYTIME_UPDATE,
	GUI_MSG_UI_PANDORA_SONGINFO_UPDATE,
	GUI_MSG_UI_PANDORA_FUNC_MAIN_UPDATE,
	GUI_MSG_UI_PANDORA_FUNC_BROWSER,
	GUI_MSG_UI_PANDORA_FUNC_BOOKMARK,
	GUI_MSG_UI_PANDORA_FUNC_STATION_MANAGE, //180
	GUI_MSG_UI_PANDORA_FUNC_GENRE,
	GUI_MSG_UI_PANDORA_FUNC_EXIT,

 	GUI_MSG_UI_DAB_SLS,
	GUI_MSG_UI_DAB_DLS,
	GUI_MSG_UI_DAB_SCAN_COMPLETE,
	GUI_MSG_UI_DAB_PLAY_COMPLETE,
	GUI_MSG_UI_DAB_SEEK_STAR,
	GUI_MSG_UI_DAB_SEEK_COMPLETE,
	GUI_MSG_UI_DAB_RESCAN,
	GUI_MSG_UI_DAB_NORMAL_PLAY, //190
	GUI_MSG_UI_DAB_NO_SIGNAL,
	GUI_MSG_UI_DAB_SCAN_PROG,
	GUI_MSG_UI_DAB_LOADING,
	GUI_MSG_UI_DAB_CHANNEL,
	GUI_MSG_UI_DAB_UPDATE_ENSEMBLE_NAME,
	GUI_MSG_UI_DAB_SELECT_COMPLETE,
	GUI_MSG_UI_DAB_UPDATE_SERVICE_LIST,
	GUI_MSG_UI_DAB_PTY_NO_FOUND,
	GUI_MSG_UI_DAB_SWITCH_BAND_SCAN,
	GUI_MSG_UI_DAB_SWITCH_BAND_DONE, //200
	GUI_MSG_UI_DAB_ANNOUNCEMENT_START,
	GUI_MSG_UI_DAB_ANNOUNCEMENT_STOP,
	GUI_MSG_UI_DAB_RELATED_SERVICE_POPUP_TIPS,
	GUI_MSG_UI_DAB_RELATED_SERVICE_CANCEL_TIPS,

	GUI_MSG_UI_DAB_RESET1,
	GUI_MSG_UI_DAB_RESET2,
	GUI_MSG_UI_DAB_RESET3,

	GUI_MSG_BACK_CAR_DET,
	GUI_MSG_BACK_CAR_INTO_APP,

	//BT
	GUI_MSG_UI_BT_TID_STATUS, //210
	GUI_MSG_UI_BT_PLAY_STATUS,
	GUI_MSG_UI_BT_USB_IPOD_BROWSE_READY,

	        	//life test
	GUI_MSG_UI_LIFE_TEST_PWR_ONOFF,

	//day night mode
	GUI_MSG_UI_SYS_DAY_NIGHT_SWITCH,

	//sys config setting update from USB
	GUI_MSG_UI_SYS_CONFIG_SETTING_UPDATE,

	//for menu list ,new menu settting
	GUI_MSG_UI_SMENU_LIST_NORMAL1,
	GUI_MSG_UI_SMENU_LIST_NORMAL2,
	GUI_MSG_UI_SMENU_LIST_POPUP,
	GUI_MSG_UI_SMENU_LIST_RENAME,
	//for splash screen
	GUI_MSG_UI_SYS_SAVE_SPLASH_MENU, //220
	GUI_MSG_UI_SYS_SAVE_SPLASH_EXIT,

	//for NO Usb Device
	GUI_MSG_UI_USB_NO_DEVICE,
	GUI_MSG_UI_USB_DEVICE_ERROR,

	//for input Key part
	GUI_MSG_UI_KEY_ENTER,
	GUI_MSG_UI_KEY_PREV,
	GUI_MSG_UI_KEY_NEXT,
	GUI_MSG_UI_KEY_FB,
	GUI_MSG_UI_KEY_FF,
	GUI_MSG_UI_KEY_FASTEND,
	GUI_MSG_UI_KEY_PLAY_PAUSE, //230
	GUI_MSG_UI_KEY_RETURN,
	GUI_MSG_UI_KEY_UP,
	GUI_MSG_UI_KEY_DOWN,

	//for HMR100 Settings
	GUI_MSG_UI_SETTINGS,
	//bt
	GUI_MSG_UI_SETTINGS_BT,
	GUI_MSG_UI_SETTINGS_BT_DEVICE_SELECT,
	GUI_MSG_UI_SETTINGS_BT_DEVICE_REMOVE,
	//display
	GUI_MSG_UI_SETTINGS_DISPLAY,
	GUI_MSG_UI_SETTINGS_DISPLAY_BRIGHTNESS,
	GUI_MSG_UI_SETTINGS_DISPLAY_STARTUP_LOGO, //240

	//audio
	GUI_MSG_UI_SETTINGS_AUDIO,
	GUI_MSG_UI_SETTINGS_AUD_GENERAL,
	GUI_MSG_UI_SETTINGS_AUD_GENERAL_PVS,
	GUI_MSG_UI_SETTINGS_AUD_GENERAL_SvsV,
	GUI_MSG_UI_SETTINGS_AUD_GENERAL_SvsV_MINMAXSPEED,
	GUI_MSG_UI_SETTINGS_AUD_GENERAL_SvsV_VOLINCREASE,
	GUI_MSG_UI_SETTINGS_AUD_GENERAL_SvsV_SPEEDUNIT,
	GUI_MSG_UI_SETTINGS_AUD_ZONES,
	GUI_MSG_UI_SETTINGS_AUD_ZONES_ZONE,
	GUI_MSG_UI_SETTINGS_AUD_ZONES_ZONE_EQ, //250
	GUI_MSG_UI_SETTINGS_AUD_ZONES_ZONE_BAL_FAD,
	GUI_MSG_UI_SETTINGS_AUD_ZONES_ZONE_XOVER_HPF,
	GUI_MSG_UI_SETTINGS_AUD_ZONES_ZONE_SUBW,
	GUI_MSG_UI_SETTINGS_AUD_ZONES_ZONE_SUBW_LEVEL,
	GUI_MSG_UI_SETTINGS_AUD_ZONES_ZONE_SUBW_LPF,
	GUI_MSG_UI_SETTINGS_AUD_ZONES_ZONE_SUBW_POLARITY,
	GUI_MSG_UI_SETTINGS_AUD_ZONES_ZONE_MAX_VOLUME_LTD,
	GUI_MSG_UI_SETTINGS_AUD_ZONES_ZONE_PWRON_VOLUME_LTD,
	GUI_MSG_UI_SETTINGS_AUD_ZONES_ZONE_LEVE_CTL_MODE,
	GUI_MSG_UI_SETTINGS_AUD_ZONES_GROUPS, //260
	GUI_MSG_UI_SETTINGS_AUD_ZONES_GROUPS_GROUP,
	GUI_MSG_UI_SETTINGS_AUD_ZONES_GROUPS_GROUP_SETUP,
	GUI_MSG_UI_SETTINGS_AUD_ZONES_FAVORITE_ZONE,

	//source
	GUI_MSG_UI_SETTINGS_SOURCES,

	//power management
	GUI_MSG_UI_SETTINGS_PWR_MANAGE,

	//system
	GUI_MSG_UI_SETTINGS_SYSTEM,
	GUI_MSG_UI_SETTINGS_SYSTEM_INFO,
	GUI_MSG_UI_SETTINGS_SYSTEM_INFO_MAIN,
	GUI_MSG_UI_SETTINGS_SYSTEM_INFO_HU,
	GUI_MSG_UI_SETTINGS_SYSTEM_INFO_RMT, //270
	GUI_MSG_UI_SETTINGS_SYSTEM_HEADUNIT_UPDATE,
	GUI_MSG_UI_SETTINGS_SYSTEM_REMOTE_UPDATE,
	GUI_MSG_UI_SETTINGS_SYSTEM_FACTORY_DEFAULT,

	//volume adjust
	GUI_MSG_UI_VOLUME_ADJUST,
	//remote register
	GUI_MSG_UI_REMOTE_REGISTER,

	//remote update from head unit
	GUI_MSG_UI_REMOTE_UPDATE_TRANSFER,
	GUI_MSG_UI_REMOTE_UPDATE_READY,
	GUI_MSG_UI_REMOTE_UPDATE_UPDATING,
	GUI_MSG_UI_REMOTE_UPDATE_SUCCESS,
	GUI_MSG_UI_REMOTE_UPDATE_FAIL, //280
	GUI_MSG_UI_REMOTE_UPDATE_DIALOG_CONFIRM,
	GUI_MSG_UI_REMOTE_UPDATE_DIALOG_NOUSB,
	GUI_MSG_UI_REMOTE_UPDATE_DIALOG_NOUPDATEFILE,

	//power amplifier debug
	GUI_MSG_UI_DEBUG_INFO,
	GUI_MSG_UI_DEBUG_EXIT,	//285

	GUI_MSG_UI_

}__gui_msg_uiid_t;

typedef enum __GUI_MSG_UI_NEW_ID
{
// new protocol v2 defined message start
	GUI_MSG_UI_NEW_START	= GUI_MSG_UI_ + 1,
//party mode part
	GUI_MSG_UI_PARTY_UPDATE,
	GUI_MSG_UI_PARTY_SETTINGS_UPDATE,
	GUI_MSG_UI_PARTY_ROLE_UPDATE,
	GUI_MSG_UI_PARTY_KNOWN_LIST_UPDATE,
	GUI_MSG_UI_PARTY_SCAN_LIST_UPDATE,
	GUI_MSG_UI_PARTY_RECEIVER_CONNECT_STATE,
	GUI_MSG_UI_PARTY_BROADCAST_SOURCE,

//tuner part
	//base msg
	GUI_MSG_UI_NEW_TUNER_UPDATE, // tuner unified update
	GUI_MSG_UI_NEW_TUNER_FREQUENCY,
	GUI_MSG_UI_NEW_TUNER_PRESET,
	GUI_MSG_UI_NEW_TUNER_PRESET_LIST,
	//status/state msg
	GUI_MSG_UI_NEW_TUNER_STATE, //playing,seek ...
	GUI_MSG_UI_NEW_TUNER_STEREO, //st or not
	//switch
	GUI_MSG_UI_NEW_TUNER_LOCAL, // local or dist
	//RDS
	GUI_MSG_UI_NEW_TUNER_RDS_SWITCH, //rds on/off
	GUI_MSG_UI_NEW_TUNER_RDS_AF_SWITCH, //rds af on/off
	GUI_MSG_UI_NEW_TUNER_RDS_STATUS, // ps or pty
	GUI_MSG_UI_NEW_TUNER_RDS_AF_STATUS, //af status
	GUI_MSG_UI_NEW_TUNER_RDS_RT_TEXT, // rt text status and text
	GUI_MSG_UI_NEW_TUNER_RDS_TA_STATUS, // rds ta status
	GUI_MSG_UI_NEW_TUNER_SCAN_MODE, // scan mode
	GUI_MSG_UI_NEW_TUNER_TUNING_STEP, // tuning step

//USB part
	GUI_MSG_UI_NEW_USB_DISK_UPDATE, //USB DISK unified update
	GUI_MSG_UI_NEW_USB_IPOD_UPDATE, //USB IPOD unified update
	
//Audio/Volume part
	GUI_MSG_UI_NEW_AUDIO_UPDATE, //Audio system update
	GUI_MSG_UI_NEW_VOLUME_UPDATE, //Volume level update
	
//Settings part
	GUI_MSG_UI_NEW_SETTINGS_UPDATE, //Settings configuration update
}__gui_msg_ui_newid_t;

typedef enum __GUI_MY_MSGID_HOME
{
    GUI_MSG_HOME_REFRESH_INIT = GUI_MSG_ + 1,
    GUI_MSG_HOME_REFRESH_UNINIT,
    GUI_MSG_HOME_REFRESH_MSG,
    GUI_MSG_HOME_REFRESH_PART,
    GUI_MSG_HOME_OPN_LAYER,

    GUI_MSG_HOME_

} __gui_my_msgid_home_t;

typedef enum __GUI_MY_MSGID_FM
{
    GUI_MSG_FM_REFRESH_INIT = GUI_MSG_HOME_ + 1,
    GUI_MSG_FM_REFRESH_UNINIT,
    GUI_MSG_FM_REFRESH_HIDDEN,
    GUI_MSG_FM_REFRESH_TIMER,
    GUI_MSG_FM_REFRESH_MSG,
    GUI_MSG_FM_REFRESH_PART,
    GUI_MSG_FM_REFRESH_WIN,
    GUI_MSG_FM_REFRESH_DIALOG,
    GUI_MSG_FM_OPN_LAYER,
    GUI_MSG_FM_OPN_EVENT,
    GUI_MSG_FM_PRVE,
    GUI_MSG_FM_LONG_PRVE,
    GUI_MSG_FM_NEXT,
    GUI_MSG_FM_LONG_NEXT,
    GUI_MSG_FM_FM_BAND,
    GUI_MSG_FM_AM_BAND,
    GUI_MSG_FM_PRESET,
    GUI_MSG_FM_SAVE_PRESET,
    GUI_MSG_FM_STOP_SEEK,
    GUI_MSG_FM_CTRL_STOP_SEEK,
    GUI_MSG_FM_FUNC_AUTO_STORE,
    GUI_MSG_FM_FUNC_LOCAL_SEEK,
    GUI_MSG_FM_FUNC_RDS,
	GUI_MSG_FM_FUNC_AF,
	GUI_MSG_FM_FUNC_TA,
	GUI_MSG_FM_FUNC_CT,
	GUI_MSG_FM_FUNC_PTY,
	GUI_MSG_FM_FUNC_PTY_SEEK,
	GUI_MSG_FM_MUTE,

    GUI_MSG_FM_

} __gui_my_msgid_fm_t;

typedef enum __GUI_MY_MSGID_DAB
{
    GUI_MSG_DAB_REFRESH_INIT = GUI_MSG_FM_ + 1,
    GUI_MSG_DAB_REFRESH_UNINIT,
    GUI_MSG_DAB_REFRESH_HIDDEN,
	GUI_MSG_DAB_REFRESH_PART,
    GUI_MSG_DAB_REFRESH_WIN,
	GUI_MSG_DAB_REFRESH_DIALOG,
	GUI_MSG_DAB_REFRESH_MSG,
    GUI_MSG_DAB_OPN_LAYER,
    GUI_MSG_DAB_MENU_ARROW,
    GUI_MSG_DAB_FUNC,
    GUI_MSG_DAB_LIST,
    GUI_MSG_DAB_PTY,
    GUI_MSG_DAB_ANNOUNCEMENT,
    GUI_MSG_DAB_PRVE,
    GUI_MSG_DAB_LONG_PRVE,
    GUI_MSG_DAB_NEXT,
    GUI_MSG_DAB_LONG_NEXT,
    GUI_MSG_DAB_BAND,
	GUI_MSG_DAB_PRESET,
    GUI_MSG_DAB_LONG_PRESET,
    GUI_MSG_DAB_UPDATE_SLS_MOT,
    GUI_MSG_DAB_SEND_CMD,
	GUI_MSG_DAB_MUTE,

    GUI_MSG_DAB_

} __gui_my_msgid_dab_t;

typedef enum __GUI_MY_MSGID_MUSIC
{
    GUI_MSG_REFRESH_INIT = GUI_MSG_DAB_ + 1,
    GUI_MSG_REFRESH_UNINIT,
	GUI_MSG_REFRESH_HIDDEN,
	GUI_MSG_REFRESH_WIN,
    GUI_MSG_REFRESH_LIST,
	GUI_MSG_REFRESH_PART,
	GUI_MSG_REFRESH_MSG,
	GUI_MSG_OPN_TIMER,
    GUI_MSG_OPN_LAYER,
	GUI_MSG_OPN_APP_SLEEP,
	GUI_MSG_OPN_APP_ON,
	GUI_MSG_OPN_GET_AUDIO_INFO,
	GUI_MSG_OPN_PLAY_INDEX,
	GUI_MSG_OPN_PLAY_PAUSE,
	GUI_MSG_OPN_NEXT_PREV,
	GUI_MSG_OPN_FF_RR_PRESS,
	GUI_MSG_OPN_FF_RR_RELEASE,
	GUI_MSG_OPN_PROGRESS,
	GUI_MSG_OPN_RANDOM,
	GUI_MSG_OPN_REPEAT,
	GUI_MSG_OPN_LOGO,
	GUI_MSG_OPN_POWER_ON,
	GUI_MSG_OPN_POWER_OFF,
	GUI_MSG_MUSIC_MUTE,
	GUI_MSG_OPN_MUSIC,

    GUI_MSG_MUSIC_

} __gui_my_msgid_music_t;

typedef enum __GUI_MY_MSGID_MOVIE
{
    GUI_MSG_MOVIE_REFRESH_INIT = GUI_MSG_MUSIC_ + 1,
    GUI_MSG_MOVIE_REFRESH_UNINIT,
    GUI_MSG_MOVIE_REFRESH_TIMER,
    GUI_MSG_MOVIE_REFRESH_HIDDEN,
    GUI_MSG_MOVIE_REFRESH_DIALOG,
    GUI_MSG_MOVIE_REFRESH_WIN,
    GUI_MSG_MOVIE_REFRESH_PART,
    GUI_MSG_MOVIE_REFRESH_MSG,
	GUI_MSG_MOVIE_OPN_TIMER,
	GUI_MSG_MOVIE_OPN_INIT,
	GUI_MSG_MOVIE_OPN_UNINIT,
	GUI_MSG_MOVIE_OPN_LAYER,
	GUI_MSG_MOVIE_OPN_PLAY_INDEX,
	GUI_MSG_MOVIE_OPN_PLAY_PAUSE,
	GUI_MSG_MOVIE_OPN_NEXT_PREV,
	GUI_MSG_MOVIE_OPN_FF_RR_PRESS,
	GUI_MSG_MOVIE_OPN_FF_RR_RELEASE,
	GUI_MSG_MOVIE_OPN_PROGRESS,
	GUI_MSG_MOVIE_OPN_APP_SLEEP,
	GUI_MSG_MOVIE_OPN_APP_ON,
	GUI_MSG_MOVIE_OPN_ZOOM,
	GUI_MSG_MOVIE_OPN_ADJUST,
	GUI_MSG_MOVIE_OPN_ADJUST_RESET,
	GUI_MSG_MOVIE_OPN_PARKING,
	GUI_MSG_MOVIE_OPN_POWER_ON,
	GUI_MSG_MOVIE_OPN_POWER_OFF,
#ifdef VIDEO_DISPLAY_DIFF1
	GUI_MSG_MOVIE_OPN_VIDEO_PLAY,
	GUI_MSG_MOVIE_OPN_VIDEO_PAUSE,
#endif
#if LV_BG_VIDEO_IMG
	GUI_MSG_MOVIE_VIDEO_LAYER_SET,
#endif
	GUI_MSG_MOVIE_SAVE_LAST_PLAYING_FILE,
#if SAVE_LAST_VIDEO_CONFIG
	GUI_MSG_MOVIE_SAVE_VIDEO_CONFIG,
	GUI_MSG_MOVIE_SET_VIDEO_CONFIG,
#endif
	GUI_MSG_MOVIE_MUTE,

    GUI_MSG_MOVIE_

} __gui_my_msgid_movie_t;

typedef enum __GUI_MY_MSGID_BT
{
    GUI_MSG_BT_REFRESH_INIT = GUI_MSG_MOVIE_ + 1,
    GUI_MSG_BT_REFRESH_UNINIT,
    GUI_MSG_BT_REFRESH_HIDDEN,
    GUI_MSG_BT_REFRESH_DAILOG,
	GUI_MSG_BT_REFRESH_WIN,
	GUI_MSG_BT_REFRESH_PART,
    GUI_MSG_BT_REFRESH_LIST,
	GUI_MSG_BT_REFRESH_MSG,
	GUI_MSG_BT_OPN_TIMER,
	GUI_MSG_BT_OPN_LAYER,
	GUI_MSG_BT_OPN_LOGO,
	GUI_MSG_BT_OPN_BG,
	GUI_MSG_BT_OPN_PLAY_INDEX,
	GUI_MSG_BT_OPN_PLAY_PAUSE,
	GUI_MSG_BT_OPN_NEXT_PREV,
	GUI_MSG_BT_OPN_FF_RR_PRESS,
	GUI_MSG_BT_OPN_FF_RR_RELEASE,
	GUI_MSG_BT_OPN_PROGRESS,
	GUI_MSG_BT_OPN_RANDOM,
	GUI_MSG_BT_OPN_REPEAT,
	GUI_MSG_BT_MUTE,

    GUI_MSG_BT_

} __gui_my_msgid_bt_t;

typedef enum __GUI_MY_MSGID_IPOD
{
    GUI_MSG_IPOD_REFRESH_INIT = GUI_MSG_BT_ + 1,
    GUI_MSG_IPOD_REFRESH_UNINIT,
    GUI_MSG_IPOD_REFRESH_HIDDEN,
	GUI_MSG_IPOD_REFRESH_WIN,
	GUI_MSG_IPOD_REFRESH_PART,
    GUI_MSG_IPOD_REFRESH_LIST,
    GUI_MSG_IPOD_REFRESH_MSG,
	GUI_MSG_IPOD_OPN_TIMER,
	GUI_MSG_IPOD_OPN_LAYER,
	GUI_MSG_IPOD_OPN_PLAY_INDEX,
	GUI_MSG_IPOD_OPN_PLAY_PAUSE,
	GUI_MSG_IPOD_OPN_NEXT_PREV,
	GUI_MSG_IPOD_OPN_FF_RR_PRESS,
	GUI_MSG_IPOD_OPN_FF_RR_RELEASE,
	GUI_MSG_IPOD_OPN_PROGRESS,
	GUI_MSG_IPOD_OPN_RANDOM,
	GUI_MSG_IPOD_OPN_REPEAT,
	GUI_MSG_IPOD_OPN_LOGO,
	GUI_MSG_IPOD_MUTE,

    GUI_MSG_IPOD_

} __gui_my_msgid_ipod_t;

typedef enum __GUI_MY_MSGID_AV_IN
{
    GUI_MSG_AV_IN_REFRESH_INIT = GUI_MSG_IPOD_ + 1,
    GUI_MSG_AV_IN_REFRESH_UNINIT,
    GUI_MSG_AV_IN_REFRESH_HIDDEN,
	GUI_MSG_AV_IN_REFRESH_PART,
    GUI_MSG_AV_IN_REFRESH_MSG,
    GUI_MSG_AV_IN_REFRESH_TIMER,
    GUI_MSG_AV_IN_REFRESH_DIALOG,
	GUI_MSG_AV_IN_OPN_TIMER,
	GUI_MSG_AV_IN_OPN_INIT,
	GUI_MSG_AV_IN_OPN_UNINIT,
	GUI_MSG_AV_IN_OPN_ADJUST,
	GUI_MSG_AV_IN_OPN_ADJUST_RESET,
#if LV_BG_VIDEO_IMG
	GUI_MSG_AV_IN_VIDEO_LAYER_SET,
#endif
	GUI_MSG_AV_IN_MUTE,


    GUI_MSG_AV_IN_

} __gui_my_msgid_av_in_t;

typedef enum __GUI_MY_MSGID_DVR
{
    GUI_MSG_DVR_REFRESH_INIT = GUI_MSG_AV_IN_ + 1,
    GUI_MSG_DVR_REFRESH_UNINIT,
	GUI_MSG_DVR_REFRESH_PART,
    GUI_MSG_DVR_REFRESH_TIMER,
    GUI_MSG_DVR_REFRESH_WIN,
	GUI_MSG_DVR_REFRESH_DIALOG,
	GUI_MSG_DVR_REFRESH_MSG,
	GUI_MSG_DVR_OPN_TIMER,
	GUI_MSG_DVR_OPN_INIT,
	GUI_MSG_DVR_OPN_UNINIT,
#if LV_BG_VIDEO_IMG
	GUI_MSG_DVR_VIDEO_LAYER_SET,
#endif

    GUI_MSG_DVR_

} __gui_my_msgid_dvr_t;

typedef enum __GUI_MY_MSGID_SMENU
{
    GUI_MSG_SMENU_REFRESH_INIT = GUI_MSG_DVR_ + 1,
    GUI_MSG_SMENU_REFRESH_UNINIT,
    GUI_MSG_SMENU_REFRESH_SWITCH_WIN,
    GUI_MSG_SMENU_REFRESH_TITLE,
    GUI_MSG_SMENU_REFRESH_PART,
    GUI_MSG_SMENU_REFRESH_DIALOG,
	GUI_MSG_SMENU_REFRESH_CMD,
	GUI_MSG_SMENU_OPN_TIMER,
	GUI_MSG_SMENU_OPN_SWITCH_WIN,
	GUI_MSG_SMENU_OPN_FACTORY_MODE_RESET,
	GUI_MSG_SMENU_OPN_CHECK_MIDEO,
	GUI_MSG_SMENU_OPN_CHECK_LOGO,
	GUI_MSG_SMENU_OPN_SPLASH_INIT,
	GUI_MSG_SMENU_OPN_SPLASH_UNINIT,
	GUI_MSG_SMENU_OPN_SPLASH_LOGO,
	GUI_MSG_SMENU_OPN_SPLASH_MINIATURE_ITEM,
	GUI_MSG_SMENU_OPN_SPLASH_MINIATURE,
	GUI_MSG_SMENU_OPN_AHD_INIT,
	GUI_MSG_SMENU_OPN_AHD_UNINIT,
#ifdef USE_SENSOR_4_CHANNEL
	GUI_MSG_SMENU_OPN_AHD_SWITCH,
#endif
#ifdef TEST_REG_PRINT
	GUI_MSG_SMENU_OPN_AHD_PEG_PAINT,
	GUI_MSG_SMENU_OPN_AHD_SET_CLK,
#endif
	GUI_MSG_SMENU_OPN_MIRROR_SWITCH,
	GUI_MSG_SMENU_OPN_MIRROR_WIFI_CONNECT,
#if 1//def WIFI_LIST_DEMOsong20240911
	GUI_MSG_SMENU_OPN_WIRLESS_WIFI_DISCONNECT,
	GUI_MSG_SMENU_OPN_WIRLESS_SET_WIFI_SCAN,
	GUI_MSG_SMENU_OPN_WIRLESS_SET_WIFI_CONNECT,
	GUI_MSG_SMENU_OPN_WIRLESS_SET_WIFI_PAIR_INFO,
#endif
	GUI_MSG_SMENU_OPN_SET_WALLPAPER_IMG,
	GUI_MSG_SMENU_OPN_OPEN_SWC,
	GUI_MSG_SMENU_OPN_QRCODE,
#ifdef UPDATE_BY_OTA
	GUI_MSG_SMENU_OPN_LISTBAR_REFRESH_INIT,
	GUI_MSG_SMENU_OPN_WLAN_RECONNECT_INIT,
	GUI_MSG_SMENU_OPN_WIFI_ADD_SECURITY_INIT,
#endif
#ifdef TUNER_FAST_CUT_PARA
	GUI_MSG_SMENU_OPN_TUNER,
#endif

    GUI_MSGSMENU_

} __gui_my_msgid_smenu_t;

typedef enum __GUI_MY_MSGID_BACKCAR
{
    GUI_MSG_BACKCAR_REFRESH_INIT = GUI_MSGSMENU_ + 1,
    GUI_MSG_BACKCAR_REFRESH_UNINIT,
    GUI_MSG_BACKCAR_REFRESH_TIMER,
    GUI_MSG_BACKCAR_REFRESH_PART,
	GUI_MSG_BACKCAR_OPN_TIMER,
	GUI_MSG_BACKCAR_OPN_INIT,
	GUI_MSG_BACKCAR_OPN_UNINIT,
	GUI_MSG_BACKCAR_VIDEO_LAYER_SET,
	GUI_MSG_BACKCAR_VIDEO_SWITCH,

    GUI_MSG_BACKCAR_

} __gui_my_msgid_backcar_t;

typedef enum __GUI_MY_MSGID_DIALOG
{
    GUI_MSG_DIALOG_REFRESH_INIT = GUI_MSG_BACKCAR_ + 1,
    GUI_MSG_DIALOG_REFRESH_UNINIT,
    GUI_MSG_DIALOG_REFRESH_MSG,
	GUI_MSG_DIALOG_REFRESH_PART,

	GUI_MSG_DIALOG_OPN_TIMER,

    GUI_MSG_DIALOG_

} __gui_my_msgid_dialog_t;

typedef enum __GUI_MY_MSGID_VOL
{
    GUI_MSG_VOL_REFRESH_INIT = GUI_MSG_DIALOG_ + 1,
    GUI_MSG_VOL_REFRESH_UNINIT,
	GUI_MSG_VOL_REFRESH_MSG,
	GUI_MSG_VOL_REFRESH_TIMER,
	GUI_MSG_VOL_REFRESH_PART,
	GUI_MSG_VOL_OPN_TIMER,
	GUI_MSG_VOL_OPN_KEY,
	GUI_MSG_VOL_OPN_BTPHONE_VOL_LEFT,
	GUI_MSG_VOL_OPN_BTPHONE_VOL_RIGHT,
	GUI_MSG_VOL_OPN_BTPHONE_VOL_LONGRIGHT,
	GUI_MSG_VOL_OPN_VOL_ABSOLUTE,
	GUI_MSG_VOL_OPN_VOL_LEFT,
	GUI_MSG_VOL_OPN_VOL_RIGHT,
	GUI_MSG_VOL_OPN_VOL_LONGRIGHT,
	GUI_MSG_VOL_OPN_VOL_MUTE,
	GUI_MSG_VOL_OPN_VOL_NUM,

    GUI_MSG_VOL_

} __gui_my_msgid_vol_t;

typedef enum __GUI_MY_MSGID_IPHONE
{
	GUI_MSG_IPHONE_OPN_INIT = GUI_MSG_VOL_ + 1,
	GUI_MSG_IPHONE_OPN_UNINIT,
	GUI_MSG_IPHONE_OPN_TIMER,
	GUI_MSG_IPHONE_OPN_LAYER,
	GUI_MSG_IPHONE_OPN_TOUCH_POS,
	GUI_MSG_IPHONE_OPN_TOUCH_KEY,
	GUI_MSG_IPHONE_OPN_MIRROR_TOUCH_POS,
	GUI_MSG_IPHONE_OPN_SHOW,
	GUI_MSG_IPHONE_OPN_SHOW_ONLY,
	GUI_MSG_IPHONE_OPN_DIFF,
	GUI_MSG_IPHONE_OPN_VIDEO,
	GUI_MSG_IPHONE_OPN_REV_NEED,
#ifdef CONFIG_DRIVERS_USB_GADGET_QVH
	GUI_MSG_IPHONE_OPN_LAUNCH,
	GUI_MSG_IPHONE_OPN_TOUCH_GUIDANCE,
#endif
	GUI_MSG_IPHONE_VIDEO_LAYER_SET,
	GUI_MSG_IPHONE_REFRESH_INIT,
	GUI_MSG_IPHONE_REFRESH_UNINIT,
	GUI_MSG_IPHONE_REFRESH_HIDDEN,
	GUI_MSG_IPHONE_REFRESH_DIALOG,
	GUI_MSG_IPHONE_REFRESH_SWITCH_WIN,
	GUI_MSG_IPHONE_REFRESH_MSG,
	GUI_MSG_IPHONE_REFRESH_MENU_ICON,
	GUI_MSG_IPHONE_REFRESH_MENU_FUNC_ICON,
	GUI_MSG_IPHONE_REFRESH_SETUP_ICON,
    GUI_MSG_IPHONE_

} __gui_my_msgid_iphone_t;

typedef enum __GUI_MY_MSGID_ADB
{
	GUI_MSG_ADB_OPN_TIMER = GUI_MSG_IPHONE_ + 1,
	GUI_MSG_ADB_OPN_LAYER,
	GUI_MSG_ADB_OPN_SHOW,
	GUI_MSG_ADB_OPN_DESHOW,
	GUI_MSG_ADB_OPN_TOUCH_POS,
	GUI_MSG_ADB_OPN_TOUCH_KEY,
	GUI_MSG_ADB_OPN_MIRROR_TOUCH_POS,
	GUI_MSG_ADB_REFRESH_INIT,
	GUI_MSG_ADB_REFRESH_UNINIT,
	GUI_MSG_ADB_REFRESH_HIDDEN,
	GUI_MSG_ADB_REFRESH_MSG,
	GUI_MSG_ADB_REFRESH_SWITCH_WIN,
	GUI_MSG_ADB_REFRESH_DIALOG,
	GUI_MSG_ADB_REFRESH_MENU_ICON,
	GUI_MSG_ADB_REFRESH_MENU_FUNC_ICON,
	GUI_MSG_ADB_REFRESH_SETUP_ICON,
	GUI_MSG_ADB_REFRESH_ASSISTIVE_TOUCH_FUNC_ICON,
#if LV_BG_VIDEO_IMG
	GUI_MSG_ADB_VIDEO_LAYER_SET,
#endif
    GUI_MSG_ADB_

} __gui_my_msgid_adb_t;

typedef enum __GUI_MY_MSGID_WIRLESS
{
	GUI_MSG_WIRLESS_REFRESH_INIT = GUI_MSG_ADB_ + 1,
	GUI_MSG_WIRLESS_REFRESH_UNINIT,
	GUI_MSG_WIRLESS_REFRESH_TIMER,
	GUI_MSG_WIRLESS_REFRESH_DIALOG,
	GUI_MSG_WIRLESS_REFRESH_HIDDEN,
	GUI_MSG_WIRLESS_REFRESH_SWITCH_WIN,
	GUI_MSG_WIRLESS_REFRESH_MSG,
	GUI_MSG_WIRLESS_OPN_INIT,
	GUI_MSG_WIRLESS_OPN_UNINIT,
	GUI_MSG_WIRLESS_OPN_LAYER,
	GUI_MSG_WIRLESS_IPHONE_MODEL_INIT,
	GUI_MSG_WIRLESS_ANDROID_MODEL_INIT,
	GUI_MSG_WIRLESS_DEV,
	GUI_MSG_WIRLESS_STREAM,
	GUI_MSG_WIRLESS_SCRSHOT,
	GUI_MSG_WIRLESS_DIFF,
	GUI_MSG_WIRLESS_SHOW_ONLY,
	GUI_MSG_WIRLESS_EVENT_KEY,
	GUI_MSG_WIRLESS_EVENT_TOUCH,
	GUI_MSG_WIRLESS_VIDEO_LAYER_SET,
	GUI_MSG_WIRLESS_QRCODE,
	GUI_MSG_WIRLESS_RESOURCE_QRCODE,
	GUI_MSG_WIRLESS_MIRROR_TYPE,
	GUI_MSG_WIRLESS_REFRESH_MENU_FUNC_KEY,
	GUI_MSG_WIRLESS_MENU_ICON,
	GUI_MSG_WIRLESS_MENU_FUNC_ICON,
	GUI_MSG_WIRLESS_SETUP_ICON,
    GUI_MSG_WIRLESS_

} __gui_my_msgid_wirless_t;

typedef enum __GUI_MY_MSGID_AUX_IN
{
	GUI_MSG_AUX_IN_REFRESH_INIT = GUI_MSG_WIRLESS_ + 1,
	GUI_MSG_AUX_IN_REFRESH_UNINIT,
	GUI_MSG_AUX_IN_REFRESH_HIDDEN,
	GUI_MSG_AUX_IN_REFRESH_PART,
	GUI_MSG_AUX_IN_REFRESH_MSG,
	GUI_MSG_AUX_IN_OPN_INIT,
	GUI_MSG_AUX_IN_OPN_UNINIT,
	GUI_MSG_AUX_IN_OPN_LAYER,

    GUI_MSG_AUX_IN_

} __gui_my_msgid_aux_in_t;

typedef enum __GUI_MY_MSGID_PARTY_MODE
{
	GUI_MSG_PARTY_MODE_REFRESH_INIT = GUI_MSG_AUX_IN_ + 1,
	GUI_MSG_PARTY_MODE_REFRESH_UNINIT,
	GUI_MSG_PARTY_MODE_REFRESH_HIDDEN,
	GUI_MSG_PARTY_MODE_REFRESH_PART,
	GUI_MSG_PARTY_MODE_REFRESH_MSG,
	GUI_MSG_PARTY_MODE_OPN_INIT,
	GUI_MSG_PARTY_MODE_OPN_UNINIT,
	GUI_MSG_PARTY_MODE_OPN_PART,

    GUI_MSG_PARTY_MODE_

} __gui_my_msgid_party_mode_t;

typedef enum __GUI_MY_MSGID_SXM
{
	GUI_MSG_SXM_REFRESH_INIT = GUI_MSG_PARTY_MODE_ + 1,
	GUI_MSG_SXM_REFRESH_UNINIT,
	GUI_MSG_SXM_REFRESH_HIDDEN,
	GUI_MSG_SXM_REFRESH_PART,
	GUI_MSG_SXM_REFRESH_MSG,
	GUI_MSG_SXM_OPN_INIT,
	GUI_MSG_SXM_OPN_UNINIT,
	GUI_MSG_SXM_OPN_PART,

    GUI_MSG_SXM_

} __gui_my_msgid_sxm_t;

typedef enum __GUI_MY_MSGID_PHONEBT
{
	GUI_MSG_PHONEBT_REFRESH_INIT = GUI_MSG_SXM_ + 1,
	GUI_MSG_PHONEBT_REFRESH_UNINIT,
	GUI_MSG_PHONEBT_REFRESH_WIN,
	GUI_MSG_PHONEBT_REFRESH_PART,
	GUI_MSG_PHONEBT_REFRESH_MSG,
	GUI_MSG_PHONEBT_REFRESH_DIALOG,
	GUI_MSG_PHONEBT_OPN_INIT,
	GUI_MSG_PHONEBT_OPN_UNINIT,
	GUI_MSG_PHONEBT_OPN_TIMER,
	GUI_MSG_PHONEBT_OPN_CLEAR_PHONEBOOK_MEM,

    GUI_MSG_PHONEBT_

} __gui_my_msgid_phonebt_t;

typedef enum __GUI_MY_MSGID_AUDIO_SETTING
{
	GUI_MSG_AUDIO_SETTING_REFRESH_INIT = GUI_MSG_PHONEBT_ + 1,
	GUI_MSG_AUDIO_SETTING_REFRESH_UNINIT,
	GUI_MSG_AUDIO_SETTING_REFRESH_WIN,
	GUI_MSG_AUDIO_SETTING_REFRESH_PART,
	GUI_MSG_AUDIO_SETTING_REFRESH_MSG,
	GUI_MSG_AUDIO_SETTING_OPN_INIT,
	GUI_MSG_AUDIO_SETTING_OPN_UNINIT,
	GUI_MSG_AUDIO_SETTING_SBUM_PARA,
	GUI_MSG_AUDIO_SETTING_RESET_ITEM,
	GUI_MSG_AUDIO_SETTING_SBUM_ONOFF,
    GUI_MSG_AUDIO_SETTING_

} __gui_my_msgid_audio_setting_t;

typedef enum __GUI_MY_MSGID_SLIDESHOW
{
	GUI_MSG_SLIDESHOW_REFRESH_INIT = GUI_MSG_AUDIO_SETTING_ + 1,
	GUI_MSG_SLIDESHOW_REFRESH_UNINIT,
	GUI_MSG_SLIDESHOW_REFRESH_MSG,
	GUI_MSG_SLIDESHOW_OPN_INIT,
	GUI_MSG_SLIDESHOW_OPN_UNINIT,

    GUI_MSG_SLIDESHOW_

} __gui_my_msgid_slideshow_t;

typedef enum __GUI_MY_MSGID_CLOCK
{
	GUI_MSG_CLOCK_REFRESH_INIT = GUI_MSG_SLIDESHOW_ + 1,
	GUI_MSG_CLOCK_REFRESH_UNINIT,
	GUI_MSG_CLOCK_REFRESH_PART,
	GUI_MSG_CLOCK_REFRESH_MSG,
	GUI_MSG_CLOCK_OPN_INIT,
	GUI_MSG_CLOCK_OPN_UNINIT,
	GUI_MSG_CLOCK_OPN_TIMER,

    GUI_MSG_CLOCK_

} __gui_my_msgid_clock_t;

typedef enum __GUI_MY_MSGID_BG
{
	GUI_MSG_BG_REFRESH_SRC_BUFF = GUI_MSG_CLOCK_ + 1,
	GUI_MSG_BG_REFRESH_SRC,
	GUI_MSG_BG_REFRESH_FLAG,
	GUI_MSG_BG_REFRESH_IMG,

    GUI_MSG_BG_

} __gui_my_msgid_bg_t;

typedef enum __GUI_MY_MSGID_TOP_BAR
{
	GUI_MSG_TOP_BAR_REFRESH_DRAW = GUI_MSG_BG_ + 1,
	GUI_MSG_TOP_BAR_REFRESH_STATUS,

    GUI_MSG_TOP_BAR_

} __gui_my_msgid_top_bar_t;

typedef enum __GUI_MY_MSGID_MENU
{
	GUI_MSG_MENU_REFRESH_SUSPEND = GUI_MSG_TOP_BAR_ + 1,
	GUI_MSG_MENU_REFRESH_RESUME,
    GUI_MSG_MENU_

} __gui_my_msgid_menu_t;

typedef enum __GUI_MY_MSGID_ROOT
{
    GUI_MSG_ROOT_REFRESH_INIT = GUI_MSG_MENU_ + 1,//0
    GUI_MSG_ROOT_REFRESH_UNINIT,
    GUI_MSG_ROOT_REFRESH_DIALOG,
    GUI_MSG_ROOT_REFRESH_DIALOG_CANCLE,
    GUI_MSG_ROOT_REFRESH_USB,
    GUI_MSG_ROOT_REFRESH_EVENT,
    GUI_MSG_ROOT_INIT,
    GUI_MSG_ROOT_UNINIT,
    GUI_MSG_ROOT_SEND_MSG,
    GUI_MSG_ROOT_SEND_KEY_MSG,
    GUI_MSG_ROOT_SOURCE_CREATE,//10
	GUI_MSG_ROOT_SOURCE_STORE,
	GUI_MSG_ROOT_SOURCE_RESTORE,
    GUI_MSG_ROOT_VIEW_CREATE,
    GUI_MSG_ROOT_APP_CREATE,
    GUI_MSG_ROOT_APP_DELECT,
    GUI_MSG_ROOT_APP_MUTE,
    GUI_MSG_ROOT_EVENT_COMMAND_PROC,
    GUI_MSG_ROOT_EVENT_BACKCAR_DET,
	APP_ROOT_MSG_DO_ASYNC,
	GUI_MSG_ROOT_LAYER_STATUS,
	GUI_MSG_ROOT_LAYER_STORE_STATUS,//20
	GUI_MSG_ROOT_HOME_STORE,
	GUI_MSG_ROOT_HOME_RESTORE,
	GUI_MSG_ROOT_BEEP_SETTING,
	GUI_MSG_ROOT_CLOSE_LOGO,
	GUI_MSG_ROOT_SEND_UART_DATA,
	GUI_MSG_ROOT_WIRLESS_MODE_INIT,
	GUI_MSG_ROOT_WIRLESS_MODE_UNINIT,
	GUI_MSG_ROOT_WIRLESS_RESET_CURRENT_WORK_MODE,
	GUI_MSG_ROOT_WIRLESS_PLUGIN,
	GUI_MSG_ROOT_WIRLESS_PLUGOUT,
	GUI_MSG_ROOT_WIRLESS_ENABLE,
	GUI_MSG_ROOT_WIRLESS_GET_PKT_PORT_STATE,
	GUI_MSG_ROOT_WIRLESS_GET_VIDEO_PORT_STATE,

	GUI_MSG_ROOT_WIRLESS_SWITCH_SOURCE,//30
	GUI_MSG_ROOT_WIRLESS_CLOSE_PKT_SOCKET,
	GUI_MSG_ROOT_WIRLESS_SOURCE_OPE,
	GUI_MSG_ROOT_WIRLESS_IPERF_CMD,
	GUI_MSG_ROOT_WIRLESS_IPERF2_CMD,
	GUI_MSG_ROOT_WIRLESS_SAVE_WALLPAPER_DATA,
#ifdef DMX7018_SIMILARITY
	GUI_MSG_ROOT_WIRLESS_SET_ENABLE,
#endif
	GUI_MSG_ROOT_FM_DEV,
	GUI_MSG_ROOT_IAP_SET_EXIST,
	GUI_MSG_ROOT_USB_CREATE,
	GUI_MSG_ROOT_USB_SWITCH_ROLE,
	GUI_MSG_ROOT_USB_SET_EXIST,
	GUI_MSG_ROOT_USB_SET_NO_EXIST,
#if defined(CONFIG_DRIVERS_USB_GADGET_NCM) || defined(CONFIG_DRIVERS_USB_GADGET_QVH)
	GUI_MSG_ROOT_USB_SET_ROLE,
#endif
	GUI_MSG_ROOT_AOA_SET_VPLAY_EXIT,
	GUI_MSG_ROOT_AOA_SAVE_WALLPAPER_DATA,
	GUI_MSG_ROOT_AOA_SEND_START_RECORDING,
	GUI_MSG_ROOT_MUXD_SET_VPLAY_EXIT,
	GUI_MSG_ROOT_MUXD_SAVE_WALLPAPER_DATA,
	GUI_MSG_ROOT_MUXD_SEND_CURRENT_MODE,
	GUI_MSG_ROOT_MUXD_SEND_APP_LIVE_STATUS,
	GUI_MSG_ROOT_MUXD_SWITCH_INIT,
	GUI_MSG_ROOT_MUXD_SWITCH_UNINIT,
	GUI_MSG_ROOT_MUXD_CLOSE_AUDIO,
	GUI_MSG_ROOT_MCU_SAVE_FLASH,
	GUI_MSG_ROOT_MCU_FW_SCAN,
	GUI_MSG_ROOT_CHECK_UPDATE_FILE,
	GUI_MSG_ROOT_CHECK_MCU_UPDATE_FILE,
	GUI_MSG_ROOT_SBUW_PARA,
	GUI_MSG_ROOT_SET_PLAY_PAUSE,
	GUI_MSG_ROOT_DSP_EXIT_INIT_TASK,
	GUI_MSG_ROOT_UNINIT_PLUGOUT,
	GUI_MSG_ROOT_FW_UPDATE,
	GUI_MSG_ROOT_INIT_PHONE_AUDIO_SETTING,
	GUI_MSG_ROOT_RESUME_PHONE_AUDIO_SETTING,
	GUI_MSG_ROOT_GOTO_BTMODE_SETTING,
	GUI_MSG_ROOT_RESUME_GOTO_BTMODE_SETTING,
	GUI_MSG_ROOT_GOTO_BTAUDIO_SETTING,
	GUI_MSG_ROOT_SEND_MODE_MSG,
	GUI_MSG_ROOT_TIMER,
	GUI_MSG_ROOT_PART,


    GUI_MSG_ROOT_

} __gui_my_msgid_root_t;

typedef enum __GUI_MY_MSGID_MSG
{
    GUI_MSG_BEEP_SETTING = GUI_MSG_ROOT_ + 1,
    GUI_MSG_MCU_BEEP_ON_OFF,
    GUI_MSG_MCU_MEMORY,
    GUI_MSG_MCU_FLUSH_FLASH_MEMORY,
    GUI_MSG_MCU_ERASE_FLASH_MEMORY,
    GUI_MSG_MCU_OE_KEY_INIT,
    GUI_MSG_MCU_OE_KEY_QUIT,
    GUI_MSG_MCU_OE_KEY_LEARN_START,
    GUI_MSG_MCU_OE_KEY_LEARN,
    GUI_MSG_MCU_OE_KEY_CANCEL,//10
    GUI_MSG_MCU_IO,
    GUI_MSG_MCU_DVR_TIME_FOMAT,
    GUI_MSG_MCU_DVR_SWITCH_FRONT_REAR_LENS,
    GUI_MSG_MCU_DVR_SHOW_ICON,
    GUI_MSG_MCU_DATE_TIME,
    GUI_MSG_MCU_CURRENT_SOURCE,
    GUI_MSG_MCU_POWER_OFF_STATUS,
    GUI_MSG_MCU_RESET,
#ifdef IPOD_TEST
    GUI_MSG_MCU_ACC_OFF,
#endif
    GUI_MSG_MCU_SEND_CMD,

	GUI_MSG_AUX_INIT,
	GUI_MSG_AUX_CHANNEL,
	GUI_MSG_AUX_SET_MIX,
	GUI_MSG_AUX_SET_XBASS_BY_LEVEL,//20
	GUI_MSG_AUX_SET_DYNAMIC,
	GUI_MSG_AUX_ITEM_RESET,
	GUI_MSG_AUX_BLANCE_RL_SPEAKER,
	GUI_MSG_AUX_BLANCE_FAD_SPEAKER,
	GUI_MSG_AUX_SET_EQ_FROM_USER_EX,
	GUI_MSG_AUX_SET_EQ_EX,
	GUI_MSG_AUX_SET_SUBWOOFER_PHASE,
	GUI_MSG_AUX_SET_MUSIC_ZONE,
	GUI_MSG_AUX_SET_DOUBLE_TRAK,
	GUI_MSG_AUX_SET_VOL,//30
	GUI_MSG_AUX_SET_BTPHONE_VOL,
	GUI_MSG_AUX_SET_MUTE,

	GUI_MSG_DAB_SWITCH_BAND,
	GUI_MSG_DAB_KEY_FUNC,
	GUI_MSG_DAB_SEL_PRESET,
	GUI_MSG_DAB_SAVE_FLUSH,
	GUI_MSG_DAB_UNINSTALL_RADIO_MODULE,

	GUI_MSG_BT_PLAY_PAUSE,
	GUI_MSG_BT_NEXT_PREV,
	GUI_MSG_BT_PLAY_STATUS,//40
	GUI_MSG_BT_PHONE_VOICE_DATA,
	GUI_MSG_BT_AVRCP_REQUEST_CAREGORY_INDEX,
	GUI_MSG_BT_AVRCP_EXIT_BROWSER,
	GUI_MSG_BT_AVRCP_EXIT_CAREGORY_BROWSER_BEGIN,
	GUI_MSG_BT_AVRCP_EXIT_CAREGORY_PREV_LAYER,
	GUI_MSG_BT_DB_LINK_CHANGE_INDEX,
	GUI_MSG_BT_USB_CAREGORY_BROWSER_BEGIN,
	GUI_MSG_BT_USB_STOP_SEND_BRO_DATA,
	GUI_MSG_BT_USB_PLAY_STATUS,
	GUI_MSG_BT_USB_REPEAT_RANDOM_STATUS,//50
	GUI_MSG_BT_USB_NEXT_PREV,
	GUI_MSG_BT_USB_END_FORWARD,
	GUI_MSG_BT_USB_CAREGORY_PREV_LAYER,
	GUI_MSG_BT_USB_REQUEST_CAREGORY_INDEX,
	GUI_MSG_BT_BOOK_AND_HISTORY_DATA,
	GUI_MSG_BT_DOWNLOAD_PHONEBOOK,
	GUI_MSG_BT_REQUEST_UPDATE_PHONEBOOK,
	GUI_MSG_BT_THREE_WAY_CALL_DATA,
	GUI_MSG_BT_DIALING_DATA,
	GUI_MSG_BT_DIALING_KEYNUM_DATA,//60
	GUI_MSG_BT_REDIAL_DATA,
	GUI_MSG_BT_INTO_BLE,
	GUI_MSG_BT_BLE_NOTIFY_ON_OFF,
	GUI_MSG_BT_DISCONNECT_BLE,
	GUI_MSG_BT_ASK_DEVICE_NAME,
	GUI_MSG_BT_CLEAR_IAP2_MEMORY,
	GUI_MSG_BT_CONNECT_DEVICE,
	GUI_MSG_BT_DISCONNECT_DEVICE,
	GUI_MSG_BT_REMOVE_DEVICE,
	GUI_MSG_BT_CURRENT_MODE,//70
	GUI_MSG_BT_PIN_CODE_AFFIRM,
	GUI_MSG_BT_MIRROR_TYPE,
	GUI_MSG_BT_POWER_OFF_STATUS,
	GUI_MSG_BT_GLOBAL_VOICE_ON_OFF,
#ifdef USE_BT_SEARCH_FUN
	GUI_MSG_BT_SCAN_START,
	GUI_MSG_BT_SCAN_CANCEL,
	GUI_MSG_BT_SCAN_CONNECT,
#endif
#ifdef MIRROR_SPP_FUNC
	GUI_MSG_BT_MIRROR_LUANCH_APP,
	GUI_MSG_BT_SET_SPP_STATUS,//add by deng 20241104
#endif
	GUI_MSG_BT_SET_HID_STATUS,//add by deng 20241104
	GUI_MSG_WIFI_MODE_INIT,

    GUI_MSG_DISP_LCD_BRIGHTNESS,
    GUI_MSG_DISP_LCD_BRIGHTNESS_ENABLE,
    GUI_MSG_DISP_LCD_OFF,
    GUI_MSG_LANG_SET_TYPE,
    GUI_MSG_BG_SRC,
} __gui_my_msgid_msg_t;

typedef enum tag_GUI_LYRWIN_STA
{
    GUI_LYRWIN_STA_ON = 0,                            /* on                                                           */
    GUI_LYRWIN_STA_OFF,                               /* off                                                          */
    GUI_LYRWIN_STA_SLEEP,                             /* off, layer released, but frame buffer not free               */
    GUI_LYRWIN_STA_SUSPEND,                           /* off, layer released, frame buffer free                       */
    GUI_LYRWIN_STA_ON_PENDING, //mllee 180523 added
    GUI_LYRWIN_STA_SLEEP_PENDING, //mllee 180523 added
    GUI_LYRWIN_STA_ERR
} __gui_lyrwin_sta_t;

typedef __gui_msg_t  *pmsg;

/*子窗口用来向父窗口的通知消息结构*/
typedef struct __GUI_NOTIFY_MSG
{
    __u32*        hWnd;                       /*子窗口句柄*/
    __u32        id;                         /*子窗口在父窗口中的唯一标识*/
    __u32        msgcode;                    /*通知消息的通知码*/
    __u32        dwAddData;                  /*通知消息的附加值*/
    __u32        dwReserved;                 /*通知消息的保留附加值*/
} __gui_notify_msg_t;

typedef __s32(*cb_key_hook)(__gui_msg_t *msg);
typedef __s32(*cb_tp_hook)(__gui_msg_t *msg);
typedef __s32(*cb_init_hook)(__gui_msg_t *msg);
typedef __s32(*cb_tvout_tp_hook)(__gui_msg_t *msg);

typedef enum __GUI_MSG_HOOK_TYPE
{
    GUI_MSG_HOOK_TYPE_KEY = 0,
    GUI_MSG_HOOK_TYPE_TP,
    GUI_MSG_HOOK_TYPE_INIT
} __gui_msg_hook_type;
typedef struct _memit_hook_t
{
	cb_key_hook 	key_hook;
	cb_tp_hook  	tp_hook;
	cb_init_hook 	init_hook;
}__memit_hook_t;

__s32 msg_emit_init(void);
__s32 msg_emit_deinit(void);
__s32 msg_emit_register_hook(__memit_hook_t *hook,__gui_msg_hook_type type);
__s32 msg_emit_unregister_hook(__gui_msg_hook_type type);

__pCBK_t dsk_scan_file_key_set_cb(__pCBK_t cb , void * ctx);
__s32 msg_emit_register_tvout_tp_hook(cb_tvout_tp_hook hook);

#endif /* __MSG_EMIT_H__ */
