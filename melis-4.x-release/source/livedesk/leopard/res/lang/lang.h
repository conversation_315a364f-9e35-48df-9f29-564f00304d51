/*
**********************************************************************************************************************
														 ePDK
										 the Easy Portable/Player Develop Kits
*												 RES Compiler Sub-System
*
* 												(c) Copyright 2007-2010 
* 												 All Rights Reserved
*
* Moudle	: language compiler
* File		: lang.h
*
* By		: 
* Version	: v1.00
* Date		: 2025-8-16
**********************************************************************************************************************
*/


#ifndef __LANG__H__
#define __LANG__H__

// LangId 
#define	LANG_CHINESES_TYPE		0x410
#define	LANG_CHINESET_TYPE		0x420
#define	LANG_ENGLISH_TYPE		0x400
#define	LANG_SPANISH_TYPE		0x460
#define	LANG_FRENCH_TYPE		0x470
#define	LANG_GERMAN_TYPE		0x450
#define	LANG_ITALIAN_TYPE		0x480
#define	LANG_DUTCH_TYPE		0x4a0
#define	LANG_FINNISH_TYPE		0x550
#define	LANG_RUSSIAN_TYPE		0x4b0
#define	LANG_PORTUGUESE_TYPE		0x490
#define	LANG_POLISH_TYPE		0x4c0
#define	LANG_CZECH_TYPE		0x4e0
#define	LANG_HUNGARIAN_TYPE		0x500
#define	LANG_VIETNAMESE_TYPE		0x560

// StringID 
#define 	 STRING_HEADER_SIGNAL_STRENGTH		0x01
#define 	 STRING_HEADER_SIGNAL_QUALITY		0x02
#define 	 STRING_HEADER_VOICE_VOLUME		0x03
#define 	 STRING_BURN_CA_INFO_ING		0x04
#define 	 STRING_BURN_CA_INFO_FAIL		0x05
#define 	 STRING_INIT_FILE_NOT_FOUND		0x06
#define 	 STRING_BURN_CA_INFO_OK		0x07
#define 	 STRING_BURN_CA_INFO_FILE_FORMAT_ERR		0x08
#define 	 STRING_BURN_CA_INFO_XML_CREATE_FAIL		0x09
#define 	 STRING_BURN_CA_INFO_DRV_OPEN_FAIL		0x0a
#define 	 STRING_BURN_CA_INFO_SPI_ERASE_FAIL		0x0b
#define 	 STRING_BURN_CA_INFO_SPI_READ_FAIL		0x0c
#define 	 STRING_BURN_CA_INFO_SPI_WRITE_FAIL		0x0d
#define 	 STRING_BURN_CA_INFO_CHECK_SUM_FAIL		0x0e
#define 	 STRING_BURN_CA_INFO_SYSTEM_ERR		0x0f
#define 	 STRING_ERASE_CA_INFO_OK		0x10
#define 	 STRING_ERASE_CA_INFO_ERR		0x11
#define 	 STRING_USB_CONNECTING		0x12
#define 	 STRING_UPDATE_FILE_ERROR		0x13
#define 	 STRING_UPDATE_FILE_NOT_EXIST		0x14
#define 	 STRING_UPDATE_FINISH		0x15
#define 	 STRING_UPDATE_FAIL		0x16
#define 	 STRING_POWER_OFF		0x17
#define 	 STRING_UPDATE_ORCHID		0x18
#define 	 STRING_LOW_POWER		0x19
#define 	 STRING_ADJUST_FINISH		0x1a
#define 	 STRING_FM		0x1b
#define 	 STRING_INIT_ENTER_HOME		0x1c
#define 	 STRING_ADJUST_PROMPT		0x1d
#define 	 STRING_HOME_INSERT_DISK		0x3e9
#define 	 STRING_HOME_TV		0x3ea
#define 	 STRING_HOME_FM		0x3eb
#define 	 STRING_HOME_MOVIE		0x3ec
#define 	 STRING_HOME_PHOTO		0x3ed
#define 	 STRING_HOME_MUSIC		0x3ee
#define 	 STRING_HOME_OTHERS		0x3ef
#define 	 STRING_HOME_SETTING		0x3f0
#define 	 STRING_HOME_TV_PLAY		0x3f1
#define 	 STRING_HOME_CHANNEL_SEARCH		0x3f2
#define 	 STRING_HOME_AV_INPUT		0x3f3
#define 	 STRING_HOME_FM_PLAY		0x3f4
#define 	 STRING_HOME_NULL_DISK		0x3f5
#define 	 STRING_HOME_SD		0x3f6
#define 	 STRING_HOME_USB		0x3f7
#define 	 STRING_HOME_MMC		0x3f8
#define 	 STRING_HOME_EXPLORER		0x3f9
#define 	 STRING_HOME_EBOOK		0x3fa
#define 	 STRING_HOME_CALENDAR		0x3fb
#define 	 STRING_HOME_SET_COMMON		0x3fc
#define 	 STRING_HOME_SET_POWER		0x3fd
#define 	 STRING_HOME_SET_TIMER		0x3fe
#define 	 STRING_HOME_SET_MULTIMEDIA		0x3ff
#define 	 STRING_HOME_CUE		0x400
#define 	 STRING_HOME_RECORD		0x401
#define 	 STRING_HOME_TITLE		0x402
#define 	 STRING_HOME_AUX		0x403
#define 	 STRING_HOME_ALARM		0x404
#define 	 STRING_HOME_YEAR		0x405
#define 	 STRING_HOME_MONTH		0x406
#define 	 STRING_HOME_GAME		0x407
#define 	 STRING_HOME_DICT		0x408
#define 	 STRING_HOME_INSERT_CARD		0x409
#define 	 STRING_HOME_PHONEBT		0x40a
#define 	 STRING_HOME_IPOD		0x40b
#define 	 STRING_HOME_MIRRORING		0x40c
#define 	 STRING_HOME_BTAUDIO		0x40d
#define 	 STRING_HOME_DAB		0x40e
#define 	 STRING_HOME_TUNER		0x40f
#define 	 STRING_HOME_PHONE		0x410
#define 	 STRING_HOME_USB_HUB		0x411
#define 	 STRING_EXPLR_EXPLORER		0x7d1
#define 	 STRING_EXPLR_MOVIE		0x7d2
#define 	 STRING_EXPLR_GAME		0x7d3
#define 	 STRING_EXPLR_EBOOK		0x7d4
#define 	 STRING_EXPLR_SD		0x7d5
#define 	 STRING_EXPLR_USB		0x7d6
#define 	 STRING_EXPLR_TYPE		0x7d7
#define 	 STRING_EXPLR_FOLDER		0x7d8
#define 	 STRING_EXPLR_DATE		0x7d9
#define 	 STRING_EXPLR_SPACE		0x7da
#define 	 STRING_EXPLR_RESOLUTION		0x7db
#define 	 STRING_EXPLR_BACKTOUP		0x7dc
#define 	 STRING_EXPLR_TEST		0x7dd
#define 	 STRING_EXPLR_DELETE_CONFIRM		0x7de
#define 	 STRING_EXPLR_TIME_YEAR		0x7df
#define 	 STRING_EXPLR_TIME_MONTH		0x7e0
#define 	 STRING_EXPLR_TIME_DAY		0x7e1
#define 	 STRING_EXPLR_TIME_HOUR		0x7e2
#define 	 STRING_EXPLR_TIME_MINUTE		0x7e3
#define 	 STRING_EXPLR_TIME_SECOND		0x7e4
#define 	 STRING_EXPLR_MANAGER_TITLE		0x7e5
#define 	 STRING_EXPLR_TITLE		0x7e6
#define 	 STRING_EXPLR_ALL_MEDIA		0x7e7
#define 	 STRING_EXPLR_GAME_MEDIA		0x7e8
#define 	 STRING_EXPLR_EBOOK_MEDIA		0x7e9
#define 	 STRING_EXPLR_MUSIC_MEDIA		0x7ea
#define 	 STRING_EXPLR_MOVIE_MEDIA		0x7eb
#define 	 STRING_EXPLR_PHOTO_MEDIA		0x7ec
#define 	 STRING_EXPLR_DELETE_HINT		0x7ed
#define 	 STRING_EXPLR_DELETE_FAILED		0x7ee
#define 	 STRING_EXPLR_CUE		0x7ef
#define 	 STRING_EXPLR_FW_UPDATE_CONFIRM		0x7f0
#define 	 STRING_EXPLR_NULL_DISK		0x7f1
#define 	 STRING_EXPLR_FOLDER_EMPTY		0x7f2
#define 	 STRING_EXPLR_NULL_FILE		0x7f3
#define 	 STRING_EXPLR_GET_PIC_INFO_FAIL		0x7f4
#define 	 STRING_EXPLR_COPY		0x7f5
#define 	 STRING_EXPLR_PARSE		0x7f6
#define 	 STRING_EXPLR_CUT		0x7f7
#define 	 STRING_EXPLR_DEL		0x7f8
#define 	 STRING_EXPLR_CANNEL		0x7f9
#define 	 STRING_EXPLR_MUSIC_EMPTY		0x7fa
#define 	 STRING_EXPLR_EBOOK_EMPTY		0x7fb
#define 	 STRING_EXPLR_VEDIO_EMPTY		0x7fc
#define 	 STRING_EXPLR_PHOTO_EMPTY		0x7fd
#define 	 STRING_EXPLR_GAME_EMPTY		0x7fe
#define 	 STRING_EXPLR_SWITCH_TO_LEFT		0x7ff
#define 	 STRING_EXPLR_SWITCH_TO_RIGHT		0x800
#define 	 STRING_EXPLR_FILE_SIZE		0x801
#define 	 STRING_EXPLR_FILE_TIME		0x802
#define 	 STRING_EXPLR_TFROOT		0x803
#define 	 STRING_EXPLR_USBROOT		0x804
#define 	 STRING_MOVIE_RANDOM		0xbb9
#define 	 STRING_MOVIE_ROTATE_ALL		0xbba
#define 	 STRING_MOVIE_ROTATE_ONE		0xbbb
#define 	 STRING_MOVIE_SEQUENCE		0xbbc
#define 	 STRING_MOVIE_ONLY_ONE		0xbbd
#define 	 STRING_MOVIE_SCREEN_RATIO		0xbbe
#define 	 STRING_MOVIE_VIDEO_RATIO		0xbbf
#define 	 STRING_MOVIE_4R3		0xbc0
#define 	 STRING_MOVIE_ORGIN_SIZE		0xbc1
#define 	 STRING_MOVIE_16R9		0xbc2
#define 	 STRING_MOVIE_VIDEO_INFO		0xbc3
#define 	 STRING_MOVIE_AUDIO_INFO		0xbc4
#define 	 STRING_MOVIE_FILE_INFO		0xbc5
#define 	 STRING_MOVIE_SUB_STATE		0xbc6
#define 	 STRING_MOVIE_SUB_SIZE		0xbc7
#define 	 STRING_MOVIE_SUB_COLOR		0xbc8
#define 	 STRING_MOVIE_SUB_POS		0xbc9
#define 	 STRING_MOVIE_SUB_ON		0xbca
#define 	 STRING_MOVIE_SUB_OFF		0xbcb
#define 	 STRING_MOVIE_SUB_WHITE		0xbcc
#define 	 STRING_MOVIE_SUB_RED		0xbcd
#define 	 STRING_MOVIE_SUB_BLUE		0xbce
#define 	 STRING_MOVIE_SUB_BOTTOM		0xbcf
#define 	 STRING_MOVIE_SUB_MID		0xbd0
#define 	 STRING_MOVIE_SUB_TOP		0xbd1
#define 	 STRING_MOVIE_BREAKPLAY		0xbd2
#define 	 STRING_MOVIE_FILE_NOT_SUPPORT		0xbd3
#define 	 STRING_MOVIE_PLAY_END		0xbd4
#define 	 STRING_MOVIE_TIPS		0xbd5
#define 	 STRING_MOVIE_SUB_STREAM		0xbd6
#define 	 STRING_MOVIE_SUB_CHS		0xbd7
#define 	 STRING_MOVIE_SUB_CHT		0xbd8
#define 	 STRING_MOVIE_TITLE		0xbd9
#define 	 STRING_MOVIE_CHANNEL_LEFT		0xbda
#define 	 STRING_MOVIE_CHANNEL_RIGHT		0xbdb
#define 	 STRING_MOVIE_CHANNEL_STEREO		0xbdc
#define 	 STRING_MOVIE_TRACK0		0xbdd
#define 	 STRING_MOVIE_TRACK1		0xbde
#define 	 STRING_MOVIE_TRACK2		0xbdf
#define 	 STRING_MOVIE_TRACK3		0xbe0
#define 	 STRING_MOVIE_TRACK4		0xbe1
#define 	 STRING_MOVIE_TRACK5		0xbe2
#define 	 STRING_MOVIE_FF_RR_START		0xbe3
#define 	 STRING_MOVIE_FF		0xbe4
#define 	 STRING_MOVIE_RR		0xbe5
#define 	 STRING_MOVIE_RETURN		0xbe6
#define 	 STRING_MOVIE_REPEAT		0xbe7
#define 	 STRING_MOVIE_CHANNEL		0xbe8
#define 	 STRING_MOVIE_TRACK		0xbe9
#define 	 STRING_MOVIE_ZOOM		0xbea
#define 	 STRING_MOVIE_SUBTITLE		0xbeb
#define 	 STRING_MOVIE_TVOUT		0xbec
#define 	 STRING_MOVIE_AB		0xbed
#define 	 STRING_MOVIE_A		0xbee
#define 	 STRING_MOVIE_B		0xbef
#define 	 STRING_MOVIE_PRE		0xbf0
#define 	 STRING_MOVIE_PLAY		0xbf1
#define 	 STRING_MOVIE_STOP		0xbf2
#define 	 STRING_MOVIE_NEXT		0xbf3
#define 	 STRING_NO_THIS_MOVIE		0xbf4
#define 	 STRING_MOVIE_PAUSE		0xbf5
#define 	 STRING_DISK_FORMAT_NOT_SUPPORT		0xbf6
#define 	 STRING_MOVIE_BRIGHT		0xbf7
#define 	 STRING_MOVIE_CONTRAST		0xbf8
#define 	 STRING_MOVIE_SATURATION		0xbf9
#define 	 STRING_MOVIE_HUE		0xbfa
#define 	 STRING_MOVIE_DAGE		0xbfb
#define 	 STRING_MOVIE_DETAIL		0xbfc
#define 	 STRING_MUSIC_PLAY_ALL		0xfa1
#define 	 STRING_MUSIC_PLAY_ONCE		0xfa2
#define 	 STRING_MUSIC_PLAY_ROTATE_ONE		0xfa3
#define 	 STRING_MUSIC_PLAY_ROTATE_ALL		0xfa4
#define 	 STRING_MUSIC_PLAY_RANDOM		0xfa5
#define 	 STRING_MUSIC_BL_ON		0xfa6
#define 	 STRING_MUSIC_BL_10S		0xfa7
#define 	 STRING_MUSIC_BL_30S		0xfa8
#define 	 STRING_MUSIC_BL_60S		0xfa9
#define 	 STRING_MUSIC_BL_90S		0xfaa
#define 	 STRING_MUSIC_PLAY_PREVIOUS		0xfab
#define 	 STRING_MUSIC_PLAY_NEXT		0xfac
#define 	 STRING_MUSIC_PLAY_PAUSE		0xfad
#define 	 STRING_MUSIC_PLAY_FORWARD		0xfae
#define 	 STRING_MUSIC_PLAY_BACKWARD		0xfaf
#define 	 STRING_MUSIC_TITLE		0xfb0
#define 	 STRING_MUSIC_RETURN		0xfb1
#define 	 STRING_MUSIC_EQ		0xfb2
#define 	 STRING_MUSIC_AB		0xfb3
#define 	 STRING_MUSIC_A		0xfb4
#define 	 STRING_MUSIC_B		0xfb5
#define 	 STRING_MUSIC_ALBUM		0xfb6
#define 	 STRING_MUSIC_EQ_NORMAL		0xfb7
#define 	 STRING_MUSIC_EQ_DDB		0xfb8
#define 	 STRING_MUSIC_EQ_JAZZ		0xfb9
#define 	 STRING_MUSIC_EQ_ROCK		0xfba
#define 	 STRING_MUSIC_EQ_POP		0xfbb
#define 	 STRING_MUSIC_EQ_CLASSIC		0xfbc
#define 	 STRING_MUSIC_EQ_VACAL		0xfbd
#define 	 STRING_MUSIC_EQ_DANCE		0xfbe
#define 	 STRING_MUSIC_EQ_SOFT		0xfbf
#define 	 STRING_MUSIC_NO_THIS_SONG		0xfc0
#define 	 STRING_MUSIC_TACK_STEREO		0xfc1
#define 	 STRING_MUSIC_TACK_LEFT		0xfc2
#define 	 STRING_MUSIC_TACK_RIGHT		0xfc3
#define 	 STRING_MUSIC_SWITCH_TO_USB		0xfc4
#define 	 STRING_MUSIC_SWITCH_TO_TF		0xfc5
#define 	 STRING_MUSIC_SWITCH_USB_TF_INFO		0xfc6
#define 	 STRING_MUSIC_SWITCH_USB_DEVICE_FAIL		0xfc7
#define 	 STRING_MUSIC_SWITCH_TF_CARD_FAIL		0xfc8
#define 	 STRING_MUSIC_REPEAT		0xfc9
#define 	 STRING_MUSIC_RANDOM		0xfca
#define 	 STRING_MUSIC_ONE		0xfcb
#define 	 STRING_MUSIC_ALL		0xfcc
#define 	 STRING_MUSIC_NULL		0xfcd
#define 	 STRING_MUSIC_MEDIA		0xfce
#define 	 STRING_MUSIC_LIST		0xfcf
#define 	 STRING_MUSIC_NO_ARTIST		0xfd0
#define 	 STRING_MUSIC_NO_ALBUM		0xfd1
#define 	 STRING_MUSIC_NO_TITLE		0xfd2
#define 	 STRING_MUSIC_SONGS		0xfd3
#define 	 STRING_MUSIC_ARTISTS		0xfd4
#define 	 STRING_MUSIC_ALBUMS		0xfd5
#define 	 STRING_MUSIC_TRACKS		0xfd6
#define 	 STRING_SET_POWER_OFF_LAST10M_CON		0x1389
#define 	 STRING_SET_POWER_BGT_LAST10S_CON		0x138a
#define 	 STRING_SET_POWER_OFF_LAST20M_CON		0x138b
#define 	 STRING_SET_POWER_BGT_LAST30S_CON		0x138c
#define 	 STRING_SET_POWER_OFF_LAST30M_CON		0x138d
#define 	 STRING_SET_POWER_OFF_LAST1H_CON		0x138e
#define 	 STRING_SET_POWER_BGT_LAST60S_CON		0x138f
#define 	 STRING_SET_POWER_BGT_LAST90S_CON		0x1390
#define 	 STRING_SET_COMMON_PRODUCTINFO_DE		0x1391
#define 	 STRING_SET_OUTPUT_NTSC_CON		0x1392
#define 	 STRING_SET_OUTPUT_PAL_CON		0x1393
#define 	 STRING_SET_KEYTONE_TITLE		0x1394
#define 	 STRING_SET_KEYTONE_EX		0x1395
#define 	 STRING_SET_POWER_BGT_TITLE		0x1396
#define 	 STRING_SET_POWER_BGT_EX		0x1397
#define 	 STRING_SET_POWER_BGT_DE		0x1398
#define 	 STRING_SET_COMMON_PRODUCTINFO_TITLE		0x1399
#define 	 STRING_SET_COMMON_PRODUCTINFO_EX		0x139a
#define 	 STRING_SET_COMMON_FACTORY_DEFAULT_EX		0x139b
#define 	 STRING_SET_TIPS_TSIZE		0x139c
#define 	 STRING_SET_POWER_BGT_NEVER_CON		0x139d
#define 	 STRING_SET_POWER_OFF_NEVER_CON		0x139e
#define 	 STRING_SET_PROMPT_POWER_MANAGER		0x139f
#define 	 STRING_SET_COMMON_TOPIC_STYLE1_CON		0x13a0
#define 	 STRING_SET_COMMON_TOPIC_STYLE2_CON		0x13a1
#define 	 STRING_SET_COMMON_TOPIC_STYLE3_CON		0x13a2
#define 	 STRING_SET_TIPS_NO		0x13a3
#define 	 STRING_SET_KEYTONE_OFF_CON		0x13a4
#define 	 STRING_SET_POWER_BGT_OFF_TITLE		0x13a5
#define 	 STRING_SET_POWER_OFF_TITLE		0x13a6
#define 	 STRING_SET_POWER_OFF_EX		0x13a7
#define 	 STRING_SET_POWER_BGT_OFF_DE		0x13a8
#define 	 STRING_SET_COMMON_FACTORY_DEFAULT_TITLE		0x13a9
#define 	 STRING_SET_KEYTONE_ON_CON		0x13aa
#define 	 STRING_SET_KEYTONE_DE		0x13ab
#define 	 STRING_SET_POWER_BGT_ON_EX		0x13ac
#define 	 STRING_SET_POWER_BGT_LEVEL1_CON		0x13ad
#define 	 STRING_SET_POWER_BGT_LEVEL10_CON		0x13ae
#define 	 STRING_SET_POWER_BGT_LEVEL11_CON		0x13af
#define 	 STRING_SET_POWER_BGT_LEVEL12_CON		0x13b0
#define 	 STRING_SET_POWER_BGT_LEVEL13_CON		0x13b1
#define 	 STRING_SET_POWER_BGT_LEVEL14_CON		0x13b2
#define 	 STRING_SET_POWER_BGT_LEVEL16_CON		0x13b3
#define 	 STRING_SET_POWER_BGT_LEVEL15_CON		0x13b4
#define 	 STRING_SET_POWER_BGT_LEVEL2_CON		0x13b5
#define 	 STRING_SET_POWER_BGT_LEVEL3_CON		0x13b6
#define 	 STRING_SET_POWER_BGT_LEVEL4_CON		0x13b7
#define 	 STRING_SET_POWER_BGT_LEVEL5_CON		0x13b8
#define 	 STRING_SET_POWER_BGT_LEVEL6_CON		0x13b9
#define 	 STRING_SET_POWER_BGT_LEVEL7_CON		0x13ba
#define 	 STRING_SET_POWER_BGT_LEVEL8_CON		0x13bb
#define 	 STRING_SET_POWER_BGT_LEVEL9_CON		0x13bc
#define 	 STRING_SET_TIPS_SOFT_VERSION		0x13bd
#define 	 STRING_SET_POWER_OFF_DE		0x13be
#define 	 STRING_SET_POWER_BGT_OFF_EX		0x13bf
#define 	 STRING_SET_COMMON_LANGUAGE_DE		0x13c0
#define 	 STRING_SET_COMMON_LANGUAGE_EX		0x13c1
#define 	 STRING_SET_COMMON_TOPIC_STYLE_DE		0x13c2
#define 	 STRING_SET_TIPS_FSIZE		0x13c3
#define 	 STRING_SET_TIPS_YES		0x13c4
#define 	 STRING_SET_COMMON_FACTORY_DEFAULT_DE		0x13c5
#define 	 STRING_SET_OUTPUT_TITLE		0x13c6
#define 	 STRING_SET_OUTPUT_EX		0x13c7
#define 	 STRING_SET_OUTPUT_DE		0x13c8
#define 	 STRING_SET_PROMPT_COMMON		0x13c9
#define 	 STRING_SET_TUESDAY		0x13ca
#define 	 STRING_SET_SATURDAY		0x13cb
#define 	 STRING_SET_WEDNESDAY		0x13cc
#define 	 STRING_SET_THURSDAY		0x13cd
#define 	 STRING_SET_SUNDAY		0x13ce
#define 	 STRING_SET_FRIDAY		0x13cf
#define 	 STRING_SET_MONDAY		0x13d0
#define 	 STRING_SET_COMMON_LANGUAGE_ENGLISH_CON		0x13d1
#define 	 STRING_SET_COMMON_LANGUAGE_TITLE		0x13d2
#define 	 STRING_SET_COMMON_LANGUAGE_CHINESES_CON		0x13d3
#define 	 STRING_SET_COMMON_TOPIC_STYLE_TITLE		0x13d4
#define 	 STRING_SET_COMMON_RESTORE		0x13d5
#define 	 STRING_SET_ADJUST_TITLE		0x13d6
#define 	 STRING_SET_ADJUST_EX		0x13d7
#define 	 STRING_SET_ADJUST_DE		0x13d8
#define 	 STRING_SET_TITLE		0x13d9
#define 	 STRING_SET_TIPS		0x13da
#define 	 STRING_OK		0x13db
#define 	 STRING_SET_ALARM		0x13dc
#define 	 STRING_SET_ALARM1		0x13dd
#define 	 STRING_SET_ALARM2		0x13de
#define 	 STRING_SET_ALARM3		0x13df
#define 	 STRING_SET_EVERYDAY		0x13e0
#define 	 STRING_SET_DUTYDAY		0x13e1
#define 	 STRING_SET_WEEKEND		0x13e2
#define 	 STRING_SET_ONETIME		0x13e3
#define 	 STRING_SET_ALARM_SET		0x13e4
#define 	 STRING_SET_ALARMSTATE		0x13e5
#define 	 STRING_SET_ALARMSTATE_EX1		0x13e6
#define 	 STRING_SET_ALARMSTATE_EX2		0x13e7
#define 	 STRING_SET_ALARMTIME		0x13e8
#define 	 STRING_SET_ALARMTIME_EX1		0x13e9
#define 	 STRING_SET_ALARM_REPEATTIME		0x13ea
#define 	 STRING_SET_ALARM_REPEATTIME_EX1		0x13eb
#define 	 STRING_SET_ALARM_REPEATTIME_EX2		0x13ec
#define 	 STRING_SET_ALARM_RING		0x13ed
#define 	 STRING_SET_ALARM_RING_EX1		0x13ee
#define 	 STRING_SET_ALARM_RING_EX2		0x13ef
#define 	 STRING_SET_RING1		0x13f0
#define 	 STRING_SET_RING2		0x13f1
#define 	 STRING_SET_RING3		0x13f2
#define 	 STRING_SET_ALARM_VOL		0x13f3
#define 	 STRING_SET_ALARM_VOL_EX1		0x13f4
#define 	 STRING_SET_ALARM_VOL_EX2		0x13f5
#define 	 STRING_SET_ALARM_RUNTIME		0x13f6
#define 	 STRING_SET_ALARM_RUNTIME_EX1		0x13f7
#define 	 STRING_SET_ALARM_RUNTIME_EX2		0x13f8
#define 	 STRING_SET_20SECOND		0x13f9
#define 	 STRING_SET_30SECOND		0x13fa
#define 	 STRING_SET_50SECOND		0x13fb
#define 	 STRING_SET_90SECOND		0x13fc
#define 	 STRING_SET_ALARM_DELAY		0x13fd
#define 	 STRING_SET_ALARM_DELAY_EX1		0x13fe
#define 	 STRING_SET_ALARM_DELAY_EX2		0x13ff
#define 	 STRING_SET_5MINUTE		0x1400
#define 	 STRING_SET_10MINUTE		0x1401
#define 	 STRING_SET_15MINUTE		0x1402
#define 	 STRING_SET_OK		0x1403
#define 	 STRING_SET_CANCLE		0x1404
#define 	 STRING_SET_NULLDISK		0x1405
#define 	 STRING_SET_EXPLORER_LISTEN		0x1406
#define 	 STRING_SET_COMMON_LANGUAGE_CHINESET_CON		0x1407
#define 	 STRING_SET_TIMEKEEPING		0x1408
#define 	 STRING_SET_TIMEKEEPING_ON_CON		0x1409
#define 	 STRING_SET_TIMEKEEPING_OFF_CON		0x140a
#define 	 STRING_SET_UPDATE		0x140b
#define 	 STRING_SET_GAME_HANDLE_ADJUST		0x140c
#define 	 STRING_SET_GAME_HANDLE_ADJUST_SET		0x140d
#define 	 STRING_SET_GAME_HANDLE_ADJUST_DONE		0x140e
#define 	 STRING_SET_GAME_NO_HANDLE		0x140f
#define 	 STRING_SET_GAME_HANDLE_UP		0x1410
#define 	 STRING_SET_GAME_HANDLE_DOWN		0x1411
#define 	 STRING_SET_GAME_HANDLE_LEFT		0x1412
#define 	 STRING_SET_GAME_HANDLE_RIGHT		0x1413
#define 	 STRING_SET_GAME_HANDLE_A		0x1414
#define 	 STRING_SET_GAME_HANDLE_B		0x1415
#define 	 STRING_SET_GAME_HANDLE_C		0x1416
#define 	 STRING_SET_GAME_HANDLE_D		0x1417
#define 	 STRING_SET_GAME_HANDLE_LSHOOT		0x1418
#define 	 STRING_SET_GAME_HANDLE_RSHOOT		0x1419
#define 	 STRING_SET_GAME_HANDLE_START		0x141a
#define 	 STRING_SET_GAME_HANDLE_ESC		0x141b
#define 	 STRING_SET_GAME_HANDLE_SELECT		0x141c
#define 	 STRING_SET_TIMING_SHUTDOWN_OFF		0x141d
#define 	 STRING_SET_TIMING_SHUTDOWN		0x141e
#define 	 STRING_SET_30MINUTE		0x141f
#define 	 STRING_SET_45MINUTE		0x1420
#define 	 STRING_SET_60MINUTE		0x1421
#define 	 STRING_SET_POWER_BACK_LIGHT		0x1422
#define 	 STRING_SET_COMMON_KEYTONE_EX		0x1423
#define 	 STRING_SET_COMMON_TOPIC_STYLE_EX		0x1424
#define 	 STRING_SET_COMMON_BACKLIGHT_VALUE_EX		0x1425
#define 	 STRING_SET_COMMON_BACKLIGHT_ON_OFF_EX		0x1426
#define 	 STRING_SET_COMMON_TIMING_SHUTDOWN_EX		0x1427
#define 	 STRING_SET_1MINUTE		0x1428
#define 	 STRING_SET_2MINUTE		0x1429
#define 	 STRING_SET_TIMING_TO_CLOSE_SCREEN_EX		0x142a
#define 	 STRING_SET_TIMING_CLOSE_SCREEN		0x142b
#define 	 STRING_SET_LCD_BRIGHT		0x142c
#define 	 STRING_SET_LCD_CONSTRACT		0x142d
#define 	 STRING_SET_LCD_SATURATION		0x142e
#define 	 STRING_SET_LCD_ACUTANCE		0x142f
#define 	 STRING_SET_LCD_DEFINITION		0x1430
#define 	 STRING_SET_COMMON_LCD_BRIGHT_EX		0x1431
#define 	 STRING_SET_COMMON_LCD_CONSTRACT_EX		0x1432
#define 	 STRING_SET_COMMON_LCD_SATURATION_EX		0x1433
#define 	 STRING_SET_COMMON_LCD_ACUTANCE_EX		0x1434
#define 	 STRING_SET_COMMON_LCD_DEFINITION_EX		0x1435
#define 	 STRING_SET_LCD_0_PERCENT		0x1436
#define 	 STRING_SET_LCD_1_PERCENT		0x1437
#define 	 STRING_SET_LCD_2_PERCENT		0x1438
#define 	 STRING_SET_LCD_3_PERCENT		0x1439
#define 	 STRING_SET_LCD_4_PERCENT		0x143a
#define 	 STRING_SET_LCD_5_PERCENT		0x143b
#define 	 STRING_SET_LCD_6_PERCENT		0x143c
#define 	 STRING_SET_LCD_7_PERCENT		0x143d
#define 	 STRING_SET_LCD_8_PERCENT		0x143e
#define 	 STRING_SET_LCD_9_PERCENT		0x143f
#define 	 STRING_SET_LCD_10_PERCENT		0x1440
#define 	 STRING_SET_15SECOND		0x1441
#define 	 STRING_SET_45SECOND		0x1442
#define 	 STRING_SET_60SECOND		0x1443
#define 	 STRING_SETTING_HARD_VERSION		0x1444
#define 	 STRING_SETTING_MANUFACTURER_ID		0x1445
#define 	 STRING_FM_FM		0x1771
#define 	 STRING_FM_AM		0x1772
#define 	 STRING_FM_AUTO_SEEK		0x1773
#define 	 STRING_FM_AUTO_STORE		0x1774
#define 	 STRING_FM_MANUAL_SEEK		0x1775
#define 	 STRING_FM_MANUAL_STORE		0x1776
#define 	 STRING_FM_PTY_SEEK		0x1777
#define 	 STRING_FM_PREVIEW		0x1778
#define 	 STRING_FM_TUNER		0x1779
#define 	 STRING_FM_PTY_LIST		0x177a
#define 	 STRING_FM_MANUAL		0x177b
#define 	 STRING_FM_AUTO		0x177c
#define 	 STRING_FM_STORE		0x177d
#define 	 STRING_FM_LOCAL		0x177e
#define 	 STRING_FM_SEEK		0x177f
#define 	 STRING_FM_AREA		0x1780
#define 	 STRING_FM_RDS		0x1781
#define 	 STRING_FM_RDS_BRACKET		0x1782
#define 	 STRING_FM_RDS_PTY		0x1783
#define 	 STRING_FM_RDS_AF		0x1784
#define 	 STRING_FM_RDS_CT		0x1785
#define 	 STRING_FM_RDS_TA		0x1786
#define 	 STRING_FM_EUROPE		0x1787
#define 	 STRING_FM_EUROPE2		0x1788
#define 	 STRING_FM_S_AMERICA		0x1789
#define 	 STRING_FM_USA		0x178a
#define 	 STRING_FM_OTHERS		0x178b
#define 	 STRING_FM_OIRT		0x178c
#define 	 STRING_FM_LATIN		0x178d
#define 	 STRING_FM_ASIA		0x178e
#define 	 STRING_FM_JAPAN		0x178f
#define 	 STRING_FM_MID_EAST		0x1790
#define 	 STRING_FM_RUSSIA		0x1791
#define 	 STRING_FM_AUS		0x1792
#define 	 STRING_FM_ON		0x1793
#define 	 STRING_FM_OFF		0x1794
#define 	 STRING_FM_RDS_PTY_NONE		0x1795
#define 	 STRING_FM_RDS_PTY_NEWS		0x1796
#define 	 STRING_FM_RDS_PTY_AFFAIRS		0x1797
#define 	 STRING_FM_RDS_PTY_INFO		0x1798
#define 	 STRING_FM_RDS_PTY_SPORT		0x1799
#define 	 STRING_FM_RDS_PTY_EDUCATE		0x179a
#define 	 STRING_FM_RDS_PTY_DRAMA		0x179b
#define 	 STRING_FM_RDS_PTY_CULTURE		0x179c
#define 	 STRING_FM_RDS_PTY_SCIENCE		0x179d
#define 	 STRING_FM_RDS_PTY_VARIED		0x179e
#define 	 STRING_FM_RDS_PTY_POPM		0x179f
#define 	 STRING_FM_RDS_PTY_ROCKM		0x17a0
#define 	 STRING_FM_RDS_PTY_EASYM		0x17a1
#define 	 STRING_FM_RDS_PTY_LIGHTM		0x17a2
#define 	 STRING_FM_RDS_PTY_CLASSICS		0x17a3
#define 	 STRING_FM_RDS_PTY_OTHERM		0x17a4
#define 	 STRING_FM_RDS_PTY_WEATHER		0x17a5
#define 	 STRING_FM_RDS_PTY_FINANCE		0x17a6
#define 	 STRING_FM_RDS_PTY_CHILDREN		0x17a7
#define 	 STRING_FM_RDS_PTY_SOCIAL		0x17a8
#define 	 STRING_FM_RDS_PTY_RELIGION		0x17a9
#define 	 STRING_FM_RDS_PTY_PHONEIN		0x17aa
#define 	 STRING_FM_RDS_PTY_TRAVEL		0x17ab
#define 	 STRING_FM_RDS_PTY_LEISURE		0x17ac
#define 	 STRING_FM_RDS_PTY_JAZZ		0x17ad
#define 	 STRING_FM_RDS_PTY_COUNTRY		0x17ae
#define 	 STRING_FM_RDS_PTY_NATIONM		0x17af
#define 	 STRING_FM_RDS_PTY_OLDIES		0x17b0
#define 	 STRING_FM_RDS_PTY_FOLKM		0x17b1
#define 	 STRING_FM_RDS_PTY_DOCUMENT		0x17b2
#define 	 STRING_FM_RDS_PTY_TEST		0x17b3
#define 	 STRING_FM_RDS_PTY_ALARM		0x17b4
#define 	 STRING_FM_RDS_PTY_ALARM_ALARM		0x17b5
#define 	 STRING_FM_RDS_PTY_NO_MATCH		0x17b6
#define 	 STRING_FM_TRAFFIC		0x17b7
#define 	 STRING_FM_FUNTION_LOCAL_SEEK		0x17b8
#define 	 STRING_FM_FUNTION_AREA		0x17b9
#define 	 STRING_FM_FUNTION_RDS		0x17ba
#define 	 STRING_FM_FUNTION_AUTO_STORE		0x17bb
#define 	 STRING_FM_SWITCH_BAND_TIPS		0x17bc
#define 	 STRING_FM_NORTH_AMERICAN		0x17bd
#define 	 STRING_FM_OCEANCIA		0x17be
#define 	 STRING_FM_HONG_KONG		0x17bf
#define 	 STRING_FM_RBDS_PTY_NEWS		0x17c0
#define 	 STRING_FM_RBDS_PTY_INFO		0x17c1
#define 	 STRING_FM_RBDS_PTY_SPORTS		0x17c2
#define 	 STRING_FM_RBDS_PTY_TALK		0x17c3
#define 	 STRING_FM_RBDS_PTY_ROCK		0x17c4
#define 	 STRING_FM_RBDS_PTY_CLASSIC_ROCK		0x17c5
#define 	 STRING_FM_RBDS_PTY_ADULT_HITS		0x17c6
#define 	 STRING_FM_RBDS_PTY_SOFT_ROCK		0x17c7
#define 	 STRING_FM_RBDS_PTY_TOP_40		0x17c8
#define 	 STRING_FM_RBDS_PTY_COUNTRY		0x17c9
#define 	 STRING_FM_RBDS_PTY_OLDIES		0x17ca
#define 	 STRING_FM_RBDS_PTY_SOFT		0x17cb
#define 	 STRING_FM_RBDS_PTY_NOSTALGIA		0x17cc
#define 	 STRING_FM_RBDS_PTY_JAZZ		0x17cd
#define 	 STRING_FM_RBDS_PTY_CLASSICAL		0x17ce
#define 	 STRING_FM_RBDS_PTY_R_N_B		0x17cf
#define 	 STRING_FM_RBDS_PTY_SOFT_R_N_B		0x17d0
#define 	 STRING_FM_RBDS_PTY_FOREIGN_LANG		0x17d1
#define 	 STRING_FM_RBDS_PTY_RELIFIOUS_M		0x17d2
#define 	 STRING_FM_RBDS_PTY_RELIFIOUS_T		0x17d3
#define 	 STRING_FM_RBDS_PTY_PERSONALITY		0x17d4
#define 	 STRING_FM_RBDS_PTY_PUBLIC		0x17d5
#define 	 STRING_FM_RBDS_PTY_COLLEGE		0x17d6
#define 	 STRING_FM_RBDS_PTY_HABLAR_ESPANOL		0x17d7
#define 	 STRING_FM_RBDS_PTY_MUSICA_ESPANOL		0x17d8
#define 	 STRING_FM_RBDS_PTY_HIP_HOP		0x17d9
#define 	 STRING_FM_RBDS_PTY_WEATHER		0x17da
#define 	 STRING_FM_RBDS_PTY_EMERGENCY_TEST		0x17db
#define 	 STRING_FM_RBDS_PTY_ALARM		0x17dc
#define 	 STRING_FM_RBDS_CT		0x17dd
#define 	 STRING_DIALOG_OK		0x1b59
#define 	 STRING_DIALOG_YES		0x1b5a
#define 	 STRING_DIALOG_CANCEL		0x1b5b
#define 	 STRING_DIALOG_NO		0x1b5c
#define 	 STRING_DIALOG_RETRY		0x1b5d
#define 	 STRING_DIALOG_ABORT		0x1b5e
#define 	 STRING_DIALOG_IGNORE		0x1b5f
#define 	 STRING_DIALOG_DIALOG		0x1b60
#define 	 STRING_FW_UPDATE_CONFIRM		0x1b61
#define 	 STRING_C100_FW_UPDATE_CONFIRM		0x1b62
#define 	 STRING_MCU_FW_UPDATE_CONTIRM		0x1b63
#define 	 STRING_FW_UPDATE		0x1b64
#define 	 STRING_DONOT_POWEROFF		0x1b65
#define 	 STRING_FW_UPDATE_ERR		0x1b66
#define 	 STRING_FW_UPDATE_OK		0x1b67
#define 	 STRING_FW_UPDATE_SYS_RESET		0x1b68
#define 	 STRING_FW_UPDATE_AUTO_POWEROFF		0x1b69
#define 	 STRING_DIALOG_TIPS		0x1b6a
#define 	 STRING_DVR_VIDEO_LOCK		0x1b6b
#define 	 STRING_APP_AVS_LISTENING		0x1b6c
#define 	 STRING_APP_AVS_THINKING		0x1b6d
#define 	 STRING_APP_AVS_SPEAKING		0x1b6e
#define 	 STRING_INTO_APP_UPGRADE_TIPS		0x1b6f
#define 	 STRING_APP_CHANGING_WALLPAPER		0x1b70
#define 	 STRING_APP_CHANGE_WALLPAPER_SUCCEED		0x1b71
#define 	 STRING_APP_CHANGE_WALLPAPER_FAIL		0x1b72
#define 	 STRING_DISABLE_APPUP_TIPS		0x1b73
#define 	 STRING_DISABLE_MIRRORAPP_TIPS		0x1b74
#define 	 STRING_ALEXA_CONNECTED		0x1b75
#define 	 STRING_ALEXA_DISCONNECT		0x1b76
#define 	 STRING_ALEXA_AMA_STARTING		0x1b77
#define 	 STRING_ALEXA_AMA_LISTENING		0x1b78
#define 	 STRING_ALEXA_AMA_PROCESSING		0x1b79
#define 	 STRING_ALEXA_AMA_SPEAKING		0x1b7a
#define 	 STRING_BLE_CONNECTED		0x1b7b
#define 	 STRING_BLE_DISCONNECTED		0x1b7c
#define 	 STRING_BLE_CONNECT_AFFIRM		0x1b7d
#define 	 STRING_BLE_CONNECT_CANCEL		0x1b7e
#define 	 STRING_BLE_CONNECT_CONNECTED		0x1b7f
#define 	 STRING_BLE_CONNECT_DISCONNECTED		0x1b80
#define 	 STRING_DIALOG_DEVICE_LIST		0x1b81
#define 	 STRING_DIALOG_CONNECT_BLUETOOTH		0x1b82
#define 	 STRING_DIALOG_CONNECT_BT_TIP		0x1b83
#define 	 STRING_DIALOG_BT_VISIBILITY_TIP		0x1b84
#define 	 STRING_BT_CONNECT_REQUIRE		0x1b85
#define 	 STRING_MIRROR_PAIR_BT_TITLE		0x1b86
#define 	 STRING_MIRROR_PAIR_BT_CONTENT		0x1b87
#define 	 STRING_DEVICE_LIST		0x1b88
#define 	 STRING_BT_CONNECT_REQUIRE		0x1b89
#define 	 STRING_CONNECTION		0x1b8a
#define 	 STRING_CONNECTTING		0x1b8b
#define 	 STRING_CONNECTED		0x1b8c
#define 	 STRING_CLOSE		0x1b8d
#define 	 STRING_SEARCH		0x1b8e
#define 	 STRING_PAIR		0x1b8f
#define 	 STRING_PIN_CODE_PAIR_TIP		0x1b90
#define 	 STRING_BT_SEARCH_TIP		0x1b91
#define 	 STRING_CUR_MIRROR_ERROR		0x1b92
#define 	 STRING_NOT_CONNECTED		0x1b93
#define 	 STRING_CONNECT_THE_SMARTPHONE		0x1b94
#define 	 STRING_NO_DEVICE		0x1b95
#define 	 STRING_CANCELED		0x1b96
#define 	 STRING_DIALOG_PARENTAL_CODE		0x1b97
#define 	 STRING_DIALOG_PARENTAL_CODE_1		0x1b98
#define 	 STRING_DIALOG_PARENTAL_CODE_ERROR		0x1b99
#define 	 STRING_DIALOG_PARENTAL_NOT_MATCH		0x1b9a
#define 	 STRING_DIALOG_SXM_RESET		0x1b9b
#define 	 STRING_LOADING		0x1f41
#define 	 STRING_ALARM_EXIT		0x2329
#define 	 STRING_ALARM_DELAY		0x232a
#define 	 STRING_ALARM_RUNNING		0x232b
#define 	 STRING_ALARM_DELAYING		0x232c
#define 	 STRING_ROOT_NULL_DISK		0x2711
#define 	 STRING_ROOT_CUE		0x2712
#define 	 STRING_ROOT_NO_AUDIO_FILE		0x2713
#define 	 STRING_ROOT_NO_VIDEO_FILE		0x2714
#define 	 STRING_ROOT_SD_PLUGIN		0x2715
#define 	 STRING_ROOT_UD_PLUGIN		0x2716
#define 	 STRING_ROOT_NO_FILE_IN_SD		0x2717
#define 	 STRING_ROOT_NO_FILE_IN_USB		0x2718
#define 	 STRING_ROOT_NO_ENY_STORAGE		0x2719
#define 	 STRING_ROOT_NO_SD		0x271a
#define 	 STRING_ROOT_NO_USB		0x271b
#define 	 STRING_ROOT_NO_ENY_AUDIO_FILE		0x271c
#define 	 STRING_ROOT_NO_AUDIO_FILE_IN_SD		0x271d
#define 	 STRING_ROOT_NO_AUDIO_FILE_IN_USB		0x271e
#define 	 STRING_ROOT_SWITCH_TO_SD_AUDIO		0x271f
#define 	 STRING_ROOT_SWITCH_TO_USB_AUDIO		0x2720
#define 	 STRING_ROOT_SD_AUDIO		0x2721
#define 	 STRING_ROOT_USB_AUDIO		0x2722
#define 	 STRING_ROOT_NO_ENY_VIDEO_FILE		0x2723
#define 	 STRING_ROOT_NO_VIDEO_FILE_IN_SD		0x2724
#define 	 STRING_ROOT_NO_VIDEO_FILE_IN_USB		0x2725
#define 	 STRING_ROOT_SWITCH_TO_SD_VIDEO		0x2726
#define 	 STRING_ROOT_SWITCH_TO_USB_VIDEO		0x2727
#define 	 STRING_ROOT_SD_VIDEO		0x2728
#define 	 STRING_ROOT_USB_VIDEO		0x2729
#define 	 STRING_ROOT_NO_ENY_EBOOK_FILE		0x272a
#define 	 STRING_ROOT_NO_EBOOK_FILE_IN_SD		0x272b
#define 	 STRING_ROOT_NO_EBOOK_FILE_IN_USB		0x272c
#define 	 STRING_ROOT_SWITCH_TO_SD_EBOOK		0x272d
#define 	 STRING_ROOT_SWITCH_TO_USB_EBOOK		0x272e
#define 	 STRING_ROOT_SD_EBOOK		0x272f
#define 	 STRING_ROOT_USB_EBOOK		0x2730
#define 	 STRING_ROOT_NO_ENY_PHOTO_FILE		0x2731
#define 	 STRING_ROOT_NO_PHOTO_FILE_IN_SD		0x2732
#define 	 STRING_ROOT_NO_PHOTO_FILE_IN_USB		0x2733
#define 	 STRING_ROOT_SWITCH_TO_SD_PHOTO		0x2734
#define 	 STRING_ROOT_SWITCH_TO_USB_PHOTO		0x2735
#define 	 STRING_ROOT_SD_PHOTO		0x2736
#define 	 STRING_ROOT_USB_PHOTO		0x2737
#define 	 STRING_ROOT_LINEIN_NOT_INSERT		0x2738
#define 	 STRING_ROOT_EXIT		0x2739
#define 	 STRING_ROOT_SD_KARAOKE		0x273a
#define 	 STRING_ROOT_USB_KARAOKE		0x273b
#define 	 STRING_ROOT_SWITCH_TO_SD_KARAOKE		0x273c
#define 	 STRING_ROOT_SWITCH_TO_USB_KARAOKE		0x273d
#define 	 STRING_ROOT_NO_KAK_FILE_IN_SD		0x273e
#define 	 STRING_ROOT_NO_KAK_FILE_IN_USB		0x273f
#define 	 STRING_ROOT_NO_ENY_KAK_FILE		0x2740
#define 	 STRING_ROOT_KARAOKE_HAVE_RUN		0x2741
#define 	 STRING_ROOT_KARAOKE_DATA_ERROR		0x2742
#define 	 STRING_ROOT_SD_KARAOKE_DATA_ERROR		0x2743
#define 	 STRING_ROOT_USB_KARAOKE_DATA_ERROR		0x2744
#define 	 STRING_ROOT_MUTE		0x2745
#define 	 STRING_ROOT_NOT_MUTE		0x2746
#define 	 STRING_ROOT_SWITCH_TO_NTSC		0x2747
#define 	 STRING_ROOT_SWITCH_TO_PAL		0x2748
#define 	 STRING_ROOT_SWITCH_TO_TV		0x2749
#define 	 STRING_ROOT_SWITCH_TO_FM		0x274a
#define 	 STRING_ROOT_DISK_PLUG_OUT		0x274b
#define 	 STRING_ROOT_TID_NOT_SUPPORT_HUB		0x274c
#define 	 STRING_ROOT_TID_UNSUPPORTED_DEVICE		0x274d
#define 	 STRING_ROOT_TID_DEVICE_NO_RESPONSE		0x274e
#define 	 STRING_ROOT_AUDIO_GENERAL		0x274f
#define 	 STRING_ROOT_AUDIO_ZONE		0x2750
#define 	 STRING_ROOT_AUDIO_GENERAL_DEFEAT		0x2751
#define 	 STRING_ROOT_AUDIO_GENERAL_VOL_SYNC		0x2752
#define 	 STRING_ROOT_AUDIO_GENERAL_VOL_SYNC_ALL		0x2753
#define 	 STRING_ROOT_AUDIO_GENERAL_VOL_SYNC_ZONE1		0x2754
#define 	 STRING_ROOT_AUDIO_GENERAL_VOL_SYNC_ZONE2		0x2755
#define 	 STRING_ROOT_AUDIO_GENERAL_VOL_RATIOS		0x2756
#define 	 STRING_ROOT_AUDIO_GENERAL_VOLUME_OFFSET		0x2757
#define 	 STRING_ROOT_AUDIO_GENERAL_SUBWOOFER		0x2758
#define 	 STRING_ROOT_AUDIO_GENERAL_PHASE		0x2759
#define 	 STRING_ROOT_AUDIO_GENERAL_RESET		0x275a
#define 	 STRING_ROOT_AUDIO_ZONE_1		0x275b
#define 	 STRING_ROOT_AUDIO_ZONE_2		0x275c
#define 	 STRING_ROOT_AUDIO_ZONE_BANE_EQ		0x275d
#define 	 STRING_ROOT_AUDIO_ZONE_FADER_BALANCE		0x275e
#define 	 STRING_ROOT_AUDIO_ZONE_CROSSOVER		0x275f
#define 	 STRING_ROOT_AUDIO_ZONE_AUDIO_ZONES		0x2760
#define 	 STRING_ROOT_AUDIO_ZONE_PUNCH_EQ		0x2761
#define 	 STRING_ROOT_AUDIO_ZONE_LOUDNESS		0x2762
#define 	 STRING_ROOT_AUDIO_ZONE_AUX_INPUT		0x2763
#define 	 STRING_ROOT_AUDIO_ZONE_SOURCE_GAIN		0x2764
#define 	 STRING_ROOT_AUDIO_ZONE_ASSIGN		0x2765
#define 	 STRING_ROOT_BROADCAST		0x2766
#define 	 STRING_ROOT_RECEIVER		0x2767
#define 	 STRING_ROOT_SOURCE		0x2768
#define 	 STRING_ROOT_DEVICE_NAME		0x2769
#define 	 STRING_ROOT_JUMP_TO_LIVE		0x276a
#define 	 STRING_ROOT_CATEGORY		0x276b
#define 	 STRING_ROOT_DIRECT_TUNING		0x276c
#define 	 STRING_ROOT_PARENTAL_CONTROL		0x276d
#define 	 STRING_ROOT_RESET		0x276e
#define 	 STRING_ROOT_PARENTAL_CONTROLS		0x276f
#define 	 STRING_ROOT_LOCK_CHANNELS		0x2770
#define 	 STRING_ROOT_CHANGE_MY_CODE		0x2771
#define 	 STRING_ROOT_RENAME		0x2772
#define 	 STRING_ROOT_ZONE1_AUDIO		0x2773
#define 	 STRING_ROOT_ZONE2_AUDIO		0x2774
#define 	 STRING_ROOT_VOLTAGE_GAUGE		0x2775
#define 	 STRING_ROOT_LANGUAGE		0x2776
#define 	 STRING_ROOT_RADIO_GEGION		0x2777
#define 	 STRING_ROOT_BRIGHTNESS		0x2778
#define 	 STRING_ROOT_THEME		0x2779
#define 	 STRING_ROOT_FACTORY_DEFAULT		0x277a
#define 	 STRING_ROOT_AUTO_BRIGHTNESS		0x277b
#define 	 STRING_ROOT_BEEP		0x277c
#define 	 STRING_ROOT_AUX_IN		0x277d
#define 	 STRING_ROOT_BLUETOOTH_INFO		0x277e
#define 	 STRING_ROOT_SYSTEM_INFO		0x277f
#define 	 STRING_ROOT_SYSTEM_FW_UPDATE		0x2780
#define 	 STRING_ROOT_FW_UPDATE_VIA_USB		0x2781
#define 	 STRING_ROOT_FW_UPDATE_OTA		0x2782
#define 	 STRING_ROOT_BLUETOOTH		0x2783
#define 	 STRING_ROOT_BLUETOOTH_NAME		0x2784
#define 	 STRING_ROOT_BLUETOOTH_ADD		0x2785
#define 	 STRING_ROOT_CUSTOM		0x2786
#define 	 STRING_ROOT_KOREAN		0x2787
#define 	 STRING_ROOT_JAPANESE		0x2788
#define 	 STRING_ROOT_USA		0x2789
#define 	 STRING_ROOT_EUROPE		0x278a
#define 	 STRING_ROOT_JAPAN		0x278b
#define 	 STRING_ROOT_AUSTRALASIA		0x278c
#define 	 STRING_ROOT_DAY		0x278d
#define 	 STRING_ROOT_NIGHT		0x278e
#define 	 STRING_ROOT_TYPE		0x278f
#define 	 STRING_ROOT_FREQ		0x2790
#define 	 STRING_ROOT_SLOPE		0x2791
#define 	 STRING_ROOT_MODE		0x2792
#define 	 STRING_ROOT_RESET_AUDIO_SETTINGS		0x2793
#define 	 STRING_ROOT_CHANGE_BROADCAST_MODE		0x2794
#define 	 STRING_ROOT_CHANGE_RECEIVER_MODE		0x2795
#define 	 STRING_ROOT_DELETE_DEVICE		0x2796
#define 	 STRING_LINE_IN_EXTERNAL_SOUND		0x2af9
#define 	 STRING_LINE_IN_TITLE		0x2afa
#define 	 STRING_SMENU_MENU_MENU		0x2ee1
#define 	 STRING_SMENU_MENU_AUDIO		0x2ee2
#define 	 STRING_SMENU_MENU_SYSTEM		0x2ee3
#define 	 STRING_SMENU_MENU_EQ		0x2ee4
#define 	 STRING_SMENU_MENU_AUDIO_BASS		0x2ee5
#define 	 STRING_SMENU_MENU_AUDIO_TREBLE		0x2ee6
#define 	 STRING_SMENU_MENU_AUDIO_BALANCE		0x2ee7
#define 	 STRING_SMENU_MENU_AUDIO_FADER		0x2ee8
#define 	 STRING_SMENU_MENU_TUNER_AREA		0x2ee9
#define 	 STRING_SMENU_MENU_ON		0x2eea
#define 	 STRING_SMENU_MENU_OFF		0x2eeb
#define 	 STRING_SMENU_MENU_SYSTEM_BEEP		0x2eec
#define 	 STRING_SMENU_MENU_SYSTEM_TIME		0x2eed
#define 	 STRING_SMENU_MENU_SYSTEM_RESET		0x2eee
#define 	 STRING_SMENU_MENU_SYSTEM_DEMO		0x2eef
#define 	 STRING_SMENU_MENU_SYSTEM_LANGUAGE		0x2ef0
#define 	 STRING_SMENU_MENU_SYSTEM_LAN_CHINESE		0x2ef1
#define 	 STRING_SMENU_MENU_SYSTEM_LAN_ENGLISH		0x2ef2
#define 	 STRING_SMENU_MENU_SYSTEM_LAN_SPANISH		0x2ef3
#define 	 STRING_SMENU_MENU_SYSTEM_LAN_FRENCH		0x2ef4
#define 	 STRING_SMENU_MENU_SYSTEM_LAN_GERMAN		0x2ef5
#define 	 STRING_SMENU_MENU_SYSTEM_LAN_ITALIAN		0x2ef6
#define 	 STRING_SMENU_MENU_SYSTEM_LAN_DUTCH		0x2ef7
#define 	 STRING_SMENU_MENU_SYSTEM_LAN_FINNISH		0x2ef8
#define 	 STRING_SMENU_MENU_SYSTEM_LAN_RUSSIAN		0x2ef9
#define 	 STRING_SMENU_MENU_SYSTEM_LAN_PORTUGUESE		0x2efa
#define 	 STRING_SMENU_MENU_SYSTEM_LAN_POLISH		0x2efb
#define 	 STRING_SMENU_MENU_SYSTEM_LAN_CZECH		0x2efc
#define 	 STRING_SMENU_MENU_SYSTEM_LAN_HUNGARIAN		0x2efd
#define 	 STRING_SMENU_MENU_EQ_OFF		0x2efe
#define 	 STRING_SMENU_MENU_EQ_POP		0x2eff
#define 	 STRING_SMENU_MENU_EQ_TECHNO		0x2f00
#define 	 STRING_SMENU_MENU_EQ_CLASSIC		0x2f01
#define 	 STRING_SMENU_MENU_EQ_JAZZ		0x2f02
#define 	 STRING_SMENU_MENU_EQ_EASY		0x2f03
#define 	 STRING_SMENU_MENU_EQ_POWERFUL		0x2f04
#define 	 STRING_SMENU_MENU_EQ_TOP40		0x2f05
#define 	 STRING_SMENU_MENU_EQ_FLAT		0x2f06
#define 	 STRING_SMENU_MENU_EQ_OPTIMAL		0x2f07
#define 	 STRING_SMENU_MENU_EQ_VOCAL		0x2f08
#define 	 STRING_SMENU_MENU_EQ_ACOUSTIC		0x2f09
#define 	 STRING_SMENU_MENU_EQ_ELECTRONIC		0x2f0a
#define 	 STRING_SMENU_MENU_EQ_HIPHOP		0x2f0b
#define 	 STRING_SMENU_MENU_EQ_DANCE		0x2f0c
#define 	 STRING_SMENU_MENU_EQ_ROCK		0x2f0d
#define 	 STRING_SMENU_BT_STATUS_PAIRED_OK		0x2f0e
#define 	 STRING_SMENU_BT_STATUS_PAIRED_FAILED		0x2f0f
#define 	 STRING_SMENU_BT_STATUS_CONNECTED_OK		0x2f10
#define 	 STRING_SMENU_BT_STATUS_CONNECTED_FAILED		0x2f11
#define 	 STRING_SMENU_BT_STATUS_DISCONNECTED_OK		0x2f12
#define 	 STRING_SMENU_BT_STATUS_DISCONNECTED_FAILED		0x2f13
#define 	 STRING_SMENU_BT_PHONE_STATUS_CALLINGIN		0x2f14
#define 	 STRING_SMENU_BT_PHONE_STATUS_CALLINGOUT		0x2f15
#define 	 STRING_SMENU_BT_PHONE_STATUS_TALKING		0x2f16
#define 	 STRING_SMENU_BT_PHONE_STATUS_CALLEND		0x2f17
#define 	 STRING_SMENU_CLOCK_MODE		0x2f18
#define 	 STRING_SMENU_CLOCK_24HR		0x2f19
#define 	 STRING_SMENU_CLOCK_12HR		0x2f1a
#define 	 STRING_SMENU_DEVICE_NAME		0x2f1b
#define 	 STRING_SMENU_SETUP_INFORMATION		0x2f1c
#define 	 STRING_SMENU_BT_STATUS_CONNECTED		0x2f1d
#define 	 STRING_SMENU_BT_STATUS_DISCONNECTED		0x2f1e
#define 	 STRING_SMENU_BT_STATUS_CONNECTEDING		0x2f1f
#define 	 STRING_SMENU_BT_STATUS_DISCONNECTING		0x2f20
#define 	 STRING_SMENU_SYSTEM_RESET		0x2f21
#define 	 STRING_SMENU_PHONE_AUTO_ANSWER		0x2f22
#define 	 STRING_SMENU_PARKING_LINE_DRIVE_TIPS		0x2f23
#define 	 STRING_SMENU_PARKING_LINE_ON_OFF		0x2f24
#define 	 STRING_SMENU_PARKING_LINE		0x2f25
#define 	 STRING_SMENU_PARKING_LINE_INITIALIZE_TIPS		0x2f26
#define 	 STRING_SMENU_SWC		0x2f27
#define 	 STRING_SMENU_PARKING		0x2f28
#define 	 STRING_SMENU_PARKING_OFF		0x2f29
#define 	 STRING_SMENU_BT_VERSION		0x2f2a
#define 	 STRING_SMENU_MPG_VERSION		0x2f2b
#define 	 STRING_SMENU_MCU_VERSION		0x2f2c
#define 	 STRING_SMENU_BLUETOOTH		0x2f2d
#define 	 STRING_SMENU_SCREEN		0x2f2e
#define 	 STRING_SMENU_FACTORY		0x2f2f
#define 	 STRING_SMENU_BT_SELECT_DEVICE		0x2f30
#define 	 STRING_SMENU_BT_DEVICE_ADDRESS		0x2f31
#define 	 STRING_SMENU_DIMMER		0x2f32
#define 	 STRING_SMENU_WALLPAPER		0x2f33
#define 	 STRING_SMENU_DVR_ON_OFF		0x2f34
#define 	 STRING_SMENU_CAMERA_IN		0x2f35
#define 	 STRING_SMENU_PIN_CODE		0x2f36
#define 	 STRING_SMENU_LOW		0x2f37
#define 	 STRING_SMENU_MID		0x2f38
#define 	 STRING_SMENU_HIGH		0x2f39
#define 	 STRING_SMENU_SWC_SELECT_KEY		0x2f3a
#define 	 STRING_SMENU_SWC_LONG_PRESS		0x2f3b
#define 	 STRING_SMENU_SWC_LEARNNING		0x2f3c
#define 	 STRING_SMENU_SWC_LEARN_OK		0x2f3d
#define 	 STRING_SMENU_SWC_CANCELING		0x2f3e
#define 	 STRING_SMENU_SWC_CANCEL_OK		0x2f3f
#define 	 STRING_SMENU_SWC_INITIALIZING		0x2f40
#define 	 STRING_SMENU_SWC_INITIALIZ_OK		0x2f41
#define 	 STRING_SMENU_REAR_DVR		0x2f42
#define 	 STRING_SMENU_CAMERA		0x2f43
#define 	 STRING_SMENU_SETTING		0x2f44
#define 	 STRING_SMENU_SWC_INIT_STRING		0x2f45
#define 	 STRING_SMENU_SECURITY_TITLE		0x2f46
#define 	 STRING_SMENU_SECURITY_USER_UPDATE_LOGO		0x2f47
#define 	 STRING_SMENU_SECURITY_RESTORE_LOGO		0x2f48
#define 	 STRING_SMENU_SECURITY_RESTORE_LOGO_TIPS		0x2f49
#define 	 STRING_SMENU_SECURITY_NO_NEED_RESTORE		0x2f4a
#define 	 STRING_SMENU_SPLASH_SELECT_LOGO_DIALOG		0x2f4b
#define 	 STRING_SMENU_SPLASH_SELECT_LOGO_OK		0x2f4c
#define 	 STRING_SMENU_SPLASH_SELECT_LOGO_FAIL		0x2f4d
#define 	 STRING_SMENU_MODEL_NO		0x2f4e
#define 	 STRING_SMENU_CLOCK_MONTH_JAN		0x2f4f
#define 	 STRING_SMENU_CLOCK_MONTH_FEB		0x2f50
#define 	 STRING_SMENU_CLOCK_MONTH_MAR		0x2f51
#define 	 STRING_SMENU_CLOCK_MONTH_APR		0x2f52
#define 	 STRING_SMENU_CLOCK_MONTH_MAY		0x2f53
#define 	 STRING_SMENU_CLOCK_MONTH_JUN		0x2f54
#define 	 STRING_SMENU_CLOCK_MONTH_JUL		0x2f55
#define 	 STRING_SMENU_CLOCK_MONTH_AUG		0x2f56
#define 	 STRING_SMENU_CLOCK_MONTH_SEPT		0x2f57
#define 	 STRING_SMENU_CLOCK_MONTH_OCT		0x2f58
#define 	 STRING_SMENU_CLOCK_MONTH_NOV		0x2f59
#define 	 STRING_SMENU_CLOCK_MONTH_DEC		0x2f5a
#define 	 STRING_SMENU_REAR_AV_IN		0x2f5b
#define 	 STRING_SMENU_BACKUP_CAMERA		0x2f5c
#define 	 STRING_SMENU_DVR_VOICE		0x2f5d
#define 	 STRING_SMENU_AV_IN		0x2f5e
#define 	 STRING_SMENU_REAR_CAMERA		0x2f5f
#define 	 STRING_SMENU_PARKING_MODE		0x2f60
#define 	 STRING_SMENU_SYSTEM_STEERING_LEARN		0x2f61
#define 	 STRING_SMENU_SYSTEM_PARKING_ASSIST_GUIDE		0x2f62
#define 	 STRING_SMENU_SYSTEM_PARKING_GUIDE_ADJUST		0x2f63
#define 	 STRING_SMENU_SYSTEM_FACTORY_DEFAULT		0x2f64
#define 	 STRING_SMENU_SYSTEM_ENABLE		0x2f65
#define 	 STRING_SMENU_SYSTEM_DISABLE		0x2f66
#define 	 STRING_SMENU_INIT_SETTING		0x2f67
#define 	 STRING_SMENU_INIT_FINISH		0x2f68
#define 	 STRING_SMENU_RESTORE_FACTORY_DEFAULT		0x2f69
#define 	 STRING_SMENU_NO_VIDEO		0x2f6a
#define 	 STRING_SMENU_PARKING_IS_ENABLED		0x2f6b
#define 	 STRING_SMENU_MENU_SYSTEM_LAN_VIETNAMESE		0x2f6c
#define 	 STRING_SMENU_SYSTEM_WALLPAPER		0x2f6d
#define 	 STRING_SMENU_SYSTEM_RESET_WALLPAPER		0x2f6e
#define 	 STRING_SMENU_MENU_SYSTEM_GLOBAL_VOICE		0x2f6f
#define 	 STRING_SMENU_SYSTEM_DEFAULT_VOLUME		0x2f70
#define 	 STRING_SMENU_MENU_SYSTEM_FACTORY_MODE		0x2f71
#define 	 STRING_SMENU_OPEN_LOGO_UPDATE		0x2f72
#define 	 STRING_SMENU_SWC_LEARN		0x2f73
#define 	 STRING_SMENU_MENU_INCORRECT_PASSWORD		0x2f74
#define 	 STRING_SMENU_ANDROID_ICON_TOUCH		0x2f75
#define 	 STRING_SMENU_CAMERA_MEDIA_AUDIO		0x2f76
#define 	 STRING_SMENU_CAMERA_ALWAY_ON		0x2f77
#define 	 STRING_SMENU_BLUETOOTH_MIC_LEVEL		0x2f78
#define 	 STRING_SMENU_MENU_MICROPHONE		0x2f79
#define 	 STRING_SMENU_MENU_MICROPHONE_INTERNAL		0x2f7a
#define 	 STRING_SMENU_MENU_MICROPHONE_EXTERNAL		0x2f7b
#define 	 STRING_SMENU_PHONE_MIRROR		0x2f7c
#define 	 STRING_SMENU_ANDROID_ICON_TOUCH		0x2f7d
#define 	 STRING_SMENU_BT_AUDIO_INTERRUPT		0x2f7e
#define 	 STRING_SMENU_BT_IF_THE_AUDIO		0x2f7f
#define 	 STRING_SMENU_THE_HEAD_UNIT		0x2f80
#define 	 STRING_SMENU_USE_CASE_1		0x2f81
#define 	 STRING_SMENU_A_SMASRT_PHONE		0x2f82
#define 	 STRING_SMENU_USE_CASE_2		0x2f83
#define 	 STRING_SMENU_WHEN_A_PLAYBACK		0x2f84
#define 	 STRING_SMENU_TIPS		0x2f85
#define 	 STRING_SMENU_IN_SOME_CASE		0x2f86
#define 	 STRING_SMENU_XING_1		0x2f87
#define 	 STRING_SMENU_XING_2		0x2f88
#define 	 STRING_SMENU_XING_3		0x2f89
#define 	 STRING_SMENU_TIPS1		0x2f8a
#define 	 STRING_SMENU_SYSTEM_FACT_MODE_DEFAULT		0x2f8b
#define 	 STRING_SMENU_SYSTEM_FACT_MODE_DEFAULT1		0x2f8c
#define 	 STRING_SMENU_SYSTEM_FACT_DEFAULT_OK		0x2f8d
#define 	 STRING_SMENU_SYSTEM_LOGO_RESTORE_OK		0x2f8e
#define 	 STRING_SMENU_SWC_LEARN_MODE		0x2f8f
#define 	 STRING_SMENU_SWC_LEARN_VALUE		0x2f90
#define 	 STRING_SMENU_PARKING_GUIDE_VALUE		0x2f91
#define 	 STRING_SMENU_PARKING_GUIDE		0x2f92
#define 	 STRING_SMENU_SFOTWARE_UPDATE		0x2f93
#define 	 STRING_SMENU_USB_DISCONNECTED		0x2f94
#define 	 STRING_SMENU_USB_CONNECTED		0x2f95
#define 	 STRING_SMENU_USB_UPDATE_TIP1		0x2f96
#define 	 STRING_SMENU_USB_UPDATE_TIP2		0x2f97
#define 	 STRING_SMENU_INSTALL_APPUP		0x2f98
#define 	 STRING_SMENU_HOW_INS_APPUP		0x2f99
#define 	 STRING_SMENU_INSTALL_METHOD1		0x2f9a
#define 	 STRING_SMENU_INSTALL_METHOD2		0x2f9b
#define 	 STRING_SMENU_INSTALL_TIP1		0x2f9c
#define 	 STRING_SMENU_INSTALL_TIP2		0x2f9d
#define 	 STRING_SMENU_EXIT_SW_DIELOG		0x2f9e
#define 	 STRING_SMENU_SW_FILES_RECE_TIP1		0x2f9f
#define 	 STRING_SMENU_SW_FILES_RECE_TIP2		0x2fa0
#define 	 STRING_SMENU_UPDATE_NOTE		0x2fa1
#define 	 STRING_SMENU_BRAND_NO		0x2fa2
#define 	 STRING_SMENU_SELECT_BLE		0x2fa3
#define 	 STRING_SMENU_SYSTEM_WALLPAPER_DEFAULT		0x2fa4
#define 	 STRING_SMENU_SYSTEM_WALLPAPER_RESTTING		0x2fa5
#define 	 STRING_SMENU_WALLPAPER_RESTTING_OK		0x2fa6
#define 	 STRING_SMENU_WALLPAPER_NO_NEED_RESTORE		0x2fa7
#define 	 STRING_SMENU_ANDRIOD_APP_INSTALLATION		0x2fa8
#define 	 STRING_SMENU_IPHONE_APP_INSTALLATION		0x2fa9
#define 	 STRING_SMENU_TUNER_PARA		0x2faa
#define 	 STRING_SMENU_TUNER_FAST_HIGH_CUT		0x2fab
#define 	 STRING_SMENU_TUNER_HIGH_CUT		0x2fac
#define 	 STRING_SMENU_TUNER_SOFT_MUTE		0x2fad
#define 	 STRING_SMENU_TUNER_MULTI_PATH		0x2fae
#define 	 STRING_SMENU_BEFORE_UPGRADE		0x2faf
#define 	 STRING_SMENU_BEFORE_UPGRADE_TIP		0x2fb0
#define 	 STRING_SMENU_UPDATE_START		0x2fb1
#define 	 STRING_SMENU_BLE_IS_CONNECTED		0x2fb2
#define 	 STRING_SMENU_SMARTPHONE_MIRROR		0x2fb3
#define 	 STRING_SMENU_UPDATE_INFO		0x2fb4
#define 	 STRING_SMENU_WIRELESS_DEVICE		0x2fb5
#define 	 STRING_SMENU_MIRRROR_INSTALL		0x2fb6
#define 	 STRING_SMENU_MIRRRORAPP		0x2fb7
#define 	 STRING_SMENU_INSTALL_MIRRRORAPP		0x2fb8
#define 	 STRING_SMENU_INSTALL_MIRRRORAPP_TIP		0x2fb9
#define 	 STRING_SMENU_ANDROID_PHONE		0x2fba
#define 	 STRING_SMENU_IPHONE		0x2fbb
#define 	 STRING_SMENU_SOFTWARE_INFO		0x2fbc
#define 	 STRING_SMENU_BLE_TIP1		0x2fbd
#define 	 STRING_SMENU_BLE_TIP2		0x2fbe
#define 	 STRING_SMENU_BLE_TIP3		0x2fbf
#define 	 STRING_SMENU_ALEXA_INSTALL_TIP1		0x2fc0
#define 	 STRING_SMENU_ALEXA_INSTALL_TIP2		0x2fc1
#define 	 STRING_SMENU_ALEXA_INSTALL_TIP3		0x2fc2
#define 	 STRING_SMENU_ALEXA_CONNECTION		0x2fc3
#define 	 STRING_SMENU_ALEXA_ENTER		0x2fc4
#define 	 STRING_SMENU_PAIR_DEVICE		0x2fc5
#define 	 STRING_SMENU_SEARCH_DEVICE		0x2fc6
#define 	 STRING_SMENU_SEARCHING		0x2fc7
#define 	 STRING_SMENU_CONNECTING		0x2fc8
#define 	 STRING_SMENU_BLE_DISCONNECT_TIP		0x2fc9
#define 	 STRING_SMENU_BLE_DISCONNECT		0x2fca
#define 	 STRING_SMENU_BLE_VERSION		0x2fcb
#define 	 STRING_SMENU_BLE_BATTERY_LOW_TIP		0x2fcc
#define 	 STRING_SMENU_WALLP_PLS_SEL_PHOTO_ONAPP		0x2fcd
#define 	 STRING_SMENU_WALLP_FROM_SMARTPHONE		0x2fce
#define 	 STRING_SMENU_WALLP_DEFAULT		0x2fcf
#define 	 STRING_SMENU_WALLP_TIP1		0x2fd0
#define 	 STRING_SMENU_WALLP_TIP2		0x2fd1
#define 	 STRING_SMENU_WALLP_INSTALL_MIRRORAPP		0x2fd2
#define 	 STRING_SMENU_WALLP_HOW_TO_SYNC_PHOTO		0x2fd3
#define 	 STRING_SMENU_WALLP_SYNC_PHOTO_STEP1		0x2fd4
#define 	 STRING_SMENU_WALLP_SYNC_PHOTO_TIP1		0x2fd5
#define 	 STRING_SMENU_WALLP_SYNC_PHOTO_STEP2		0x2fd6
#define 	 STRING_SMENU_WALLP_SYNC_PHOTO_TIP2		0x2fd7
#define 	 STRING_SMENU_WALLP_SYNC_PHOTO_STEP3		0x2fd8
#define 	 STRING_SMENU_WALLP_SYNC_PHOTO_TIP3		0x2fd9
#define 	 STRING_SMENU_WALLP_DELECT_PHOTO_TIP		0x2fda
#define 	 STRING_SMENU_WALLP_DELECT		0x2fdb
#define 	 STRING_SMENU_BLE_CONNECT_FAIL		0x2fdc
#define 	 STRING_SMENU_WALLP_SELECT_PHOTO_TIP		0x2fdd
#define 	 STRING_SMENU_WALLP_SELECT_PHOTO_HOW		0x2fde
#define 	 STRING_SMENU_MENU_SYSTEM_LAN_CHINESET		0x2fdf
#define 	 STRING_SMENU_CAUTION_TIP		0x2fe0
#define 	 STRING_SMENU_AGREE		0x2fe1
#define 	 STRING_SMENU_CAUTION		0x2fe2
#define 	 STRING_SMENU_BLUETOOTH_MIC_LEVEL		0x2fe3
#define 	 STRING_SMENU_AV_OUT_FORMAT		0x2fe4
#define 	 STRING_SMENU_BT_IS_CONNECTED		0x2fe5
#define 	 STRING_SMENU_AHD_FRONT		0x2fe6
#define 	 STRING_SMENU_AHD_REAR		0x2fe7
#define 	 STRING_SMENU_AHD_CAMERA		0x2fe8
#define 	 STRING_SMENU_BT_CONNECT_FULL		0x2fe9
#define 	 STRING_SMENU_BLE_TIP4		0x2fea
#define 	 STRING_SMENU_BLE_TIP5		0x2feb
#define 	 STRING_SMENU_BLE_TIP6		0x2fec
#define 	 STRING_SMENU_BLE_TIP7		0x2fed
#define 	 STRING_SMENU_BLE_TIP8		0x2fee
#define 	 STRING_SMENU_BLE_TIP9		0x2fef
#define 	 STRING_SMENU_BLE_WIRE_SWC		0x2ff0
#define 	 STRING_SMENU_SELECT_MIRROR_MODE		0x2ff1
#define 	 STRING_SMENU_SELECT_USB_MIRROR		0x2ff2
#define 	 STRING_SMENU_SELECT_WIFI_MIRROR		0x2ff3
#define 	 STRING_SMENU_MIRROR_CONNECT_TIP1		0x2ff4
#define 	 STRING_SMENU_MIRROR_CONNECT_TIP2		0x2ff5
#define 	 STRING_SMENU_WIFI_LIST		0x2ff6
#define 	 STRING_SMENU_WIFI_INIT		0x2ff7
#define 	 STRING_SMENU_GENERAL_SETUP		0x2ff8
#define 	 STRING_SMENU_PARK_CAMERA		0x2ff9
#define 	 STRING_SMENU_SMARTPHONE_DEVICE		0x2ffa
#define 	 STRING_SMENU_BLUETOOTH_SETTING		0x2ffb
#define 	 STRING_SMENU_WIFI_INT_MODE		0x2ffc
#define 	 STRING_SMENU_CONNECTION		0x2ffd
#define 	 STRING_SMENU_NEW_DEVICE		0x2ffe
#define 	 STRING_SMENU_DEVICE_PAIR_TIP		0x2fff
#define 	 STRING_SMENU_GSWC_TIP1		0x3000
#define 	 STRING_SMENU_GSWC_TIP2		0x3001
#define 	 STRING_SMENU_GSWC_TIP3		0x3002
#define 	 STRING_SMENU_GSWC_TIP4		0x3003
#define 	 STRING_SMENU_FACTORY_ENTER_PWD		0x3004
#define 	 STRING_SMENU_MIRROR_WALLPAPER		0x3005
#define 	 STRING_SMENU_WALLPAPER_GUIDE		0x3006
#define 	 STRING_SMENU_SYNC_PHOTO		0x3007
#define 	 STRING_SMENU_SELECT_PHOTO		0x3008
#define 	 STRING_SMENU_SYNC_TO_DEVICE		0x3009
#define 	 STRING_SMENU_SYSTEM_INT_AMP		0x300a
#define 	 STRING_SMENU_UPDATE_OTA		0x300b
#define 	 STRING_SMENU_UPDATE_USB		0x300c
#define 	 STRING_SMENU_UPDATE_USB_IN_USB		0x300d
#define 	 STRING_SMENU_UPDATE_USB_PLEASE		0x300e
#define 	 STRING_SMENU_UPDATE_USB_LOADING		0x300f
#define 	 STRING_SMENU_UPDATE_USB_NO_FILE		0x3010
#define 	 STRING_SMENU_UPDATE_USB_NO_FILE_FOUND		0x3011
#define 	 STRING_SMENU_UPDATE_USB_FILE_ERROR		0x3012
#define 	 STRING_SMENU_UPDATE_USB_FILE_FAILED		0x3013
#define 	 STRING_SMENU_UPDATE_USB_UPDATE		0x3014
#define 	 STRING_SMENU_UPDATE_USB_UPDATE_CONFIRM		0x3015
#define 	 STRING_SMENU_UPDATE_USB_UPDATE_CONFIRM_TOP		0x3016
#define 	 STRING_SMENU_DIALOG_CLOSE		0x3017
#define 	 STRING_SMENU_DIALOG_CONNECT		0x3018
#define 	 STRING_SMENU_DIALOG_CANCEL		0x3019
#define 	 STRING_SMENU_NEW_NETWORK		0x301a
#define 	 STRING_SMENU_DIALOG_NEXT		0x301b
#define 	 STRING_SMENU_UPDATE_OTA_WIFI_DISCON		0x301c
#define 	 STRING_SMENU_UPDATE_OTA_WIFI_DISCON_INFOR		0x301d
#define 	 STRING_SMENU_UPDATE_OTA_CHECKBOX		0x301e
#define 	 STRING_SMENU_UPDATE_OTA_APPUP_INFOR_TIT		0x301f
#define 	 STRING_SMENU_UPDATE_OTA_APPUP_INFOR		0x3020
#define 	 STRING_SMENU_UPDATE_OTA_INSTALL_MIRRORAPP		0x3021
#define 	 STRING_SMENU_UPDATE_OTA_INSTALL_CO_INF		0x3022
#define 	 STRING_SMENU_UPDATE_OTA_INSTALL_SEA_INF		0x3023
#define 	 STRING_SMENU_UPDATE_OTA_INSTALL_CODE		0x3024
#define 	 STRING_SMENU_UPDATE_OTA_INSTALL_SEARCH		0x3025
#define 	 STRING_SMENU_TITLE_INT_SETTING		0x3026
#define 	 STRING_SMENU_DIALOG_WIFI_CONNECT		0x3027
#define 	 STRING_SMENU_UPDATE_OTA_CODE_INF		0x3028
#define 	 STRING_SMENU_DIALOG_CON_TO_SERVER		0x3029
#define 	 STRING_SMENU_TITLE_DONT		0x302a
#define 	 STRING_SMENU_DIALOG_DOWNLOAD		0x302b
#define 	 STRING_SMENU_DIALOG_DOWNLOAD_INF		0x302c
#define 	 STRING_SMENU_DIALOG_UP_COMPLETE		0x302d
#define 	 STRING_SMENU_DIALOG_WIFI_CON_ERROR		0x302e
#define 	 STRING_SMENU_DIALOG_DOWNLOADING		0x302f
#define 	 STRING_SMENU_DIALOG_DOWNLOADED		0x3030
#define 	 STRING_SMENU_DIALOG_WIFI_CON		0x3031
#define 	 STRING_SMENU_DIALOG_WIFI_CON_INFO		0x3032
#define 	 STRING_SMENU_DIALOG_CON_FAIL		0x3033
#define 	 STRING_SMENU_DIALOG_CON_FAIL_INFO		0x3034
#define 	 STRING_SMENU_DIALOG_CON_FAIL_BTN		0x3035
#define 	 STRING_SMENU_ADD_NETWORK		0x3036
#define 	 STRING_SMENU_INTERNET_SETTINGS		0x3037
#define 	 STRING_SMENU_CONNECTION		0x3038
#define 	 STRING_SMENU_ADD_NETWORK_NAME		0x3039
#define 	 STRING_SMENU_ADD_NETWORK_PASSWORD		0x303a
#define 	 STRING_SMENU_ADD_NETWORK_SECURITY_NONE		0x303b
#define 	 STRING_SMENU_ADD_NETWORK_SECURITY_WPA		0x303c
#define 	 STRING_SMENU_LEFT_CAMERA		0x303d
#define 	 STRING_SMENU_FRONT_CAMERA		0x303e
#define 	 STRING_SMENU_RIGHT_CAMERA		0x303f
#define 	 STRING_SMENU_PHONE_MIRROR_SETUP		0x3040
#define 	 STRING_BTAUDIO_MUSIC_REPEAT		0x32c9
#define 	 STRING_BTAUDIO_MUSIC_RANDOM		0x32ca
#define 	 STRING_BTAUDIO_MUSIC_ONE		0x32cb
#define 	 STRING_BTAUDIO_MUSIC_ALL		0x32cc
#define 	 STRING_BTAUDIO_MUSIC_NULL		0x32cd
#define 	 STRING_BTAUDIO_MUSIC_MEDIA		0x32ce
#define 	 STRING_BTAUDIO_MUSIC_LIST		0x32cf
#define 	 STRING_BTAUDIO_BT_STATUS_PAIRED_OK		0x32d0
#define 	 STRING_BTAUDIO_BT_STATUS_PAIRED_FAILED		0x32d1
#define 	 STRING_BTAUDIO_BT_STATUS_CONNECTED_OK		0x32d2
#define 	 STRING_BTAUDIO_BT_STATUS_CONNECTED_FAILED		0x32d3
#define 	 STRING_BTAUDIO_BT_STATUS_DISCONNECTED_OK		0x32d4
#define 	 STRING_BTAUDIO_BT_STATUS_DISCONNECTED_FAI		0x32d5
#define 	 STRING_BTAUDIO_BT_PHONE_STATUS_CALLINGIN		0x32d6
#define 	 STRING_BTAUDIO_BT_PHONE_STATUS_CALLOUT		0x32d7
#define 	 STRING_BTAUDIO_BT_PHONE_STATUS_TALKING		0x32d8
#define 	 STRING_BTAUDIO_BT_PHONE_STATUS_CALLEND		0x32d9
#define 	 STRING_BTAUDIO_BT_PHONE_BOOK_DOWNLOAD		0x32da
#define 	 STRING_BTAUDIO_BT_WARN		0x32db
#define 	 STRING_BTAUDIO_BT_BLUETOOTH_DISCONNECTED		0x32dc
#define 	 STRING_BTAUDIO_BT_STATUS_CONNECTEDING		0x32dd
#define 	 STRING_BTAUDIO_BT_STATUS_DISCONNECTING		0x32de
#define 	 STRING_BTAUDIO_MUSIC_NO_ARTIST		0x32df
#define 	 STRING_BTAUDIO_MUSIC_NO_ALBUM		0x32e0
#define 	 STRING_BTAUDIO_MUSIC_NO_TITLE		0x32e1
#define 	 STRING_BTAUDIO_MUSIC_SONGS		0x32e2
#define 	 STRING_BTAUDIO_MUSIC_ARTISTS		0x32e3
#define 	 STRING_BTAUDIO_MUSIC_ALBUMS		0x32e4
#define 	 STRING_BTAUDIO_MUSIC_SHUFFLE		0x32e5
#define 	 STRING_BTAUDIO_MUSIC_TRACKS		0x32e6
#define 	 STRING_BTAUDIO_BT_STATUS_CONNECTED		0x32e7
#define 	 STRING_BTAUDIO_BT_STATUS_DISCONNECTED		0x32e8
#define 	 STRING_BTAUDIO_BT_PHONE_BOOK_EMPTY		0x32e9
#define 	 STRING_BTAUDIO_BT_PHONE_HISTORY_EMPTY		0x32ea
#define 	 STRING_BTAUDIO_BT_PHONE_VOICE_CTRL		0x32eb
#define 	 STRING_BTAUDIO_BT_PHONE_NOT_SUPPORT_SIRI		0x32ec
#define 	 STRING_BTAUDIO_BT_PHONE_SIRI_CANCEL		0x32ed
#define 	 STRING_BTAUDIO_BT_MUSIC_FUNCTION		0x32ee
#define 	 STRING_BTAUDIO_BT_CHECK_PIN_CODE		0x32ef
#define 	 STRING_BTAUDIO_BT_VOICE_CONNECT_BT		0x32f0
#define 	 STRING_BTAUDIO_BT_VOICE_SET_ON		0x32f1
#define 	 STRING_BTAUDIO_BT_DISCNT_TIP		0x32f2
#define 	 STRING_BTAUDIO_BT_TWO_PHONE_CONNCT		0x32f3
#define 	 STRING_BTAUDIO_CONNECT_BLUETOOTH		0x32f4
#define 	 STRING_BTAUDIO_CONNECT_BT_TIP		0x32f5
#define 	 STRING_BTAUDIO_SEARCH_BLUETOOTH		0x32f6
#define 	 STRING_BTAUDIO_BT_PAIR		0x32f7
#define 	 STRING_BTAUDIO_BLUETOOTH_PAIRING		0x32f8
#define 	 STRING_BTAUDIO_BLUETOOTH_CONNECTED		0x32f9
#define 	 STRING_BTAUDIO_BLUETOOTH_DISCONNECTED		0x32fa
#define 	 STRING_AV_IN		0x36b1
#define 	 STRING_AV_IN_LUMA		0x36b2
#define 	 STRING_AV_IN_CONTRAST		0x36b3
#define 	 STRING_AV_IN_SATURATION		0x36b4
#define 	 STRING_AV_IN_NO_DVR		0x36b5
#define 	 STRING_DAB_SCAN_NO_SINGAL		0x3a99
#define 	 STRING_DAB_LOADING		0x3a9a
#define 	 STRING_DAB_SCAN		0x3a9b
#define 	 STRING_DAB_ZOOM_DLS		0x3a9c
#define 	 STRING_DAB_ADD_FAVOR		0x3a9d
#define 	 STRING_DAB_PTY_SEARCH		0x3a9e
#define 	 STRING_DAB_ANT_POWER		0x3a9f
#define 	 STRING_DAB_INFORMATION		0x3aa0
#define 	 STRING_DAB_ADDED		0x3aa1
#define 	 STRING_DAB_NO_SERVICE		0x3aa2
#define 	 STRING_DAB_SCANNING		0x3aa3
#define 	 STRING_DAB_SCAN_STATION		0x3aa4
#define 	 STRING_DAB_RESET_DIALOG		0x3aa5
#define 	 STRING_DAB_INFOR_SW_VERSION		0x3aa6
#define 	 STRING_DAB_INFOR_BIT_SAMP_RATE		0x3aa7
#define 	 STRING_DAB_INFOR_CODEC		0x3aa8
#define 	 STRING_DAB_INFOR_ERROR_RATE		0x3aa9
#define 	 STRING_DAB_INFOR_ENSEMBLE		0x3aaa
#define 	 STRING_DAB_INFOR_FREQ		0x3aab
#define 	 STRING_DAB_RESET		0x3aac
#define 	 STRING_DAB_LABEL_WAITING		0x3aad
#define 	 STRING_DAB_ENSEMBLE_NO_INFORMATION		0x3aae
#define 	 STRING_DAB_PTY_NONE		0x3aaf
#define 	 STRING_DAB_PTY_NEWS		0x3ab0
#define 	 STRING_DAB_PTY_CURRENT_AFFAIRS		0x3ab1
#define 	 STRING_DAB_PTY_INFORMATION		0x3ab2
#define 	 STRING_DAB_PTY_SPORT		0x3ab3
#define 	 STRING_DAB_PTY_EDUCATION		0x3ab4
#define 	 STRING_DAB_PTY_DRAMA		0x3ab5
#define 	 STRING_DAB_PTY_ARTS		0x3ab6
#define 	 STRING_DAB_PTY_SCIENCE		0x3ab7
#define 	 STRING_DAB_PTY_TALK		0x3ab8
#define 	 STRING_DAB_PTY_POP_MUSIC		0x3ab9
#define 	 STRING_DAB_PTY_ROCK_MUSIC		0x3aba
#define 	 STRING_DAB_PTY_EASY_LISTENING		0x3abb
#define 	 STRING_DAB_PTY_LIGHT_CLASSICAL		0x3abc
#define 	 STRING_DAB_PTY_CLASSICAL_MUSIC		0x3abd
#define 	 STRING_DAB_PTY_OTHER_MUSIC		0x3abe
#define 	 STRING_DAB_PTY_WEATHER		0x3abf
#define 	 STRING_DAB_PTY_FINANCE		0x3ac0
#define 	 STRING_DAB_PTY_CHILDREN_S		0x3ac1
#define 	 STRING_DAB_PTY_FACTUAL		0x3ac2
#define 	 STRING_DAB_PTY_RELIGION		0x3ac3
#define 	 STRING_DAB_PTY_PHONE_IN		0x3ac4
#define 	 STRING_DAB_PTY_TRAVEL		0x3ac5
#define 	 STRING_DAB_PTY_LEISURE		0x3ac6
#define 	 STRING_DAB_PTY_JAZZ_AND_BLUES		0x3ac7
#define 	 STRING_DAB_PTY_COUNTRY_MUSIC		0x3ac8
#define 	 STRING_DAB_PTY_NATIONAL_MUSIC		0x3ac9
#define 	 STRING_DAB_PTY_OLDIES_MUSIC		0x3aca
#define 	 STRING_DAB_PTY_FOLK_MUSIC		0x3acb
#define 	 STRING_DAB_PTY_DOCUMENTARY		0x3acc
#define 	 STRING_DAB_TI		0x3acd
#define 	 STRING_DAB_SERVICE		0x3ace
#define 	 STRING_DAB_SETUP		0x3acf
#define 	 STRING_DAB_PTY		0x3ad0
#define 	 STRING_DAB_PRIORITY		0x3ad1
#define 	 STRING_DAB_RELATED_SERVICE		0x3ad2
#define 	 STRING_DAB_ANNOUNCEMENT_SELECT		0x3ad3
#define 	 STRING_DAB_ANNOUNCEMENT_ALARM		0x3ad4
#define 	 STRING_DAB_ANNO_ROAD_TRAFFIC_FLASH		0x3ad5
#define 	 STRING_DAB_ANNO_TRANSPORT_FLASH		0x3ad6
#define 	 STRING_DAB_ANNO_WARNING_SERVICE		0x3ad7
#define 	 STRING_DAB_ANNO_NEWS_FLASH		0x3ad8
#define 	 STRING_DAB_ANNO_AREA_WEATHER_FLASH		0x3ad9
#define 	 STRING_DAB_ANNO_EVENT_ANNOUNCEMENT		0x3ada
#define 	 STRING_DAB_ANNO_SPECIAL_EVENT		0x3adb
#define 	 STRING_DAB_ANNO_PROGRAMME_INFORMATION		0x3adc
#define 	 STRING_DAB_ANNO_SPORT_REPORT		0x3add
#define 	 STRING_DAB_ANNO_FINANCIAL_REPORT		0x3ade
#define 	 STRING_DAB_RELATED_SERVICE_TIPS		0x3adf
#define 	 STRING_USB_IPOD_MUSIC_REPEAT		0x3e81
#define 	 STRING_USB_IPOD_MUSIC_RANDOM		0x3e82
#define 	 STRING_USB_IPOD_MUSIC_ONE		0x3e83
#define 	 STRING_USB_IPOD_MUSIC_ALL		0x3e84
#define 	 STRING_USB_IPOD_MUSIC_NULL		0x3e85
#define 	 STRING_USB_IPOD_MUSIC_MEDIA		0x3e86
#define 	 STRING_USB_IPOD_MUSIC_LIST		0x3e87
#define 	 STRING_USB_IPOD_MUSIC_NO_ARTIST		0x3e88
#define 	 STRING_USB_IPOD_MUSIC_NO_ALBUM		0x3e89
#define 	 STRING_USB_IPOD_MUSIC_NO_TITLE		0x3e8a
#define 	 STRING_USB_IPOD_MUSIC_SONGS		0x3e8b
#define 	 STRING_USB_IPOD_MUSIC_ARTISTS		0x3e8c
#define 	 STRING_USB_IPOD_MUSIC_ALBUMS		0x3e8d
#define 	 STRING_USB_IPOD_MUSIC_SHUFFLE		0x3e8e
#define 	 STRING_USB_IPOD_MUSIC_TRACKS		0x3e8f
#define 	 STRING_USB_IPOD_DIALOG_BROWSER_SYNC		0x3e90
#define 	 STRING_AUDIO_SETTING_MAIN		0x4269
#define 	 STRING_AUDIO_SETTING_PRESET_EQ		0x426a
#define 	 STRING_AUDIO_SETTING_POSITION_DTA		0x426b
#define 	 STRING_AUDIO_SETTING_VIRTUAL_SUBW		0x426c
#define 	 STRING_AUDIO_SETTING_XBASS		0x426d
#define 	 STRING_AUDIO_SETTING_SPEAKER		0x426e
#define 	 STRING_AUDIO_SETTING_MUSIC_ZONE		0x426f
#define 	 STRING_AUDIO_SETTING_CROSS_OVER		0x4270
#define 	 STRING_AUDIO_SETTING_SUBW_MODE		0x4271
#define 	 STRING_AUDIO_SETTING_SUBW_ON		0x4272
#define 	 STRING_AUDIO_SETTING_SUBW_OFF		0x4273
#define 	 STRING_AUDIO_SETTING_SUBW_SLOPE		0x4274
#define 	 STRING_AUDIO_SETTING_SUBW_FREQ		0x4275
#define 	 STRING_AUDIO_SETTING_SUBW_LEVEL		0x4276
#define 	 STRING_AUDIO_SETTING_TITIE_MAIN		0x4277
#define 	 STRING_AUDIO_SETTING_TITIE_EQ		0x4278
#define 	 STRING_AUDIO_SETTING_TITIE_POSITION		0x4279
#define 	 STRING_AUDIO_SETTING_TITIE_XOVER		0x427a
#define 	 STRING_AUDIO_SETTING_TITIE_REAR_SUBW		0x427b
#define 	 STRING_AUDIO_SETTING_TITIE_FADER_BALANCE		0x427c
#define 	 STRING_AUDIO_SETTING_TITIE_XBASS		0x427d
#define 	 STRING_AUDIO_SETTING_EQ_CLASSIC		0x427e
#define 	 STRING_AUDIO_SETTING_EQ_ROCK		0x427f
#define 	 STRING_AUDIO_SETTING_EQ_R_AND_B		0x4280
#define 	 STRING_AUDIO_SETTING_EQ_JAZZ		0x4281
#define 	 STRING_AUDIO_SETTING_EQ_POP		0x4282
#define 	 STRING_AUDIO_SETTING_EQ_FLAT		0x4283
#define 	 STRING_AUDIO_SETTING_EQ_IPOD		0x4284
#define 	 STRING_AUDIO_SETTING_EQ_HIP_HOP		0x4285
#define 	 STRING_AUDIO_SETTING_EQ_USER1		0x4286
#define 	 STRING_AUDIO_SETTING_EQ_USER2		0x4287
#define 	 STRING_AUDIO_SETTING_EQ_USER3		0x4288
#define 	 STRING_AUDIO_SETTING_EQ_EASY		0x4289
#define 	 STRING_AUDIO_SETTING_EQ_TOP40		0x428a
#define 	 STRING_AUDIO_SETTING_EQ_POWERFUL		0x428b
#define 	 STRING_AUDIO_SETTING_EQ_HARD_ROCK		0x428c
#define 	 STRING_AUDIO_SETTING_POSITION_DELAY		0x428d
#define 	 STRING_AUDIO_SETTING_POSITION_LEVEL		0x428e
#define 	 STRING_AUDIO_SETTING_XBASS_BASS_BOOSTER		0x428f
#define 	 STRING_AUDIO_SETTING_XBASS_DYNAMIC_BASS		0x4290
#define 	 STRING_AUDIO_SETTING_CROSS_OVER_PHASE		0x4291
#define 	 STRING_AUDIO_SETTING_FADER_TIPS		0x4292
#define 	 STRING_AUDIO_SETTING_FADER_REAR		0x4293
#define 	 STRING_AUDIO_SETTING_FADER_FRONT		0x4294
#define 	 STRING_AUDIO_SETTING_BALANCE_TIPS		0x4295
#define 	 STRING_AUDIO_SETTING_BALANCE_LEFT		0x4296
#define 	 STRING_AUDIO_SETTING_BALANCE_RIGHT		0x4297
#define 	 STRING_AUDIO_SETTING_BALANCE_CENTER		0x4298
#define 	 STRING_AUDIO_SETTING_LIS_POS_FRONT_LEFT		0x4299
#define 	 STRING_AUDIO_SETTING_LIS_POS_FRONT_RIGHT		0x429a
#define 	 STRING_AUDIO_SETTING_LIS_POS_FRONT_ALL		0x429b
#define 	 STRING_AUDIO_SETTING_LIS_POS_MIDDLE		0x429c
#define 	 STRING_AUDIO_SETTING_FADER_BAL_BLOCK		0x429d
#define 	 STRING_AUDIO_SETTING_LIS_POS_BLOCK		0x429e
#define 	 STRING_AUDIO_SETTING_DTA_FRONT_L		0x429f
#define 	 STRING_AUDIO_SETTING_DTA_FRONT_R		0x42a0
#define 	 STRING_AUDIO_SETTING_DTA_REAR_L		0x42a1
#define 	 STRING_AUDIO_SETTING_DTA_REAR_R		0x42a2
#define 	 STRING_AUDIO_SETTING_DTA_SUBWOOFER		0x42a3
#define 	 STRING_AUDIO_SETTING_TITIE_TIME_ALIGHNMENT		0x42a4
#define 	 STRING_AUDIO_SETTING_TITIE_CROSS_OVER		0x42a5
#define 	 STRING_AUDIO_SETTING_MUSIC_ZONE_1		0x42a6
#define 	 STRING_ADB_DISCONNECT		0x4a39
#define 	 STRING_ADB_HELP		0x4a3a
#define 	 STRING_ADB_HELP_HOW_TO_LOAD		0x4a3b
#define 	 STRING_ADB_HELP_CONNECT_USB		0x4a3c
#define 	 STRING_ADB_HELP_UNLOCK_THE_PHONE		0x4a3d
#define 	 STRING_ADB_HELP_ACTIVATE_DEVELOPER		0x4a3e
#define 	 STRING_ADB_HELP_EN_USB_DEBUG		0x4a3f
#define 	 STRING_ADB_HELP_REMIND1		0x4a40
#define 	 STRING_ADB_HELP_REMIND2		0x4a41
#define 	 STRING_ADB_METHOD		0x4a42
#define 	 STRING_ADB_QR_CODE		0x4a43
#define 	 STRING_ADB_DEVELOPER_OPTION_REMIND		0x4a44
#define 	 STRING_ADB_GO_TO_ABOUT		0x4a45
#define 	 STRING_ADB_CLICK_ONE_OF_BELOW		0x4a46
#define 	 STRING_ADB_ANDRIOD_VETION		0x4a47
#define 	 STRING_ADB_BULIT_VESION		0x4a48
#define 	 STRING_ADB_VESION_NUMBER		0x4a49
#define 	 STRING_ADB_USB_DEBUG		0x4a4a
#define 	 STRING_ADB_USB_DEBUG_SEL_MODE		0x4a4b
#define 	 STRING_ADB_MTP		0x4a4c
#define 	 STRING_ADB_PTP		0x4a4d
#define 	 STRING_ADB_USB_STORAGE		0x4a4e
#define 	 STRING_ADB_BT_DISCONNECT		0x4a4f
#define 	 STRING_ADB_SOUND_FROM		0x4a50
#define 	 STRING_ADB_DEVELOPER_OPTION		0x4a51
#define 	 STRING_ADB_APP_PHONE_AUTHORISE		0x4a52
#define 	 STRING_ADB_MIRROR_FIRST_TIME_JVC		0x4a53
#define 	 STRING_ADB_METHOD1_JVC		0x4a54
#define 	 STRING_ADB_METHOD2_JVC		0x4a55
#define 	 STRING_ADB_APP_INSTALLATION		0x4a56
#define 	 STRING_ADB_BT_CONNECTING		0x4a57
#define 	 STRING_ADB_IF_BT_CONNECT_FAIL		0x4a58
#define 	 STRING_ADB_PLEASE_SEARCH		0x4a59
#define 	 STRING_ADB_NOT_SUPPORT_TOUCH		0x4a5a
#define 	 STRING_ADB_JVC_APP_STOP		0x4a5b
#define 	 STRING_ADB_KENWOOD_APP_STOP		0x4a5c
#define 	 STRING_ADB_MIRROR_FIRST_TIME_KENWOOD		0x4a5d
#define 	 STRING_ADB_METHOD1_KENWOOD		0x4a5e
#define 	 STRING_ADB_METHOD2_KENWOOD		0x4a5f
#define 	 STRING_ADB_BT_CONNECTING_TIP		0x4a60
#define 	 STRING_ADB_DONT_SHOW_ANGIN		0x4a61
#define 	 STRING_ADB_NO_DEVICE		0x4a62
#define 	 STRING_ADB_METHOD1		0x4a63
#define 	 STRING_ADB_METHOD2		0x4a64
#define 	 STRING_ADB_METHOD1_DETAILS		0x4a65
#define 	 STRING_ADB_METHOD2_DETAILS		0x4a66
#define 	 STRING_ADB_THE_DEVICE_SUPPORT_USB		0x4a67
#define 	 STRING_ADB_SCREEN_CAPTURING_TIP		0x4a68
#define 	 STRING_ADB_IPHONE_LOCK_SCREEN_TIPS		0x4a69
#define 	 STRING_ADB_IPHONE_LOCKDOWN_BUILD_TIPS		0x4a6a
#define 	 STRING_ADB_IPHONE_LOCKDOWN_TIMEOUT_TIPS		0x4a6b
#define 	 STRING_ADB_IPHONE_LOCKDOWN_ERROR_TIPS		0x4a6c
#define 	 STRING_ADB_TAP_START_NOW		0x4a6d
#define 	 STRING_ADB_TAP_START_NOW_CANCEL		0x4a6e
#define 	 STRING_ADB_RETURN_HOME		0x4a6f
#define 	 STRING_ADB_TRY_AGAIN		0x4a70
#define 	 STRING_ADB_OPEN_MIRROR_TIP		0x4a71
#define 	 STRING_ADB_MIRROR_SCREEN_LOCK		0x4a72
#define 	 STRING_IPHONE_MIRROR_DISCONNECT		0x4e21
#define 	 STRING_IPHONE_MIRROR_BT_DISCONNECT		0x4e22
#define 	 STRING_IPHONE_MIRROR_BT_CONNECTING		0x4e23
#define 	 STRING_IPHONE_MIRROR_NOT_SUPPORT_TOUCH		0x4e24
#define 	 STRING_IPHONE_MIRROR_BT_CONNECTING_TIP		0x4e25
#define 	 STRING_IPHONE_MIRROR_NO_DEVICE		0x4e26
#define 	 STRING_IPHONE_MIRROR_LOCK_SCREEN_TIPS		0x4e27
#define 	 STRING_IPHONE_MIRROR_LOCKDOWN_BUILD_TIPS		0x4e28
#define 	 STRING_IPHONE_MIRROR_LOCKDOWN_TIMEOUT_TIPS		0x4e29
#define 	 STRING_IPHONE_MIRROR_LOCKDOWN_ERROR_TIPS		0x4e2a
#define 	 STRING_IPHONE_MIRROR_CONNECT_ERROR_TIPS		0x4e2b
#define 	 STRING_IPHONE_MIRROR_APP_INSTALL_TIPS		0x4e2c
#define 	 STRING_IPHONE_MIRROR_APP_INSTALL_TIPS1		0x4e2d
#define 	 STRING_IPHONE_MIRROR_APP_INSTALL_TIPS2		0x4e2e
#define 	 STRING_IPHONE_MIRROR_PHONE_MIRROR		0x4e2f
#define 	 STRING_IPHONE_MIRROR_SELECT_TIPS		0x4e30
#define 	 STRING_IPHONE_MIRROR_BY_USB		0x4e31
#define 	 STRING_IPHONE_MIRROR_BY_WIFI		0x4e32
#define 	 STRING_IPHONE_MIRRORMIRROR_APP_TIPS		0x4e33
#define 	 STRING_IPHONE_MIRROR_INSTALL_APP		0x4e34
#define 	 STRING_IPHONE_MIRROR_CONNECT_APP		0x4e35
#define 	 STRING_IPHONE_MIRROR_USB_INSTALL_APP_TIP1		0x4e36
#define 	 STRING_IPHONE_MIRROR_USB_INSTALL_APP_TIP2		0x4e37
#define 	 STRING_IPHONE_MIRROR_SCEEN_MIRROR_FROM		0x4e38
#define 	 STRING_IPHONE_MIRROR_WIFI_IPHONE		0x4e39
#define 	 STRING_IPHONE_MIRROR_WIFI_ANDROID		0x4e3a
#define 	 STRING_IPHONE_MIRROR_WIFI_CONNECT_TIP1		0x4e3b
#define 	 STRING_IPHONE_MIRROR_WIFI_CONNECT_TIP2		0x4e3c
#define 	 STRING_IPHONE_MIRROR_WIFI_CONNECT_TIP3		0x4e3d
#define 	 STRING_IPHONE_MIRROR_WIFI_LOCK_SCREEN_TIPS		0x4e3e
#define 	 STRING_MIRROR_SCAN_QR_CODE		0x4e3f
#define 	 STRING_MIRROR_INSTALL_QR_CODE		0x4e40
#define 	 STRING_MIRROR_INSTALL_APP		0x4e41
#define 	 STRING_MIRROR_PHONE_GUIDE		0x4e42
#define 	 STRING_MIRROR_START_NOW_TEXT		0x4e43
#define 	 STRING_MIRROR_SCREEN_MIRROR_TEXT		0x4e44
#define 	 STRING_MIRROR_START_BROADCAST_TEXT		0x4e45
#define 	 STRING_IPHONE_MIRROR_NOT_SUP_TOUCH_TITLE		0x4e46
#define 	 STRING_IPHONE_MIRROR_TOUCH_START_METH		0x4e47
#define 	 STRING_IPHONE_MIRROR_TOUCH_APP_TIP		0x4e48
#define 	 STRING_MIRROR_START_MIRROR_TEXT		0x4e49
#define 	 STRING_MIRROR_CONFIRM_CON_WIFI		0x4e4a


#endif //__LANG__H__ 


