Archive member included to satisfy reference by file (symbol)

./elibrary/bin//libsyscall.a(syscall.o)
                              livedesk/leopard/mod_desktop/functions/built-in.o (esCFG_GetKeyValue)
./elibrary/bin//libsyscall.a(syscall_fops.o)
                              livedesk/leopard/mod_desktop/functions/built-in.o (ioctl)
./elibrary/bin//libminic.a(elibs_stdio.o)
                              livedesk/leopard/mod_desktop/functions/built-in.o (eLIBs_fopen)
./elibrary/bin//libminic.a(elibs_string.o)
                              livedesk/leopard/mod_desktop/functions/built-in.o (eLIBs_strlen)
./elibrary/bin//libminic.a(elibs_sprintf.o)
                              ./elibrary/bin//libminic.a(elibs_stdio.o) (eLIBs_vscnprintf)
./elibrary/bin//liblzma.a(az100.o)
                              livedesk/leopard/mod_desktop/functions/built-in.o (AZ100_IsCompress)
./elibrary/bin//liblzma.a(compress.o)
                              ./elibrary/bin//liblzma.a(az100.o) (AZ100_DataUncompress)
./elibrary/bin//liblzma.a(LzmaLib.o)
                              ./elibrary/bin//liblzma.a(compress.o) (LzmaCompress)
./elibrary/bin//liblzma.a(LzmaDec.o)
                              ./elibrary/bin//liblzma.a(LzmaLib.o) (LzmaDecode)
./elibrary/bin//liblzma.a(LzmaEnc.o)
                              ./elibrary/bin//liblzma.a(LzmaLib.o) (LzmaEncProps_Init)
./elibrary/bin//liblzma.a(LzFind.o)
                              ./elibrary/bin//liblzma.a(LzmaEnc.o) (MatchFinder_Construct)
./elibrary/bin//libresreader.a(LangDec.o)
                              livedesk/leopard/mod_desktop/functions/built-in.o (Lang_Open)
./elibrary/bin//libresreader.a(themedec.o)
                              livedesk/leopard/mod_desktop/functions/built-in.o (OpenRes)
./elibrary/bin//libminic.a(elibs_stdlib.o)
                              ./elibrary/bin//liblzma.a(LzmaLib.o) (eLIBs_malloc)
/home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/../../../../riscv64-unknown-elf/lib/rv64imafdcxtheadc/lp64d/libc.a(lib_a-fclose.o)
                              livedesk/leopard/mod_desktop/functions/built-in.o (fclose)
/home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/../../../../riscv64-unknown-elf/lib/rv64imafdcxtheadc/lp64d/libc.a(lib_a-fflush.o)
                              /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/../../../../riscv64-unknown-elf/lib/rv64imafdcxtheadc/lp64d/libc.a(lib_a-fclose.o) (__sflush_r)
/home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/../../../../riscv64-unknown-elf/lib/rv64imafdcxtheadc/lp64d/libc.a(lib_a-findfp.o)
                              /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/../../../../riscv64-unknown-elf/lib/rv64imafdcxtheadc/lp64d/libc.a(lib_a-fclose.o) (__sinit)
/home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/../../../../riscv64-unknown-elf/lib/rv64imafdcxtheadc/lp64d/libc.a(lib_a-fopen.o)
                              livedesk/leopard/mod_desktop/functions/built-in.o (fopen)
/home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/../../../../riscv64-unknown-elf/lib/rv64imafdcxtheadc/lp64d/libc.a(lib_a-fseek.o)
                              /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/../../../../riscv64-unknown-elf/lib/rv64imafdcxtheadc/lp64d/libc.a(lib_a-fopen.o) (_fseek_r)
/home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/../../../../riscv64-unknown-elf/lib/rv64imafdcxtheadc/lp64d/libc.a(lib_a-fseeko.o)
                              /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/../../../../riscv64-unknown-elf/lib/rv64imafdcxtheadc/lp64d/libc.a(lib_a-fseek.o) (_fseeko_r)
/home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/../../../../riscv64-unknown-elf/lib/rv64imafdcxtheadc/lp64d/libc.a(lib_a-fwalk.o)
                              /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/../../../../riscv64-unknown-elf/lib/rv64imafdcxtheadc/lp64d/libc.a(lib_a-findfp.o) (_fwalk)
/home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/../../../../riscv64-unknown-elf/lib/rv64imafdcxtheadc/lp64d/libc.a(lib_a-fwrite.o)
                              livedesk/leopard/mod_desktop/functions/built-in.o (fwrite)
/home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/../../../../riscv64-unknown-elf/lib/rv64imafdcxtheadc/lp64d/libc.a(lib_a-impure.o)
                              /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/../../../../riscv64-unknown-elf/lib/rv64imafdcxtheadc/lp64d/libc.a(lib_a-fflush.o) (_global_impure_ptr)
/home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/../../../../riscv64-unknown-elf/lib/rv64imafdcxtheadc/lp64d/libc.a(lib_a-makebuf.o)
                              /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/../../../../riscv64-unknown-elf/lib/rv64imafdcxtheadc/lp64d/libc.a(lib_a-fseeko.o) (__smakebuf_r)
/home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/../../../../riscv64-unknown-elf/lib/rv64imafdcxtheadc/lp64d/libc.a(lib_a-memcpy.o)
                              livedesk/leopard/mod_desktop/functions/built-in.o (memcpy)
/home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/../../../../riscv64-unknown-elf/lib/rv64imafdcxtheadc/lp64d/libc.a(lib_a-memmove-stub.o)
                              ./elibrary/bin//liblzma.a(LzFind.o) (memmove)
/home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/../../../../riscv64-unknown-elf/lib/rv64imafdcxtheadc/lp64d/libc.a(lib_a-memset.o)
                              livedesk/leopard/mod_desktop/functions/built-in.o (memset)
/home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/../../../../riscv64-unknown-elf/lib/rv64imafdcxtheadc/lp64d/libc.a(lib_a-refill.o)
                              /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/../../../../riscv64-unknown-elf/lib/rv64imafdcxtheadc/lp64d/libc.a(lib_a-fseeko.o) (__srefill_r)
/home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/../../../../riscv64-unknown-elf/lib/rv64imafdcxtheadc/lp64d/libc.a(lib_a-stdio.o)
                              /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/../../../../riscv64-unknown-elf/lib/rv64imafdcxtheadc/lp64d/libc.a(lib_a-findfp.o) (__sread)
/home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/../../../../riscv64-unknown-elf/lib/rv64imafdcxtheadc/lp64d/libc.a(lib_a-strcpy.o)
                              livedesk/leopard/mod_desktop/functions/built-in.o (strcpy)
/home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/../../../../riscv64-unknown-elf/lib/rv64imafdcxtheadc/lp64d/libc.a(lib_a-strncpy.o)
                              livedesk/leopard/mod_desktop/functions/built-in.o (strncpy)
/home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/../../../../riscv64-unknown-elf/lib/rv64imafdcxtheadc/lp64d/libc.a(lib_a-sysclose.o)
                              livedesk/leopard/mod_desktop/functions/built-in.o (close)
/home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/../../../../riscv64-unknown-elf/lib/rv64imafdcxtheadc/lp64d/libc.a(lib_a-sysopen.o)
                              livedesk/leopard/mod_desktop/functions/built-in.o (open)
/home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/../../../../riscv64-unknown-elf/lib/rv64imafdcxtheadc/lp64d/libc.a(lib_a-flags.o)
                              /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/../../../../riscv64-unknown-elf/lib/rv64imafdcxtheadc/lp64d/libc.a(lib_a-fopen.o) (__sflags)
/home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/../../../../riscv64-unknown-elf/lib/rv64imafdcxtheadc/lp64d/libc.a(lib_a-fvwrite.o)
                              /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/../../../../riscv64-unknown-elf/lib/rv64imafdcxtheadc/lp64d/libc.a(lib_a-fwrite.o) (__sfvwrite_r)
/home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/../../../../riscv64-unknown-elf/lib/rv64imafdcxtheadc/lp64d/libc.a(lib_a-memchr.o)
                              /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/../../../../riscv64-unknown-elf/lib/rv64imafdcxtheadc/lp64d/libc.a(lib_a-fvwrite.o) (memchr)
/home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/../../../../riscv64-unknown-elf/lib/rv64imafdcxtheadc/lp64d/libc.a(lib_a-wsetup.o)
                              /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/../../../../riscv64-unknown-elf/lib/rv64imafdcxtheadc/lp64d/libc.a(lib_a-fvwrite.o) (__swsetup_r)

Discarded input sections

 .text          0x0000000000000000        0x0 livedesk/leopard/mod_desktop/desktop_api.o
 .data          0x0000000000000000        0x0 livedesk/leopard/mod_desktop/desktop_api.o
 .bss           0x0000000000000000        0x0 livedesk/leopard/mod_desktop/desktop_api.o
 .text          0x0000000000000000        0x0 livedesk/leopard/mod_desktop/mod_desktop.o
 .data          0x0000000000000000        0x0 livedesk/leopard/mod_desktop/mod_desktop.o
 .bss           0x0000000000000000        0x0 livedesk/leopard/mod_desktop/mod_desktop.o
 .text          0x0000000000000000        0x0 livedesk/leopard/mod_desktop/magic.o
 .data          0x0000000000000000        0x0 livedesk/leopard/mod_desktop/magic.o
 .bss           0x0000000000000000        0x0 livedesk/leopard/mod_desktop/magic.o
 .text          0x0000000000000000        0x0 livedesk/leopard/mod_desktop/functions/built-in.o
 .text.bas_tre_to_eq
                0x0000000000000000       0x16 livedesk/leopard/mod_desktop/functions/built-in.o
 .text.dsk_aux_close
                0x0000000000000000        0x8 livedesk/leopard/mod_desktop/functions/built-in.o
 .text.dsk_aux_mcu_close
                0x0000000000000000        0x8 livedesk/leopard/mod_desktop/functions/built-in.o
 .text.dsk_btmoudel_close
                0x0000000000000000       0x14 livedesk/leopard/mod_desktop/functions/built-in.o
 .text.save_backlight_to_mixture
                0x0000000000000000       0x46 livedesk/leopard/mod_desktop/functions/built-in.o
 .text.dsk_display_set_lcd_fadeon
                0x0000000000000000       0x56 livedesk/leopard/mod_desktop/functions/built-in.o
 .text.dsk_display_set_lcd_fadeoff
                0x0000000000000000       0x52 livedesk/leopard/mod_desktop/functions/built-in.o
 .text.dsk_mcu_close
                0x0000000000000000       0x14 livedesk/leopard/mod_desktop/functions/built-in.o
 .text.pull_down_gpio
                0x0000000000000000       0x96 livedesk/leopard/mod_desktop/functions/built-in.o
 .text.pull_up_gpio
                0x0000000000000000       0x96 livedesk/leopard/mod_desktop/functions/built-in.o
 .rodata.aux_eq_dbssoff_table
                0x0000000000000000       0x6c livedesk/leopard/mod_desktop/functions/built-in.o
 .rodata.aux_xbass_dbssoff_table
                0x0000000000000000        0xc livedesk/leopard/mod_desktop/functions/built-in.o
 .rodata.aux_xbass_dbsson_table
                0x0000000000000000        0xc livedesk/leopard/mod_desktop/functions/built-in.o
 .rodata.save_backlight_to_mixture.str1.8
                0x0000000000000000       0x17 livedesk/leopard/mod_desktop/functions/built-in.o
 .data          0x0000000000000000        0x0 livedesk/leopard/mod_desktop/functions/built-in.o
 .bss           0x0000000000000000        0x0 livedesk/leopard/mod_desktop/functions/built-in.o
 .text          0x0000000000000000        0x0 livedesk/leopard/mod_desktop/msg_srv/built-in.o
 .data          0x0000000000000000        0x0 livedesk/leopard/mod_desktop/msg_srv/built-in.o
 .bss           0x0000000000000000        0x0 livedesk/leopard/mod_desktop/msg_srv/built-in.o
 .text          0x0000000000000000        0x0 livedesk/leopard/mod_desktop/util/built-in.o
 .text.eLIBs_language_name2enm
                0x0000000000000000       0x60 livedesk/leopard/mod_desktop/util/built-in.o
 .text.eLIBs_language_enm2name
                0x0000000000000000       0x66 livedesk/leopard/mod_desktop/util/built-in.o
 .text.mini_allocator_default_destroy
                0x0000000000000000        0x2 livedesk/leopard/mod_desktop/util/built-in.o
 .text.mini_allocator_default_alloc
                0x0000000000000000        0xc livedesk/leopard/mod_desktop/util/built-in.o
 .text.mini_allocator_default_realloc
                0x0000000000000000        0xc livedesk/leopard/mod_desktop/util/built-in.o
 .text.mini_allocator_default_free
                0x0000000000000000        0xa livedesk/leopard/mod_desktop/util/built-in.o
 .text.mini_allocator_default_create
                0x0000000000000000        0xa livedesk/leopard/mod_desktop/util/built-in.o
 .rodata.language_map_tbl
                0x0000000000000000      0x30e livedesk/leopard/mod_desktop/util/built-in.o
 .rodata.g_default_allocator
                0x0000000000000000       0x28 livedesk/leopard/mod_desktop/util/built-in.o
 .data          0x0000000000000000        0x0 livedesk/leopard/mod_desktop/util/built-in.o
 .bss           0x0000000000000000        0x0 livedesk/leopard/mod_desktop/util/built-in.o
 .comment       0x0000000000000000       0x66 livedesk/leopard/mod_desktop/util/built-in.o
 .riscv.attributes
                0x0000000000000000       0x3d livedesk/leopard/mod_desktop/util/built-in.o
 .debug_aranges
                0x0000000000000000       0xb0 livedesk/leopard/mod_desktop/util/built-in.o
 .debug_info    0x0000000000000000     0x1454 livedesk/leopard/mod_desktop/util/built-in.o
 .debug_abbrev  0x0000000000000000      0x36c livedesk/leopard/mod_desktop/util/built-in.o
 .debug_line    0x0000000000000000      0x8a3 livedesk/leopard/mod_desktop/util/built-in.o
 .debug_frame   0x0000000000000000      0x110 livedesk/leopard/mod_desktop/util/built-in.o
 .debug_str     0x0000000000000000     0x1229 livedesk/leopard/mod_desktop/util/built-in.o
 .debug_loc     0x0000000000000000      0x31f livedesk/leopard/mod_desktop/util/built-in.o
 .text          0x0000000000000000        0x0 ./elibrary/bin//libsyscall.a(syscall.o)
 .data          0x0000000000000000        0x0 ./elibrary/bin//libsyscall.a(syscall.o)
 .bss           0x0000000000000000        0x0 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esCFG_Exit_Ex
                0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esCFG_GetGPIOSecData
                0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esCFG_GetGPIOSecData_Ex
                0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esCFG_GetGPIOSecKeyCount
                0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esCFG_GetGPIOSecKeyCount_Ex
                0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esCFG_GetKeyValue_Ex
                0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esCFG_GetSecCount
                0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esCFG_GetSecCount_Ex
                0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esCFG_GetSecKeyCount
                0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esCFG_GetSecKeyCount_Ex
                0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esCFG_Init_Ex
                0x0000000000000000       0x16 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esCHS_Char2Uni
                0x0000000000000000       0x1c ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esCHS_GetChLowerTbl
                0x0000000000000000       0x1c ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esCHS_GetChUpperTbl
                0x0000000000000000       0x1c ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esCHS_Uni2Char
                0x0000000000000000       0x1c ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esCLK_CloseMclk
                0x0000000000000000       0x1c ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esCLK_GetMclkDiv
                0x0000000000000000       0x1c ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esCLK_GetMclkSrc
                0x0000000000000000       0x1c ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esCLK_GetRound_Rate
                0x0000000000000000       0x20 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esCLK_GetSrcFreq
                0x0000000000000000       0x1c ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esCLK_MclkAssert
                0x0000000000000000       0x1c ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esCLK_MclkDeassert
                0x0000000000000000       0x1c ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esCLK_MclkGetRstStatus
                0x0000000000000000       0x1c ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esCLK_MclkOnOff
                0x0000000000000000       0x20 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esCLK_MclkRegCb
                0x0000000000000000       0x1c ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esCLK_MclkReset
                0x0000000000000000       0x1c ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esCLK_MclkUnregCb
                0x0000000000000000       0x1c ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esCLK_ModInfo
                0x0000000000000000       0x16 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esCLK_OpenMclk
                0x0000000000000000       0x1c ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esCLK_SetMclkDiv
                0x0000000000000000       0x20 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esCLK_SetMclkSrc
                0x0000000000000000       0x20 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esCLK_SetSrcFreq
                0x0000000000000000       0x20 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esCLK_SysInfo
                0x0000000000000000       0x16 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esDEV_Close
                0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esDEV_DevReg
                0x0000000000000000       0x16 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esDEV_DevUnreg
                0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esDEV_Insmod
                0x0000000000000000       0x1c ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esDEV_Ioctl
                0x0000000000000000       0x1c ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esDEV_Lock
                0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esDEV_Open
                0x0000000000000000       0x1a ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esDEV_Plugin
                0x0000000000000000       0x1c ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esDEV_Plugout
                0x0000000000000000       0x1c ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esDEV_Read
                0x0000000000000000       0x20 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esDEV_Unimod
                0x0000000000000000       0x1c ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esDEV_Unlock
                0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esDEV_Write
                0x0000000000000000       0x20 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.rt_device_register
                0x0000000000000000       0x16 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.rt_device_unregister
                0x0000000000000000       0x16 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esDMA_ChangeMode
                0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esDMA_DisableINT
                0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esDMA_EnableINT
                0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esDMA_Information
                0x0000000000000000       0x16 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esDMA_QueryDst
                0x0000000000000000       0x16 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esDMA_QueryRestCount
                0x0000000000000000       0x16 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esDMA_QuerySrc
                0x0000000000000000       0x16 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esDMA_QueryStat
                0x0000000000000000       0x1a ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esDMA_RegDmaHdler
                0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esDMA_Release
                0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esDMA_Request
                0x0000000000000000       0x16 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esDMA_Restart
                0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esDMA_Setting
                0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esDMA_Start
                0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esDMA_Stop
                0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esDMA_UnregDmaHdler
                0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esEXEC_PCreate
                0x0000000000000000       0x1e ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esEXEC_PDel
                0x0000000000000000       0x1a ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esEXEC_PDelReq
                0x0000000000000000       0x1a ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esEXEC_Run
                0x0000000000000000       0x1e ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esFSYS_clearpartupdateflag
                0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esFSYS_fd2file
                0x0000000000000000       0x16 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esFSYS_ferrclr
                0x0000000000000000       0x16 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esFSYS_ferror
                0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esFSYS_file2fd
                0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esFSYS_fsdbg
                0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esFSYS_fseekex
                0x0000000000000000       0x16 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esFSYS_fsreg
                0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esFSYS_fstat
                0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esFSYS_fsunreg
                0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esFSYS_ftell
                0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esFSYS_ftellex
                0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esFSYS_ftruncate
                0x0000000000000000       0x1c ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esFSYS_mkdir
                0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esFSYS_mntfs
                0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esFSYS_mntparts
                0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esFSYS_open
                0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esFSYS_partfslck
                0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esFSYS_partfsunlck
                0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esFSYS_pclose
                0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esFSYS_pdreg
                0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esFSYS_pdunreg
                0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esFSYS_perrclr
                0x0000000000000000       0x16 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esFSYS_perror
                0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esFSYS_pioctrl
                0x0000000000000000       0x1c ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esFSYS_popen
                0x0000000000000000       0x16 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esFSYS_pread
                0x0000000000000000       0x20 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esFSYS_premove
                0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esFSYS_prename
                0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esFSYS_pwrite
                0x0000000000000000       0x20 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esFSYS_querypartupdateflag
                0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esFSYS_readdir
                0x0000000000000000       0x16 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esFSYS_remove
                0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esFSYS_rename
                0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esFSYS_rewinddir
                0x0000000000000000       0x16 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esFSYS_rmdir
                0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esFSYS_statfs
                0x0000000000000000       0x1c ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esFSYS_umntfs
                0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esFSYS_umntparts
                0x0000000000000000       0x1c ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esHID_hiddevreg
                0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esHID_hiddevunreg
                0x0000000000000000       0x1c ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esHID_SendMsg
                0x0000000000000000       0x1c ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esINPUT_LdevFeedback
                0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esINPUT_RegDev
                0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esINPUT_SendEvent
                0x0000000000000000       0x20 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esINPUT_UnregDev
                0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esINT_DisableINT
                0x0000000000000000       0x1c ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esINT_EnableINT
                0x0000000000000000       0x1c ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esINT_InsFIR
                0x0000000000000000       0x1c ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esINT_InsISR
                0x0000000000000000       0x1c ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esINT_SetIRQPrio
                0x0000000000000000       0x20 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esINT_UniFIR
                0x0000000000000000       0x1c ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esINT_UniISR
                0x0000000000000000       0x1c ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esKRNL_DumpStack
                0x0000000000000000       0x16 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esKRNL_FlagAccept
                0x0000000000000000       0x1c ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esKRNL_FlagCreate
                0x0000000000000000       0x1a ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esKRNL_FlagDel
                0x0000000000000000       0x16 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esKRNL_FlagNameGet
                0x0000000000000000       0x1a ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esKRNL_FlagNameSet
                0x0000000000000000       0x16 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esKRNL_FlagPend
                0x0000000000000000       0x20 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esKRNL_FlagPendGetFlagsRdy
                0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esKRNL_FlagPost
                0x0000000000000000       0x1c ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esKRNL_FlagQuery
                0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esKRNL_GetCallBack
                0x0000000000000000       0x16 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esKRNL_GetTIDCur
                0x0000000000000000       0x16 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esKRNL_InterruptDisable
                0x0000000000000000       0x16 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esKRNL_InterruptEnable
                0x0000000000000000       0x16 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esKRNL_MboxAccept
                0x0000000000000000       0x16 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esKRNL_MboxCreate
                0x0000000000000000       0x1a ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esKRNL_MboxDel
                0x0000000000000000       0x16 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esKRNL_MboxPend
                0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esKRNL_MboxPost
                0x0000000000000000       0x1a ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esKRNL_MboxPostOpt
                0x0000000000000000       0x1e ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esKRNL_MboxQuery
                0x0000000000000000       0x1a ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esKRNL_MemLeakChk
                0x0000000000000000       0x1a ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esKRNL_MutexCreate
                0x0000000000000000       0x16 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esKRNL_MutexDel
                0x0000000000000000       0x16 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esKRNL_MutexPend
                0x0000000000000000       0x16 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esKRNL_MutexPost
                0x0000000000000000       0x1a ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esKRNL_QFlush
                0x0000000000000000       0x1a ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esKRNL_QPend
                0x0000000000000000       0x16 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esKRNL_QPostFront
                0x0000000000000000       0x1a ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esKRNL_QPostOpt
                0x0000000000000000       0x1a ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esKRNL_QQuery
                0x0000000000000000       0x1a ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esKRNL_SchedLock
                0x0000000000000000       0x16 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esKRNL_SchedUnlock
                0x0000000000000000       0x16 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esKRNL_SemAccept
                0x0000000000000000       0x1a ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esKRNL_SemSet
                0x0000000000000000       0x16 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esKRNL_SktAccept
                0x0000000000000000       0x16 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esKRNL_SktCreate
                0x0000000000000000       0x22 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esKRNL_SktDel
                0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esKRNL_SktPend
                0x0000000000000000       0x1a ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esKRNL_SktPost
                0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esKRNL_TaskPrefEn
                0x0000000000000000       0x1a ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esKRNL_TaskQuery
                0x0000000000000000       0x1e ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esKRNL_TaskResume
                0x0000000000000000       0x1e ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esKRNL_TaskSuspend
                0x0000000000000000       0x1e ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esKRNL_Time
                0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esKRNL_TimeDlyResume
                0x0000000000000000       0x1e ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esKRNL_TimeGet
                0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esKRNL_TimeSet
                0x0000000000000000       0x1a ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esKRNL_TmrCreate
                0x0000000000000000       0x1a ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esKRNL_TmrDel
                0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esKRNL_TmrError
                0x0000000000000000       0x1a ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esKRNL_TmrRemainGet
                0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esKRNL_TmrStart
                0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esKRNL_TmrStateGet
                0x0000000000000000       0x1a ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esKRNL_TmrStop
                0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esKRNL_Version
                0x0000000000000000       0x1a ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esKRNL_Ioctrl
                0x0000000000000000       0x16 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esKSRV_ClearWatchDog
                0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esKSRV_DisableWatchDog
                0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esKSRV_DisableWatchDogSrv
                0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esKSRV_EnableWatchDog
                0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esKSRV_EnableWatchDogSrv
                0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esKSRV_GetAddPara
                0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esKSRV_GetDramCfgPara
                0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esKSRV_GetHighMsg
                0x0000000000000000       0x16 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esKSRV_GetLowMsg
                0x0000000000000000       0x16 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esKSRV_GetPara
                0x0000000000000000       0x1c ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esKSRV_GetSocID
                0x0000000000000000       0x1a ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esKSRV_GetTargetPara
                0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esKSRV_GetVersion
                0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esKSRV_Save_Mixture_Hld
                0x0000000000000000       0x16 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esKSRV_Get_Mixture_Hld
                0x0000000000000000       0x16 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esKSRV_Save_Raw_Decoder_Hld
                0x0000000000000000       0x16 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esKSRV_Get_Raw_Decoder_Hld
                0x0000000000000000       0x16 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esKSRV_memcpy
                0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esKSRV_memset
                0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esKSRV_PowerOff
                0x0000000000000000       0x16 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esKSRV_Random
                0x0000000000000000       0x1c ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esKSRV_Reset
                0x0000000000000000       0x16 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esKSRV_Save_Display_Hld
                0x0000000000000000       0x16 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esKSRV_SendMsg
                0x0000000000000000       0x20 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esKSRV_SendMsgEx
                0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esKSRV_SysInfo
                0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esMEMS_Calloc
                0x0000000000000000       0x1e ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esMEMS_CleanDCache
                0x0000000000000000       0x16 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esMEMS_CleanDCacheRegion
                0x0000000000000000       0x1a ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esMEMS_CleanFlushCache
                0x0000000000000000       0x16 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esMEMS_CleanFlushCacheRegion
                0x0000000000000000       0x1a ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esMEMS_CleanFlushDCache
                0x0000000000000000       0x16 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esMEMS_CleanFlushDCacheRegion
                0x0000000000000000       0x1a ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esMEMS_CleanInvalidateCacheAll
                0x0000000000000000       0x16 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esMEMS_FlushCache
                0x0000000000000000       0x16 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esMEMS_FlushCacheRegion
                0x0000000000000000       0x1a ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esMEMS_FlushDCache
                0x0000000000000000       0x16 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esMEMS_FlushDCacheRegion
                0x0000000000000000       0x1a ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esMEMS_FlushICache
                0x0000000000000000       0x16 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esMEMS_FlushICacheRegion
                0x0000000000000000       0x1a ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esMEMS_FreeMemSize
                0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esMEMS_GetIoVAByPA
                0x0000000000000000       0x1c ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esMEMS_HeapCreate
                0x0000000000000000       0x1c ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esMEMS_HeapDel
                0x0000000000000000       0x16 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esMEMS_Info
                0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esMEMS_Palloc
                0x0000000000000000       0x1e ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esMEMS_Pfree
                0x0000000000000000       0x1a ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esMEMS_Realloc
                0x0000000000000000       0x1a ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esMEMS_TotalMemSize
                0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esMEMS_VA2PA
                0x0000000000000000       0x16 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esMEMS_VMalloc
                0x0000000000000000       0x16 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esMEMS_VMCreate
                0x0000000000000000       0x1c ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esMEMS_VMDelete
                0x0000000000000000       0x1a ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esMEMS_VMfree
                0x0000000000000000       0x16 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esMEM_BWDisable
                0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esMEM_BWEnable
                0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esMEM_BWGet
                0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esMEM_DramSuspend
                0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esMEM_DramWakeup
                0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esMEM_MasterGet
                0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esMEM_MasterSet
                0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esMEM_RegDramAccess
                0x0000000000000000       0x16 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esMEM_ReleaseDramUsrMode
                0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esMEM_RequestDramUsrMode
                0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esMEM_SramRelBlk
                0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esMEM_SramReqBlk
                0x0000000000000000       0x16 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esMEM_SramSwitchBlk
                0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esMEM_UnRegDramAccess
                0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esMODS_MRead
                0x0000000000000000       0x20 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esMODS_MWrite
                0x0000000000000000       0x20 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esMSTUB_GetFuncEntry
                0x0000000000000000       0x1e ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esMSTUB_RegFuncTbl
                0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esMSTUB_UnRegFuncTbl
                0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esPINS_ClearPending
                0x0000000000000000       0x16 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esPINS_DisbaleInt
                0x0000000000000000       0x16 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esPINS_EnbaleInt
                0x0000000000000000       0x16 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esPINS_GetPinGrpStat
                0x0000000000000000       0x1a ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esPINS_GetPinStat
                0x0000000000000000       0x16 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esPINS_PinGrpRel
                0x0000000000000000       0x16 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esPINS_QueryInt
                0x0000000000000000       0x16 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esPINS_RegIntHdler
                0x0000000000000000       0x16 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esPINS_SetIntMode
                0x0000000000000000       0x1a ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esPINS_SetPinDrive
                0x0000000000000000       0x1a ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esPINS_SetPinIO
                0x0000000000000000       0x16 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esPINS_SetPinPull
                0x0000000000000000       0x1a ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esPINS_SetPinStat
                0x0000000000000000       0x16 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esPINS_UnregIntHdler
                0x0000000000000000       0x16 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esPWRMAN_EnterStandby
                0x0000000000000000       0x1a ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esPWRMAN_GetStandbyPara
                0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esPWRMAN_LockCpuFreq
                0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esPWRMAN_RegDevice
                0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esPWRMAN_RelPwrmanMode
                0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esPWRMAN_ReqPwrmanMode
                0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esPWRMAN_SetStandbyMode
                0x0000000000000000       0x1c ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esPWRMAN_UnlockCpuFreq
                0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esPWRMAN_UnregDevice
                0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esPWRMAN_UsrEventNotify
                0x0000000000000000       0x16 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esSIOS_getchar
                0x0000000000000000       0x16 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esSIOS_gets
                0x0000000000000000       0x14 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esSIOS_putarg
                0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esSIOS_setbaud
                0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esSVC_RegCloseNode
                0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esSVC_RegClrError
                0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esSVC_RegCreateKey
                0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esSVC_RegCreatePath
                0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esSVC_RegCreateSet
                0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esSVC_RegDeleteKey
                0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esSVC_RegDeleteNode
                0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esSVC_RegDeletePath
                0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esSVC_RegDeleteSet
                0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esSVC_RegGetError
                0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esSVC_RegGetFirstKey
                0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esSVC_RegGetFirstSet
                0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esSVC_RegGetKeyCount
                0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esSVC_RegGetKeyValue
                0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esSVC_RegGetNextKey
                0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esSVC_RegGetNextSet
                0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esSVC_RegGetRootPath
                0x0000000000000000       0x16 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esSVC_RegGetSetCount
                0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esSVC_RegIni2Reg
                0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esSVC_RegOpenNode
                0x0000000000000000       0x16 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esSVC_RegReg2Ini
                0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esSVC_RegSetKeyValue
                0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esSVC_RegSetRootPath
                0x0000000000000000       0x16 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esSVC_ResourceRel
                0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esSVC_ResourceReq
                0x0000000000000000       0x22 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esTIME_ContiCntr
                0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esTIME_GetDate
                0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esTIME_GetTime
                0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esTIME_PauseCntr
                0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esTIME_QuerryAlarm
                0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esTIME_QuerryCntr
                0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esTIME_QuerryCntrStat
                0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esTIME_QuerryTimer
                0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esTIME_ReleaseAlarm
                0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esTIME_ReleaseCntr
                0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esTIME_ReleaseTimer
                0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esTIME_RequestAlarm
                0x0000000000000000       0x1a ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esTIME_RequestCntr
                0x0000000000000000       0x16 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esTIME_RequestTimer
                0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esTIME_SetCntrPrescale
                0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esTIME_SetCntrValue
                0x0000000000000000       0x1c ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esTIME_SetDate
                0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esTIME_SetTime
                0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esTIME_StartAlarm
                0x0000000000000000       0x1c ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esTIME_StartCntr
                0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esTIME_StartTimer
                0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esTIME_StopAlarm
                0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esTIME_StopCntr
                0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.esTIME_StopTimer
                0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.backtrace
                0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.select   0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.mmap     0x0000000000000000       0x16 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.munmap   0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall.o)
 .text.poll     0x0000000000000000       0x1c ./elibrary/bin//libsyscall.a(syscall.o)
 .text.pipe     0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall.o)
 .text          0x0000000000000000        0x0 ./elibrary/bin//libsyscall.a(syscall_fops.o)
 .data          0x0000000000000000        0x0 ./elibrary/bin//libsyscall.a(syscall_fops.o)
 .bss           0x0000000000000000        0x0 ./elibrary/bin//libsyscall.a(syscall_fops.o)
 .text.dup      0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall_fops.o)
 .text.dup2     0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall_fops.o)
 .text.closedir
                0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall_fops.o)
 .text.fioctrl  0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall_fops.o)
 .text._fstat_r
                0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall_fops.o)
 .text._lseek_r
                0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall_fops.o)
 .text._rename_r
                0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall_fops.o)
 .text._stat_r  0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall_fops.o)
 .text._mkdir_r
                0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall_fops.o)
 .text.fsync    0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall_fops.o)
 .text._unlink_r
                0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall_fops.o)
 .text._read_r  0x0000000000000000       0x16 ./elibrary/bin//libsyscall.a(syscall_fops.o)
 .text._write_r
                0x0000000000000000       0x16 ./elibrary/bin//libsyscall.a(syscall_fops.o)
 .text._gettimeofday_r
                0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall_fops.o)
 .text.settimeofday
                0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall_fops.o)
 .text._malloc_r
                0x0000000000000000       0x16 ./elibrary/bin//libsyscall.a(syscall_fops.o)
 .text._realloc_r
                0x0000000000000000       0x16 ./elibrary/bin//libsyscall.a(syscall_fops.o)
 .text._calloc_r
                0x0000000000000000       0x16 ./elibrary/bin//libsyscall.a(syscall_fops.o)
 .text._free_r  0x0000000000000000       0x16 ./elibrary/bin//libsyscall.a(syscall_fops.o)
 .text._isatty_r
                0x0000000000000000       0x16 ./elibrary/bin//libsyscall.a(syscall_fops.o)
 .text._isatty  0x0000000000000000       0x16 ./elibrary/bin//libsyscall.a(syscall_fops.o)
 .text.__libc_init_array
                0x0000000000000000       0x16 ./elibrary/bin//libsyscall.a(syscall_fops.o)
 .text._system  0x0000000000000000       0x16 ./elibrary/bin//libsyscall.a(syscall_fops.o)
 .text.__malloc_lock
                0x0000000000000000        0x2 ./elibrary/bin//libsyscall.a(syscall_fops.o)
 .text.__malloc_unlock
                0x0000000000000000        0x2 ./elibrary/bin//libsyscall.a(syscall_fops.o)
 .text.ftruncate
                0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall_fops.o)
 .text.mkdir    0x0000000000000000       0x1c ./elibrary/bin//libsyscall.a(syscall_fops.o)
 .text.opendir  0x0000000000000000       0x16 ./elibrary/bin//libsyscall.a(syscall_fops.o)
 .text.readdir  0x0000000000000000       0x16 ./elibrary/bin//libsyscall.a(syscall_fops.o)
 .text.remove   0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall_fops.o)
 .text.rewinddir
                0x0000000000000000       0x16 ./elibrary/bin//libsyscall.a(syscall_fops.o)
 .text.rmdir    0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall_fops.o)
 .text.statfs   0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall_fops.o)
 .text.access   0x0000000000000000       0x18 ./elibrary/bin//libsyscall.a(syscall_fops.o)
 .text          0x0000000000000000        0x0 ./elibrary/bin//libminic.a(elibs_stdio.o)
 .data          0x0000000000000000        0x0 ./elibrary/bin//libminic.a(elibs_stdio.o)
 .bss           0x0000000000000000        0x0 ./elibrary/bin//libminic.a(elibs_stdio.o)
 .text.eLIBs_remove
                0x0000000000000000        0x8 ./elibrary/bin//libminic.a(elibs_stdio.o)
 .text.eLIBs_rename
                0x0000000000000000        0x8 ./elibrary/bin//libminic.a(elibs_stdio.o)
 .text.eLIBs_ftruncate
                0x0000000000000000        0x8 ./elibrary/bin//libminic.a(elibs_stdio.o)
 .text.eLIBs_fllseek
                0x0000000000000000       0x1e ./elibrary/bin//libminic.a(elibs_stdio.o)
 .text.eLIBs_ftell
                0x0000000000000000        0x8 ./elibrary/bin//libminic.a(elibs_stdio.o)
 .text.eLIBs_flltell
                0x0000000000000000       0x26 ./elibrary/bin//libminic.a(elibs_stdio.o)
 .text.eLIBs_ferror
                0x0000000000000000        0x8 ./elibrary/bin//libminic.a(elibs_stdio.o)
 .text.eLIBs_ferrclr
                0x0000000000000000        0x8 ./elibrary/bin//libminic.a(elibs_stdio.o)
 .text.eLIBs_opendir
                0x0000000000000000        0x8 ./elibrary/bin//libminic.a(elibs_stdio.o)
 .text.eLIBs_readdir
                0x0000000000000000        0x8 ./elibrary/bin//libminic.a(elibs_stdio.o)
 .text.eLIBs_rewinddir
                0x0000000000000000        0x8 ./elibrary/bin//libminic.a(elibs_stdio.o)
 .text.eLIBs_mkdir
                0x0000000000000000        0x8 ./elibrary/bin//libminic.a(elibs_stdio.o)
 .text.eLIBs_rmdir
                0x0000000000000000        0x8 ./elibrary/bin//libminic.a(elibs_stdio.o)
 .text.eLIBs_DirEnt2Attr
                0x0000000000000000        0x8 ./elibrary/bin//libminic.a(elibs_stdio.o)
 .text.eLIBs_DirEnt2Name
                0x0000000000000000       0x2e ./elibrary/bin//libminic.a(elibs_stdio.o)
 .text.eLIBs_DirEnt2Size
                0x0000000000000000        0x6 ./elibrary/bin//libminic.a(elibs_stdio.o)
 .text.eLIBs_DirEnt2Time
                0x0000000000000000        0x4 ./elibrary/bin//libminic.a(elibs_stdio.o)
 .text.eLIBs_GetNumFiles
                0x0000000000000000       0x3e ./elibrary/bin//libminic.a(elibs_stdio.o)
 .text.eLIBs_GetNumSubItems
                0x0000000000000000       0x4e ./elibrary/bin//libminic.a(elibs_stdio.o)
 .text.eLIBs_GetFileAttributes
                0x0000000000000000       0x4c ./elibrary/bin//libminic.a(elibs_stdio.o)
 .text.eLIBs_SetFileAttributes
                0x0000000000000000        0x4 ./elibrary/bin//libminic.a(elibs_stdio.o)
 .text.eLIBs_GetDirSize
                0x0000000000000000      0x108 ./elibrary/bin//libminic.a(elibs_stdio.o)
 .text.eLIBs_GetFileSize
                0x0000000000000000       0x40 ./elibrary/bin//libminic.a(elibs_stdio.o)
 .text.eLIBs_GetVolName
                0x0000000000000000       0x80 ./elibrary/bin//libminic.a(elibs_stdio.o)
 .text.eLIBs_GetVolNameLen
                0x0000000000000000       0x50 ./elibrary/bin//libminic.a(elibs_stdio.o)
 .text.eLIBs_GetVolNameCharset
                0x0000000000000000       0x52 ./elibrary/bin//libminic.a(elibs_stdio.o)
 .text.eLIBs_GetVolTSpace
                0x0000000000000000       0x30 ./elibrary/bin//libminic.a(elibs_stdio.o)
 .text.eLIBs_GetVolFSpace
                0x0000000000000000       0x30 ./elibrary/bin//libminic.a(elibs_stdio.o)
 .text.eLIBs_GetVolClustSize
                0x0000000000000000       0x26 ./elibrary/bin//libminic.a(elibs_stdio.o)
 .text.eLIBs_GetFSName
                0x0000000000000000       0x80 ./elibrary/bin//libminic.a(elibs_stdio.o)
 .text.eLIBs_GetFSNameLen
                0x0000000000000000       0x50 ./elibrary/bin//libminic.a(elibs_stdio.o)
 .text.eLIBs_GetFSAttribute
                0x0000000000000000       0x36 ./elibrary/bin//libminic.a(elibs_stdio.o)
 .text.eLIBs_GetPTName
                0x0000000000000000       0x62 ./elibrary/bin//libminic.a(elibs_stdio.o)
 .text.eLIBs_disklock
                0x0000000000000000        0x8 ./elibrary/bin//libminic.a(elibs_stdio.o)
 .text.eLIBs_diskunlock
                0x0000000000000000        0x8 ./elibrary/bin//libminic.a(elibs_stdio.o)
 .text.eLIBs_GetDiskArray
                0x0000000000000000       0x62 ./elibrary/bin//libminic.a(elibs_stdio.o)
 .text.eLIBs_feof
                0x0000000000000000       0x1e ./elibrary/bin//libminic.a(elibs_stdio.o)
 .text.eLIBs_fgetc
                0x0000000000000000       0x2c ./elibrary/bin//libminic.a(elibs_stdio.o)
 .text.eLIBs_fgets
                0x0000000000000000       0x6c ./elibrary/bin//libminic.a(elibs_stdio.o)
 .text.eLIBs_fputc
                0x0000000000000000       0x32 ./elibrary/bin//libminic.a(elibs_stdio.o)
 .text.eLIBs_fputs
                0x0000000000000000       0x42 ./elibrary/bin//libminic.a(elibs_stdio.o)
 .text.eLIBs_GetFileATime
                0x0000000000000000       0x50 ./elibrary/bin//libminic.a(elibs_stdio.o)
 .text.eLIBs_GetFileMTime
                0x0000000000000000       0x50 ./elibrary/bin//libminic.a(elibs_stdio.o)
 .text.eLIBs_GetFileCTime
                0x0000000000000000       0x50 ./elibrary/bin//libminic.a(elibs_stdio.o)
 .text.eLIBs_GetDirATime
                0x0000000000000000       0x48 ./elibrary/bin//libminic.a(elibs_stdio.o)
 .text.eLIBs_GetDirMTime
                0x0000000000000000       0x48 ./elibrary/bin//libminic.a(elibs_stdio.o)
 .text.eLIBs_GetDirCTime
                0x0000000000000000       0x48 ./elibrary/bin//libminic.a(elibs_stdio.o)
 .text.eLIBs_file2fd
                0x0000000000000000        0x8 ./elibrary/bin//libminic.a(elibs_stdio.o)
 .text.eLIBs_fd2file
                0x0000000000000000        0x8 ./elibrary/bin//libminic.a(elibs_stdio.o)
 .text.eLIBs_open
                0x0000000000000000        0x8 ./elibrary/bin//libminic.a(elibs_stdio.o)
 .text.eLIBs_fstat
                0x0000000000000000        0x8 ./elibrary/bin//libminic.a(elibs_stdio.o)
 .text.eLIBs_statfs
                0x0000000000000000        0xc ./elibrary/bin//libminic.a(elibs_stdio.o)
 .text.eLIBs_GetDevName
                0x0000000000000000       0x6a ./elibrary/bin//libminic.a(elibs_stdio.o)
 .text.eLIBs_GetDevParts
                0x0000000000000000       0x88 ./elibrary/bin//libminic.a(elibs_stdio.o)
 .text.eLIBs_GetTime
                0x0000000000000000        0x8 ./elibrary/bin//libminic.a(elibs_stdio.o)
 .text.eLIBs_GetDate
                0x0000000000000000        0x8 ./elibrary/bin//libminic.a(elibs_stdio.o)
 .bss.dir_level.6470
                0x0000000000000000        0x4 ./elibrary/bin//libminic.a(elibs_stdio.o)
 .rodata.eLIBs_GetDirSize.str1.8
                0x0000000000000000        0x2 ./elibrary/bin//libminic.a(elibs_stdio.o)
 .rodata.eLIBs_GetFileAttributes.str1.8
                0x0000000000000000        0x3 ./elibrary/bin//libminic.a(elibs_stdio.o)
 .rodata.eLIBs_GetVolNameCharset.str1.8
                0x0000000000000000        0x5 ./elibrary/bin//libminic.a(elibs_stdio.o)
 .text          0x0000000000000000        0x0 ./elibrary/bin//libminic.a(elibs_string.o)
 .data          0x0000000000000000        0x0 ./elibrary/bin//libminic.a(elibs_string.o)
 .bss           0x0000000000000000        0x0 ./elibrary/bin//libminic.a(elibs_string.o)
 .text.eLIBs_strnlen
                0x0000000000000000        0xc ./elibrary/bin//libminic.a(elibs_string.o)
 .text.eLIBs_strcat
                0x0000000000000000        0xc ./elibrary/bin//libminic.a(elibs_string.o)
 .text.eLIBs_strncat
                0x0000000000000000        0xc ./elibrary/bin//libminic.a(elibs_string.o)
 .text.eLIBs_stricmp
                0x0000000000000000        0xc ./elibrary/bin//libminic.a(elibs_string.o)
 .text.eLIBs_strnicmp
                0x0000000000000000        0xc ./elibrary/bin//libminic.a(elibs_string.o)
 .text.eLIBs_strchr
                0x0000000000000000        0xc ./elibrary/bin//libminic.a(elibs_string.o)
 .text.eLIBs_strnchr
                0x0000000000000000        0xc ./elibrary/bin//libminic.a(elibs_string.o)
 .text.eLIBs_strchrlast
                0x0000000000000000        0xc ./elibrary/bin//libminic.a(elibs_string.o)
 .text.eLIBs_strstr
                0x0000000000000000        0xc ./elibrary/bin//libminic.a(elibs_string.o)
 .text.eLIBs_memclr
                0x0000000000000000       0x10 ./elibrary/bin//libminic.a(elibs_string.o)
 .text.eLIBs_memmove
                0x0000000000000000       0x3c ./elibrary/bin//libminic.a(elibs_string.o)
 .text          0x0000000000000000        0x0 ./elibrary/bin//libminic.a(elibs_sprintf.o)
 .data          0x0000000000000000        0x0 ./elibrary/bin//libminic.a(elibs_sprintf.o)
 .bss           0x0000000000000000        0x0 ./elibrary/bin//libminic.a(elibs_sprintf.o)
 .text.eLIBs_snprintf
                0x0000000000000000       0x20 ./elibrary/bin//libminic.a(elibs_sprintf.o)
 .text.eLIBs_scnprintf
                0x0000000000000000       0x2e ./elibrary/bin//libminic.a(elibs_sprintf.o)
 .text.eLIBs_vsprintf
                0x0000000000000000        0xe ./elibrary/bin//libminic.a(elibs_sprintf.o)
 .text.eLIBs_sprintf
                0x0000000000000000       0x26 ./elibrary/bin//libminic.a(elibs_sprintf.o)
 .text          0x0000000000000000        0x0 ./elibrary/bin//liblzma.a(az100.o)
 .data          0x0000000000000000        0x0 ./elibrary/bin//liblzma.a(az100.o)
 .bss           0x0000000000000000        0x0 ./elibrary/bin//liblzma.a(az100.o)
 .text          0x0000000000000000        0x0 ./elibrary/bin//liblzma.a(compress.o)
 .data          0x0000000000000000        0x0 ./elibrary/bin//liblzma.a(compress.o)
 .bss           0x0000000000000000        0x0 ./elibrary/bin//liblzma.a(compress.o)
 .text.AZ100_Compress
                0x0000000000000000       0x2e ./elibrary/bin//liblzma.a(compress.o)
 .text          0x0000000000000000        0x0 ./elibrary/bin//liblzma.a(LzmaLib.o)
 .data          0x0000000000000000        0x0 ./elibrary/bin//liblzma.a(LzmaLib.o)
 .bss           0x0000000000000000        0x0 ./elibrary/bin//liblzma.a(LzmaLib.o)
 .text.LzmaCompress
                0x0000000000000000       0x68 ./elibrary/bin//liblzma.a(LzmaLib.o)
 .text          0x0000000000000000        0x0 ./elibrary/bin//liblzma.a(LzmaDec.o)
 .data          0x0000000000000000        0x0 ./elibrary/bin//liblzma.a(LzmaDec.o)
 .bss           0x0000000000000000        0x0 ./elibrary/bin//liblzma.a(LzmaDec.o)
 .text.LzmaDec_InitDicAndState
                0x0000000000000000       0x1e ./elibrary/bin//liblzma.a(LzmaDec.o)
 .text.LzmaDec_DecodeToBuf
                0x0000000000000000       0xd6 ./elibrary/bin//liblzma.a(LzmaDec.o)
 .text.LzmaDec_Free
                0x0000000000000000       0x2a ./elibrary/bin//liblzma.a(LzmaDec.o)
 .text.LzmaDec_Allocate
                0x0000000000000000       0x80 ./elibrary/bin//liblzma.a(LzmaDec.o)
 .text          0x0000000000000000        0x0 ./elibrary/bin//liblzma.a(LzmaEnc.o)
 .data          0x0000000000000000        0x0 ./elibrary/bin//liblzma.a(LzmaEnc.o)
 .bss           0x0000000000000000        0x0 ./elibrary/bin//liblzma.a(LzmaEnc.o)
 .text.LitEnc_GetPrice
                0x0000000000000000       0x34 ./elibrary/bin//liblzma.a(LzmaEnc.o)
 .text.LitEnc_GetPriceMatched
                0x0000000000000000       0x58 ./elibrary/bin//liblzma.a(LzmaEnc.o)
 .text.RcTree_GetPrice
                0x0000000000000000       0x3a ./elibrary/bin//liblzma.a(LzmaEnc.o)
 .text.RcTree_ReverseGetPrice
                0x0000000000000000       0x3a ./elibrary/bin//liblzma.a(LzmaEnc.o)
 .text.LenEnc_Init
                0x0000000000000000       0x40 ./elibrary/bin//liblzma.a(LzmaEnc.o)
 .text.LenEnc_SetPrices
                0x0000000000000000      0x104 ./elibrary/bin//liblzma.a(LzmaEnc.o)
 .text.ReadMatchDistances
                0x0000000000000000       0xd6 ./elibrary/bin//liblzma.a(LzmaEnc.o)
 .text.FillAlignPrices
                0x0000000000000000       0x68 ./elibrary/bin//liblzma.a(LzmaEnc.o)
 .text.FillDistancesPrices
                0x0000000000000000      0x176 ./elibrary/bin//liblzma.a(LzmaEnc.o)
 .text.MyWrite  0x0000000000000000       0x3c ./elibrary/bin//liblzma.a(LzmaEnc.o)
 .text.RangeEnc_FlushStream.part.1
                0x0000000000000000       0x34 ./elibrary/bin//liblzma.a(LzmaEnc.o)
 .text.RangeEnc_ShiftLow
                0x0000000000000000       0x70 ./elibrary/bin//liblzma.a(LzmaEnc.o)
 .text.RangeEnc_EncodeDirectBits
                0x0000000000000000       0x56 ./elibrary/bin//liblzma.a(LzmaEnc.o)
 .text.RangeEnc_EncodeBit
                0x0000000000000000       0x56 ./elibrary/bin//liblzma.a(LzmaEnc.o)
 .text.LitEnc_Encode
                0x0000000000000000       0x42 ./elibrary/bin//liblzma.a(LzmaEnc.o)
 .text.RcTree_Encode
                0x0000000000000000       0x56 ./elibrary/bin//liblzma.a(LzmaEnc.o)
 .text.RcTree_ReverseEncode
                0x0000000000000000       0x5c ./elibrary/bin//liblzma.a(LzmaEnc.o)
 .text.LenEnc_Encode
                0x0000000000000000       0x9e ./elibrary/bin//liblzma.a(LzmaEnc.o)
 .text.MovePos  0x0000000000000000       0x1c ./elibrary/bin//liblzma.a(LzmaEnc.o)
 .text.GetPureRepPrice
                0x0000000000000000       0xaa ./elibrary/bin//liblzma.a(LzmaEnc.o)
 .text.CheckErrors
                0x0000000000000000       0x40 ./elibrary/bin//liblzma.a(LzmaEnc.o)
 .text.Flush    0x0000000000000000      0x1fe ./elibrary/bin//liblzma.a(LzmaEnc.o)
 .text.LzmaEnc_CodeOneBlock
                0x0000000000000000     0x1bb2 ./elibrary/bin//liblzma.a(LzmaEnc.o)
 .text.LzmaEnc_Encode2
                0x0000000000000000       0x5e ./elibrary/bin//liblzma.a(LzmaEnc.o)
 .text.LzmaEncProps_Init
                0x0000000000000000       0x18 ./elibrary/bin//liblzma.a(LzmaEnc.o)
 .text.LzmaEncProps_Normalize
                0x0000000000000000       0xb2 ./elibrary/bin//liblzma.a(LzmaEnc.o)
 .text.LzmaEncProps_GetDictSize
                0x0000000000000000       0x26 ./elibrary/bin//liblzma.a(LzmaEnc.o)
 .text.LzmaEnc_FastPosInit
                0x0000000000000000       0x34 ./elibrary/bin//liblzma.a(LzmaEnc.o)
 .text.LzmaEnc_SaveState
                0x0000000000000000      0x1f2 ./elibrary/bin//liblzma.a(LzmaEnc.o)
 .text.LzmaEnc_RestoreState
                0x0000000000000000      0x1f0 ./elibrary/bin//liblzma.a(LzmaEnc.o)
 .text.LzmaEnc_SetProps
                0x0000000000000000       0xd2 ./elibrary/bin//liblzma.a(LzmaEnc.o)
 .text.LzmaEnc_InitPriceTables
                0x0000000000000000       0x40 ./elibrary/bin//liblzma.a(LzmaEnc.o)
 .text.LzmaEnc_Construct
                0x0000000000000000       0x76 ./elibrary/bin//liblzma.a(LzmaEnc.o)
 .text.LzmaEnc_Create
                0x0000000000000000       0x28 ./elibrary/bin//liblzma.a(LzmaEnc.o)
 .text.LzmaEnc_FreeLits
                0x0000000000000000       0x44 ./elibrary/bin//liblzma.a(LzmaEnc.o)
 .text.LzmaEnc_Destruct
                0x0000000000000000       0x44 ./elibrary/bin//liblzma.a(LzmaEnc.o)
 .text.LzmaEnc_Destroy
                0x0000000000000000       0x26 ./elibrary/bin//liblzma.a(LzmaEnc.o)
 .text.LzmaEnc_Init
                0x0000000000000000      0x17e ./elibrary/bin//liblzma.a(LzmaEnc.o)
 .text.LzmaEnc_InitPrices
                0x0000000000000000      0x130 ./elibrary/bin//liblzma.a(LzmaEnc.o)
 .text.LzmaEnc_AllocAndInit
                0x0000000000000000      0x17c ./elibrary/bin//liblzma.a(LzmaEnc.o)
 .text.LzmaEnc_PrepareForLzma2
                0x0000000000000000       0x20 ./elibrary/bin//liblzma.a(LzmaEnc.o)
 .text.LzmaEnc_MemPrepare
                0x0000000000000000       0x26 ./elibrary/bin//liblzma.a(LzmaEnc.o)
 .text.LzmaEnc_Finish
                0x0000000000000000        0x2 ./elibrary/bin//liblzma.a(LzmaEnc.o)
 .text.LzmaEnc_GetNumAvailableBytes
                0x0000000000000000        0x8 ./elibrary/bin//liblzma.a(LzmaEnc.o)
 .text.LzmaEnc_GetCurBuf
                0x0000000000000000       0x22 ./elibrary/bin//liblzma.a(LzmaEnc.o)
 .text.LzmaEnc_CodeOneMemBlock
                0x0000000000000000       0xc4 ./elibrary/bin//liblzma.a(LzmaEnc.o)
 .text.LzmaEnc_Encode
                0x0000000000000000       0x4c ./elibrary/bin//liblzma.a(LzmaEnc.o)
 .text.LzmaEnc_WriteProperties
                0x0000000000000000       0x66 ./elibrary/bin//liblzma.a(LzmaEnc.o)
 .text.LzmaEnc_MemEncode
                0x0000000000000000       0x74 ./elibrary/bin//liblzma.a(LzmaEnc.o)
 .text.LzmaEncode
                0x0000000000000000       0xaa ./elibrary/bin//liblzma.a(LzmaEnc.o)
 .rodata.kLiteralNextStates
                0x0000000000000000       0x30 ./elibrary/bin//liblzma.a(LzmaEnc.o)
 .rodata.kMatchNextStates
                0x0000000000000000       0x30 ./elibrary/bin//liblzma.a(LzmaEnc.o)
 .rodata.kRepNextStates
                0x0000000000000000       0x30 ./elibrary/bin//liblzma.a(LzmaEnc.o)
 .rodata.kShortRepNextStates
                0x0000000000000000       0x30 ./elibrary/bin//liblzma.a(LzmaEnc.o)
 .debug_info    0x0000000000000000     0x4ec2 ./elibrary/bin//liblzma.a(LzmaEnc.o)
 .debug_abbrev  0x0000000000000000      0x4dc ./elibrary/bin//liblzma.a(LzmaEnc.o)
 .debug_loc     0x0000000000000000     0x537e ./elibrary/bin//liblzma.a(LzmaEnc.o)
 .debug_aranges
                0x0000000000000000      0x340 ./elibrary/bin//liblzma.a(LzmaEnc.o)
 .debug_line    0x0000000000000000     0x720b ./elibrary/bin//liblzma.a(LzmaEnc.o)
 .debug_str     0x0000000000000000     0x1554 ./elibrary/bin//liblzma.a(LzmaEnc.o)
 .comment       0x0000000000000000       0x33 ./elibrary/bin//liblzma.a(LzmaEnc.o)
 .debug_frame   0x0000000000000000      0x908 ./elibrary/bin//liblzma.a(LzmaEnc.o)
 .riscv.attributes
                0x0000000000000000       0x3d ./elibrary/bin//liblzma.a(LzmaEnc.o)
 .text          0x0000000000000000        0x0 ./elibrary/bin//liblzma.a(LzFind.o)
 .data          0x0000000000000000        0x0 ./elibrary/bin//liblzma.a(LzFind.o)
 .bss           0x0000000000000000        0x0 ./elibrary/bin//liblzma.a(LzFind.o)
 .text.MatchFinder_GetPointerToCurrentPos
                0x0000000000000000        0x4 ./elibrary/bin//liblzma.a(LzFind.o)
 .text.MatchFinder_GetIndexByte
                0x0000000000000000        0x8 ./elibrary/bin//liblzma.a(LzFind.o)
 .text.MatchFinder_GetNumAvailableBytes
                0x0000000000000000        0xa ./elibrary/bin//liblzma.a(LzFind.o)
 .text.MatchFinder_SetLimits
                0x0000000000000000       0x4c ./elibrary/bin//liblzma.a(LzFind.o)
 .text.Hc_GetMatchesSpec
                0x0000000000000000       0x8e ./elibrary/bin//liblzma.a(LzFind.o)
 .text.SkipMatchesSpec
                0x0000000000000000       0xd0 ./elibrary/bin//liblzma.a(LzFind.o)
 .text.MatchFinder_ReadBlock
                0x0000000000000000       0x86 ./elibrary/bin//liblzma.a(LzFind.o)
 .text.MatchFinder_Init
                0x0000000000000000       0x4c ./elibrary/bin//liblzma.a(LzFind.o)
 .text.MatchFinder_ReduceOffsets
                0x0000000000000000       0x14 ./elibrary/bin//liblzma.a(LzFind.o)
 .text.MatchFinder_MoveBlock
                0x0000000000000000       0x3c ./elibrary/bin//liblzma.a(LzFind.o)
 .text.MatchFinder_NeedMove
                0x0000000000000000       0x24 ./elibrary/bin//liblzma.a(LzFind.o)
 .text.MatchFinder_ReadIfRequired
                0x0000000000000000       0x1c ./elibrary/bin//liblzma.a(LzFind.o)
 .text.MatchFinder_Construct
                0x0000000000000000       0x50 ./elibrary/bin//liblzma.a(LzFind.o)
 .text.MatchFinder_Free
                0x0000000000000000       0x32 ./elibrary/bin//liblzma.a(LzFind.o)
 .text.MatchFinder_Create
                0x0000000000000000      0x184 ./elibrary/bin//liblzma.a(LzFind.o)
 .text.MatchFinder_Normalize3
                0x0000000000000000       0x22 ./elibrary/bin//liblzma.a(LzFind.o)
 .text.MatchFinder_CheckLimits
                0x0000000000000000       0x90 ./elibrary/bin//liblzma.a(LzFind.o)
 .text.MatchFinder_MovePos
                0x0000000000000000       0x26 ./elibrary/bin//liblzma.a(LzFind.o)
 .text.Bt4_MatchFinder_Skip
                0x0000000000000000       0xd6 ./elibrary/bin//liblzma.a(LzFind.o)
 .text.Bt3_MatchFinder_Skip
                0x0000000000000000       0xaa ./elibrary/bin//liblzma.a(LzFind.o)
 .text.Bt2_MatchFinder_Skip
                0x0000000000000000       0x7e ./elibrary/bin//liblzma.a(LzFind.o)
 .text.Hc4_MatchFinder_Skip
                0x0000000000000000       0xc6 ./elibrary/bin//liblzma.a(LzFind.o)
 .text.Hc4_MatchFinder_GetMatches
                0x0000000000000000      0x1a2 ./elibrary/bin//liblzma.a(LzFind.o)
 .text.GetMatchesSpec1
                0x0000000000000000      0x126 ./elibrary/bin//liblzma.a(LzFind.o)
 .text.Bt4_MatchFinder_GetMatches
                0x0000000000000000      0x1b2 ./elibrary/bin//liblzma.a(LzFind.o)
 .text.Bt3_MatchFinder_GetMatches
                0x0000000000000000      0x13c ./elibrary/bin//liblzma.a(LzFind.o)
 .text.Bt2_MatchFinder_GetMatches
                0x0000000000000000       0x88 ./elibrary/bin//liblzma.a(LzFind.o)
 .text.Bt3Zip_MatchFinder_GetMatches
                0x0000000000000000       0xa0 ./elibrary/bin//liblzma.a(LzFind.o)
 .text.Hc3Zip_MatchFinder_GetMatches
                0x0000000000000000       0xa0 ./elibrary/bin//liblzma.a(LzFind.o)
 .text.Bt3Zip_MatchFinder_Skip
                0x0000000000000000       0x96 ./elibrary/bin//liblzma.a(LzFind.o)
 .text.Hc3Zip_MatchFinder_Skip
                0x0000000000000000       0x8a ./elibrary/bin//liblzma.a(LzFind.o)
 .text.MatchFinder_CreateVTable
                0x0000000000000000       0x8c ./elibrary/bin//liblzma.a(LzFind.o)
 .debug_info    0x0000000000000000     0x23cf ./elibrary/bin//liblzma.a(LzFind.o)
 .debug_abbrev  0x0000000000000000      0x4db ./elibrary/bin//liblzma.a(LzFind.o)
 .debug_loc     0x0000000000000000     0x2494 ./elibrary/bin//liblzma.a(LzFind.o)
 .debug_aranges
                0x0000000000000000      0x220 ./elibrary/bin//liblzma.a(LzFind.o)
 .debug_line    0x0000000000000000     0x2b56 ./elibrary/bin//liblzma.a(LzFind.o)
 .debug_str     0x0000000000000000      0xe64 ./elibrary/bin//liblzma.a(LzFind.o)
 .comment       0x0000000000000000       0x33 ./elibrary/bin//liblzma.a(LzFind.o)
 .debug_frame   0x0000000000000000      0x550 ./elibrary/bin//liblzma.a(LzFind.o)
 .riscv.attributes
                0x0000000000000000       0x3d ./elibrary/bin//liblzma.a(LzFind.o)
 .text          0x0000000000000000        0x0 ./elibrary/bin//libresreader.a(LangDec.o)
 .data          0x0000000000000000        0x0 ./elibrary/bin//libresreader.a(LangDec.o)
 .bss           0x0000000000000000        0x0 ./elibrary/bin//libresreader.a(LangDec.o)
 .text.Lang_Read
                0x0000000000000000       0x34 ./elibrary/bin//libresreader.a(LangDec.o)
 .text.Lang_GetStringAddress
                0x0000000000000000       0xee ./elibrary/bin//libresreader.a(LangDec.o)
 .text          0x0000000000000000        0x0 ./elibrary/bin//libresreader.a(themedec.o)
 .data          0x0000000000000000        0x0 ./elibrary/bin//libresreader.a(themedec.o)
 .bss           0x0000000000000000        0x0 ./elibrary/bin//libresreader.a(themedec.o)
 .text.ReadRes  0x0000000000000000       0x80 ./elibrary/bin//libresreader.a(themedec.o)
 .text.GetResAddr
                0x0000000000000000       0xc8 ./elibrary/bin//libresreader.a(themedec.o)
 .text.GetResType
                0x0000000000000000       0xc4 ./elibrary/bin//libresreader.a(themedec.o)
 .rodata.ReadRes.str1.8
                0x0000000000000000       0x55 ./elibrary/bin//libresreader.a(themedec.o)
 .rodata.__func__.4990
                0x0000000000000000        0x8 ./elibrary/bin//libresreader.a(themedec.o)
 .rodata.__func__.5016
                0x0000000000000000        0xb ./elibrary/bin//libresreader.a(themedec.o)
 .rodata.__func__.5024
                0x0000000000000000        0xb ./elibrary/bin//libresreader.a(themedec.o)
 .text          0x0000000000000000        0x0 ./elibrary/bin//libminic.a(elibs_stdlib.o)
 .data          0x0000000000000000        0x0 ./elibrary/bin//libminic.a(elibs_stdlib.o)
 .bss           0x0000000000000000        0x0 ./elibrary/bin//libminic.a(elibs_stdlib.o)
 .text.eLIBs_int2str_dec
                0x0000000000000000        0xc ./elibrary/bin//libminic.a(elibs_stdlib.o)
 .text.eLIBs_uint2str_dec
                0x0000000000000000        0xc ./elibrary/bin//libminic.a(elibs_stdlib.o)
 .text.eLIBs_int2str_hex
                0x0000000000000000        0xc ./elibrary/bin//libminic.a(elibs_stdlib.o)
 .text.eLIBs_atoi
                0x0000000000000000        0xc ./elibrary/bin//libminic.a(elibs_stdlib.o)
 .text.eLIBs_toupper
                0x0000000000000000        0xc ./elibrary/bin//libminic.a(elibs_stdlib.o)
 .text.eLIBs_isspace
                0x0000000000000000        0xc ./elibrary/bin//libminic.a(elibs_stdlib.o)
 .text.eLIBs_strtol
                0x0000000000000000        0xc ./elibrary/bin//libminic.a(elibs_stdlib.o)
 .text.eLIBs_ramdom
                0x0000000000000000        0x8 ./elibrary/bin//libminic.a(elibs_stdlib.o)
 .text.eLIBs_realloc
                0x0000000000000000       0x10 ./elibrary/bin//libminic.a(elibs_stdlib.o)
 .text.eLIBs_atof
                0x0000000000000000       0x18 ./elibrary/bin//libminic.a(elibs_stdlib.o)
 .text.eLIBs_sscanf
                0x0000000000000000       0x24 ./elibrary/bin//libminic.a(elibs_stdlib.o)
 .text._exit    0x0000000000000000        0x2 ./elibrary/bin//libminic.a(elibs_stdlib.o)
 .text._write   0x0000000000000000        0x2 ./elibrary/bin//libminic.a(elibs_stdlib.o)
 .text._kill    0x0000000000000000        0x2 ./elibrary/bin//libminic.a(elibs_stdlib.o)
 .text._getpid  0x0000000000000000        0x2 ./elibrary/bin//libminic.a(elibs_stdlib.o)
 .text._sbrk    0x0000000000000000        0x2 ./elibrary/bin//libminic.a(elibs_stdlib.o)
 .text          0x0000000000000000      0x100 /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/../../../../riscv64-unknown-elf/lib/rv64imafdcxtheadc/lp64d/libc.a(lib_a-fclose.o)
 .data          0x0000000000000000        0x0 /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/../../../../riscv64-unknown-elf/lib/rv64imafdcxtheadc/lp64d/libc.a(lib_a-fclose.o)
 .bss           0x0000000000000000        0x0 /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/../../../../riscv64-unknown-elf/lib/rv64imafdcxtheadc/lp64d/libc.a(lib_a-fclose.o)
 .comment       0x0000000000000000       0x33 /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/../../../../riscv64-unknown-elf/lib/rv64imafdcxtheadc/lp64d/libc.a(lib_a-fclose.o)
 .riscv.attributes
                0x0000000000000000       0x3e /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/../../../../riscv64-unknown-elf/lib/rv64imafdcxtheadc/lp64d/libc.a(lib_a-fclose.o)
 .text          0x0000000000000000      0x250 /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/../../../../riscv64-unknown-elf/lib/rv64imafdcxtheadc/lp64d/libc.a(lib_a-fflush.o)
 .data          0x0000000000000000        0x0 /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/../../../../riscv64-unknown-elf/lib/rv64imafdcxtheadc/lp64d/libc.a(lib_a-fflush.o)
 .bss           0x0000000000000000        0x0 /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/../../../../riscv64-unknown-elf/lib/rv64imafdcxtheadc/lp64d/libc.a(lib_a-fflush.o)
 .comment       0x0000000000000000       0x33 /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/../../../../riscv64-unknown-elf/lib/rv64imafdcxtheadc/lp64d/libc.a(lib_a-fflush.o)
 .riscv.attributes
                0x0000000000000000       0x3e /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/../../../../riscv64-unknown-elf/lib/rv64imafdcxtheadc/lp64d/libc.a(lib_a-fflush.o)
 .text          0x0000000000000000      0x3a0 /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/../../../../riscv64-unknown-elf/lib/rv64imafdcxtheadc/lp64d/libc.a(lib_a-findfp.o)
 .data          0x0000000000000000        0x0 /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/../../../../riscv64-unknown-elf/lib/rv64imafdcxtheadc/lp64d/libc.a(lib_a-findfp.o)
 .bss           0x0000000000000000        0x0 /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/../../../../riscv64-unknown-elf/lib/rv64imafdcxtheadc/lp64d/libc.a(lib_a-findfp.o)
 .comment       0x0000000000000000       0x33 /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/../../../../riscv64-unknown-elf/lib/rv64imafdcxtheadc/lp64d/libc.a(lib_a-findfp.o)
 .riscv.attributes
                0x0000000000000000       0x3e /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/../../../../riscv64-unknown-elf/lib/rv64imafdcxtheadc/lp64d/libc.a(lib_a-findfp.o)
 .text          0x0000000000000000      0x110 /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/../../../../riscv64-unknown-elf/lib/rv64imafdcxtheadc/lp64d/libc.a(lib_a-fopen.o)
 .data          0x0000000000000000        0x0 /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/../../../../riscv64-unknown-elf/lib/rv64imafdcxtheadc/lp64d/libc.a(lib_a-fopen.o)
 .bss           0x0000000000000000        0x0 /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/../../../../riscv64-unknown-elf/lib/rv64imafdcxtheadc/lp64d/libc.a(lib_a-fopen.o)
 .comment       0x0000000000000000       0x33 /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/../../../../riscv64-unknown-elf/lib/rv64imafdcxtheadc/lp64d/libc.a(lib_a-fopen.o)
 .riscv.attributes
                0x0000000000000000       0x3e /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/../../../../riscv64-unknown-elf/lib/rv64imafdcxtheadc/lp64d/libc.a(lib_a-fopen.o)
 .text          0x0000000000000000       0x40 /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/../../../../riscv64-unknown-elf/lib/rv64imafdcxtheadc/lp64d/libc.a(lib_a-fseek.o)
 .data          0x0000000000000000        0x0 /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/../../../../riscv64-unknown-elf/lib/rv64imafdcxtheadc/lp64d/libc.a(lib_a-fseek.o)
 .bss           0x0000000000000000        0x0 /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/../../../../riscv64-unknown-elf/lib/rv64imafdcxtheadc/lp64d/libc.a(lib_a-fseek.o)
 .comment       0x0000000000000000       0x33 /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/../../../../riscv64-unknown-elf/lib/rv64imafdcxtheadc/lp64d/libc.a(lib_a-fseek.o)
 .riscv.attributes
                0x0000000000000000       0x3e /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/../../../../riscv64-unknown-elf/lib/rv64imafdcxtheadc/lp64d/libc.a(lib_a-fseek.o)
 .text          0x0000000000000000      0x3b0 /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/../../../../riscv64-unknown-elf/lib/rv64imafdcxtheadc/lp64d/libc.a(lib_a-fseeko.o)
 .data          0x0000000000000000        0x0 /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/../../../../riscv64-unknown-elf/lib/rv64imafdcxtheadc/lp64d/libc.a(lib_a-fseeko.o)
 .bss           0x0000000000000000        0x0 /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/../../../../riscv64-unknown-elf/lib/rv64imafdcxtheadc/lp64d/libc.a(lib_a-fseeko.o)
 .comment       0x0000000000000000       0x33 /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/../../../../riscv64-unknown-elf/lib/rv64imafdcxtheadc/lp64d/libc.a(lib_a-fseeko.o)
 .riscv.attributes
                0x0000000000000000       0x3e /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/../../../../riscv64-unknown-elf/lib/rv64imafdcxtheadc/lp64d/libc.a(lib_a-fseeko.o)
 .text          0x0000000000000000      0x150 /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/../../../../riscv64-unknown-elf/lib/rv64imafdcxtheadc/lp64d/libc.a(lib_a-fwalk.o)
 .data          0x0000000000000000        0x0 /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/../../../../riscv64-unknown-elf/lib/rv64imafdcxtheadc/lp64d/libc.a(lib_a-fwalk.o)
 .bss           0x0000000000000000        0x0 /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/../../../../riscv64-unknown-elf/lib/rv64imafdcxtheadc/lp64d/libc.a(lib_a-fwalk.o)
 .comment       0x0000000000000000       0x33 /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/../../../../riscv64-unknown-elf/lib/rv64imafdcxtheadc/lp64d/libc.a(lib_a-fwalk.o)
 .riscv.attributes
                0x0000000000000000       0x3e /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/../../../../riscv64-unknown-elf/lib/rv64imafdcxtheadc/lp64d/libc.a(lib_a-fwalk.o)
 .text          0x0000000000000000       0xc0 /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/../../../../riscv64-unknown-elf/lib/rv64imafdcxtheadc/lp64d/libc.a(lib_a-fwrite.o)
 .data          0x0000000000000000        0x0 /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/../../../../riscv64-unknown-elf/lib/rv64imafdcxtheadc/lp64d/libc.a(lib_a-fwrite.o)
 .bss           0x0000000000000000        0x0 /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/../../../../riscv64-unknown-elf/lib/rv64imafdcxtheadc/lp64d/libc.a(lib_a-fwrite.o)
 .comment       0x0000000000000000       0x33 /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/../../../../riscv64-unknown-elf/lib/rv64imafdcxtheadc/lp64d/libc.a(lib_a-fwrite.o)
 .riscv.attributes
                0x0000000000000000       0x3e /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/../../../../riscv64-unknown-elf/lib/rv64imafdcxtheadc/lp64d/libc.a(lib_a-fwrite.o)
 .text          0x0000000000000000        0x0 /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/../../../../riscv64-unknown-elf/lib/rv64imafdcxtheadc/lp64d/libc.a(lib_a-impure.o)
 .bss           0x0000000000000000        0x0 /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/../../../../riscv64-unknown-elf/lib/rv64imafdcxtheadc/lp64d/libc.a(lib_a-impure.o)
 .rodata        0x0000000000000000        0x8 /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/../../../../riscv64-unknown-elf/lib/rv64imafdcxtheadc/lp64d/libc.a(lib_a-impure.o)
 .text          0x0000000000000000      0x180 /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/../../../../riscv64-unknown-elf/lib/rv64imafdcxtheadc/lp64d/libc.a(lib_a-makebuf.o)
 .data          0x0000000000000000        0x0 /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/../../../../riscv64-unknown-elf/lib/rv64imafdcxtheadc/lp64d/libc.a(lib_a-makebuf.o)
 .bss           0x0000000000000000        0x0 /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/../../../../riscv64-unknown-elf/lib/rv64imafdcxtheadc/lp64d/libc.a(lib_a-makebuf.o)
 .comment       0x0000000000000000       0x33 /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/../../../../riscv64-unknown-elf/lib/rv64imafdcxtheadc/lp64d/libc.a(lib_a-makebuf.o)
 .riscv.attributes
                0x0000000000000000       0x3e /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/../../../../riscv64-unknown-elf/lib/rv64imafdcxtheadc/lp64d/libc.a(lib_a-makebuf.o)
 .data          0x0000000000000000        0x0 /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/../../../../riscv64-unknown-elf/lib/rv64imafdcxtheadc/lp64d/libc.a(lib_a-memcpy.o)
 .bss           0x0000000000000000        0x0 /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/../../../../riscv64-unknown-elf/lib/rv64imafdcxtheadc/lp64d/libc.a(lib_a-memcpy.o)
 .text          0x0000000000000000       0xd2 /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/../../../../riscv64-unknown-elf/lib/rv64imafdcxtheadc/lp64d/libc.a(lib_a-memmove-stub.o)
 .data          0x0000000000000000        0x0 /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/../../../../riscv64-unknown-elf/lib/rv64imafdcxtheadc/lp64d/libc.a(lib_a-memmove-stub.o)
 .bss           0x0000000000000000        0x0 /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/../../../../riscv64-unknown-elf/lib/rv64imafdcxtheadc/lp64d/libc.a(lib_a-memmove-stub.o)
 .comment       0x0000000000000000       0x33 /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/../../../../riscv64-unknown-elf/lib/rv64imafdcxtheadc/lp64d/libc.a(lib_a-memmove-stub.o)
 .riscv.attributes
                0x0000000000000000       0x3e /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/../../../../riscv64-unknown-elf/lib/rv64imafdcxtheadc/lp64d/libc.a(lib_a-memmove-stub.o)
 .data          0x0000000000000000        0x0 /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/../../../../riscv64-unknown-elf/lib/rv64imafdcxtheadc/lp64d/libc.a(lib_a-memset.o)
 .bss           0x0000000000000000        0x0 /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/../../../../riscv64-unknown-elf/lib/rv64imafdcxtheadc/lp64d/libc.a(lib_a-memset.o)
 .text          0x0000000000000000      0x1c0 /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/../../../../riscv64-unknown-elf/lib/rv64imafdcxtheadc/lp64d/libc.a(lib_a-refill.o)
 .data          0x0000000000000000        0x0 /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/../../../../riscv64-unknown-elf/lib/rv64imafdcxtheadc/lp64d/libc.a(lib_a-refill.o)
 .bss           0x0000000000000000        0x0 /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/../../../../riscv64-unknown-elf/lib/rv64imafdcxtheadc/lp64d/libc.a(lib_a-refill.o)
 .comment       0x0000000000000000       0x33 /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/../../../../riscv64-unknown-elf/lib/rv64imafdcxtheadc/lp64d/libc.a(lib_a-refill.o)
 .riscv.attributes
                0x0000000000000000       0x3e /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/../../../../riscv64-unknown-elf/lib/rv64imafdcxtheadc/lp64d/libc.a(lib_a-refill.o)
 .text          0x0000000000000000      0x140 /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/../../../../riscv64-unknown-elf/lib/rv64imafdcxtheadc/lp64d/libc.a(lib_a-stdio.o)
 .data          0x0000000000000000        0x0 /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/../../../../riscv64-unknown-elf/lib/rv64imafdcxtheadc/lp64d/libc.a(lib_a-stdio.o)
 .bss           0x0000000000000000        0x0 /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/../../../../riscv64-unknown-elf/lib/rv64imafdcxtheadc/lp64d/libc.a(lib_a-stdio.o)
 .comment       0x0000000000000000       0x33 /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/../../../../riscv64-unknown-elf/lib/rv64imafdcxtheadc/lp64d/libc.a(lib_a-stdio.o)
 .riscv.attributes
                0x0000000000000000       0x3e /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/../../../../riscv64-unknown-elf/lib/rv64imafdcxtheadc/lp64d/libc.a(lib_a-stdio.o)
 .data          0x0000000000000000        0x0 /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/../../../../riscv64-unknown-elf/lib/rv64imafdcxtheadc/lp64d/libc.a(lib_a-strcpy.o)
 .bss           0x0000000000000000        0x0 /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/../../../../riscv64-unknown-elf/lib/rv64imafdcxtheadc/lp64d/libc.a(lib_a-strcpy.o)
 .data          0x0000000000000000        0x0 /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/../../../../riscv64-unknown-elf/lib/rv64imafdcxtheadc/lp64d/libc.a(lib_a-strncpy.o)
 .bss           0x0000000000000000        0x0 /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/../../../../riscv64-unknown-elf/lib/rv64imafdcxtheadc/lp64d/libc.a(lib_a-strncpy.o)
 .data          0x0000000000000000        0x0 /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/../../../../riscv64-unknown-elf/lib/rv64imafdcxtheadc/lp64d/libc.a(lib_a-sysclose.o)
 .bss           0x0000000000000000        0x0 /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/../../../../riscv64-unknown-elf/lib/rv64imafdcxtheadc/lp64d/libc.a(lib_a-sysclose.o)
 .data          0x0000000000000000        0x0 /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/../../../../riscv64-unknown-elf/lib/rv64imafdcxtheadc/lp64d/libc.a(lib_a-sysopen.o)
 .bss           0x0000000000000000        0x0 /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/../../../../riscv64-unknown-elf/lib/rv64imafdcxtheadc/lp64d/libc.a(lib_a-sysopen.o)
 .text          0x0000000000000000       0xb0 /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/../../../../riscv64-unknown-elf/lib/rv64imafdcxtheadc/lp64d/libc.a(lib_a-flags.o)
 .data          0x0000000000000000        0x0 /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/../../../../riscv64-unknown-elf/lib/rv64imafdcxtheadc/lp64d/libc.a(lib_a-flags.o)
 .bss           0x0000000000000000        0x0 /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/../../../../riscv64-unknown-elf/lib/rv64imafdcxtheadc/lp64d/libc.a(lib_a-flags.o)
 .comment       0x0000000000000000       0x33 /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/../../../../riscv64-unknown-elf/lib/rv64imafdcxtheadc/lp64d/libc.a(lib_a-flags.o)
 .riscv.attributes
                0x0000000000000000       0x3e /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/../../../../riscv64-unknown-elf/lib/rv64imafdcxtheadc/lp64d/libc.a(lib_a-flags.o)
 .text          0x0000000000000000      0x3c0 /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/../../../../riscv64-unknown-elf/lib/rv64imafdcxtheadc/lp64d/libc.a(lib_a-fvwrite.o)
 .data          0x0000000000000000        0x0 /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/../../../../riscv64-unknown-elf/lib/rv64imafdcxtheadc/lp64d/libc.a(lib_a-fvwrite.o)
 .bss           0x0000000000000000        0x0 /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/../../../../riscv64-unknown-elf/lib/rv64imafdcxtheadc/lp64d/libc.a(lib_a-fvwrite.o)
 .comment       0x0000000000000000       0x33 /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/../../../../riscv64-unknown-elf/lib/rv64imafdcxtheadc/lp64d/libc.a(lib_a-fvwrite.o)
 .riscv.attributes
                0x0000000000000000       0x3e /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/../../../../riscv64-unknown-elf/lib/rv64imafdcxtheadc/lp64d/libc.a(lib_a-fvwrite.o)
 .text          0x0000000000000000       0xd0 /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/../../../../riscv64-unknown-elf/lib/rv64imafdcxtheadc/lp64d/libc.a(lib_a-memchr.o)
 .data          0x0000000000000000        0x0 /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/../../../../riscv64-unknown-elf/lib/rv64imafdcxtheadc/lp64d/libc.a(lib_a-memchr.o)
 .bss           0x0000000000000000        0x0 /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/../../../../riscv64-unknown-elf/lib/rv64imafdcxtheadc/lp64d/libc.a(lib_a-memchr.o)
 .rodata.cst8   0x0000000000000000       0x10 /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/../../../../riscv64-unknown-elf/lib/rv64imafdcxtheadc/lp64d/libc.a(lib_a-memchr.o)
 .comment       0x0000000000000000       0x33 /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/../../../../riscv64-unknown-elf/lib/rv64imafdcxtheadc/lp64d/libc.a(lib_a-memchr.o)
 .riscv.attributes
                0x0000000000000000       0x3e /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/../../../../riscv64-unknown-elf/lib/rv64imafdcxtheadc/lp64d/libc.a(lib_a-memchr.o)
 .text          0x0000000000000000      0x120 /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/../../../../riscv64-unknown-elf/lib/rv64imafdcxtheadc/lp64d/libc.a(lib_a-wsetup.o)
 .data          0x0000000000000000        0x0 /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/../../../../riscv64-unknown-elf/lib/rv64imafdcxtheadc/lp64d/libc.a(lib_a-wsetup.o)
 .bss           0x0000000000000000        0x0 /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/../../../../riscv64-unknown-elf/lib/rv64imafdcxtheadc/lp64d/libc.a(lib_a-wsetup.o)
 .comment       0x0000000000000000       0x33 /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/../../../../riscv64-unknown-elf/lib/rv64imafdcxtheadc/lp64d/libc.a(lib_a-wsetup.o)
 .riscv.attributes
                0x0000000000000000       0x3e /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/../../../../riscv64-unknown-elf/lib/rv64imafdcxtheadc/lp64d/libc.a(lib_a-wsetup.o)

Memory Configuration

Name             Origin             Length             Attributes
dram0_0_seg      0x0000000040000000 0x0000000004000000 xrw
dram0_1_seg      0x00000000e0000000 0x0000000010000000 xrw
dram0_2_seg      0x00000000e1f00000 0x0000000000100000 xrw
magic_info_seg   0x00000000ffff0000 0x0000000000000100 r
*default*        0x0000000000000000 0xffffffffffffffff

Linker script and memory map

LOAD livedesk/leopard/mod_desktop/desktop_api.o
LOAD livedesk/leopard/mod_desktop/mod_desktop.o
LOAD livedesk/leopard/mod_desktop/magic.o
LOAD livedesk/leopard/mod_desktop/built-in.o
LOAD livedesk/leopard/mod_desktop/functions/built-in.o
LOAD livedesk/leopard/mod_desktop/msg_srv/built-in.o
LOAD livedesk/leopard/mod_desktop/util/built-in.o
START GROUP
START GROUP
LOAD ./elibrary/bin//libsyscall.a
LOAD ./elibrary/bin//libminic.a
LOAD ./elibrary/bin//liblzma.a
LOAD ./elibrary/bin//libcharset.a
LOAD ./elibrary/bin//libmediainfo.a
LOAD ./elibrary/bin//liblvgl.a
LOAD ./elibrary/bin//libresreader.a
END GROUP
START GROUP
LOAD ./elibrary/bin//libcheck_app.a
END GROUP
LOAD /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/../../../../riscv64-unknown-elf/lib/rv64imafdcxtheadc/lp64d/libstdc++.a
LOAD /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/../../../../riscv64-unknown-elf/lib/rv64imafdcxtheadc/lp64d/libc.a
LOAD /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/rv64imafdcxtheadc/lp64d/libgcc.a
LOAD /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/../../../../riscv64-unknown-elf/lib/rv64imafdcxtheadc/lp64d/libm.a
END GROUP
                0x00000000e1f00000                __DRAM0_MOD_BASE = ORIGIN (dram0_2_seg)
                0x00000000ffff0000                __DRAM0_INF_BASE = ORIGIN (magic_info_seg)
                0x00000000e1f00000                __DRAM0_MOD_BASE = ORIGIN (dram0_2_seg)
                0x00000000ffff0000                __DRAM0_INF_BASE = ORIGIN (magic_info_seg)
                0x00000000e1f00000                . = __DRAM0_MOD_BASE

DESKTOP_API_TBL
                0x00000000e1f00000      0xe38
 *desktop_api.o(DESKTOP_API_TBL)
 DESKTOP_API_TBL
                0x00000000e1f00000      0xe38 livedesk/leopard/mod_desktop/desktop_api.o
                0x00000000e1f00000                desktop_api

.mod.text       0x00000000e1f00e38     0xce84
 *(.text)
 .text          0x00000000e1f00e38       0xd0 /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/../../../../riscv64-unknown-elf/lib/rv64imafdcxtheadc/lp64d/libc.a(lib_a-memcpy.o)
                0x00000000e1f00e38                memcpy
 .text          0x00000000e1f00f08       0xaa /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/../../../../riscv64-unknown-elf/lib/rv64imafdcxtheadc/lp64d/libc.a(lib_a-memset.o)
                0x00000000e1f00f08                memset
 *fill*         0x00000000e1f00fb2        0x6 
 .text          0x00000000e1f00fb8       0xb0 /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/../../../../riscv64-unknown-elf/lib/rv64imafdcxtheadc/lp64d/libc.a(lib_a-strcpy.o)
                0x00000000e1f00fb8                strcpy
 *fill*         0x00000000e1f01068        0x8 
 .text          0x00000000e1f01070       0xa8 /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/../../../../riscv64-unknown-elf/lib/rv64imafdcxtheadc/lp64d/libc.a(lib_a-strncpy.o)
                0x00000000e1f01070                strncpy
 *fill*         0x00000000e1f01118        0x8 
 .text          0x00000000e1f01120       0x1e /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/../../../../riscv64-unknown-elf/lib/rv64imafdcxtheadc/lp64d/libc.a(lib_a-sysclose.o)
                0x00000000e1f01120                close
 *fill*         0x00000000e1f0113e        0x2 
 .text          0x00000000e1f01140       0x3e /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/../../../../riscv64-unknown-elf/lib/rv64imafdcxtheadc/lp64d/libc.a(lib_a-sysopen.o)
                0x00000000e1f01140                open
 *(.text.*)
 .text.DESKTOP_MInit
                0x00000000e1f0117e        0x4 livedesk/leopard/mod_desktop/mod_desktop.o
                0x00000000e1f0117e                DESKTOP_MInit
 .text.DESKTOP_MExit
                0x00000000e1f01182        0x4 livedesk/leopard/mod_desktop/mod_desktop.o
                0x00000000e1f01182                DESKTOP_MExit
 .text.DESKTOP_MOpen
                0x00000000e1f01186       0x38 livedesk/leopard/mod_desktop/mod_desktop.o
                0x00000000e1f01186                DESKTOP_MOpen
 .text.DESKTOP_MClose
                0x00000000e1f011be       0x2e livedesk/leopard/mod_desktop/mod_desktop.o
                0x00000000e1f011be                DESKTOP_MClose
 .text.DESKTOP_MRead
                0x00000000e1f011ec        0x6 livedesk/leopard/mod_desktop/mod_desktop.o
                0x00000000e1f011ec                DESKTOP_MRead
 .text.DESKTOP_MWrite
                0x00000000e1f011f2        0x6 livedesk/leopard/mod_desktop/mod_desktop.o
                0x00000000e1f011f2                DESKTOP_MWrite
 .text.DESKTOP_MIoctrl
                0x00000000e1f011f8        0x4 livedesk/leopard/mod_desktop/mod_desktop.o
                0x00000000e1f011f8                DESKTOP_MIoctrl
 .text.dsk_set_hdmi_audio_gate
                0x00000000e1f011fc        0x4 livedesk/leopard/mod_desktop/functions/built-in.o
                0x00000000e1f011fc                dsk_set_hdmi_audio_gate
 .text.dsk_set_audio_if
                0x00000000e1f01200        0x4 livedesk/leopard/mod_desktop/functions/built-in.o
                0x00000000e1f01200                dsk_set_audio_if
 .text.dsk_get_audio_if
                0x00000000e1f01204        0x4 livedesk/leopard/mod_desktop/functions/built-in.o
                0x00000000e1f01204                dsk_get_audio_if
 .text.dsk_set_auto_off_time
                0x00000000e1f01208        0x2 livedesk/leopard/mod_desktop/functions/built-in.o
                0x00000000e1f01208                dsk_set_auto_off_time
 .text.dsk_power_off
                0x00000000e1f0120a        0x2 livedesk/leopard/mod_desktop/functions/built-in.o
                0x00000000e1f0120a                dsk_power_off
 .text.eq_to_bas
                0x00000000e1f0120c       0x9a livedesk/leopard/mod_desktop/functions/built-in.o
                0x00000000e1f0120c                eq_to_bas
 .text.eq_to_mid
                0x00000000e1f012a6       0x90 livedesk/leopard/mod_desktop/functions/built-in.o
                0x00000000e1f012a6                eq_to_mid
 .text.eq_to_tre
                0x00000000e1f01336       0x9a livedesk/leopard/mod_desktop/functions/built-in.o
                0x00000000e1f01336                eq_to_tre
 .text.dsk_audio_play_open
                0x00000000e1f013d0       0x24 livedesk/leopard/mod_desktop/functions/built-in.o
                0x00000000e1f013d0                dsk_audio_play_open
 .text.dsk_audio_play_close
                0x00000000e1f013f4       0x16 livedesk/leopard/mod_desktop/functions/built-in.o
                0x00000000e1f013f4                dsk_audio_play_close
 .text.dsk_audio_play_set_mute
                0x00000000e1f0140a       0x38 livedesk/leopard/mod_desktop/functions/built-in.o
                0x00000000e1f0140a                dsk_audio_play_set_mute
 .text.dsk_aux_open
                0x00000000e1f01442       0x24 livedesk/leopard/mod_desktop/functions/built-in.o
                0x00000000e1f01442                dsk_aux_open
 .text.dsk_aux_get_select_channel
                0x00000000e1f01466       0x14 livedesk/leopard/mod_desktop/functions/built-in.o
                0x00000000e1f01466                dsk_aux_get_select_channel
 .text.dsk_aux_get_volume
                0x00000000e1f0147a       0x14 livedesk/leopard/mod_desktop/functions/built-in.o
                0x00000000e1f0147a                dsk_aux_get_volume
 .text.dsk_aux_get_btphone_volume
                0x00000000e1f0148e       0x14 livedesk/leopard/mod_desktop/functions/built-in.o
                0x00000000e1f0148e                dsk_aux_get_btphone_volume
 .text.dsk_aux_get_EQ_mode
                0x00000000e1f014a2       0x14 livedesk/leopard/mod_desktop/functions/built-in.o
                0x00000000e1f014a2                dsk_aux_get_EQ_mode
 .text.dsk_aux_get_EQ_type
                0x00000000e1f014b6       0x14 livedesk/leopard/mod_desktop/functions/built-in.o
                0x00000000e1f014b6                dsk_aux_get_EQ_type
 .text.dsk_aux_get_mute_status
                0x00000000e1f014ca       0x14 livedesk/leopard/mod_desktop/functions/built-in.o
                0x00000000e1f014ca                dsk_aux_get_mute_status
 .text.dsk_aux_get_bass
                0x00000000e1f014de       0x1c livedesk/leopard/mod_desktop/functions/built-in.o
                0x00000000e1f014de                dsk_aux_get_bass
 .text.dsk_aux_get_treble
                0x00000000e1f014fa       0x1c livedesk/leopard/mod_desktop/functions/built-in.o
                0x00000000e1f014fa                dsk_aux_get_treble
 .text.dsk_aux_get_middle
                0x00000000e1f01516        0x4 livedesk/leopard/mod_desktop/functions/built-in.o
                0x00000000e1f01516                dsk_aux_get_middle
 .text.dsk_aux_get_bass_by_eq
                0x00000000e1f0151a        0x4 livedesk/leopard/mod_desktop/functions/built-in.o
                0x00000000e1f0151a                dsk_aux_get_bass_by_eq
 .text.dsk_aux_get_middle_by_eq
                0x00000000e1f0151e        0x4 livedesk/leopard/mod_desktop/functions/built-in.o
                0x00000000e1f0151e                dsk_aux_get_middle_by_eq
 .text.dsk_aux_get_treble_by_eq
                0x00000000e1f01522        0x4 livedesk/leopard/mod_desktop/functions/built-in.o
                0x00000000e1f01522                dsk_aux_get_treble_by_eq
 .text.dsk_aux_get_balance_RL_speaker
                0x00000000e1f01526       0x14 livedesk/leopard/mod_desktop/functions/built-in.o
                0x00000000e1f01526                dsk_aux_get_balance_RL_speaker
 .text.dsk_aux_get_balance_FAD_speaker
                0x00000000e1f0153a       0x14 livedesk/leopard/mod_desktop/functions/built-in.o
                0x00000000e1f0153a                dsk_aux_get_balance_FAD_speaker
 .text.dsk_aux_get_channel_loud
                0x00000000e1f0154e       0x14 livedesk/leopard/mod_desktop/functions/built-in.o
                0x00000000e1f0154e                dsk_aux_get_channel_loud
 .text.dsk_aux_get_xbass
                0x00000000e1f01562        0x4 livedesk/leopard/mod_desktop/functions/built-in.o
                0x00000000e1f01562                dsk_aux_get_xbass
 .text.dsk_aux_get_dbss
                0x00000000e1f01566       0x14 livedesk/leopard/mod_desktop/functions/built-in.o
                0x00000000e1f01566                dsk_aux_get_dbss
 .text.dsk_aux_get_subwoofer_onoff
                0x00000000e1f0157a       0x14 livedesk/leopard/mod_desktop/functions/built-in.o
                0x00000000e1f0157a                dsk_aux_get_subwoofer_onoff
 .text.dsk_aux_get_subwoofer_volume
                0x00000000e1f0158e       0x14 livedesk/leopard/mod_desktop/functions/built-in.o
                0x00000000e1f0158e                dsk_aux_get_subwoofer_volume
 .text.dsk_aux_get_subwoofer_cutoff
                0x00000000e1f015a2       0x14 livedesk/leopard/mod_desktop/functions/built-in.o
                0x00000000e1f015a2                dsk_aux_get_subwoofer_cutoff
 .text.dsk_aux_get_rbs_onoff
                0x00000000e1f015b6       0x14 livedesk/leopard/mod_desktop/functions/built-in.o
                0x00000000e1f015b6                dsk_aux_get_rbs_onoff
 .text.dsk_aux_get_rbs_cutoff
                0x00000000e1f015ca       0x14 livedesk/leopard/mod_desktop/functions/built-in.o
                0x00000000e1f015ca                dsk_aux_get_rbs_cutoff
 .text.dsk_aux_get_rbs_volume
                0x00000000e1f015de       0x14 livedesk/leopard/mod_desktop/functions/built-in.o
                0x00000000e1f015de                dsk_aux_get_rbs_volume
 .text.dsk_aux_get_mix_mode
                0x00000000e1f015f2       0x14 livedesk/leopard/mod_desktop/functions/built-in.o
                0x00000000e1f015f2                dsk_aux_get_mix_mode
 .text.dsk_aux_get_mix_level
                0x00000000e1f01606       0x14 livedesk/leopard/mod_desktop/functions/built-in.o
                0x00000000e1f01606                dsk_aux_get_mix_level
 .text.dsk_aux_get_iheadset_onoff
                0x00000000e1f0161a       0x14 livedesk/leopard/mod_desktop/functions/built-in.o
                0x00000000e1f0161a                dsk_aux_get_iheadset_onoff
 .text.dsk_aux_get_iheadset_volume
                0x00000000e1f0162e       0x14 livedesk/leopard/mod_desktop/functions/built-in.o
                0x00000000e1f0162e                dsk_aux_get_iheadset_volume
 .text.dsk_aux_init
                0x00000000e1f01642       0x46 livedesk/leopard/mod_desktop/functions/built-in.o
                0x00000000e1f01642                dsk_aux_init
 .text.dsk_aux_reset
                0x00000000e1f01688      0x3e2 livedesk/leopard/mod_desktop/functions/built-in.o
                0x00000000e1f01688                dsk_aux_reset
 .text.dsk_aux_item_reset
                0x00000000e1f01a6a      0x32c livedesk/leopard/mod_desktop/functions/built-in.o
                0x00000000e1f01a6a                dsk_aux_item_reset
 .text.dsk_aux_mcu_open
                0x00000000e1f01d96       0x24 livedesk/leopard/mod_desktop/functions/built-in.o
                0x00000000e1f01d96                dsk_aux_mcu_open
 .text.dsk_aux_select_channel
                0x00000000e1f01dba      0x244 livedesk/leopard/mod_desktop/functions/built-in.o
                0x00000000e1f01dba                dsk_aux_select_channel
 .text.dsk_aux_select_channel_with_mute
                0x00000000e1f01ffe       0x48 livedesk/leopard/mod_desktop/functions/built-in.o
                0x00000000e1f01ffe                dsk_aux_select_channel_with_mute
 .text.dsk_aux_set_volume
                0x00000000e1f02046       0x50 livedesk/leopard/mod_desktop/functions/built-in.o
                0x00000000e1f02046                dsk_aux_set_volume
 .text.dsk_aux_set_btphone_volume
                0x00000000e1f02096       0x52 livedesk/leopard/mod_desktop/functions/built-in.o
                0x00000000e1f02096                dsk_aux_set_btphone_volume
 .text.dsk_aux_set_EQ
                0x00000000e1f020e8       0x70 livedesk/leopard/mod_desktop/functions/built-in.o
                0x00000000e1f020e8                dsk_aux_set_EQ
 .text.dsk_aux_set_EQ_ex
                0x00000000e1f02158      0x13c livedesk/leopard/mod_desktop/functions/built-in.o
                0x00000000e1f02158                dsk_aux_set_EQ_ex
 .text.dsk_aux_set_EQ_from_user
                0x00000000e1f02294       0x72 livedesk/leopard/mod_desktop/functions/built-in.o
                0x00000000e1f02294                dsk_aux_set_EQ_from_user
 .text.dsk_aux_set_EQ_from_user_ex
                0x00000000e1f02306      0x14c livedesk/leopard/mod_desktop/functions/built-in.o
                0x00000000e1f02306                dsk_aux_set_EQ_from_user_ex
 .text.dsk_aux_set_btphone_EQ_off_mode
                0x00000000e1f02452       0x38 livedesk/leopard/mod_desktop/functions/built-in.o
                0x00000000e1f02452                dsk_aux_set_btphone_EQ_off_mode
 .text.dsk_aux_set_bass
                0x00000000e1f0248a       0x30 livedesk/leopard/mod_desktop/functions/built-in.o
                0x00000000e1f0248a                dsk_aux_set_bass
 .text.dsk_aux_set_treble
                0x00000000e1f024ba       0x30 livedesk/leopard/mod_desktop/functions/built-in.o
                0x00000000e1f024ba                dsk_aux_set_treble
 .text.dsk_aux_balance_RL_speaker
                0x00000000e1f024ea       0x40 livedesk/leopard/mod_desktop/functions/built-in.o
                0x00000000e1f024ea                dsk_aux_balance_RL_speaker
 .text.dsk_aux_balance_FAD_speaker
                0x00000000e1f0252a       0x40 livedesk/leopard/mod_desktop/functions/built-in.o
                0x00000000e1f0252a                dsk_aux_balance_FAD_speaker
 .text.dsk_aux_set_channel_loud
                0x00000000e1f0256a       0x56 livedesk/leopard/mod_desktop/functions/built-in.o
                0x00000000e1f0256a                dsk_aux_set_channel_loud
 .text.dsk_aux_set_mute
                0x00000000e1f025c0       0x44 livedesk/leopard/mod_desktop/functions/built-in.o
                0x00000000e1f025c0                dsk_aux_set_mute
 .text.dsk_aux_set_i2s_input_format_1_2_3
                0x00000000e1f02604       0x46 livedesk/leopard/mod_desktop/functions/built-in.o
                0x00000000e1f02604                dsk_aux_set_i2s_input_format_1_2_3
 .text.dsk_aux_set_i2s_input_format_4
                0x00000000e1f0264a       0x46 livedesk/leopard/mod_desktop/functions/built-in.o
                0x00000000e1f0264a                dsk_aux_set_i2s_input_format_4
 .text.dsk_aux_set_music_zone
                0x00000000e1f02690      0x126 livedesk/leopard/mod_desktop/functions/built-in.o
                0x00000000e1f02690                dsk_aux_set_music_zone
 .text.dsk_aux_set_front_rear_mute
                0x00000000e1f027b6       0x4a livedesk/leopard/mod_desktop/functions/built-in.o
                0x00000000e1f027b6                dsk_aux_set_front_rear_mute
 .text.dsk_aux_set_middle
                0x00000000e1f02800       0x30 livedesk/leopard/mod_desktop/functions/built-in.o
                0x00000000e1f02800                dsk_aux_set_middle
 .text.dsk_aux_set_dynamic
                0x00000000e1f02830       0x58 livedesk/leopard/mod_desktop/functions/built-in.o
                0x00000000e1f02830                dsk_aux_set_dynamic
 .text.dsk_aux_set_delay_ms
                0x00000000e1f02888       0x6a livedesk/leopard/mod_desktop/functions/built-in.o
                0x00000000e1f02888                dsk_aux_set_delay_ms
 .text.dsk_aux_set_delay_level
                0x00000000e1f028f2      0x104 livedesk/leopard/mod_desktop/functions/built-in.o
                0x00000000e1f028f2                dsk_aux_set_delay_level
 .text.dsk_aux_set_xbass
                0x00000000e1f029f6       0xd0 livedesk/leopard/mod_desktop/functions/built-in.o
                0x00000000e1f029f6                dsk_aux_set_xbass
 .text.dsk_aux_set_xbass_by_level
                0x00000000e1f02ac6       0x66 livedesk/leopard/mod_desktop/functions/built-in.o
                0x00000000e1f02ac6                dsk_aux_set_xbass_by_level
 .text.dsk_aux_set_dbss
                0x00000000e1f02b2c       0xf8 livedesk/leopard/mod_desktop/functions/built-in.o
                0x00000000e1f02b2c                dsk_aux_set_dbss
 .text.dsk_aux_set_EQ_mode
                0x00000000e1f02c24       0xcc livedesk/leopard/mod_desktop/functions/built-in.o
                0x00000000e1f02c24                dsk_aux_set_EQ_mode
 .text.dsk_aux_reset_EQ_mode
                0x00000000e1f02cf0       0xd2 livedesk/leopard/mod_desktop/functions/built-in.o
                0x00000000e1f02cf0                dsk_aux_reset_EQ_mode
 .text.dsk_aux_set_subwoofer_volume
                0x00000000e1f02dc2       0x56 livedesk/leopard/mod_desktop/functions/built-in.o
                0x00000000e1f02dc2                dsk_aux_set_subwoofer_volume
 .text.dsk_aux_set_subwoofer_phase
                0x00000000e1f02e18       0x44 livedesk/leopard/mod_desktop/functions/built-in.o
                0x00000000e1f02e18                dsk_aux_set_subwoofer_phase
 .text.mute_subwoofer
                0x00000000e1f02e5c       0x38 livedesk/leopard/mod_desktop/functions/built-in.o
                0x00000000e1f02e5c                mute_subwoofer
 .text.dsk_aux_set_subwoofer_onoff
                0x00000000e1f02e94       0x48 livedesk/leopard/mod_desktop/functions/built-in.o
                0x00000000e1f02e94                dsk_aux_set_subwoofer_onoff
 .text.dsk_aux_set_rbs_volume
                0x00000000e1f02edc       0x56 livedesk/leopard/mod_desktop/functions/built-in.o
                0x00000000e1f02edc                dsk_aux_set_rbs_volume
 .text.dsk_aux_set_rbs_onoff
                0x00000000e1f02f32       0x48 livedesk/leopard/mod_desktop/functions/built-in.o
                0x00000000e1f02f32                dsk_aux_set_rbs_onoff
 .text.dsk_aux_set_subwoofer_cutoff
                0x00000000e1f02f7a       0x46 livedesk/leopard/mod_desktop/functions/built-in.o
                0x00000000e1f02f7a                dsk_aux_set_subwoofer_cutoff
 .text.dsk_aux_set_rbs_cutoff
                0x00000000e1f02fc0       0x46 livedesk/leopard/mod_desktop/functions/built-in.o
                0x00000000e1f02fc0                dsk_aux_set_rbs_cutoff
 .text.dsk_aux_set_mix_on_off
                0x00000000e1f03006       0x46 livedesk/leopard/mod_desktop/functions/built-in.o
                0x00000000e1f03006                dsk_aux_set_mix_on_off
 .text.dsk_aux_set_mix_mode
                0x00000000e1f0304c       0x46 livedesk/leopard/mod_desktop/functions/built-in.o
                0x00000000e1f0304c                dsk_aux_set_mix_mode
 .text.dsk_aux_set_mix_level
                0x00000000e1f03092       0x46 livedesk/leopard/mod_desktop/functions/built-in.o
                0x00000000e1f03092                dsk_aux_set_mix_level
 .text.dsk_aux_set_bt_module_volume
                0x00000000e1f030d8        0x2 livedesk/leopard/mod_desktop/functions/built-in.o
                0x00000000e1f030d8                dsk_aux_set_bt_module_volume
 .text.dsk_aux_set_iheadset_volume
                0x00000000e1f030da       0x1c livedesk/leopard/mod_desktop/functions/built-in.o
                0x00000000e1f030da                dsk_aux_set_iheadset_volume
 .text.dsk_aux_set_double_trak
                0x00000000e1f030f6       0x36 livedesk/leopard/mod_desktop/functions/built-in.o
                0x00000000e1f030f6                dsk_aux_set_double_trak
 .text.dsk_aux_set_iheadset_onoff
                0x00000000e1f0312c        0x4 livedesk/leopard/mod_desktop/functions/built-in.o
                0x00000000e1f0312c                dsk_aux_set_iheadset_onoff
 .text.dsk_btmoudel_open
                0x00000000e1f03130       0x24 livedesk/leopard/mod_desktop/functions/built-in.o
                0x00000000e1f03130                dsk_btmoudel_open
 .text.dsk_get_bluetooth_a2dp_connect_status
                0x00000000e1f03154       0x14 livedesk/leopard/mod_desktop/functions/built-in.o
                0x00000000e1f03154                dsk_get_bluetooth_a2dp_connect_status
 .text.dsk_get_bluetooth_hf_connect_status
                0x00000000e1f03168       0x14 livedesk/leopard/mod_desktop/functions/built-in.o
                0x00000000e1f03168                dsk_get_bluetooth_hf_connect_status
 .text.dsk_get_bluetooth_call_status
                0x00000000e1f0317c       0x14 livedesk/leopard/mod_desktop/functions/built-in.o
                0x00000000e1f0317c                dsk_get_bluetooth_call_status
 .text.dsk_get_bluetooth_phone_talking_time
                0x00000000e1f03190       0x14 livedesk/leopard/mod_desktop/functions/built-in.o
                0x00000000e1f03190                dsk_get_bluetooth_phone_talking_time
 .text.dsk_get_bluetooth_phone_transfer_status
                0x00000000e1f031a4       0x14 livedesk/leopard/mod_desktop/functions/built-in.o
                0x00000000e1f031a4                dsk_get_bluetooth_phone_transfer_status
 .text.dsk_get_bluetooth_phone_book_memory_total_num
                0x00000000e1f031b8       0x18 livedesk/leopard/mod_desktop/functions/built-in.o
                0x00000000e1f031b8                dsk_get_bluetooth_phone_book_memory_total_num
 .text.dsk_get_bluetooth_phonenum_book_memory_index
                0x00000000e1f031d0       0x18 livedesk/leopard/mod_desktop/functions/built-in.o
                0x00000000e1f031d0                dsk_get_bluetooth_phonenum_book_memory_index
 .text.dsk_get_bluetooth_phone_book_memory_name
                0x00000000e1f031e8       0x3e livedesk/leopard/mod_desktop/functions/built-in.o
                0x00000000e1f031e8                dsk_get_bluetooth_phone_book_memory_name
 .text.dsk_get_bluetooth_phone_book_memory_phonenum
                0x00000000e1f03226       0x38 livedesk/leopard/mod_desktop/functions/built-in.o
                0x00000000e1f03226                dsk_get_bluetooth_phone_book_memory_phonenum
 .text.dsk_get_bluetooth_phone_history_out_year
                0x00000000e1f0325e       0x26 livedesk/leopard/mod_desktop/functions/built-in.o
                0x00000000e1f0325e                dsk_get_bluetooth_phone_history_out_year
 .text.dsk_get_bluetooth_phone_history_out_month
                0x00000000e1f03284       0x20 livedesk/leopard/mod_desktop/functions/built-in.o
                0x00000000e1f03284                dsk_get_bluetooth_phone_history_out_month
 .text.dsk_get_bluetooth_phone_history_out_day
                0x00000000e1f032a4       0x20 livedesk/leopard/mod_desktop/functions/built-in.o
                0x00000000e1f032a4                dsk_get_bluetooth_phone_history_out_day
 .text.dsk_get_bluetooth_phone_history_out_hour
                0x00000000e1f032c4       0x20 livedesk/leopard/mod_desktop/functions/built-in.o
                0x00000000e1f032c4                dsk_get_bluetooth_phone_history_out_hour
 .text.dsk_get_bluetooth_phone_history_out_min
                0x00000000e1f032e4       0x20 livedesk/leopard/mod_desktop/functions/built-in.o
                0x00000000e1f032e4                dsk_get_bluetooth_phone_history_out_min
 .text.dsk_get_bluetooth_phone_history_out_sec
                0x00000000e1f03304       0x20 livedesk/leopard/mod_desktop/functions/built-in.o
                0x00000000e1f03304                dsk_get_bluetooth_phone_history_out_sec
 .text.dsk_get_bluetooth_play_profile
                0x00000000e1f03324       0x14 livedesk/leopard/mod_desktop/functions/built-in.o
                0x00000000e1f03324                dsk_get_bluetooth_play_profile
 .text.dsk_get_bluetooth_total_song_number
                0x00000000e1f03338       0x18 livedesk/leopard/mod_desktop/functions/built-in.o
                0x00000000e1f03338                dsk_get_bluetooth_total_song_number
 .text.dsk_get_bluetooth_song_index
                0x00000000e1f03350       0x18 livedesk/leopard/mod_desktop/functions/built-in.o
                0x00000000e1f03350                dsk_get_bluetooth_song_index
 .text.dsk_get_bluetooth_folder_index
                0x00000000e1f03368       0x18 livedesk/leopard/mod_desktop/functions/built-in.o
                0x00000000e1f03368                dsk_get_bluetooth_folder_index
 .text.dsk_get_bluetooth_play_time
                0x00000000e1f03380       0x18 livedesk/leopard/mod_desktop/functions/built-in.o
                0x00000000e1f03380                dsk_get_bluetooth_play_time
 .text.dsk_get_bluetooth_total_time
                0x00000000e1f03398       0x18 livedesk/leopard/mod_desktop/functions/built-in.o
                0x00000000e1f03398                dsk_get_bluetooth_total_time
 .text.dsk_get_bluetooth_encoding_type
                0x00000000e1f033b0       0x32 livedesk/leopard/mod_desktop/functions/built-in.o
                0x00000000e1f033b0                dsk_get_bluetooth_encoding_type
 .text.dsk_get_bluetooth_id3_status
                0x00000000e1f033e2       0x18 livedesk/leopard/mod_desktop/functions/built-in.o
                0x00000000e1f033e2                dsk_get_bluetooth_id3_status
 .text.dsk_get_bluetooth_get_id3
                0x00000000e1f033fa       0x6a livedesk/leopard/mod_desktop/functions/built-in.o
                0x00000000e1f033fa                dsk_get_bluetooth_get_id3
 .text.dsk_get_bluetooth_repeat_mode
                0x00000000e1f03464       0x18 livedesk/leopard/mod_desktop/functions/built-in.o
                0x00000000e1f03464                dsk_get_bluetooth_repeat_mode
 .text.dsk_get_bluetooth_random_mode
                0x00000000e1f0347c       0x18 livedesk/leopard/mod_desktop/functions/built-in.o
                0x00000000e1f0347c                dsk_get_bluetooth_random_mode
 .text.dsk_get_bluetooth_play_status
                0x00000000e1f03494       0x18 livedesk/leopard/mod_desktop/functions/built-in.o
                0x00000000e1f03494                dsk_get_bluetooth_play_status
 .text.dsk_get_bluetooth_usb_total_song_number
                0x00000000e1f034ac       0x18 livedesk/leopard/mod_desktop/functions/built-in.o
                0x00000000e1f034ac                dsk_get_bluetooth_usb_total_song_number
 .text.dsk_get_bluetooth_usb_song_index
                0x00000000e1f034c4       0x18 livedesk/leopard/mod_desktop/functions/built-in.o
                0x00000000e1f034c4                dsk_get_bluetooth_usb_song_index
 .text.dsk_get_bluetooth_usb_folder_index
                0x00000000e1f034dc       0x18 livedesk/leopard/mod_desktop/functions/built-in.o
                0x00000000e1f034dc                dsk_get_bluetooth_usb_folder_index
 .text.dsk_get_bluetooth_usb_play_time
                0x00000000e1f034f4       0x18 livedesk/leopard/mod_desktop/functions/built-in.o
                0x00000000e1f034f4                dsk_get_bluetooth_usb_play_time
 .text.dsk_get_bluetooth_usb_total_time
                0x00000000e1f0350c       0x18 livedesk/leopard/mod_desktop/functions/built-in.o
                0x00000000e1f0350c                dsk_get_bluetooth_usb_total_time
 .text.dsk_get_bluetooth_usb_encoding_type
                0x00000000e1f03524       0x32 livedesk/leopard/mod_desktop/functions/built-in.o
                0x00000000e1f03524                dsk_get_bluetooth_usb_encoding_type
 .text.dsk_get_bluetooth_usb_id3_status
                0x00000000e1f03556       0x18 livedesk/leopard/mod_desktop/functions/built-in.o
                0x00000000e1f03556                dsk_get_bluetooth_usb_id3_status
 .text.dsk_get_bluetooth_usb_get_id3
                0x00000000e1f0356e       0x6a livedesk/leopard/mod_desktop/functions/built-in.o
                0x00000000e1f0356e                dsk_get_bluetooth_usb_get_id3
 .text.dsk_get_bluetooth_usb_repeat_mode
                0x00000000e1f035d8       0x18 livedesk/leopard/mod_desktop/functions/built-in.o
                0x00000000e1f035d8                dsk_get_bluetooth_usb_repeat_mode
 .text.dsk_get_bluetooth_usb_random_mode
                0x00000000e1f035f0       0x18 livedesk/leopard/mod_desktop/functions/built-in.o
                0x00000000e1f035f0                dsk_get_bluetooth_usb_random_mode
 .text.dsk_get_bluetooth_usb_play_status
                0x00000000e1f03608       0x18 livedesk/leopard/mod_desktop/functions/built-in.o
                0x00000000e1f03608                dsk_get_bluetooth_usb_play_status
 .text.dsk_get_bluetooth_upgrade_file_found
                0x00000000e1f03620       0x20 livedesk/leopard/mod_desktop/functions/built-in.o
                0x00000000e1f03620                dsk_get_bluetooth_upgrade_file_found
 .text.dsk_get_bluetooth_upgrade_file_size
                0x00000000e1f03640       0x18 livedesk/leopard/mod_desktop/functions/built-in.o
                0x00000000e1f03640                dsk_get_bluetooth_upgrade_file_size
 .text.dsk_get_bluetooth_upgrade_file_pro
                0x00000000e1f03658       0x20 livedesk/leopard/mod_desktop/functions/built-in.o
                0x00000000e1f03658                dsk_get_bluetooth_upgrade_file_pro
 .text.dsk_get_bluetooth_upgrade_ask
                0x00000000e1f03678       0x18 livedesk/leopard/mod_desktop/functions/built-in.o
                0x00000000e1f03678                dsk_get_bluetooth_upgrade_ask
 .text.dsk_get_bluetooth_upgrade_success
                0x00000000e1f03690       0x18 livedesk/leopard/mod_desktop/functions/built-in.o
                0x00000000e1f03690                dsk_get_bluetooth_upgrade_success
 .text.dsk_get_bluetooth_usb_device_exist
                0x00000000e1f036a8       0x18 livedesk/leopard/mod_desktop/functions/built-in.o
                0x00000000e1f036a8                dsk_get_bluetooth_usb_device_exist
 .text.dsk_get_usb_ipod_status
                0x00000000e1f036c0       0x18 livedesk/leopard/mod_desktop/functions/built-in.o
                0x00000000e1f036c0                dsk_get_usb_ipod_status
 .text.dsk_get_usb_ipod_category_number
                0x00000000e1f036d8       0x18 livedesk/leopard/mod_desktop/functions/built-in.o
                0x00000000e1f036d8                dsk_get_usb_ipod_category_number
 .text.dsk_get_usb_ipod_previous_layer_num
                0x00000000e1f036f0       0x18 livedesk/leopard/mod_desktop/functions/built-in.o
                0x00000000e1f036f0                dsk_get_usb_ipod_previous_layer_num
 .text.dsk_get_usb_ipod_song_layer_yes_or_no
                0x00000000e1f03708       0x18 livedesk/leopard/mod_desktop/functions/built-in.o
                0x00000000e1f03708                dsk_get_usb_ipod_song_layer_yes_or_no
 .text.dsk_get_usb_ipod_previous_layer_song_layer_yes_or_no
                0x00000000e1f03720       0x18 livedesk/leopard/mod_desktop/functions/built-in.o
                0x00000000e1f03720                dsk_get_usb_ipod_previous_layer_song_layer_yes_or_no
 .text.dsk_get_usb_ipod_name_total_number
                0x00000000e1f03738       0x18 livedesk/leopard/mod_desktop/functions/built-in.o
                0x00000000e1f03738                dsk_get_usb_ipod_name_total_number
 .text.dsk_get_usb_ipod_name_current_number
                0x00000000e1f03750       0x18 livedesk/leopard/mod_desktop/functions/built-in.o
                0x00000000e1f03750                dsk_get_usb_ipod_name_current_number
 .text.dsk_get_usb_ipod_folder_name
                0x00000000e1f03768       0x30 livedesk/leopard/mod_desktop/functions/built-in.o
                0x00000000e1f03768                dsk_get_usb_ipod_folder_name
 .text.dsk_get_bt_avrcp_status
                0x00000000e1f03798       0x18 livedesk/leopard/mod_desktop/functions/built-in.o
                0x00000000e1f03798                dsk_get_bt_avrcp_status
 .text.dsk_get_bt_avrcp_category_number
                0x00000000e1f037b0       0x18 livedesk/leopard/mod_desktop/functions/built-in.o
                0x00000000e1f037b0                dsk_get_bt_avrcp_category_number
 .text.dsk_get_bt_avrcp_previous_layer_num
                0x00000000e1f037c8       0x18 livedesk/leopard/mod_desktop/functions/built-in.o
                0x00000000e1f037c8                dsk_get_bt_avrcp_previous_layer_num
 .text.dsk_get_bt_avrcp_song_layer_yes_or_no
                0x00000000e1f037e0       0x18 livedesk/leopard/mod_desktop/functions/built-in.o
                0x00000000e1f037e0                dsk_get_bt_avrcp_song_layer_yes_or_no
 .text.dsk_get_bt_avrcp_previous_layer_song_layer_yes_or_no
                0x00000000e1f037f8       0x18 livedesk/leopard/mod_desktop/functions/built-in.o
                0x00000000e1f037f8                dsk_get_bt_avrcp_previous_layer_song_layer_yes_or_no
 .text.dsk_get_bt_avrcp_name_total_number
                0x00000000e1f03810       0x18 livedesk/leopard/mod_desktop/functions/built-in.o
                0x00000000e1f03810                dsk_get_bt_avrcp_name_total_number
 .text.dsk_get_bt_avrcp_name_current_number
                0x00000000e1f03828       0x18 livedesk/leopard/mod_desktop/functions/built-in.o
                0x00000000e1f03828                dsk_get_bt_avrcp_name_current_number
 .text.dsk_get_bt_avrcp_folder_name
                0x00000000e1f03840       0x34 livedesk/leopard/mod_desktop/functions/built-in.o
                0x00000000e1f03840                dsk_get_bt_avrcp_folder_name
 .text.dsk_get_bt_device_name
                0x00000000e1f03874       0x30 livedesk/leopard/mod_desktop/functions/built-in.o
                0x00000000e1f03874                dsk_get_bt_device_name
 .text.dsk_get_ble_device_name
                0x00000000e1f038a4       0x34 livedesk/leopard/mod_desktop/functions/built-in.o
                0x00000000e1f038a4                dsk_get_ble_device_name
 .text.dsk_get_ble_device_addr
                0x00000000e1f038d8       0x34 livedesk/leopard/mod_desktop/functions/built-in.o
                0x00000000e1f038d8                dsk_get_ble_device_addr
 .text.dsk_set_bluetooth_current_mode
                0x00000000e1f0390c       0x5a livedesk/leopard/mod_desktop/functions/built-in.o
                0x00000000e1f0390c                dsk_set_bluetooth_current_mode
 .text.dsk_set_bluetooth_play_status
                0x00000000e1f03966       0x50 livedesk/leopard/mod_desktop/functions/built-in.o
                0x00000000e1f03966                dsk_set_bluetooth_play_status
 .text.dsk_set_bluetooth_play_pause
                0x00000000e1f039b6       0x38 livedesk/leopard/mod_desktop/functions/built-in.o
                0x00000000e1f039b6                dsk_set_bluetooth_play_pause
 .text.dsk_set_bluetooth_prev_song
                0x00000000e1f039ee       0x32 livedesk/leopard/mod_desktop/functions/built-in.o
                0x00000000e1f039ee                dsk_set_bluetooth_prev_song
 .text.dsk_set_bluetooth_next_song
                0x00000000e1f03a20       0x32 livedesk/leopard/mod_desktop/functions/built-in.o
                0x00000000e1f03a20                dsk_set_bluetooth_next_song
 .text.dsk_set_bluetooth_fast_forward
                0x00000000e1f03a52       0x32 livedesk/leopard/mod_desktop/functions/built-in.o
                0x00000000e1f03a52                dsk_set_bluetooth_fast_forward
 .text.dsk_set_bluetooth_back_forward
                0x00000000e1f03a84       0x32 livedesk/leopard/mod_desktop/functions/built-in.o
                0x00000000e1f03a84                dsk_set_bluetooth_back_forward
 .text.dsk_set_bluetooth_end_ff_fb
                0x00000000e1f03ab6       0x32 livedesk/leopard/mod_desktop/functions/built-in.o
                0x00000000e1f03ab6                dsk_set_bluetooth_end_ff_fb
 .text.dsk_set_bluetooth_folder_up
                0x00000000e1f03ae8       0x32 livedesk/leopard/mod_desktop/functions/built-in.o
                0x00000000e1f03ae8                dsk_set_bluetooth_folder_up
 .text.dsk_set_bluetooth_folder_down
                0x00000000e1f03b1a       0x32 livedesk/leopard/mod_desktop/functions/built-in.o
                0x00000000e1f03b1a                dsk_set_bluetooth_folder_down
 .text.dsk_set_bluetooth_mute
                0x00000000e1f03b4c       0x3a livedesk/leopard/mod_desktop/functions/built-in.o
                0x00000000e1f03b4c                dsk_set_bluetooth_mute
 .text.dsk_set_bluetooth_music_time
                0x00000000e1f03b86       0x48 livedesk/leopard/mod_desktop/functions/built-in.o
                0x00000000e1f03b86                dsk_set_bluetooth_music_time
 .text.dsk_set_bluetooth_request_play_mode
                0x00000000e1f03bce       0x3a livedesk/leopard/mod_desktop/functions/built-in.o
                0x00000000e1f03bce                dsk_set_bluetooth_request_play_mode
 .text.dsk_set_bluetooth_repeat_mode
                0x00000000e1f03c08       0x3a livedesk/leopard/mod_desktop/functions/built-in.o
                0x00000000e1f03c08                dsk_set_bluetooth_repeat_mode
 .text.dsk_set_bluetooth_random_mode
                0x00000000e1f03c42       0x46 livedesk/leopard/mod_desktop/functions/built-in.o
                0x00000000e1f03c42                dsk_set_bluetooth_random_mode
 .text.dsk_set_bluetooth_confirm_upgrade
                0x00000000e1f03c88       0x30 livedesk/leopard/mod_desktop/functions/built-in.o
                0x00000000e1f03c88                dsk_set_bluetooth_confirm_upgrade
 .text.dsk_send_bluetooth_found_upgrade_file
                0x00000000e1f03cb8       0x30 livedesk/leopard/mod_desktop/functions/built-in.o
                0x00000000e1f03cb8                dsk_send_bluetooth_found_upgrade_file
 .text.dsk_send_bluetooth_upgrate_file_lenth
                0x00000000e1f03ce8       0x46 livedesk/leopard/mod_desktop/functions/built-in.o
                0x00000000e1f03ce8                dsk_send_bluetooth_upgrate_file_lenth
 .text.dsk_send_bluetooth_upgrate_file_data
                0x00000000e1f03d2e       0x48 livedesk/leopard/mod_desktop/functions/built-in.o
                0x00000000e1f03d2e                dsk_send_bluetooth_upgrate_file_data
 .text.dsk_send_bluetooth_reset_system
                0x00000000e1f03d76       0x4e livedesk/leopard/mod_desktop/functions/built-in.o
                0x00000000e1f03d76                dsk_send_bluetooth_reset_system
 .text.dsk_set_bluetooth_usb_repeat_random_status
                0x00000000e1f03dc4       0x40 livedesk/leopard/mod_desktop/functions/built-in.o
                0x00000000e1f03dc4                dsk_set_bluetooth_usb_repeat_random_status
 .text.dsk_set_usb_request_play_mode
                0x00000000e1f03e04       0x3a livedesk/leopard/mod_desktop/functions/built-in.o
                0x00000000e1f03e04                dsk_set_usb_request_play_mode
 .text.dsk_set_bluetooth_usb_play_status
                0x00000000e1f03e3e       0x50 livedesk/leopard/mod_desktop/functions/built-in.o
                0x00000000e1f03e3e                dsk_set_bluetooth_usb_play_status
 .text.dsk_set_bluetooth_usb_play_pause
                0x00000000e1f03e8e       0x38 livedesk/leopard/mod_desktop/functions/built-in.o
                0x00000000e1f03e8e                dsk_set_bluetooth_usb_play_pause
 .text.dsk_set_bluetooth_usb_prev_song
                0x00000000e1f03ec6       0x32 livedesk/leopard/mod_desktop/functions/built-in.o
                0x00000000e1f03ec6                dsk_set_bluetooth_usb_prev_song
 .text.dsk_set_bluetooth_usb_next_song
                0x00000000e1f03ef8       0x32 livedesk/leopard/mod_desktop/functions/built-in.o
                0x00000000e1f03ef8                dsk_set_bluetooth_usb_next_song
 .text.dsk_set_bluetooth_usb_fast_forward
                0x00000000e1f03f2a       0x32 livedesk/leopard/mod_desktop/functions/built-in.o
                0x00000000e1f03f2a                dsk_set_bluetooth_usb_fast_forward
 .text.dsk_set_bluetooth_usb_back_forward
                0x00000000e1f03f5c       0x32 livedesk/leopard/mod_desktop/functions/built-in.o
                0x00000000e1f03f5c                dsk_set_bluetooth_usb_back_forward
 .text.dsk_set_bluetooth_usb_end_forward
                0x00000000e1f03f8e       0x32 livedesk/leopard/mod_desktop/functions/built-in.o
                0x00000000e1f03f8e                dsk_set_bluetooth_usb_end_forward
 .text.dsk_set_bluetooth_usb_music_time
                0x00000000e1f03fc0       0x48 livedesk/leopard/mod_desktop/functions/built-in.o
                0x00000000e1f03fc0                dsk_set_bluetooth_usb_music_time
 .text.dsk_send_bluetooth_usb_caregory_browse_begin
                0x00000000e1f04008       0x32 livedesk/leopard/mod_desktop/functions/built-in.o
                0x00000000e1f04008                dsk_send_bluetooth_usb_caregory_browse_begin
 .text.dsk_send_bluetooth_usb_request_caregory_index
                0x00000000e1f0403a       0x50 livedesk/leopard/mod_desktop/functions/built-in.o
                0x00000000e1f0403a                dsk_send_bluetooth_usb_request_caregory_index
 .text.dsk_send_bluetooth_usb_category_previous_layer
                0x00000000e1f0408a       0x32 livedesk/leopard/mod_desktop/functions/built-in.o
                0x00000000e1f0408a                dsk_send_bluetooth_usb_category_previous_layer
 .text.dsk_send_bluetooth_usb_category_name
                0x00000000e1f040bc       0x38 livedesk/leopard/mod_desktop/functions/built-in.o
                0x00000000e1f040bc                dsk_send_bluetooth_usb_category_name
 .text.dsk_send_bluetooth_avrcp_caregory_browse_begin
                0x00000000e1f040f4       0x32 livedesk/leopard/mod_desktop/functions/built-in.o
                0x00000000e1f040f4                dsk_send_bluetooth_avrcp_caregory_browse_begin
 .text.dsk_send_bluetooth_avrcp_request_caregory_index
                0x00000000e1f04126       0x50 livedesk/leopard/mod_desktop/functions/built-in.o
                0x00000000e1f04126                dsk_send_bluetooth_avrcp_request_caregory_index
 .text.dsk_send_bluetooth_avrcp_category_previous_layer
                0x00000000e1f04176       0x32 livedesk/leopard/mod_desktop/functions/built-in.o
                0x00000000e1f04176                dsk_send_bluetooth_avrcp_category_previous_layer
 .text.dsk_send_bluetooth_avrcp_category_name
                0x00000000e1f041a8       0x38 livedesk/leopard/mod_desktop/functions/built-in.o
                0x00000000e1f041a8                dsk_send_bluetooth_avrcp_category_name
 .text.dsk_send_bluetooth_avrcp_exit_browser
                0x00000000e1f041e0       0x3a livedesk/leopard/mod_desktop/functions/built-in.o
                0x00000000e1f041e0                dsk_send_bluetooth_avrcp_exit_browser
 .text.dsk_send_bluetooth_dialing_data
                0x00000000e1f0421a       0x38 livedesk/leopard/mod_desktop/functions/built-in.o
                0x00000000e1f0421a                dsk_send_bluetooth_dialing_data
 .text.dsk_send_bluetooth_three_way_call_data
                0x00000000e1f04252       0x3a livedesk/leopard/mod_desktop/functions/built-in.o
                0x00000000e1f04252                dsk_send_bluetooth_three_way_call_data
 .text.dsk_send_bluetooth_scan_start
                0x00000000e1f0428c       0x12 livedesk/leopard/mod_desktop/functions/built-in.o
                0x00000000e1f0428c                dsk_send_bluetooth_scan_start
 .text.dsk_send_bluetooth_scan_cancel
                0x00000000e1f0429e       0x12 livedesk/leopard/mod_desktop/functions/built-in.o
                0x00000000e1f0429e                dsk_send_bluetooth_scan_cancel
 .text.dsk_send_bluetooth_scan_connect
                0x00000000e1f042b0       0x12 livedesk/leopard/mod_desktop/functions/built-in.o
                0x00000000e1f042b0                dsk_send_bluetooth_scan_connect
 .text.dsk_request_bluetooth_redial_data
                0x00000000e1f042c2       0x32 livedesk/leopard/mod_desktop/functions/built-in.o
                0x00000000e1f042c2                dsk_request_bluetooth_redial_data
 .text.dsk_send_bluetooth_phonevoice_data
                0x00000000e1f042f4       0x3a livedesk/leopard/mod_desktop/functions/built-in.o
                0x00000000e1f042f4                dsk_send_bluetooth_phonevoice_data
 .text.dsk_send_bluetooth_dialing_keynum_data
                0x00000000e1f0432e       0x38 livedesk/leopard/mod_desktop/functions/built-in.o
                0x00000000e1f0432e                dsk_send_bluetooth_dialing_keynum_data
 .text.dsk_send_bluetooth_book_and_history_data
                0x00000000e1f04366       0x3a livedesk/leopard/mod_desktop/functions/built-in.o
                0x00000000e1f04366                dsk_send_bluetooth_book_and_history_data
 .text.dsk_send_requrst_update_phonebook
                0x00000000e1f043a0       0x32 livedesk/leopard/mod_desktop/functions/built-in.o
                0x00000000e1f043a0                dsk_send_requrst_update_phonebook
 .text.dsk_send_download_phonebook
                0x00000000e1f043d2       0x32 livedesk/leopard/mod_desktop/functions/built-in.o
                0x00000000e1f043d2                dsk_send_download_phonebook
 .text.dsk_send_stop_phonebook
                0x00000000e1f04404       0x32 livedesk/leopard/mod_desktop/functions/built-in.o
                0x00000000e1f04404                dsk_send_stop_phonebook
 .text.dsk_set_bluetooth_mirror_type
                0x00000000e1f04436       0x18 livedesk/leopard/mod_desktop/functions/built-in.o
                0x00000000e1f04436                dsk_set_bluetooth_mirror_type
 .text.dsk_set_bluetooth_luauch_app
                0x00000000e1f0444e       0xa0 livedesk/leopard/mod_desktop/functions/built-in.o
                0x00000000e1f0444e                dsk_set_bluetooth_luauch_app
 .text.dsk_set_bluetooth_mirror_app_connect_bt
                0x00000000e1f044ee       0x46 livedesk/leopard/mod_desktop/functions/built-in.o
                0x00000000e1f044ee                dsk_set_bluetooth_mirror_app_connect_bt
 .text.dsk_bt_send_remove_device
                0x00000000e1f04534       0x3a livedesk/leopard/mod_desktop/functions/built-in.o
                0x00000000e1f04534                dsk_bt_send_remove_device
 .text.dsk_bt_send_ask_device_name
                0x00000000e1f0456e       0x32 livedesk/leopard/mod_desktop/functions/built-in.o
                0x00000000e1f0456e                dsk_bt_send_ask_device_name
 .text.dsk_bt_send_connect_device
                0x00000000e1f045a0       0x3a livedesk/leopard/mod_desktop/functions/built-in.o
                0x00000000e1f045a0                dsk_bt_send_connect_device
 .text.dsk_bt_send_disconnect_device
                0x00000000e1f045da       0x3a livedesk/leopard/mod_desktop/functions/built-in.o
                0x00000000e1f045da                dsk_bt_send_disconnect_device
 .text.dsk_bt_send_ask_ble_name
                0x00000000e1f04614       0x32 livedesk/leopard/mod_desktop/functions/built-in.o
                0x00000000e1f04614                dsk_bt_send_ask_ble_name
 .text.dsk_bt_send_ask_ble_pair_name
                0x00000000e1f04646       0x32 livedesk/leopard/mod_desktop/functions/built-in.o
                0x00000000e1f04646                dsk_bt_send_ask_ble_pair_name
 .text.dsk_bt_send_connect_ble
                0x00000000e1f04678       0x4a livedesk/leopard/mod_desktop/functions/built-in.o
                0x00000000e1f04678                dsk_bt_send_connect_ble
 .text.dsk_bt_send_disconnect_ble
                0x00000000e1f046c2       0x32 livedesk/leopard/mod_desktop/functions/built-in.o
                0x00000000e1f046c2                dsk_bt_send_disconnect_ble
 .text.dsk_bt_send_into_ble
                0x00000000e1f046f4       0x5c livedesk/leopard/mod_desktop/functions/built-in.o
                0x00000000e1f046f4                dsk_bt_send_into_ble
 .text.dsk_bt_send_remove_ble_device
                0x00000000e1f04750       0x3a livedesk/leopard/mod_desktop/functions/built-in.o
                0x00000000e1f04750                dsk_bt_send_remove_ble_device
 .text.dsk_bt_send_ble_notify_onoff
                0x00000000e1f0478a       0x12 livedesk/leopard/mod_desktop/functions/built-in.o
                0x00000000e1f0478a                dsk_bt_send_ble_notify_onoff
 .text.dsk_send_poweroff_status
                0x00000000e1f0479c       0x12 livedesk/leopard/mod_desktop/functions/built-in.o
                0x00000000e1f0479c                dsk_send_poweroff_status
 .text.dsk_send_pin_code_affirm
                0x00000000e1f047ae       0x3a livedesk/leopard/mod_desktop/functions/built-in.o
                0x00000000e1f047ae                dsk_send_pin_code_affirm
 .text.dsk_send_bluetooth_db_link_change_index
                0x00000000e1f047e8       0x12 livedesk/leopard/mod_desktop/functions/built-in.o
                0x00000000e1f047e8                dsk_send_bluetooth_db_link_change_index
 .text.dsk_send_cur_mode
                0x00000000e1f047fa       0x30 livedesk/leopard/mod_desktop/functions/built-in.o
                0x00000000e1f047fa                dsk_send_cur_mode
 .text.dsk_send_bluetooth_back_connect
                0x00000000e1f0482a       0x32 livedesk/leopard/mod_desktop/functions/built-in.o
                0x00000000e1f0482a                dsk_send_bluetooth_back_connect
 .text.dsk_send_bluetooth_clear_iap2_memory
                0x00000000e1f0485c       0x32 livedesk/leopard/mod_desktop/functions/built-in.o
                0x00000000e1f0485c                dsk_send_bluetooth_clear_iap2_memory
 .text.dsk_get_bluetooth_sw
                0x00000000e1f0488e       0x30 livedesk/leopard/mod_desktop/functions/built-in.o
                0x00000000e1f0488e                dsk_get_bluetooth_sw
 .text.dsk_bt_send_voice_on_off
                0x00000000e1f048be       0x3a livedesk/leopard/mod_desktop/functions/built-in.o
                0x00000000e1f048be                dsk_bt_send_voice_on_off
 .text.dsk_set_bluetooth_spp_status
                0x00000000e1f048f8       0x12 livedesk/leopard/mod_desktop/functions/built-in.o
                0x00000000e1f048f8                dsk_set_bluetooth_spp_status
 .text.dsk_set_bluetooth_hid_status
                0x00000000e1f0490a       0x38 livedesk/leopard/mod_desktop/functions/built-in.o
                0x00000000e1f0490a                dsk_set_bluetooth_hid_status
 .text.dsk_get_langres_charset
                0x00000000e1f04942        0x4 livedesk/leopard/mod_desktop/functions/built-in.o
                0x00000000e1f04942                dsk_get_langres_charset
 .text.dsk_set_fs_charset
                0x00000000e1f04946       0x26 livedesk/leopard/mod_desktop/functions/built-in.o
                0x00000000e1f04946                dsk_set_fs_charset
 .text.dsk_get_fs_charset
                0x00000000e1f0496c       0x28 livedesk/leopard/mod_desktop/functions/built-in.o
                0x00000000e1f0496c                dsk_get_fs_charset
 .text.dsk_set_isn_charset
                0x00000000e1f04994        0xc livedesk/leopard/mod_desktop/functions/built-in.o
                0x00000000e1f04994                dsk_set_isn_charset
 .text.dsk_get_isn_charset
                0x00000000e1f049a0        0xe livedesk/leopard/mod_desktop/functions/built-in.o
                0x00000000e1f049a0                dsk_get_isn_charset
 .text.dsk_dab_open
                0x00000000e1f049ae       0x24 livedesk/leopard/mod_desktop/functions/built-in.o
                0x00000000e1f049ae                dsk_dab_open
 .text.dsk_dab_close
                0x00000000e1f049d2       0x18 livedesk/leopard/mod_desktop/functions/built-in.o
                0x00000000e1f049d2                dsk_dab_close
 .text.dsk_dab_sel_radio_module
                0x00000000e1f049ea       0x6a livedesk/leopard/mod_desktop/functions/built-in.o
                0x00000000e1f049ea                dsk_dab_sel_radio_module
 .text.dsk_dab_load_and_play
                0x00000000e1f04a54       0x6c livedesk/leopard/mod_desktop/functions/built-in.o
                0x00000000e1f04a54                dsk_dab_load_and_play
 .text.dsk_dab_uninstall_radio_module
                0x00000000e1f04ac0       0x50 livedesk/leopard/mod_desktop/functions/built-in.o
                0x00000000e1f04ac0                dsk_dab_uninstall_radio_module
 .text.dsk_dab_init_mode
                0x00000000e1f04b10       0x66 livedesk/leopard/mod_desktop/functions/built-in.o
                0x00000000e1f04b10                dsk_dab_init_mode
 .text.dsk_dab_mode_key_func
                0x00000000e1f04b76       0x3e livedesk/leopard/mod_desktop/functions/built-in.o
                0x00000000e1f04b76                dsk_dab_mode_key_func
 .text.dsk_dab_mode_switch_band
                0x00000000e1f04bb4       0x34 livedesk/leopard/mod_desktop/functions/built-in.o
                0x00000000e1f04bb4                dsk_dab_mode_switch_band
 .text.dsk_dab_mode_preset
                0x00000000e1f04be8       0x30 livedesk/leopard/mod_desktop/functions/built-in.o
                0x00000000e1f04be8                dsk_dab_mode_preset
 .text.dsk_dab_mode_sel_preset
                0x00000000e1f04c18       0x30 livedesk/leopard/mod_desktop/functions/built-in.o
                0x00000000e1f04c18                dsk_dab_mode_sel_preset
 .text.dsk_dab_mode_save_flush
                0x00000000e1f04c48       0x2e livedesk/leopard/mod_desktop/functions/built-in.o
                0x00000000e1f04c48                dsk_dab_mode_save_flush
 .text.dsk_dab_mode_save_erase
                0x00000000e1f04c76       0x2e livedesk/leopard/mod_desktop/functions/built-in.o
                0x00000000e1f04c76                dsk_dab_mode_save_erase
 .text.dsk_display_lcd_on
                0x00000000e1f04ca4       0x34 livedesk/leopard/mod_desktop/functions/built-in.o
                0x00000000e1f04ca4                dsk_display_lcd_on
 .text.dsk_display_lcd_off
                0x00000000e1f04cd8       0x34 livedesk/leopard/mod_desktop/functions/built-in.o
                0x00000000e1f04cd8                dsk_display_lcd_off
 .text.dsk_display_lcd_backlight_enable
                0x00000000e1f04d0c       0x50 livedesk/leopard/mod_desktop/functions/built-in.o
                0x00000000e1f04d0c                dsk_display_lcd_backlight_enable
 .text.dsk_display_set_lcd_brightness
                0x00000000e1f04d5c       0x66 livedesk/leopard/mod_desktop/functions/built-in.o
                0x00000000e1f04d5c                dsk_display_set_lcd_brightness
 .text.dsk_display_get_lcd_brightness
                0x00000000e1f04dc2       0xea livedesk/leopard/mod_desktop/functions/built-in.o
                0x00000000e1f04dc2                dsk_display_get_lcd_brightness
 .text.dsk_display_set_lcd_bright
                0x00000000e1f04eac       0x60 livedesk/leopard/mod_desktop/functions/built-in.o
                0x00000000e1f04eac                dsk_display_set_lcd_bright
 .text.dsk_display_get_lcd_bright
                0x00000000e1f04f0c       0x24 livedesk/leopard/mod_desktop/functions/built-in.o
                0x00000000e1f04f0c                dsk_display_get_lcd_bright
 .text.dsk_display_set_lcd_bright_ex
                0x00000000e1f04f30       0x46 livedesk/leopard/mod_desktop/functions/built-in.o
                0x00000000e1f04f30                dsk_display_set_lcd_bright_ex
 .text.dsk_display_set_lcd_bright1_ex
                0x00000000e1f04f76       0x36 livedesk/leopard/mod_desktop/functions/built-in.o
                0x00000000e1f04f76                dsk_display_set_lcd_bright1_ex
 .text.dsk_display_get_lcd_bright_ex
                0x00000000e1f04fac       0x24 livedesk/leopard/mod_desktop/functions/built-in.o
                0x00000000e1f04fac                dsk_display_get_lcd_bright_ex
 .text.dsk_display_set_lcd_constract
                0x00000000e1f04fd0       0x60 livedesk/leopard/mod_desktop/functions/built-in.o
                0x00000000e1f04fd0                dsk_display_set_lcd_constract
 .text.dsk_display_get_lcd_constract
                0x00000000e1f05030       0x24 livedesk/leopard/mod_desktop/functions/built-in.o
                0x00000000e1f05030                dsk_display_get_lcd_constract
 .text.dsk_display_set_lcd_constract_ex
                0x00000000e1f05054       0x46 livedesk/leopard/mod_desktop/functions/built-in.o
                0x00000000e1f05054                dsk_display_set_lcd_constract_ex
 .text.dsk_display_set_lcd_constract1_ex
                0x00000000e1f0509a       0x36 livedesk/leopard/mod_desktop/functions/built-in.o
                0x00000000e1f0509a                dsk_display_set_lcd_constract1_ex
 .text.dsk_display_get_lcd_constract_ex
                0x00000000e1f050d0       0x24 livedesk/leopard/mod_desktop/functions/built-in.o
                0x00000000e1f050d0                dsk_display_get_lcd_constract_ex
 .text.dsk_display_set_lcd_saturation
                0x00000000e1f050f4       0x60 livedesk/leopard/mod_desktop/functions/built-in.o
                0x00000000e1f050f4                dsk_display_set_lcd_saturation
 .text.dsk_display_get_lcd_saturation
                0x00000000e1f05154       0x24 livedesk/leopard/mod_desktop/functions/built-in.o
                0x00000000e1f05154                dsk_display_get_lcd_saturation
 .text.dsk_display_set_lcd_saturation_ex
                0x00000000e1f05178       0x46 livedesk/leopard/mod_desktop/functions/built-in.o
                0x00000000e1f05178                dsk_display_set_lcd_saturation_ex
 .text.dsk_display_set_lcd_saturation1_ex
                0x00000000e1f051be       0x36 livedesk/leopard/mod_desktop/functions/built-in.o
                0x00000000e1f051be                dsk_display_set_lcd_saturation1_ex
 .text.dsk_display_get_lcd_saturation_ex
                0x00000000e1f051f4       0x24 livedesk/leopard/mod_desktop/functions/built-in.o
                0x00000000e1f051f4                dsk_display_get_lcd_saturation_ex
 .text.dsk_display_set_lcd_hue
                0x00000000e1f05218        0x4 livedesk/leopard/mod_desktop/functions/built-in.o
                0x00000000e1f05218                dsk_display_set_lcd_hue
 .text.dsk_display_get_lcd_hue
                0x00000000e1f0521c        0x4 livedesk/leopard/mod_desktop/functions/built-in.o
                0x00000000e1f0521c                dsk_display_get_lcd_hue
 .text.dsk_display_set_lcd_hue_ex
                0x00000000e1f05220       0x46 livedesk/leopard/mod_desktop/functions/built-in.o
                0x00000000e1f05220                dsk_display_set_lcd_hue_ex
 .text.dsk_display_set_lcd_hue1_ex
                0x00000000e1f05266       0x36 livedesk/leopard/mod_desktop/functions/built-in.o
                0x00000000e1f05266                dsk_display_set_lcd_hue1_ex
 .text.dsk_display_get_lcd_hue_ex
                0x00000000e1f0529c       0x24 livedesk/leopard/mod_desktop/functions/built-in.o
                0x00000000e1f0529c                dsk_display_get_lcd_hue_ex
 .text.dsk_display_set_lcd_edge
                0x00000000e1f052c0        0x4 livedesk/leopard/mod_desktop/functions/built-in.o
                0x00000000e1f052c0                dsk_display_set_lcd_edge
 .text.dsk_display_get_lcd_edge
                0x00000000e1f052c4        0x4 livedesk/leopard/mod_desktop/functions/built-in.o
                0x00000000e1f052c4                dsk_display_get_lcd_edge
 .text.dsk_display_set_lcd_detail
                0x00000000e1f052c8        0x4 livedesk/leopard/mod_desktop/functions/built-in.o
                0x00000000e1f052c8                dsk_display_set_lcd_detail
 .text.dsk_display_get_lcd_detail
                0x00000000e1f052cc        0x4 livedesk/leopard/mod_desktop/functions/built-in.o
                0x00000000e1f052cc                dsk_display_get_lcd_detail
 .text.dsk_display_set_lcd_denoise
                0x00000000e1f052d0        0x4 livedesk/leopard/mod_desktop/functions/built-in.o
                0x00000000e1f052d0                dsk_display_set_lcd_denoise
 .text.dsk_display_get_lcd_denoise
                0x00000000e1f052d4        0x4 livedesk/leopard/mod_desktop/functions/built-in.o
                0x00000000e1f052d4                dsk_display_get_lcd_denoise
 .text.dsk_display_enhance_enable
                0x00000000e1f052d8       0x44 livedesk/leopard/mod_desktop/functions/built-in.o
                0x00000000e1f052d8                dsk_display_enhance_enable
 .text.dsk_display_enhance_disable
                0x00000000e1f0531c       0x40 livedesk/leopard/mod_desktop/functions/built-in.o
                0x00000000e1f0531c                dsk_display_enhance_disable
 .text.dsk_display_get_enhance_enable
                0x00000000e1f0535c        0x4 livedesk/leopard/mod_desktop/functions/built-in.o
                0x00000000e1f0535c                dsk_display_get_enhance_enable
 .text.dsk_display_enhance_demo_enable
                0x00000000e1f05360       0x24 livedesk/leopard/mod_desktop/functions/built-in.o
                0x00000000e1f05360                dsk_display_enhance_demo_enable
 .text.dsk_display_enhance_demo_disable
                0x00000000e1f05384       0x24 livedesk/leopard/mod_desktop/functions/built-in.o
                0x00000000e1f05384                dsk_display_enhance_demo_disable
 .text.dsk_display_hdmi_audio_enable
                0x00000000e1f053a8        0x4 livedesk/leopard/mod_desktop/functions/built-in.o
                0x00000000e1f053a8                dsk_display_hdmi_audio_enable
 .text.dsk_display_get_size
                0x00000000e1f053ac       0x26 livedesk/leopard/mod_desktop/functions/built-in.o
                0x00000000e1f053ac                dsk_display_get_size
 .text.dsk_display_get_size1
                0x00000000e1f053d2       0x3e livedesk/leopard/mod_desktop/functions/built-in.o
                0x00000000e1f053d2                dsk_display_get_size1
 .text.dsk_display_set_backcolor
                0x00000000e1f05410       0x38 livedesk/leopard/mod_desktop/functions/built-in.o
                0x00000000e1f05410                dsk_display_set_backcolor
 .text.dsk_display_get_backcolor
                0x00000000e1f05448       0x30 livedesk/leopard/mod_desktop/functions/built-in.o
                0x00000000e1f05448                dsk_display_get_backcolor
 .text.dsk_display_get_output_type
                0x00000000e1f05478       0x22 livedesk/leopard/mod_desktop/functions/built-in.o
                0x00000000e1f05478                dsk_display_get_output_type
 .text.dsk_display_dskfmt2epdkfmt
                0x00000000e1f0549a      0x10a livedesk/leopard/mod_desktop/functions/built-in.o
                0x00000000e1f0549a                dsk_display_dskfmt2epdkfmt
 .text.dsk_display_set_format
                0x00000000e1f055a4       0x42 livedesk/leopard/mod_desktop/functions/built-in.o
                0x00000000e1f055a4                dsk_display_set_format
 .text.dsk_display_epdkfmt2dskfmt
                0x00000000e1f055e6      0x13e livedesk/leopard/mod_desktop/functions/built-in.o
                0x00000000e1f055e6                dsk_display_epdkfmt2dskfmt
 .text.dsk_display_get_format
                0x00000000e1f05724       0x3c livedesk/leopard/mod_desktop/functions/built-in.o
                0x00000000e1f05724                dsk_display_get_format
 .text.dsk_display_on
                0x00000000e1f05760       0x16 livedesk/leopard/mod_desktop/functions/built-in.o
                0x00000000e1f05760                dsk_display_on
 .text.dsk_display_off
                0x00000000e1f05776       0x42 livedesk/leopard/mod_desktop/functions/built-in.o
                0x00000000e1f05776                dsk_display_off
 .text.dsk_keytone_init
                0x00000000e1f057b8       0x12 livedesk/leopard/mod_desktop/functions/built-in.o
                0x00000000e1f057b8                dsk_keytone_init
 .text.dsk_keytone_exit
                0x00000000e1f057ca        0x4 livedesk/leopard/mod_desktop/functions/built-in.o
                0x00000000e1f057ca                dsk_keytone_exit
 .text.dsk_keytone_on
                0x00000000e1f057ce        0x4 livedesk/leopard/mod_desktop/functions/built-in.o
                0x00000000e1f057ce                dsk_keytone_on
 .text.dsk_keytone_set_state
                0x00000000e1f057d2        0x4 livedesk/leopard/mod_desktop/functions/built-in.o
                0x00000000e1f057d2                dsk_keytone_set_state
 .text.dsk_keytone_get_state
                0x00000000e1f057d6        0x4 livedesk/leopard/mod_desktop/functions/built-in.o
                0x00000000e1f057d6                dsk_keytone_get_state
 .text.dsk_keytone_set_key_type
                0x00000000e1f057da        0x2 livedesk/leopard/mod_desktop/functions/built-in.o
                0x00000000e1f057da                dsk_keytone_set_key_type
 .text.dsk_keytone_get_key_type
                0x00000000e1f057dc        0x4 livedesk/leopard/mod_desktop/functions/built-in.o
                0x00000000e1f057dc                dsk_keytone_get_key_type
 .text.dsk_langres_init
                0x00000000e1f057e0       0x38 livedesk/leopard/mod_desktop/functions/built-in.o
                0x00000000e1f057e0                dsk_langres_init
 .text.dsk_langres_exit
                0x00000000e1f05818       0x26 livedesk/leopard/mod_desktop/functions/built-in.o
                0x00000000e1f05818                dsk_langres_exit
 .text.dsk_langres_set_type
                0x00000000e1f0583e        0xc livedesk/leopard/mod_desktop/functions/built-in.o
                0x00000000e1f0583e                dsk_langres_set_type
 .text.dsk_langres_get_type
                0x00000000e1f0584a        0xa livedesk/leopard/mod_desktop/functions/built-in.o
                0x00000000e1f0584a                dsk_langres_get_type
 .text.dsk_langres_get_menu_len
                0x00000000e1f05854       0x28 livedesk/leopard/mod_desktop/functions/built-in.o
                0x00000000e1f05854                dsk_langres_get_menu_len
 .text.dsk_langres_get_menu_text
                0x00000000e1f0587c       0x58 livedesk/leopard/mod_desktop/functions/built-in.o
                0x00000000e1f0587c                dsk_langres_get_menu_text
 .text.dsk_mcu_open
                0x00000000e1f058d4       0x24 livedesk/leopard/mod_desktop/functions/built-in.o
                0x00000000e1f058d4                dsk_mcu_open
 .text.dsk_send_mcu_c800_update_quit
                0x00000000e1f058f8       0x44 livedesk/leopard/mod_desktop/functions/built-in.o
                0x00000000e1f058f8                dsk_send_mcu_c800_update_quit
 .text.dsk_send_mcu_power_off
                0x00000000e1f0593c       0x44 livedesk/leopard/mod_desktop/functions/built-in.o
                0x00000000e1f0593c                dsk_send_mcu_power_off
 .text.dsk_send_mcu_reset
                0x00000000e1f05980       0x60 livedesk/leopard/mod_desktop/functions/built-in.o
                0x00000000e1f05980                dsk_send_mcu_reset
 .text.dsk_send_mcu_restart
                0x00000000e1f059e0       0x4c livedesk/leopard/mod_desktop/functions/built-in.o
                0x00000000e1f059e0                dsk_send_mcu_restart
 .text.dsk_send_mcu_io
                0x00000000e1f05a2c       0xbe livedesk/leopard/mod_desktop/functions/built-in.o
                0x00000000e1f05a2c                dsk_send_mcu_io
 .text.dsk_send_mcu_ready
                0x00000000e1f05aea       0x42 livedesk/leopard/mod_desktop/functions/built-in.o
                0x00000000e1f05aea                dsk_send_mcu_ready
 .text.dsk_request_mcu_mamery_data
                0x00000000e1f05b2c       0x4a livedesk/leopard/mod_desktop/functions/built-in.o
                0x00000000e1f05b2c                dsk_request_mcu_mamery_data
 .text.dsk_send_mcu_date_time
                0x00000000e1f05b76       0x76 livedesk/leopard/mod_desktop/functions/built-in.o
                0x00000000e1f05b76                dsk_send_mcu_date_time
 .text.dsk_send_tuner_type
                0x00000000e1f05bec       0x4c livedesk/leopard/mod_desktop/functions/built-in.o
                0x00000000e1f05bec                dsk_send_tuner_type
 .text.dsk_send_mcu_memory
                0x00000000e1f05c38       0x44 livedesk/leopard/mod_desktop/functions/built-in.o
                0x00000000e1f05c38                dsk_send_mcu_memory
 .text.dsk_send_mcu_current_source
                0x00000000e1f05c7c       0x6c livedesk/leopard/mod_desktop/functions/built-in.o
                0x00000000e1f05c7c                dsk_send_mcu_current_source
 .text.dsk_send_mcu_beep_on_off
                0x00000000e1f05ce8       0x50 livedesk/leopard/mod_desktop/functions/built-in.o
                0x00000000e1f05ce8                dsk_send_mcu_beep_on_off
 .text.dsk_send_mcu_beep_setting
                0x00000000e1f05d38       0x6c livedesk/leopard/mod_desktop/functions/built-in.o
                0x00000000e1f05d38                dsk_send_mcu_beep_setting
 .text.dsk_send_mcu_power_off_status
                0x00000000e1f05da4       0x50 livedesk/leopard/mod_desktop/functions/built-in.o
                0x00000000e1f05da4                dsk_send_mcu_power_off_status
 .text.dsk_send_mcu_oe_key_learn_start
                0x00000000e1f05df4       0x4a livedesk/leopard/mod_desktop/functions/built-in.o
                0x00000000e1f05df4                dsk_send_mcu_oe_key_learn_start
 .text.dsk_send_mcu_oe_key_learn
                0x00000000e1f05e3e       0x6a livedesk/leopard/mod_desktop/functions/built-in.o
                0x00000000e1f05e3e                dsk_send_mcu_oe_key_learn
 .text.dsk_send_mcu_oe_key_cancel
                0x00000000e1f05ea8       0x6a livedesk/leopard/mod_desktop/functions/built-in.o
                0x00000000e1f05ea8                dsk_send_mcu_oe_key_cancel
 .text.dsk_send_mcu_oe_key_quit
                0x00000000e1f05f12       0x46 livedesk/leopard/mod_desktop/functions/built-in.o
                0x00000000e1f05f12                dsk_send_mcu_oe_key_quit
 .text.dsk_send_mcu_oe_key_init
                0x00000000e1f05f58       0x4a livedesk/leopard/mod_desktop/functions/built-in.o
                0x00000000e1f05f58                dsk_send_mcu_oe_key_init
 .text.dsk_send_mcu_dvr_x_y_point
                0x00000000e1f05fa2       0x8c livedesk/leopard/mod_desktop/functions/built-in.o
                0x00000000e1f05fa2                dsk_send_mcu_dvr_x_y_point
 .text.dsk_send_mcu_dvr_year_month
                0x00000000e1f0602e       0x62 livedesk/leopard/mod_desktop/functions/built-in.o
                0x00000000e1f0602e                dsk_send_mcu_dvr_year_month
 .text.dsk_send_mcu_dvr_day_hours
                0x00000000e1f06090       0x62 livedesk/leopard/mod_desktop/functions/built-in.o
                0x00000000e1f06090                dsk_send_mcu_dvr_day_hours
 .text.dsk_send_mcu_dvr_minute_seconds
                0x00000000e1f060f2       0x68 livedesk/leopard/mod_desktop/functions/built-in.o
                0x00000000e1f060f2                dsk_send_mcu_dvr_minute_seconds
 .text.dsk_send_mcu_dvr_fast_record_lock
                0x00000000e1f0615a       0x52 livedesk/leopard/mod_desktop/functions/built-in.o
                0x00000000e1f0615a                dsk_send_mcu_dvr_fast_record_lock
 .text.dsk_send_mcu_dvr_switch_front_lens
                0x00000000e1f061ac       0x52 livedesk/leopard/mod_desktop/functions/built-in.o
                0x00000000e1f061ac                dsk_send_mcu_dvr_switch_front_lens
 .text.dsk_send_mcu_dvr_switch_rear_lens
                0x00000000e1f061fe       0x52 livedesk/leopard/mod_desktop/functions/built-in.o
                0x00000000e1f061fe                dsk_send_mcu_dvr_switch_rear_lens
 .text.dsk_send_mcu_dvr_switch_front_rear_lens
                0x00000000e1f06250       0x52 livedesk/leopard/mod_desktop/functions/built-in.o
                0x00000000e1f06250                dsk_send_mcu_dvr_switch_front_rear_lens
 .text.dsk_send_mcu_dvr_time_fomat_24h
                0x00000000e1f062a2       0x52 livedesk/leopard/mod_desktop/functions/built-in.o
                0x00000000e1f062a2                dsk_send_mcu_dvr_time_fomat_24h
 .text.dsk_send_mcu_dvr_time_fomat_12h
                0x00000000e1f062f4       0x54 livedesk/leopard/mod_desktop/functions/built-in.o
                0x00000000e1f062f4                dsk_send_mcu_dvr_time_fomat_12h
 .text.dsk_send_mcu_dvr_into_record
                0x00000000e1f06348       0x54 livedesk/leopard/mod_desktop/functions/built-in.o
                0x00000000e1f06348                dsk_send_mcu_dvr_into_record
 .text.dsk_send_mcu_dvr_hide_icon
                0x00000000e1f0639c       0x52 livedesk/leopard/mod_desktop/functions/built-in.o
                0x00000000e1f0639c                dsk_send_mcu_dvr_hide_icon
 .text.dsk_send_mcu_dvr_show_icon
                0x00000000e1f063ee       0x52 livedesk/leopard/mod_desktop/functions/built-in.o
                0x00000000e1f063ee                dsk_send_mcu_dvr_show_icon
 .text.dsk_send_mcu_cmd
                0x00000000e1f06440       0x3a livedesk/leopard/mod_desktop/functions/built-in.o
                0x00000000e1f06440                dsk_send_mcu_cmd
 .text.dsk_send_mcu_update_begin
                0x00000000e1f0647a       0x30 livedesk/leopard/mod_desktop/functions/built-in.o
                0x00000000e1f0647a                dsk_send_mcu_update_begin
 .text.dsk_send_mcu_update_data
                0x00000000e1f064aa       0x72 livedesk/leopard/mod_desktop/functions/built-in.o
                0x00000000e1f064aa                dsk_send_mcu_update_data
 .text.dsk_send_mcu_c800_update_begin
                0x00000000e1f0651c       0x18 livedesk/leopard/mod_desktop/functions/built-in.o
                0x00000000e1f0651c                dsk_send_mcu_c800_update_begin
 .text.dsk_send_mcu_mute
                0x00000000e1f06534       0x1c livedesk/leopard/mod_desktop/functions/built-in.o
                0x00000000e1f06534                dsk_send_mcu_mute
 .text.dsk_send_mcu_zones_para
                0x00000000e1f06550       0x16 livedesk/leopard/mod_desktop/functions/built-in.o
                0x00000000e1f06550                dsk_send_mcu_zones_para
 .text.dsk_orchid_createDisk
                0x00000000e1f06566       0x3c livedesk/leopard/mod_desktop/functions/built-in.o
                0x00000000e1f06566                dsk_orchid_createDisk
 .text.dsk_orchid_check
                0x00000000e1f065a2        0x4 livedesk/leopard/mod_desktop/functions/built-in.o
                0x00000000e1f065a2                dsk_orchid_check
 .text.dsk_orchid_update
                0x00000000e1f065a6        0x4 livedesk/leopard/mod_desktop/functions/built-in.o
                0x00000000e1f065a6                dsk_orchid_update
 .text.dsk_orchid_save_npl
                0x00000000e1f065aa        0x4 livedesk/leopard/mod_desktop/functions/built-in.o
                0x00000000e1f065aa                dsk_orchid_save_npl
 .text.dsk_orchid_load_last_npl
                0x00000000e1f065ae        0x4 livedesk/leopard/mod_desktop/functions/built-in.o
                0x00000000e1f065ae                dsk_orchid_load_last_npl
 .text.dsk_power_dev_init
                0x00000000e1f065b2       0x24 livedesk/leopard/mod_desktop/functions/built-in.o
                0x00000000e1f065b2                dsk_power_dev_init
 .text.dsk_power_dev_uninit
                0x00000000e1f065d6       0x24 livedesk/leopard/mod_desktop/functions/built-in.o
                0x00000000e1f065d6                dsk_power_dev_uninit
 .text.dsk_power_get_battery_level
                0x00000000e1f065fa        0x4 livedesk/leopard/mod_desktop/functions/built-in.o
                0x00000000e1f065fa                dsk_power_get_battery_level
 .text.dsk_power_get_voltage_level
                0x00000000e1f065fe       0x6c livedesk/leopard/mod_desktop/functions/built-in.o
                0x00000000e1f065fe                dsk_power_get_voltage_level
 .text.dsk_power_is_full
                0x00000000e1f0666a       0x34 livedesk/leopard/mod_desktop/functions/built-in.o
                0x00000000e1f0666a                dsk_power_is_full
 .text.dsk_power_is_low
                0x00000000e1f0669e       0x70 livedesk/leopard/mod_desktop/functions/built-in.o
                0x00000000e1f0669e                dsk_power_is_low
 .text.dsk_power_set_off_time
                0x00000000e1f0670e        0x4 livedesk/leopard/mod_desktop/functions/built-in.o
                0x00000000e1f0670e                dsk_power_set_off_time
 .text.dsk_power_get_battary_state
                0x00000000e1f06712        0x4 livedesk/leopard/mod_desktop/functions/built-in.o
                0x00000000e1f06712                dsk_power_get_battary_state
 .text.dsk_speaker_turnoff
                0x00000000e1f06716        0x2 livedesk/leopard/mod_desktop/functions/built-in.o
                0x00000000e1f06716                dsk_speaker_turnoff
 .text.dsk_speaker_turnon
                0x00000000e1f06718        0x2 livedesk/leopard/mod_desktop/functions/built-in.o
                0x00000000e1f06718                dsk_speaker_turnon
 .text.dsk_speaker_resume
                0x00000000e1f0671a        0x2 livedesk/leopard/mod_desktop/functions/built-in.o
                0x00000000e1f0671a                dsk_speaker_resume
 .text.dsk_get_charge_state
                0x00000000e1f0671c       0x3e livedesk/leopard/mod_desktop/functions/built-in.o
                0x00000000e1f0671c                dsk_get_charge_state
 .text.save_flash_task
                0x00000000e1f0675a      0x1ba livedesk/leopard/mod_desktop/functions/built-in.o
 .text.calc_crc32.constprop.3
                0x00000000e1f06914       0x72 livedesk/leopard/mod_desktop/functions/built-in.o
 .text.dsk_reg_init_para
                0x00000000e1f06986      0x4f8 livedesk/leopard/mod_desktop/functions/built-in.o
                0x00000000e1f06986                dsk_reg_init_para
 .text.dsk_reg_flush
                0x00000000e1f06e7e       0x6c livedesk/leopard/mod_desktop/functions/built-in.o
                0x00000000e1f06e7e                dsk_reg_flush
 .text.dsk_reg_deinit_para
                0x00000000e1f06eea       0xb6 livedesk/leopard/mod_desktop/functions/built-in.o
                0x00000000e1f06eea                dsk_reg_deinit_para
 .text.dsk_reg_get_para_by_app
                0x00000000e1f06fa0       0x8e livedesk/leopard/mod_desktop/functions/built-in.o
                0x00000000e1f06fa0                dsk_reg_get_para_by_app
 .text.dsk_reg_get_default_para_by_app
                0x00000000e1f0702e        0x4 livedesk/leopard/mod_desktop/functions/built-in.o
                0x00000000e1f0702e                dsk_reg_get_default_para_by_app
 .text.dsk_reg_get_app_restore_flag
                0x00000000e1f07032       0x1e livedesk/leopard/mod_desktop/functions/built-in.o
                0x00000000e1f07032                dsk_reg_get_app_restore_flag
 .text.dsk_reg_set_app_restore_flag
                0x00000000e1f07050       0x1a livedesk/leopard/mod_desktop/functions/built-in.o
                0x00000000e1f07050                dsk_reg_set_app_restore_flag
 .text.dsk_reg_save_cur_play_info
                0x00000000e1f0706a      0x132 livedesk/leopard/mod_desktop/functions/built-in.o
                0x00000000e1f0706a                dsk_reg_save_cur_play_info
 .text.dsk_reg_set_app_restore_all
                0x00000000e1f0719c        0x4 livedesk/leopard/mod_desktop/functions/built-in.o
                0x00000000e1f0719c                dsk_reg_set_app_restore_all
 .text._release_theme_res
                0x00000000e1f071a0       0x2a livedesk/leopard/mod_desktop/functions/built-in.o
 .text.dsk_theme_init
                0x00000000e1f071ca       0x2e livedesk/leopard/mod_desktop/functions/built-in.o
                0x00000000e1f071ca                dsk_theme_init
 .text.dsk_theme_init_ex
                0x00000000e1f071f8       0x2e livedesk/leopard/mod_desktop/functions/built-in.o
                0x00000000e1f071f8                dsk_theme_init_ex
 .text.dsk_theme_exit
                0x00000000e1f07226       0x24 livedesk/leopard/mod_desktop/functions/built-in.o
                0x00000000e1f07226                dsk_theme_exit
 .text.dsk_theme_exit_ex
                0x00000000e1f0724a       0x24 livedesk/leopard/mod_desktop/functions/built-in.o
                0x00000000e1f0724a                dsk_theme_exit_ex
 .text.dsk_theme_set_style
                0x00000000e1f0726e        0xc livedesk/leopard/mod_desktop/functions/built-in.o
                0x00000000e1f0726e                dsk_theme_set_style
 .text.dsk_theme_get_style
                0x00000000e1f0727a        0xc livedesk/leopard/mod_desktop/functions/built-in.o
                0x00000000e1f0727a                dsk_theme_get_style
 .text.dsk_theme_open
                0x00000000e1f07286       0xc4 livedesk/leopard/mod_desktop/functions/built-in.o
                0x00000000e1f07286                dsk_theme_open
 .text.dsk_theme_open_ex
                0x00000000e1f0734a       0xc4 livedesk/leopard/mod_desktop/functions/built-in.o
                0x00000000e1f0734a                dsk_theme_open_ex
 .text.dsk_theme_close
                0x00000000e1f0740e        0x8 livedesk/leopard/mod_desktop/functions/built-in.o
                0x00000000e1f0740e                dsk_theme_close
 .text.dsk_theme_close_ex
                0x00000000e1f07416        0x4 livedesk/leopard/mod_desktop/functions/built-in.o
                0x00000000e1f07416                dsk_theme_close_ex
 .text.dsk_theme_really_close_ex
                0x00000000e1f0741a       0x56 livedesk/leopard/mod_desktop/functions/built-in.o
                0x00000000e1f0741a                dsk_theme_really_close_ex
 .text.dsk_theme_really_close_by_id_ex
                0x00000000e1f07470       0x4c livedesk/leopard/mod_desktop/functions/built-in.o
                0x00000000e1f07470                dsk_theme_really_close_by_id_ex
 .text.dsk_theme_hdl2size
                0x00000000e1f074bc        0xa livedesk/leopard/mod_desktop/functions/built-in.o
                0x00000000e1f074bc                dsk_theme_hdl2size
 .text.dsk_theme_hdl2size_ex
                0x00000000e1f074c6        0xa livedesk/leopard/mod_desktop/functions/built-in.o
                0x00000000e1f074c6                dsk_theme_hdl2size_ex
 .text.dsk_theme_hdl2buf
                0x00000000e1f074d0       0xc6 livedesk/leopard/mod_desktop/functions/built-in.o
                0x00000000e1f074d0                dsk_theme_hdl2buf
 .text.dsk_theme_hdl2buf_ex
                0x00000000e1f07596       0xc6 livedesk/leopard/mod_desktop/functions/built-in.o
                0x00000000e1f07596                dsk_theme_hdl2buf_ex
 .text.dsk_usbd_remove
                0x00000000e1f0765c        0x2 livedesk/leopard/mod_desktop/functions/built-in.o
                0x00000000e1f0765c                dsk_usbd_remove
 .text.dsk_send_insmod_usbh_cmd
                0x00000000e1f0765e        0x4 livedesk/leopard/mod_desktop/functions/built-in.o
                0x00000000e1f0765e                dsk_send_insmod_usbh_cmd
 .text.dsk_send_rmmod_usbh_cmd
                0x00000000e1f07662        0x4 livedesk/leopard/mod_desktop/functions/built-in.o
                0x00000000e1f07662                dsk_send_rmmod_usbh_cmd
 .text.dsk_usbh_is_working
                0x00000000e1f07666        0x4 livedesk/leopard/mod_desktop/functions/built-in.o
                0x00000000e1f07666                dsk_usbh_is_working
 .text.__init_hp_det_pin_init
                0x00000000e1f0766a       0x4c livedesk/leopard/mod_desktop/functions/built-in.o
 .text.dsk_pa_shdn_pin_init
                0x00000000e1f076b6       0x60 livedesk/leopard/mod_desktop/functions/built-in.o
 .text._set_volume
                0x00000000e1f07716       0xa4 livedesk/leopard/mod_desktop/functions/built-in.o
 .text._get_volume
                0x00000000e1f077ba       0x46 livedesk/leopard/mod_desktop/functions/built-in.o
 .text.dsk_volume_set
                0x00000000e1f07800       0x42 livedesk/leopard/mod_desktop/functions/built-in.o
                0x00000000e1f07800                dsk_volume_set
 .text.dsk_volume_get
                0x00000000e1f07842       0x12 livedesk/leopard/mod_desktop/functions/built-in.o
                0x00000000e1f07842                dsk_volume_get
 .text.dsk_volume_inc
                0x00000000e1f07854       0x64 livedesk/leopard/mod_desktop/functions/built-in.o
                0x00000000e1f07854                dsk_volume_inc
 .text.dsk_volume_dec
                0x00000000e1f078b8       0x60 livedesk/leopard/mod_desktop/functions/built-in.o
                0x00000000e1f078b8                dsk_volume_dec
 .text.dsk_volume_on
                0x00000000e1f07918       0x32 livedesk/leopard/mod_desktop/functions/built-in.o
                0x00000000e1f07918                dsk_volume_on
 .text.dsk_volume_off
                0x00000000e1f0794a       0x28 livedesk/leopard/mod_desktop/functions/built-in.o
                0x00000000e1f0794a                dsk_volume_off
 .text.dsk_volume_is_on
                0x00000000e1f07972        0xe livedesk/leopard/mod_desktop/functions/built-in.o
                0x00000000e1f07972                dsk_volume_is_on
 .text.dsk_volume_app_line_in
                0x00000000e1f07980        0x4 livedesk/leopard/mod_desktop/functions/built-in.o
                0x00000000e1f07980                dsk_volume_app_line_in
 .text.dsk_amplifier_onoff
                0x00000000e1f07984       0xd2 livedesk/leopard/mod_desktop/functions/built-in.o
                0x00000000e1f07984                dsk_amplifier_onoff
 .text.dsk_amplifier_is_on
                0x00000000e1f07a56       0x12 livedesk/leopard/mod_desktop/functions/built-in.o
                0x00000000e1f07a56                dsk_amplifier_is_on
 .text.dsk_hp_det_pin_det
                0x00000000e1f07a68       0x5c livedesk/leopard/mod_desktop/functions/built-in.o
                0x00000000e1f07a68                dsk_hp_det_pin_det
 .text.tp_msg_cb
                0x00000000e1f07ac4      0x194 livedesk/leopard/mod_desktop/msg_srv/built-in.o
 .text.key_msg_cb
                0x00000000e1f07c58      0x432 livedesk/leopard/mod_desktop/msg_srv/built-in.o
 .text.msg_stop_scan_file_prog
                0x00000000e1f0808a       0x40 livedesk/leopard/mod_desktop/msg_srv/built-in.o
 .text.dsk_scan_file_key_set_cb
                0x00000000e1f080ca       0x18 livedesk/leopard/mod_desktop/msg_srv/built-in.o
                0x00000000e1f080ca                dsk_scan_file_key_set_cb
 .text.msg_sendhwsccmd
                0x00000000e1f080e2       0x38 livedesk/leopard/mod_desktop/msg_srv/built-in.o
                0x00000000e1f080e2                msg_sendhwsccmd
 .text.ksrv_msg_thread
                0x00000000e1f0811a     0x311a livedesk/leopard/mod_desktop/msg_srv/built-in.o
 .text.msg_srv_init_tp_channel
                0x00000000e1f0b234       0x32 livedesk/leopard/mod_desktop/msg_srv/built-in.o
                0x00000000e1f0b234                msg_srv_init_tp_channel
 .text.msg_srv_init_key_channel
                0x00000000e1f0b266       0x54 livedesk/leopard/mod_desktop/msg_srv/built-in.o
                0x00000000e1f0b266                msg_srv_init_key_channel
 .text.msg_srv_deinit_key_channel
                0x00000000e1f0b2ba       0x1c livedesk/leopard/mod_desktop/msg_srv/built-in.o
                0x00000000e1f0b2ba                msg_srv_deinit_key_channel
 .text.msg_srv_deinit_tp_channel
                0x00000000e1f0b2d6       0x70 livedesk/leopard/mod_desktop/msg_srv/built-in.o
                0x00000000e1f0b2d6                msg_srv_deinit_tp_channel
 .text.msg_srv_init_ksrv_channel
                0x00000000e1f0b346       0x44 livedesk/leopard/mod_desktop/msg_srv/built-in.o
                0x00000000e1f0b346                msg_srv_init_ksrv_channel
 .text.msg_srv_deinit_ksrv_channel
                0x00000000e1f0b38a       0x2e livedesk/leopard/mod_desktop/msg_srv/built-in.o
                0x00000000e1f0b38a                msg_srv_deinit_ksrv_channel
 .text.msg_srv_get_message
                0x00000000e1f0b3b8       0x2a livedesk/leopard/mod_desktop/msg_srv/built-in.o
                0x00000000e1f0b3b8                msg_srv_get_message
 .text.msg_srv_dispatch_message
                0x00000000e1f0b3e2      0x17a livedesk/leopard/mod_desktop/msg_srv/built-in.o
                0x00000000e1f0b3e2                msg_srv_dispatch_message
 .text.msg_srv_thread
                0x00000000e1f0b55c       0xde livedesk/leopard/mod_desktop/msg_srv/built-in.o
                0x00000000e1f0b55c                msg_srv_thread
 .text.msg_emit_init
                0x00000000e1f0b63a       0x42 livedesk/leopard/mod_desktop/msg_srv/built-in.o
                0x00000000e1f0b63a                msg_emit_init
 .text.msg_emit_deinit
                0x00000000e1f0b67c       0x38 livedesk/leopard/mod_desktop/msg_srv/built-in.o
                0x00000000e1f0b67c                msg_emit_deinit
 .text.msg_emit_register_hook
                0x00000000e1f0b6b4       0x36 livedesk/leopard/mod_desktop/msg_srv/built-in.o
                0x00000000e1f0b6b4                msg_emit_register_hook
 .text.msg_emit_unregister_hook
                0x00000000e1f0b6ea       0x30 livedesk/leopard/mod_desktop/msg_srv/built-in.o
                0x00000000e1f0b6ea                msg_emit_unregister_hook
 .text.msg_emit_register_tvout_tp_hook
                0x00000000e1f0b71a       0x3c livedesk/leopard/mod_desktop/msg_srv/built-in.o
                0x00000000e1f0b71a                msg_emit_register_tvout_tp_hook
 .text.esCFG_GetKeyValue
                0x00000000e1f0b756       0x18 ./elibrary/bin//libsyscall.a(syscall.o)
                0x00000000e1f0b756                esCFG_GetKeyValue
 .text.esFSYS_closedir
                0x00000000e1f0b76e       0x18 ./elibrary/bin//libsyscall.a(syscall.o)
                0x00000000e1f0b76e                esFSYS_closedir
 .text.esFSYS_fclose
                0x00000000e1f0b786       0x18 ./elibrary/bin//libsyscall.a(syscall.o)
                0x00000000e1f0b786                esFSYS_fclose
 .text.esFSYS_fioctrl
                0x00000000e1f0b79e       0x18 ./elibrary/bin//libsyscall.a(syscall.o)
                0x00000000e1f0b79e                esFSYS_fioctrl
 .text.esFSYS_fopen
                0x00000000e1f0b7b6       0x16 ./elibrary/bin//libsyscall.a(syscall.o)
                0x00000000e1f0b7b6                esFSYS_fopen
 .text.esFSYS_format
                0x00000000e1f0b7cc       0x18 ./elibrary/bin//libsyscall.a(syscall.o)
                0x00000000e1f0b7cc                esFSYS_format
 .text.esFSYS_fread
                0x00000000e1f0b7e4       0x20 ./elibrary/bin//libsyscall.a(syscall.o)
                0x00000000e1f0b7e4                esFSYS_fread
 .text.esFSYS_fseek
                0x00000000e1f0b804       0x18 ./elibrary/bin//libsyscall.a(syscall.o)
                0x00000000e1f0b804                esFSYS_fseek
 .text.esFSYS_fsync
                0x00000000e1f0b81c       0x18 ./elibrary/bin//libsyscall.a(syscall.o)
                0x00000000e1f0b81c                esFSYS_fsync
 .text.esFSYS_fwrite
                0x00000000e1f0b834       0x20 ./elibrary/bin//libsyscall.a(syscall.o)
                0x00000000e1f0b834                esFSYS_fwrite
 .text.esFSYS_getfscharset
                0x00000000e1f0b854       0x18 ./elibrary/bin//libsyscall.a(syscall.o)
                0x00000000e1f0b854                esFSYS_getfscharset
 .text.esFSYS_opendir
                0x00000000e1f0b86c       0x16 ./elibrary/bin//libsyscall.a(syscall.o)
                0x00000000e1f0b86c                esFSYS_opendir
 .text.esFSYS_setfs
                0x00000000e1f0b882       0x1c ./elibrary/bin//libsyscall.a(syscall.o)
                0x00000000e1f0b882                esFSYS_setfs
 .text.esFSYS_statpt
                0x00000000e1f0b89e       0x18 ./elibrary/bin//libsyscall.a(syscall.o)
                0x00000000e1f0b89e                esFSYS_statpt
 .text.esINPUT_GetLdevID
                0x00000000e1f0b8b6       0x18 ./elibrary/bin//libsyscall.a(syscall.o)
                0x00000000e1f0b8b6                esINPUT_GetLdevID
 .text.esINPUT_LdevCtl
                0x00000000e1f0b8ce       0x18 ./elibrary/bin//libsyscall.a(syscall.o)
                0x00000000e1f0b8ce                esINPUT_LdevCtl
 .text.esINPUT_LdevGrab
                0x00000000e1f0b8e6       0x16 ./elibrary/bin//libsyscall.a(syscall.o)
                0x00000000e1f0b8e6                esINPUT_LdevGrab
 .text.esINPUT_LdevRelease
                0x00000000e1f0b8fc       0x18 ./elibrary/bin//libsyscall.a(syscall.o)
                0x00000000e1f0b8fc                esINPUT_LdevRelease
 .text.esKRNL_CallBack
                0x00000000e1f0b914       0x18 ./elibrary/bin//libsyscall.a(syscall.o)
                0x00000000e1f0b914                esKRNL_CallBack
 .text.esKRNL_QAccept
                0x00000000e1f0b92c       0x16 ./elibrary/bin//libsyscall.a(syscall.o)
                0x00000000e1f0b92c                esKRNL_QAccept
 .text.esKRNL_QCreate
                0x00000000e1f0b942       0x16 ./elibrary/bin//libsyscall.a(syscall.o)
                0x00000000e1f0b942                esKRNL_QCreate
 .text.esKRNL_QDel
                0x00000000e1f0b958       0x16 ./elibrary/bin//libsyscall.a(syscall.o)
                0x00000000e1f0b958                esKRNL_QDel
 .text.esKRNL_QPost
                0x00000000e1f0b96e       0x1a ./elibrary/bin//libsyscall.a(syscall.o)
                0x00000000e1f0b96e                esKRNL_QPost
 .text.esKRNL_SemCreate
                0x00000000e1f0b988       0x16 ./elibrary/bin//libsyscall.a(syscall.o)
                0x00000000e1f0b988                esKRNL_SemCreate
 .text.esKRNL_SemDel
                0x00000000e1f0b99e       0x16 ./elibrary/bin//libsyscall.a(syscall.o)
                0x00000000e1f0b99e                esKRNL_SemDel
 .text.esKRNL_SemPend
                0x00000000e1f0b9b4       0x16 ./elibrary/bin//libsyscall.a(syscall.o)
                0x00000000e1f0b9b4                esKRNL_SemPend
 .text.esKRNL_SemPost
                0x00000000e1f0b9ca       0x1a ./elibrary/bin//libsyscall.a(syscall.o)
                0x00000000e1f0b9ca                esKRNL_SemPost
 .text.esKRNL_SemQuery
                0x00000000e1f0b9e4       0x1a ./elibrary/bin//libsyscall.a(syscall.o)
                0x00000000e1f0b9e4                esKRNL_SemQuery
 .text.esKRNL_TaskNameSet
                0x00000000e1f0b9fe       0x1a ./elibrary/bin//libsyscall.a(syscall.o)
                0x00000000e1f0b9fe                esKRNL_TaskNameSet
 .text.esKRNL_TCreate
                0x00000000e1f0ba18       0x1c ./elibrary/bin//libsyscall.a(syscall.o)
                0x00000000e1f0ba18                esKRNL_TCreate
 .text.esKRNL_TDel
                0x00000000e1f0ba34       0x1e ./elibrary/bin//libsyscall.a(syscall.o)
                0x00000000e1f0ba34                esKRNL_TDel
 .text.esKRNL_TDelReq
                0x00000000e1f0ba52       0x1e ./elibrary/bin//libsyscall.a(syscall.o)
                0x00000000e1f0ba52                esKRNL_TDelReq
 .text.esKRNL_TimeDly
                0x00000000e1f0ba70       0x16 ./elibrary/bin//libsyscall.a(syscall.o)
                0x00000000e1f0ba70                esKRNL_TimeDly
 .text.esKSRV_GetMsg
                0x00000000e1f0ba86       0x16 ./elibrary/bin//libsyscall.a(syscall.o)
                0x00000000e1f0ba86                esKSRV_GetMsg
 .text.esKSRV_Get_Display_Hld
                0x00000000e1f0ba9c       0x16 ./elibrary/bin//libsyscall.a(syscall.o)
                0x00000000e1f0ba9c                esKSRV_Get_Display_Hld
 .text.esMEMS_Balloc
                0x00000000e1f0bab2       0x16 ./elibrary/bin//libsyscall.a(syscall.o)
                0x00000000e1f0bab2                esMEMS_Balloc
 .text.esMEMS_Bfree
                0x00000000e1f0bac8       0x16 ./elibrary/bin//libsyscall.a(syscall.o)
                0x00000000e1f0bac8                esMEMS_Bfree
 .text.esMEMS_Malloc
                0x00000000e1f0bade       0x1a ./elibrary/bin//libsyscall.a(syscall.o)
                0x00000000e1f0bade                esMEMS_Malloc
 .text.esMEMS_Mfree
                0x00000000e1f0baf8       0x16 ./elibrary/bin//libsyscall.a(syscall.o)
                0x00000000e1f0baf8                esMEMS_Mfree
 .text.esMODS_MClose
                0x00000000e1f0bb0e       0x18 ./elibrary/bin//libsyscall.a(syscall.o)
                0x00000000e1f0bb0e                esMODS_MClose
 .text.esMODS_MInstall
                0x00000000e1f0bb26       0x18 ./elibrary/bin//libsyscall.a(syscall.o)
                0x00000000e1f0bb26                esMODS_MInstall
 .text.esMODS_MIoctrl
                0x00000000e1f0bb3e       0x1a ./elibrary/bin//libsyscall.a(syscall.o)
                0x00000000e1f0bb3e                esMODS_MIoctrl
 .text.esMODS_MOpen
                0x00000000e1f0bb58       0x16 ./elibrary/bin//libsyscall.a(syscall.o)
                0x00000000e1f0bb58                esMODS_MOpen
 .text.esMODS_MUninstall
                0x00000000e1f0bb6e       0x18 ./elibrary/bin//libsyscall.a(syscall.o)
                0x00000000e1f0bb6e                esMODS_MUninstall
 .text.esPINS_PinGrpReq
                0x00000000e1f0bb86       0x16 ./elibrary/bin//libsyscall.a(syscall.o)
                0x00000000e1f0bb86                esPINS_PinGrpReq
 .text.esPINS_ReadPinData
                0x00000000e1f0bb9c       0x16 ./elibrary/bin//libsyscall.a(syscall.o)
                0x00000000e1f0bb9c                esPINS_ReadPinData
 .text.esPINS_WritePinData
                0x00000000e1f0bbb2       0x1a ./elibrary/bin//libsyscall.a(syscall.o)
                0x00000000e1f0bbb2                esPINS_WritePinData
 .text.esRESM_RClose
                0x00000000e1f0bbcc       0x18 ./elibrary/bin//libsyscall.a(syscall.o)
                0x00000000e1f0bbcc                esRESM_RClose
 .text.esRESM_ROpen
                0x00000000e1f0bbe4       0x16 ./elibrary/bin//libsyscall.a(syscall.o)
                0x00000000e1f0bbe4                esRESM_ROpen
 .text.esRESM_RRead
                0x00000000e1f0bbfa       0x20 ./elibrary/bin//libsyscall.a(syscall.o)
                0x00000000e1f0bbfa                esRESM_RRead
 .text.esRESM_RSeek
                0x00000000e1f0bc1a       0x18 ./elibrary/bin//libsyscall.a(syscall.o)
                0x00000000e1f0bc1a                esRESM_RSeek
 .text.esSIOS_putstr
                0x00000000e1f0bc32       0x14 ./elibrary/bin//libsyscall.a(syscall.o)
                0x00000000e1f0bc32                esSIOS_putstr
 .text._close_r
                0x00000000e1f0bc46       0x18 ./elibrary/bin//libsyscall.a(syscall_fops.o)
                0x00000000e1f0bc46                _close_r
 .text._open_r  0x00000000e1f0bc5e       0x18 ./elibrary/bin//libsyscall.a(syscall_fops.o)
                0x00000000e1f0bc5e                _open_r
 .text.ioctl    0x00000000e1f0bc76       0x28 ./elibrary/bin//libsyscall.a(syscall_fops.o)
                0x00000000e1f0bc76                ioctl
 .text.eLIBs_fopen
                0x00000000e1f0bc9e        0x4 ./elibrary/bin//libminic.a(elibs_stdio.o)
                0x00000000e1f0bc9e                eLIBs_fopen
 .text.eLIBs_fclose
                0x00000000e1f0bca2        0x4 ./elibrary/bin//libminic.a(elibs_stdio.o)
                0x00000000e1f0bca2                eLIBs_fclose
 .text.eLIBs_fsync
                0x00000000e1f0bca6        0x4 ./elibrary/bin//libminic.a(elibs_stdio.o)
                0x00000000e1f0bca6                eLIBs_fsync
 .text.eLIBs_fread
                0x00000000e1f0bcaa        0x4 ./elibrary/bin//libminic.a(elibs_stdio.o)
                0x00000000e1f0bcaa                eLIBs_fread
 .text.eLIBs_fwrite
                0x00000000e1f0bcae        0x4 ./elibrary/bin//libminic.a(elibs_stdio.o)
                0x00000000e1f0bcae                eLIBs_fwrite
 .text.eLIBs_fseek
                0x00000000e1f0bcb2       0x12 ./elibrary/bin//libminic.a(elibs_stdio.o)
                0x00000000e1f0bcb2                eLIBs_fseek
 .text.eLIBs_fioctrl
                0x00000000e1f0bcc4        0x4 ./elibrary/bin//libminic.a(elibs_stdio.o)
                0x00000000e1f0bcc4                eLIBs_fioctrl
 .text.eLIBs_closedir
                0x00000000e1f0bcc8        0x4 ./elibrary/bin//libminic.a(elibs_stdio.o)
                0x00000000e1f0bcc8                eLIBs_closedir
 .text.eLIBs_format
                0x00000000e1f0bccc        0x4 ./elibrary/bin//libminic.a(elibs_stdio.o)
                0x00000000e1f0bccc                eLIBs_format
 .text.eLIBs_IsPartFormated
                0x00000000e1f0bcd0       0x80 ./elibrary/bin//libminic.a(elibs_stdio.o)
                0x00000000e1f0bcd0                eLIBs_IsPartFormated
 .text.eLIBs_GetFSCharset
                0x00000000e1f0bd50        0xc ./elibrary/bin//libminic.a(elibs_stdio.o)
                0x00000000e1f0bd50                eLIBs_GetFSCharset
 .text.eLIBs_vprintf
                0x00000000e1f0bd5c       0x4c ./elibrary/bin//libminic.a(elibs_stdio.o)
                0x00000000e1f0bd5c                eLIBs_vprintf
 .text.eLIBs_printf
                0x00000000e1f0bda8       0x20 ./elibrary/bin//libminic.a(elibs_stdio.o)
                0x00000000e1f0bda8                eLIBs_printf
                0x00000000e1f0bda8                printk
 .text.eLIBs_setfs
                0x00000000e1f0bdc8       0x10 ./elibrary/bin//libminic.a(elibs_stdio.o)
                0x00000000e1f0bdc8                eLIBs_setfs
 .text.eLIBs_strlen
                0x00000000e1f0bdd8        0xc ./elibrary/bin//libminic.a(elibs_string.o)
                0x00000000e1f0bdd8                eLIBs_strlen
 .text.eLIBs_strcpy
                0x00000000e1f0bde4        0xc ./elibrary/bin//libminic.a(elibs_string.o)
                0x00000000e1f0bde4                eLIBs_strcpy
 .text.eLIBs_strncpy
                0x00000000e1f0bdf0        0xc ./elibrary/bin//libminic.a(elibs_string.o)
                0x00000000e1f0bdf0                eLIBs_strncpy
 .text.eLIBs_strcmp
                0x00000000e1f0bdfc        0xc ./elibrary/bin//libminic.a(elibs_string.o)
                0x00000000e1f0bdfc                eLIBs_strcmp
 .text.eLIBs_strncmp
                0x00000000e1f0be08        0xc ./elibrary/bin//libminic.a(elibs_string.o)
                0x00000000e1f0be08                eLIBs_strncmp
 .text.eLIBs_memset
                0x00000000e1f0be14        0xc ./elibrary/bin//libminic.a(elibs_string.o)
                0x00000000e1f0be14                eLIBs_memset
 .text.eLIBs_memcpy
                0x00000000e1f0be20        0xc ./elibrary/bin//libminic.a(elibs_string.o)
                0x00000000e1f0be20                eLIBs_memcpy
 .text.eLIBs_memcmp
                0x00000000e1f0be2c       0x18 ./elibrary/bin//libminic.a(elibs_string.o)
                0x00000000e1f0be2c                eLIBs_memcmp
 .text.eLIBs_vsnprintf
                0x00000000e1f0be44        0xc ./elibrary/bin//libminic.a(elibs_sprintf.o)
                0x00000000e1f0be44                eLIBs_vsnprintf
 .text.eLIBs_vscnprintf
                0x00000000e1f0be50       0x1c ./elibrary/bin//libminic.a(elibs_sprintf.o)
                0x00000000e1f0be50                eLIBs_vscnprintf
 .text.AZ100_IsCompress
                0x00000000e1f0be6c       0x2e ./elibrary/bin//liblzma.a(az100.o)
                0x00000000e1f0be6c                AZ100_IsCompress
 .text.AZ100_GetUncompressSize
                0x00000000e1f0be9a       0x52 ./elibrary/bin//liblzma.a(az100.o)
                0x00000000e1f0be9a                AZ100_GetUncompressSize
 .text.AZ100_Uncompress
                0x00000000e1f0beec       0x24 ./elibrary/bin//liblzma.a(az100.o)
                0x00000000e1f0beec                AZ100_Uncompress
 .text.AZ100_DataUncompress
                0x00000000e1f0bf10       0x26 ./elibrary/bin//liblzma.a(compress.o)
                0x00000000e1f0bf10                AZ100_DataUncompress
 .text.SzFree   0x00000000e1f0bf36        0x6 ./elibrary/bin//liblzma.a(LzmaLib.o)
 .text.SzAlloc  0x00000000e1f0bf3c        0x6 ./elibrary/bin//liblzma.a(LzmaLib.o)
 .text.LzmaUncompress
                0x00000000e1f0bf42       0x20 ./elibrary/bin//liblzma.a(LzmaLib.o)
                0x00000000e1f0bf42                LzmaUncompress
 .text.LzmaDec_WriteRem
                0x00000000e1f0bf62       0x80 ./elibrary/bin//liblzma.a(LzmaDec.o)
 .text.LzmaDec_DecodeReal2
                0x00000000e1f0bfe2      0xc54 ./elibrary/bin//liblzma.a(LzmaDec.o)
 .text.LzmaDec_TryDummy
                0x00000000e1f0cc36      0x500 ./elibrary/bin//liblzma.a(LzmaDec.o)
 .text.LzmaDec_Init
                0x00000000e1f0d136       0x1a ./elibrary/bin//liblzma.a(LzmaDec.o)
                0x00000000e1f0d136                LzmaDec_Init
 .text.LzmaDec_DecodeToDic
                0x00000000e1f0d150      0x26c ./elibrary/bin//liblzma.a(LzmaDec.o)
                0x00000000e1f0d150                LzmaDec_DecodeToDic
 .text.LzmaDec_FreeProbs
                0x00000000e1f0d3bc       0x1c ./elibrary/bin//liblzma.a(LzmaDec.o)
                0x00000000e1f0d3bc                LzmaDec_FreeProbs
 .text.LzmaDec_AllocateProbs2
                0x00000000e1f0d3d8       0x58 ./elibrary/bin//liblzma.a(LzmaDec.o)
 .text.LzmaProps_Decode
                0x00000000e1f0d430       0x5c ./elibrary/bin//liblzma.a(LzmaDec.o)
                0x00000000e1f0d430                LzmaProps_Decode
 .text.LzmaDec_AllocateProbs
                0x00000000e1f0d48c       0x30 ./elibrary/bin//liblzma.a(LzmaDec.o)
                0x00000000e1f0d48c                LzmaDec_AllocateProbs
 .text.LzmaDecode
                0x00000000e1f0d4bc       0xaa ./elibrary/bin//liblzma.a(LzmaDec.o)
                0x00000000e1f0d4bc                LzmaDecode
 .text.Lang_Open
                0x00000000e1f0d566      0x16c ./elibrary/bin//libresreader.a(LangDec.o)
                0x00000000e1f0d566                Lang_Open
 .text.Lang_GetStringSize
                0x00000000e1f0d6d2       0xda ./elibrary/bin//libresreader.a(LangDec.o)
                0x00000000e1f0d6d2                Lang_GetStringSize
 .text.Lang_GetString
                0x00000000e1f0d7ac      0x102 ./elibrary/bin//libresreader.a(LangDec.o)
                0x00000000e1f0d7ac                Lang_GetString
 .text.Lang_Close
                0x00000000e1f0d8ae       0x28 ./elibrary/bin//libresreader.a(LangDec.o)
                0x00000000e1f0d8ae                Lang_Close
 .text.search_res
                0x00000000e1f0d8d6       0xd0 ./elibrary/bin//libresreader.a(themedec.o)
 .text.OpenRes  0x00000000e1f0d9a6      0x11e ./elibrary/bin//libresreader.a(themedec.o)
                0x00000000e1f0d9a6                OpenRes
 .text.CloseRes
                0x00000000e1f0dac4       0x5a ./elibrary/bin//libresreader.a(themedec.o)
                0x00000000e1f0dac4                CloseRes
 .text.GetResSize
                0x00000000e1f0db1e       0xbc ./elibrary/bin//libresreader.a(themedec.o)
                0x00000000e1f0db1e                GetResSize
 .text.GetRes   0x00000000e1f0dbda       0xcc ./elibrary/bin//libresreader.a(themedec.o)
                0x00000000e1f0dbda                GetRes
 .text.eLIBs_malloc
                0x00000000e1f0dca6        0xa ./elibrary/bin//libminic.a(elibs_stdlib.o)
                0x00000000e1f0dca6                eLIBs_malloc
 .text.eLIBs_free
                0x00000000e1f0dcb0        0xc ./elibrary/bin//libminic.a(elibs_stdlib.o)
                0x00000000e1f0dcb0                eLIBs_free
 *(x$fpl$*)
 *(.stub)
 *(.gnu.warning)
 *(.gnu.linkonce.t*)
                0x00000000e1f0dcbc                . = ALIGN (0x4)
 *fill*         0x00000000e1f0dcbc        0x0 

.mod.rodata     0x00000000e1f0dcbc      0xe94
 *(.rodata)
 *fill*         0x00000000e1f0dcbc        0x4 
 .rodata        0x00000000e1f0dcc0       0x98 livedesk/leopard/mod_desktop/functions/built-in.o
 *(.rodata.*)
 .rodata.DESKTOP_MOpen.str1.8
                0x00000000e1f0dd58       0x14 livedesk/leopard/mod_desktop/mod_desktop.o
 .rodata.eq_to_bas
                0x00000000e1f0dd6c       0x20 livedesk/leopard/mod_desktop/functions/built-in.o
 .rodata.eq_to_mid
                0x00000000e1f0dd8c       0x20 livedesk/leopard/mod_desktop/functions/built-in.o
 .rodata.eq_to_tre
                0x00000000e1f0ddac       0x20 livedesk/leopard/mod_desktop/functions/built-in.o
 .rodata.dsk_aux_item_reset
                0x00000000e1f0ddcc       0x1c livedesk/leopard/mod_desktop/functions/built-in.o
 .rodata.dsk_aux_set_delay_level
                0x00000000e1f0dde8       0x18 livedesk/leopard/mod_desktop/functions/built-in.o
 .rodata.dsk_aux_reset_EQ_mode
                0x00000000e1f0de00       0x18 livedesk/leopard/mod_desktop/functions/built-in.o
 .rodata.aux_eq_dbsson_table
                0x00000000e1f0de18       0x6c livedesk/leopard/mod_desktop/functions/built-in.o
                0x00000000e1f0de18                aux_eq_dbsson_table
 *fill*         0x00000000e1f0de84        0x4 
 .rodata.dsk_audio_play_open.str1.8
                0x00000000e1f0de88       0x11 livedesk/leopard/mod_desktop/functions/built-in.o
 *fill*         0x00000000e1f0de99        0x7 
 .rodata.dsk_aux_mcu_open.str1.8
                0x00000000e1f0dea0        0x9 livedesk/leopard/mod_desktop/functions/built-in.o
 *fill*         0x00000000e1f0dea9        0x7 
 .rodata.dsk_aux_open.str1.8
                0x00000000e1f0deb0        0x9 livedesk/leopard/mod_desktop/functions/built-in.o
 *fill*         0x00000000e1f0deb9        0x3 
 .rodata.dsk_aux_reset.cst4
                0x00000000e1f0debc        0x4 livedesk/leopard/mod_desktop/functions/built-in.o
 .rodata.dsk_aux_reset.cst8
                0x00000000e1f0dec0        0x8 livedesk/leopard/mod_desktop/functions/built-in.o
 .rodata.dsk_aux_reset.str1.8
                0x00000000e1f0dec8        0xd livedesk/leopard/mod_desktop/functions/built-in.o
 *fill*         0x00000000e1f0ded5        0x3 
 .rodata.dsk_btmoudel_open.str1.8
                0x00000000e1f0ded8        0x8 livedesk/leopard/mod_desktop/functions/built-in.o
 .rodata.dsk_send_bluetooth_upgrate_file_data.str1.8
                0x00000000e1f0dee0        0xb livedesk/leopard/mod_desktop/functions/built-in.o
 *fill*         0x00000000e1f0deeb        0x5 
 .rodata.dsk_set_bluetooth_luauch_app.str1.8
                0x00000000e1f0def0       0x3d livedesk/leopard/mod_desktop/functions/built-in.o
 *fill*         0x00000000e1f0df2d        0x3 
 .rodata.dsk_get_fs_charset.str1.8
                0x00000000e1f0df30        0x4 livedesk/leopard/mod_desktop/functions/built-in.o
 *fill*         0x00000000e1f0df34        0x4 
 .rodata.dsk_dab_open.str1.8
                0x00000000e1f0df38        0x9 livedesk/leopard/mod_desktop/functions/built-in.o
 *fill*         0x00000000e1f0df41        0x7 
 .rodata.CSWTCH.0
                0x00000000e1f0df48       0x10 livedesk/leopard/mod_desktop/functions/built-in.o
 .rodata.dsk_display_dskfmt2epdkfmt
                0x00000000e1f0df58       0xa0 livedesk/leopard/mod_desktop/functions/built-in.o
 .rodata.dsk_mcu_open.str1.8
                0x00000000e1f0dff8        0xa livedesk/leopard/mod_desktop/functions/built-in.o
 *fill*         0x00000000e1f0e002        0x6 
 .rodata.dsk_send_mcu_current_source.str1.8
                0x00000000e1f0e008       0x1b livedesk/leopard/mod_desktop/functions/built-in.o
 *fill*         0x00000000e1f0e023        0x5 
 .rodata.dsk_send_mcu_io.str1.8
                0x00000000e1f0e028       0x22 livedesk/leopard/mod_desktop/functions/built-in.o
 *fill*         0x00000000e1f0e04a        0x6 
 .rodata.dsk_send_mcu_reset.str1.8
                0x00000000e1f0e050       0x36 livedesk/leopard/mod_desktop/functions/built-in.o
 *fill*         0x00000000e1f0e086        0x2 
 .rodata.dsk_orchid_createDisk.str1.8
                0x00000000e1f0e088        0xc livedesk/leopard/mod_desktop/functions/built-in.o
 *fill*         0x00000000e1f0e094        0x4 
 .rodata.dsk_power_dev_init.str1.8
                0x00000000e1f0e098        0xb livedesk/leopard/mod_desktop/functions/built-in.o
 *fill*         0x00000000e1f0e0a3        0x1 
 .rodata.dsk_reg_get_para_by_app
                0x00000000e1f0e0a4       0x40 livedesk/leopard/mod_desktop/functions/built-in.o
 .rodata.dsk_reg_save_cur_play_info
                0x00000000e1f0e0e4       0x28 livedesk/leopard/mod_desktop/functions/built-in.o
 *fill*         0x00000000e1f0e10c        0x4 
 .rodata.__func__.22875
                0x00000000e1f0e110       0x19 livedesk/leopard/mod_desktop/functions/built-in.o
 *fill*         0x00000000e1f0e129        0x7 
 .rodata.__func__.22882
                0x00000000e1f0e130       0x1a livedesk/leopard/mod_desktop/functions/built-in.o
 *fill*         0x00000000e1f0e14a        0x6 
 .rodata.__func__.22889
                0x00000000e1f0e150       0x1a livedesk/leopard/mod_desktop/functions/built-in.o
 *fill*         0x00000000e1f0e16a        0x6 
 .rodata.__func__.22910
                0x00000000e1f0e170       0x12 livedesk/leopard/mod_desktop/functions/built-in.o
 *fill*         0x00000000e1f0e182        0x6 
 .rodata.dsk_reg_init_para.cst8
                0x00000000e1f0e188       0x20 livedesk/leopard/mod_desktop/functions/built-in.o
 .rodata.dsk_reg_init_para.str1.8
                0x00000000e1f0e1a8      0x167 livedesk/leopard/mod_desktop/functions/built-in.o
                                        0x17f (size before relaxing)
 *fill*         0x00000000e1f0e30f        0x1 
 .rodata.save_flash_task.str1.8
                0x00000000e1f0e310       0xc5 livedesk/leopard/mod_desktop/functions/built-in.o
 *fill*         0x00000000e1f0e3d5        0x3 
 .rodata.__init_hp_det_pin_init.str1.8
                0x00000000e1f0e3d8       0x13 livedesk/leopard/mod_desktop/functions/built-in.o
 .rodata._set_volume.str1.8
                0x00000000e1f0e3eb       0x11 livedesk/leopard/mod_desktop/functions/built-in.o
 *fill*         0x00000000e1f0e3eb        0x5 
 .rodata.dsk_pa_shdn_pin_init.str1.8
                0x00000000e1f0e3f0        0x8 livedesk/leopard/mod_desktop/functions/built-in.o
 .rodata.key_msg_cb
                0x00000000e1f0e3f8      0x2e8 livedesk/leopard/mod_desktop/msg_srv/built-in.o
 .rodata.ksrv_msg_thread
                0x00000000e1f0e6e0       0x70 livedesk/leopard/mod_desktop/msg_srv/built-in.o
 .rodata.ksrv_msg_thread.str1.8
                0x00000000e1f0e750       0xc2 livedesk/leopard/mod_desktop/msg_srv/built-in.o
 *fill*         0x00000000e1f0e812        0x6 
 .rodata.msg_emit_init.str1.8
                0x00000000e1f0e818        0xd livedesk/leopard/mod_desktop/msg_srv/built-in.o
 *fill*         0x00000000e1f0e825        0x3 
 .rodata.msg_sendhwsccmd.str1.8
                0x00000000e1f0e828        0xa livedesk/leopard/mod_desktop/msg_srv/built-in.o
 *fill*         0x00000000e1f0e832        0x6 
 .rodata.msg_srv_init_key_channel.str1.8
                0x00000000e1f0e838        0xd livedesk/leopard/mod_desktop/msg_srv/built-in.o
 *fill*         0x00000000e1f0e845        0x3 
 .rodata.msg_srv_init_ksrv_channel.str1.8
                0x00000000e1f0e848        0xd livedesk/leopard/mod_desktop/msg_srv/built-in.o
 *fill*         0x00000000e1f0e855        0x3 
 .rodata.msg_srv_init_tp_channel.str1.8
                0x00000000e1f0e858        0x7 livedesk/leopard/mod_desktop/msg_srv/built-in.o
 *fill*         0x00000000e1f0e85f        0x1 
 .rodata.AZ100_IsCompress.str1.8
                0x00000000e1f0e860        0x5 ./elibrary/bin//liblzma.a(az100.o)
 *fill*         0x00000000e1f0e865        0x3 
 .rodata.Lang_Open.str1.8
                0x00000000e1f0e868       0x11 ./elibrary/bin//libresreader.a(LangDec.o)
 *fill*         0x00000000e1f0e879        0x7 
 .rodata.CloseRes.str1.8
                0x00000000e1f0e880       0x4e ./elibrary/bin//libresreader.a(themedec.o)
 *fill*         0x00000000e1f0e8ce        0x2 
 .rodata.GetResSize.str1.8
                0x00000000e1f0e8d0       0xe1 ./elibrary/bin//libresreader.a(themedec.o)
 *fill*         0x00000000e1f0e9b1        0x7 
 .rodata.OpenRes.str1.8
                0x00000000e1f0e9b8      0x150 ./elibrary/bin//libresreader.a(themedec.o)
                                        0x153 (size before relaxing)
 .rodata.__func__.4976
                0x00000000e1f0eb08        0x8 ./elibrary/bin//libresreader.a(themedec.o)
 .rodata.__func__.4981
                0x00000000e1f0eb10        0x9 ./elibrary/bin//libresreader.a(themedec.o)
 *fill*         0x00000000e1f0eb19        0x7 
 .rodata.__func__.5008
                0x00000000e1f0eb20        0xb ./elibrary/bin//libresreader.a(themedec.o)
 *fill*         0x00000000e1f0eb2b        0x5 
 .rodata.__func__.5035
                0x00000000e1f0eb30        0x7 ./elibrary/bin//libresreader.a(themedec.o)
 *fill*         0x00000000e1f0eb37        0x1 
 .rodata.cst8   0x00000000e1f0eb38        0x8 /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/../../../../riscv64-unknown-elf/lib/rv64imafdcxtheadc/lp64d/libc.a(lib_a-strcpy.o)
 .rodata.cst8   0x00000000e1f0eb40       0x10 /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/../../../../riscv64-unknown-elf/lib/rv64imafdcxtheadc/lp64d/libc.a(lib_a-strncpy.o)
 *(.const*)
 *(.gnu.linkonce.r*)
                0x00000000e1f0eb50                . = ALIGN (0x4)

.mod.data       0x00000000e1f0eb50      0x8b8
 *(.data)
 .data          0x00000000e1f0eb50      0x750 /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/../../../../riscv64-unknown-elf/lib/rv64imafdcxtheadc/lp64d/libc.a(lib_a-impure.o)
                0x00000000e1f0eb50                _impure_ptr
 *(.data.*)
 .data.Eq_Gain  0x00000000e1f0f2a0       0x82 livedesk/leopard/mod_desktop/functions/built-in.o
                0x00000000e1f0f2a0                Eq_Gain
 *fill*         0x00000000e1f0f322        0x6 
 .data.init_position_para
                0x00000000e1f0f328       0xb0 livedesk/leopard/mod_desktop/functions/built-in.o
                0x00000000e1f0f328                init_position_para
 .data.into_ble_flag.23527
                0x00000000e1f0f3d8        0x1 livedesk/leopard/mod_desktop/functions/built-in.o
 .data.fs_charset
                0x00000000e1f0f3d9        0x1 livedesk/leopard/mod_desktop/functions/built-in.o
 .data.isn_charset
                0x00000000e1f0f3da        0x1 livedesk/leopard/mod_desktop/functions/built-in.o
 *fill*         0x00000000e1f0f3db        0x1 
 .data.lang_ID  0x00000000e1f0f3dc        0x2 livedesk/leopard/mod_desktop/functions/built-in.o
 *fill*         0x00000000e1f0f3de        0x2 
 .data.power_fp
                0x00000000e1f0f3e0        0x4 livedesk/leopard/mod_desktop/functions/built-in.o
 .data.dsk_volume
                0x00000000e1f0f3e4        0x4 livedesk/leopard/mod_desktop/functions/built-in.o
 .data.dsk_volume_gate_on
                0x00000000e1f0f3e8        0x1 livedesk/leopard/mod_desktop/functions/built-in.o
 *fill*         0x00000000e1f0f3e9        0x3 
 .data.last_action.23514
                0x00000000e1f0f3ec        0x4 livedesk/leopard/mod_desktop/msg_srv/built-in.o
 .data.last_id.23515
                0x00000000e1f0f3f0        0x4 livedesk/leopard/mod_desktop/msg_srv/built-in.o
 *fill*         0x00000000e1f0f3f4        0x4 
 .data.g_Alloc  0x00000000e1f0f3f8       0x10 ./elibrary/bin//liblzma.a(LzmaLib.o)
 *(.gnu.linkonce.d.*)
 *(.sdata)
 *(.sdata.*)
 *(.gnu.linkonce.s.*)
 *(.sdata2)
 *(.sdata2.*)
 *(.gnu.linkonce.s2.*)
                0x00000000e1f0f408                . = ALIGN (0x4)

.mod.bss        0x00000000e1f0f408      0xa5c
                0x00000000e1f0f408                __bss_start = ABSOLUTE (.)
 *(.bss)
 *(.bss.*)
 .bss.desktp_data
                0x00000000e1f0f408       0x10 livedesk/leopard/mod_desktop/mod_desktop.o
                0x00000000e1f0f408                desktp_data
 .bss.hlang     0x00000000e1f0f418        0x8 livedesk/leopard/mod_desktop/functions/built-in.o
 .bss.low_power_cnt.22977
                0x00000000e1f0f420        0x4 livedesk/leopard/mod_desktop/functions/built-in.o
 *fill*         0x00000000e1f0f424        0x4 
 .bss.all_app_para
                0x00000000e1f0f428        0x8 livedesk/leopard/mod_desktop/functions/built-in.o
 .bss.all_app_para_bak
                0x00000000e1f0f430        0x8 livedesk/leopard/mod_desktop/functions/built-in.o
 .bss.dsk_reg_flush_thread_id
                0x00000000e1f0f438        0x4 livedesk/leopard/mod_desktop/functions/built-in.o
 *fill*         0x00000000e1f0f43c        0x4 
 .bss.spinor_proc_flag_sem
                0x00000000e1f0f440        0x8 livedesk/leopard/mod_desktop/functions/built-in.o
 .bss.spinor_proc_sem
                0x00000000e1f0f448        0x8 livedesk/leopard/mod_desktop/functions/built-in.o
 .bss.g_id2hdl  0x00000000e1f0f450      0x130 livedesk/leopard/mod_desktop/functions/built-in.o
 .bss.h_kres    0x00000000e1f0f580        0x8 livedesk/leopard/mod_desktop/functions/built-in.o
 .bss.hres      0x00000000e1f0f588        0x8 livedesk/leopard/mod_desktop/functions/built-in.o
 .bss.style     0x00000000e1f0f590        0x4 livedesk/leopard/mod_desktop/functions/built-in.o
 .bss.g_amplifier_status
                0x00000000e1f0f594        0x4 livedesk/leopard/mod_desktop/functions/built-in.o
 .bss.g_hp_det_hdl
                0x00000000e1f0f598        0x8 livedesk/leopard/mod_desktop/functions/built-in.o
 .bss.g_open_amp
                0x00000000e1f0f5a0        0x1 livedesk/leopard/mod_desktop/functions/built-in.o
 *fill*         0x00000000e1f0f5a1        0x7 
 .bss.g_pa_hdl  0x00000000e1f0f5a8        0x8 livedesk/leopard/mod_desktop/functions/built-in.o
 .bss.cb_ctx_scan_key_next
                0x00000000e1f0f5b0        0x8 livedesk/leopard/mod_desktop/msg_srv/built-in.o
 .bss.cb_scan_key_next
                0x00000000e1f0f5b8        0x8 livedesk/leopard/mod_desktop/msg_srv/built-in.o
 .bss.emit_ctr  0x00000000e1f0f5c0      0x858 livedesk/leopard/mod_desktop/msg_srv/built-in.o
 .bss.last_touch_action
                0x00000000e1f0fe18        0x4 livedesk/leopard/mod_desktop/msg_srv/built-in.o
 *fill*         0x00000000e1f0fe1c        0x4 
 .bss.msg.23516
                0x00000000e1f0fe20       0x40 livedesk/leopard/mod_desktop/msg_srv/built-in.o
 .bss.msg_srv_tid
                0x00000000e1f0fe60        0x4 livedesk/leopard/mod_desktop/msg_srv/built-in.o
 *(.gnu.linkonce.b.*)
 *(.sbss)
 *(.sbss.*)
 *(.gnu.linkonce.sb.*)
 *(.sbss2)
 *(.sbss2.*)
 *(.gnu.linkonce.sb2.*)
 *(COMMON)
 *(.scommon)
                0x00000000e1f0fe64                . = ALIGN (0x4)
                0x00000000e1f0fe64                __bss_end = ABSOLUTE (.)
                0x00000000e1f0fe64                _end = ABSOLUTE (.)

MAGIC           0x00000000ffff0000       0x50
 *magic.o(.magic)
 .magic         0x00000000ffff0000       0x50 livedesk/leopard/mod_desktop/magic.o
                0x00000000ffff0000                modinfo_desktop

.reginfo
 *(.reginfo)

.mdebug
 *(.mdebug)

.note
 *(.note)

.comment        0x0000000000000000       0x32
 *(.comment)
 .comment       0x0000000000000000       0x32 livedesk/leopard/mod_desktop/desktop_api.o
                                         0x33 (size before relaxing)
 .comment       0x0000000000000032       0x33 livedesk/leopard/mod_desktop/mod_desktop.o
 .comment       0x0000000000000032       0x33 livedesk/leopard/mod_desktop/magic.o
 .comment       0x0000000000000032      0x3fc livedesk/leopard/mod_desktop/functions/built-in.o
 .comment       0x0000000000000032       0x33 livedesk/leopard/mod_desktop/msg_srv/built-in.o
 .comment       0x0000000000000032       0x33 ./elibrary/bin//libsyscall.a(syscall.o)
 .comment       0x0000000000000032       0x33 ./elibrary/bin//libsyscall.a(syscall_fops.o)
 .comment       0x0000000000000032       0x33 ./elibrary/bin//libminic.a(elibs_stdio.o)
 .comment       0x0000000000000032       0x33 ./elibrary/bin//libminic.a(elibs_string.o)
 .comment       0x0000000000000032       0x33 ./elibrary/bin//libminic.a(elibs_sprintf.o)
 .comment       0x0000000000000032       0x33 ./elibrary/bin//liblzma.a(az100.o)
 .comment       0x0000000000000032       0x33 ./elibrary/bin//liblzma.a(compress.o)
 .comment       0x0000000000000032       0x33 ./elibrary/bin//liblzma.a(LzmaLib.o)
 .comment       0x0000000000000032       0x33 ./elibrary/bin//liblzma.a(LzmaDec.o)
 .comment       0x0000000000000032       0x33 ./elibrary/bin//libresreader.a(LangDec.o)
 .comment       0x0000000000000032       0x33 ./elibrary/bin//libresreader.a(themedec.o)
 .comment       0x0000000000000032       0x33 ./elibrary/bin//libminic.a(elibs_stdlib.o)
 .comment       0x0000000000000032       0x33 /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/../../../../riscv64-unknown-elf/lib/rv64imafdcxtheadc/lp64d/libc.a(lib_a-impure.o)
 .comment       0x0000000000000032       0x33 /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/../../../../riscv64-unknown-elf/lib/rv64imafdcxtheadc/lp64d/libc.a(lib_a-memcpy.o)
 .comment       0x0000000000000032       0x33 /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/../../../../riscv64-unknown-elf/lib/rv64imafdcxtheadc/lp64d/libc.a(lib_a-strcpy.o)
 .comment       0x0000000000000032       0x33 /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/../../../../riscv64-unknown-elf/lib/rv64imafdcxtheadc/lp64d/libc.a(lib_a-strncpy.o)
 .comment       0x0000000000000032       0x33 /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/../../../../riscv64-unknown-elf/lib/rv64imafdcxtheadc/lp64d/libc.a(lib_a-sysclose.o)
 .comment       0x0000000000000032       0x33 /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/../../../../riscv64-unknown-elf/lib/rv64imafdcxtheadc/lp64d/libc.a(lib_a-sysopen.o)

.riscv.attributes
                0x0000000000000000       0x48
 .riscv.attributes
                0x0000000000000000       0x3d livedesk/leopard/mod_desktop/desktop_api.o
 .riscv.attributes
                0x000000000000003d       0x3d livedesk/leopard/mod_desktop/mod_desktop.o
 .riscv.attributes
                0x000000000000007a       0x3d livedesk/leopard/mod_desktop/magic.o
 .riscv.attributes
                0x00000000000000b7       0x3d livedesk/leopard/mod_desktop/functions/built-in.o
 .riscv.attributes
                0x00000000000000f4       0x3d livedesk/leopard/mod_desktop/msg_srv/built-in.o
 .riscv.attributes
                0x0000000000000131       0x3d ./elibrary/bin//libsyscall.a(syscall.o)
 .riscv.attributes
                0x000000000000016e       0x3d ./elibrary/bin//libsyscall.a(syscall_fops.o)
 .riscv.attributes
                0x00000000000001ab       0x3d ./elibrary/bin//libminic.a(elibs_stdio.o)
 .riscv.attributes
                0x00000000000001e8       0x3d ./elibrary/bin//libminic.a(elibs_string.o)
 .riscv.attributes
                0x0000000000000225       0x3d ./elibrary/bin//libminic.a(elibs_sprintf.o)
 .riscv.attributes
                0x0000000000000262       0x3d ./elibrary/bin//liblzma.a(az100.o)
 .riscv.attributes
                0x000000000000029f       0x3d ./elibrary/bin//liblzma.a(compress.o)
 .riscv.attributes
                0x00000000000002dc       0x3d ./elibrary/bin//liblzma.a(LzmaLib.o)
 .riscv.attributes
                0x0000000000000319       0x3d ./elibrary/bin//liblzma.a(LzmaDec.o)
 .riscv.attributes
                0x0000000000000356       0x3d ./elibrary/bin//libresreader.a(LangDec.o)
 .riscv.attributes
                0x0000000000000393       0x3d ./elibrary/bin//libresreader.a(themedec.o)
 .riscv.attributes
                0x00000000000003d0       0x3d ./elibrary/bin//libminic.a(elibs_stdlib.o)
 .riscv.attributes
                0x000000000000040d       0x3e /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/../../../../riscv64-unknown-elf/lib/rv64imafdcxtheadc/lp64d/libc.a(lib_a-impure.o)
 .riscv.attributes
                0x000000000000044b       0x3e /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/../../../../riscv64-unknown-elf/lib/rv64imafdcxtheadc/lp64d/libc.a(lib_a-memcpy.o)
 .riscv.attributes
                0x0000000000000489       0x3e /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/../../../../riscv64-unknown-elf/lib/rv64imafdcxtheadc/lp64d/libc.a(lib_a-memset.o)
 .riscv.attributes
                0x00000000000004c7       0x3e /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/../../../../riscv64-unknown-elf/lib/rv64imafdcxtheadc/lp64d/libc.a(lib_a-strcpy.o)
 .riscv.attributes
                0x0000000000000505       0x3e /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/../../../../riscv64-unknown-elf/lib/rv64imafdcxtheadc/lp64d/libc.a(lib_a-strncpy.o)
 .riscv.attributes
                0x0000000000000543       0x3e /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/../../../../riscv64-unknown-elf/lib/rv64imafdcxtheadc/lp64d/libc.a(lib_a-sysclose.o)
 .riscv.attributes
                0x0000000000000581       0x3e /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/../../../../riscv64-unknown-elf/lib/rv64imafdcxtheadc/lp64d/libc.a(lib_a-sysopen.o)

.ARM.attributes
 *(.ARM.attributes)

/DISCARD/
 *(.note.GNU-stack)
 *(.ARM.exidx* .gnu.linkonce.armexidx.*)
OUTPUT(livedesk/leopard/mod_desktop/desktop.o elf64-littleriscv)

.debug_info     0x0000000000000000    0x77dfb
 .debug_info    0x0000000000000000     0x5118 livedesk/leopard/mod_desktop/desktop_api.o
 .debug_info    0x0000000000005118     0x2c98 livedesk/leopard/mod_desktop/mod_desktop.o
 .debug_info    0x0000000000007db0     0x2c27 livedesk/leopard/mod_desktop/magic.o
 .debug_info    0x000000000000a9d7    0x43c6e livedesk/leopard/mod_desktop/functions/built-in.o
 .debug_info    0x000000000004e645     0x89c8 livedesk/leopard/mod_desktop/msg_srv/built-in.o
 .debug_info    0x000000000005700d    0x12bf0 ./elibrary/bin//libsyscall.a(syscall.o)
 .debug_info    0x0000000000069bfd     0x2f5e ./elibrary/bin//libsyscall.a(syscall_fops.o)
 .debug_info    0x000000000006cb5b     0x2b1c ./elibrary/bin//libminic.a(elibs_stdio.o)
 .debug_info    0x000000000006f677      0xb2d ./elibrary/bin//libminic.a(elibs_string.o)
 .debug_info    0x00000000000701a4      0x7d5 ./elibrary/bin//libminic.a(elibs_sprintf.o)
 .debug_info    0x0000000000070979      0xf3c ./elibrary/bin//liblzma.a(az100.o)
 .debug_info    0x00000000000718b5      0x1f3 ./elibrary/bin//liblzma.a(compress.o)
 .debug_info    0x0000000000071aa8      0x3dc ./elibrary/bin//liblzma.a(LzmaLib.o)
 .debug_info    0x0000000000071e84     0x1e78 ./elibrary/bin//liblzma.a(LzmaDec.o)
 .debug_info    0x0000000000073cfc     0x152a ./elibrary/bin//libresreader.a(LangDec.o)
 .debug_info    0x0000000000075226     0x1544 ./elibrary/bin//libresreader.a(themedec.o)
 .debug_info    0x000000000007676a     0x1691 ./elibrary/bin//libminic.a(elibs_stdlib.o)

.debug_abbrev   0x0000000000000000     0x6a0b
 .debug_abbrev  0x0000000000000000      0x246 livedesk/leopard/mod_desktop/desktop_api.o
 .debug_abbrev  0x0000000000000246      0x340 livedesk/leopard/mod_desktop/mod_desktop.o
 .debug_abbrev  0x0000000000000586      0x242 livedesk/leopard/mod_desktop/magic.o
 .debug_abbrev  0x00000000000007c8     0x3e3c livedesk/leopard/mod_desktop/functions/built-in.o
 .debug_abbrev  0x0000000000004604      0x53e livedesk/leopard/mod_desktop/msg_srv/built-in.o
 .debug_abbrev  0x0000000000004b42      0x3a2 ./elibrary/bin//libsyscall.a(syscall.o)
 .debug_abbrev  0x0000000000004ee4      0x3ec ./elibrary/bin//libsyscall.a(syscall_fops.o)
 .debug_abbrev  0x00000000000052d0      0x3d0 ./elibrary/bin//libminic.a(elibs_stdio.o)
 .debug_abbrev  0x00000000000056a0      0x155 ./elibrary/bin//libminic.a(elibs_string.o)
 .debug_abbrev  0x00000000000057f5      0x14d ./elibrary/bin//libminic.a(elibs_sprintf.o)
 .debug_abbrev  0x0000000000005942      0x238 ./elibrary/bin//liblzma.a(az100.o)
 .debug_abbrev  0x0000000000005b7a       0x88 ./elibrary/bin//liblzma.a(compress.o)
 .debug_abbrev  0x0000000000005c02      0x13d ./elibrary/bin//liblzma.a(LzmaLib.o)
 .debug_abbrev  0x0000000000005d3f      0x41d ./elibrary/bin//liblzma.a(LzmaDec.o)
 .debug_abbrev  0x000000000000615c      0x2c7 ./elibrary/bin//libresreader.a(LangDec.o)
 .debug_abbrev  0x0000000000006423      0x2f5 ./elibrary/bin//libresreader.a(themedec.o)
 .debug_abbrev  0x0000000000006718      0x2f3 ./elibrary/bin//libminic.a(elibs_stdlib.o)

.debug_aranges  0x0000000000000000     0x4240
 .debug_aranges
                0x0000000000000000       0x20 livedesk/leopard/mod_desktop/desktop_api.o
 .debug_aranges
                0x0000000000000020       0x70 livedesk/leopard/mod_desktop/mod_desktop.o
 .debug_aranges
                0x0000000000000090       0x20 livedesk/leopard/mod_desktop/magic.o
 .debug_aranges
                0x00000000000000b0     0x1af0 livedesk/leopard/mod_desktop/functions/built-in.o
 .debug_aranges
                0x0000000000001ba0      0x160 livedesk/leopard/mod_desktop/msg_srv/built-in.o
 .debug_aranges
                0x0000000000001d00     0x1840 ./elibrary/bin//libsyscall.a(syscall.o)
 .debug_aranges
                0x0000000000003540      0x260 ./elibrary/bin//libsyscall.a(syscall_fops.o)
 .debug_aranges
                0x00000000000037a0      0x480 ./elibrary/bin//libminic.a(elibs_stdio.o)
 .debug_aranges
                0x0000000000003c20      0x150 ./elibrary/bin//libminic.a(elibs_string.o)
 .debug_aranges
                0x0000000000003d70       0x80 ./elibrary/bin//libminic.a(elibs_sprintf.o)
 .debug_aranges
                0x0000000000003df0       0x50 ./elibrary/bin//liblzma.a(az100.o)
 .debug_aranges
                0x0000000000003e40       0x40 ./elibrary/bin//liblzma.a(compress.o)
 .debug_aranges
                0x0000000000003e80       0x60 ./elibrary/bin//liblzma.a(LzmaLib.o)
 .debug_aranges
                0x0000000000003ee0      0x100 ./elibrary/bin//liblzma.a(LzmaDec.o)
 .debug_aranges
                0x0000000000003fe0       0x80 ./elibrary/bin//libresreader.a(LangDec.o)
 .debug_aranges
                0x0000000000004060       0xa0 ./elibrary/bin//libresreader.a(themedec.o)
 .debug_aranges
                0x0000000000004100      0x140 ./elibrary/bin//libminic.a(elibs_stdlib.o)

.debug_line     0x0000000000000000    0x404d0
 .debug_line    0x0000000000000000      0xab7 livedesk/leopard/mod_desktop/desktop_api.o
 .debug_line    0x0000000000000ab7      0x9d9 livedesk/leopard/mod_desktop/mod_desktop.o
 .debug_line    0x0000000000001490      0x811 livedesk/leopard/mod_desktop/magic.o
 .debug_line    0x0000000000001ca1    0x1dc61 livedesk/leopard/mod_desktop/functions/built-in.o
 .debug_line    0x000000000001f902     0x7eb0 livedesk/leopard/mod_desktop/msg_srv/built-in.o
 .debug_line    0x00000000000277b2     0xe68f ./elibrary/bin//libsyscall.a(syscall.o)
 .debug_line    0x0000000000035e41     0x1864 ./elibrary/bin//libsyscall.a(syscall_fops.o)
 .debug_line    0x00000000000376a5     0x22a9 ./elibrary/bin//libminic.a(elibs_stdio.o)
 .debug_line    0x000000000003994e      0x5f9 ./elibrary/bin//libminic.a(elibs_string.o)
 .debug_line    0x0000000000039f47      0x495 ./elibrary/bin//libminic.a(elibs_sprintf.o)
 .debug_line    0x000000000003a3dc      0x4e7 ./elibrary/bin//liblzma.a(az100.o)
 .debug_line    0x000000000003a8c3      0x31c ./elibrary/bin//liblzma.a(compress.o)
 .debug_line    0x000000000003abdf      0x280 ./elibrary/bin//liblzma.a(LzmaLib.o)
 .debug_line    0x000000000003ae5f     0x36aa ./elibrary/bin//liblzma.a(LzmaDec.o)
 .debug_line    0x000000000003e509      0xd31 ./elibrary/bin//libresreader.a(LangDec.o)
 .debug_line    0x000000000003f23a      0xbcb ./elibrary/bin//libresreader.a(themedec.o)
 .debug_line    0x000000000003fe05      0x6cb ./elibrary/bin//libminic.a(elibs_stdlib.o)

.debug_str      0x0000000000000000    0x1ef33
 .debug_str     0x0000000000000000     0xa783 livedesk/leopard/mod_desktop/desktop_api.o
                                       0xa8bc (size before relaxing)
 .debug_str     0x000000000000a783       0xc7 livedesk/leopard/mod_desktop/mod_desktop.o
                                       0x6a24 (size before relaxing)
 .debug_str     0x000000000000a84a       0x73 livedesk/leopard/mod_desktop/magic.o
                                       0x6a06 (size before relaxing)
 .debug_str     0x000000000000a8bd     0xd39c livedesk/leopard/mod_desktop/functions/built-in.o
                                      0x4e6ab (size before relaxing)
 .debug_str     0x0000000000017c59     0x2acf livedesk/leopard/mod_desktop/msg_srv/built-in.o
                                       0xd406 (size before relaxing)
 .debug_str     0x000000000001a728     0x2e99 ./elibrary/bin//libsyscall.a(syscall.o)
                                       0x3c33 (size before relaxing)
 .debug_str     0x000000000001d5c1      0x2c0 ./elibrary/bin//libsyscall.a(syscall_fops.o)
                                       0x1068 (size before relaxing)
 .debug_str     0x000000000001d881      0x665 ./elibrary/bin//libminic.a(elibs_stdio.o)
                                       0x1624 (size before relaxing)
 .debug_str     0x000000000001dee6      0x37a ./elibrary/bin//libminic.a(elibs_string.o)
                                        0x6d8 (size before relaxing)
 .debug_str     0x000000000001e260       0x81 ./elibrary/bin//libminic.a(elibs_sprintf.o)
                                        0x5d3 (size before relaxing)
 .debug_str     0x000000000001e2e1       0x80 ./elibrary/bin//liblzma.a(az100.o)
                                        0xa4a (size before relaxing)
 .debug_str     0x000000000001e361       0x88 ./elibrary/bin//liblzma.a(compress.o)
                                        0x3bd (size before relaxing)
 .debug_str     0x000000000001e3e9      0x19f ./elibrary/bin//liblzma.a(LzmaLib.o)
                                        0x4cb (size before relaxing)
 .debug_str     0x000000000001e588      0x39e ./elibrary/bin//liblzma.a(LzmaDec.o)
                                        0xbc3 (size before relaxing)
 .debug_str     0x000000000001e926      0x3a9 ./elibrary/bin//libresreader.a(LangDec.o)
                                        0xbea (size before relaxing)
 .debug_str     0x000000000001eccf      0x168 ./elibrary/bin//libresreader.a(themedec.o)
                                        0xbe8 (size before relaxing)
 .debug_str     0x000000000001ee37       0xfc ./elibrary/bin//libminic.a(elibs_stdlib.o)
                                        0xd03 (size before relaxing)

.debug_loc      0x0000000000000000    0x2473f
 .debug_loc     0x0000000000000000      0x147 livedesk/leopard/mod_desktop/mod_desktop.o
 .debug_loc     0x0000000000000147     0xd851 livedesk/leopard/mod_desktop/functions/built-in.o
 .debug_loc     0x000000000000d998      0xc39 livedesk/leopard/mod_desktop/msg_srv/built-in.o
 .debug_loc     0x000000000000e5d1     0xda5d ./elibrary/bin//libsyscall.a(syscall.o)
 .debug_loc     0x000000000001c02e     0x1325 ./elibrary/bin//libsyscall.a(syscall_fops.o)
 .debug_loc     0x000000000001d353     0x25c5 ./elibrary/bin//libminic.a(elibs_stdio.o)
 .debug_loc     0x000000000001f918      0x705 ./elibrary/bin//libminic.a(elibs_string.o)
 .debug_loc     0x000000000002001d      0x3f2 ./elibrary/bin//libminic.a(elibs_sprintf.o)
 .debug_loc     0x000000000002040f      0x296 ./elibrary/bin//liblzma.a(az100.o)
 .debug_loc     0x00000000000206a5      0x26b ./elibrary/bin//liblzma.a(compress.o)
 .debug_loc     0x0000000000020910      0x33b ./elibrary/bin//liblzma.a(LzmaLib.o)
 .debug_loc     0x0000000000020c4b     0x2123 ./elibrary/bin//liblzma.a(LzmaDec.o)
 .debug_loc     0x0000000000022d6e      0xa7f ./elibrary/bin//libresreader.a(LangDec.o)
 .debug_loc     0x00000000000237ed      0xbc7 ./elibrary/bin//libresreader.a(themedec.o)
 .debug_loc     0x00000000000243b4      0x38b ./elibrary/bin//libminic.a(elibs_stdlib.o)

.debug_frame    0x0000000000000000     0xa1c8
 .debug_frame   0x0000000000000000       0xd8 livedesk/leopard/mod_desktop/mod_desktop.o
 .debug_frame   0x00000000000000d8     0x4498 livedesk/leopard/mod_desktop/functions/built-in.o
 .debug_frame   0x0000000000004570      0x3c8 livedesk/leopard/mod_desktop/msg_srv/built-in.o
 .debug_frame   0x0000000000004938     0x3c60 ./elibrary/bin//libsyscall.a(syscall.o)
 .debug_frame   0x0000000000008598      0x5b8 ./elibrary/bin//libsyscall.a(syscall_fops.o)
 .debug_frame   0x0000000000008b50      0xa28 ./elibrary/bin//libminic.a(elibs_stdio.o)
 .debug_frame   0x0000000000009578      0x1e8 ./elibrary/bin//libminic.a(elibs_string.o)
 .debug_frame   0x0000000000009760       0xe0 ./elibrary/bin//libminic.a(elibs_sprintf.o)
 .debug_frame   0x0000000000009840       0x80 ./elibrary/bin//liblzma.a(az100.o)
 .debug_frame   0x00000000000098c0       0x68 ./elibrary/bin//liblzma.a(compress.o)
 .debug_frame   0x0000000000009928       0x98 ./elibrary/bin//liblzma.a(LzmaLib.o)
 .debug_frame   0x00000000000099c0      0x2e0 ./elibrary/bin//liblzma.a(LzmaDec.o)
 .debug_frame   0x0000000000009ca0      0x180 ./elibrary/bin//libresreader.a(LangDec.o)
 .debug_frame   0x0000000000009e20      0x1c8 ./elibrary/bin//libresreader.a(themedec.o)
 .debug_frame   0x0000000000009fe8      0x1e0 ./elibrary/bin//libminic.a(elibs_stdlib.o)

Cross Reference Table

Symbol                                            File
AZ100_Compress                                    ./elibrary/bin//liblzma.a(compress.o)
AZ100_DataUncompress                              ./elibrary/bin//liblzma.a(compress.o)
                                                  ./elibrary/bin//liblzma.a(az100.o)
AZ100_GetUncompressSize                           ./elibrary/bin//liblzma.a(az100.o)
                                                  livedesk/leopard/mod_desktop/functions/built-in.o
AZ100_IsCompress                                  ./elibrary/bin//liblzma.a(az100.o)
                                                  livedesk/leopard/mod_desktop/functions/built-in.o
AZ100_Uncompress                                  ./elibrary/bin//liblzma.a(az100.o)
                                                  livedesk/leopard/mod_desktop/functions/built-in.o
Bt3Zip_MatchFinder_GetMatches                     ./elibrary/bin//liblzma.a(LzFind.o)
Bt3Zip_MatchFinder_Skip                           ./elibrary/bin//liblzma.a(LzFind.o)
CloseRes                                          ./elibrary/bin//libresreader.a(themedec.o)
                                                  livedesk/leopard/mod_desktop/functions/built-in.o
DESKTOP_MClose                                    livedesk/leopard/mod_desktop/mod_desktop.o
                                                  livedesk/leopard/mod_desktop/magic.o
DESKTOP_MExit                                     livedesk/leopard/mod_desktop/mod_desktop.o
                                                  livedesk/leopard/mod_desktop/magic.o
DESKTOP_MInit                                     livedesk/leopard/mod_desktop/mod_desktop.o
                                                  livedesk/leopard/mod_desktop/magic.o
DESKTOP_MIoctrl                                   livedesk/leopard/mod_desktop/mod_desktop.o
                                                  livedesk/leopard/mod_desktop/magic.o
DESKTOP_MOpen                                     livedesk/leopard/mod_desktop/mod_desktop.o
                                                  livedesk/leopard/mod_desktop/magic.o
DESKTOP_MRead                                     livedesk/leopard/mod_desktop/mod_desktop.o
                                                  livedesk/leopard/mod_desktop/magic.o
DESKTOP_MWrite                                    livedesk/leopard/mod_desktop/mod_desktop.o
                                                  livedesk/leopard/mod_desktop/magic.o
Eq_Gain                                           livedesk/leopard/mod_desktop/functions/built-in.o
GetMatchesSpec1                                   ./elibrary/bin//liblzma.a(LzFind.o)
GetRes                                            ./elibrary/bin//libresreader.a(themedec.o)
                                                  livedesk/leopard/mod_desktop/functions/built-in.o
GetResAddr                                        ./elibrary/bin//libresreader.a(themedec.o)
GetResSize                                        ./elibrary/bin//libresreader.a(themedec.o)
                                                  livedesk/leopard/mod_desktop/functions/built-in.o
GetResType                                        ./elibrary/bin//libresreader.a(themedec.o)
Hc3Zip_MatchFinder_GetMatches                     ./elibrary/bin//liblzma.a(LzFind.o)
Hc3Zip_MatchFinder_Skip                           ./elibrary/bin//liblzma.a(LzFind.o)
Lang_Close                                        ./elibrary/bin//libresreader.a(LangDec.o)
                                                  livedesk/leopard/mod_desktop/functions/built-in.o
Lang_GetString                                    ./elibrary/bin//libresreader.a(LangDec.o)
                                                  livedesk/leopard/mod_desktop/functions/built-in.o
Lang_GetStringAddress                             ./elibrary/bin//libresreader.a(LangDec.o)
Lang_GetStringSize                                ./elibrary/bin//libresreader.a(LangDec.o)
                                                  livedesk/leopard/mod_desktop/functions/built-in.o
Lang_Open                                         ./elibrary/bin//libresreader.a(LangDec.o)
                                                  livedesk/leopard/mod_desktop/functions/built-in.o
Lang_Read                                         ./elibrary/bin//libresreader.a(LangDec.o)
LzmaCompress                                      ./elibrary/bin//liblzma.a(LzmaLib.o)
                                                  ./elibrary/bin//liblzma.a(compress.o)
LzmaDec_Allocate                                  ./elibrary/bin//liblzma.a(LzmaDec.o)
LzmaDec_AllocateProbs                             ./elibrary/bin//liblzma.a(LzmaDec.o)
LzmaDec_DecodeToBuf                               ./elibrary/bin//liblzma.a(LzmaDec.o)
LzmaDec_DecodeToDic                               ./elibrary/bin//liblzma.a(LzmaDec.o)
LzmaDec_Free                                      ./elibrary/bin//liblzma.a(LzmaDec.o)
LzmaDec_FreeProbs                                 ./elibrary/bin//liblzma.a(LzmaDec.o)
LzmaDec_Init                                      ./elibrary/bin//liblzma.a(LzmaDec.o)
LzmaDec_InitDicAndState                           ./elibrary/bin//liblzma.a(LzmaDec.o)
LzmaDecode                                        ./elibrary/bin//liblzma.a(LzmaDec.o)
                                                  ./elibrary/bin//liblzma.a(LzmaLib.o)
LzmaEncProps_GetDictSize                          ./elibrary/bin//liblzma.a(LzmaEnc.o)
LzmaEncProps_Init                                 ./elibrary/bin//liblzma.a(LzmaEnc.o)
                                                  ./elibrary/bin//liblzma.a(LzmaLib.o)
LzmaEncProps_Normalize                            ./elibrary/bin//liblzma.a(LzmaEnc.o)
LzmaEnc_CodeOneMemBlock                           ./elibrary/bin//liblzma.a(LzmaEnc.o)
LzmaEnc_Construct                                 ./elibrary/bin//liblzma.a(LzmaEnc.o)
LzmaEnc_Create                                    ./elibrary/bin//liblzma.a(LzmaEnc.o)
LzmaEnc_Destroy                                   ./elibrary/bin//liblzma.a(LzmaEnc.o)
LzmaEnc_Destruct                                  ./elibrary/bin//liblzma.a(LzmaEnc.o)
LzmaEnc_Encode                                    ./elibrary/bin//liblzma.a(LzmaEnc.o)
LzmaEnc_FastPosInit                               ./elibrary/bin//liblzma.a(LzmaEnc.o)
LzmaEnc_Finish                                    ./elibrary/bin//liblzma.a(LzmaEnc.o)
LzmaEnc_FreeLits                                  ./elibrary/bin//liblzma.a(LzmaEnc.o)
LzmaEnc_GetCurBuf                                 ./elibrary/bin//liblzma.a(LzmaEnc.o)
LzmaEnc_GetNumAvailableBytes                      ./elibrary/bin//liblzma.a(LzmaEnc.o)
LzmaEnc_Init                                      ./elibrary/bin//liblzma.a(LzmaEnc.o)
LzmaEnc_InitPriceTables                           ./elibrary/bin//liblzma.a(LzmaEnc.o)
LzmaEnc_InitPrices                                ./elibrary/bin//liblzma.a(LzmaEnc.o)
LzmaEnc_MemEncode                                 ./elibrary/bin//liblzma.a(LzmaEnc.o)
LzmaEnc_MemPrepare                                ./elibrary/bin//liblzma.a(LzmaEnc.o)
LzmaEnc_PrepareForLzma2                           ./elibrary/bin//liblzma.a(LzmaEnc.o)
LzmaEnc_RestoreState                              ./elibrary/bin//liblzma.a(LzmaEnc.o)
LzmaEnc_SaveState                                 ./elibrary/bin//liblzma.a(LzmaEnc.o)
LzmaEnc_SetProps                                  ./elibrary/bin//liblzma.a(LzmaEnc.o)
LzmaEnc_WriteProperties                           ./elibrary/bin//liblzma.a(LzmaEnc.o)
LzmaEncode                                        ./elibrary/bin//liblzma.a(LzmaEnc.o)
                                                  ./elibrary/bin//liblzma.a(LzmaLib.o)
LzmaProps_Decode                                  ./elibrary/bin//liblzma.a(LzmaDec.o)
LzmaUncompress                                    ./elibrary/bin//liblzma.a(LzmaLib.o)
                                                  ./elibrary/bin//liblzma.a(compress.o)
MatchFinder_Construct                             ./elibrary/bin//liblzma.a(LzFind.o)
                                                  ./elibrary/bin//liblzma.a(LzmaEnc.o)
MatchFinder_Create                                ./elibrary/bin//liblzma.a(LzFind.o)
                                                  ./elibrary/bin//liblzma.a(LzmaEnc.o)
MatchFinder_CreateVTable                          ./elibrary/bin//liblzma.a(LzFind.o)
                                                  ./elibrary/bin//liblzma.a(LzmaEnc.o)
MatchFinder_Free                                  ./elibrary/bin//liblzma.a(LzFind.o)
                                                  ./elibrary/bin//liblzma.a(LzmaEnc.o)
MatchFinder_GetIndexByte                          ./elibrary/bin//liblzma.a(LzFind.o)
MatchFinder_GetNumAvailableBytes                  ./elibrary/bin//liblzma.a(LzFind.o)
MatchFinder_GetPointerToCurrentPos                ./elibrary/bin//liblzma.a(LzFind.o)
MatchFinder_Init                                  ./elibrary/bin//liblzma.a(LzFind.o)
MatchFinder_MoveBlock                             ./elibrary/bin//liblzma.a(LzFind.o)
MatchFinder_NeedMove                              ./elibrary/bin//liblzma.a(LzFind.o)
MatchFinder_Normalize3                            ./elibrary/bin//liblzma.a(LzFind.o)
MatchFinder_ReadIfRequired                        ./elibrary/bin//liblzma.a(LzFind.o)
MatchFinder_ReduceOffsets                         ./elibrary/bin//liblzma.a(LzFind.o)
OpenRes                                           ./elibrary/bin//libresreader.a(themedec.o)
                                                  livedesk/leopard/mod_desktop/functions/built-in.o
ReadRes                                           ./elibrary/bin//libresreader.a(themedec.o)
__fp_lock_all                                     /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/../../../../riscv64-unknown-elf/lib/rv64imafdcxtheadc/lp64d/libc.a(lib_a-findfp.o)
__fp_unlock_all                                   /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/../../../../riscv64-unknown-elf/lib/rv64imafdcxtheadc/lp64d/libc.a(lib_a-findfp.o)
__libc_init_array                                 ./elibrary/bin//libsyscall.a(syscall_fops.o)
__malloc_lock                                     ./elibrary/bin//libsyscall.a(syscall_fops.o)
__malloc_unlock                                   ./elibrary/bin//libsyscall.a(syscall_fops.o)
__sclose                                          /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/../../../../riscv64-unknown-elf/lib/rv64imafdcxtheadc/lp64d/libc.a(lib_a-stdio.o)
                                                  /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/../../../../riscv64-unknown-elf/lib/rv64imafdcxtheadc/lp64d/libc.a(lib_a-fopen.o)
                                                  /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/../../../../riscv64-unknown-elf/lib/rv64imafdcxtheadc/lp64d/libc.a(lib_a-findfp.o)
__seofread                                        /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/../../../../riscv64-unknown-elf/lib/rv64imafdcxtheadc/lp64d/libc.a(lib_a-stdio.o)
__sflags                                          /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/../../../../riscv64-unknown-elf/lib/rv64imafdcxtheadc/lp64d/libc.a(lib_a-flags.o)
                                                  /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/../../../../riscv64-unknown-elf/lib/rv64imafdcxtheadc/lp64d/libc.a(lib_a-fopen.o)
__sflush_r                                        /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/../../../../riscv64-unknown-elf/lib/rv64imafdcxtheadc/lp64d/libc.a(lib_a-fflush.o)
                                                  /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/../../../../riscv64-unknown-elf/lib/rv64imafdcxtheadc/lp64d/libc.a(lib_a-refill.o)
                                                  /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/../../../../riscv64-unknown-elf/lib/rv64imafdcxtheadc/lp64d/libc.a(lib_a-fclose.o)
__sfmoreglue                                      /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/../../../../riscv64-unknown-elf/lib/rv64imafdcxtheadc/lp64d/libc.a(lib_a-findfp.o)
__sfp                                             /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/../../../../riscv64-unknown-elf/lib/rv64imafdcxtheadc/lp64d/libc.a(lib_a-findfp.o)
                                                  /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/../../../../riscv64-unknown-elf/lib/rv64imafdcxtheadc/lp64d/libc.a(lib_a-fopen.o)
__sfp_lock_acquire                                /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/../../../../riscv64-unknown-elf/lib/rv64imafdcxtheadc/lp64d/libc.a(lib_a-findfp.o)
                                                  /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/../../../../riscv64-unknown-elf/lib/rv64imafdcxtheadc/lp64d/libc.a(lib_a-fopen.o)
                                                  /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/../../../../riscv64-unknown-elf/lib/rv64imafdcxtheadc/lp64d/libc.a(lib_a-fclose.o)
__sfp_lock_release                                /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/../../../../riscv64-unknown-elf/lib/rv64imafdcxtheadc/lp64d/libc.a(lib_a-findfp.o)
                                                  /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/../../../../riscv64-unknown-elf/lib/rv64imafdcxtheadc/lp64d/libc.a(lib_a-fopen.o)
                                                  /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/../../../../riscv64-unknown-elf/lib/rv64imafdcxtheadc/lp64d/libc.a(lib_a-fclose.o)
__sfvwrite_r                                      /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/../../../../riscv64-unknown-elf/lib/rv64imafdcxtheadc/lp64d/libc.a(lib_a-fvwrite.o)
                                                  /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/../../../../riscv64-unknown-elf/lib/rv64imafdcxtheadc/lp64d/libc.a(lib_a-fwrite.o)
__sinit                                           /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/../../../../riscv64-unknown-elf/lib/rv64imafdcxtheadc/lp64d/libc.a(lib_a-findfp.o)
                                                  /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/../../../../riscv64-unknown-elf/lib/rv64imafdcxtheadc/lp64d/libc.a(lib_a-wsetup.o)
                                                  /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/../../../../riscv64-unknown-elf/lib/rv64imafdcxtheadc/lp64d/libc.a(lib_a-refill.o)
                                                  /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/../../../../riscv64-unknown-elf/lib/rv64imafdcxtheadc/lp64d/libc.a(lib_a-fwrite.o)
                                                  /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/../../../../riscv64-unknown-elf/lib/rv64imafdcxtheadc/lp64d/libc.a(lib_a-fseeko.o)
                                                  /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/../../../../riscv64-unknown-elf/lib/rv64imafdcxtheadc/lp64d/libc.a(lib_a-fflush.o)
                                                  /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/../../../../riscv64-unknown-elf/lib/rv64imafdcxtheadc/lp64d/libc.a(lib_a-fclose.o)
__sinit_lock_acquire                              /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/../../../../riscv64-unknown-elf/lib/rv64imafdcxtheadc/lp64d/libc.a(lib_a-findfp.o)
__sinit_lock_release                              /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/../../../../riscv64-unknown-elf/lib/rv64imafdcxtheadc/lp64d/libc.a(lib_a-findfp.o)
__smakebuf_r                                      /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/../../../../riscv64-unknown-elf/lib/rv64imafdcxtheadc/lp64d/libc.a(lib_a-makebuf.o)
                                                  /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/../../../../riscv64-unknown-elf/lib/rv64imafdcxtheadc/lp64d/libc.a(lib_a-wsetup.o)
                                                  /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/../../../../riscv64-unknown-elf/lib/rv64imafdcxtheadc/lp64d/libc.a(lib_a-refill.o)
                                                  /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/../../../../riscv64-unknown-elf/lib/rv64imafdcxtheadc/lp64d/libc.a(lib_a-fseeko.o)
__sread                                           /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/../../../../riscv64-unknown-elf/lib/rv64imafdcxtheadc/lp64d/libc.a(lib_a-stdio.o)
                                                  /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/../../../../riscv64-unknown-elf/lib/rv64imafdcxtheadc/lp64d/libc.a(lib_a-fopen.o)
                                                  /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/../../../../riscv64-unknown-elf/lib/rv64imafdcxtheadc/lp64d/libc.a(lib_a-findfp.o)
__srefill_r                                       /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/../../../../riscv64-unknown-elf/lib/rv64imafdcxtheadc/lp64d/libc.a(lib_a-refill.o)
                                                  /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/../../../../riscv64-unknown-elf/lib/rv64imafdcxtheadc/lp64d/libc.a(lib_a-fseeko.o)
__sseek                                           /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/../../../../riscv64-unknown-elf/lib/rv64imafdcxtheadc/lp64d/libc.a(lib_a-stdio.o)
                                                  /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/../../../../riscv64-unknown-elf/lib/rv64imafdcxtheadc/lp64d/libc.a(lib_a-fseeko.o)
                                                  /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/../../../../riscv64-unknown-elf/lib/rv64imafdcxtheadc/lp64d/libc.a(lib_a-fopen.o)
                                                  /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/../../../../riscv64-unknown-elf/lib/rv64imafdcxtheadc/lp64d/libc.a(lib_a-findfp.o)
__swhatbuf_r                                      /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/../../../../riscv64-unknown-elf/lib/rv64imafdcxtheadc/lp64d/libc.a(lib_a-makebuf.o)
__swrite                                          /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/../../../../riscv64-unknown-elf/lib/rv64imafdcxtheadc/lp64d/libc.a(lib_a-stdio.o)
                                                  /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/../../../../riscv64-unknown-elf/lib/rv64imafdcxtheadc/lp64d/libc.a(lib_a-fopen.o)
                                                  /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/../../../../riscv64-unknown-elf/lib/rv64imafdcxtheadc/lp64d/libc.a(lib_a-findfp.o)
__swsetup_r                                       /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/../../../../riscv64-unknown-elf/lib/rv64imafdcxtheadc/lp64d/libc.a(lib_a-wsetup.o)
                                                  /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/../../../../riscv64-unknown-elf/lib/rv64imafdcxtheadc/lp64d/libc.a(lib_a-fvwrite.o)
_calloc_r                                         ./elibrary/bin//libsyscall.a(syscall_fops.o)
_cleanup                                          /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/../../../../riscv64-unknown-elf/lib/rv64imafdcxtheadc/lp64d/libc.a(lib_a-findfp.o)
_cleanup_r                                        /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/../../../../riscv64-unknown-elf/lib/rv64imafdcxtheadc/lp64d/libc.a(lib_a-findfp.o)
                                                  /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/../../../../riscv64-unknown-elf/lib/rv64imafdcxtheadc/lp64d/libc.a(lib_a-makebuf.o)
_close_r                                          ./elibrary/bin//libsyscall.a(syscall_fops.o)
                                                  /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/../../../../riscv64-unknown-elf/lib/rv64imafdcxtheadc/lp64d/libc.a(lib_a-sysclose.o)
                                                  /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/../../../../riscv64-unknown-elf/lib/rv64imafdcxtheadc/lp64d/libc.a(lib_a-stdio.o)
_exit                                             ./elibrary/bin//libminic.a(elibs_stdlib.o)
_fclose_r                                         /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/../../../../riscv64-unknown-elf/lib/rv64imafdcxtheadc/lp64d/libc.a(lib_a-fclose.o)
                                                  /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/../../../../riscv64-unknown-elf/lib/rv64imafdcxtheadc/lp64d/libc.a(lib_a-findfp.o)
_fflush_r                                         /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/../../../../riscv64-unknown-elf/lib/rv64imafdcxtheadc/lp64d/libc.a(lib_a-fflush.o)
                                                  /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/../../../../riscv64-unknown-elf/lib/rv64imafdcxtheadc/lp64d/libc.a(lib_a-fvwrite.o)
                                                  /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/../../../../riscv64-unknown-elf/lib/rv64imafdcxtheadc/lp64d/libc.a(lib_a-refill.o)
                                                  /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/../../../../riscv64-unknown-elf/lib/rv64imafdcxtheadc/lp64d/libc.a(lib_a-fseeko.o)
_fopen_r                                          /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/../../../../riscv64-unknown-elf/lib/rv64imafdcxtheadc/lp64d/libc.a(lib_a-fopen.o)
_free_r                                           ./elibrary/bin//libsyscall.a(syscall_fops.o)
                                                  /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/../../../../riscv64-unknown-elf/lib/rv64imafdcxtheadc/lp64d/libc.a(lib_a-wsetup.o)
                                                  /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/../../../../riscv64-unknown-elf/lib/rv64imafdcxtheadc/lp64d/libc.a(lib_a-fvwrite.o)
                                                  /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/../../../../riscv64-unknown-elf/lib/rv64imafdcxtheadc/lp64d/libc.a(lib_a-refill.o)
                                                  /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/../../../../riscv64-unknown-elf/lib/rv64imafdcxtheadc/lp64d/libc.a(lib_a-fseeko.o)
                                                  /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/../../../../riscv64-unknown-elf/lib/rv64imafdcxtheadc/lp64d/libc.a(lib_a-fflush.o)
                                                  /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/../../../../riscv64-unknown-elf/lib/rv64imafdcxtheadc/lp64d/libc.a(lib_a-fclose.o)
_fseek_r                                          /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/../../../../riscv64-unknown-elf/lib/rv64imafdcxtheadc/lp64d/libc.a(lib_a-fseek.o)
                                                  /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/../../../../riscv64-unknown-elf/lib/rv64imafdcxtheadc/lp64d/libc.a(lib_a-fopen.o)
_fseeko_r                                         /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/../../../../riscv64-unknown-elf/lib/rv64imafdcxtheadc/lp64d/libc.a(lib_a-fseeko.o)
                                                  /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/../../../../riscv64-unknown-elf/lib/rv64imafdcxtheadc/lp64d/libc.a(lib_a-fseek.o)
_fstat_r                                          ./elibrary/bin//libsyscall.a(syscall_fops.o)
                                                  /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/../../../../riscv64-unknown-elf/lib/rv64imafdcxtheadc/lp64d/libc.a(lib_a-makebuf.o)
                                                  /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/../../../../riscv64-unknown-elf/lib/rv64imafdcxtheadc/lp64d/libc.a(lib_a-fseeko.o)
_fwalk                                            /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/../../../../riscv64-unknown-elf/lib/rv64imafdcxtheadc/lp64d/libc.a(lib_a-fwalk.o)
                                                  /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/../../../../riscv64-unknown-elf/lib/rv64imafdcxtheadc/lp64d/libc.a(lib_a-refill.o)
                                                  /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/../../../../riscv64-unknown-elf/lib/rv64imafdcxtheadc/lp64d/libc.a(lib_a-findfp.o)
_fwalk_reent                                      /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/../../../../riscv64-unknown-elf/lib/rv64imafdcxtheadc/lp64d/libc.a(lib_a-fwalk.o)
                                                  /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/../../../../riscv64-unknown-elf/lib/rv64imafdcxtheadc/lp64d/libc.a(lib_a-findfp.o)
                                                  /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/../../../../riscv64-unknown-elf/lib/rv64imafdcxtheadc/lp64d/libc.a(lib_a-fflush.o)
_fwrite_r                                         /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/../../../../riscv64-unknown-elf/lib/rv64imafdcxtheadc/lp64d/libc.a(lib_a-fwrite.o)
_getpid                                           ./elibrary/bin//libminic.a(elibs_stdlib.o)
_gettimeofday_r                                   ./elibrary/bin//libsyscall.a(syscall_fops.o)
_global_impure_ptr                                /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/../../../../riscv64-unknown-elf/lib/rv64imafdcxtheadc/lp64d/libc.a(lib_a-impure.o)
                                                  /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/../../../../riscv64-unknown-elf/lib/rv64imafdcxtheadc/lp64d/libc.a(lib_a-refill.o)
                                                  /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/../../../../riscv64-unknown-elf/lib/rv64imafdcxtheadc/lp64d/libc.a(lib_a-findfp.o)
                                                  /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/../../../../riscv64-unknown-elf/lib/rv64imafdcxtheadc/lp64d/libc.a(lib_a-fflush.o)
_impure_ptr                                       /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/../../../../riscv64-unknown-elf/lib/rv64imafdcxtheadc/lp64d/libc.a(lib_a-impure.o)
                                                  /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/../../../../riscv64-unknown-elf/lib/rv64imafdcxtheadc/lp64d/libc.a(lib_a-wsetup.o)
                                                  /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/../../../../riscv64-unknown-elf/lib/rv64imafdcxtheadc/lp64d/libc.a(lib_a-sysopen.o)
                                                  /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/../../../../riscv64-unknown-elf/lib/rv64imafdcxtheadc/lp64d/libc.a(lib_a-sysclose.o)
                                                  /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/../../../../riscv64-unknown-elf/lib/rv64imafdcxtheadc/lp64d/libc.a(lib_a-fwrite.o)
                                                  /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/../../../../riscv64-unknown-elf/lib/rv64imafdcxtheadc/lp64d/libc.a(lib_a-fseeko.o)
                                                  /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/../../../../riscv64-unknown-elf/lib/rv64imafdcxtheadc/lp64d/libc.a(lib_a-fseek.o)
                                                  /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/../../../../riscv64-unknown-elf/lib/rv64imafdcxtheadc/lp64d/libc.a(lib_a-fopen.o)
                                                  /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/../../../../riscv64-unknown-elf/lib/rv64imafdcxtheadc/lp64d/libc.a(lib_a-findfp.o)
                                                  /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/../../../../riscv64-unknown-elf/lib/rv64imafdcxtheadc/lp64d/libc.a(lib_a-fflush.o)
                                                  /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/../../../../riscv64-unknown-elf/lib/rv64imafdcxtheadc/lp64d/libc.a(lib_a-fclose.o)
_isatty                                           ./elibrary/bin//libsyscall.a(syscall_fops.o)
_isatty_r                                         ./elibrary/bin//libsyscall.a(syscall_fops.o)
                                                  /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/../../../../riscv64-unknown-elf/lib/rv64imafdcxtheadc/lp64d/libc.a(lib_a-makebuf.o)
_kill                                             ./elibrary/bin//libminic.a(elibs_stdlib.o)
_lseek_r                                          ./elibrary/bin//libsyscall.a(syscall_fops.o)
                                                  /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/../../../../riscv64-unknown-elf/lib/rv64imafdcxtheadc/lp64d/libc.a(lib_a-stdio.o)
_malloc_r                                         ./elibrary/bin//libsyscall.a(syscall_fops.o)
                                                  /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/../../../../riscv64-unknown-elf/lib/rv64imafdcxtheadc/lp64d/libc.a(lib_a-fvwrite.o)
                                                  /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/../../../../riscv64-unknown-elf/lib/rv64imafdcxtheadc/lp64d/libc.a(lib_a-makebuf.o)
                                                  /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/../../../../riscv64-unknown-elf/lib/rv64imafdcxtheadc/lp64d/libc.a(lib_a-findfp.o)
_mkdir_r                                          ./elibrary/bin//libsyscall.a(syscall_fops.o)
_open_r                                           ./elibrary/bin//libsyscall.a(syscall_fops.o)
                                                  /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/../../../../riscv64-unknown-elf/lib/rv64imafdcxtheadc/lp64d/libc.a(lib_a-sysopen.o)
                                                  /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/../../../../riscv64-unknown-elf/lib/rv64imafdcxtheadc/lp64d/libc.a(lib_a-fopen.o)
_read_r                                           ./elibrary/bin//libsyscall.a(syscall_fops.o)
                                                  /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/../../../../riscv64-unknown-elf/lib/rv64imafdcxtheadc/lp64d/libc.a(lib_a-stdio.o)
_realloc_r                                        ./elibrary/bin//libsyscall.a(syscall_fops.o)
                                                  /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/../../../../riscv64-unknown-elf/lib/rv64imafdcxtheadc/lp64d/libc.a(lib_a-fvwrite.o)
_rename_r                                         ./elibrary/bin//libsyscall.a(syscall_fops.o)
_sbrk                                             ./elibrary/bin//libminic.a(elibs_stdlib.o)
_stat_r                                           ./elibrary/bin//libsyscall.a(syscall_fops.o)
_system                                           ./elibrary/bin//libsyscall.a(syscall_fops.o)
_unlink_r                                         ./elibrary/bin//libsyscall.a(syscall_fops.o)
_write                                            ./elibrary/bin//libminic.a(elibs_stdlib.o)
_write_r                                          ./elibrary/bin//libsyscall.a(syscall_fops.o)
                                                  /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/../../../../riscv64-unknown-elf/lib/rv64imafdcxtheadc/lp64d/libc.a(lib_a-stdio.o)
access                                            ./elibrary/bin//libsyscall.a(syscall_fops.o)
aux_eq_dbssoff_table                              livedesk/leopard/mod_desktop/functions/built-in.o
aux_eq_dbsson_table                               livedesk/leopard/mod_desktop/functions/built-in.o
aux_xbass_dbssoff_table                           livedesk/leopard/mod_desktop/functions/built-in.o
aux_xbass_dbsson_table                            livedesk/leopard/mod_desktop/functions/built-in.o
backtrace                                         ./elibrary/bin//libsyscall.a(syscall.o)
bas_tre_to_eq                                     livedesk/leopard/mod_desktop/functions/built-in.o
close                                             /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/../../../../riscv64-unknown-elf/lib/rv64imafdcxtheadc/lp64d/libc.a(lib_a-sysclose.o)
                                                  livedesk/leopard/mod_desktop/msg_srv/built-in.o
                                                  livedesk/leopard/mod_desktop/functions/built-in.o
closedir                                          ./elibrary/bin//libsyscall.a(syscall_fops.o)
desktop_api                                       livedesk/leopard/mod_desktop/desktop_api.o
desktp_data                                       livedesk/leopard/mod_desktop/mod_desktop.o
dsk_amplifier_is_on                               livedesk/leopard/mod_desktop/functions/built-in.o
                                                  livedesk/leopard/mod_desktop/desktop_api.o
dsk_amplifier_onoff                               livedesk/leopard/mod_desktop/functions/built-in.o
                                                  livedesk/leopard/mod_desktop/desktop_api.o
dsk_audio_play_close                              livedesk/leopard/mod_desktop/functions/built-in.o
dsk_audio_play_open                               livedesk/leopard/mod_desktop/functions/built-in.o
dsk_audio_play_set_mute                           livedesk/leopard/mod_desktop/functions/built-in.o
                                                  livedesk/leopard/mod_desktop/desktop_api.o
dsk_aux_balance_FAD_speaker                       livedesk/leopard/mod_desktop/functions/built-in.o
                                                  livedesk/leopard/mod_desktop/desktop_api.o
dsk_aux_balance_RL_speaker                        livedesk/leopard/mod_desktop/functions/built-in.o
                                                  livedesk/leopard/mod_desktop/desktop_api.o
dsk_aux_close                                     livedesk/leopard/mod_desktop/functions/built-in.o
dsk_aux_get_EQ_mode                               livedesk/leopard/mod_desktop/functions/built-in.o
                                                  livedesk/leopard/mod_desktop/desktop_api.o
dsk_aux_get_EQ_type                               livedesk/leopard/mod_desktop/functions/built-in.o
                                                  livedesk/leopard/mod_desktop/desktop_api.o
dsk_aux_get_balance_FAD_speaker                   livedesk/leopard/mod_desktop/functions/built-in.o
                                                  livedesk/leopard/mod_desktop/desktop_api.o
dsk_aux_get_balance_RL_speaker                    livedesk/leopard/mod_desktop/functions/built-in.o
                                                  livedesk/leopard/mod_desktop/desktop_api.o
dsk_aux_get_bass                                  livedesk/leopard/mod_desktop/functions/built-in.o
                                                  livedesk/leopard/mod_desktop/desktop_api.o
dsk_aux_get_bass_by_eq                            livedesk/leopard/mod_desktop/functions/built-in.o
                                                  livedesk/leopard/mod_desktop/desktop_api.o
dsk_aux_get_btphone_volume                        livedesk/leopard/mod_desktop/functions/built-in.o
                                                  livedesk/leopard/mod_desktop/desktop_api.o
dsk_aux_get_channel_loud                          livedesk/leopard/mod_desktop/functions/built-in.o
                                                  livedesk/leopard/mod_desktop/desktop_api.o
dsk_aux_get_dbss                                  livedesk/leopard/mod_desktop/functions/built-in.o
                                                  livedesk/leopard/mod_desktop/desktop_api.o
dsk_aux_get_iheadset_onoff                        livedesk/leopard/mod_desktop/functions/built-in.o
                                                  livedesk/leopard/mod_desktop/desktop_api.o
dsk_aux_get_iheadset_volume                       livedesk/leopard/mod_desktop/functions/built-in.o
                                                  livedesk/leopard/mod_desktop/desktop_api.o
dsk_aux_get_middle                                livedesk/leopard/mod_desktop/functions/built-in.o
                                                  livedesk/leopard/mod_desktop/desktop_api.o
dsk_aux_get_middle_by_eq                          livedesk/leopard/mod_desktop/functions/built-in.o
                                                  livedesk/leopard/mod_desktop/desktop_api.o
dsk_aux_get_mix_level                             livedesk/leopard/mod_desktop/functions/built-in.o
                                                  livedesk/leopard/mod_desktop/desktop_api.o
dsk_aux_get_mix_mode                              livedesk/leopard/mod_desktop/functions/built-in.o
                                                  livedesk/leopard/mod_desktop/desktop_api.o
dsk_aux_get_mute_status                           livedesk/leopard/mod_desktop/functions/built-in.o
                                                  livedesk/leopard/mod_desktop/desktop_api.o
dsk_aux_get_rbs_cutoff                            livedesk/leopard/mod_desktop/functions/built-in.o
                                                  livedesk/leopard/mod_desktop/desktop_api.o
dsk_aux_get_rbs_onoff                             livedesk/leopard/mod_desktop/functions/built-in.o
                                                  livedesk/leopard/mod_desktop/desktop_api.o
dsk_aux_get_rbs_volume                            livedesk/leopard/mod_desktop/functions/built-in.o
                                                  livedesk/leopard/mod_desktop/desktop_api.o
dsk_aux_get_select_channel                        livedesk/leopard/mod_desktop/functions/built-in.o
                                                  livedesk/leopard/mod_desktop/desktop_api.o
dsk_aux_get_subwoofer_cutoff                      livedesk/leopard/mod_desktop/functions/built-in.o
                                                  livedesk/leopard/mod_desktop/desktop_api.o
dsk_aux_get_subwoofer_onoff                       livedesk/leopard/mod_desktop/functions/built-in.o
                                                  livedesk/leopard/mod_desktop/desktop_api.o
dsk_aux_get_subwoofer_volume                      livedesk/leopard/mod_desktop/functions/built-in.o
                                                  livedesk/leopard/mod_desktop/desktop_api.o
dsk_aux_get_treble                                livedesk/leopard/mod_desktop/functions/built-in.o
                                                  livedesk/leopard/mod_desktop/desktop_api.o
dsk_aux_get_treble_by_eq                          livedesk/leopard/mod_desktop/functions/built-in.o
                                                  livedesk/leopard/mod_desktop/desktop_api.o
dsk_aux_get_volume                                livedesk/leopard/mod_desktop/functions/built-in.o
                                                  livedesk/leopard/mod_desktop/desktop_api.o
dsk_aux_get_xbass                                 livedesk/leopard/mod_desktop/functions/built-in.o
                                                  livedesk/leopard/mod_desktop/desktop_api.o
dsk_aux_init                                      livedesk/leopard/mod_desktop/functions/built-in.o
                                                  livedesk/leopard/mod_desktop/desktop_api.o
dsk_aux_item_reset                                livedesk/leopard/mod_desktop/functions/built-in.o
                                                  livedesk/leopard/mod_desktop/desktop_api.o
dsk_aux_mcu_close                                 livedesk/leopard/mod_desktop/functions/built-in.o
dsk_aux_mcu_open                                  livedesk/leopard/mod_desktop/functions/built-in.o
dsk_aux_open                                      livedesk/leopard/mod_desktop/functions/built-in.o
dsk_aux_reset                                     livedesk/leopard/mod_desktop/functions/built-in.o
                                                  livedesk/leopard/mod_desktop/desktop_api.o
dsk_aux_reset_EQ_mode                             livedesk/leopard/mod_desktop/functions/built-in.o
                                                  livedesk/leopard/mod_desktop/desktop_api.o
dsk_aux_select_channel                            livedesk/leopard/mod_desktop/functions/built-in.o
                                                  livedesk/leopard/mod_desktop/desktop_api.o
dsk_aux_select_channel_with_mute                  livedesk/leopard/mod_desktop/functions/built-in.o
                                                  livedesk/leopard/mod_desktop/desktop_api.o
dsk_aux_set_EQ                                    livedesk/leopard/mod_desktop/functions/built-in.o
                                                  livedesk/leopard/mod_desktop/desktop_api.o
dsk_aux_set_EQ_ex                                 livedesk/leopard/mod_desktop/functions/built-in.o
                                                  livedesk/leopard/mod_desktop/desktop_api.o
dsk_aux_set_EQ_from_user                          livedesk/leopard/mod_desktop/functions/built-in.o
                                                  livedesk/leopard/mod_desktop/desktop_api.o
dsk_aux_set_EQ_from_user_ex                       livedesk/leopard/mod_desktop/functions/built-in.o
                                                  livedesk/leopard/mod_desktop/desktop_api.o
dsk_aux_set_EQ_mode                               livedesk/leopard/mod_desktop/functions/built-in.o
                                                  livedesk/leopard/mod_desktop/desktop_api.o
dsk_aux_set_bass                                  livedesk/leopard/mod_desktop/functions/built-in.o
                                                  livedesk/leopard/mod_desktop/desktop_api.o
dsk_aux_set_bt_module_volume                      livedesk/leopard/mod_desktop/functions/built-in.o
                                                  livedesk/leopard/mod_desktop/desktop_api.o
dsk_aux_set_btphone_EQ_off_mode                   livedesk/leopard/mod_desktop/functions/built-in.o
                                                  livedesk/leopard/mod_desktop/desktop_api.o
dsk_aux_set_btphone_volume                        livedesk/leopard/mod_desktop/functions/built-in.o
                                                  livedesk/leopard/mod_desktop/desktop_api.o
dsk_aux_set_channel_loud                          livedesk/leopard/mod_desktop/functions/built-in.o
                                                  livedesk/leopard/mod_desktop/desktop_api.o
dsk_aux_set_dbss                                  livedesk/leopard/mod_desktop/functions/built-in.o
                                                  livedesk/leopard/mod_desktop/desktop_api.o
dsk_aux_set_delay_level                           livedesk/leopard/mod_desktop/functions/built-in.o
                                                  livedesk/leopard/mod_desktop/desktop_api.o
dsk_aux_set_delay_ms                              livedesk/leopard/mod_desktop/functions/built-in.o
                                                  livedesk/leopard/mod_desktop/desktop_api.o
dsk_aux_set_double_trak                           livedesk/leopard/mod_desktop/functions/built-in.o
                                                  livedesk/leopard/mod_desktop/desktop_api.o
dsk_aux_set_dynamic                               livedesk/leopard/mod_desktop/functions/built-in.o
                                                  livedesk/leopard/mod_desktop/desktop_api.o
dsk_aux_set_front_rear_mute                       livedesk/leopard/mod_desktop/functions/built-in.o
                                                  livedesk/leopard/mod_desktop/desktop_api.o
dsk_aux_set_i2s_input_format_1_2_3                livedesk/leopard/mod_desktop/functions/built-in.o
                                                  livedesk/leopard/mod_desktop/desktop_api.o
dsk_aux_set_i2s_input_format_4                    livedesk/leopard/mod_desktop/functions/built-in.o
                                                  livedesk/leopard/mod_desktop/desktop_api.o
dsk_aux_set_iheadset_onoff                        livedesk/leopard/mod_desktop/functions/built-in.o
                                                  livedesk/leopard/mod_desktop/desktop_api.o
dsk_aux_set_iheadset_volume                       livedesk/leopard/mod_desktop/functions/built-in.o
                                                  livedesk/leopard/mod_desktop/desktop_api.o
dsk_aux_set_middle                                livedesk/leopard/mod_desktop/functions/built-in.o
                                                  livedesk/leopard/mod_desktop/desktop_api.o
dsk_aux_set_mix_level                             livedesk/leopard/mod_desktop/functions/built-in.o
                                                  livedesk/leopard/mod_desktop/desktop_api.o
dsk_aux_set_mix_mode                              livedesk/leopard/mod_desktop/functions/built-in.o
                                                  livedesk/leopard/mod_desktop/desktop_api.o
dsk_aux_set_mix_on_off                            livedesk/leopard/mod_desktop/functions/built-in.o
                                                  livedesk/leopard/mod_desktop/desktop_api.o
dsk_aux_set_music_zone                            livedesk/leopard/mod_desktop/functions/built-in.o
                                                  livedesk/leopard/mod_desktop/desktop_api.o
dsk_aux_set_mute                                  livedesk/leopard/mod_desktop/functions/built-in.o
                                                  livedesk/leopard/mod_desktop/desktop_api.o
dsk_aux_set_rbs_cutoff                            livedesk/leopard/mod_desktop/functions/built-in.o
                                                  livedesk/leopard/mod_desktop/desktop_api.o
dsk_aux_set_rbs_onoff                             livedesk/leopard/mod_desktop/functions/built-in.o
                                                  livedesk/leopard/mod_desktop/desktop_api.o
dsk_aux_set_rbs_volume                            livedesk/leopard/mod_desktop/functions/built-in.o
                                                  livedesk/leopard/mod_desktop/desktop_api.o
dsk_aux_set_subwoofer_cutoff                      livedesk/leopard/mod_desktop/functions/built-in.o
                                                  livedesk/leopard/mod_desktop/desktop_api.o
dsk_aux_set_subwoofer_onoff                       livedesk/leopard/mod_desktop/functions/built-in.o
                                                  livedesk/leopard/mod_desktop/desktop_api.o
dsk_aux_set_subwoofer_phase                       livedesk/leopard/mod_desktop/functions/built-in.o
                                                  livedesk/leopard/mod_desktop/desktop_api.o
dsk_aux_set_subwoofer_volume                      livedesk/leopard/mod_desktop/functions/built-in.o
                                                  livedesk/leopard/mod_desktop/desktop_api.o
dsk_aux_set_treble                                livedesk/leopard/mod_desktop/functions/built-in.o
                                                  livedesk/leopard/mod_desktop/desktop_api.o
dsk_aux_set_volume                                livedesk/leopard/mod_desktop/functions/built-in.o
                                                  livedesk/leopard/mod_desktop/desktop_api.o
dsk_aux_set_xbass                                 livedesk/leopard/mod_desktop/functions/built-in.o
                                                  livedesk/leopard/mod_desktop/desktop_api.o
dsk_aux_set_xbass_by_level                        livedesk/leopard/mod_desktop/functions/built-in.o
                                                  livedesk/leopard/mod_desktop/desktop_api.o
dsk_bt_send_ask_ble_name                          livedesk/leopard/mod_desktop/functions/built-in.o
                                                  livedesk/leopard/mod_desktop/desktop_api.o
dsk_bt_send_ask_ble_pair_name                     livedesk/leopard/mod_desktop/functions/built-in.o
                                                  livedesk/leopard/mod_desktop/desktop_api.o
dsk_bt_send_ask_device_name                       livedesk/leopard/mod_desktop/functions/built-in.o
                                                  livedesk/leopard/mod_desktop/desktop_api.o
dsk_bt_send_ble_notify_onoff                      livedesk/leopard/mod_desktop/functions/built-in.o
                                                  livedesk/leopard/mod_desktop/desktop_api.o
dsk_bt_send_connect_ble                           livedesk/leopard/mod_desktop/functions/built-in.o
                                                  livedesk/leopard/mod_desktop/desktop_api.o
dsk_bt_send_connect_device                        livedesk/leopard/mod_desktop/functions/built-in.o
                                                  livedesk/leopard/mod_desktop/desktop_api.o
dsk_bt_send_disconnect_ble                        livedesk/leopard/mod_desktop/functions/built-in.o
                                                  livedesk/leopard/mod_desktop/desktop_api.o
dsk_bt_send_disconnect_device                     livedesk/leopard/mod_desktop/functions/built-in.o
                                                  livedesk/leopard/mod_desktop/desktop_api.o
dsk_bt_send_into_ble                              livedesk/leopard/mod_desktop/functions/built-in.o
                                                  livedesk/leopard/mod_desktop/desktop_api.o
dsk_bt_send_remove_ble_device                     livedesk/leopard/mod_desktop/functions/built-in.o
                                                  livedesk/leopard/mod_desktop/desktop_api.o
dsk_bt_send_remove_device                         livedesk/leopard/mod_desktop/functions/built-in.o
                                                  livedesk/leopard/mod_desktop/desktop_api.o
dsk_bt_send_voice_on_off                          livedesk/leopard/mod_desktop/functions/built-in.o
                                                  livedesk/leopard/mod_desktop/desktop_api.o
dsk_btmoudel_close                                livedesk/leopard/mod_desktop/functions/built-in.o
dsk_btmoudel_open                                 livedesk/leopard/mod_desktop/functions/built-in.o
dsk_dab_close                                     livedesk/leopard/mod_desktop/functions/built-in.o
dsk_dab_init_mode                                 livedesk/leopard/mod_desktop/functions/built-in.o
                                                  livedesk/leopard/mod_desktop/desktop_api.o
dsk_dab_load_and_play                             livedesk/leopard/mod_desktop/functions/built-in.o
                                                  livedesk/leopard/mod_desktop/desktop_api.o
dsk_dab_mode_key_func                             livedesk/leopard/mod_desktop/functions/built-in.o
                                                  livedesk/leopard/mod_desktop/desktop_api.o
dsk_dab_mode_preset                               livedesk/leopard/mod_desktop/functions/built-in.o
                                                  livedesk/leopard/mod_desktop/desktop_api.o
dsk_dab_mode_save_erase                           livedesk/leopard/mod_desktop/functions/built-in.o
                                                  livedesk/leopard/mod_desktop/desktop_api.o
dsk_dab_mode_save_flush                           livedesk/leopard/mod_desktop/functions/built-in.o
                                                  livedesk/leopard/mod_desktop/desktop_api.o
dsk_dab_mode_sel_preset                           livedesk/leopard/mod_desktop/functions/built-in.o
                                                  livedesk/leopard/mod_desktop/desktop_api.o
dsk_dab_mode_switch_band                          livedesk/leopard/mod_desktop/functions/built-in.o
                                                  livedesk/leopard/mod_desktop/desktop_api.o
dsk_dab_open                                      livedesk/leopard/mod_desktop/functions/built-in.o
dsk_dab_sel_radio_module                          livedesk/leopard/mod_desktop/functions/built-in.o
                                                  livedesk/leopard/mod_desktop/desktop_api.o
dsk_dab_uninstall_radio_module                    livedesk/leopard/mod_desktop/functions/built-in.o
                                                  livedesk/leopard/mod_desktop/desktop_api.o
dsk_display_dskfmt2epdkfmt                        livedesk/leopard/mod_desktop/functions/built-in.o
                                                  livedesk/leopard/mod_desktop/desktop_api.o
dsk_display_enhance_demo_disable                  livedesk/leopard/mod_desktop/functions/built-in.o
                                                  livedesk/leopard/mod_desktop/desktop_api.o
dsk_display_enhance_demo_enable                   livedesk/leopard/mod_desktop/functions/built-in.o
                                                  livedesk/leopard/mod_desktop/desktop_api.o
dsk_display_enhance_disable                       livedesk/leopard/mod_desktop/functions/built-in.o
                                                  livedesk/leopard/mod_desktop/desktop_api.o
dsk_display_enhance_enable                        livedesk/leopard/mod_desktop/functions/built-in.o
                                                  livedesk/leopard/mod_desktop/desktop_api.o
dsk_display_epdkfmt2dskfmt                        livedesk/leopard/mod_desktop/functions/built-in.o
                                                  livedesk/leopard/mod_desktop/desktop_api.o
dsk_display_get_backcolor                         livedesk/leopard/mod_desktop/functions/built-in.o
                                                  livedesk/leopard/mod_desktop/desktop_api.o
dsk_display_get_enhance_enable                    livedesk/leopard/mod_desktop/functions/built-in.o
                                                  livedesk/leopard/mod_desktop/desktop_api.o
dsk_display_get_format                            livedesk/leopard/mod_desktop/functions/built-in.o
                                                  livedesk/leopard/mod_desktop/desktop_api.o
dsk_display_get_lcd_bright                        livedesk/leopard/mod_desktop/functions/built-in.o
                                                  livedesk/leopard/mod_desktop/desktop_api.o
dsk_display_get_lcd_bright_ex                     livedesk/leopard/mod_desktop/functions/built-in.o
                                                  livedesk/leopard/mod_desktop/desktop_api.o
dsk_display_get_lcd_brightness                    livedesk/leopard/mod_desktop/functions/built-in.o
                                                  livedesk/leopard/mod_desktop/desktop_api.o
dsk_display_get_lcd_constract                     livedesk/leopard/mod_desktop/functions/built-in.o
                                                  livedesk/leopard/mod_desktop/desktop_api.o
dsk_display_get_lcd_constract_ex                  livedesk/leopard/mod_desktop/functions/built-in.o
                                                  livedesk/leopard/mod_desktop/desktop_api.o
dsk_display_get_lcd_denoise                       livedesk/leopard/mod_desktop/functions/built-in.o
                                                  livedesk/leopard/mod_desktop/desktop_api.o
dsk_display_get_lcd_detail                        livedesk/leopard/mod_desktop/functions/built-in.o
                                                  livedesk/leopard/mod_desktop/desktop_api.o
dsk_display_get_lcd_edge                          livedesk/leopard/mod_desktop/functions/built-in.o
                                                  livedesk/leopard/mod_desktop/desktop_api.o
dsk_display_get_lcd_hue                           livedesk/leopard/mod_desktop/functions/built-in.o
                                                  livedesk/leopard/mod_desktop/desktop_api.o
dsk_display_get_lcd_hue_ex                        livedesk/leopard/mod_desktop/functions/built-in.o
                                                  livedesk/leopard/mod_desktop/desktop_api.o
dsk_display_get_lcd_saturation                    livedesk/leopard/mod_desktop/functions/built-in.o
                                                  livedesk/leopard/mod_desktop/desktop_api.o
dsk_display_get_lcd_saturation_ex                 livedesk/leopard/mod_desktop/functions/built-in.o
                                                  livedesk/leopard/mod_desktop/desktop_api.o
dsk_display_get_output_type                       livedesk/leopard/mod_desktop/functions/built-in.o
                                                  livedesk/leopard/mod_desktop/desktop_api.o
dsk_display_get_size                              livedesk/leopard/mod_desktop/functions/built-in.o
                                                  livedesk/leopard/mod_desktop/desktop_api.o
dsk_display_get_size1                             livedesk/leopard/mod_desktop/functions/built-in.o
                                                  livedesk/leopard/mod_desktop/desktop_api.o
dsk_display_hdmi_audio_enable                     livedesk/leopard/mod_desktop/functions/built-in.o
                                                  livedesk/leopard/mod_desktop/desktop_api.o
dsk_display_lcd_backlight_enable                  livedesk/leopard/mod_desktop/functions/built-in.o
                                                  livedesk/leopard/mod_desktop/desktop_api.o
dsk_display_lcd_off                               livedesk/leopard/mod_desktop/functions/built-in.o
                                                  livedesk/leopard/mod_desktop/desktop_api.o
dsk_display_lcd_on                                livedesk/leopard/mod_desktop/functions/built-in.o
                                                  livedesk/leopard/mod_desktop/desktop_api.o
dsk_display_off                                   livedesk/leopard/mod_desktop/functions/built-in.o
                                                  livedesk/leopard/mod_desktop/desktop_api.o
dsk_display_on                                    livedesk/leopard/mod_desktop/functions/built-in.o
                                                  livedesk/leopard/mod_desktop/desktop_api.o
dsk_display_set_backcolor                         livedesk/leopard/mod_desktop/functions/built-in.o
                                                  livedesk/leopard/mod_desktop/desktop_api.o
dsk_display_set_format                            livedesk/leopard/mod_desktop/functions/built-in.o
                                                  livedesk/leopard/mod_desktop/desktop_api.o
dsk_display_set_lcd_bright                        livedesk/leopard/mod_desktop/functions/built-in.o
                                                  livedesk/leopard/mod_desktop/desktop_api.o
dsk_display_set_lcd_bright1_ex                    livedesk/leopard/mod_desktop/functions/built-in.o
                                                  livedesk/leopard/mod_desktop/desktop_api.o
dsk_display_set_lcd_bright_ex                     livedesk/leopard/mod_desktop/functions/built-in.o
                                                  livedesk/leopard/mod_desktop/desktop_api.o
dsk_display_set_lcd_brightness                    livedesk/leopard/mod_desktop/functions/built-in.o
                                                  livedesk/leopard/mod_desktop/desktop_api.o
dsk_display_set_lcd_constract                     livedesk/leopard/mod_desktop/functions/built-in.o
                                                  livedesk/leopard/mod_desktop/desktop_api.o
dsk_display_set_lcd_constract1_ex                 livedesk/leopard/mod_desktop/functions/built-in.o
                                                  livedesk/leopard/mod_desktop/desktop_api.o
dsk_display_set_lcd_constract_ex                  livedesk/leopard/mod_desktop/functions/built-in.o
                                                  livedesk/leopard/mod_desktop/desktop_api.o
dsk_display_set_lcd_denoise                       livedesk/leopard/mod_desktop/functions/built-in.o
                                                  livedesk/leopard/mod_desktop/desktop_api.o
dsk_display_set_lcd_detail                        livedesk/leopard/mod_desktop/functions/built-in.o
                                                  livedesk/leopard/mod_desktop/desktop_api.o
dsk_display_set_lcd_edge                          livedesk/leopard/mod_desktop/functions/built-in.o
                                                  livedesk/leopard/mod_desktop/desktop_api.o
dsk_display_set_lcd_fadeoff                       livedesk/leopard/mod_desktop/functions/built-in.o
dsk_display_set_lcd_fadeon                        livedesk/leopard/mod_desktop/functions/built-in.o
dsk_display_set_lcd_hue                           livedesk/leopard/mod_desktop/functions/built-in.o
                                                  livedesk/leopard/mod_desktop/desktop_api.o
dsk_display_set_lcd_hue1_ex                       livedesk/leopard/mod_desktop/functions/built-in.o
                                                  livedesk/leopard/mod_desktop/desktop_api.o
dsk_display_set_lcd_hue_ex                        livedesk/leopard/mod_desktop/functions/built-in.o
                                                  livedesk/leopard/mod_desktop/desktop_api.o
dsk_display_set_lcd_saturation                    livedesk/leopard/mod_desktop/functions/built-in.o
                                                  livedesk/leopard/mod_desktop/desktop_api.o
dsk_display_set_lcd_saturation1_ex                livedesk/leopard/mod_desktop/functions/built-in.o
                                                  livedesk/leopard/mod_desktop/desktop_api.o
dsk_display_set_lcd_saturation_ex                 livedesk/leopard/mod_desktop/functions/built-in.o
                                                  livedesk/leopard/mod_desktop/desktop_api.o
dsk_get_audio_if                                  livedesk/leopard/mod_desktop/functions/built-in.o
                                                  livedesk/leopard/mod_desktop/desktop_api.o
dsk_get_ble_device_addr                           livedesk/leopard/mod_desktop/functions/built-in.o
                                                  livedesk/leopard/mod_desktop/desktop_api.o
dsk_get_ble_device_name                           livedesk/leopard/mod_desktop/functions/built-in.o
                                                  livedesk/leopard/mod_desktop/desktop_api.o
dsk_get_bluetooth_a2dp_connect_status             livedesk/leopard/mod_desktop/functions/built-in.o
                                                  livedesk/leopard/mod_desktop/desktop_api.o
dsk_get_bluetooth_call_status                     livedesk/leopard/mod_desktop/functions/built-in.o
                                                  livedesk/leopard/mod_desktop/desktop_api.o
dsk_get_bluetooth_encoding_type                   livedesk/leopard/mod_desktop/functions/built-in.o
                                                  livedesk/leopard/mod_desktop/desktop_api.o
dsk_get_bluetooth_folder_index                    livedesk/leopard/mod_desktop/functions/built-in.o
                                                  livedesk/leopard/mod_desktop/desktop_api.o
dsk_get_bluetooth_get_id3                         livedesk/leopard/mod_desktop/functions/built-in.o
                                                  livedesk/leopard/mod_desktop/desktop_api.o
dsk_get_bluetooth_hf_connect_status               livedesk/leopard/mod_desktop/functions/built-in.o
                                                  livedesk/leopard/mod_desktop/desktop_api.o
dsk_get_bluetooth_id3_status                      livedesk/leopard/mod_desktop/functions/built-in.o
                                                  livedesk/leopard/mod_desktop/desktop_api.o
dsk_get_bluetooth_phone_book_memory_name          livedesk/leopard/mod_desktop/functions/built-in.o
                                                  livedesk/leopard/mod_desktop/desktop_api.o
dsk_get_bluetooth_phone_book_memory_phonenum      livedesk/leopard/mod_desktop/functions/built-in.o
                                                  livedesk/leopard/mod_desktop/desktop_api.o
dsk_get_bluetooth_phone_book_memory_total_num     livedesk/leopard/mod_desktop/functions/built-in.o
                                                  livedesk/leopard/mod_desktop/desktop_api.o
dsk_get_bluetooth_phone_history_out_day           livedesk/leopard/mod_desktop/functions/built-in.o
                                                  livedesk/leopard/mod_desktop/desktop_api.o
dsk_get_bluetooth_phone_history_out_hour          livedesk/leopard/mod_desktop/functions/built-in.o
                                                  livedesk/leopard/mod_desktop/desktop_api.o
dsk_get_bluetooth_phone_history_out_min           livedesk/leopard/mod_desktop/functions/built-in.o
                                                  livedesk/leopard/mod_desktop/desktop_api.o
dsk_get_bluetooth_phone_history_out_month         livedesk/leopard/mod_desktop/functions/built-in.o
                                                  livedesk/leopard/mod_desktop/desktop_api.o
dsk_get_bluetooth_phone_history_out_sec           livedesk/leopard/mod_desktop/functions/built-in.o
                                                  livedesk/leopard/mod_desktop/desktop_api.o
dsk_get_bluetooth_phone_history_out_year          livedesk/leopard/mod_desktop/functions/built-in.o
                                                  livedesk/leopard/mod_desktop/desktop_api.o
dsk_get_bluetooth_phone_talking_time              livedesk/leopard/mod_desktop/functions/built-in.o
                                                  livedesk/leopard/mod_desktop/desktop_api.o
dsk_get_bluetooth_phone_transfer_status           livedesk/leopard/mod_desktop/functions/built-in.o
                                                  livedesk/leopard/mod_desktop/desktop_api.o
dsk_get_bluetooth_phonenum_book_memory_index      livedesk/leopard/mod_desktop/functions/built-in.o
                                                  livedesk/leopard/mod_desktop/desktop_api.o
dsk_get_bluetooth_play_profile                    livedesk/leopard/mod_desktop/functions/built-in.o
                                                  livedesk/leopard/mod_desktop/desktop_api.o
dsk_get_bluetooth_play_status                     livedesk/leopard/mod_desktop/functions/built-in.o
                                                  livedesk/leopard/mod_desktop/desktop_api.o
dsk_get_bluetooth_play_time                       livedesk/leopard/mod_desktop/functions/built-in.o
                                                  livedesk/leopard/mod_desktop/desktop_api.o
dsk_get_bluetooth_random_mode                     livedesk/leopard/mod_desktop/functions/built-in.o
                                                  livedesk/leopard/mod_desktop/desktop_api.o
dsk_get_bluetooth_repeat_mode                     livedesk/leopard/mod_desktop/functions/built-in.o
                                                  livedesk/leopard/mod_desktop/desktop_api.o
dsk_get_bluetooth_song_index                      livedesk/leopard/mod_desktop/functions/built-in.o
                                                  livedesk/leopard/mod_desktop/desktop_api.o
dsk_get_bluetooth_sw                              livedesk/leopard/mod_desktop/functions/built-in.o
                                                  livedesk/leopard/mod_desktop/desktop_api.o
dsk_get_bluetooth_total_song_number               livedesk/leopard/mod_desktop/functions/built-in.o
                                                  livedesk/leopard/mod_desktop/desktop_api.o
dsk_get_bluetooth_total_time                      livedesk/leopard/mod_desktop/functions/built-in.o
                                                  livedesk/leopard/mod_desktop/desktop_api.o
dsk_get_bluetooth_upgrade_ask                     livedesk/leopard/mod_desktop/functions/built-in.o
                                                  livedesk/leopard/mod_desktop/desktop_api.o
dsk_get_bluetooth_upgrade_file_found              livedesk/leopard/mod_desktop/functions/built-in.o
                                                  livedesk/leopard/mod_desktop/desktop_api.o
dsk_get_bluetooth_upgrade_file_pro                livedesk/leopard/mod_desktop/functions/built-in.o
                                                  livedesk/leopard/mod_desktop/desktop_api.o
dsk_get_bluetooth_upgrade_file_size               livedesk/leopard/mod_desktop/functions/built-in.o
                                                  livedesk/leopard/mod_desktop/desktop_api.o
dsk_get_bluetooth_upgrade_success                 livedesk/leopard/mod_desktop/functions/built-in.o
                                                  livedesk/leopard/mod_desktop/desktop_api.o
dsk_get_bluetooth_usb_device_exist                livedesk/leopard/mod_desktop/functions/built-in.o
                                                  livedesk/leopard/mod_desktop/desktop_api.o
dsk_get_bluetooth_usb_encoding_type               livedesk/leopard/mod_desktop/functions/built-in.o
                                                  livedesk/leopard/mod_desktop/desktop_api.o
dsk_get_bluetooth_usb_folder_index                livedesk/leopard/mod_desktop/functions/built-in.o
                                                  livedesk/leopard/mod_desktop/desktop_api.o
dsk_get_bluetooth_usb_get_id3                     livedesk/leopard/mod_desktop/functions/built-in.o
                                                  livedesk/leopard/mod_desktop/desktop_api.o
dsk_get_bluetooth_usb_id3_status                  livedesk/leopard/mod_desktop/functions/built-in.o
                                                  livedesk/leopard/mod_desktop/desktop_api.o
dsk_get_bluetooth_usb_play_status                 livedesk/leopard/mod_desktop/functions/built-in.o
                                                  livedesk/leopard/mod_desktop/desktop_api.o
dsk_get_bluetooth_usb_play_time                   livedesk/leopard/mod_desktop/functions/built-in.o
                                                  livedesk/leopard/mod_desktop/desktop_api.o
dsk_get_bluetooth_usb_random_mode                 livedesk/leopard/mod_desktop/functions/built-in.o
                                                  livedesk/leopard/mod_desktop/desktop_api.o
dsk_get_bluetooth_usb_repeat_mode                 livedesk/leopard/mod_desktop/functions/built-in.o
                                                  livedesk/leopard/mod_desktop/desktop_api.o
dsk_get_bluetooth_usb_song_index                  livedesk/leopard/mod_desktop/functions/built-in.o
                                                  livedesk/leopard/mod_desktop/desktop_api.o
dsk_get_bluetooth_usb_total_song_number           livedesk/leopard/mod_desktop/functions/built-in.o
                                                  livedesk/leopard/mod_desktop/desktop_api.o
dsk_get_bluetooth_usb_total_time                  livedesk/leopard/mod_desktop/functions/built-in.o
                                                  livedesk/leopard/mod_desktop/desktop_api.o
dsk_get_bt_avrcp_category_number                  livedesk/leopard/mod_desktop/functions/built-in.o
                                                  livedesk/leopard/mod_desktop/desktop_api.o
dsk_get_bt_avrcp_folder_name                      livedesk/leopard/mod_desktop/functions/built-in.o
                                                  livedesk/leopard/mod_desktop/desktop_api.o
dsk_get_bt_avrcp_name_current_number              livedesk/leopard/mod_desktop/functions/built-in.o
                                                  livedesk/leopard/mod_desktop/desktop_api.o
dsk_get_bt_avrcp_name_total_number                livedesk/leopard/mod_desktop/functions/built-in.o
                                                  livedesk/leopard/mod_desktop/desktop_api.o
dsk_get_bt_avrcp_previous_layer_num               livedesk/leopard/mod_desktop/functions/built-in.o
                                                  livedesk/leopard/mod_desktop/desktop_api.o
dsk_get_bt_avrcp_previous_layer_song_layer_yes_or_no livedesk/leopard/mod_desktop/functions/built-in.o
                                                  livedesk/leopard/mod_desktop/desktop_api.o
dsk_get_bt_avrcp_song_layer_yes_or_no             livedesk/leopard/mod_desktop/functions/built-in.o
                                                  livedesk/leopard/mod_desktop/desktop_api.o
dsk_get_bt_avrcp_status                           livedesk/leopard/mod_desktop/functions/built-in.o
                                                  livedesk/leopard/mod_desktop/desktop_api.o
dsk_get_bt_device_name                            livedesk/leopard/mod_desktop/functions/built-in.o
                                                  livedesk/leopard/mod_desktop/desktop_api.o
dsk_get_charge_state                              livedesk/leopard/mod_desktop/functions/built-in.o
                                                  livedesk/leopard/mod_desktop/desktop_api.o
dsk_get_fs_charset                                livedesk/leopard/mod_desktop/functions/built-in.o
                                                  livedesk/leopard/mod_desktop/desktop_api.o
dsk_get_isn_charset                               livedesk/leopard/mod_desktop/functions/built-in.o
                                                  livedesk/leopard/mod_desktop/desktop_api.o
dsk_get_langres_charset                           livedesk/leopard/mod_desktop/functions/built-in.o
                                                  livedesk/leopard/mod_desktop/desktop_api.o
dsk_get_usb_ipod_category_number                  livedesk/leopard/mod_desktop/functions/built-in.o
                                                  livedesk/leopard/mod_desktop/desktop_api.o
dsk_get_usb_ipod_folder_name                      livedesk/leopard/mod_desktop/functions/built-in.o
                                                  livedesk/leopard/mod_desktop/desktop_api.o
dsk_get_usb_ipod_name_current_number              livedesk/leopard/mod_desktop/functions/built-in.o
                                                  livedesk/leopard/mod_desktop/desktop_api.o
dsk_get_usb_ipod_name_total_number                livedesk/leopard/mod_desktop/functions/built-in.o
                                                  livedesk/leopard/mod_desktop/desktop_api.o
dsk_get_usb_ipod_previous_layer_num               livedesk/leopard/mod_desktop/functions/built-in.o
                                                  livedesk/leopard/mod_desktop/desktop_api.o
dsk_get_usb_ipod_previous_layer_song_layer_yes_or_no livedesk/leopard/mod_desktop/functions/built-in.o
                                                  livedesk/leopard/mod_desktop/desktop_api.o
dsk_get_usb_ipod_song_layer_yes_or_no             livedesk/leopard/mod_desktop/functions/built-in.o
                                                  livedesk/leopard/mod_desktop/desktop_api.o
dsk_get_usb_ipod_status                           livedesk/leopard/mod_desktop/functions/built-in.o
                                                  livedesk/leopard/mod_desktop/desktop_api.o
dsk_hp_det_pin_det                                livedesk/leopard/mod_desktop/functions/built-in.o
                                                  livedesk/leopard/mod_desktop/desktop_api.o
dsk_keytone_exit                                  livedesk/leopard/mod_desktop/functions/built-in.o
                                                  livedesk/leopard/mod_desktop/desktop_api.o
dsk_keytone_get_key_type                          livedesk/leopard/mod_desktop/functions/built-in.o
                                                  livedesk/leopard/mod_desktop/desktop_api.o
dsk_keytone_get_state                             livedesk/leopard/mod_desktop/functions/built-in.o
                                                  livedesk/leopard/mod_desktop/desktop_api.o
dsk_keytone_init                                  livedesk/leopard/mod_desktop/functions/built-in.o
                                                  livedesk/leopard/mod_desktop/desktop_api.o
dsk_keytone_on                                    livedesk/leopard/mod_desktop/functions/built-in.o
                                                  livedesk/leopard/mod_desktop/desktop_api.o
dsk_keytone_set_key_type                          livedesk/leopard/mod_desktop/functions/built-in.o
                                                  livedesk/leopard/mod_desktop/desktop_api.o
dsk_keytone_set_state                             livedesk/leopard/mod_desktop/functions/built-in.o
                                                  livedesk/leopard/mod_desktop/desktop_api.o
dsk_langres_exit                                  livedesk/leopard/mod_desktop/functions/built-in.o
                                                  livedesk/leopard/mod_desktop/desktop_api.o
dsk_langres_get_menu_len                          livedesk/leopard/mod_desktop/functions/built-in.o
                                                  livedesk/leopard/mod_desktop/desktop_api.o
dsk_langres_get_menu_text                         livedesk/leopard/mod_desktop/functions/built-in.o
                                                  livedesk/leopard/mod_desktop/desktop_api.o
dsk_langres_get_type                              livedesk/leopard/mod_desktop/functions/built-in.o
                                                  livedesk/leopard/mod_desktop/desktop_api.o
dsk_langres_init                                  livedesk/leopard/mod_desktop/functions/built-in.o
                                                  livedesk/leopard/mod_desktop/desktop_api.o
dsk_langres_set_type                              livedesk/leopard/mod_desktop/functions/built-in.o
                                                  livedesk/leopard/mod_desktop/desktop_api.o
dsk_mcu_close                                     livedesk/leopard/mod_desktop/functions/built-in.o
dsk_mcu_open                                      livedesk/leopard/mod_desktop/functions/built-in.o
dsk_orchid_check                                  livedesk/leopard/mod_desktop/functions/built-in.o
                                                  livedesk/leopard/mod_desktop/desktop_api.o
dsk_orchid_createDisk                             livedesk/leopard/mod_desktop/functions/built-in.o
                                                  livedesk/leopard/mod_desktop/desktop_api.o
dsk_orchid_load_last_npl                          livedesk/leopard/mod_desktop/functions/built-in.o
                                                  livedesk/leopard/mod_desktop/desktop_api.o
dsk_orchid_save_npl                               livedesk/leopard/mod_desktop/functions/built-in.o
                                                  livedesk/leopard/mod_desktop/desktop_api.o
dsk_orchid_update                                 livedesk/leopard/mod_desktop/functions/built-in.o
                                                  livedesk/leopard/mod_desktop/desktop_api.o
dsk_power_dev_init                                livedesk/leopard/mod_desktop/functions/built-in.o
                                                  livedesk/leopard/mod_desktop/desktop_api.o
dsk_power_dev_uninit                              livedesk/leopard/mod_desktop/functions/built-in.o
                                                  livedesk/leopard/mod_desktop/desktop_api.o
dsk_power_get_battary_state                       livedesk/leopard/mod_desktop/functions/built-in.o
                                                  livedesk/leopard/mod_desktop/desktop_api.o
dsk_power_get_battery_level                       livedesk/leopard/mod_desktop/functions/built-in.o
                                                  livedesk/leopard/mod_desktop/desktop_api.o
dsk_power_get_voltage_level                       livedesk/leopard/mod_desktop/functions/built-in.o
                                                  livedesk/leopard/mod_desktop/desktop_api.o
dsk_power_is_full                                 livedesk/leopard/mod_desktop/functions/built-in.o
                                                  livedesk/leopard/mod_desktop/desktop_api.o
dsk_power_is_low                                  livedesk/leopard/mod_desktop/functions/built-in.o
                                                  livedesk/leopard/mod_desktop/desktop_api.o
dsk_power_off                                     livedesk/leopard/mod_desktop/functions/built-in.o
                                                  livedesk/leopard/mod_desktop/desktop_api.o
dsk_power_set_off_time                            livedesk/leopard/mod_desktop/functions/built-in.o
                                                  livedesk/leopard/mod_desktop/desktop_api.o
dsk_reg_deinit_para                               livedesk/leopard/mod_desktop/functions/built-in.o
                                                  livedesk/leopard/mod_desktop/desktop_api.o
dsk_reg_flush                                     livedesk/leopard/mod_desktop/functions/built-in.o
                                                  livedesk/leopard/mod_desktop/desktop_api.o
dsk_reg_get_app_restore_flag                      livedesk/leopard/mod_desktop/functions/built-in.o
                                                  livedesk/leopard/mod_desktop/desktop_api.o
dsk_reg_get_default_para_by_app                   livedesk/leopard/mod_desktop/functions/built-in.o
                                                  livedesk/leopard/mod_desktop/desktop_api.o
dsk_reg_get_para_by_app                           livedesk/leopard/mod_desktop/functions/built-in.o
                                                  livedesk/leopard/mod_desktop/msg_srv/built-in.o
                                                  livedesk/leopard/mod_desktop/desktop_api.o
dsk_reg_init_para                                 livedesk/leopard/mod_desktop/functions/built-in.o
                                                  livedesk/leopard/mod_desktop/desktop_api.o
dsk_reg_save_cur_play_info                        livedesk/leopard/mod_desktop/functions/built-in.o
                                                  livedesk/leopard/mod_desktop/desktop_api.o
dsk_reg_set_app_restore_all                       livedesk/leopard/mod_desktop/functions/built-in.o
                                                  livedesk/leopard/mod_desktop/desktop_api.o
dsk_reg_set_app_restore_flag                      livedesk/leopard/mod_desktop/functions/built-in.o
                                                  livedesk/leopard/mod_desktop/desktop_api.o
dsk_request_bluetooth_redial_data                 livedesk/leopard/mod_desktop/functions/built-in.o
                                                  livedesk/leopard/mod_desktop/desktop_api.o
dsk_request_mcu_mamery_data                       livedesk/leopard/mod_desktop/functions/built-in.o
                                                  livedesk/leopard/mod_desktop/desktop_api.o
dsk_scan_file_key_set_cb                          livedesk/leopard/mod_desktop/msg_srv/built-in.o
                                                  livedesk/leopard/mod_desktop/desktop_api.o
dsk_send_bluetooth_avrcp_caregory_browse_begin    livedesk/leopard/mod_desktop/functions/built-in.o
                                                  livedesk/leopard/mod_desktop/desktop_api.o
dsk_send_bluetooth_avrcp_category_name            livedesk/leopard/mod_desktop/functions/built-in.o
                                                  livedesk/leopard/mod_desktop/desktop_api.o
dsk_send_bluetooth_avrcp_category_previous_layer  livedesk/leopard/mod_desktop/functions/built-in.o
                                                  livedesk/leopard/mod_desktop/desktop_api.o
dsk_send_bluetooth_avrcp_exit_browser             livedesk/leopard/mod_desktop/functions/built-in.o
                                                  livedesk/leopard/mod_desktop/desktop_api.o
dsk_send_bluetooth_avrcp_request_caregory_index   livedesk/leopard/mod_desktop/functions/built-in.o
                                                  livedesk/leopard/mod_desktop/desktop_api.o
dsk_send_bluetooth_back_connect                   livedesk/leopard/mod_desktop/functions/built-in.o
                                                  livedesk/leopard/mod_desktop/desktop_api.o
dsk_send_bluetooth_book_and_history_data          livedesk/leopard/mod_desktop/functions/built-in.o
                                                  livedesk/leopard/mod_desktop/desktop_api.o
dsk_send_bluetooth_clear_iap2_memory              livedesk/leopard/mod_desktop/functions/built-in.o
                                                  livedesk/leopard/mod_desktop/desktop_api.o
dsk_send_bluetooth_db_link_change_index           livedesk/leopard/mod_desktop/functions/built-in.o
                                                  livedesk/leopard/mod_desktop/desktop_api.o
dsk_send_bluetooth_dialing_data                   livedesk/leopard/mod_desktop/functions/built-in.o
                                                  livedesk/leopard/mod_desktop/desktop_api.o
dsk_send_bluetooth_dialing_keynum_data            livedesk/leopard/mod_desktop/functions/built-in.o
                                                  livedesk/leopard/mod_desktop/desktop_api.o
dsk_send_bluetooth_found_upgrade_file             livedesk/leopard/mod_desktop/functions/built-in.o
                                                  livedesk/leopard/mod_desktop/desktop_api.o
dsk_send_bluetooth_phonevoice_data                livedesk/leopard/mod_desktop/functions/built-in.o
                                                  livedesk/leopard/mod_desktop/desktop_api.o
dsk_send_bluetooth_reset_system                   livedesk/leopard/mod_desktop/functions/built-in.o
                                                  livedesk/leopard/mod_desktop/desktop_api.o
dsk_send_bluetooth_scan_cancel                    livedesk/leopard/mod_desktop/functions/built-in.o
                                                  livedesk/leopard/mod_desktop/desktop_api.o
dsk_send_bluetooth_scan_connect                   livedesk/leopard/mod_desktop/functions/built-in.o
                                                  livedesk/leopard/mod_desktop/desktop_api.o
dsk_send_bluetooth_scan_start                     livedesk/leopard/mod_desktop/functions/built-in.o
                                                  livedesk/leopard/mod_desktop/desktop_api.o
dsk_send_bluetooth_three_way_call_data            livedesk/leopard/mod_desktop/functions/built-in.o
                                                  livedesk/leopard/mod_desktop/desktop_api.o
dsk_send_bluetooth_upgrate_file_data              livedesk/leopard/mod_desktop/functions/built-in.o
                                                  livedesk/leopard/mod_desktop/desktop_api.o
dsk_send_bluetooth_upgrate_file_lenth             livedesk/leopard/mod_desktop/functions/built-in.o
                                                  livedesk/leopard/mod_desktop/desktop_api.o
dsk_send_bluetooth_usb_caregory_browse_begin      livedesk/leopard/mod_desktop/functions/built-in.o
                                                  livedesk/leopard/mod_desktop/desktop_api.o
dsk_send_bluetooth_usb_category_name              livedesk/leopard/mod_desktop/functions/built-in.o
                                                  livedesk/leopard/mod_desktop/desktop_api.o
dsk_send_bluetooth_usb_category_previous_layer    livedesk/leopard/mod_desktop/functions/built-in.o
                                                  livedesk/leopard/mod_desktop/desktop_api.o
dsk_send_bluetooth_usb_request_caregory_index     livedesk/leopard/mod_desktop/functions/built-in.o
                                                  livedesk/leopard/mod_desktop/desktop_api.o
dsk_send_cur_mode                                 livedesk/leopard/mod_desktop/functions/built-in.o
                                                  livedesk/leopard/mod_desktop/desktop_api.o
dsk_send_download_phonebook                       livedesk/leopard/mod_desktop/functions/built-in.o
                                                  livedesk/leopard/mod_desktop/desktop_api.o
dsk_send_insmod_usbh_cmd                          livedesk/leopard/mod_desktop/functions/built-in.o
                                                  livedesk/leopard/mod_desktop/desktop_api.o
dsk_send_mcu_beep_on_off                          livedesk/leopard/mod_desktop/functions/built-in.o
                                                  livedesk/leopard/mod_desktop/desktop_api.o
dsk_send_mcu_beep_setting                         livedesk/leopard/mod_desktop/functions/built-in.o
                                                  livedesk/leopard/mod_desktop/desktop_api.o
dsk_send_mcu_c800_update_begin                    livedesk/leopard/mod_desktop/functions/built-in.o
                                                  livedesk/leopard/mod_desktop/desktop_api.o
dsk_send_mcu_c800_update_quit                     livedesk/leopard/mod_desktop/functions/built-in.o
                                                  livedesk/leopard/mod_desktop/desktop_api.o
dsk_send_mcu_cmd                                  livedesk/leopard/mod_desktop/functions/built-in.o
                                                  livedesk/leopard/mod_desktop/desktop_api.o
dsk_send_mcu_current_source                       livedesk/leopard/mod_desktop/functions/built-in.o
                                                  livedesk/leopard/mod_desktop/desktop_api.o
dsk_send_mcu_date_time                            livedesk/leopard/mod_desktop/functions/built-in.o
                                                  livedesk/leopard/mod_desktop/desktop_api.o
dsk_send_mcu_dvr_day_hours                        livedesk/leopard/mod_desktop/functions/built-in.o
                                                  livedesk/leopard/mod_desktop/desktop_api.o
dsk_send_mcu_dvr_fast_record_lock                 livedesk/leopard/mod_desktop/functions/built-in.o
                                                  livedesk/leopard/mod_desktop/desktop_api.o
dsk_send_mcu_dvr_hide_icon                        livedesk/leopard/mod_desktop/functions/built-in.o
                                                  livedesk/leopard/mod_desktop/desktop_api.o
dsk_send_mcu_dvr_into_record                      livedesk/leopard/mod_desktop/functions/built-in.o
                                                  livedesk/leopard/mod_desktop/desktop_api.o
dsk_send_mcu_dvr_minute_seconds                   livedesk/leopard/mod_desktop/functions/built-in.o
                                                  livedesk/leopard/mod_desktop/desktop_api.o
dsk_send_mcu_dvr_show_icon                        livedesk/leopard/mod_desktop/functions/built-in.o
                                                  livedesk/leopard/mod_desktop/desktop_api.o
dsk_send_mcu_dvr_switch_front_lens                livedesk/leopard/mod_desktop/functions/built-in.o
                                                  livedesk/leopard/mod_desktop/desktop_api.o
dsk_send_mcu_dvr_switch_front_rear_lens           livedesk/leopard/mod_desktop/functions/built-in.o
                                                  livedesk/leopard/mod_desktop/desktop_api.o
dsk_send_mcu_dvr_switch_rear_lens                 livedesk/leopard/mod_desktop/functions/built-in.o
                                                  livedesk/leopard/mod_desktop/desktop_api.o
dsk_send_mcu_dvr_time_fomat_12h                   livedesk/leopard/mod_desktop/functions/built-in.o
                                                  livedesk/leopard/mod_desktop/desktop_api.o
dsk_send_mcu_dvr_time_fomat_24h                   livedesk/leopard/mod_desktop/functions/built-in.o
                                                  livedesk/leopard/mod_desktop/desktop_api.o
dsk_send_mcu_dvr_x_y_point                        livedesk/leopard/mod_desktop/functions/built-in.o
                                                  livedesk/leopard/mod_desktop/desktop_api.o
dsk_send_mcu_dvr_year_month                       livedesk/leopard/mod_desktop/functions/built-in.o
                                                  livedesk/leopard/mod_desktop/desktop_api.o
dsk_send_mcu_io                                   livedesk/leopard/mod_desktop/functions/built-in.o
                                                  livedesk/leopard/mod_desktop/desktop_api.o
dsk_send_mcu_memory                               livedesk/leopard/mod_desktop/functions/built-in.o
                                                  livedesk/leopard/mod_desktop/desktop_api.o
dsk_send_mcu_mute                                 livedesk/leopard/mod_desktop/functions/built-in.o
                                                  livedesk/leopard/mod_desktop/desktop_api.o
dsk_send_mcu_oe_key_cancel                        livedesk/leopard/mod_desktop/functions/built-in.o
                                                  livedesk/leopard/mod_desktop/desktop_api.o
dsk_send_mcu_oe_key_init                          livedesk/leopard/mod_desktop/functions/built-in.o
                                                  livedesk/leopard/mod_desktop/desktop_api.o
dsk_send_mcu_oe_key_learn                         livedesk/leopard/mod_desktop/functions/built-in.o
                                                  livedesk/leopard/mod_desktop/desktop_api.o
dsk_send_mcu_oe_key_learn_start                   livedesk/leopard/mod_desktop/functions/built-in.o
                                                  livedesk/leopard/mod_desktop/desktop_api.o
dsk_send_mcu_oe_key_quit                          livedesk/leopard/mod_desktop/functions/built-in.o
                                                  livedesk/leopard/mod_desktop/desktop_api.o
dsk_send_mcu_power_off                            livedesk/leopard/mod_desktop/functions/built-in.o
                                                  livedesk/leopard/mod_desktop/desktop_api.o
dsk_send_mcu_power_off_status                     livedesk/leopard/mod_desktop/functions/built-in.o
                                                  livedesk/leopard/mod_desktop/desktop_api.o
dsk_send_mcu_ready                                livedesk/leopard/mod_desktop/functions/built-in.o
                                                  livedesk/leopard/mod_desktop/desktop_api.o
dsk_send_mcu_reset                                livedesk/leopard/mod_desktop/functions/built-in.o
                                                  livedesk/leopard/mod_desktop/desktop_api.o
dsk_send_mcu_restart                              livedesk/leopard/mod_desktop/functions/built-in.o
                                                  livedesk/leopard/mod_desktop/desktop_api.o
dsk_send_mcu_update_begin                         livedesk/leopard/mod_desktop/functions/built-in.o
                                                  livedesk/leopard/mod_desktop/desktop_api.o
dsk_send_mcu_update_data                          livedesk/leopard/mod_desktop/functions/built-in.o
                                                  livedesk/leopard/mod_desktop/desktop_api.o
dsk_send_mcu_zones_para                           livedesk/leopard/mod_desktop/functions/built-in.o
                                                  livedesk/leopard/mod_desktop/desktop_api.o
dsk_send_pin_code_affirm                          livedesk/leopard/mod_desktop/functions/built-in.o
                                                  livedesk/leopard/mod_desktop/desktop_api.o
dsk_send_poweroff_status                          livedesk/leopard/mod_desktop/functions/built-in.o
                                                  livedesk/leopard/mod_desktop/desktop_api.o
dsk_send_requrst_update_phonebook                 livedesk/leopard/mod_desktop/functions/built-in.o
                                                  livedesk/leopard/mod_desktop/desktop_api.o
dsk_send_rmmod_usbh_cmd                           livedesk/leopard/mod_desktop/functions/built-in.o
                                                  livedesk/leopard/mod_desktop/desktop_api.o
dsk_send_stop_phonebook                           livedesk/leopard/mod_desktop/functions/built-in.o
                                                  livedesk/leopard/mod_desktop/desktop_api.o
dsk_send_tuner_type                               livedesk/leopard/mod_desktop/functions/built-in.o
                                                  livedesk/leopard/mod_desktop/desktop_api.o
dsk_set_audio_if                                  livedesk/leopard/mod_desktop/functions/built-in.o
                                                  livedesk/leopard/mod_desktop/desktop_api.o
dsk_set_auto_off_time                             livedesk/leopard/mod_desktop/functions/built-in.o
                                                  livedesk/leopard/mod_desktop/desktop_api.o
dsk_set_bluetooth_back_forward                    livedesk/leopard/mod_desktop/functions/built-in.o
                                                  livedesk/leopard/mod_desktop/desktop_api.o
dsk_set_bluetooth_confirm_upgrade                 livedesk/leopard/mod_desktop/functions/built-in.o
                                                  livedesk/leopard/mod_desktop/desktop_api.o
dsk_set_bluetooth_current_mode                    livedesk/leopard/mod_desktop/functions/built-in.o
                                                  livedesk/leopard/mod_desktop/desktop_api.o
dsk_set_bluetooth_end_ff_fb                       livedesk/leopard/mod_desktop/functions/built-in.o
                                                  livedesk/leopard/mod_desktop/desktop_api.o
dsk_set_bluetooth_fast_forward                    livedesk/leopard/mod_desktop/functions/built-in.o
                                                  livedesk/leopard/mod_desktop/desktop_api.o
dsk_set_bluetooth_folder_down                     livedesk/leopard/mod_desktop/functions/built-in.o
                                                  livedesk/leopard/mod_desktop/desktop_api.o
dsk_set_bluetooth_folder_up                       livedesk/leopard/mod_desktop/functions/built-in.o
                                                  livedesk/leopard/mod_desktop/desktop_api.o
dsk_set_bluetooth_hid_status                      livedesk/leopard/mod_desktop/functions/built-in.o
                                                  livedesk/leopard/mod_desktop/desktop_api.o
dsk_set_bluetooth_luauch_app                      livedesk/leopard/mod_desktop/functions/built-in.o
                                                  livedesk/leopard/mod_desktop/desktop_api.o
dsk_set_bluetooth_mirror_app_connect_bt           livedesk/leopard/mod_desktop/functions/built-in.o
                                                  livedesk/leopard/mod_desktop/desktop_api.o
dsk_set_bluetooth_mirror_type                     livedesk/leopard/mod_desktop/functions/built-in.o
                                                  livedesk/leopard/mod_desktop/desktop_api.o
dsk_set_bluetooth_music_time                      livedesk/leopard/mod_desktop/functions/built-in.o
                                                  livedesk/leopard/mod_desktop/desktop_api.o
dsk_set_bluetooth_mute                            livedesk/leopard/mod_desktop/functions/built-in.o
                                                  livedesk/leopard/mod_desktop/desktop_api.o
dsk_set_bluetooth_next_song                       livedesk/leopard/mod_desktop/functions/built-in.o
                                                  livedesk/leopard/mod_desktop/desktop_api.o
dsk_set_bluetooth_play_pause                      livedesk/leopard/mod_desktop/functions/built-in.o
                                                  livedesk/leopard/mod_desktop/desktop_api.o
dsk_set_bluetooth_play_status                     livedesk/leopard/mod_desktop/functions/built-in.o
                                                  livedesk/leopard/mod_desktop/desktop_api.o
dsk_set_bluetooth_prev_song                       livedesk/leopard/mod_desktop/functions/built-in.o
                                                  livedesk/leopard/mod_desktop/desktop_api.o
dsk_set_bluetooth_random_mode                     livedesk/leopard/mod_desktop/functions/built-in.o
                                                  livedesk/leopard/mod_desktop/desktop_api.o
dsk_set_bluetooth_repeat_mode                     livedesk/leopard/mod_desktop/functions/built-in.o
                                                  livedesk/leopard/mod_desktop/desktop_api.o
dsk_set_bluetooth_request_play_mode               livedesk/leopard/mod_desktop/functions/built-in.o
                                                  livedesk/leopard/mod_desktop/desktop_api.o
dsk_set_bluetooth_spp_status                      livedesk/leopard/mod_desktop/functions/built-in.o
                                                  livedesk/leopard/mod_desktop/desktop_api.o
dsk_set_bluetooth_usb_back_forward                livedesk/leopard/mod_desktop/functions/built-in.o
                                                  livedesk/leopard/mod_desktop/desktop_api.o
dsk_set_bluetooth_usb_end_forward                 livedesk/leopard/mod_desktop/functions/built-in.o
                                                  livedesk/leopard/mod_desktop/desktop_api.o
dsk_set_bluetooth_usb_fast_forward                livedesk/leopard/mod_desktop/functions/built-in.o
                                                  livedesk/leopard/mod_desktop/desktop_api.o
dsk_set_bluetooth_usb_music_time                  livedesk/leopard/mod_desktop/functions/built-in.o
                                                  livedesk/leopard/mod_desktop/desktop_api.o
dsk_set_bluetooth_usb_next_song                   livedesk/leopard/mod_desktop/functions/built-in.o
                                                  livedesk/leopard/mod_desktop/desktop_api.o
dsk_set_bluetooth_usb_play_pause                  livedesk/leopard/mod_desktop/functions/built-in.o
                                                  livedesk/leopard/mod_desktop/desktop_api.o
dsk_set_bluetooth_usb_play_status                 livedesk/leopard/mod_desktop/functions/built-in.o
                                                  livedesk/leopard/mod_desktop/desktop_api.o
dsk_set_bluetooth_usb_prev_song                   livedesk/leopard/mod_desktop/functions/built-in.o
                                                  livedesk/leopard/mod_desktop/desktop_api.o
dsk_set_bluetooth_usb_repeat_random_status        livedesk/leopard/mod_desktop/functions/built-in.o
                                                  livedesk/leopard/mod_desktop/desktop_api.o
dsk_set_fs_charset                                livedesk/leopard/mod_desktop/functions/built-in.o
                                                  livedesk/leopard/mod_desktop/desktop_api.o
dsk_set_hdmi_audio_gate                           livedesk/leopard/mod_desktop/functions/built-in.o
                                                  livedesk/leopard/mod_desktop/desktop_api.o
dsk_set_isn_charset                               livedesk/leopard/mod_desktop/functions/built-in.o
                                                  livedesk/leopard/mod_desktop/desktop_api.o
dsk_set_usb_request_play_mode                     livedesk/leopard/mod_desktop/functions/built-in.o
                                                  livedesk/leopard/mod_desktop/desktop_api.o
dsk_speaker_resume                                livedesk/leopard/mod_desktop/functions/built-in.o
                                                  livedesk/leopard/mod_desktop/desktop_api.o
dsk_speaker_turnoff                               livedesk/leopard/mod_desktop/functions/built-in.o
                                                  livedesk/leopard/mod_desktop/desktop_api.o
dsk_speaker_turnon                                livedesk/leopard/mod_desktop/functions/built-in.o
                                                  livedesk/leopard/mod_desktop/desktop_api.o
dsk_theme_close                                   livedesk/leopard/mod_desktop/functions/built-in.o
                                                  livedesk/leopard/mod_desktop/desktop_api.o
dsk_theme_close_ex                                livedesk/leopard/mod_desktop/functions/built-in.o
                                                  livedesk/leopard/mod_desktop/desktop_api.o
dsk_theme_exit                                    livedesk/leopard/mod_desktop/functions/built-in.o
                                                  livedesk/leopard/mod_desktop/desktop_api.o
dsk_theme_exit_ex                                 livedesk/leopard/mod_desktop/functions/built-in.o
                                                  livedesk/leopard/mod_desktop/desktop_api.o
dsk_theme_get_style                               livedesk/leopard/mod_desktop/functions/built-in.o
                                                  livedesk/leopard/mod_desktop/desktop_api.o
dsk_theme_hdl2buf                                 livedesk/leopard/mod_desktop/functions/built-in.o
                                                  livedesk/leopard/mod_desktop/desktop_api.o
dsk_theme_hdl2buf_ex                              livedesk/leopard/mod_desktop/functions/built-in.o
                                                  livedesk/leopard/mod_desktop/desktop_api.o
dsk_theme_hdl2size                                livedesk/leopard/mod_desktop/functions/built-in.o
                                                  livedesk/leopard/mod_desktop/desktop_api.o
dsk_theme_hdl2size_ex                             livedesk/leopard/mod_desktop/functions/built-in.o
                                                  livedesk/leopard/mod_desktop/desktop_api.o
dsk_theme_init                                    livedesk/leopard/mod_desktop/functions/built-in.o
                                                  livedesk/leopard/mod_desktop/desktop_api.o
dsk_theme_init_ex                                 livedesk/leopard/mod_desktop/functions/built-in.o
                                                  livedesk/leopard/mod_desktop/desktop_api.o
dsk_theme_open                                    livedesk/leopard/mod_desktop/functions/built-in.o
                                                  livedesk/leopard/mod_desktop/desktop_api.o
dsk_theme_open_ex                                 livedesk/leopard/mod_desktop/functions/built-in.o
                                                  livedesk/leopard/mod_desktop/desktop_api.o
dsk_theme_really_close_by_id_ex                   livedesk/leopard/mod_desktop/functions/built-in.o
                                                  livedesk/leopard/mod_desktop/desktop_api.o
dsk_theme_really_close_ex                         livedesk/leopard/mod_desktop/functions/built-in.o
                                                  livedesk/leopard/mod_desktop/desktop_api.o
dsk_theme_set_style                               livedesk/leopard/mod_desktop/functions/built-in.o
                                                  livedesk/leopard/mod_desktop/desktop_api.o
dsk_usbd_remove                                   livedesk/leopard/mod_desktop/functions/built-in.o
                                                  livedesk/leopard/mod_desktop/desktop_api.o
dsk_usbh_is_working                               livedesk/leopard/mod_desktop/functions/built-in.o
                                                  livedesk/leopard/mod_desktop/desktop_api.o
dsk_volume_app_line_in                            livedesk/leopard/mod_desktop/functions/built-in.o
                                                  livedesk/leopard/mod_desktop/desktop_api.o
dsk_volume_dec                                    livedesk/leopard/mod_desktop/functions/built-in.o
                                                  livedesk/leopard/mod_desktop/desktop_api.o
dsk_volume_get                                    livedesk/leopard/mod_desktop/functions/built-in.o
                                                  livedesk/leopard/mod_desktop/desktop_api.o
dsk_volume_inc                                    livedesk/leopard/mod_desktop/functions/built-in.o
                                                  livedesk/leopard/mod_desktop/desktop_api.o
dsk_volume_is_on                                  livedesk/leopard/mod_desktop/functions/built-in.o
                                                  livedesk/leopard/mod_desktop/desktop_api.o
dsk_volume_off                                    livedesk/leopard/mod_desktop/functions/built-in.o
                                                  livedesk/leopard/mod_desktop/desktop_api.o
dsk_volume_on                                     livedesk/leopard/mod_desktop/functions/built-in.o
                                                  livedesk/leopard/mod_desktop/desktop_api.o
dsk_volume_set                                    livedesk/leopard/mod_desktop/functions/built-in.o
                                                  livedesk/leopard/mod_desktop/desktop_api.o
dup                                               ./elibrary/bin//libsyscall.a(syscall_fops.o)
dup2                                              ./elibrary/bin//libsyscall.a(syscall_fops.o)
eLIBs_DirEnt2Attr                                 ./elibrary/bin//libminic.a(elibs_stdio.o)
eLIBs_DirEnt2Name                                 ./elibrary/bin//libminic.a(elibs_stdio.o)
eLIBs_DirEnt2Size                                 ./elibrary/bin//libminic.a(elibs_stdio.o)
eLIBs_DirEnt2Time                                 ./elibrary/bin//libminic.a(elibs_stdio.o)
eLIBs_GetDate                                     ./elibrary/bin//libminic.a(elibs_stdio.o)
eLIBs_GetDevName                                  ./elibrary/bin//libminic.a(elibs_stdio.o)
eLIBs_GetDevParts                                 ./elibrary/bin//libminic.a(elibs_stdio.o)
eLIBs_GetDirATime                                 ./elibrary/bin//libminic.a(elibs_stdio.o)
eLIBs_GetDirCTime                                 ./elibrary/bin//libminic.a(elibs_stdio.o)
eLIBs_GetDirMTime                                 ./elibrary/bin//libminic.a(elibs_stdio.o)
eLIBs_GetDirSize                                  ./elibrary/bin//libminic.a(elibs_stdio.o)
eLIBs_GetDiskArray                                ./elibrary/bin//libminic.a(elibs_stdio.o)
eLIBs_GetFSAttribute                              ./elibrary/bin//libminic.a(elibs_stdio.o)
eLIBs_GetFSCharset                                ./elibrary/bin//libminic.a(elibs_stdio.o)
                                                  livedesk/leopard/mod_desktop/functions/built-in.o
eLIBs_GetFSName                                   ./elibrary/bin//libminic.a(elibs_stdio.o)
eLIBs_GetFSNameLen                                ./elibrary/bin//libminic.a(elibs_stdio.o)
eLIBs_GetFileATime                                ./elibrary/bin//libminic.a(elibs_stdio.o)
eLIBs_GetFileAttributes                           ./elibrary/bin//libminic.a(elibs_stdio.o)
eLIBs_GetFileCTime                                ./elibrary/bin//libminic.a(elibs_stdio.o)
eLIBs_GetFileMTime                                ./elibrary/bin//libminic.a(elibs_stdio.o)
eLIBs_GetFileSize                                 ./elibrary/bin//libminic.a(elibs_stdio.o)
eLIBs_GetNumFiles                                 ./elibrary/bin//libminic.a(elibs_stdio.o)
eLIBs_GetNumSubItems                              ./elibrary/bin//libminic.a(elibs_stdio.o)
eLIBs_GetPTName                                   ./elibrary/bin//libminic.a(elibs_stdio.o)
eLIBs_GetTime                                     ./elibrary/bin//libminic.a(elibs_stdio.o)
eLIBs_GetVolClustSize                             ./elibrary/bin//libminic.a(elibs_stdio.o)
eLIBs_GetVolFSpace                                ./elibrary/bin//libminic.a(elibs_stdio.o)
eLIBs_GetVolName                                  ./elibrary/bin//libminic.a(elibs_stdio.o)
eLIBs_GetVolNameCharset                           ./elibrary/bin//libminic.a(elibs_stdio.o)
eLIBs_GetVolNameLen                               ./elibrary/bin//libminic.a(elibs_stdio.o)
eLIBs_GetVolTSpace                                ./elibrary/bin//libminic.a(elibs_stdio.o)
eLIBs_IsPartFormated                              ./elibrary/bin//libminic.a(elibs_stdio.o)
                                                  livedesk/leopard/mod_desktop/functions/built-in.o
eLIBs_SetFileAttributes                           ./elibrary/bin//libminic.a(elibs_stdio.o)
eLIBs_atof                                        ./elibrary/bin//libminic.a(elibs_stdlib.o)
eLIBs_atoi                                        ./elibrary/bin//libminic.a(elibs_stdlib.o)
eLIBs_closedir                                    ./elibrary/bin//libminic.a(elibs_stdio.o)
eLIBs_disklock                                    ./elibrary/bin//libminic.a(elibs_stdio.o)
eLIBs_diskunlock                                  ./elibrary/bin//libminic.a(elibs_stdio.o)
eLIBs_fclose                                      ./elibrary/bin//libminic.a(elibs_stdio.o)
                                                  livedesk/leopard/mod_desktop/functions/built-in.o
eLIBs_fd2file                                     ./elibrary/bin//libminic.a(elibs_stdio.o)
eLIBs_feof                                        ./elibrary/bin//libminic.a(elibs_stdio.o)
eLIBs_ferrclr                                     ./elibrary/bin//libminic.a(elibs_stdio.o)
eLIBs_ferror                                      ./elibrary/bin//libminic.a(elibs_stdio.o)
eLIBs_fgetc                                       ./elibrary/bin//libminic.a(elibs_stdio.o)
eLIBs_fgets                                       ./elibrary/bin//libminic.a(elibs_stdio.o)
eLIBs_file2fd                                     ./elibrary/bin//libminic.a(elibs_stdio.o)
eLIBs_fioctrl                                     ./elibrary/bin//libminic.a(elibs_stdio.o)
                                                  livedesk/leopard/mod_desktop/functions/built-in.o
eLIBs_fllseek                                     ./elibrary/bin//libminic.a(elibs_stdio.o)
eLIBs_flltell                                     ./elibrary/bin//libminic.a(elibs_stdio.o)
eLIBs_fopen                                       ./elibrary/bin//libminic.a(elibs_stdio.o)
                                                  livedesk/leopard/mod_desktop/functions/built-in.o
eLIBs_format                                      ./elibrary/bin//libminic.a(elibs_stdio.o)
                                                  livedesk/leopard/mod_desktop/functions/built-in.o
eLIBs_fputc                                       ./elibrary/bin//libminic.a(elibs_stdio.o)
eLIBs_fputs                                       ./elibrary/bin//libminic.a(elibs_stdio.o)
eLIBs_fread                                       ./elibrary/bin//libminic.a(elibs_stdio.o)
                                                  livedesk/leopard/mod_desktop/functions/built-in.o
eLIBs_free                                        ./elibrary/bin//libminic.a(elibs_stdlib.o)
                                                  ./elibrary/bin//liblzma.a(LzmaLib.o)
eLIBs_fseek                                       ./elibrary/bin//libminic.a(elibs_stdio.o)
                                                  livedesk/leopard/mod_desktop/functions/built-in.o
eLIBs_fstat                                       ./elibrary/bin//libminic.a(elibs_stdio.o)
eLIBs_fsync                                       ./elibrary/bin//libminic.a(elibs_stdio.o)
                                                  livedesk/leopard/mod_desktop/functions/built-in.o
eLIBs_ftell                                       ./elibrary/bin//libminic.a(elibs_stdio.o)
eLIBs_ftruncate                                   ./elibrary/bin//libminic.a(elibs_stdio.o)
eLIBs_fwrite                                      ./elibrary/bin//libminic.a(elibs_stdio.o)
                                                  livedesk/leopard/mod_desktop/functions/built-in.o
eLIBs_int2str_dec                                 ./elibrary/bin//libminic.a(elibs_stdlib.o)
eLIBs_int2str_hex                                 ./elibrary/bin//libminic.a(elibs_stdlib.o)
eLIBs_isspace                                     ./elibrary/bin//libminic.a(elibs_stdlib.o)
eLIBs_language_enm2name                           livedesk/leopard/mod_desktop/util/built-in.o
eLIBs_language_name2enm                           livedesk/leopard/mod_desktop/util/built-in.o
eLIBs_malloc                                      ./elibrary/bin//libminic.a(elibs_stdlib.o)
                                                  ./elibrary/bin//liblzma.a(LzmaLib.o)
eLIBs_memclr                                      ./elibrary/bin//libminic.a(elibs_string.o)
eLIBs_memcmp                                      ./elibrary/bin//libminic.a(elibs_string.o)
                                                  livedesk/leopard/mod_desktop/functions/built-in.o
eLIBs_memcpy                                      ./elibrary/bin//libminic.a(elibs_string.o)
                                                  ./elibrary/bin//libresreader.a(LangDec.o)
                                                  ./elibrary/bin//libminic.a(elibs_stdio.o)
                                                  livedesk/leopard/mod_desktop/msg_srv/built-in.o
                                                  livedesk/leopard/mod_desktop/functions/built-in.o
eLIBs_memmove                                     ./elibrary/bin//libminic.a(elibs_string.o)
eLIBs_memset                                      ./elibrary/bin//libminic.a(elibs_string.o)
                                                  livedesk/leopard/mod_desktop/msg_srv/built-in.o
                                                  livedesk/leopard/mod_desktop/functions/built-in.o
eLIBs_mkdir                                       ./elibrary/bin//libminic.a(elibs_stdio.o)
eLIBs_open                                        ./elibrary/bin//libminic.a(elibs_stdio.o)
eLIBs_opendir                                     ./elibrary/bin//libminic.a(elibs_stdio.o)
eLIBs_printf                                      ./elibrary/bin//libminic.a(elibs_stdio.o)
                                                  livedesk/leopard/mod_desktop/msg_srv/built-in.o
                                                  livedesk/leopard/mod_desktop/functions/built-in.o
eLIBs_ramdom                                      ./elibrary/bin//libminic.a(elibs_stdlib.o)
eLIBs_readdir                                     ./elibrary/bin//libminic.a(elibs_stdio.o)
eLIBs_realloc                                     ./elibrary/bin//libminic.a(elibs_stdlib.o)
eLIBs_remove                                      ./elibrary/bin//libminic.a(elibs_stdio.o)
eLIBs_rename                                      ./elibrary/bin//libminic.a(elibs_stdio.o)
eLIBs_rewinddir                                   ./elibrary/bin//libminic.a(elibs_stdio.o)
eLIBs_rmdir                                       ./elibrary/bin//libminic.a(elibs_stdio.o)
eLIBs_scnprintf                                   ./elibrary/bin//libminic.a(elibs_sprintf.o)
eLIBs_setfs                                       ./elibrary/bin//libminic.a(elibs_stdio.o)
                                                  livedesk/leopard/mod_desktop/functions/built-in.o
eLIBs_snprintf                                    ./elibrary/bin//libminic.a(elibs_sprintf.o)
eLIBs_sprintf                                     ./elibrary/bin//libminic.a(elibs_sprintf.o)
eLIBs_sscanf                                      ./elibrary/bin//libminic.a(elibs_stdlib.o)
eLIBs_statfs                                      ./elibrary/bin//libminic.a(elibs_stdio.o)
eLIBs_strcat                                      ./elibrary/bin//libminic.a(elibs_string.o)
                                                  ./elibrary/bin//libminic.a(elibs_stdio.o)
eLIBs_strchr                                      ./elibrary/bin//libminic.a(elibs_string.o)
eLIBs_strchrlast                                  ./elibrary/bin//libminic.a(elibs_string.o)
eLIBs_strcmp                                      ./elibrary/bin//libminic.a(elibs_string.o)
                                                  ./elibrary/bin//libresreader.a(LangDec.o)
                                                  ./elibrary/bin//libminic.a(elibs_stdio.o)
                                                  livedesk/leopard/mod_desktop/util/built-in.o
eLIBs_strcpy                                      ./elibrary/bin//libminic.a(elibs_string.o)
                                                  ./elibrary/bin//libminic.a(elibs_stdio.o)
                                                  livedesk/leopard/mod_desktop/functions/built-in.o
eLIBs_stricmp                                     ./elibrary/bin//libminic.a(elibs_string.o)
                                                  ./elibrary/bin//libminic.a(elibs_stdio.o)
eLIBs_strlen                                      ./elibrary/bin//libminic.a(elibs_string.o)
                                                  ./elibrary/bin//libminic.a(elibs_stdio.o)
                                                  livedesk/leopard/mod_desktop/functions/built-in.o
eLIBs_strncat                                     ./elibrary/bin//libminic.a(elibs_string.o)
eLIBs_strnchr                                     ./elibrary/bin//libminic.a(elibs_string.o)
eLIBs_strncmp                                     ./elibrary/bin//libminic.a(elibs_string.o)
                                                  ./elibrary/bin//liblzma.a(az100.o)
eLIBs_strncpy                                     ./elibrary/bin//libminic.a(elibs_string.o)
                                                  ./elibrary/bin//libminic.a(elibs_stdio.o)
                                                  livedesk/leopard/mod_desktop/util/built-in.o
eLIBs_strnicmp                                    ./elibrary/bin//libminic.a(elibs_string.o)
eLIBs_strnlen                                     ./elibrary/bin//libminic.a(elibs_string.o)
eLIBs_strstr                                      ./elibrary/bin//libminic.a(elibs_string.o)
eLIBs_strtol                                      ./elibrary/bin//libminic.a(elibs_stdlib.o)
eLIBs_toupper                                     ./elibrary/bin//libminic.a(elibs_stdlib.o)
eLIBs_uint2str_dec                                ./elibrary/bin//libminic.a(elibs_stdlib.o)
eLIBs_vprintf                                     ./elibrary/bin//libminic.a(elibs_stdio.o)
eLIBs_vscnprintf                                  ./elibrary/bin//libminic.a(elibs_sprintf.o)
                                                  ./elibrary/bin//libminic.a(elibs_stdio.o)
eLIBs_vsnprintf                                   ./elibrary/bin//libminic.a(elibs_sprintf.o)
eLIBs_vsprintf                                    ./elibrary/bin//libminic.a(elibs_sprintf.o)
eq_to_bas                                         livedesk/leopard/mod_desktop/functions/built-in.o
eq_to_mid                                         livedesk/leopard/mod_desktop/functions/built-in.o
eq_to_tre                                         livedesk/leopard/mod_desktop/functions/built-in.o
esCFG_Exit_Ex                                     ./elibrary/bin//libsyscall.a(syscall.o)
esCFG_GetGPIOSecData                              ./elibrary/bin//libsyscall.a(syscall.o)
esCFG_GetGPIOSecData_Ex                           ./elibrary/bin//libsyscall.a(syscall.o)
esCFG_GetGPIOSecKeyCount                          ./elibrary/bin//libsyscall.a(syscall.o)
esCFG_GetGPIOSecKeyCount_Ex                       ./elibrary/bin//libsyscall.a(syscall.o)
esCFG_GetKeyValue                                 ./elibrary/bin//libsyscall.a(syscall.o)
                                                  livedesk/leopard/mod_desktop/functions/built-in.o
esCFG_GetKeyValue_Ex                              ./elibrary/bin//libsyscall.a(syscall.o)
esCFG_GetSecCount                                 ./elibrary/bin//libsyscall.a(syscall.o)
esCFG_GetSecCount_Ex                              ./elibrary/bin//libsyscall.a(syscall.o)
esCFG_GetSecKeyCount                              ./elibrary/bin//libsyscall.a(syscall.o)
esCFG_GetSecKeyCount_Ex                           ./elibrary/bin//libsyscall.a(syscall.o)
esCFG_Init_Ex                                     ./elibrary/bin//libsyscall.a(syscall.o)
esCHS_Char2Uni                                    ./elibrary/bin//libsyscall.a(syscall.o)
esCHS_GetChLowerTbl                               ./elibrary/bin//libsyscall.a(syscall.o)
esCHS_GetChUpperTbl                               ./elibrary/bin//libsyscall.a(syscall.o)
esCHS_Uni2Char                                    ./elibrary/bin//libsyscall.a(syscall.o)
esCLK_CloseMclk                                   ./elibrary/bin//libsyscall.a(syscall.o)
esCLK_GetMclkDiv                                  ./elibrary/bin//libsyscall.a(syscall.o)
esCLK_GetMclkSrc                                  ./elibrary/bin//libsyscall.a(syscall.o)
esCLK_GetRound_Rate                               ./elibrary/bin//libsyscall.a(syscall.o)
esCLK_GetSrcFreq                                  ./elibrary/bin//libsyscall.a(syscall.o)
esCLK_MclkAssert                                  ./elibrary/bin//libsyscall.a(syscall.o)
esCLK_MclkDeassert                                ./elibrary/bin//libsyscall.a(syscall.o)
esCLK_MclkGetRstStatus                            ./elibrary/bin//libsyscall.a(syscall.o)
esCLK_MclkOnOff                                   ./elibrary/bin//libsyscall.a(syscall.o)
esCLK_MclkRegCb                                   ./elibrary/bin//libsyscall.a(syscall.o)
esCLK_MclkReset                                   ./elibrary/bin//libsyscall.a(syscall.o)
esCLK_MclkUnregCb                                 ./elibrary/bin//libsyscall.a(syscall.o)
esCLK_ModInfo                                     ./elibrary/bin//libsyscall.a(syscall.o)
esCLK_OpenMclk                                    ./elibrary/bin//libsyscall.a(syscall.o)
esCLK_SetMclkDiv                                  ./elibrary/bin//libsyscall.a(syscall.o)
esCLK_SetMclkSrc                                  ./elibrary/bin//libsyscall.a(syscall.o)
esCLK_SetSrcFreq                                  ./elibrary/bin//libsyscall.a(syscall.o)
esCLK_SysInfo                                     ./elibrary/bin//libsyscall.a(syscall.o)
esDEV_Close                                       ./elibrary/bin//libsyscall.a(syscall.o)
esDEV_DevReg                                      ./elibrary/bin//libsyscall.a(syscall.o)
esDEV_DevUnreg                                    ./elibrary/bin//libsyscall.a(syscall.o)
esDEV_Insmod                                      ./elibrary/bin//libsyscall.a(syscall.o)
esDEV_Ioctl                                       ./elibrary/bin//libsyscall.a(syscall.o)
esDEV_Lock                                        ./elibrary/bin//libsyscall.a(syscall.o)
esDEV_Open                                        ./elibrary/bin//libsyscall.a(syscall.o)
esDEV_Plugin                                      ./elibrary/bin//libsyscall.a(syscall.o)
esDEV_Plugout                                     ./elibrary/bin//libsyscall.a(syscall.o)
esDEV_Read                                        ./elibrary/bin//libsyscall.a(syscall.o)
esDEV_Unimod                                      ./elibrary/bin//libsyscall.a(syscall.o)
esDEV_Unlock                                      ./elibrary/bin//libsyscall.a(syscall.o)
esDEV_Write                                       ./elibrary/bin//libsyscall.a(syscall.o)
esDMA_ChangeMode                                  ./elibrary/bin//libsyscall.a(syscall.o)
esDMA_DisableINT                                  ./elibrary/bin//libsyscall.a(syscall.o)
esDMA_EnableINT                                   ./elibrary/bin//libsyscall.a(syscall.o)
esDMA_Information                                 ./elibrary/bin//libsyscall.a(syscall.o)
esDMA_QueryDst                                    ./elibrary/bin//libsyscall.a(syscall.o)
esDMA_QueryRestCount                              ./elibrary/bin//libsyscall.a(syscall.o)
esDMA_QuerySrc                                    ./elibrary/bin//libsyscall.a(syscall.o)
esDMA_QueryStat                                   ./elibrary/bin//libsyscall.a(syscall.o)
esDMA_RegDmaHdler                                 ./elibrary/bin//libsyscall.a(syscall.o)
esDMA_Release                                     ./elibrary/bin//libsyscall.a(syscall.o)
esDMA_Request                                     ./elibrary/bin//libsyscall.a(syscall.o)
esDMA_Restart                                     ./elibrary/bin//libsyscall.a(syscall.o)
esDMA_Setting                                     ./elibrary/bin//libsyscall.a(syscall.o)
esDMA_Start                                       ./elibrary/bin//libsyscall.a(syscall.o)
esDMA_Stop                                        ./elibrary/bin//libsyscall.a(syscall.o)
esDMA_UnregDmaHdler                               ./elibrary/bin//libsyscall.a(syscall.o)
esEXEC_PCreate                                    ./elibrary/bin//libsyscall.a(syscall.o)
esEXEC_PDel                                       ./elibrary/bin//libsyscall.a(syscall.o)
esEXEC_PDelReq                                    ./elibrary/bin//libsyscall.a(syscall.o)
esEXEC_Run                                        ./elibrary/bin//libsyscall.a(syscall.o)
esFSYS_clearpartupdateflag                        ./elibrary/bin//libsyscall.a(syscall.o)
esFSYS_closedir                                   ./elibrary/bin//libsyscall.a(syscall.o)
                                                  ./elibrary/bin//libminic.a(elibs_stdio.o)
esFSYS_fclose                                     ./elibrary/bin//libsyscall.a(syscall.o)
                                                  ./elibrary/bin//libminic.a(elibs_stdio.o)
esFSYS_fd2file                                    ./elibrary/bin//libsyscall.a(syscall.o)
                                                  ./elibrary/bin//libminic.a(elibs_stdio.o)
esFSYS_ferrclr                                    ./elibrary/bin//libsyscall.a(syscall.o)
                                                  ./elibrary/bin//libminic.a(elibs_stdio.o)
esFSYS_ferror                                     ./elibrary/bin//libsyscall.a(syscall.o)
                                                  ./elibrary/bin//libminic.a(elibs_stdio.o)
esFSYS_file2fd                                    ./elibrary/bin//libsyscall.a(syscall.o)
                                                  ./elibrary/bin//libminic.a(elibs_stdio.o)
esFSYS_fioctrl                                    ./elibrary/bin//libsyscall.a(syscall.o)
                                                  ./elibrary/bin//libminic.a(elibs_stdio.o)
esFSYS_fopen                                      ./elibrary/bin//libsyscall.a(syscall.o)
                                                  ./elibrary/bin//libminic.a(elibs_stdio.o)
esFSYS_format                                     ./elibrary/bin//libsyscall.a(syscall.o)
                                                  ./elibrary/bin//libminic.a(elibs_stdio.o)
esFSYS_fread                                      ./elibrary/bin//libsyscall.a(syscall.o)
                                                  ./elibrary/bin//libminic.a(elibs_stdio.o)
esFSYS_fsdbg                                      ./elibrary/bin//libsyscall.a(syscall.o)
esFSYS_fseek                                      ./elibrary/bin//libsyscall.a(syscall.o)
                                                  ./elibrary/bin//libminic.a(elibs_stdio.o)
esFSYS_fseekex                                    ./elibrary/bin//libsyscall.a(syscall.o)
                                                  ./elibrary/bin//libminic.a(elibs_stdio.o)
esFSYS_fsreg                                      ./elibrary/bin//libsyscall.a(syscall.o)
esFSYS_fstat                                      ./elibrary/bin//libsyscall.a(syscall.o)
                                                  ./elibrary/bin//libminic.a(elibs_stdio.o)
esFSYS_fsunreg                                    ./elibrary/bin//libsyscall.a(syscall.o)
esFSYS_fsync                                      ./elibrary/bin//libsyscall.a(syscall.o)
                                                  ./elibrary/bin//libminic.a(elibs_stdio.o)
esFSYS_ftell                                      ./elibrary/bin//libsyscall.a(syscall.o)
                                                  ./elibrary/bin//libminic.a(elibs_stdio.o)
esFSYS_ftellex                                    ./elibrary/bin//libsyscall.a(syscall.o)
                                                  ./elibrary/bin//libminic.a(elibs_stdio.o)
esFSYS_ftruncate                                  ./elibrary/bin//libsyscall.a(syscall.o)
                                                  ./elibrary/bin//libminic.a(elibs_stdio.o)
esFSYS_fwrite                                     ./elibrary/bin//libsyscall.a(syscall.o)
                                                  ./elibrary/bin//libminic.a(elibs_stdio.o)
esFSYS_getfscharset                               ./elibrary/bin//libsyscall.a(syscall.o)
                                                  ./elibrary/bin//libminic.a(elibs_stdio.o)
esFSYS_mkdir                                      ./elibrary/bin//libsyscall.a(syscall.o)
                                                  ./elibrary/bin//libminic.a(elibs_stdio.o)
esFSYS_mntfs                                      ./elibrary/bin//libsyscall.a(syscall.o)
esFSYS_mntparts                                   ./elibrary/bin//libsyscall.a(syscall.o)
esFSYS_open                                       ./elibrary/bin//libsyscall.a(syscall.o)
                                                  ./elibrary/bin//libminic.a(elibs_stdio.o)
esFSYS_opendir                                    ./elibrary/bin//libsyscall.a(syscall.o)
                                                  ./elibrary/bin//libminic.a(elibs_stdio.o)
esFSYS_partfslck                                  ./elibrary/bin//libsyscall.a(syscall.o)
                                                  ./elibrary/bin//libminic.a(elibs_stdio.o)
esFSYS_partfsunlck                                ./elibrary/bin//libsyscall.a(syscall.o)
                                                  ./elibrary/bin//libminic.a(elibs_stdio.o)
esFSYS_pclose                                     ./elibrary/bin//libsyscall.a(syscall.o)
esFSYS_pdreg                                      ./elibrary/bin//libsyscall.a(syscall.o)
esFSYS_pdunreg                                    ./elibrary/bin//libsyscall.a(syscall.o)
esFSYS_perrclr                                    ./elibrary/bin//libsyscall.a(syscall.o)
esFSYS_perror                                     ./elibrary/bin//libsyscall.a(syscall.o)
esFSYS_pioctrl                                    ./elibrary/bin//libsyscall.a(syscall.o)
esFSYS_popen                                      ./elibrary/bin//libsyscall.a(syscall.o)
esFSYS_pread                                      ./elibrary/bin//libsyscall.a(syscall.o)
esFSYS_premove                                    ./elibrary/bin//libsyscall.a(syscall.o)
esFSYS_prename                                    ./elibrary/bin//libsyscall.a(syscall.o)
esFSYS_pwrite                                     ./elibrary/bin//libsyscall.a(syscall.o)
esFSYS_querypartupdateflag                        ./elibrary/bin//libsyscall.a(syscall.o)
esFSYS_readdir                                    ./elibrary/bin//libsyscall.a(syscall.o)
                                                  ./elibrary/bin//libminic.a(elibs_stdio.o)
esFSYS_remove                                     ./elibrary/bin//libsyscall.a(syscall.o)
                                                  ./elibrary/bin//libminic.a(elibs_stdio.o)
esFSYS_rename                                     ./elibrary/bin//libsyscall.a(syscall.o)
                                                  ./elibrary/bin//libminic.a(elibs_stdio.o)
esFSYS_rewinddir                                  ./elibrary/bin//libsyscall.a(syscall.o)
                                                  ./elibrary/bin//libminic.a(elibs_stdio.o)
esFSYS_rmdir                                      ./elibrary/bin//libsyscall.a(syscall.o)
                                                  ./elibrary/bin//libminic.a(elibs_stdio.o)
esFSYS_setfs                                      ./elibrary/bin//libsyscall.a(syscall.o)
                                                  ./elibrary/bin//libminic.a(elibs_stdio.o)
esFSYS_statfs                                     ./elibrary/bin//libsyscall.a(syscall.o)
                                                  ./elibrary/bin//libminic.a(elibs_stdio.o)
esFSYS_statpt                                     ./elibrary/bin//libsyscall.a(syscall.o)
                                                  ./elibrary/bin//libminic.a(elibs_stdio.o)
esFSYS_umntfs                                     ./elibrary/bin//libsyscall.a(syscall.o)
esFSYS_umntparts                                  ./elibrary/bin//libsyscall.a(syscall.o)
esHID_SendMsg                                     ./elibrary/bin//libsyscall.a(syscall.o)
esHID_hiddevreg                                   ./elibrary/bin//libsyscall.a(syscall.o)
esHID_hiddevunreg                                 ./elibrary/bin//libsyscall.a(syscall.o)
esINPUT_GetLdevID                                 ./elibrary/bin//libsyscall.a(syscall.o)
                                                  livedesk/leopard/mod_desktop/msg_srv/built-in.o
esINPUT_LdevCtl                                   ./elibrary/bin//libsyscall.a(syscall.o)
                                                  livedesk/leopard/mod_desktop/msg_srv/built-in.o
esINPUT_LdevFeedback                              ./elibrary/bin//libsyscall.a(syscall.o)
esINPUT_LdevGrab                                  ./elibrary/bin//libsyscall.a(syscall.o)
                                                  livedesk/leopard/mod_desktop/msg_srv/built-in.o
esINPUT_LdevRelease                               ./elibrary/bin//libsyscall.a(syscall.o)
                                                  livedesk/leopard/mod_desktop/msg_srv/built-in.o
esINPUT_RegDev                                    ./elibrary/bin//libsyscall.a(syscall.o)
esINPUT_SendEvent                                 ./elibrary/bin//libsyscall.a(syscall.o)
esINPUT_UnregDev                                  ./elibrary/bin//libsyscall.a(syscall.o)
esINT_DisableINT                                  ./elibrary/bin//libsyscall.a(syscall.o)
esINT_EnableINT                                   ./elibrary/bin//libsyscall.a(syscall.o)
esINT_InsFIR                                      ./elibrary/bin//libsyscall.a(syscall.o)
esINT_InsISR                                      ./elibrary/bin//libsyscall.a(syscall.o)
esINT_SetIRQPrio                                  ./elibrary/bin//libsyscall.a(syscall.o)
esINT_UniFIR                                      ./elibrary/bin//libsyscall.a(syscall.o)
esINT_UniISR                                      ./elibrary/bin//libsyscall.a(syscall.o)
esKRNL_CallBack                                   ./elibrary/bin//libsyscall.a(syscall.o)
                                                  livedesk/leopard/mod_desktop/msg_srv/built-in.o
esKRNL_DumpStack                                  ./elibrary/bin//libsyscall.a(syscall.o)
esKRNL_FlagAccept                                 ./elibrary/bin//libsyscall.a(syscall.o)
esKRNL_FlagCreate                                 ./elibrary/bin//libsyscall.a(syscall.o)
esKRNL_FlagDel                                    ./elibrary/bin//libsyscall.a(syscall.o)
esKRNL_FlagNameGet                                ./elibrary/bin//libsyscall.a(syscall.o)
esKRNL_FlagNameSet                                ./elibrary/bin//libsyscall.a(syscall.o)
esKRNL_FlagPend                                   ./elibrary/bin//libsyscall.a(syscall.o)
esKRNL_FlagPendGetFlagsRdy                        ./elibrary/bin//libsyscall.a(syscall.o)
esKRNL_FlagPost                                   ./elibrary/bin//libsyscall.a(syscall.o)
esKRNL_FlagQuery                                  ./elibrary/bin//libsyscall.a(syscall.o)
esKRNL_GetCallBack                                ./elibrary/bin//libsyscall.a(syscall.o)
esKRNL_GetTIDCur                                  ./elibrary/bin//libsyscall.a(syscall.o)
esKRNL_InterruptDisable                           ./elibrary/bin//libsyscall.a(syscall.o)
esKRNL_InterruptEnable                            ./elibrary/bin//libsyscall.a(syscall.o)
esKRNL_Ioctrl                                     ./elibrary/bin//libsyscall.a(syscall.o)
esKRNL_MboxAccept                                 ./elibrary/bin//libsyscall.a(syscall.o)
esKRNL_MboxCreate                                 ./elibrary/bin//libsyscall.a(syscall.o)
esKRNL_MboxDel                                    ./elibrary/bin//libsyscall.a(syscall.o)
esKRNL_MboxPend                                   ./elibrary/bin//libsyscall.a(syscall.o)
esKRNL_MboxPost                                   ./elibrary/bin//libsyscall.a(syscall.o)
esKRNL_MboxPostOpt                                ./elibrary/bin//libsyscall.a(syscall.o)
esKRNL_MboxQuery                                  ./elibrary/bin//libsyscall.a(syscall.o)
esKRNL_MemLeakChk                                 ./elibrary/bin//libsyscall.a(syscall.o)
esKRNL_MutexCreate                                ./elibrary/bin//libsyscall.a(syscall.o)
esKRNL_MutexDel                                   ./elibrary/bin//libsyscall.a(syscall.o)
esKRNL_MutexPend                                  ./elibrary/bin//libsyscall.a(syscall.o)
esKRNL_MutexPost                                  ./elibrary/bin//libsyscall.a(syscall.o)
esKRNL_QAccept                                    ./elibrary/bin//libsyscall.a(syscall.o)
                                                  livedesk/leopard/mod_desktop/msg_srv/built-in.o
esKRNL_QCreate                                    ./elibrary/bin//libsyscall.a(syscall.o)
                                                  livedesk/leopard/mod_desktop/msg_srv/built-in.o
esKRNL_QDel                                       ./elibrary/bin//libsyscall.a(syscall.o)
                                                  livedesk/leopard/mod_desktop/msg_srv/built-in.o
esKRNL_QFlush                                     ./elibrary/bin//libsyscall.a(syscall.o)
esKRNL_QPend                                      ./elibrary/bin//libsyscall.a(syscall.o)
esKRNL_QPost                                      ./elibrary/bin//libsyscall.a(syscall.o)
                                                  livedesk/leopard/mod_desktop/msg_srv/built-in.o
esKRNL_QPostFront                                 ./elibrary/bin//libsyscall.a(syscall.o)
esKRNL_QPostOpt                                   ./elibrary/bin//libsyscall.a(syscall.o)
esKRNL_QQuery                                     ./elibrary/bin//libsyscall.a(syscall.o)
esKRNL_SchedLock                                  ./elibrary/bin//libsyscall.a(syscall.o)
esKRNL_SchedUnlock                                ./elibrary/bin//libsyscall.a(syscall.o)
esKRNL_SemAccept                                  ./elibrary/bin//libsyscall.a(syscall.o)
esKRNL_SemCreate                                  ./elibrary/bin//libsyscall.a(syscall.o)
                                                  livedesk/leopard/mod_desktop/msg_srv/built-in.o
                                                  livedesk/leopard/mod_desktop/functions/built-in.o
esKRNL_SemDel                                     ./elibrary/bin//libsyscall.a(syscall.o)
                                                  livedesk/leopard/mod_desktop/msg_srv/built-in.o
                                                  livedesk/leopard/mod_desktop/functions/built-in.o
esKRNL_SemPend                                    ./elibrary/bin//libsyscall.a(syscall.o)
                                                  livedesk/leopard/mod_desktop/msg_srv/built-in.o
                                                  livedesk/leopard/mod_desktop/functions/built-in.o
esKRNL_SemPost                                    ./elibrary/bin//libsyscall.a(syscall.o)
                                                  livedesk/leopard/mod_desktop/msg_srv/built-in.o
                                                  livedesk/leopard/mod_desktop/functions/built-in.o
esKRNL_SemQuery                                   ./elibrary/bin//libsyscall.a(syscall.o)
                                                  livedesk/leopard/mod_desktop/functions/built-in.o
esKRNL_SemSet                                     ./elibrary/bin//libsyscall.a(syscall.o)
esKRNL_SktAccept                                  ./elibrary/bin//libsyscall.a(syscall.o)
esKRNL_SktCreate                                  ./elibrary/bin//libsyscall.a(syscall.o)
esKRNL_SktDel                                     ./elibrary/bin//libsyscall.a(syscall.o)
esKRNL_SktPend                                    ./elibrary/bin//libsyscall.a(syscall.o)
esKRNL_SktPost                                    ./elibrary/bin//libsyscall.a(syscall.o)
esKRNL_TCreate                                    ./elibrary/bin//libsyscall.a(syscall.o)
                                                  livedesk/leopard/mod_desktop/msg_srv/built-in.o
                                                  livedesk/leopard/mod_desktop/functions/built-in.o
esKRNL_TDel                                       ./elibrary/bin//libsyscall.a(syscall.o)
                                                  livedesk/leopard/mod_desktop/msg_srv/built-in.o
                                                  livedesk/leopard/mod_desktop/functions/built-in.o
esKRNL_TDelReq                                    ./elibrary/bin//libsyscall.a(syscall.o)
                                                  livedesk/leopard/mod_desktop/msg_srv/built-in.o
                                                  livedesk/leopard/mod_desktop/functions/built-in.o
esKRNL_TaskNameSet                                ./elibrary/bin//libsyscall.a(syscall.o)
                                                  livedesk/leopard/mod_desktop/msg_srv/built-in.o
esKRNL_TaskPrefEn                                 ./elibrary/bin//libsyscall.a(syscall.o)
esKRNL_TaskQuery                                  ./elibrary/bin//libsyscall.a(syscall.o)
esKRNL_TaskResume                                 ./elibrary/bin//libsyscall.a(syscall.o)
esKRNL_TaskSuspend                                ./elibrary/bin//libsyscall.a(syscall.o)
esKRNL_Time                                       ./elibrary/bin//libsyscall.a(syscall.o)
esKRNL_TimeDly                                    ./elibrary/bin//libsyscall.a(syscall.o)
                                                  livedesk/leopard/mod_desktop/msg_srv/built-in.o
                                                  livedesk/leopard/mod_desktop/functions/built-in.o
esKRNL_TimeDlyResume                              ./elibrary/bin//libsyscall.a(syscall.o)
esKRNL_TimeGet                                    ./elibrary/bin//libsyscall.a(syscall.o)
esKRNL_TimeSet                                    ./elibrary/bin//libsyscall.a(syscall.o)
esKRNL_TmrCreate                                  ./elibrary/bin//libsyscall.a(syscall.o)
esKRNL_TmrDel                                     ./elibrary/bin//libsyscall.a(syscall.o)
esKRNL_TmrError                                   ./elibrary/bin//libsyscall.a(syscall.o)
esKRNL_TmrRemainGet                               ./elibrary/bin//libsyscall.a(syscall.o)
esKRNL_TmrStart                                   ./elibrary/bin//libsyscall.a(syscall.o)
esKRNL_TmrStateGet                                ./elibrary/bin//libsyscall.a(syscall.o)
esKRNL_TmrStop                                    ./elibrary/bin//libsyscall.a(syscall.o)
esKRNL_Version                                    ./elibrary/bin//libsyscall.a(syscall.o)
esKSRV_ClearWatchDog                              ./elibrary/bin//libsyscall.a(syscall.o)
esKSRV_DisableWatchDog                            ./elibrary/bin//libsyscall.a(syscall.o)
esKSRV_DisableWatchDogSrv                         ./elibrary/bin//libsyscall.a(syscall.o)
esKSRV_EnableWatchDog                             ./elibrary/bin//libsyscall.a(syscall.o)
esKSRV_EnableWatchDogSrv                          ./elibrary/bin//libsyscall.a(syscall.o)
esKSRV_GetAddPara                                 ./elibrary/bin//libsyscall.a(syscall.o)
esKSRV_GetDramCfgPara                             ./elibrary/bin//libsyscall.a(syscall.o)
esKSRV_GetHighMsg                                 ./elibrary/bin//libsyscall.a(syscall.o)
esKSRV_GetLowMsg                                  ./elibrary/bin//libsyscall.a(syscall.o)
esKSRV_GetMsg                                     ./elibrary/bin//libsyscall.a(syscall.o)
                                                  livedesk/leopard/mod_desktop/msg_srv/built-in.o
esKSRV_GetPara                                    ./elibrary/bin//libsyscall.a(syscall.o)
esKSRV_GetSocID                                   ./elibrary/bin//libsyscall.a(syscall.o)
esKSRV_GetTargetPara                              ./elibrary/bin//libsyscall.a(syscall.o)
esKSRV_GetVersion                                 ./elibrary/bin//libsyscall.a(syscall.o)
esKSRV_Get_Display_Hld                            ./elibrary/bin//libsyscall.a(syscall.o)
                                                  livedesk/leopard/mod_desktop/functions/built-in.o
esKSRV_Get_Mixture_Hld                            ./elibrary/bin//libsyscall.a(syscall.o)
esKSRV_Get_Raw_Decoder_Hld                        ./elibrary/bin//libsyscall.a(syscall.o)
esKSRV_PowerOff                                   ./elibrary/bin//libsyscall.a(syscall.o)
esKSRV_Random                                     ./elibrary/bin//libsyscall.a(syscall.o)
                                                  ./elibrary/bin//libminic.a(elibs_stdlib.o)
esKSRV_Reset                                      ./elibrary/bin//libsyscall.a(syscall.o)
esKSRV_Save_Display_Hld                           ./elibrary/bin//libsyscall.a(syscall.o)
esKSRV_Save_Mixture_Hld                           ./elibrary/bin//libsyscall.a(syscall.o)
esKSRV_Save_Raw_Decoder_Hld                       ./elibrary/bin//libsyscall.a(syscall.o)
esKSRV_SendMsg                                    ./elibrary/bin//libsyscall.a(syscall.o)
esKSRV_SendMsgEx                                  ./elibrary/bin//libsyscall.a(syscall.o)
esKSRV_SysInfo                                    ./elibrary/bin//libsyscall.a(syscall.o)
esKSRV_memcpy                                     ./elibrary/bin//libsyscall.a(syscall.o)
esKSRV_memset                                     ./elibrary/bin//libsyscall.a(syscall.o)
esMEMS_Balloc                                     ./elibrary/bin//libsyscall.a(syscall.o)
                                                  livedesk/leopard/mod_desktop/functions/built-in.o
esMEMS_Bfree                                      ./elibrary/bin//libsyscall.a(syscall.o)
                                                  livedesk/leopard/mod_desktop/functions/built-in.o
esMEMS_Calloc                                     ./elibrary/bin//libsyscall.a(syscall.o)
esMEMS_CleanDCache                                ./elibrary/bin//libsyscall.a(syscall.o)
esMEMS_CleanDCacheRegion                          ./elibrary/bin//libsyscall.a(syscall.o)
esMEMS_CleanFlushCache                            ./elibrary/bin//libsyscall.a(syscall.o)
esMEMS_CleanFlushCacheRegion                      ./elibrary/bin//libsyscall.a(syscall.o)
esMEMS_CleanFlushDCache                           ./elibrary/bin//libsyscall.a(syscall.o)
esMEMS_CleanFlushDCacheRegion                     ./elibrary/bin//libsyscall.a(syscall.o)
esMEMS_CleanInvalidateCacheAll                    ./elibrary/bin//libsyscall.a(syscall.o)
esMEMS_FlushCache                                 ./elibrary/bin//libsyscall.a(syscall.o)
esMEMS_FlushCacheRegion                           ./elibrary/bin//libsyscall.a(syscall.o)
esMEMS_FlushDCache                                ./elibrary/bin//libsyscall.a(syscall.o)
esMEMS_FlushDCacheRegion                          ./elibrary/bin//libsyscall.a(syscall.o)
esMEMS_FlushICache                                ./elibrary/bin//libsyscall.a(syscall.o)
esMEMS_FlushICacheRegion                          ./elibrary/bin//libsyscall.a(syscall.o)
esMEMS_FreeMemSize                                ./elibrary/bin//libsyscall.a(syscall.o)
esMEMS_GetIoVAByPA                                ./elibrary/bin//libsyscall.a(syscall.o)
esMEMS_HeapCreate                                 ./elibrary/bin//libsyscall.a(syscall.o)
esMEMS_HeapDel                                    ./elibrary/bin//libsyscall.a(syscall.o)
esMEMS_Info                                       ./elibrary/bin//libsyscall.a(syscall.o)
esMEMS_Malloc                                     ./elibrary/bin//libsyscall.a(syscall.o)
                                                  ./elibrary/bin//libminic.a(elibs_stdlib.o)
                                                  ./elibrary/bin//libresreader.a(themedec.o)
                                                  ./elibrary/bin//libresreader.a(LangDec.o)
                                                  ./elibrary/bin//libminic.a(elibs_stdio.o)
                                                  livedesk/leopard/mod_desktop/util/built-in.o
                                                  livedesk/leopard/mod_desktop/functions/built-in.o
esMEMS_Mfree                                      ./elibrary/bin//libsyscall.a(syscall.o)
                                                  ./elibrary/bin//libminic.a(elibs_stdlib.o)
                                                  ./elibrary/bin//libresreader.a(themedec.o)
                                                  ./elibrary/bin//libresreader.a(LangDec.o)
                                                  ./elibrary/bin//libminic.a(elibs_stdio.o)
                                                  livedesk/leopard/mod_desktop/util/built-in.o
                                                  livedesk/leopard/mod_desktop/functions/built-in.o
esMEMS_Palloc                                     ./elibrary/bin//libsyscall.a(syscall.o)
esMEMS_Pfree                                      ./elibrary/bin//libsyscall.a(syscall.o)
esMEMS_Realloc                                    ./elibrary/bin//libsyscall.a(syscall.o)
                                                  ./elibrary/bin//libminic.a(elibs_stdlib.o)
                                                  livedesk/leopard/mod_desktop/util/built-in.o
esMEMS_TotalMemSize                               ./elibrary/bin//libsyscall.a(syscall.o)
esMEMS_VA2PA                                      ./elibrary/bin//libsyscall.a(syscall.o)
esMEMS_VMCreate                                   ./elibrary/bin//libsyscall.a(syscall.o)
esMEMS_VMDelete                                   ./elibrary/bin//libsyscall.a(syscall.o)
esMEMS_VMalloc                                    ./elibrary/bin//libsyscall.a(syscall.o)
esMEMS_VMfree                                     ./elibrary/bin//libsyscall.a(syscall.o)
esMEM_BWDisable                                   ./elibrary/bin//libsyscall.a(syscall.o)
esMEM_BWEnable                                    ./elibrary/bin//libsyscall.a(syscall.o)
esMEM_BWGet                                       ./elibrary/bin//libsyscall.a(syscall.o)
esMEM_DramSuspend                                 ./elibrary/bin//libsyscall.a(syscall.o)
esMEM_DramWakeup                                  ./elibrary/bin//libsyscall.a(syscall.o)
esMEM_MasterGet                                   ./elibrary/bin//libsyscall.a(syscall.o)
esMEM_MasterSet                                   ./elibrary/bin//libsyscall.a(syscall.o)
esMEM_RegDramAccess                               ./elibrary/bin//libsyscall.a(syscall.o)
esMEM_ReleaseDramUsrMode                          ./elibrary/bin//libsyscall.a(syscall.o)
esMEM_RequestDramUsrMode                          ./elibrary/bin//libsyscall.a(syscall.o)
esMEM_SramRelBlk                                  ./elibrary/bin//libsyscall.a(syscall.o)
esMEM_SramReqBlk                                  ./elibrary/bin//libsyscall.a(syscall.o)
esMEM_SramSwitchBlk                               ./elibrary/bin//libsyscall.a(syscall.o)
esMEM_UnRegDramAccess                             ./elibrary/bin//libsyscall.a(syscall.o)
esMODS_MClose                                     ./elibrary/bin//libsyscall.a(syscall.o)
                                                  livedesk/leopard/mod_desktop/mod_desktop.o
esMODS_MInstall                                   ./elibrary/bin//libsyscall.a(syscall.o)
                                                  livedesk/leopard/mod_desktop/mod_desktop.o
esMODS_MIoctrl                                    ./elibrary/bin//libsyscall.a(syscall.o)
                                                  livedesk/leopard/mod_desktop/functions/built-in.o
esMODS_MOpen                                      ./elibrary/bin//libsyscall.a(syscall.o)
                                                  livedesk/leopard/mod_desktop/mod_desktop.o
esMODS_MRead                                      ./elibrary/bin//libsyscall.a(syscall.o)
esMODS_MUninstall                                 ./elibrary/bin//libsyscall.a(syscall.o)
                                                  livedesk/leopard/mod_desktop/mod_desktop.o
esMODS_MWrite                                     ./elibrary/bin//libsyscall.a(syscall.o)
esMSTUB_GetFuncEntry                              ./elibrary/bin//libsyscall.a(syscall.o)
esMSTUB_RegFuncTbl                                ./elibrary/bin//libsyscall.a(syscall.o)
esMSTUB_UnRegFuncTbl                              ./elibrary/bin//libsyscall.a(syscall.o)
esPINS_ClearPending                               ./elibrary/bin//libsyscall.a(syscall.o)
esPINS_DisbaleInt                                 ./elibrary/bin//libsyscall.a(syscall.o)
esPINS_EnbaleInt                                  ./elibrary/bin//libsyscall.a(syscall.o)
esPINS_GetPinGrpStat                              ./elibrary/bin//libsyscall.a(syscall.o)
esPINS_GetPinStat                                 ./elibrary/bin//libsyscall.a(syscall.o)
esPINS_PinGrpRel                                  ./elibrary/bin//libsyscall.a(syscall.o)
esPINS_PinGrpReq                                  ./elibrary/bin//libsyscall.a(syscall.o)
                                                  livedesk/leopard/mod_desktop/functions/built-in.o
esPINS_QueryInt                                   ./elibrary/bin//libsyscall.a(syscall.o)
esPINS_ReadPinData                                ./elibrary/bin//libsyscall.a(syscall.o)
                                                  livedesk/leopard/mod_desktop/functions/built-in.o
esPINS_RegIntHdler                                ./elibrary/bin//libsyscall.a(syscall.o)
esPINS_SetIntMode                                 ./elibrary/bin//libsyscall.a(syscall.o)
esPINS_SetPinDrive                                ./elibrary/bin//libsyscall.a(syscall.o)
esPINS_SetPinIO                                   ./elibrary/bin//libsyscall.a(syscall.o)
esPINS_SetPinPull                                 ./elibrary/bin//libsyscall.a(syscall.o)
esPINS_SetPinStat                                 ./elibrary/bin//libsyscall.a(syscall.o)
esPINS_UnregIntHdler                              ./elibrary/bin//libsyscall.a(syscall.o)
esPINS_WritePinData                               ./elibrary/bin//libsyscall.a(syscall.o)
                                                  livedesk/leopard/mod_desktop/functions/built-in.o
esPWRMAN_EnterStandby                             ./elibrary/bin//libsyscall.a(syscall.o)
esPWRMAN_GetStandbyPara                           ./elibrary/bin//libsyscall.a(syscall.o)
esPWRMAN_LockCpuFreq                              ./elibrary/bin//libsyscall.a(syscall.o)
esPWRMAN_RegDevice                                ./elibrary/bin//libsyscall.a(syscall.o)
esPWRMAN_RelPwrmanMode                            ./elibrary/bin//libsyscall.a(syscall.o)
esPWRMAN_ReqPwrmanMode                            ./elibrary/bin//libsyscall.a(syscall.o)
esPWRMAN_SetStandbyMode                           ./elibrary/bin//libsyscall.a(syscall.o)
esPWRMAN_UnlockCpuFreq                            ./elibrary/bin//libsyscall.a(syscall.o)
esPWRMAN_UnregDevice                              ./elibrary/bin//libsyscall.a(syscall.o)
esPWRMAN_UsrEventNotify                           ./elibrary/bin//libsyscall.a(syscall.o)
esRESM_RClose                                     ./elibrary/bin//libsyscall.a(syscall.o)
                                                  ./elibrary/bin//libresreader.a(themedec.o)
                                                  ./elibrary/bin//libresreader.a(LangDec.o)
esRESM_ROpen                                      ./elibrary/bin//libsyscall.a(syscall.o)
                                                  ./elibrary/bin//libresreader.a(themedec.o)
                                                  ./elibrary/bin//libresreader.a(LangDec.o)
esRESM_RRead                                      ./elibrary/bin//libsyscall.a(syscall.o)
                                                  ./elibrary/bin//libresreader.a(themedec.o)
                                                  ./elibrary/bin//libresreader.a(LangDec.o)
esRESM_RSeek                                      ./elibrary/bin//libsyscall.a(syscall.o)
                                                  ./elibrary/bin//libresreader.a(themedec.o)
                                                  ./elibrary/bin//libresreader.a(LangDec.o)
esSIOS_getchar                                    ./elibrary/bin//libsyscall.a(syscall.o)
esSIOS_gets                                       ./elibrary/bin//libsyscall.a(syscall.o)
esSIOS_putarg                                     ./elibrary/bin//libsyscall.a(syscall.o)
esSIOS_putstr                                     ./elibrary/bin//libsyscall.a(syscall.o)
                                                  ./elibrary/bin//libminic.a(elibs_stdio.o)
esSIOS_setbaud                                    ./elibrary/bin//libsyscall.a(syscall.o)
esSVC_RegCloseNode                                ./elibrary/bin//libsyscall.a(syscall.o)
esSVC_RegClrError                                 ./elibrary/bin//libsyscall.a(syscall.o)
esSVC_RegCreateKey                                ./elibrary/bin//libsyscall.a(syscall.o)
esSVC_RegCreatePath                               ./elibrary/bin//libsyscall.a(syscall.o)
esSVC_RegCreateSet                                ./elibrary/bin//libsyscall.a(syscall.o)
esSVC_RegDeleteKey                                ./elibrary/bin//libsyscall.a(syscall.o)
esSVC_RegDeleteNode                               ./elibrary/bin//libsyscall.a(syscall.o)
esSVC_RegDeletePath                               ./elibrary/bin//libsyscall.a(syscall.o)
esSVC_RegDeleteSet                                ./elibrary/bin//libsyscall.a(syscall.o)
esSVC_RegGetError                                 ./elibrary/bin//libsyscall.a(syscall.o)
esSVC_RegGetFirstKey                              ./elibrary/bin//libsyscall.a(syscall.o)
esSVC_RegGetFirstSet                              ./elibrary/bin//libsyscall.a(syscall.o)
esSVC_RegGetKeyCount                              ./elibrary/bin//libsyscall.a(syscall.o)
esSVC_RegGetKeyValue                              ./elibrary/bin//libsyscall.a(syscall.o)
esSVC_RegGetNextKey                               ./elibrary/bin//libsyscall.a(syscall.o)
esSVC_RegGetNextSet                               ./elibrary/bin//libsyscall.a(syscall.o)
esSVC_RegGetRootPath                              ./elibrary/bin//libsyscall.a(syscall.o)
esSVC_RegGetSetCount                              ./elibrary/bin//libsyscall.a(syscall.o)
esSVC_RegIni2Reg                                  ./elibrary/bin//libsyscall.a(syscall.o)
esSVC_RegOpenNode                                 ./elibrary/bin//libsyscall.a(syscall.o)
esSVC_RegReg2Ini                                  ./elibrary/bin//libsyscall.a(syscall.o)
esSVC_RegSetKeyValue                              ./elibrary/bin//libsyscall.a(syscall.o)
esSVC_RegSetRootPath                              ./elibrary/bin//libsyscall.a(syscall.o)
esSVC_ResourceRel                                 ./elibrary/bin//libsyscall.a(syscall.o)
esSVC_ResourceReq                                 ./elibrary/bin//libsyscall.a(syscall.o)
esTIME_ContiCntr                                  ./elibrary/bin//libsyscall.a(syscall.o)
esTIME_GetDate                                    ./elibrary/bin//libsyscall.a(syscall.o)
                                                  ./elibrary/bin//libminic.a(elibs_stdio.o)
esTIME_GetTime                                    ./elibrary/bin//libsyscall.a(syscall.o)
                                                  ./elibrary/bin//libminic.a(elibs_stdio.o)
esTIME_PauseCntr                                  ./elibrary/bin//libsyscall.a(syscall.o)
esTIME_QuerryAlarm                                ./elibrary/bin//libsyscall.a(syscall.o)
esTIME_QuerryCntr                                 ./elibrary/bin//libsyscall.a(syscall.o)
esTIME_QuerryCntrStat                             ./elibrary/bin//libsyscall.a(syscall.o)
esTIME_QuerryTimer                                ./elibrary/bin//libsyscall.a(syscall.o)
esTIME_ReleaseAlarm                               ./elibrary/bin//libsyscall.a(syscall.o)
esTIME_ReleaseCntr                                ./elibrary/bin//libsyscall.a(syscall.o)
esTIME_ReleaseTimer                               ./elibrary/bin//libsyscall.a(syscall.o)
esTIME_RequestAlarm                               ./elibrary/bin//libsyscall.a(syscall.o)
esTIME_RequestCntr                                ./elibrary/bin//libsyscall.a(syscall.o)
esTIME_RequestTimer                               ./elibrary/bin//libsyscall.a(syscall.o)
esTIME_SetCntrPrescale                            ./elibrary/bin//libsyscall.a(syscall.o)
esTIME_SetCntrValue                               ./elibrary/bin//libsyscall.a(syscall.o)
esTIME_SetDate                                    ./elibrary/bin//libsyscall.a(syscall.o)
esTIME_SetTime                                    ./elibrary/bin//libsyscall.a(syscall.o)
esTIME_StartAlarm                                 ./elibrary/bin//libsyscall.a(syscall.o)
esTIME_StartCntr                                  ./elibrary/bin//libsyscall.a(syscall.o)
esTIME_StartTimer                                 ./elibrary/bin//libsyscall.a(syscall.o)
esTIME_StopAlarm                                  ./elibrary/bin//libsyscall.a(syscall.o)
esTIME_StopCntr                                   ./elibrary/bin//libsyscall.a(syscall.o)
esTIME_StopTimer                                  ./elibrary/bin//libsyscall.a(syscall.o)
fclose                                            /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/../../../../riscv64-unknown-elf/lib/rv64imafdcxtheadc/lp64d/libc.a(lib_a-fclose.o)
                                                  livedesk/leopard/mod_desktop/functions/built-in.o
fflush                                            /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/../../../../riscv64-unknown-elf/lib/rv64imafdcxtheadc/lp64d/libc.a(lib_a-fflush.o)
                                                  /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/../../../../riscv64-unknown-elf/lib/rv64imafdcxtheadc/lp64d/libc.a(lib_a-refill.o)
fioctrl                                           ./elibrary/bin//libsyscall.a(syscall_fops.o)
fopen                                             /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/../../../../riscv64-unknown-elf/lib/rv64imafdcxtheadc/lp64d/libc.a(lib_a-fopen.o)
                                                  livedesk/leopard/mod_desktop/functions/built-in.o
fseek                                             /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/../../../../riscv64-unknown-elf/lib/rv64imafdcxtheadc/lp64d/libc.a(lib_a-fseek.o)
fseeko                                            /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/../../../../riscv64-unknown-elf/lib/rv64imafdcxtheadc/lp64d/libc.a(lib_a-fseeko.o)
fsync                                             ./elibrary/bin//libsyscall.a(syscall_fops.o)
ftruncate                                         ./elibrary/bin//libsyscall.a(syscall_fops.o)
fwrite                                            /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/../../../../riscv64-unknown-elf/lib/rv64imafdcxtheadc/lp64d/libc.a(lib_a-fwrite.o)
                                                  livedesk/leopard/mod_desktop/functions/built-in.o
init_position_para                                livedesk/leopard/mod_desktop/functions/built-in.o
ioctl                                             ./elibrary/bin//libsyscall.a(syscall_fops.o)
                                                  livedesk/leopard/mod_desktop/msg_srv/built-in.o
                                                  livedesk/leopard/mod_desktop/functions/built-in.o
memchr                                            /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/../../../../riscv64-unknown-elf/lib/rv64imafdcxtheadc/lp64d/libc.a(lib_a-memchr.o)
                                                  /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/../../../../riscv64-unknown-elf/lib/rv64imafdcxtheadc/lp64d/libc.a(lib_a-fvwrite.o)
memcpy                                            /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/../../../../riscv64-unknown-elf/lib/rv64imafdcxtheadc/lp64d/libc.a(lib_a-memcpy.o)
                                                  /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/../../../../riscv64-unknown-elf/lib/rv64imafdcxtheadc/lp64d/libc.a(lib_a-fvwrite.o)
                                                  ./elibrary/bin//libresreader.a(themedec.o)
                                                  ./elibrary/bin//liblzma.a(LzmaEnc.o)
                                                  ./elibrary/bin//liblzma.a(LzmaDec.o)
                                                  livedesk/leopard/mod_desktop/functions/built-in.o
memmove                                           /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/../../../../riscv64-unknown-elf/lib/rv64imafdcxtheadc/lp64d/libc.a(lib_a-memmove-stub.o)
                                                  /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/../../../../riscv64-unknown-elf/lib/rv64imafdcxtheadc/lp64d/libc.a(lib_a-fvwrite.o)
                                                  ./elibrary/bin//liblzma.a(LzFind.o)
memset                                            /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/../../../../riscv64-unknown-elf/lib/rv64imafdcxtheadc/lp64d/libc.a(lib_a-memset.o)
                                                  /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/../../../../riscv64-unknown-elf/lib/rv64imafdcxtheadc/lp64d/libc.a(lib_a-fseeko.o)
                                                  /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/../../../../riscv64-unknown-elf/lib/rv64imafdcxtheadc/lp64d/libc.a(lib_a-findfp.o)
                                                  ./elibrary/bin//libminic.a(elibs_stdio.o)
                                                  livedesk/leopard/mod_desktop/functions/built-in.o
mini_allocator_default_alloc                      livedesk/leopard/mod_desktop/util/built-in.o
mini_allocator_default_create                     livedesk/leopard/mod_desktop/util/built-in.o
mini_allocator_default_destroy                    livedesk/leopard/mod_desktop/util/built-in.o
mini_allocator_default_free                       livedesk/leopard/mod_desktop/util/built-in.o
mini_allocator_default_realloc                    livedesk/leopard/mod_desktop/util/built-in.o
mkdir                                             ./elibrary/bin//libsyscall.a(syscall_fops.o)
mmap                                              ./elibrary/bin//libsyscall.a(syscall.o)
modinfo_desktop                                   livedesk/leopard/mod_desktop/magic.o
msg_emit_deinit                                   livedesk/leopard/mod_desktop/msg_srv/built-in.o
                                                  livedesk/leopard/mod_desktop/desktop_api.o
msg_emit_init                                     livedesk/leopard/mod_desktop/msg_srv/built-in.o
                                                  livedesk/leopard/mod_desktop/desktop_api.o
msg_emit_register_hook                            livedesk/leopard/mod_desktop/msg_srv/built-in.o
                                                  livedesk/leopard/mod_desktop/desktop_api.o
msg_emit_register_tvout_tp_hook                   livedesk/leopard/mod_desktop/msg_srv/built-in.o
                                                  livedesk/leopard/mod_desktop/desktop_api.o
msg_emit_unregister_hook                          livedesk/leopard/mod_desktop/msg_srv/built-in.o
                                                  livedesk/leopard/mod_desktop/desktop_api.o
msg_sendhwsccmd                                   livedesk/leopard/mod_desktop/msg_srv/built-in.o
msg_srv_deinit_key_channel                        livedesk/leopard/mod_desktop/msg_srv/built-in.o
msg_srv_deinit_ksrv_channel                       livedesk/leopard/mod_desktop/msg_srv/built-in.o
msg_srv_deinit_tp_channel                         livedesk/leopard/mod_desktop/msg_srv/built-in.o
msg_srv_dispatch_message                          livedesk/leopard/mod_desktop/msg_srv/built-in.o
msg_srv_get_message                               livedesk/leopard/mod_desktop/msg_srv/built-in.o
msg_srv_init_key_channel                          livedesk/leopard/mod_desktop/msg_srv/built-in.o
msg_srv_init_ksrv_channel                         livedesk/leopard/mod_desktop/msg_srv/built-in.o
msg_srv_init_tp_channel                           livedesk/leopard/mod_desktop/msg_srv/built-in.o
msg_srv_thread                                    livedesk/leopard/mod_desktop/msg_srv/built-in.o
munmap                                            ./elibrary/bin//libsyscall.a(syscall.o)
mute_subwoofer                                    livedesk/leopard/mod_desktop/functions/built-in.o
open                                              /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/../../../../riscv64-unknown-elf/lib/rv64imafdcxtheadc/lp64d/libc.a(lib_a-sysopen.o)
                                                  livedesk/leopard/mod_desktop/msg_srv/built-in.o
                                                  livedesk/leopard/mod_desktop/functions/built-in.o
opendir                                           ./elibrary/bin//libsyscall.a(syscall_fops.o)
pipe                                              ./elibrary/bin//libsyscall.a(syscall.o)
poll                                              ./elibrary/bin//libsyscall.a(syscall.o)
printk                                            ./elibrary/bin//libminic.a(elibs_stdio.o)
                                                  ./elibrary/bin//libresreader.a(themedec.o)
pull_down_gpio                                    livedesk/leopard/mod_desktop/functions/built-in.o
pull_up_gpio                                      livedesk/leopard/mod_desktop/functions/built-in.o
readdir                                           ./elibrary/bin//libsyscall.a(syscall_fops.o)
remove                                            ./elibrary/bin//libsyscall.a(syscall_fops.o)
rewinddir                                         ./elibrary/bin//libsyscall.a(syscall_fops.o)
rmdir                                             ./elibrary/bin//libsyscall.a(syscall_fops.o)
rt_device_register                                ./elibrary/bin//libsyscall.a(syscall.o)
rt_device_unregister                              ./elibrary/bin//libsyscall.a(syscall.o)
save_backlight_to_mixture                         livedesk/leopard/mod_desktop/functions/built-in.o
select                                            ./elibrary/bin//libsyscall.a(syscall.o)
settimeofday                                      ./elibrary/bin//libsyscall.a(syscall_fops.o)
statfs                                            ./elibrary/bin//libsyscall.a(syscall_fops.o)
strcpy                                            /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/../../../../riscv64-unknown-elf/lib/rv64imafdcxtheadc/lp64d/libc.a(lib_a-strcpy.o)
                                                  livedesk/leopard/mod_desktop/functions/built-in.o
strncpy                                           /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/../../../../riscv64-unknown-elf/lib/rv64imafdcxtheadc/lp64d/libc.a(lib_a-strncpy.o)
                                                  livedesk/leopard/mod_desktop/functions/built-in.o
