cmd_livedesk/leopard/elibs/lib_ex/robin/subtitle/robin_subtitle.o := /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/source/../toolchain/riscv64-elf-x86_64-20201104//bin/riscv64-unknown-elf-gcc -Wp,-MD,livedesk/leopard/elibs/lib_ex/robin/subtitle/.robin_subtitle.o.d  -isystem /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/include  -Iinclude -Iinclude/melis -Iinclude/melis/common -Iinclude/melis/eboot -Iinclude/melis/eboard -Iinclude/melis/ekernel -Iinclude/melis/ekernel/arch -Iinclude/melis/ekernel/csp -Iinclude/melis/ekernel/drivers -Iinclude/melis/ekernel/filesystem -Iinclude/melis/ekernel/pthread -Iinclude/melis/elibrary -Iinclude/melis/elibrary/libc -Iinclude/melis/elibrary/libc/mediainfo -Iinclude/melis/elibrary/libc/misc -Iinclude/melis/elibrary/libc/misc/pub0 -Iinclude/melis/emodules -Iinclude/melis/ipc -Iinclude/melis/misc -Iinclude/melis/of -Iinclude/melis/sys -Iinclude/melis/video -Iekernel/components/thirdparty/finsh_cli -Iekernel/core/rt-thread/include -Iekernel/components/thirdparty/dfs/include -Iekernel/drivers/rtos-hal/hal/source -Iekernel/drivers/include/osal -Iinclude/generated -Iinclude/generated/uapi/melis -I./include/melis/elibrary -include ./include/melis/kconfig.h -Iinclude/melis/ekernel/arch/riscv -Iinclude/melis/arch/riscv -DARCH_CPU_64BIT -mcmodel=medany -mabi=lp64d -march=rv64gcxthead -falign-functions=4 -mcmodel=medany -mabi=lp64d -march=rv64gcxthead -Wall -Wundef -Wstrict-prototypes -Wno-trigraphs -fno-strict-aliasing -fno-common -Werror-implicit-function-declaration -Wno-format-security -fno-builtin-printf -D_SYS__PTHREADTYPES_H_ -fno-delete-null-pointer-checks -ffunction-sections -fdata-sections -fzero-initialized-in-bss -Wmaybe-uninitialized -Os -g -gdwarf-2 -gstrict-dwarf -Wstrict-prototypes -Wundef -Wno-unused-function -Wno-unused-variable -fshort-enums -Wsizeof-pointer-memaccess --param=allow-store-data-races=0 -Wframe-larger-than=8192 -fno-stack-protector -Wno-unused-but-set-variable -fomit-frame-pointer -fno-var-tracking-assignments -g -Wdeclaration-after-statement -Wno-pointer-sign -fno-strict-overflow -fconserve-stack -Werror=implicit-int -Werror=strict-prototypes -DCC_HAVE_ASM_GOTO -Wno-declaration-after-statement -mcmodel=medany -mabi=lp64d -march=rv64gcxthead -Wno-unused-label -Wextra -Wno-unused-parameter -Wno-old-style-declaration -Wno-sign-compare -I./ekernel/drivers/include/hal -I./ekernel/drivers/include/hal -I/home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/source/livedesk/leopard/elibs/libapps/module_adapter -I/home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/source/livedesk/leopard/elibs/libapps/scene/listbar -I/home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/source/livedesk/leopard/elibs/lib_ex/anole -I/home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/source/livedesk/leopard/elibs/lib_ex/monkey -I/home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/source/livedesk/leopard/elibs/lib_ex/monkey/bookmark -I/home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/source/livedesk/leopard/elibs/lib_ex/monkey/formats -I/home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/source/livedesk/leopard/elibs/lib_ex/monkey/formats/encode_parser -I/home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/source/livedesk/leopard/elibs/lib_ex/monkey/formats/encode_parser/gbk -I/home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/source/livedesk/leopard/elibs/lib_ex/monkey/formats/encode_parser/utf16_big -I/home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/source/livedesk/leopard/elibs/lib_ex/monkey/formats/encode_parser/utf16_little -I/home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/source/livedesk/leopard/elibs/lib_ex/monkey/formats/encode_parser/utf8 -I/home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/source/livedesk/leopard/elibs/lib_ex/monkey/mnpl -I/home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/source/livedesk/leopard/elibs/lib_ex/monkey/regedit -I/home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/source/livedesk/leopard/elibs/lib_ex/robin -I/home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/source/livedesk/leopard/elibs/lib_ex/robin/foundation -I/home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/source/livedesk/leopard/elibs/lib_ex/robin/lib -I/home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/source/livedesk/leopard/include -I/home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/source/livedesk/leopard/include/applets -I/home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/source/livedesk/leopard/include/elibs -I/home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/source/livedesk/leopard/include/elibs/lib_ex -I/home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/source/livedesk/leopard/include/elibs/lib_ex/robin/ab -I/home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/source/livedesk/leopard/include/elibs/lib_ex/robin/channel -I/home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/source/livedesk/leopard/include/elibs/lib_ex/robin/disp_output -I/home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/source/livedesk/leopard/include/elibs/lib_ex/robin/eq -I/home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/source/livedesk/leopard/include/elibs/lib_ex/robin/ff_rr_speed -I/home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/source/livedesk/leopard/include/elibs/lib_ex/robin/fsm_ctrl -I/home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/source/livedesk/leopard/include/elibs/lib_ex/robin/lyric -I/home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/source/livedesk/leopard/include/elibs/lib_ex/robin/media_info -I/home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/source/livedesk/leopard/include/elibs/lib_ex/robin/npl -I/home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/source/livedesk/leopard/include/elibs/lib_ex/robin/palette -I/home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/source/livedesk/leopard/include/elibs/lib_ex/robin/play_mode -I/home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/source/livedesk/leopard/include/elibs/lib_ex/robin/play_speed -I/home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/source/livedesk/leopard/include/elibs/lib_ex/robin/spectrum -I/home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/source/livedesk/leopard/include/elibs/lib_ex/robin/star -I/home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/source/livedesk/leopard/include/elibs/lib_ex/robin/subtitle -I/home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/source/livedesk/leopard/include/elibs/lib_ex/robin/track -I/home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/source/livedesk/leopard/include/elibs/lib_ex/robin/video_layer -I/home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/source/livedesk/leopard/include/elibs/lib_ex/robin/video_win -I/home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/source/livedesk/leopard/include/elibs/lib_ex/robin/vision_effect -I/home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/source/livedesk/leopard/include/elibs/lib_ex/robin/volume -I/home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/source/livedesk/leopard/include/elibs/lib_ex/robin/zoom -I/home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/source/livedesk/leopard/include/elibs/lib_ex/scanfile -I/home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/source/livedesk/leopard/include/init -I/home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/source/livedesk/leopard/include/init/alarm -I/home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/source/livedesk/leopard/include/init/background -I/home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/source/livedesk/leopard/include/init/bookengine -I/home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/source/livedesk/leopard/include/init/display -I/home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/source/livedesk/leopard/include/init/headbar -I/home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/source/livedesk/leopard/include/init/mini_music -I/home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/source/livedesk/leopard/include/init/screen_lock -I/home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/source/livedesk/leopard/include/init/tp_adjust -I/home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/source/livedesk/leopard/include/init/ui/tp_adjust_scene -I/home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/source/livedesk/leopard/include/libapps -I/home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/source/livedesk/leopard/include/libapps/module_adapter -I/home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/source/livedesk/leopard/include/libapps/scene -I/home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/source/livedesk/leopard/include/libapps/srf -I/home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/source/livedesk/leopard/include/mod_desktop -I/home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/source/livedesk/leopard/init/ -I/home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/source/livedesk/leopard/init/background/fb_lib -I/home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/source/livedesk/leopard/init/headbar/ -I/home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/source/livedesk/leopard/init/headbar/prog_bar -I/home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/source/livedesk/leopard/init/mini_music -I/home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/source/livedesk/leopard/init/screen_lock -I/home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/source/livedesk/leopard/init/ui -I/home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/source/livedesk/leopard/init/ui/assistant_scene -I/home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/source/livedesk/leopard/init/ui/dialog_scene -I/home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/source/livedesk/leopard/init/ui/tp_adjust_scene -I/home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/source/livedesk/leopard/mod_desktop -I/home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/source/livedesk/leopard/mod_desktop/engine -I/home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/source/livedesk/leopard/mod_desktop/engine/radio -I/home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/source/livedesk/leopard/mod_desktop/engine/radio/receive -I/home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/source/livedesk/leopard/mod_desktop/engine/radio/send -I/home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/source/livedesk/leopard/mod_desktop/engine/walkman -I/home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/source/livedesk/leopard/mod_desktop/engine/walkman/foundation -I/home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/source/livedesk/leopard/mod_desktop/engine/walkman/lib -I/home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/source/livedesk/leopard/mod_desktop/framework -I/home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/source/livedesk/leopard/mod_desktop/functions -I/home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/source/livedesk/leopard/mod_desktop/include -I/home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/source/livedesk/leopard/mod_desktop/util -I/home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/source/livedesk/leopard/include/mod_desktop/msg_srv -I./include/melis/module -I./livedesk/leopard/elibs/lib_ex/anole -I./livedesk/leopard/elibs/lib_ex/rat -I./livedesk/leopard/elibs/lib_ex/rat/scanfile -I./livedesk/leopard/include/elibs -I./livedesk/leopard/include/elibs/lib_ex -I./livedesk/leopard/include/elibs/lib_ex/scanfile -I./livedesk/leopard/include/elibs/lib_ex/qrencode -I./livedesk/leopard/include -I./include/melis/kernel -I./include/melis/misc -I./include/melis/libs/libc -I./livedesk/leopard/elibs/lib_ex/robin    -D"KBUILD_STR(s)=\#s" -D"KBUILD_BASENAME=KBUILD_STR(robin_subtitle)"  -D"KBUILD_MODNAME=KBUILD_STR(robin_subtitle)" -c -o livedesk/leopard/elibs/lib_ex/robin/subtitle/.tmp_robin_subtitle.o livedesk/leopard/elibs/lib_ex/robin/subtitle/robin_subtitle.c

source_livedesk/leopard/elibs/lib_ex/robin/subtitle/robin_subtitle.o := livedesk/leopard/elibs/lib_ex/robin/subtitle/robin_subtitle.c

deps_livedesk/leopard/elibs/lib_ex/robin/subtitle/robin_subtitle.o := \
  include/melis/kconfig.h \
    $(wildcard include/config/h.h) \
  /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/source/livedesk/leopard/elibs/lib_ex/robin/robin_i.h \
  /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/source/livedesk/leopard/include/apps.h \
  include/melis/kconfig.h \
  ekernel/components/thirdparty/dfs/include/dfs_posix.h \
  ekernel/components/thirdparty/dfs/include/dfs_file.h \
  ekernel/components/thirdparty/dfs/include/dfs.h \
  /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/riscv64-unknown-elf/include/stdio.h \
  /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/riscv64-unknown-elf/include/_ansi.h \
  /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/riscv64-unknown-elf/include/newlib.h \
  /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/riscv64-unknown-elf/include/_newlib_version.h \
  /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/riscv64-unknown-elf/include/sys/config.h \
    $(wildcard include/config/h//.h) \
  /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/riscv64-unknown-elf/include/machine/ieeefp.h \
  /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/riscv64-unknown-elf/include/sys/features.h \
  /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/riscv64-unknown-elf/include/sys/cdefs.h \
  /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/riscv64-unknown-elf/include/machine/_default_types.h \
  /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/lib/gcc/riscv64-unknown-elf/8.4.0/include/stddef.h \
  /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/lib/gcc/riscv64-unknown-elf/8.4.0/include/stdarg.h \
  /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/riscv64-unknown-elf/include/sys/reent.h \
  /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/riscv64-unknown-elf/include/_ansi.h \
  /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/riscv64-unknown-elf/include/sys/_types.h \
  /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/riscv64-unknown-elf/include/machine/_types.h \
  /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/riscv64-unknown-elf/include/sys/lock.h \
  /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/riscv64-unknown-elf/include/sys/types.h \
  /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/riscv64-unknown-elf/include/sys/_stdint.h \
  /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/riscv64-unknown-elf/include/machine/endian.h \
  /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/riscv64-unknown-elf/include/machine/_endian.h \
  /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/riscv64-unknown-elf/include/sys/select.h \
  /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/riscv64-unknown-elf/include/sys/_sigset.h \
  /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/riscv64-unknown-elf/include/sys/_timeval.h \
  /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/riscv64-unknown-elf/include/sys/timespec.h \
  /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/riscv64-unknown-elf/include/sys/_timespec.h \
  /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/riscv64-unknown-elf/include/sys/_pthreadtypes.h \
  /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/riscv64-unknown-elf/include/machine/types.h \
  /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/riscv64-unknown-elf/include/sys/stdio.h \
  /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/lib/gcc/riscv64-unknown-elf/8.4.0/include/stdint.h \
  /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/riscv64-unknown-elf/include/stdint.h \
  /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/riscv64-unknown-elf/include/sys/_intsup.h \
  /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/riscv64-unknown-elf/include/stdlib.h \
  /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/riscv64-unknown-elf/include/machine/stdlib.h \
  /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/riscv64-unknown-elf/include/alloca.h \
  /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/riscv64-unknown-elf/include/string.h \
  /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/riscv64-unknown-elf/include/xlocale.h \
  /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/riscv64-unknown-elf/include/strings.h \
  /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/riscv64-unknown-elf/include/sys/string.h \
  /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/riscv64-unknown-elf/include/time.h \
  /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/riscv64-unknown-elf/include/machine/time.h \
  ekernel/core/rt-thread/include/rtthread.h \
  ekernel/core/rt-thread/include/rtconfig.h \
    $(wildcard include/config/rt/name/max.h) \
    $(wildcard include/config/rt/align/size.h) \
    $(wildcard include/config/modules.h) \
    $(wildcard include/config/rt/thread/priority/max.h) \
    $(wildcard include/config/hz.h) \
    $(wildcard include/config/rt/debug.h) \
    $(wildcard include/config/rt/debug/init/config.h) \
    $(wildcard include/config/rt/debug/thread/config.h) \
    $(wildcard include/config/rt/debug/scheduler/config.h) \
    $(wildcard include/config/rt/debug/ipc/config.h) \
    $(wildcard include/config/rt/debug/timer/config.h) \
    $(wildcard include/config/rt/debug/irq/config.h) \
    $(wildcard include/config/rt/debug/mem/config.h) \
    $(wildcard include/config/rt/debug/slab/config.h) \
    $(wildcard include/config/rt/debug/memheap/config.h) \
    $(wildcard include/config/rt/debug/module/config.h) \
    $(wildcard include/config/rt/using/overflow/check.h) \
    $(wildcard include/config/rt/using/hook.h) \
    $(wildcard include/config/rt/using/idle/hook.h) \
    $(wildcard include/config/rt/using/timer/soft.h) \
    $(wildcard include/config/rt/timer/thread/prio.h) \
    $(wildcard include/config/rt/timer/thread/stack/size.h) \
    $(wildcard include/config/rt/using/semaphore.h) \
    $(wildcard include/config/rt/using/mutex.h) \
    $(wildcard include/config/rt/using/event.h) \
    $(wildcard include/config/rt/using/mailbox.h) \
    $(wildcard include/config/rt/using/messagequeue.h) \
    $(wildcard include/config/rt/using/pipe.h) \
    $(wildcard include/config/rt/using/ringbuffer.h) \
    $(wildcard include/config/rt/using/waitqueue.h) \
    $(wildcard include/config/rt/using/workqueue.h) \
    $(wildcard include/config/rt/using/completion.h) \
    $(wildcard include/config/rt/using/signals.h) \
    $(wildcard include/config/rt/using/mempool.h) \
    $(wildcard include/config/rt/using/memheap.h) \
    $(wildcard include/config/rt/using/noheap.h) \
    $(wildcard include/config/rt/using/small/mem.h) \
    $(wildcard include/config/rt/using/slab.h) \
    $(wildcard include/config/rt/using/memheap/as/heap.h) \
    $(wildcard include/config/rt/using/memtrace.h) \
    $(wildcard include/config/rt/using/heap.h) \
    $(wildcard include/config/rt/using/device.h) \
    $(wildcard include/config/rt/using/interrupt/info.h) \
    $(wildcard include/config/rt/using/console.h) \
    $(wildcard include/config/rt/consolebuf/size.h) \
    $(wildcard include/config/rt/console/device/name.h) \
    $(wildcard include/config/rt/using/finsh.h) \
    $(wildcard include/config/finsh/using/symtab.h) \
    $(wildcard include/config/finsh/thread/name.h) \
    $(wildcard include/config/finsh/using/history.h) \
    $(wildcard include/config/finsh/history/lines.h) \
    $(wildcard include/config/finsh/using/description.h) \
    $(wildcard include/config/finsh/thread/priority.h) \
    $(wildcard include/config/finsh/thread/stack/size.h) \
    $(wildcard include/config/finsh/cmd/size.h) \
    $(wildcard include/config/finsh/using/msh.h) \
    $(wildcard include/config/finsh/using/msh/default.h) \
    $(wildcard include/config/finsh/arg/max.h) \
    $(wildcard include/config/rt/using/dfs.h) \
  ekernel/core/rt-thread/include/rtdebug.h \
  ekernel/core/rt-thread/include/rtdef.h \
    $(wildcard include/config/arch/riscv.h) \
    $(wildcard include/config/arch/riscv/vector.h) \
  include/melis/ekernel/arch/riscv/excep.h \
    $(wildcard include/config/fpu/double.h) \
  ekernel/core/rt-thread/include/rtservice.h \
  ekernel/core/rt-thread/include/rtm.h \
  ekernel/components/thirdparty/finsh_cli/finsh_api.h \
  /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/riscv64-unknown-elf/include/sys/stat.h \
  /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/riscv64-unknown-elf/include/errno.h \
  /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/riscv64-unknown-elf/include/sys/errno.h \
  /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/riscv64-unknown-elf/include/fcntl.h \
  /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/riscv64-unknown-elf/include/sys/fcntl.h \
  /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/riscv64-unknown-elf/include/sys/_default_fcntl.h \
  /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/riscv64-unknown-elf/include/sys/unistd.h \
  ekernel/components/thirdparty/dfs/include/dfs_fs.h \
  include/melis/elibrary/libc.h \
  include/melis/elibrary/./libc/elibs_stdlib.h \
  include/melis/typedef.h \
    $(wildcard include/config/drivers/usb/gadget/qvh.h) \
  /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/lib/gcc/riscv64-unknown-elf/8.4.0/include-fixed/limits.h \
  /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/lib/gcc/riscv64-unknown-elf/8.4.0/include-fixed/syslimits.h \
  /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/riscv64-unknown-elf/include/limits.h \
  include/melis/elibrary/./libc/elibs_string.h \
  include/melis/elibrary/./libc/elibs_stdio.h \
  include/melis/ekernel/kapi.h \
    $(wildcard include/config/melis/legacy/driver/man.h) \
    $(wildcard include/config/t.h) \
    $(wildcard include/config/kernel/use/sbi.h) \
  include/melis/ekernel/kmsg.h \
    $(wildcard include/config/drivers/usb/gadget/ncm.h) \
    $(wildcard include/config/setting/update.h) \
  include/melis/eboot/boot.h \
  include/melis/ekernel/drivers/sys_charset.h \
  include/melis/ekernel/drivers/sys_clock.h \
  include/melis/ekernel/ktype.h \
  include/melis/ekernel/csp/csp_ccm_para.h \
    $(wildcard include/config/drivers/sunxi/clk.h) \
    $(wildcard include/config/drivers/sunxi/ccu.h) \
  include/melis/ekernel/drivers/sys_device.h \
    $(wildcard include/config/spinor/cmmb/data/offset.h) \
    $(wildcard include/config/spinor/cmmb/data/size.h) \
    $(wildcard include/config/spinor/apps/data/offset.h) \
    $(wildcard include/config/spinor/apps/data/size.h) \
    $(wildcard include/config/spinor/mcu/update/data/offset.h) \
    $(wildcard include/config/spinor/mcu/update/data/size.h) \
  include/melis/ekernel/drivers/sys_fsys.h \
    $(wildcard include/config/soc/sun20iw1.h) \
    $(wildcard include/config/soc/sun8iw20.h) \
    $(wildcard include/config/driver/spinor.h) \
  /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/lib/gcc/riscv64-unknown-elf/8.4.0/include/stdbool.h \
  include/melis/ekernel/drivers/sys_device.h \
  include/melis/ekernel/drivers/sys_hwsc.h \
  include/melis/ekernel/drivers/sys_input.h \
  include/melis/ekernel/drivers/sys_mems.h \
  include/melis/ekernel/csp/csp_dram_para.h \
    $(wildcard include/config/soc/sun3iw2.h) \
    $(wildcard include/config/soc/sun8i.h) \
    $(wildcard include/config/soc/sun20iw3.h) \
    $(wildcard include/config/soc/sun3iw1.h) \
  include/melis/ekernel/drivers/sys_pins.h \
  include/melis/script.h \
  include/melis/ekernel/drivers/sys_powerman.h \
  include/melis/ekernel/drivers/sys_svc.h \
  include/melis/ekernel/drivers/sys_time.h \
  include/melis/ekernel/csp/csp_dma_para.h \
    $(wildcard include/config/soc/sun8iw19.h) \
  include/melis/ekernel/csp/csp_int_para.h \
  include/melis/elibrary/./libc/elibs_misc.h \
  include/melis/elibrary/./libc/misc/pub0.h \
  include/melis/elibrary/./libc/misc/pub0/elibs_cacheop.h \
  include/melis/elibrary/./libc/misc/pub0/elibs_reg.h \
  include/melis/ekernel/arch/riscv/arch.h \
  include/melis/emodules/mod_defs.h \
  include/melis/ekernel/drivers/sys_time.h \
  include/melis/emodules/mod_orchid.h \
  include/melis/elibrary/libc/mediainfo/mediafmt.h \
  include/melis/emodules/mod_defs.h \
  include/melis/emodules/mod_charset.h \
  include/melis/emodules/mod_willow.h \
  include/melis/misc/fb.h \
  include/melis/emodules/mod_defs.h \
  include/melis/emodules/mod_cedar.h \
    $(wildcard include/config/video/lyr/ck/enable.h) \
  include/melis/misc/fb.h \
  include/melis/emodules/mod_audio.h \
    $(wildcard include/config/plan/sprite.h) \
  include/melis/emodules/mod_adb.h \
  include/melis/emodules/mod_dab.h \
  include/melis/emodules/mod_aoa.h \
  include/melis/emodules/mod_mcu.h \
  include/melis/emodules/mod_fm.h \
  include/melis/emodules/mod_aux.h \
  include/melis/emodules/mod_bp1064.h \
  include/melis/emodules/mod_bt_moudel.h \
  include/melis/emodules/SaveRds.h \
  include/melis/emodules/mod_video_play.h \
    $(wildcard include/config/para.h) \
  include/melis/emodules/../../../emodules/drv_mcu_uart/core/uart_cmd_set.h \
  include/melis/elibrary/libc/elibs_stdio.h \
  include/melis/emodules/../../../emodules/drv_mcu_uart/core/sxi_defs.h \
  include/melis/emodules/../../../emodules/drv_mcu_uart/core/handlers/protocol/mcu_new_protocol_handler.h \
  include/melis/emodules/../../../emodules/drv_mcu_uart/core/handlers/protocol/../../mcu_cmd_dispatcher.h \
  include/melis/emodules/../../../emodules/drv_mcu_uart/core/handlers/protocol/../../uart_cmd_set.h \
  include/melis/emodules/../../../emodules/drv_mcu_uart/core/handlers/protocol/system/mcu_system_category_handler.h \
  include/melis/emodules/../../../emodules/drv_mcu_uart/core/handlers/protocol/audio/mcu_audio_category_handler.h \
  include/melis/emodules/../../../emodules/drv_mcu_uart/core/handlers/protocol/tuner/mcu_tuner_category_handler.h \
  include/melis/emodules/../../../emodules/drv_mcu_uart/core/handlers/protocol/tuner/../mcu_protocol_sync.h \
  include/melis/emodules/../../../emodules/drv_mcu_uart/core/handlers/protocol/usb/mcu_usb_category_handler.h \
  include/melis/emodules/../../../emodules/drv_mcu_uart/core/handlers/protocol/party/mcu_party_category_handler.h \
  include/melis/emodules/../../../emodules/drv_mcu_uart/core/handlers/protocol/settings/mcu_settings_category_handler.h \
  include/melis/emodules/mod_touchpanel.h \
  include/melis/emodules/mod_codec_cmd.h \
  /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/source/livedesk/leopard/include/applets/app_load_para.h \
  /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/source/livedesk/leopard/include/applets/tv_visible_area.h \
  /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/source/livedesk/leopard/include/elibs/lib_ex.h \
  /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/source/livedesk/leopard/include/elibs/lib_ex/anole.h \
  include/melis/emodules/mod_display.h \
  include/melis/log.h \
    $(wildcard include/config/log/default/level.h) \
    $(wildcard include/config/log/release.h) \
    $(wildcard include/config/dynamic/log/level/support.h) \
    $(wildcard include/config/disable/all/debuglog.h) \
  include/melis/video/sunxi_display2.h \
  /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/source/livedesk/leopard/include/elibs/lib_ex/scanfile/OAL.h \
  /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/source/livedesk/leopard/include/elibs/lib_ex/scanfile/ScanStack.h \
  /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/source/livedesk/leopard/include/elibs/lib_ex/scanfile/ScanStackPri.h \
  /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/source/livedesk/leopard/include/elibs/lib_ex/scanfile/ScanStack.h \
  /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/source/livedesk/leopard/include/elibs/lib_ex/scanfile/Scan.h \
  /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/source/livedesk/leopard/include/elibs/lib_ex/scanfile/ScanPri.h \
  /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/source/livedesk/leopard/include/elibs/lib_ex/scanfile/OAL.h \
  /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/source/livedesk/leopard/include/elibs/lib_ex/scanfile/Scan.h \
  /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/source/livedesk/leopard/include/elibs/lib_ex/rat_common.h \
  /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/source/livedesk/leopard/include/elibs/lib_ex/rat_miniature.h \
  include/melis/emodules/mod_willow.h \
  /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/source/livedesk/leopard/include/elibs/lib_ex/rat_common.h \
  include/melis/emodules/mod_cedar.h \
  include/melis/emodules/mod_raw_decoder.h \
  /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/source/livedesk/leopard/include/elibs/lib_ex/rat.h \
  /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/source/livedesk/leopard/include/elibs/lib_ex/rat_npl.h \
  /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/source/livedesk/leopard/include/elibs/lib_ex/rat_partition.h \
  /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/source/livedesk/leopard/include/elibs/lib_ex/robin.h \
  /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/source/livedesk/leopard/include/elibs/lib_ex/robin/ab/robin_ab.h \
  /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/source/livedesk/leopard/include/elibs/lib_ex/robin/channel/robin_channel.h \
  /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/source/livedesk/leopard/include/elibs/lib_ex/robin/eq/robin_eq.h \
  /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/source/livedesk/leopard/include/elibs/lib_ex/robin/ff_rr_speed/robin_ff_rr_speed.h \
  /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/source/livedesk/leopard/include/elibs/lib_ex/robin/fsm_ctrl/robin_fsm_ctrl.h \
  /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/source/livedesk/leopard/include/elibs/lib_ex/robin/lyric/robin_lyric.h \
  /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/source/livedesk/leopard/include/elibs/lib_ex/robin/media_info/robin_media_info.h \
  /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/source/livedesk/leopard/include/elibs/lib_ex/robin/npl/robin_npl.h \
  /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/source/livedesk/leopard/include/elibs/lib_ex/robin/play_mode/robin_play_mode.h \
  /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/source/livedesk/leopard/include/elibs/lib_ex/robin/play_speed/robin_play_speed.h \
  /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/source/livedesk/leopard/include/elibs/lib_ex/robin/spectrum/robin_spectrum.h \
  /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/source/livedesk/leopard/include/elibs/lib_ex/robin/star/robin_star.h \
  /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/source/livedesk/leopard/include/elibs/lib_ex/robin/subtitle/robin_subtitle.h \
  /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/source/livedesk/leopard/include/elibs/lib_ex/robin/track/robin_track.h \
  /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/source/livedesk/leopard/include/elibs/lib_ex/robin/video_layer/robin_video_layer.h \
  /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/source/livedesk/leopard/include/elibs/lib_ex/robin/video_win/robin_video_win.h \
  /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/source/livedesk/leopard/include/elibs/lib_ex/robin/vision_effect/robin_vision_effect.h \
  /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/source/livedesk/leopard/include/elibs/lib_ex/robin/volume/robin_volume.h \
  /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/source/livedesk/leopard/include/elibs/lib_ex/robin/zoom/robin_zoom.h \
  /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/source/livedesk/leopard/include/elibs/lib_ex/robin/disp_output/robin_disp_output.h \
  /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/source/livedesk/leopard/include/elibs/lib_ex/robin/palette/robin_palette.h \
  /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/source/livedesk/leopard/include/mod_desktop/desktop.h \
  /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/source/livedesk/leopard/include/mod_desktop/util/elibs_language.h \
  /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/source/livedesk/leopard/include/mod_desktop/msg_srv/dsk_msg.h \
  /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/source/livedesk/leopard/include/mod_desktop/msg_srv/msg_emit.h \
  /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/source/livedesk/leopard/include/gui_msg.h \
  /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/source/livedesk/leopard/include/mod_desktop/msg_srv/msg_emit.h \
  /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/source/livedesk/leopard/include/mod_desktop/functions/dsk_reg.h \
  /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/source/livedesk/leopard/include/mod_desktop/functions/reg/reg_audio_output.h \
  /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/source/livedesk/leopard/include/mod_desktop/functions/reg/reg_display.h \
  /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/source/livedesk/leopard/include/mod_desktop/functions/reg/reg_lcd_bright.h \
  /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/source/livedesk/leopard/include/elibs/lib_ex.h \
  include/melis/emodules/mod_mcu.h \
  /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/source/livedesk/leopard/include/mod_desktop/functions/dsk_display.h \
  /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/source/livedesk/leopard/include/mod_desktop/functions/display/dsk_display_gamma.h \
  /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/source/livedesk/leopard/include/mod_desktop/functions/display/dsk_display_misc.h \
  /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/source/livedesk/leopard/include/mod_desktop/functions/display/dsk_display_output.h \
  /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/source/livedesk/leopard/include/mod_desktop/functions/display/dsk_display_lcd.h \
  /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/source/livedesk/leopard/include/mod_desktop/functions/dsk_volume.h \
  /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/source/livedesk/leopard/include/mod_desktop/functions/dsk_keytone.h \
  /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/source/livedesk/leopard/include/mod_desktop/functions/dsk_usbh.h \
  /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/source/livedesk/leopard/include/mod_desktop/functions/dsk_audio_if.h \
  include/melis/emodules/mod_audio.h \
  /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/source/livedesk/leopard/include/mod_desktop/functions/dsk_charset.h \
  include/melis/emodules/mod_charset.h \
  /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/source/livedesk/leopard/include/mod_desktop/functions/dsk_langres.h \
  /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/source/livedesk/leopard/include/mod_desktop/functions/dsk_theme.h \
  /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/source/livedesk/leopard/include/mod_desktop/functions/dsk_aux.h \
  /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/source/livedesk/leopard/include/mod_desktop/functions/dsk_mcu.h \
  /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/source/livedesk/leopard/include/mod_desktop/functions/dsk_bluetooth.h \
  /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/source/livedesk/leopard/include/mod_desktop/functions/dsk_dab.h \
  /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/source/livedesk/leopard/include/mod_desktop/functions/dsk_usbd.h \
  /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/source/livedesk/leopard/include/mod_desktop/functions/dsk_power.h \
  /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/source/livedesk/leopard/include/mod_desktop/functions/dsk_orchid.h \
  /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/source/livedesk/leopard/include/mod_desktop/functions/dsk_auto_off.h \
  /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/source/livedesk/leopard/include/mod_desktop/desktop_api.h \
  /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/source/livedesk/leopard/include/mod_desktop/framework/activity.h \
  /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/source/livedesk/leopard/include/mod_desktop/framework/applet_info_manager.h \
  /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/source/livedesk/leopard/include/mod_desktop/framework/activity_manager.h \
  /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/source/livedesk/leopard/include/mod_desktop/framework/applet_info_manager.h \
  /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/source/livedesk/leopard/include/mod_desktop/framework/globals.h \
  /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/source/livedesk/leopard/include/mod_desktop/engine/engine.h \
  /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/source/livedesk/leopard/include/mod_desktop/engine/dsk_walkman.h \
  /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/source/livedesk/leopard/include/mod_desktop/engine/walkman//dsk_wkm_npl.h \
  /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/source/livedesk/leopard/include/mod_desktop/engine/walkman//dsk_wkm_play_mode.h \
  include/melis/emodules/mod_orchid.h \
  /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/source/livedesk/leopard/include/mod_desktop/engine/walkman//dsk_wkm_fsm_ctrl.h \
  /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/source/livedesk/leopard/include/mod_desktop/engine/walkman//dsk_wkm_ff_rr_speed.h \
  /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/source/livedesk/leopard/include/mod_desktop/engine/walkman//dsk_wkm_eq.h \
  /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/source/livedesk/leopard/include/mod_desktop/engine/walkman//dsk_wkm_ab.h \
  /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/source/livedesk/leopard/include/mod_desktop/engine/walkman//dsk_wkm_lyric.h \
  /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/source/livedesk/leopard/include/mod_desktop/engine/walkman//dsk_wkm_spectrum.h \
  /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/source/livedesk/leopard/include/mod_desktop/engine/walkman//dsk_wkm_media_info.h \
  /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/source/livedesk/leopard/include/mod_desktop/engine/walkman//dsk_wkm_play_speed.h \
  /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/source/livedesk/leopard/include/mod_desktop/engine/walkman//dsk_wkm_star.h \
  /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/source/livedesk/leopard/include/mod_desktop/engine/walkman//dsk_wkm_monitor_gate.h \
  /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/source/livedesk/leopard/include/mod_desktop/engine/dsk_radio.h \
  /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/source/livedesk/leopard/include/mod_desktop/engine/radio/dsk_radio_receive.h \
  /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/source/livedesk/leopard/include/mod_desktop/engine/radio/dsk_radio_send.h \
  /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/source/livedesk/leopard/include/res/lang.h \
  /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/source/livedesk/leopard/include/res/theme.h \
  /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/source/livedesk/leopard/include/res/karaoke_theme.h \
  /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/source/livedesk/leopard/elibs/lib_ex/robin/lib/robin_lib.h \
  /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/source/livedesk/leopard/elibs/lib_ex/robin/lib/robin_gp_def.h \
  /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/source/livedesk/leopard/elibs/lib_ex/robin/lib/robin_redef.h \
  /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/source/livedesk/leopard/elibs/lib_ex/robin/lib/robin_misc.h \
  /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/source/livedesk/leopard/elibs/lib_ex/robin/foundation/foundation.h \
  /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/source/livedesk/leopard/elibs/lib_ex/robin/foundation/cmdQ.h \
  /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/source/livedesk/leopard/elibs/lib_ex/robin/foundation/robin_feedbackQ.h \
  /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/source/livedesk/leopard/elibs/lib_ex/robin/foundation/robin_monitor.h \
  /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/source/livedesk/leopard/elibs/lib_ex/robin/foundation/robin_cedar_mutex.h \
  /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/source/livedesk/leopard/elibs/lib_ex/robin/foundation/robin_cmd_mutex.h \
  /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/source/livedesk/leopard/elibs/lib_ex/robin/foundation/robin_syn_op.h \

livedesk/leopard/elibs/lib_ex/robin/subtitle/robin_subtitle.o: $(deps_livedesk/leopard/elibs/lib_ex/robin/subtitle/robin_subtitle.o)

$(deps_livedesk/leopard/elibs/lib_ex/robin/subtitle/robin_subtitle.o):
