// SquareLine LVGL GENERATED FILE
// EDITOR VERSION: SquareLine Studio 1.2.0
// LVGL VERSION: 8.3.4
// PROJECT: SquareLine_Project

#ifndef _UI_MUSIC_HELPERS_H
#define _UI_MUSIC_HELPERS_H

#ifdef __cplusplus
extern "C" {
#endif

#include "app_root_helpers.h"
#include "ui_music_res.h"
#include "ui_source_communication_hub.h"

//第一次搜索文件的个数
#define		C_SEARCH_FILE_CNT_AT_FIRST			0								

#define ITEM_NUM_IN_LIST_PRE_PAGE		5 

#define PREV_NEXT_SONG_SLIDE_OUT
#define DELAY_DRAW_MUSIC_ALBUM_PIC


#define music_playing_explorer_mode_time  (11*100)	//11s	用于浏览播放歌曲切换的，每首歌播放10s
#define music_select_song_time  (10*100)	//10	 用于数字选择按键定时的，按下数字选曲后，10内完成输入
#define Music_Prev_Next_Key_Time  (80)	//500ms

#define CTRL_TIMER_TIMES		50
#define AUTO_EXIT_POP_TIMES	6 // 3s 


//1代表10ms
#define		MUSIC_SYSTIMER_INTERVAL	(50)


#define		MUSIC_TIMER_INTERVAL			(50)//(15)//(22)    //(22)
#define		MUSIC_REFRESH_PLAYTIME_CNT	(90 / MUSIC_TIMER_INTERVAL) //mllee 120911//(80 / MUSIC_TIMER_INTERVAL)
#define		MUSIC_REFRESH_LYRICS_CNT	(100 / MUSIC_TIMER_INTERVAL)
#define		MUSIC_SEND_MCU_PLAYTIME_CNT	  (150 / MUSIC_TIMER_INTERVAL)
#define		MUSIC_CHANGE_PLAY_CNT		(100 / MUSIC_TIMER_INTERVAL)
#ifdef DELAY_DRAW_MUSIC_ALBUM_PIC
#define		MUSIC_DRAW_ALBUM_ART_CNT		(100 / MUSIC_TIMER_INTERVAL)
#endif

#define		MUSIC_SPECTRUM_TIMER_INTERVAL	(15)

#define		MUSIC_CHECK_FOCUS_TIMER_INTERVAL (100*30)			//30秒钟没有操作，则返回到实际播放的条目
#define		MUSIC_CHECK_FOCUS_TIMER_CNT		  (20)				//

#define		MUSIC_FF_RR_TIMER_INTERVAL		 (300)					//这个时间可以稍微定义大一些
//2秒自动退出菜单
#define		C_AUTO_EXIT_MENU_TIME	(35)

#define		C_AUTO_EXIT_FUNC_TIME	(FUNC_AUTO_CLOSE_TIME*(100/MUSIC_TIMER_INTERVAL))///(27) //6s

#define ROOTTYPE_TO_SPCTRM_TIMES (50 / MUSIC_TIMER_INTERVAL) //mllee 131216 1s root type switch to spctrm show

#define MUSIC_FF_SPEED		(CEDAR_FASTEST_FRSPEED/16) //mllee 120911//16)  //8)
#define MUSIC_RR_SPEED		(CEDAR_FASTEST_FRSPEED/4)

//normal

typedef enum
{
	music_stashow_play=0,
	music_stashow_pause,
	music_stashow_ff,
	music_stashow_rr,
	music_stashow_num
}music_stashow_t;

typedef enum music_menu_mode_s
{
	MUSIC_EXPLORER_MODE,
	MUSIC_SET_PLAYER_MODE,	
	MUSIC_FUNCTION_MODE,
}music_menu_mode_e;


//EQ mode
typedef enum music_EQ_mode_s
{
	MUSIC_EQ_NORMAL,
	MUSIC_EQ_ROCK,
	MUSIC_EQ_CLASSIC,
	MUSIC_EQ_JAZZ,
	MUSIC_EQ_POP,
	MUSIC_EQ_OFF,
}music_EQ_mode_e;

//background light last time
typedef enum music_BL_time_s
{
	MUSIC_BL_ON = 0,					//Never off
	MUSIC_BL_LAST_10S, 				//last 10second
	MUSIC_BL_LAST_30S,					//last 30second
	MUSIC_BL_LAST_60S,					//last 60second
	MUSIC_BL_LAST_90S,					//last 90second
}music_BL_time_e;


typedef struct audio_file_info_s
{
	char file[RAT_MAX_FULL_PATH_LEN];
	__epdk_charset_enm_e file_charset;
    
	char filename[RAT_MAX_FULL_PATH_LEN];
	__epdk_charset_enm_e  title_charset; //mllee 120802
	char artist[ROBIN_ARTIST_SIZE];	
	__epdk_charset_enm_e  artist_charset;//mllee 120802
	char album[ROBIN_ALBUM_SIZE];
	__epdk_charset_enm_e  album_charset; //mllee 120802
	__u32 duration;
	__s32 filesize;
	__s32 bitrate;	
	__s32 sample_rate;
	__s32 filebit;
}audio_file_info_t;

typedef struct
{
	char file[RAT_MAX_FULL_PATH_LEN];
    char *buf;      //数据
    __u32 width;                        //宽度
    __u32  height;                       //高度
}song_albumart_t;

typedef struct music_res_para
{
	HTHEME lv_music_bmp[MAX_MUSIC_BMP_ITEM];
	lv_img_dsc_t lv_music_icon[MAX_MUSIC_BMP_ITEM];
	lv_img_dsc_t lv_music_album_logo;
	lv_img_dsc_t lv_music_album_bg;
}ui_music_res_para_t;

typedef struct music_data_para
{
	__u8 bt_status;
	__u32 cur_play_state; //play pause
	__u32 cur_play_mode; //repeat
	__u8 repeat_mode; //0: off  1: one 2: folder 3: all 
	__u8 random_mode; // 0: off  1: folder 2: all
	__u32 cur_playtime;
	__u32 total_playtime;

	__u8 *artwork_buf;
	__u32 artwork_width;
	__u32 artwork_height;
	__u32 artwork_size;
	__u8  artwork_status;		
	__u8  artwork_type;  // 0: RGB mode, 1: ori pic mod; 6: ARGB mode for usb
	
    char Album[70+1];//64-->70
    char Title[70+1];//64-->70
    char Artist[70+1];//64-->70

    char btaudio_phone_name[128];//[16];
}ui_music_data_para_t;

typedef struct music_para
{
	__u8 mode_store;
	__bool				visiable;					//是否显示主界面，不显示表示背景播放状态
	__s32				root_type;					//USB or SDCard
	
	rat_media_type_t	media_type; 				//搜索文件的类型 	

	music_menu_mode_e	menu_mode;					//菜单模式, Explorer mode ,or setting player mode
	__cedar_status_t	setting_status; 			//FF,正在设置快进
	rat_play_mode_e 	play_mode;					//播放模式
	music_EQ_mode_e 	EQ_mode;					//声效模式
	music_BL_time_e 	BL_time;					//背光持续时间
	__s32				volume; 					//音量

	audio_file_info_t	play_file_info; 			//播放文件信息

	__u32 last_start_id;
	__u32 last_focused_id;
	char *last_filename; 										//最后一次浏览文件名
	
	char search_path[RAT_MAX_FULL_PATH_LEN]; 			//搜索路径
	HRAT rat_handle; 										//搜索句柄
	__u32 total;												//条目总数
	__u32 play_index; 										//当前播放文件索引号
	__u32 RefreshPlayTimeCnt; 		//刷新播放时间timercount

	__u32 next_prev_index;
	rat_play_mode_e play_mode_repeat;
	__u32 play_mode_random;//0:no ramdom	1:ramdom all		2: ramdom folder
	music_stashow_t playsta;
	__s32  jump_to_last_time;
	__u32 explorer_to_play;
	__u32 explorer_focus_index;

	__u32 player_mute_status;
	__u32 player_froce_mute;
	__u32 SendMcuPlayTimeCnt;
	__u8  ChangePlay;//1: next change play	 2:prev change play
	__u8  RotateChangePlay;//1: next change play   2:prev change play
	__u32 ChangePlayNumberCnt;
#ifdef USB_PLAYER_SUPPORT_M4A_FILE
	__u8 is_m4a_file;
	__u8 ff_rr;//0: no status	1:ff	2:rr
	__u8 no_allow_ff_rr_flag;//0: no status 1:ff	2:rr
#endif
	__u8 music_stop_flag;
	song_albumart_t AlbumArt;
	song_albumart_t AlbumArtBg;
#ifdef DELAY_DRAW_MUSIC_ALBUM_PIC
	int AlbumArtIndex;
	__u8 AlbumArtFlag;
	__u32 AlbumArtCount;
#endif
	__u8 first_time_open_music_player_win;
	__bool	music_rr_valid;
	__bool	music_ff_valid;
#ifdef USE_GXPMX30_MODEL
	__u32 work_source;
	__u32 usb_device; //0:usb ,  1: iPod

	__u32 party_work_source;
    
	RECT tags_miniature_rect;
    RECT tags_miniature_rect_bg;
	lv_obj_t * music_menu_obj;
    lv_obj_t * ui_func_menu;
    lv_obj_t * ui_func_broadcast_select_source;
    lv_obj_t * ui_func_broadcast_select_source_blur_panel;
    lv_obj_t * ui_func_broadcast_select_source_blur;
    lv_obj_t * ui_func_broadcast_select_source_panel;
    lv_obj_t * ui_func_broadcast_select_source_roller;
    lv_obj_t * ui_func_menu_repeat;
    lv_obj_t * ui_func_menu_repeat_roller;
    lv_obj_t * ui_func_menu_shuffle;
    lv_obj_t * ui_func_menu_shuffle_roller;

    lv_obj_t * ui_keyboard_panel;
    lv_obj_t * ui_keyboar;

    lv_obj_t * menu_group_obj;

	lv_anim_t * logo_anim;
#ifdef USE_ALBUM_BG_FG
    lv_img_dsc_t * snapshot;
#endif

    //broadcast
    __u8 broadcast_source_flag;

    //playing top bar
    __u8 low_voltage_flag;
    __u8 charge_flag;
    __u8 eq_flag;

    //theme
    __u32 theme;//__THEME_TYPE
    __u32 theme_bmp_start;
    __u32 theme_bmp_end;
    __u32 menu_win;
    __u8  bt_artwork_type;

    __u8 repeat_mode;
    __u8 random_mode;

    __u32 bottom_fouse_flag;
    __u32 bottom_fouse_index;

	H_WIN dialog_win;

    void * source_obj;
    ui_source_para_t * source_para;
    lv_anim_t * menu_anim[6];
    ui_music_data_para_t ui_music_data_para;
	__u8 have_album_tags;
#endif
#ifdef USB_BROSWER_ALBUM_ARTIST_SORT
	void*album_list_hdle;
	__u32 album_total;
	void*artist_list_hdle;
	__u32 artist_total;
	void*title_list_hdle;
	__u32 title_total;
	char cur_music_path[RAT_MAX_FULL_PATH_LEN];

	__u8 into_list_sort_flag;
	__u8 list_sort_layer;
	__u8 song_layer_flag;
	__u8 list_sort_type; //0:album 1:artist 2:title
	__u32 cur_album_index; 
	__u32 cur_artist_index; 
	__u32 cur_title_index; 
	__u8 select_play_flag;
#endif

	lv_indev_data_t * indev_data;
	rat_media_type_t menu_media_type;
	lv_obj_t * music_main_obj;
	lv_obj_t * ui_list_obj;
	ui_music_res_para_t ui_music_res_para;
	explr_list_para_t ui_list_para;
	__gui_msg_t 		music_msg;
	bool mode_uninit_flag;
	
	// MCU Protocol v2 communication interface
	mcu_data_ops_t mcu_ops;
}ui_music_para_t;

extern ui_music_para_t * ui_music_para;

//ui_music_helpers.c
#ifdef USB_BROSWER_ALBUM_ARTIST_SORT
extern __s32 ui_music_explorer_Jumpto_sort_dir(ui_music_para_t *music_para, explr_list_para_t *list_para);
#endif
extern __s32 __music_cmd2parent(H_WIN hwin, __s32 id, __s32 data1, __s32 data2);
extern void music_player_mute_on(ui_music_para_t *music_para);
extern void music_player_mute_off(ui_music_para_t *music_para);
extern __u8 is_force_mute(void);
extern void music_force_mute_on(void);
extern void music_force_mute_off(void);
extern __s32 music_player_device_init(ui_music_para_t *music_para);
extern __s32 music_player_device_uninit(ui_music_para_t *music_para);
extern rat_media_type_t MusicGetListItemFileMediaType(HRAT rat_handle, __s32 ItemIndex);
extern void __music_player_ctrl_ratio_time(ui_music_para_t *music_para,__s32 cur_time);
extern __s32 music_player_rat_init(ui_music_para_t *music_para);
extern __s32 music_player_rat_uninit(ui_music_para_t *music_para);
extern __s32 MusicGetPlayerItemFileFullPath(HRAT rat_handle, __s32 ItemIndex, char *FilePath);
extern __s32 music_player_get_last_playing_para(ui_music_para_t *music_para);
extern __s32 music_player_save_playing_last_para(ui_music_para_t *music_para);
extern __s32 music_player_check_play_status_on_timer(ui_music_para_t *music_para);
extern __s32 music_player_get_last_para(ui_music_para_t *music_para);
extern __s32 music_player_save_last_para(ui_music_para_t *music_para);
extern __s32 music_player_get_breakpoint_info(ui_music_para_t *music_para);
extern __s32 music_player_save_breakpoint_info(ui_music_para_t *music_para);
extern __s32 music_explorer_on_next_key(ui_music_para_t * music_para);
extern __s32 music_explorer_on_previous_key(ui_music_para_t * music_para);
extern __s32 music_player_set_play_pause(ui_music_para_t *music_para);
extern __s32 music_player_set_play_backward(ui_music_para_t *music_para);
extern __s32 music_player_set_play_forward(ui_music_para_t *music_para);
extern __s32 __music_get_audio_file_info(ui_music_para_t *music_para, char *filename);
#ifdef USB_PLAYER_SUPPORT_M4A_FILE
extern __s32 music_chcek_play_file_is_m4a(ui_music_para_t *music_para, char *filename);
extern __s32 music_chcek_play_file_is_wma(ui_music_para_t *music_para, char *filename);
#endif
extern __s32 music_player_update_play_progress(ui_music_para_t * music_para);
extern __s32 __music_play_file(ui_music_para_t * music_para, char *filename);
extern __s32 music_player_check_play_status_on_timer(ui_music_para_t *music_para);
extern __s32 __music_player_win_on_timer(ui_music_para_t * music_para);
extern __s32 music_player_win_on_paint(ui_music_para_t * music_para);
extern __s32 music_player_win_on_destroy(ui_music_para_t * music_para);
extern void __ui_music_player_ctrl_play_pause(ui_music_para_t * music_para);
extern void __ui_music_player_ctrl_prev_next(ui_music_para_t * music_para, __u8 prev_next);
extern void __ui_music_player_ctrl_random(ui_music_para_t * music_para);
extern void __ui_music_player_ctrl_repeat(ui_music_para_t * music_para);
#ifdef USE_GXPMX30_MODEL
extern void __ui_music_player_ctrl_random_by_mode(ui_music_para_t * music_para, __u8 play_mode);
extern void __ui_music_player_ctrl_repeat_by_mode(ui_music_para_t * music_para, __u8 play_mode);
#endif
extern __s32 __ui_music_player_ctrl_ff_rr_press(ui_music_para_t * music_para, __u8 prev_next);
extern __s32 __ui_music_player_ctrl_ff_rr_release(ui_music_para_t * music_para, __u8 prev_next);
extern __u32 ui_music_get_track_length(ui_music_para_t * music_para);
extern void __ui_music_set_mute(ui_music_para_t * music_para);
extern ui_music_para_t * ui_music_get_para(void);
extern __s32 ui_music_helpers_init(ui_music_para_t * music_para, void * para);
extern __s32 ui_music_helpers_uninit(ui_music_para_t * music_para);


//ui_music_res.c
extern lv_img_dsc_t * ui_music_get_res(ui_music_para_t * music_para, __u32 icon);
extern void ui_music_init_res(ui_music_para_t * music_para);
extern void ui_music_uninit_res(ui_music_para_t * music_para);


//ui_music.c
extern __s32 __music_tips_dialog_create(ui_music_para_t *music_para,__s32 title_id,__s32 content_id,__u32 time_out,__s32 id);
extern __s32 __music_tips_dialog_cancel(ui_music_para_t *music_para);
extern __s32 __music_tips_dialog_confirm(ui_music_para_t *music_para);
extern void __music_update_low_voltage(ui_music_para_t * music_para, __u8 update);
extern void __music_update_charge(ui_music_para_t * music_para, __u8 update);
extern void __music_update_eq(ui_music_para_t * music_para, __u8 update);
#ifdef DELAY_DRAW_MUSIC_ALBUM_PIC
extern __s32 ResumeDrawMusicRootType(ui_music_para_t *music_para);
#endif
extern __s32 DrawMusicPlayer(ui_music_para_t *music_para);
extern __s32 DrawMusicProgressBar(__u32 total, __u32 cur_value);
extern __s32 __DrawMusicDrawPic(ui_music_para_t *music_para, __u32 bmp);
extern __s32 __DrawMusicBottom(ui_music_para_t *music_para);
extern __s32 __DrawMusicRootTypeorAlbumTagPic(ui_music_para_t *music_para);
extern __s32 __DrawMusicPlaySongNum(ui_music_para_t *music_para,__u32 song_num);
extern __s32 __DrawMusicFileArtist(ui_music_para_t *music_para,char *artist_name, __epdk_charset_enm_e charset);
extern __s32 __DrawMusicFileAlbum(ui_music_para_t *music_para,char *album_name, __epdk_charset_enm_e charset);
extern __s32 __DrawMusicFileName(ui_music_para_t *music_para,char *file_name, __epdk_charset_enm_e charset);
extern __s32 __DrawMusicPlayingTime(__u32 time);
extern __s32 __CleanMusicTotalTime(void);
extern __s32 __DrawMusicProgressBarPoint(__u32 total, __u32 cur_value);
extern __s32 __DrawMusicPlayerFileInfo(ui_music_para_t *music_para, audio_file_info_t *file_info);
#ifdef USE_GXPMX30_MODEL
extern __s32 __music_draw_bt_status(ui_music_para_t *music_para);
extern __s32 __music_draw_bt_phone_text(ui_music_para_t *music_para);
extern __s32 __DrawMusicRandom(ui_music_para_t *music_para);
extern __s32 __DrawMusicRepeat(ui_music_para_t *music_para);
extern __s32 __DrawMusicPlayPause(ui_music_para_t *music_para);
#endif
extern __s32 __music_player_long_string_stop_roll(ui_music_para_t * music_para);
#ifdef PREV_NEXT_SONG_SLIDE_OUT
extern __s32 __MusicPlayInfoSlideShow(ui_music_para_t *music_para,__u8 prev_next);
#endif
extern void __ui_music_power_on(ui_music_para_t * music_para);
extern void __ui_music_power_off(ui_music_para_t * music_para);
extern void __ui_music_remove_hidden(ui_music_para_t * music_para);
extern void __ui_music_add_hidden(ui_music_para_t * music_para);
extern void __ui_music_layer_on(ui_music_para_t * music_para);
extern void __ui_music_layer_sleep(ui_music_para_t * music_para);


//ui_music_events.c
extern __s32 ui_music_opn_msg(__gui_msg_t * pmsg, __u32 sync);
extern __s32 ui_music_refresh_msg(__gui_msg_t * pmsg, __u32 sync);
extern __s32 ui_music_send_srv_msg(__s32 id, unsigned long data1, unsigned long data2, unsigned long dwReserved, void *p_arg, __u32 flag, __u32 sync);
extern __s32 ui_music_send_opn_msg(__s32 id, unsigned long data1, unsigned long data2, unsigned long dwReserved, void *p_arg, __u32 flag, __u32 sync);
extern __s32 music_cmd2parent(H_WIN hwin, __s32 id, __s32 data1, __s32 data2);
extern __s32 music_tips_dialog_create(ui_music_para_t *music_para,__s32 title_id,__s32 content_id,__u32 time_out,__s32 id);
extern __s32 music_tips_dialog_cancel(ui_music_para_t *music_para);
extern __s32 music_tips_dialog_confirm(ui_music_para_t *music_para);
extern __s32 music_update_low_voltage(ui_music_para_t * music_para, __u8 update);
extern __s32 music_update_charge(ui_music_para_t * music_para, __u8 update);
extern __s32 music_update_eq(ui_music_para_t * music_para, __u8 update);
extern __s32 DrawMusicDrawPic(ui_music_para_t *music_para, __u32 bmp, __u32 sync);
extern __s32 DrawMusicBottom(ui_music_para_t *music_para);
extern __s32 DrawMusicRootTypeorAlbumTagPic(ui_music_para_t *music_para);
extern __s32 DrawMusicPlaySongNum(ui_music_para_t *music_para,__u32 song_num);
extern __s32 DrawMusicFileArtist(ui_music_para_t *music_para,char *artist_name, __epdk_charset_enm_e charset);
extern __s32 DrawMusicFileAlbum(ui_music_para_t *music_para,char *album_name, __epdk_charset_enm_e charset);
extern __s32 DrawMusicFileName(ui_music_para_t *music_para,char *file_name, __epdk_charset_enm_e charset);
extern __s32 DrawMusicPlayingTime(__u32 time);
extern __s32 CleanMusicTotalTime(void);
extern __s32 DrawMusicProgressBarPoint(__u32 total, __u32 cur_value);
extern __s32 DrawMusicPlayerFileInfo(ui_music_para_t *music_para, audio_file_info_t *file_info);
#ifdef USE_GXPMX30_MODEL
extern __s32 music_draw_bt_status(ui_music_para_t *music_para);
extern __s32 music_draw_bt_phone_text(ui_music_para_t *music_para);
extern __s32 DrawMusicRandom(ui_music_para_t *music_para);
extern __s32 DrawMusicRepeat(ui_music_para_t *music_para);
extern __s32 DrawMusicPlayPause(ui_music_para_t *music_para);
#endif
extern __s32 music_player_long_string_stop_roll(ui_music_para_t * music_para);
#ifdef PREV_NEXT_SONG_SLIDE_OUT
extern __s32 MusicPlayInfoSlideShow(ui_music_para_t *music_para,__u8 prev_next);
#endif
extern __s32 music_open_normal_win(ui_music_para_t * music_para);
extern __s32 music_close_normal_win(ui_music_para_t * music_para);
extern __s32 music_open_funtion_win(ui_music_para_t * music_para);
extern __s32 music_close_funtion_win(ui_music_para_t * music_para);
extern __s32 ui_music_power_on(ui_music_para_t * music_para);
extern __s32 ui_music_power_off(ui_music_para_t * music_para);
extern __s32 ui_music_remove_hidden(ui_music_para_t * music_para);
extern __s32 ui_music_add_hidden(ui_music_para_t * music_para);
extern __s32 ui_music_layer_on(ui_music_para_t * music_para);
extern __s32 ui_music_layer_sleep(ui_music_para_t * music_para);
extern __s32 music_player_ctrl_ratio_time(ui_music_para_t *music_para,__s32 cur_time);
extern __s32 music_get_audio_file_info(ui_music_para_t *music_para, char *filename);
extern __s32 music_play_file(ui_music_para_t * music_para, char *filename);
extern __s32 music_player_win_on_timer(ui_music_para_t * music_para);
extern __s32 ui_music_player_ctrl_play_pause(ui_music_para_t * music_para);
extern __s32 ui_music_player_ctrl_prev_next(ui_music_para_t * music_para, __u8 prev_next);
extern __s32 ui_music_player_ctrl_random(ui_music_para_t * music_para);
extern __s32 ui_music_player_ctrl_repeat(ui_music_para_t * music_para);
#ifdef USE_GXPMX30_MODEL
extern __s32 ui_music_player_ctrl_random_by_mode(ui_music_para_t * music_para, __u8 play_mode);
extern __s32 ui_music_player_ctrl_repeat_by_mode(ui_music_para_t * music_para, __u8 play_mode);
#endif
extern __s32 ui_music_player_ctrl_ff_rr_press(ui_music_para_t * music_para, __u8 prev_next);
extern __s32 ui_music_player_ctrl_ff_rr_release(ui_music_para_t * music_para, __u8 prev_next);
extern __s32 ui_music_set_mute(ui_music_para_t * music_para);

#ifdef __cplusplus
} /*extern "C"*/
#endif

#endif
