// SquareLine LVGL GENERATED FILE
// EDITOR VERSION: SquareLine Studio 1.1.1
// LVGL VERSION: 8.3.3
// PROJECT: SquareLine_Project

#include "ui_music.h"
#include "ui_music_helpers.h"
#include "ui_music_res.h"
#include "ui_helpers.h"
#include "ui_home.h"
#include "misc.h"
#include "libc/elibs_charset.h"
#include "GaussBlur.h"

#if 1//USE_LOG_PRINT
	#define __music_msg(...)			(eLIBs_printf("Music MSG:L%d:", __LINE__),				 \
													  eLIBs_printf(__VA_ARGS__)) 
#else
	#define __music_msg(...)
#endif

typedef enum __A_AUDIO_FONTTYPE
{
    A_AUDIO_FONTTYPE_ISOIEC8859_1 = 0,  //ISO/IEC 8859-1
    A_AUDIO_FONTTYPE_UTF_16LE,
    A_AUDIO_FONTTYPE_UTF_16BE,
    A_AUDIO_FONTTYPE_UTF_8,
    A_AUDIO_FONTTYPE_ = -1
} __a_audio_fonttype_e;

#ifdef USB_BROSWER_ALBUM_ARTIST_SORT
extern void*get_music_album_list(void);
extern void*get_music_artist_list(void);
extern void*get_music_title_list(void);

extern __u32 get_music_album_total(void);
extern __u32 get_music_artist_total(void);
extern __u32 get_music_title_total(void);
#ifdef USB_BROSWER_SORT_AT_BACKGROUND
extern __u8 get_music_browser_flag(void);
#endif

#endif

///////////////////// VARIABLES ////////////////////
ui_music_para_t * ui_music_para = NULL;
static void ui_event_music_back(lv_event_t * e);
static void ui_event_music_menu_item(lv_event_t * e);
static void ui_event_music_timer_cb(lv_timer_t * t);
static void ui_music_source_event_handler(ui_music_para_t * music_para);
static void ui_music_top_left_event_handler(ui_music_para_t * music_para);
static void ui_music_top_right_event_handler(ui_music_para_t * music_para);
static void ui_music_open_bar_menu_event_handler(ui_music_para_t * music_para);
static void ui_music_top_menu_event_handler(ui_music_para_t * music_para);
static void ui_music_bottom_menu_event_handler(ui_music_para_t * music_para);
static void ui_music_source_title(ui_music_para_t * music_para, __u8 * str, __u8 source_flag);

// SCREEN: ui_music
static void ui_music_broadcast_source_screen_init(ui_music_para_t * music_para);
static void ui_music_broadcast_source_screen_uninit(ui_music_para_t * music_para);
static void ui_music_menu_screen_init(ui_music_para_t * music_para);
static void ui_music_menu_screen_uninit(ui_music_para_t * music_para);
static void ui_music_func_menu_screen_init(ui_music_para_t * music_para);
static void ui_music_func_menu_screen_uninit(ui_music_para_t * music_para);;
static void ui_music_screen_init(ui_music_para_t * music_para);
static void ui_music_screen_uninit(ui_music_para_t * music_para);

static void ui_music_set_obj_theme_src(void * para, lv_obj_t * obj, __u32 theme);
static void ui_music_bottom_menu_event_handler(ui_music_para_t * music_para);
static void ui_event_music_func_item(lv_event_t * e);
static __s32 music_player_ui_update_timer_proc(ui_music_para_t * music_para);
static void ui_music_func_menu_screen_init(ui_music_para_t * music_para);

lv_obj_t * ui_music;
lv_obj_t * ui_music_play_screen;
lv_obj_t * ui_music_bg;
lv_obj_t * ui_music_bg_fg;
lv_obj_t * ui_music_play;
lv_obj_t * ui_music_play_broadcast_bg;
lv_obj_t * ui_music_play_top;
lv_obj_t * ui_music_play_top_low;
lv_obj_t * ui_music_play_top_charge;
lv_obj_t * ui_music_play_top_eq;
lv_obj_t * ui_music_play_mid;
lv_obj_t * ui_music_play_logo_panel;
lv_obj_t * ui_music_play_logo;
lv_obj_t * ui_music_play_logo_text;
lv_obj_t * ui_music_logo_panel;
lv_obj_t * ui_music_logo;
lv_obj_t * ui_music_play_disp;
lv_obj_t * ui_music_id3;
lv_obj_t * ui_music_id3_text;
lv_obj_t * ui_music_id3_text_panel;
lv_obj_t * ui_music_id3_text_panel_artist;
lv_obj_t * ui_music_id3_text_panel_artist_text;
lv_obj_t * ui_music_id3_text_panel_song;
lv_obj_t * ui_music_id3_text_panel_song_text;
lv_obj_t * ui_music_id3_text_panel_album;
lv_obj_t * ui_music_id3_text_panel_album_text;
lv_obj_t * ui_music_bt_status;
lv_obj_t * ui_music_bt_status_text;
lv_obj_t * ui_music_play_bottom;
lv_obj_t * ui_music_play_bottom_current;
lv_obj_t * ui_music_play_bottom_bar;
lv_obj_t * ui_music_play_bottom_dual_phone_panel;
lv_obj_t * ui_music_play_bottom_dual_phone;
lv_obj_t * ui_music_play_bottom_dual_phone_text;
lv_obj_t * ui_music_play_bottom_repeat_random;
lv_obj_t * ui_music_play_bottom_repeat;
lv_obj_t * ui_music_play_bottom_random;
lv_obj_t * ui_music_bottom_list;
lv_obj_t * ui_music_bottom_list_img;
lv_obj_t * ui_music_bottom_browse;
lv_obj_t * ui_music_bottom_browse_img;
lv_obj_t * ui_music_bottom_repeat;
lv_obj_t * ui_music_bottom_repeat_img;
lv_obj_t * ui_music_bottom_random;
lv_obj_t * ui_music_bottom_random_img;

lv_obj_t * ui_music_menu;

static __u8 music_slider_anim_flag = 0;
static __u8 music_slider_value = 0;

static void ui_music_night_theme(ui_music_para_t * music_para, __s32 theme);
__u32 MUSIC_THEME_TEMP_MEDIA_SETUP_REPEAT_B_BMP = 0;  
__u32 MUSIC_THEME_USB_ICON_BMP = 0;
__u32 MUSIC_THEME_BT_ICON_BMP = 0;
__u32 MUSIC_THEME_COM_BOT_BG_BMP = 0;  
__u32 MUSIC_THEME_COM_BOT_ICON_BG_A_N_BMP = 0;  
__u32 MUSIC_THEME_COM_BOT_ICON_BG_A_P_S_BMP = 0; 
__u32 MUSIC_THEME_COM_BOT_ICON_BG_B_N_BMP = 0;   
__u32 MUSIC_THEME_COM_BOT_ICON_BG_B_P_S_BMP = 0; 
__u32 MUSIC_THEME_COM_BOT_MEDIA_PAUSE_BMP = 0;   
__u32 MUSIC_THEME_COM_BOT_MEDIA_PAUSE_D_BMP = 0;
__u32 MUSIC_THEME_COM_BOT_MEDIA_PLAY_BMP = 0;    
__u32 MUSIC_THEME_COM_BOT_MEDIA_PLAY_D_BMP = 0;
__u32 MUSIC_THEME_COM_BOT_MEDIA_DOWN_N_BMP = 0;  
__u32 MUSIC_THEME_COM_BOT_MEDIA_DOWN_P_S_BMP = 0;
__u32 MUSIC_THEME_COM_BOT_MEDIA_UP_N_BMP = 0;    
__u32 MUSIC_THEME_COM_BOT_MEDIA_UP_P_S_BMP = 0;

__u32 MUSIC_THEME_COM_BOT_ICON_BG_A_L_N_BMP = 0;
__u32 MUSIC_THEME_COM_BOT_ICON_BG_A_L_S_BMP = 0;

__u32 MUSIC_THEME_COM_BOT_LIST_N_BMP = 0;
__u32 MUSIC_THEME_COM_BOT_LIST_D_BMP = 0;

__u32 MUSIC_THEME_COM_BOT_BROWSE_N_BMP = 0;
__u32 MUSIC_THEME_COM_BOT_BROWSE_D_BMP = 0;

__u32 MUSIC_THEME_COM_BOT_DIRECT_N_BMP = 0;
__u32 MUSIC_THEME_COM_BOT_DIRECT_D_BMP = 0;


__u32 MUSIC_THEME_COM_BOT_PHONE_A_N_BMP = 0;
__u32 MUSIC_THEME_COM_BOT_PHONE_B_N_BMP = 0;

__u32 MUSIC_THEME_COM_BOT_BT_CONNECT_N_BMP = 0;

__u32 MUSIC_THEME_COM_BOT_RANDOM_A_N_BMP = 0;
__u32 MUSIC_THEME_COM_BOT_RANDOM_A_D_BMP = 0;
__u32 MUSIC_THEME_COM_BOT_RANDOM_B_N_BMP = 0;
__u32 MUSIC_THEME_COM_BOT_RANDOM_B_D_BMP = 0;

__u32 MUSIC_THEME_COM_BOT_REPEAT_A_N_BMP = 0;
__u32 MUSIC_THEME_COM_BOT_REPEAT_A_D_BMP = 0;
__u32 MUSIC_THEME_COM_BOT_REPEAT_B_N_BMP = 0;
__u32 MUSIC_THEME_COM_BOT_REPEAT_B_D_BMP = 0;
__u32 MUSIC_THEME_COM_BOT_REPEAT_C_N_BMP = 0;
__u32 MUSIC_THEME_COM_BOT_REPEAT_C_D_BMP = 0;

__u32 MUSIC_THEME_COM_BRO_ALBUM_BMP = 0;
__u32 MUSIC_THEME_COM_BRO_ART_BMP = 0;
__u32 MUSIC_THEME_COM_BRO_FOLDER_BMP = 0;
__u32 MUSIC_THEME_COM_BRO_GENRES_BMP = 0;
__u32 MUSIC_THEME_COM_BRO_SONG_BMP = 0;

__u32 MUSIC_THEME_TEMP_COM_LIST_OFF_BMP = 0;          
__u32 MUSIC_THEME_TEMP_MEDIA_DUAL_PHONE_A_BMP = 0;    
__u32 MUSIC_THEME_TEMP_COM_RIG_BG_A_BMP = 0;          
__u32 MUSIC_THEME_TEMP_COM_RIG_ARROW_A_BMP = 0;       
__u32 MUSIC_THEME_TEMP_MEDIA_SETUP_REPEAT_A_BMP = 0;  
__u32 MUSIC_THEME_TEMP_COM_RIG_AUDIO_ICON_S_BMP = 0;  
__u32 MUSIC_THEME_TEMP_COM_RIG_AUDIO_ICON_N_BMP = 0;  
__u32 MUSIC_THEME_TEMP_MEDIA_DUAL_PHONE_B_BMP = 0;    
__u32 MUSIC_THEME_TEMP_COM_TOP_BACK_ICON_BMP = 0;     
__u32 MUSIC_THEME_TEMP_COM_MEDIA_ALBUM_BG_BMP = 0;    
__u32 MUSIC_THEME_TEMP_MEDIA_SETUP_RANDOM_A_BMP = 0;  
__u32 MUSIC_THEME_TEMP_COM_RIG_FUNC_ICON_S_BMP = 0;   
__u32 MUSIC_THEME_TEMP_COM_RIG_ARROW_B_BMP = 0;       
__u32 MUSIC_THEME_TEMP_COM_LOW_VOLTAGE_BMP = 0;       
__u32 MUSIC_THEME_TEMP_COM_RIG_BG_C_BMP = 0;          
__u32 MUSIC_THEME_TEMP_COM_TOP_ICON_BG_N_BMP = 0;     
__u32 MUSIC_THEME_TEMP_COM_LIST_ON_BMP = 0;           
__u32 MUSIC_THEME_TEMP_COM_LIST_BG_BMP = 0;           
__u32 MUSIC_THEME_TEMP_COM_RIG_BG_B_BMP = 0;          
__u32 MUSIC_THEME_TEMP_COM_RIG_ICON_BG_N_BMP = 0;     
__u32 MUSIC_THEME_TEMP_MEDIA_SETUP_RANDOM_B_BMP = 0;  
__u32 MUSIC_THEME_TEMP_COM_RIG_ZONE_ICON_BMP = 0;     
__u32 MUSIC_THEME_TEMP_COM_RIG_ICON_BG_P_BMP = 0;     
__u32 MUSIC_THEME_TEMP_COM_BG_BMP = 0;                
__u32 MUSIC_THEME_TEMP_COM_PD_CHARGE_BMP = 0;         
__u32 MUSIC_THEME_TEMP_MEDIA_SETUP_REPEAT_C_BMP = 0;  
__u32 MUSIC_THEME_TEMP_COM_PUNCH_EQ_BMP = 0;          
__u32 MUSIC_THEME_TEMP_COM_TOP_SOURCE_ICON_BMP = 0;   
__u32 MUSIC_THEME_TEMP_COM_RIG_ARROW_C_BMP = 0;       
__u32 MUSIC_THEME_TEMP_COM_TOP_BG_BMP = 0;            
__u32 MUSIC_THEME_TEMP_COM_RIG_ZONE_ICON_S_BMP = 0;   
__u32 MUSIC_THEME_TEMP_COM_RIG_ZONE_ICON_N_BMP = 0;   
__u32 MUSIC_THEME_TEMP_COM_RIG_AUDIO_ICON_BMP = 0;    
__u32 MUSIC_THEME_TEMP_COM_TOP_LIST_ICON_BMP = 0;     
__u32 MUSIC_THEME_TEMP_MEDIA_DUAL_PHONE_D_BMP = 0;    
__u32 MUSIC_THEME_TEMP_COM_RIG_FUNC_ICON_N_BMP = 0;   
__u32 MUSIC_THEME_TEMP_COM_TOP_ICON_BG_P_S_BMP = 0;   
__u32 MUSIC_THEME_TEMP_COM_RIG_FUNC_ICON_BMP = 0;     

lv_timer_t  * ui_music_timer = NULL;

///////////////////// TEST LVGL SETTINGS ////////////////////
#if LV_COLOR_DEPTH != 32
    #error "LV_COLOR_DEPTH should be 32bit to match SquareLine Studio's settings"
#endif
#if LV_COLOR_16_SWAP !=0
    #error "LV_COLOR_16_SWAP should be 0 to match SquareLine Studio's settings"
#endif

///////////////////// PLAYER FACE ////////////////////
__s32 __music_tips_dialog_create(ui_music_para_t *music_para,__s32 title_id,__s32 content_id,__u32 time_out,__s32 id)
{
	__s32 str[2] = {0};
	str[0] = title_id;
	str[1] = content_id;

	if(music_para->dialog_win)
	{
		ui_dialog_destroy(music_para->dialog_win);
		music_para->dialog_win = NULL;
	}
	
	music_para->dialog_win = ui_dialog_create(lv_layer_top(), APP_MUSIC_DIALOG_ID, id, str,time_out, 0);	
    
	return EPDK_OK;
}

__s32 __music_tips_dialog_cancel(ui_music_para_t *music_para)
{
	if(music_para->dialog_win)
	{
		ui_dialog_destroy(music_para->dialog_win);
		music_para->dialog_win = NULL;
	}
        
	return EPDK_OK;
}

__s32 __music_tips_dialog_confirm(ui_music_para_t *music_para)
{
    music_tips_dialog_cancel(music_para);
    
	return EPDK_OK;
}

void __music_update_low_voltage(ui_music_para_t * music_para, __u8 update)
{		
    if(music_para->low_voltage_flag == update)
    {
        return;
    }

    music_para->low_voltage_flag = update;
    
	if(music_para->low_voltage_flag)
	{
		if(lv_obj_has_flag(ui_music_play_top_low, LV_OBJ_FLAG_HIDDEN))
		{
			lv_obj_clear_flag(ui_music_play_top_low, LV_OBJ_FLAG_HIDDEN);
		}
	}
	else
	{
		if(!lv_obj_has_flag(ui_music_play_top_low, LV_OBJ_FLAG_HIDDEN))
			lv_obj_add_flag(ui_music_play_top_low, LV_OBJ_FLAG_HIDDEN);
	}
}

void __music_update_charge(ui_music_para_t * music_para, __u8 update)
{		
    if(music_para->charge_flag == update)
    {
        return;
    }

    music_para->charge_flag = update;
    
	if(music_para->charge_flag)
	{
		if(lv_obj_has_flag(ui_music_play_top_charge, LV_OBJ_FLAG_HIDDEN))
		{
			lv_obj_clear_flag(ui_music_play_top_charge, LV_OBJ_FLAG_HIDDEN);
		}
	}
	else
	{
		if(!lv_obj_has_flag(ui_music_play_top_charge, LV_OBJ_FLAG_HIDDEN))
			lv_obj_add_flag(ui_music_play_top_charge, LV_OBJ_FLAG_HIDDEN);
	}
}

void __music_update_eq(ui_music_para_t * music_para, __u8 update)
{		
    if(music_para->eq_flag == update)
    {
        return;
    }

    music_para->eq_flag = update;
    
	if(music_para->eq_flag)
	{
		if(lv_obj_has_flag(ui_music_play_top_eq, LV_OBJ_FLAG_HIDDEN))
		{
			lv_obj_clear_flag(ui_music_play_top_eq, LV_OBJ_FLAG_HIDDEN);
		}
	}
	else
	{
		if(!lv_obj_has_flag(ui_music_play_top_eq, LV_OBJ_FLAG_HIDDEN))
			lv_obj_add_flag(ui_music_play_top_eq, LV_OBJ_FLAG_HIDDEN);
	}
}

__s32 __DrawMusicDrawPic(ui_music_para_t *music_para, __u32 bmp)
{
	if(ui_music_logo == NULL)
	{
		return;
	}
	
	if(bmp < MAX_MUSIC_BMP_ITEM)
	{
		//eLIBs_printf("DrawMusicDrawPic00 time=%d\n",esKRNL_TimeGet());
		lv_img_set_src(ui_music_logo, ui_music_get_res(music_para, bmp));
#ifdef USE_GXPMX30_MODEL
        lv_obj_add_flag(ui_music_bg, LV_OBJ_FLAG_HIDDEN);    /// Flags
		lv_obj_add_flag(ui_music_logo_panel, LV_OBJ_FLAG_HIDDEN);//ui_music_logo
		lv_obj_clear_flag(ui_music_play_logo, LV_OBJ_FLAG_HIDDEN); 	/// Flags
		if(music_para->logo_anim)
		{
			//lv_anim_del(music_para->logo_anim, NULL);
			music_para->logo_anim = NULL;
		}
#ifdef USE_ALBUM_BG_FG
        if(music_para->snapshot)
        {
            lv_snapshot_free(music_para->snapshot);
            music_para->snapshot = NULL;
        }
#endif
#endif
	}
	else
	{
		//eLIBs_printf("DrawMusicDrawPic11 time=%d\n",esKRNL_TimeGet());
		music_para->ui_music_res_para.lv_music_album_logo.header.w = music_para->AlbumArt.width;
		music_para->ui_music_res_para.lv_music_album_logo.header.h = music_para->AlbumArt.height;
		music_para->ui_music_res_para.lv_music_album_logo.data_size = music_para->AlbumArt.width * music_para->AlbumArt.height * 4;
		music_para->ui_music_res_para.lv_music_album_logo.header.cf = LV_COLOR_FORMAT_ARGB8888;
		music_para->ui_music_res_para.lv_music_album_logo.data = music_para->AlbumArt.buf;
		lv_img_set_src(ui_music_logo, &music_para->ui_music_res_para.lv_music_album_logo);
#ifdef USE_GXPMX30_MODEL
        if(music_para->AlbumArtBg.buf)
        {
    		lv_obj_clear_flag(ui_music_logo_panel, LV_OBJ_FLAG_HIDDEN);//ui_music_logo
    		lv_obj_add_flag(ui_music_play_logo, LV_OBJ_FLAG_HIDDEN);
    		if(music_para->logo_anim == NULL)
    		{
    			//music_para->logo_anim = img_rorate_Animation(ui_music_logo, LV_OBJ_FLAG_HIDDEN);
    		}

            lv_obj_clear_flag(ui_music_bg, LV_OBJ_FLAG_HIDDEN);    /// Flags
    		music_para->ui_music_res_para.lv_music_album_bg.header.w = music_para->AlbumArtBg.width;
    		music_para->ui_music_res_para.lv_music_album_bg.header.h = music_para->AlbumArtBg.height;
    		music_para->ui_music_res_para.lv_music_album_bg.data_size = music_para->AlbumArtBg.width * music_para->AlbumArtBg.height * 4;
    		music_para->ui_music_res_para.lv_music_album_bg.header.cf = LV_COLOR_FORMAT_XRGB8888;
    		music_para->ui_music_res_para.lv_music_album_bg.data = music_para->AlbumArtBg.buf;
            lv_image_set_src(ui_music_bg, &music_para->ui_music_res_para.lv_music_album_bg);
            
#ifdef USE_ALBUM_BG_FG
            lv_obj_clear_flag(ui_music_bg_fg, LV_OBJ_FLAG_HIDDEN);    /// Flags

            if(music_para->snapshot == NULL)
            {
                music_para->snapshot = lv_snapshot_take(ui_music_bg, LV_COLOR_FORMAT_XRGB8888);
                
                __music_msg("snapshot=0x%x\n", music_para->snapshot);
                if(music_para->snapshot)
                {
                    lv_obj_add_flag(ui_music_bg_fg, LV_OBJ_FLAG_HIDDEN);    /// Flags
                    lv_img_set_src(ui_music_bg, music_para->snapshot);
                }
            }
#endif
        }
#endif
		__music_msg("music_para->AlbumArt.width=%d,music_para->AlbumArt.height=%d.\n",music_para->AlbumArt.width,music_para->AlbumArt.height);
	}
}

static __s32 pic_gaussian_blur(__u8 *in_buff,__u8 *out_buff,__u32 width,__u32 height)
{
    int BytesPerLine;
    
    BytesPerLine = ((32 * width + 31) >> 5) << 2;
    //eLIBs_printf("BytesPerLine=%d\n", BytesPerLine);
    GaussianBlur(width,height,BytesPerLine,SIGMA,(int *)in_buff,(int *)out_buff);
}

static __s32 external_pic_buffer_decode(__u8 *pic_out_buf,__u8 *buff,__u32 buff_Size,__u32 PIC_WIDTH,__u32 PIC_HEIGHT)
{
	int ret = EPDK_FAIL;
	rat_miniature_para_t in_para;
	rat_pic_info_t out_para;

	__music_msg("enter pic buffer decode\n");
	if(pic_out_buf == NULL)
	{
		__msg("palloc fail!!\n");
		goto err_out;
	}

	if(buff == NULL || !buff_Size)
	{
		__msg("palloc fail!!\n");
		goto err_out;
	}

	eLIBs_memset(&in_para, 0, sizeof(rat_miniature_para_t));
	eLIBs_memset(&out_para, 0, sizeof(out_para));
	ret = rat_start_miniature_decode();
	if(ret!=EPDK_OK)
	{
		return EPDK_FAIL;
	}
	//input
	in_para.input_type = 1; //buffer input 
	in_para.input_buf = buff;
	in_para.input_size = buff_Size;
	in_para.format = PIXEL_COLOR_ARGB8888;
	in_para.width = PIC_WIDTH;
	in_para.height = PIC_HEIGHT;
	in_para.mode = WILLOW_SCALE_STRETCH;	// 1为拉伸模式
	//output
	out_para.miniature.buf = pic_out_buf;
	
	ret = rat_get_pic_info(&in_para, &out_para);

	__music_msg("pic buffer decode end\n");
	__music_msg("pic info, F:%d, W:%d, H:%d\n",out_para.format,out_para.width,out_para.height);
	__music_msg("miniature info, Len:%d, W:%d, H:%d\n",out_para.miniature.len,out_para.miniature.width,out_para.miniature.height);

	rat_stop_miniature_decode();

	return ret;

	err_out:
	return EPDK_FAIL;
}

static __s32 show_external_album_pic_argb(ui_music_para_t *music_para)
{
	 RECT rect;
	__s32 ret = 0;
	__u32 pic_w,pic_h;
	__u8 *pbuf = NULL; 
	reg_system_para_t*para_sys;
	
	para_sys = (reg_system_para_t*)dsk_reg_get_para_by_app(REG_APP_SYSTEM);
    
	pbuf = music_para->ui_music_data_para.artwork_buf;
	if(music_para->ui_music_data_para.artwork_status != ARTWORK_STATUS_READY)
		return EPDK_FAIL;

	if((music_para->tags_miniature_rect.width == 0) || (music_para->tags_miniature_rect.height == 0))
	{
		if(ui_music_logo)
		{
			music_para->tags_miniature_rect.x = lv_obj_get_x(ui_music_logo);
			music_para->tags_miniature_rect.y = lv_obj_get_y(ui_music_logo);
			music_para->tags_miniature_rect.width = lv_obj_get_width(ui_music_logo);
			music_para->tags_miniature_rect.height = lv_obj_get_height(ui_music_logo);
		}
	}
	rect.x = music_para->tags_miniature_rect.x;
	rect.y = music_para->tags_miniature_rect.y;
	rect.width = music_para->tags_miniature_rect.width;
	rect.height = music_para->tags_miniature_rect.height;

	if(music_para->ui_music_data_para.artwork_width == rect.width && music_para->ui_music_data_para.artwork_height == rect.height)
	{
	#ifdef REMOTE_USB_ALBUMART_YUV422
		if(music_para->ui_music_data_para.artwork_size == (rect.width*rect.height*2))
		{
			com_yuv422_to_argb(pbuf,rect.width,rect.height,music_para->ui_music_data_para.artwork_buf+500*1024,rect.width,rect.height);
			pbuf = music_para->ui_music_data_para.artwork_buf+500*1024;
			ret = EPDK_OK;
		}
		else
			ret = EPDK_FAIL;
	#else
		if(music_para->ui_music_data_para.artwork_size == (rect.width*rect.height*4))
			ret = EPDK_OK;
		else
			ret = EPDK_FAIL;
	#endif
		pic_w = rect.width;
		pic_h = rect.height;
	}
	else
	{
		ret = EPDK_FAIL;
	}
		
	if(ret == EPDK_OK)
	{
        music_para->AlbumArt.buf = pbuf;
        music_para->AlbumArt.width = rect.width;
        music_para->AlbumArt.height = rect.height;
        music_para->bt_artwork_type = ARTWORK_TYPE_ARGB888;
	}
	else
	{
		__msg("get albem info Fail\n\n");
		//esMEMS_Bfree(pBuf, in_para.width*in_para.height*4);
		return EPDK_FAIL;
	}
	
	if(ret == EPDK_OK)
	{

	}
		__music_msg("show album tag : %d\n",ret); 
	 return ret;
}

static __s32 show_external_album_pic_argb_bg(ui_music_para_t *music_para, __s32 tags)
{
	 RECT rect;
	__s32 ret = 0;
	__u32 pic_w,pic_h;
	__u8 *pbuf = NULL; 
	__u8 *pic_out_buf = NULL;
	reg_system_para_t*para_sys;
	
	para_sys = (reg_system_para_t*)dsk_reg_get_para_by_app(REG_APP_SYSTEM);

    if(tags == EPDK_OK)
    {
    	pbuf = music_para->ui_music_data_para.artwork_buf;
    	if(music_para->ui_music_data_para.artwork_status != ARTWORK_STATUS_READY)
    	{
            __music_msg("error artwork_status=%d\n",music_para->ui_music_data_para.artwork_status); 
    		return EPDK_FAIL;
    	}

    	if((music_para->tags_miniature_rect_bg.width == 0) || (music_para->tags_miniature_rect_bg.height == 0))
    	{
    		if(ui_music_bg)
    		{
    			music_para->tags_miniature_rect_bg.x = lv_obj_get_x(ui_music_bg);
    			music_para->tags_miniature_rect_bg.y = lv_obj_get_y(ui_music_bg);
    			music_para->tags_miniature_rect_bg.width = lv_obj_get_width(ui_music_bg);
    			music_para->tags_miniature_rect_bg.height = lv_obj_get_height(ui_music_bg);
    		}
    	}
    	rect.x = music_para->tags_miniature_rect_bg.x;
    	rect.y = music_para->tags_miniature_rect_bg.y;
    	rect.width = music_para->tags_miniature_rect_bg.width;
    	rect.height = music_para->tags_miniature_rect_bg.height;

    	//if(music_para->ui_music_data_para.artwork_width == rect.width && music_para->ui_music_data_para.artwork_height == rect.height)
    	{
    	#ifdef REMOTE_USB_ALBUMART_YUV422
    		//if(music_para->ui_music_data_para.artwork_size == (rect.width*rect.height*2))
    		{
    			com_yuv422_to_argb(pbuf,music_para->ui_music_data_para.artwork_width,music_para->ui_music_data_para.artwork_height,music_para->ui_music_data_para.artwork_buf+500*1024,music_para->ui_music_data_para.artwork_width,music_para->ui_music_data_para.artwork_height);
    			pbuf = music_para->ui_music_data_para.artwork_buf+500*1024;
    			ret = EPDK_OK;
    		}
    		//else
    		//	ret = EPDK_FAIL;
    	#else
    		//if(music_para->ui_music_data_para.artwork_size == (rect.width*rect.height*4))
    			ret = EPDK_OK;
    		//else
    		//	ret = EPDK_FAIL;
    	#endif
    		pic_w = rect.width;
    		pic_h = rect.height;
    	}
    	//else
    	//{
    	//	ret = EPDK_FAIL;
    	//}
        
    	if(ret == EPDK_OK)
    	{
            pic_out_buf = esMEMS_Palloc(((rect.width*rect.height*4)+1023)/1024, 0);
            
            if(pic_out_buf == NULL)
            {       
                __music_msg("memory is not enough!!\n");
                return EPDK_FAIL;
            }
            com_argb_buf_scale(pbuf, music_para->ui_music_data_para.artwork_width, music_para->ui_music_data_para.artwork_height, pic_out_buf, rect.width, rect.height);
            
            pic_gaussian_blur(pic_out_buf, pic_out_buf, rect.width, rect.height);
            
            music_para->AlbumArtBg.buf = pic_out_buf;
            music_para->AlbumArtBg.width = rect.width;
            music_para->AlbumArtBg.height = rect.height;
#if 0//def ABLUM_PICTURE_SET_BG_ENABLE
            if(para_sys->low_battery_showing_flag == 0 && music_para->visiable)//240702 added visiable for front Camare BT/USB music  //231211 added this flag for bug 111
            {
                gscene_bgd_set_img_src_buff(pbuf,pic_w,pic_h);//(pbuf,rect.width,rect.height);
            }
#endif	
    	}
    	else
    	{
    		__music_msg("get albem info Fail\n\n");
    		//esMEMS_Bfree(pBuf, in_para.width*in_para.height*4);
#if 0//def ABLUM_PICTURE_SET_BG_ENABLE
            if(music_para->visiable) //240702 added visiable for front Camare BT/USB music
            {
                gscene_bgd_set_img_src(BG_INDEX_AUTO);
            }
#endif		
    		return EPDK_FAIL;
    	}
    	
    	if(ret == EPDK_OK)
    	{

    	}
    	__music_msg("show album tag : %d\n",ret); 
    }
	return ret;
}

static __s32 show_external_album_pic(ui_music_para_t *music_para)
{
	 RECT rect;
	__s32 ret = 0;
	__u8 *pbuf = NULL; 
	__u8 *pic_out_buf = NULL;

	reg_root_para_t *root_para = NULL;	
	reg_system_para_t*para_sys;
	root_para = dsk_reg_get_para_by_app(REG_APP_ROOT);	
	para_sys = (reg_system_para_t*)dsk_reg_get_para_by_app(REG_APP_SYSTEM);
	
	pbuf = root_para->artwork_buf;
	if(root_para->artwork_status != ARTWORK_STATUS_READY)
		return EPDK_FAIL;
	
	if((music_para->tags_miniature_rect.width == 0) || (music_para->tags_miniature_rect.height == 0))
	{
		if(ui_music_logo)
		{
			music_para->tags_miniature_rect.x = lv_obj_get_x(ui_music_logo);
			music_para->tags_miniature_rect.y = lv_obj_get_y(ui_music_logo);
			music_para->tags_miniature_rect.width = lv_obj_get_width(ui_music_logo);
			music_para->tags_miniature_rect.height = lv_obj_get_height(ui_music_logo);
		}
	}
	rect.x = music_para->tags_miniature_rect.x;
	rect.y = music_para->tags_miniature_rect.y;
	rect.width = music_para->tags_miniature_rect.width;
	rect.height = music_para->tags_miniature_rect.height;

     if(music_para->AlbumArt.buf && music_para->AlbumArt.width && music_para->AlbumArt.height)
     {
         __music_msg("width=%d, height=%d\n", music_para->AlbumArt.width, music_para->AlbumArt.height);
         esMEMS_Pfree(music_para->AlbumArt.buf, ((music_para->AlbumArt.width*music_para->AlbumArt.height*4)+1023)/1024);
         __music_msg("here11\n");
         music_para->AlbumArt.buf = NULL;
         music_para->AlbumArt.width = 0;
         music_para->AlbumArt.height = 0;
     }
     pic_out_buf = esMEMS_Palloc(((rect.width*rect.height*4)+1023)/1024, 0);
     
     if(pic_out_buf == NULL)
     {       
         __music_msg("memory is not enough!!\n");
         return EPDK_FAIL;
     }
	 __music_msg("external_album_tag_data_converter \n");
	//ret = external_album_tag_data_converter(pic_out_buf,pbuf,root_para->artwork_size,rect.width,rect.height);
	ret = external_pic_buffer_decode(pic_out_buf,pbuf,root_para->artwork_size,rect.width,rect.height);
	__music_msg("ret=%d\n",ret);

	if(ret == EPDK_OK)
	{
        music_para->AlbumArt.buf = pic_out_buf;
        music_para->AlbumArt.width = rect.width;
        music_para->AlbumArt.height = rect.height;
        music_para->bt_artwork_type = ARTWORK_TYPE_PIC;
	}
	else
	{
		__msg("get albem info Fail\n\n");
		//esMEMS_Bfree(pBuf, in_para.width*in_para.height*4);
		return EPDK_FAIL;
	}
	
	if(ret == EPDK_OK)
	{

	}
	__music_msg("ret=%d\n",ret);

	/*if(pic_out_buf)
	{
		esMEMS_Pfree(pic_out_buf, OUTPUT_BUF_SIZE);
		pic_out_buf = NULL;
		__music_msg("free pic_out_buf OK!\n");
	}*/
	
	__music_msg("show album tag : %d\n",ret); 
	 return ret;
}

static __s32 show_external_album_pic_bg(ui_music_para_t *music_para, __s32 tags)
{
	 RECT rect;
	__s32 ret = 0;
	__u8 *pbuf = NULL; 
	__u8 *pic_out_buf = NULL;

	reg_root_para_t *root_para = NULL;	
	reg_system_para_t*para_sys;
	root_para = dsk_reg_get_para_by_app(REG_APP_ROOT);	
	para_sys = (reg_system_para_t*)dsk_reg_get_para_by_app(REG_APP_SYSTEM);

    if(tags == EPDK_OK)
    {
    	pbuf = root_para->artwork_buf;
    	if(root_para->artwork_status != ARTWORK_STATUS_READY)
    		return EPDK_FAIL;
    	
    	if((music_para->tags_miniature_rect_bg.width == 0) || (music_para->tags_miniature_rect_bg.height == 0))
    	{
    		if(ui_music_bg)
    		{
    			music_para->tags_miniature_rect_bg.x = lv_obj_get_x(ui_music_bg);
    			music_para->tags_miniature_rect_bg.y = lv_obj_get_y(ui_music_bg);
    			music_para->tags_miniature_rect_bg.width = lv_obj_get_width(ui_music_bg);
    			music_para->tags_miniature_rect_bg.height = lv_obj_get_height(ui_music_bg);
    		}
    	}
    	rect.x = music_para->tags_miniature_rect_bg.x;
    	rect.y = music_para->tags_miniature_rect_bg.y;
    	rect.width = music_para->tags_miniature_rect_bg.width;
    	rect.height = music_para->tags_miniature_rect_bg.height;

         if(music_para->AlbumArtBg.buf && music_para->AlbumArtBg.width && music_para->AlbumArtBg.height)
         {
             __music_msg("width=%d, height=%d\n", music_para->AlbumArtBg.width, music_para->AlbumArtBg.height);
             esMEMS_Pfree(music_para->AlbumArtBg.buf, ((music_para->AlbumArtBg.width*music_para->AlbumArtBg.height*4)+1023)/1024);
             __music_msg("here11\n");
             music_para->AlbumArtBg.buf = NULL;
             music_para->AlbumArtBg.width = 0;
             music_para->AlbumArtBg.height = 0;
         }
         pic_out_buf = esMEMS_Palloc(((rect.width*rect.height*4)+1023)/1024, 0);
         
         if(pic_out_buf == NULL)
         {       
             __music_msg("memory is not enough!!\n");
             return EPDK_FAIL;
         }
    	 __music_msg("external_album_tag_data_converter \n");
    	//ret = external_album_tag_data_converter(pic_out_buf,pbuf,root_para->artwork_size,rect.width,rect.height);
    	ret = external_pic_buffer_decode(pic_out_buf,pbuf,root_para->artwork_size,rect.width,rect.height);
    	__music_msg("ret=%d\n",ret);
        
    	if(ret == EPDK_OK)
    	{
            music_para->AlbumArtBg.buf = pic_out_buf;
            music_para->AlbumArtBg.width = rect.width;
            music_para->AlbumArtBg.height = rect.height;

            pic_gaussian_blur(pic_out_buf, pic_out_buf, rect.width, rect.height);
#if 0//def ABLUM_PICTURE_SET_BG_ENABLE
            if(para_sys->low_battery_showing_flag == 0 && music_para->visiable) //240702 added visiable for front Camare BT/USB music //231211 added this flag for bug 111
            {       
                gscene_bgd_set_img_src_buff(pic_out_buf,rect.width,rect.height);
            }
#endif	
    	}
    	else
    	{
    		__msg("get albem info Fail\n\n");
    		//esMEMS_Bfree(pBuf, in_para.width*in_para.height*4);
#if 0//def ABLUM_PICTURE_SET_BG_ENABLE
            if(music_para->visiable) //240702 added visiable for front Camare BT/USB music
            {
                gscene_bgd_set_img_src(BG_INDEX_AUTO);
            }
#endif      
    		return EPDK_FAIL;
    	}
    	
    	if(ret == EPDK_OK)
    	{

    	}
    	__music_msg("ret=%d\n",ret);

    	/*if(pic_out_buf)
    	{
    		esMEMS_Pfree(pic_out_buf, OUTPUT_BUF_SIZE);
    		pic_out_buf = NULL;
    		__music_msg("free pic_out_buf OK!\n");
    	}*/
    	
    	__music_msg("show album tag : %d\n",ret); 
    }
	return ret;
}

__s32 DrawMusicRootTypeorAlbumExternal(ui_music_para_t *music_para)
{
	__s32 ret= EPDK_FAIL;

  	__music_msg("artwork_type = %d\n",music_para->ui_music_data_para.artwork_type);
   	if(music_para->ui_music_data_para.artwork_type == ARTWORK_TYPE_PIC)
   	{
		ret = show_external_album_pic(music_para);
    	show_external_album_pic_bg(music_para, ret);
   	}
	else if(music_para->ui_music_data_para.artwork_type == ARTWORK_TYPE_ARGB888)
	{
		ret = show_external_album_pic_argb(music_para);
    	show_external_album_pic_argb_bg(music_para, ret);
	}
	else if(music_para->ui_music_data_para.artwork_type != ARTWORK_TYPE_LOGO) //mllee 201013 added LOGO type pretect
	{
        if(music_para->AlbumArt.buf && music_para->AlbumArt.width && music_para->AlbumArt.height)
        {
            if(music_para->bt_artwork_type == ARTWORK_TYPE_PIC)
                esMEMS_Pfree(music_para->AlbumArt.buf, ((music_para->AlbumArt.width*music_para->AlbumArt.height*4)+1023)/1024);
            music_para->AlbumArt.buf = NULL;
            music_para->AlbumArt.width = 0;
            music_para->AlbumArt.height = 0;
            eLIBs_memset(&music_para->tags_miniature_rect, 0, sizeof(RECT));
        }
        
        if(music_para->AlbumArtBg.buf && music_para->AlbumArtBg.width && music_para->AlbumArtBg.height)
        {
            esMEMS_Pfree(music_para->AlbumArtBg.buf, ((music_para->AlbumArtBg.width*music_para->AlbumArtBg.height*4)+1023)/1024);
            music_para->AlbumArtBg.buf = NULL;
            music_para->AlbumArtBg.width = 0;
            music_para->AlbumArtBg.height = 0;
            eLIBs_memset(&music_para->tags_miniature_rect_bg, 0, sizeof(RECT));
        }
	}
  	__music_msg("DrawMusicRootTypeorAlbumExternal ret = %d\n",ret);

	return ret;
}

static __s32 Draw_album_tags(ui_music_para_t *music_para)
{

	char FilePath[RAT_MAX_FULL_PATH_LEN];
	int ret = 0;
 	void *pBuf= NULL;
 	
	rat_miniature_para_t in_para;
	rat_audio_info_t out_para;
	RECT miniature_rect;
	reg_init_para_t*para_init;
	reg_system_para_t*para_sys;
	reg_root_para_t *last_root_para = NULL;	
    
	last_root_para = dsk_reg_get_para_by_app(REG_APP_ROOT);
	para_init = (reg_init_para_t*)dsk_reg_get_para_by_app(REG_APP_INIT);		
	para_sys = (reg_system_para_t*)dsk_reg_get_para_by_app(REG_APP_SYSTEM);		

	if(ui_music_logo == NULL)
	{
		return;
	}
	
	eLIBs_memset(FilePath,0,sizeof(FilePath));		
	eLIBs_memset(&in_para, 0, sizeof(in_para));
	ret = MusicGetPlayerItemFileFullPath(music_para->rat_handle,music_para->play_index,FilePath);
	if(ret == EPDK_FAIL)
	{
		__music_msg("album tags get file path Fail : index= %d!!!!\n\n",music_para->play_index);
		return EPDK_FAIL;
	}

	if((music_para->tags_miniature_rect.width == 0) || (music_para->tags_miniature_rect.height == 0))
	{
		if(ui_music_logo)
		{
			music_para->tags_miniature_rect.x = lv_obj_get_x(ui_music_logo);
			music_para->tags_miniature_rect.y = lv_obj_get_y(ui_music_logo);
			music_para->tags_miniature_rect.width = lv_obj_get_width(ui_music_logo);
			music_para->tags_miniature_rect.height = lv_obj_get_height(ui_music_logo);
		}
	}
	miniature_rect.x = music_para->tags_miniature_rect.x;
	miniature_rect.y = music_para->tags_miniature_rect.y;
	miniature_rect.width = music_para->tags_miniature_rect.width;
	miniature_rect.height = music_para->tags_miniature_rect.height;

	__music_msg("music_para->play_index=%x\n",music_para->play_index);
	if(strcmp(FilePath,music_para->AlbumArt.file) == 0)
	{
		if(music_para->AlbumArt.buf != NULL)
		{
			in_para.width = miniature_rect.width;
			in_para.height = miniature_rect.height;
			out_para.album.buf = music_para->AlbumArt.buf;
#ifdef REMOTE_USB_ALBUMART_ENABLE
            last_root_para->artwork_status = ARTWORK_STATUS_READY;
            last_root_para->artwork_type = ARTWORK_TYPE_ARGB888;
            last_root_para->artwork_width = miniature_rect.width;
            last_root_para->artwork_height = miniature_rect.height;
#ifdef REMOTE_USB_ALBUMART_YUV422
            last_root_para->artwork_size = in_para.width*in_para.height*2;
            com_argb2yuv422(music_para->AlbumArt.buf,miniature_rect.width,miniature_rect.height,last_root_para->artwork_buf,miniature_rect.width,miniature_rect.height);
#else
            last_root_para->artwork_size = in_para.width*in_para.height*4;
#endif
            {
                __u8 mcu_data[1] = {0}; //begin send album art data
                dsk_send_mcu_cmd(UART_SEND_USB_SEND_ALBUMART,mcu_data);
            }
#endif      
			ret = EPDK_OK;
		}
		else
			ret = EPDK_FAIL;
	}
	else
	{
		if(music_para->AlbumArt.buf && music_para->AlbumArt.width && music_para->AlbumArt.height)
		{
			__music_msg("width=%d, height=%d\n", music_para->AlbumArt.width, music_para->AlbumArt.height);
#ifndef REMOTE_USB_ALBUMART_ENABLE
			esMEMS_Pfree(music_para->AlbumArt.buf, ((music_para->AlbumArt.width*music_para->AlbumArt.height*4)+1023)/1024);
#else
            last_root_para->artwork_status = ARTWORK_STATUS_NONE;
            last_root_para->artwork_size = 0;
#endif
			__music_msg("here11\n");
			music_para->AlbumArt.buf = NULL;
			music_para->AlbumArt.width = 0;
			music_para->AlbumArt.height = 0;
		}
		eLIBs_strcpy(music_para->AlbumArt.file, FilePath);
		__music_msg("music_para->AlbumArt.file=%s\n",music_para->AlbumArt.file);
		eLIBs_strcpy(in_para.file, FilePath);
		in_para.format = PIXEL_COLOR_ARGB8888;
		in_para.width = miniature_rect.width;
		in_para.height = miniature_rect.height;
		in_para.mode =  WILLOW_SCALE_STRETCH;//1;//0;	// 1Ϊ����ģʽ

#ifdef USB_PLAYER_SUPPORT_M4A_FILE
		if(music_para->is_m4a_file)
		{
			in_para.is_m4a_file = music_para->is_m4a_file;
		}
		else
		{
			in_para.is_m4a_file=0;
		}
#endif
#ifdef REMOTE_USB_ALBUMART_ENABLE
#ifdef REMOTE_USB_ALBUMART_YUV422
        pBuf = last_root_para->artwork_buf + 500*1024;
#else
        pBuf = last_root_para->artwork_buf;
#endif
#else
		pBuf = esMEMS_Palloc(((in_para.width*in_para.height*4)+1023)/1024, 0);
#endif
		//pBuf = esMEMS_Balloc(in_para.width*in_para.height*4);	//ARGB8888,���4����С
		if(pBuf == NULL)										//���Էŵ���ʼ���룬�˳�ʱ�ͷ�
		{		
			__music_msg("memory is not enough!!\n");
			return EPDK_FAIL;
		}
		
		//rat_start_miniature_decode();	
		
		eLIBs_memset(&out_para, 0, sizeof(out_para));
#ifdef REMOTE_USB_ALBUMART_ENABLE
#else
		eLIBs_memset(pBuf, 0, in_para.width*in_para.height*4);
#endif
		out_para.album.buf = pBuf;

		__music_msg("out_para.album.buf=%x\n",out_para.album.buf);
		__music_msg("pBuf=%x\n",pBuf);
		ret = rat_get_album_info(&in_para, &out_para);	
		__music_msg("ret=%d\n",ret);
		if(ret == EPDK_OK)
		{
			__music_msg("music_para->AlbumArt.buf =%x\n",music_para->AlbumArt.buf );
			music_para->AlbumArt.buf = out_para.album.buf;
			__music_msg("music_para->AlbumArt.buf =%x\n",music_para->AlbumArt.buf );
			music_para->AlbumArt.width = in_para.width;
			music_para->AlbumArt.height = in_para.height;
#ifdef REMOTE_USB_ALBUMART_ENABLE
            last_root_para->artwork_status = ARTWORK_STATUS_READY;
            last_root_para->artwork_type = ARTWORK_TYPE_ARGB888;
            last_root_para->artwork_width = in_para.width;
            last_root_para->artwork_height = in_para.height;
#ifdef REMOTE_USB_ALBUMART_YUV422
            last_root_para->artwork_size = in_para.width*in_para.height*2;
            com_argb2yuv422(pBuf,in_para.width,in_para.height,last_root_para->artwork_buf,in_para.width,in_para.height);
#if 0//test for compress
            {
                __u8            *pCompressData;
                    __u8             OutProps[FB_FILE_MAX_COMPRESS_PROPS_LEN];
                    unsigned long            OutPropsLen = 128;
                    unsigned long            CompressLen;
    
                pCompressData = pBuf;
                CompressLen = in_para.width*in_para.height*2;
                
                 if(Fb_File_Compress(pCompressData, &CompressLen,\
                                (__u8 *)root_para->artwork_buf, in_para.width*in_para.height*2,\
                                OutProps, &OutPropsLen) == EPDK_OK)
                 {
                    eLIBs_printf("compress, src_size: %d, dst_size = %d\n",in_para.width*in_para.height*2,CompressLen);
                 }
            }
#endif
#else
            last_root_para->artwork_size = in_para.width*in_para.height*4;
#endif
            {
                __u8 mcu_data[1] = {0}; //begin send album art data
                dsk_send_mcu_cmd(UART_SEND_USB_SEND_ALBUMART,mcu_data);
            }
#endif      
		}
	}
	__music_msg("ret=%d\n",ret);
	if(ret == EPDK_OK)
	{
	#ifdef ABLUM_PICTURE_SET_BG_ENABLE
		if(para_sys->wallpaper_on_off)
		{
			__music_msg("out_para.album.buf=%x\n",out_para.album.buf);
			__music_msg("in_para.width=%d\n",in_para.width);
			__music_msg("in_para.height=%d\n",in_para.height);
			//gscene_bgd_set_img_src_buff(out_para.album.buf,in_para.width,in_para.height);
		}
		else
		{
			//gscene_bgd_set_img_src(BG_INDEX_AUTO);
		}
	#endif	
	}
	else
	{
#ifdef REMOTE_USB_ALBUMART_ENABLE
		last_root_para->artwork_status = ARTWORK_STATUS_NONE;
		last_root_para->artwork_size = 0;
#endif
		{
			__u8 mcu_data[1] = {1}; //send album art data none //no album art
			dsk_send_mcu_cmd(UART_SEND_USB_SEND_ALBUMART,mcu_data);
		}

		__music_msg("get albem info Fail\n\n");
		//gscene_bgd_set_img_src(BG_INDEX_AUTO);
		#if 1
#ifdef REMOTE_USB_ALBUMART_ENABLE
		//last_root_para->artwork_status = ARTWORK_STATUS_NONE;
		//last_root_para->artwork_size = 0;
#else
        esMEMS_Pfree(pBuf, ((in_para.width*in_para.height*4)+1023)/1024);
#endif  
		music_para->AlbumArt.buf = 0;
		music_para->AlbumArt.width = 0;
		music_para->AlbumArt.height = 0;
		eLIBs_memset(&music_para->tags_miniature_rect, 0, sizeof(RECT));

		#endif
		return EPDK_FAIL;
	}
	
	//rat_stop_miniature_decode();	
	__music_msg("ret=%d\n",ret);
#if 0
	if(EPDK_OK == ret)
	{
		DrawMusicDrawPic(music_para, MAX_MUSIC_BMP_ITEM, 0);
	}	
#endif
	
	return ret;
}

static __s32 Draw_album_bg(ui_music_para_t *music_para, __s32 tags)
{
	char FilePath[RAT_MAX_FULL_PATH_LEN];
	int ret = EPDK_OK;
 	void *pBuf1= NULL;
 	
	rat_miniature_para_t in_para1;
	rat_audio_info_t out_para1;
	RECT miniature_rect;
	reg_init_para_t*para_init;
	reg_system_para_t*para_sys;
	para_init = (reg_init_para_t*)dsk_reg_get_para_by_app(REG_APP_INIT);		
	para_sys = (reg_system_para_t*)dsk_reg_get_para_by_app(REG_APP_SYSTEM);		
	eLIBs_memset(FilePath,0,sizeof(FilePath));		
	eLIBs_memset(&in_para1, 0, sizeof(in_para1));
		
	if(tags == EPDK_OK)
	{
		ret = MusicGetPlayerItemFileFullPath(music_para->rat_handle,music_para->play_index,FilePath);
		if(ret == EPDK_FAIL)
		{
			__music_msg("album tags get file path Fail : index= %d!!!!\n\n",music_para->play_index);
			return EPDK_FAIL;
		}
        
		rat_start_miniature_decode();
		if(strcmp(FilePath,music_para->AlbumArtBg.file) == 0)
		{
			if(music_para->AlbumArtBg.buf != NULL)
			{
				in_para1.width = lv_obj_get_width(ui_music_bg);
				in_para1.height = lv_obj_get_height(ui_music_bg);
				out_para1.album.buf = music_para->AlbumArtBg.buf;
				ret = EPDK_OK;
			}
			else
				ret = EPDK_FAIL;
		}
		else
		{
			if(music_para->AlbumArtBg.buf && music_para->AlbumArtBg.width && music_para->AlbumArtBg.height)
			{
				__music_msg("width=%d, height=%d\n", music_para->AlbumArtBg.width, music_para->AlbumArtBg.height);
				esMEMS_Pfree(music_para->AlbumArtBg.buf, ((music_para->AlbumArtBg.width*music_para->AlbumArtBg.height*4)+1023)/1024);

				__music_msg("here11\n");
				music_para->AlbumArtBg.buf = NULL;
				music_para->AlbumArtBg.width = 0;
				music_para->AlbumArtBg.height = 0;
			}
			eLIBs_strcpy(music_para->AlbumArtBg.file, FilePath);
			eLIBs_strcpy(in_para1.file, FilePath);
			in_para1.format = PIXEL_COLOR_ARGB8888;
            in_para1.width = lv_obj_get_width(ui_music_bg);
            in_para1.height = lv_obj_get_height(ui_music_bg);
			in_para1.mode =  WILLOW_SCALE_STRETCH;//1;//0;	// 1Ϊ����ģʽ

#ifdef USB_PLAYER_SUPPORT_M4A_FILE
			if(music_para->is_m4a_file)
			{
				in_para1.is_m4a_file = music_para->is_m4a_file;
			}
			else
			{
				in_para1.is_m4a_file=0;
			}
#endif

			pBuf1 = esMEMS_Palloc(((in_para1.width*in_para1.height*4)+1023)/1024, 0);
			if(pBuf1 == NULL)
			{		
				__music_msg("memory is not enough!!\n");
				rat_stop_miniature_decode();
				return EPDK_FAIL;
			}
			
			eLIBs_memset(&out_para1, 0, sizeof(out_para1));
			eLIBs_memset(pBuf1, 0, in_para1.width*in_para1.height*4);
			out_para1.album.buf = pBuf1;
			
			__music_msg("out_para1.album.buf=%x\n",out_para1.album.buf);
			__music_msg("pBuf1=%x\n",pBuf1);
			ret = rat_get_album_info(&in_para1, &out_para1);

			__music_msg("ret=%d\n",ret);
			if(ret == EPDK_OK)
			{
				__music_msg("music_para->AlbumArtBg.buf =%x\n",music_para->AlbumArtBg.buf);
				music_para->AlbumArtBg.buf = out_para1.album.buf;
				__music_msg("music_para->AlbumArtBg.buf =%x\n",music_para->AlbumArtBg.buf);
				music_para->AlbumArtBg.width = in_para1.width;
				music_para->AlbumArtBg.height = in_para1.height;
			}
            pic_gaussian_blur(out_para1.album.buf, out_para1.album.buf, in_para1.width, in_para1.height);
		}

		__music_msg("ret=%d\n",ret);

		if(ret == EPDK_OK)
		{
#ifdef ABLUM_PICTURE_SET_BG_ENABLE
            //eLIBs_printf("para_sys->wallpaper_on_off=%d\n", para_sys->wallpaper_on_off);
			if(para_sys->wallpaper_on_off)
			{
				gscene_bgd_set_img_src_buff(out_para1.album.buf,in_para1.width,in_para1.height);
			}
			else
			{
				gscene_bgd_set_img_src(BG_INDEX_AUTO);
			}
#endif	
		}
		else
		{
			__music_msg("get albem info Fail\n\n");
			gscene_bgd_set_img_src(BG_INDEX_AUTO);
	        esMEMS_Pfree(pBuf1, ((in_para1.width*in_para1.height*4)+1023)/1024);
			music_para->AlbumArtBg.buf = 0;
			music_para->AlbumArtBg.width = 0;
			music_para->AlbumArtBg.height = 0;
		}

		rat_stop_miniature_decode();
	}
	else
	{
		gscene_bgd_set_img_src(BG_INDEX_AUTO);
	}
	
	return ret;
}

__s32 __DrawMusicRandom(ui_music_para_t *music_para)
{
	__s32 ret;
	reg_bt_para_t *para;
	reg_system_para_t*sy_para;
	reg_init_para_t*para_init;
	reg_root_para_t *last_root_para = NULL;	
	reg_music_para_t *music_last_para = NULL;	
	
	last_root_para = dsk_reg_get_para_by_app(REG_APP_ROOT);
	music_last_para = dsk_reg_get_para_by_app(REG_APP_MUSIC);
	para_init = (reg_init_para_t*)dsk_reg_get_para_by_app(REG_APP_INIT);
	sy_para = (reg_system_para_t*)dsk_reg_get_para_by_app(REG_APP_SYSTEM);
	para = (reg_bt_para_t*)dsk_reg_get_para_by_app(REG_APP_BT);

    __music_msg("sy_para->is_head_unit=%d, music_para->work_source=%d, music_para->usb_device=%d\n", sy_para->is_head_unit, music_para->work_source, music_para->usb_device);
	if((music_para->work_source == ID_WORK_SOURCE_USB || (music_para->party_work_source == PARTY_D_SOURCE_USB)) && music_para->usb_device == USB_DEVICE_LOCAL &&  music_para->root_type != RAT_USB_REMOTE)
	{
        __music_msg("__DrawMusicBottom robin_get_play_mode=%d\n", robin_get_play_mode());
        
        //radom key
        if(robin_get_play_mode() == RAT_PLAY_MODE_ROTATE_ONE || robin_get_play_mode() == RAT_PLAY_MODE_ROTATE_FOLDER || robin_get_play_mode() == RAT_PLAY_MODE_ROTATE_ALL)
        {
            lv_img_set_src(ui_music_bottom_random_img, ui_music_get_res(music_para, MUSIC_THEME_COM_BOT_RANDOM_A_N_BMP));
            //lv_image_set_src(ui_music_play_bottom_random, ui_music_get_res(music_para, MUSIC_THEME_TEMP_MEDIA_SETUP_RANDOM_A_BMP));
            //if(music_para->ui_func_menu_shuffle_roller)
            //    lv_roller_set_selected(music_para->ui_func_menu_shuffle_roller, MUSIC_MENU_SHUFFLE_OFF, LV_ANIM_OFF);
        }
        else if(robin_get_play_mode() == RAT_PLAY_MODE_RANDOM_FOLDER)
        {
            lv_img_set_src(ui_music_bottom_random_img, ui_music_get_res(music_para, MUSIC_THEME_COM_BOT_RANDOM_B_N_BMP));
            //lv_image_set_src(ui_music_play_bottom_random, ui_music_get_res(music_para, MUSIC_THEME_TEMP_MEDIA_SETUP_RANDOM_B_BMP));
            //if(music_para->ui_func_menu_shuffle_roller)
            //    lv_roller_set_selected(music_para->ui_func_menu_shuffle_roller, MUSIC_MENU_REPEAT_FOLDER, LV_ANIM_OFF);
        }
        else if(robin_get_play_mode() == RAT_PLAY_MODE_RANDOM)
        {
            lv_img_set_src(ui_music_bottom_random_img, ui_music_get_res(music_para, MUSIC_THEME_COM_BOT_RANDOM_A_N_BMP));
            //lv_image_set_src(ui_music_play_bottom_random, ui_music_get_res(music_para, MUSIC_THEME_TEMP_MEDIA_SETUP_RANDOM_A_BMP));
            //if(music_para->ui_func_menu_shuffle_roller)
            //    lv_roller_set_selected(music_para->ui_func_menu_shuffle_roller, MUSIC_MENU_SHUFFLE_OFF, LV_ANIM_OFF);
        }
        else
        {
            lv_img_set_src(ui_music_bottom_random_img, ui_music_get_res(music_para, MUSIC_THEME_COM_BOT_RANDOM_A_N_BMP));
            //lv_image_set_src(ui_music_play_bottom_random, ui_music_get_res(music_para, MUSIC_THEME_TEMP_MEDIA_SETUP_RANDOM_A_BMP));
            //if(music_para->ui_func_menu_shuffle_roller)
            //    lv_roller_set_selected(music_para->ui_func_menu_shuffle_roller, MUSIC_MENU_SHUFFLE_OFF, LV_ANIM_OFF);
        }
	}
    else // bt or usb-ipod or usb remote
    {
        __music_msg("music_para->ui_music_data_para.random_mode=%d, music_para->ui_music_data_para.repeat_mode=%d, music_para->ui_music_data_para.cur_play_state=%d\n", music_para->ui_music_data_para.random_mode, music_para->ui_music_data_para.repeat_mode, music_para->ui_music_data_para.cur_play_state);
        if(music_para->ui_music_data_para.random_mode == RAT_RANDOM_MODE_ALL)
        {
            lv_img_set_src(ui_music_bottom_random_img, ui_music_get_res(music_para, MUSIC_THEME_COM_BOT_RANDOM_A_N_BMP));
            //lv_image_set_src(ui_music_play_bottom_random, ui_music_get_res(music_para, MUSIC_THEME_TEMP_MEDIA_SETUP_RANDOM_A_BMP));
        }
        else if(music_para->ui_music_data_para.random_mode == RAT_RANDOM_MODE_FOLDER)
        {
            lv_img_set_src(ui_music_bottom_random_img, ui_music_get_res(music_para, MUSIC_THEME_COM_BOT_RANDOM_B_N_BMP));
            //lv_image_set_src(ui_music_play_bottom_random, ui_music_get_res(music_para, MUSIC_THEME_TEMP_MEDIA_SETUP_RANDOM_B_BMP));
        }
    }

	return EPDK_OK;
}

__s32 __DrawMusicRepeat(ui_music_para_t *music_para)
{
	__s32 ret;
	reg_bt_para_t *para;
	reg_system_para_t*sy_para;
	reg_init_para_t*para_init;
	reg_root_para_t *last_root_para = NULL;	
	reg_music_para_t *music_last_para = NULL;	
	
	last_root_para = dsk_reg_get_para_by_app(REG_APP_ROOT);
	music_last_para = dsk_reg_get_para_by_app(REG_APP_MUSIC);
	para_init = (reg_init_para_t*)dsk_reg_get_para_by_app(REG_APP_INIT);
	sy_para = (reg_system_para_t*)dsk_reg_get_para_by_app(REG_APP_SYSTEM);
	para = (reg_bt_para_t*)dsk_reg_get_para_by_app(REG_APP_BT);

    __music_msg("sy_para->is_head_unit=%d, music_para->work_source=%d, music_para->usb_device=%d\n", sy_para->is_head_unit, music_para->work_source, music_para->usb_device);
	if((music_para->work_source == ID_WORK_SOURCE_USB || (music_para->party_work_source == PARTY_D_SOURCE_USB)) && music_para->usb_device == USB_DEVICE_LOCAL &&  music_para->root_type != RAT_USB_REMOTE)
	{
        __music_msg("__DrawMusicBottom robin_get_play_mode=%d\n", robin_get_play_mode());
                    
        //repeat key
        if(robin_get_play_mode() == RAT_PLAY_MODE_SEQUENCE)
        {
            if(music_para->play_mode_repeat==RAT_PLAY_MODE_ROTATE_ONE)
            {
                lv_img_set_src(ui_music_bottom_repeat_img, ui_music_get_res(music_para, MUSIC_THEME_COM_BOT_REPEAT_B_N_BMP));
                //lv_image_set_src(ui_music_play_bottom_repeat, ui_music_get_res(music_para, MUSIC_THEME_TEMP_MEDIA_SETUP_REPEAT_C_BMP));
                //if(music_para->ui_func_menu_repeat_roller)
                //    lv_roller_set_selected(music_para->ui_func_menu_repeat_roller, MUSIC_MENU_REPEAT_ONE, LV_ANIM_OFF);
            }
            else if(music_para->play_mode_repeat==RAT_PLAY_MODE_ROTATE_FOLDER)
            {
                lv_img_set_src(ui_music_bottom_repeat_img, ui_music_get_res(music_para, MUSIC_THEME_COM_BOT_REPEAT_A_N_BMP));
                //lv_image_set_src(ui_music_play_bottom_repeat, ui_music_get_res(music_para, MUSIC_THEME_TEMP_MEDIA_SETUP_REPEAT_A_BMP));
                //if(music_para->ui_func_menu_repeat_roller)
                //    lv_roller_set_selected(music_para->ui_func_menu_repeat_roller, MUSIC_MENU_REPEAT_FOLDER, LV_ANIM_OFF);
            }
            else if(music_para->play_mode_repeat==RAT_PLAY_MODE_ROTATE_ALL)
            {
                lv_img_set_src(ui_music_bottom_repeat_img, ui_music_get_res(music_para, MUSIC_THEME_COM_BOT_REPEAT_C_N_BMP));
                //lv_image_set_src(ui_music_play_bottom_repeat, ui_music_get_res(music_para, MUSIC_THEME_TEMP_MEDIA_SETUP_REPEAT_B_BMP));
                //if(music_para->ui_func_menu_repeat_roller)
                //    lv_roller_set_selected(music_para->ui_func_menu_repeat_roller, MUSIC_MENU_REPEAT_ALL, LV_ANIM_OFF);
            }
            else
            {
                lv_img_set_src(ui_music_bottom_repeat_img, ui_music_get_res(music_para, MUSIC_THEME_COM_BOT_REPEAT_B_N_BMP));
                //lv_image_set_src(ui_music_play_bottom_repeat, ui_music_get_res(music_para, MUSIC_THEME_TEMP_MEDIA_SETUP_REPEAT_C_BMP));
                //if(music_para->ui_func_menu_repeat_roller)
                //    lv_roller_set_selected(music_para->ui_func_menu_repeat_roller, MUSIC_MENU_REPEAT_ONE, LV_ANIM_OFF);
            }
                
        }
        else if(robin_get_play_mode() == RAT_PLAY_MODE_ROTATE_ONE)
        {
            lv_img_set_src(ui_music_bottom_repeat_img, ui_music_get_res(music_para, MUSIC_THEME_COM_BOT_REPEAT_B_N_BMP));
            //lv_image_set_src(ui_music_play_bottom_repeat, ui_music_get_res(music_para, MUSIC_THEME_TEMP_MEDIA_SETUP_REPEAT_C_BMP));
            //if(music_para->ui_func_menu_repeat_roller)
            //    lv_roller_set_selected(music_para->ui_func_menu_repeat_roller, MUSIC_MENU_REPEAT_ONE, LV_ANIM_OFF);
        }   
        else if(robin_get_play_mode() == RAT_PLAY_MODE_ROTATE_FOLDER)
        {
            lv_img_set_src(ui_music_bottom_repeat_img, ui_music_get_res(music_para, MUSIC_THEME_COM_BOT_REPEAT_A_N_BMP));
            //lv_image_set_src(ui_music_play_bottom_repeat, ui_music_get_res(music_para, MUSIC_THEME_TEMP_MEDIA_SETUP_REPEAT_A_BMP));
            //if(music_para->ui_func_menu_repeat_roller)
            //    lv_roller_set_selected(music_para->ui_func_menu_repeat_roller, MUSIC_MENU_REPEAT_FOLDER, LV_ANIM_OFF);
        }
        else if(robin_get_play_mode() == RAT_PLAY_MODE_ROTATE_ALL || robin_get_play_mode() == RAT_PLAY_MODE_RANDOM_FOLDER)
        {
            lv_img_set_src(ui_music_bottom_repeat_img, ui_music_get_res(music_para, MUSIC_THEME_COM_BOT_REPEAT_C_N_BMP));
            //lv_image_set_src(ui_music_play_bottom_repeat, ui_music_get_res(music_para, MUSIC_THEME_TEMP_MEDIA_SETUP_REPEAT_B_BMP));
            //if(music_para->ui_func_menu_repeat_roller)
            //    lv_roller_set_selected(music_para->ui_func_menu_repeat_roller, MUSIC_MENU_REPEAT_ALL, LV_ANIM_OFF);
        }
        else
        {
            lv_img_set_src(ui_music_bottom_repeat_img, ui_music_get_res(music_para, MUSIC_THEME_COM_BOT_REPEAT_B_N_BMP));
            //lv_image_set_src(ui_music_play_bottom_repeat, ui_music_get_res(music_para, MUSIC_THEME_TEMP_MEDIA_SETUP_REPEAT_C_BMP));
            //if(music_para->ui_func_menu_repeat_roller)
            //    lv_roller_set_selected(music_para->ui_func_menu_repeat_roller, MUSIC_MENU_REPEAT_ONE, LV_ANIM_OFF);
        }
	}
    else // bt or usb-ipod or usb remote
    {
        __music_msg("music_para->ui_music_data_para.random_mode=%d, music_para->ui_music_data_para.repeat_mode=%d, music_para->ui_music_data_para.cur_play_state=%d\n", music_para->ui_music_data_para.random_mode, music_para->ui_music_data_para.repeat_mode, music_para->ui_music_data_para.cur_play_state);
        if(music_para->ui_music_data_para.repeat_mode == RAT_REPEAT_MODE_ONE)
        {
            lv_img_set_src(ui_music_bottom_repeat_img, ui_music_get_res(music_para, MUSIC_THEME_COM_BOT_REPEAT_B_N_BMP));
            //lv_image_set_src(ui_music_play_bottom_repeat, ui_music_get_res(music_para, MUSIC_THEME_TEMP_MEDIA_SETUP_REPEAT_C_BMP));
        }
        else if(music_para->ui_music_data_para.repeat_mode == RAT_REPEAT_MODE_FOLDER)
        {
            lv_img_set_src(ui_music_bottom_repeat_img, ui_music_get_res(music_para, MUSIC_THEME_COM_BOT_REPEAT_A_N_BMP));
            //lv_image_set_src(ui_music_play_bottom_repeat, ui_music_get_res(music_para, MUSIC_THEME_TEMP_MEDIA_SETUP_REPEAT_A_BMP));
        }
        else if(music_para->ui_music_data_para.repeat_mode == RAT_REPEAT_MODE_ALL)
        {
            lv_img_set_src(ui_music_bottom_repeat_img, ui_music_get_res(music_para, MUSIC_THEME_COM_BOT_REPEAT_C_N_BMP));
            //lv_image_set_src(ui_music_play_bottom_repeat, ui_music_get_res(music_para, MUSIC_THEME_TEMP_MEDIA_SETUP_REPEAT_B_BMP));
        }
        else
        {
            lv_img_set_src(ui_music_bottom_repeat_img, ui_music_get_res(music_para, MUSIC_THEME_COM_BOT_REPEAT_C_N_BMP));
            //lv_image_set_src(ui_music_play_bottom_repeat, ui_music_get_res(music_para, MUSIC_THEME_TEMP_MEDIA_SETUP_REPEAT_B_BMP));
        }

    }

	return EPDK_OK;
}

__s32 __DrawMusicPlayPause(ui_music_para_t *music_para)
{
	__s32 ret;
	reg_bt_para_t *para;
	reg_system_para_t*sy_para;
	reg_init_para_t*para_init;
	reg_root_para_t *last_root_para = NULL;	
	reg_music_para_t *music_last_para = NULL;	
	
	last_root_para = dsk_reg_get_para_by_app(REG_APP_ROOT);
	music_last_para = dsk_reg_get_para_by_app(REG_APP_MUSIC);
	para_init = (reg_init_para_t*)dsk_reg_get_para_by_app(REG_APP_INIT);
	sy_para = (reg_system_para_t*)dsk_reg_get_para_by_app(REG_APP_SYSTEM);
	para = (reg_bt_para_t*)dsk_reg_get_para_by_app(REG_APP_BT);

    __music_msg("sy_para->is_head_unit=%d, music_para->work_source=%d, music_para->usb_device=%d\n", sy_para->is_head_unit, music_para->work_source, music_para->usb_device);
	
    if((music_para->work_source == ID_WORK_SOURCE_BT || (music_para->party_work_source == PARTY_D_SOURCE_BT_AUDIO)) || (music_para->work_source == ID_WORK_SOURCE_USB && music_para->usb_device == USB_DEVICE_IPOD) || music_para->root_type == RAT_USB_REMOTE)// bt or usb-ipod or usb remote
    {        
		if(music_para->ui_music_data_para.cur_play_state == RAT_PLAYSTATE_PLAY) 
            ui_source_bottom_play(music_para->source_para, SOURCE_BOTTOM_PAUSE);
		else if(music_para->ui_music_data_para.cur_play_state == RAT_PLAYSTATE_PAUSE)
            ui_source_bottom_play(music_para->source_para, SOURCE_BOTTOM_PLAY);
		/*else if(music_para->ui_music_data_para.cur_play_state == RAT_PLAYSTATE_FF)
			status = MUSIC_FF;
		else if(music_para->ui_music_data_para.cur_play_state == RAT_PLAYSTATE_RR)
			status = MUSIC_RR;
		else if(music_para->ui_music_data_para.cur_play_state == RAT_PLAYSTATE_PREV)
			status = MUSIC_PRV;
		else if(music_para->ui_music_data_para.cur_play_state == RAT_PLAYSTATE_NEXT)
			status = MUSIC_NEXT;
		else if(music_para->ui_music_data_para.cur_play_state == RAT_PLAYSTATE_STOP)
			status = MUSIC_STOPED;
		else 
			status = MUSIC_NONE;*/
    }
    else if(music_para->work_source == ID_WORK_SOURCE_USB || (music_para->party_work_source == PARTY_D_SOURCE_USB))
	{        
        __music_msg("robin_get_fsm_status()=%d\n",robin_get_fsm_status());  
        //play/pause key
        if(robin_get_fsm_status() == CEDAR_STAT_PLAY || robin_get_fsm_status() == CEDAR_STAT_JUMP)
        {    
            ui_source_bottom_play(music_para->source_para, SOURCE_BOTTOM_PAUSE);
            //lv_imgbtn_set_state(ui_music_bottom_play_img, LV_IMGBTN_STATE_CHECKED_RELEASED);
        }
        else if(robin_get_fsm_status() == CEDAR_STAT_PAUSE)
        {    
            ui_source_bottom_play(music_para->source_para, SOURCE_BOTTOM_PLAY);
            //lv_imgbtn_set_state(ui_music_bottom_play_img, LV_IMGBTN_STATE_RELEASED);
        }
        else
        {    
            ui_source_bottom_play(music_para->source_para, SOURCE_BOTTOM_PLAY);
            //lv_imgbtn_set_state(ui_music_bottom_play_img, LV_IMGBTN_STATE_RELEASED);
        }
	}

	return EPDK_OK;
}

__s32 __DrawMusicBottom(ui_music_para_t *music_para)
{
	__s32 ret;

    DrawMusicRandom(music_para);
    DrawMusicRepeat(music_para);
    DrawMusicPlayPause(music_para);
    
	return EPDK_OK;
}

#ifdef DELAY_DRAW_MUSIC_ALBUM_PIC
__s32 ResumeDrawMusicRootType(ui_music_para_t *music_para)
{
	__s32 ret;
	reg_system_para_t*para_sys;
	
	reg_init_para_t*para_init;
	para_init = (reg_init_para_t*)dsk_reg_get_para_by_app(REG_APP_INIT);	
	para_sys = (reg_system_para_t*)dsk_reg_get_para_by_app(REG_APP_SYSTEM);	

	gscene_bgd_set_img_src(BG_INDEX_AUTO);
	
#ifndef MUSIC_SPECTRUM_ENABLE //mllee 131130
	if(music_para->root_type == RAT_USB)
	{
		//if(music_para->play_file_info.sample_rate>=96000)
		if(music_para->play_file_info.filebit == MUSIC_PLAY_FILE_24_BIT)
			DrawMusicDrawPic(music_para, ID_HI_RES_BMP, 0);
		else
			DrawMusicDrawPic(music_para, MUSIC_TEMP_COM_MEDIA_ALBUM_BG_BMP, 0);
	}
	else if(music_para->root_type == RAT_TF)
	{
		DrawMusicDrawPic(music_para, ID_SD_BMP, 0);
	}
#endif
	
	return EPDK_OK;
}
#endif

__s32 __DrawMusicRootTypeorAlbumTagPic(ui_music_para_t *music_para)
{
	__s32 ret = EPDK_FAIL;
	char FilePath[RAT_MAX_FULL_PATH_LEN];
	reg_root_para_t *last_root_para = NULL;	
	reg_music_para_t *music_last_para = NULL;	

	music_last_para = dsk_reg_get_para_by_app(REG_APP_MUSIC);	
	last_root_para = dsk_reg_get_para_by_app(REG_APP_ROOT);

	if((music_para->work_source == ID_WORK_SOURCE_BT || (music_para->party_work_source == PARTY_D_SOURCE_BT_AUDIO)) || (music_para->work_source == ID_WORK_SOURCE_USB && music_para->usb_device == USB_DEVICE_IPOD) || music_para->root_type==RAT_USB_REMOTE)
	{
        ret = DrawMusicRootTypeorAlbumExternal(music_para);

        if(ret != EPDK_OK)
        {
            DrawMusicDrawPic(music_para, MUSIC_TEMP_COM_MEDIA_ALBUM_BG_BMP, 0);
        }
        else
        {
            DrawMusicDrawPic(music_para, MAX_MUSIC_BMP_ITEM, 1);
        }
	}
    else if(music_para->work_source == ID_WORK_SOURCE_USB || (music_para->party_work_source == PARTY_D_SOURCE_USB))
    {
    	ret = MusicGetPlayerItemFileFullPath(music_para->rat_handle,music_para->play_index,FilePath);
    	__music_msg("FilePath=%s\n",FilePath);
    	__music_msg("file=%s\n",music_para->AlbumArt.file);
    	if(strcmp(FilePath,music_para->AlbumArt.file) != 0)
    		ret = rat_start_miniature_decode();
    	
    	ret = Draw_album_tags(music_para);
    	rat_stop_miniature_decode();

    	Draw_album_bg(music_para, ret);

    	if(ret != EPDK_OK)
    	{
#ifndef MUSIC_SPECTRUM_ENABLE //mllee 131130
    		if(music_para->root_type == RAT_USB)
    		{
    			if(music_para->play_file_info.filebit == MUSIC_PLAY_FILE_24_BIT)
    				DrawMusicDrawPic(music_para, ID_HI_RES_BMP, 1);
    			else
    				DrawMusicDrawPic(music_para, MUSIC_TEMP_COM_MEDIA_ALBUM_BG_BMP, 0);
    		}
    		else if(music_para->root_type == RAT_TF)
    		{
    			DrawMusicDrawPic(music_para, ID_SD_BMP, 1);
    		}
#endif
    	}
    	else
    	{
    		DrawMusicDrawPic(music_para, MAX_MUSIC_BMP_ITEM, 1);
    	}
    }
    
	return EPDK_OK;
}

__s32 __DrawMusicPlaySongNum(ui_music_para_t *music_para,__u32 song_num)
{
	char ucStringBuf[50] = {0};
	char ucStringBuf1[50] = {0};
	void *pic_buf = NULL;
	__s32 X = 0, Y = 0;	
	int total;
	int len;

	//total = robin_npl_get_total_count();
	total = robin_npl_get_EnPlayFileTotal_count();
	

	if(total >= 10000)//如果曲目总数为四位
	{	
		eLIBs_sprintf(ucStringBuf1,"%d%d%d%d%d",total/(10000)%10,total/(1000)%10,total/(100)%10,total/(10)%10,total%10); 
		
		if(song_num>= 10000)
			eLIBs_sprintf(ucStringBuf,"%d%d%d%d%d",song_num/(10000)%10,song_num/(1000)%10,song_num/(100)%10,song_num/(10)%10,song_num%10);
		else
			eLIBs_sprintf(ucStringBuf,"%d%d%d%d",song_num/(1000)%10,song_num/(100)%10,song_num/(10)%10,song_num%10);	
			
	}
	else if(total >= 1000)
	{
		eLIBs_sprintf(ucStringBuf1,"%d%d%d%d",total/(1000)%10,total/(100)%10,total/(10)%10,total%10); 
		eLIBs_sprintf(ucStringBuf,"%d%d%d%d",song_num/(1000)%10,song_num/(100)%10,song_num/(10)%10,song_num%10);	
	}
	else 
	{
		eLIBs_sprintf(ucStringBuf1,"%d%d%d",total/(100)%10,total/(10)%10,total%10);	
		eLIBs_sprintf(ucStringBuf,"%d%d%d",song_num/(100)%10,song_num/(10)%10,song_num%10);	
	}


	eLIBs_strcat(ucStringBuf,"/");
	eLIBs_strcat(ucStringBuf, ucStringBuf1);
	__music_msg("__DrawMusicPlaySongNum total=%d\n", total);
    //lv_label_set_text(ui_music_play_top_total, ucStringBuf);
	//_ui_set_text_shadow(ui_music_play_top_total, GUI_WHITE, GUI_DARKBLACK, 2, 2);

	return EPDK_OK;	
}

__s32 __DrawMusicFileArtist(ui_music_para_t *music_para,char *artist_name, __epdk_charset_enm_e charset)
{
	char ucStringBuf[50] = {0};

	if(eLIBs_strlen(artist_name) == 0)
	{
		dsk_langres_get_menu_text(STRING_MUSIC_NO_ARTIST, ucStringBuf, 50);
		eLIBs_strcpy(artist_name, ucStringBuf);
	}

	__music_msg("1:artist_name:%s\n", artist_name);

    lv_label_set_text(ui_music_id3_text_panel_artist_text, artist_name);
	//_ui_set_text_shadow_scroll(ui_music_id3_text_panel_artist_text, GUI_WHITE, GUI_DARKBLACK, 2, 2);

	return EPDK_OK;
}

__s32 __DrawMusicFileAlbum(ui_music_para_t *music_para,char *album_name, __epdk_charset_enm_e charset)
{
	char ucStringBuf[50] = {0};

	if(eLIBs_strlen(album_name) == 0)
	{
		dsk_langres_get_menu_text(STRING_MUSIC_NO_ALBUM, ucStringBuf, 50);
		eLIBs_strcpy(album_name, ucStringBuf);
	}
	__music_msg("1:album_name:%s\n", album_name);

	lv_label_set_text(ui_music_id3_text_panel_album_text, album_name);
	//_ui_set_text_shadow_scroll(ui_music_id3_text_panel_album_text, GUI_WHITE, GUI_DARKBLACK, 2, 2);

	return EPDK_OK;
}

__s32 __DrawMusicFileName(ui_music_para_t *music_para,char *file_name, __epdk_charset_enm_e charset)
{
	char ucStringBuf[50] = {0};

	char *pFileName;

	pFileName = eLIBs_strchrlast(file_name, '\\');
	if(pFileName != NULL)
	{
		pFileName++;
		eLIBs_strcpy(file_name, pFileName);	
	}	

	if(eLIBs_strlen(file_name) == 0)
	{
		dsk_langres_get_menu_text(STRING_MUSIC_NO_TITLE, ucStringBuf, 50);
		eLIBs_strcpy(file_name, ucStringBuf);
	}
	__music_msg("1:file_name:%s\n", file_name);

	lv_label_set_text(ui_music_id3_text_panel_song_text, file_name);
	//_ui_set_text_shadow_scroll(ui_music_id3_text_panel_song_text, GUI_WHITE, GUI_DARKBLACK, 2, 2);

	return EPDK_OK;
}


//显示当前播放时间
__s32 __DrawMusicPlayingTime(__u32 time)
{	
	__s32 X,Y;
	char time_text[20];
	void *pic_buf = NULL;

	eLIBs_memset(time_text, 0 ,sizeof(time_text));	

	time2str_by_format(time, time_text, TIME_AUTO_HMS);

    lv_label_set_text(ui_music_play_bottom_current, time_text);
	//_ui_set_text_shadow(ui_music_play_bottom_current, GUI_WHITE, GUI_DARKBLACK, 2, 2);

	return EPDK_OK;	
}

__s32 __CleanMusicTotalTime(void)
{
	lv_label_set_text(ui_music_play_bottom_current, "  ");
	//_ui_set_text_shadow(ui_music_play_bottom_current, GUI_WHITE, GUI_DARKBLACK, 2, 2);

	return EPDK_OK;
}
	

//只画进度点，不画背景条
__s32 __DrawMusicProgressBarPoint(__u32 total, __u32 cur_value)
{		
	if((total == 0)||(cur_value == 0))
	{		
		lv_slider_set_value(ui_music_play_bottom_bar, 0, LV_ANIM_OFF);
		return EPDK_OK;						//没有任何进度
	}
	
	if(cur_value > total)
	{
		__music_msg(" cur_time is bigger than total time ........\n");
		cur_value = total ;
	}
        
	if(lv_slider_get_max_value(ui_music_play_bottom_bar) != total/1000)
	{
		__music_msg(" total time : %d\n", total);
		lv_slider_set_range(ui_music_play_bottom_bar, 0, total/1000);
	}

	//__music_msg("total=%d\n",total);
	//__music_msg("cur_value=%d\n",cur_value);
	//pro_max_value = lv_slider_get_max_value(ui_music_progress);

	//pro_value = cur_value * pro_max_value / total;
    //__music_msg("music_slider_anim_flag=%d, cur_value=%d\n", cur_value, music_slider_anim_flag);
	//if(music_slider_anim_flag == LV_ANIM_ON)
	{
        //music_slider_value = cur_value/1000;
        //if(music_slider_value == 0)
            lv_slider_set_value(ui_music_play_bottom_bar, cur_value/1000, LV_ANIM_OFF);
        //else
	    //    lv_slider_set_value(ui_music_play_bottom_bar, cur_value/1000, music_slider_anim_flag);
	}

	return EPDK_OK;
}

//画进度条,total为总量， cur_value为当前进度值
__s32 DrawMusicProgressBar(__u32 total, __u32 cur_value)
{	
	DrawMusicProgressBarPoint(total, cur_value);
	
	return EPDK_OK;
}

__s32 DrawMusicHeadbar(ui_music_para_t *music_para)
{	
	reg_aux_para_t* para;
	para = (reg_aux_para_t*)dsk_reg_get_para_by_app(REG_APP_AUX);	

	return EPDK_OK;
}

__s32 __DrawMusicPlayerFileInfo(ui_music_para_t *music_para, audio_file_info_t *file_info)
{
    if(lv_obj_has_flag(ui_music_id3, LV_OBJ_FLAG_HIDDEN))
    {
        lv_obj_remove_flag(ui_music_id3, LV_OBJ_FLAG_HIDDEN);      /// Flags
        lv_obj_add_flag(ui_music_bt_status, LV_OBJ_FLAG_HIDDEN);      /// Flags
    }

    lv_obj_set_x(ui_music_id3_text_panel, 0);
    lv_obj_set_y(ui_music_id3_text_panel, 0);
	DrawMusicFileArtist(music_para,file_info->artist,file_info->artist_charset);
	DrawMusicFileAlbum(music_para,file_info->album,file_info->album_charset);
	DrawMusicFileName(music_para,file_info->filename,file_info->title_charset);

	return EPDK_OK;
}

__s32 DrawMusicPlayer(ui_music_para_t *music_para)
{
	DrawMusicPlayingTime(0);	
	DrawMusicProgressBar(0,0);
	return EPDK_OK;
}

__s32 __music_draw_bt_status(ui_music_para_t *music_para)
{
    __s32 ret;
	char str[128] = {0};
    
	if(music_para->ui_music_data_para.bt_status == BT_STATUS_CONNECTED_OK)
	{
		eLIBs_strcpy(str,"Bluetooth is\n connected!");
        ret = 1;
	}
	else
	{
		eLIBs_strcpy(str,"Bluetooth is\n disconnected!");
        ret = 0;
	}

    if(lv_obj_has_flag(ui_music_bt_status, LV_OBJ_FLAG_HIDDEN))
    {
        lv_obj_add_flag(ui_music_id3, LV_OBJ_FLAG_HIDDEN);      /// Flags
        lv_obj_remove_flag(ui_music_bt_status, LV_OBJ_FLAG_HIDDEN);      /// Flags
    }
    
    lv_label_set_text(ui_music_bt_status_text, str);
    __music_msg("__music_draw_bt_status ret=%d\n", ret);

  	return ret;
}

__s32 __music_draw_bt_phone_text(ui_music_para_t *music_para)
{
    __s32 ret;
	char str[128] = {0};

    __music_msg("__music_draw_bt_phone_text\n");
	if(music_para->ui_music_data_para.bt_status == BT_STATUS_CONNECTED_OK)
	{
		eLIBs_strcpy(str,music_para->ui_music_data_para.btaudio_phone_name);
        ret = 1;
	}
	else
	{
		eLIBs_strcpy(str," ");
        ret = 0;
	}
    
    if(lv_obj_has_flag(ui_music_play_bottom_dual_phone_panel, LV_OBJ_FLAG_HIDDEN))
    {
        lv_obj_remove_flag(ui_music_play_bottom_dual_phone_panel, LV_OBJ_FLAG_HIDDEN);      /// Flags
    }
    
    lv_label_set_text(ui_music_play_bottom_dual_phone_text, str);

  	return ret;
}

__s32 __music_player_long_string_stop_roll(ui_music_para_t * music_para)
{
	//lv_label_set_long_mode(ui_music_id3_text_panel_song_text, LV_LABEL_LONG_CLIP);
	//_ui_set_text_shadow_scroll(ui_music_id3_text_panel_song_text, GUI_WHITE, GUI_DARKBLACK, 2, 2);
	//lv_label_set_long_mode(ui_music_id3_text_panel_artist_text, LV_LABEL_LONG_CLIP);
	//_ui_set_text_shadow_scroll(ui_music_id3_text_panel_artist_text, GUI_WHITE, GUI_DARKBLACK, 2, 2);
	//lv_label_set_long_mode(ui_music_id3_text_panel_album_text, LV_LABEL_LONG_CLIP);
	//_ui_set_text_shadow_scroll(ui_music_id3_text_panel_album_text, GUI_WHITE, GUI_DARKBLACK, 2, 2);

	return EPDK_FAIL;
}

#ifdef PREV_NEXT_SONG_SLIDE_OUT
#define SLIDE_OUT_STEP	400//200

//0:PREV    1:NEXT
__s32 __MusicPlayInfoSlideShow(ui_music_para_t *music_para,__u8 prev_next)
{
	__s32 anim_start = 0;
	__s32 anim_end;

#ifdef USE_GXPMX30_MODEL
	return;
#endif

	//lv_obj_add_flag(ui_music_id3_text_panel, LV_OBJ_FLAG_HIDDEN);	  /// Flags
	if(prev_next)
	{
		anim_end = lv_obj_get_width(ui_music_id3_text_panel) + 150;
		__music_msg("next:anim_start=%d, anim_end=%d\n", anim_start, anim_end);
		ui_snapshot_anim(ui_snapshot_anim_set_para(ui_music_id3_text_panel, ui_music_id3_text, NULL, NULL, 0, 0, 0, 0, anim_start, -anim_end, 0, 0, 0, 0), SLIDE_OUT_STEP, 200);
		//playerinfo_Animation(ui_music_id3_text_panel, (void *)music_para, 0, SLIDE_OUT_STEP, NULL, anim_start, -anim_end);
	}
	else
	{
		anim_end = lv_obj_get_width(ui_music_id3_text_panel) + 150;
		__music_msg("prev:anim_start=%d, anim_end=%d\n", anim_start, anim_end);
		ui_snapshot_anim(ui_snapshot_anim_set_para(ui_music_id3_text_panel, ui_music_id3_text, NULL, NULL, 0, 0, 0, 0, anim_start, anim_end, 0, 0, 0, 0), SLIDE_OUT_STEP, 200);
		//playerinfo_Animation(ui_music_id3_text_panel, (void *)music_para, 0, SLIDE_OUT_STEP, NULL, anim_start, anim_end);
	}
	
    return EPDK_OK;
}
#endif

static __s32 ui_music_draw_listview_item_text(__lv_draw_para_t *draw_param, lv_obj_t * text, __u8 index)
{
	int ret;
	__s32 item_index;
	char FileName[RAT_MAX_FULL_PATH_LEN];
	char *name = NULL;
	rat_media_type_t media_type;
	char LastFileName[RAT_MAX_FULL_PATH_LEN];
	reg_root_para_t *root_para = NULL;	
	root_para = dsk_reg_get_para_by_app(REG_APP_ROOT);
	ui_music_para_t * music_para = (ui_music_para_t *)draw_param->attr;

#ifdef USB_BROSWER_ALBUM_ARTIST_SORT
    if(music_para->menu_win == MUSIC_MENU_WIN_BROWSE)
    {
		__music_msg("list_sort_layer=%d\n", music_para->list_sort_layer);
        if(music_para->list_sort_layer == 0)
        {
            if(0 == draw_param->index)
            {
                sprintf(FileName,"Album");
                ui_source_title(music_para->source_para, "Category List", 0);
            }
            else if(1 == draw_param->index)
            {
                sprintf(FileName,"Artist");
            }
            else if(2 == draw_param->index)
            {
                sprintf(FileName,"All Songs");
            }
        }
        else
        {
            __music_msg("list_sort_type=%d\n", music_para->list_sort_type);
			if(music_para->list_sort_type==0)
			{
				if(music_para->song_layer_flag==0)
				{
					if(draw_param->index==0)
					{
						sprintf(FileName,"Album");
                        ui_source_title(music_para->source_para, "Album", 0);
					}
					else
					{
						get_list_album_list(music_para->album_list_hdle,draw_param->index-1,FileName);
						
						//ExtractFileName(FileName, FilePath);
						__music_msg("FileName=%s\n",FileName);
					}
				}
				else
				{
					__music_msg("cur_album_index:%d\n",music_para->cur_album_index);
					if(draw_param->index==0)
					{
						get_list_album_list(music_para->album_list_hdle,music_para->cur_album_index,FileName);
					}
					else
					{
						#ifdef USB_BROSWER_SAVE_INDEX_SORT
						ret = get_list_album_music_list(music_para->album_list_hdle,music_para->cur_album_index,draw_param->index-1);
						__music_msg("ret=%d\n",ret);
						//index = GetListItemDirIndex(music_para->ui_list_para.rat.handle, ret);
						ret = GetListItemFileName(&music_para->ui_list_para, ret, FileName);
						//__music_msg("index=%d\n",index);
						//GetListItemFileName(&music_para->ui_list_para, index, FileName);
						#else
						get_list_album_music_list(music_para->album_list_hdle,music_para->cur_album_index,draw_param->index-1,FilePath);
						ExtractFileName(FileName, FilePath);
						#endif
						
						__music_msg("FileName=%s\n",FileName);
					}
				}
			}
			else if(music_para->list_sort_type==1)
			{
				if(music_para->song_layer_flag==0)
				{
					if(draw_param->index==0)
					{
						sprintf(FileName,"Artist");
                        ui_source_title(music_para->source_para, "Artist", 0);
					}
					else
					{
						get_list_artist_list(music_para->artist_list_hdle,draw_param->index-1,FileName);
						//ExtractFileName(FileName, FilePath);
						__music_msg("FileName=%s\n",FileName);
					}
				}
				else
				{
					__music_msg("cur_artist_index:%d\n",music_para->cur_artist_index);
					if(draw_param->index==0)
					{
						get_list_artist_list(music_para->artist_list_hdle,music_para->cur_artist_index,FileName);
					}
					else
					{
						#ifdef USB_BROSWER_SAVE_INDEX_SORT
						ret = get_list_artist_music_list(music_para->artist_list_hdle,music_para->cur_artist_index,draw_param->index-1);
						__music_msg("ret=%d\n",ret);
						ret = GetListItemFileName(&music_para->ui_list_para, ret, FileName);
						#else
						get_list_artist_music_list(music_para->artist_list_hdle,music_para->cur_artist_index,draw_param->index-1,FilePath);
						ExtractFileName(FileName, FilePath);
						#endif
						__music_msg("FileName=%s\n",FileName);
					}
				}
			}
			else if(music_para->list_sort_type==2)
			{
				if(music_para->song_layer_flag==1)
				{
					if(draw_param->index==0)
					{
						sprintf(FileName,"All Songs");
                        ui_source_title(music_para->source_para, "All Songs", 0);
					}
					else
					{
						#ifdef USB_BROSWER_SAVE_INDEX_SORT
						ret = get_list_title_list(music_para->title_list_hdle,draw_param->index-1);
						__music_msg("ret=%d\n",ret);
						ret = GetListItemFileName(&music_para->ui_list_para, ret, FileName);
						#else
						get_list_title_list(music_para->title_list_hdle,draw_param->index-1,FilePath);
						ExtractFileName(FileName, FilePath);
						#endif
						__music_msg("FileName=%s\n",FileName);
					}
				}	
			}
		}
    }
    else 
#endif
    {
    	//__music_msg("draw_param->index = %d\n", draw_param->index);
    	item_index = draw_param->index;
    	eLIBs_memset(FileName,0,sizeof(FileName));
    	ret = GetListItemFileName(&music_para->ui_list_para, item_index, FileName);
    	if(ret == EPDK_FAIL)
    	{
    		__wrn("get file information form rat fail!!");
    		return EPDK_FAIL;
    	}

    	if(0 == item_index)
    	{
    		if(RAT_TF ==music_para->ui_list_para.root_type)
    		{
    			dsk_langres_get_menu_text(STRING_EXPLR_TFROOT,FileName, RAT_MAX_FULL_PATH_LEN);
    		}
    		else if(RAT_USB==music_para->ui_list_para.root_type)
    		{
    			dsk_langres_get_menu_text(STRING_EXPLR_USBROOT,FileName, RAT_MAX_FULL_PATH_LEN);
    		}
    		else
    		{
    			dsk_langres_get_menu_text(STRING_EXPLR_USBROOT,FileName, RAT_MAX_FULL_PATH_LEN);
    		}
    	}

    	media_type = GetListItemFileMediaType(music_para->ui_list_para.rat.handle, item_index);
    		
    }
	
    lv_label_set_text(text, FileName);
    _ui_set_text_shadow_scroll(text, GUI_WHITE, GUI_BLACK, 3, 3);               
    
	return EPDK_OK;
}

static __s32 ui_music_draw_listview_item_icon(ui_music_para_t * music_para, lv_obj_t * icon, __u32 index, bool focus)
{
	__s32 ret = MAX_MUSIC_BMP_ITEM;
	__u32 icon2 = MAX_MUSIC_BMP_ITEM;
	const void * src = NULL;
	lv_obj_t * ui_list_item_icon2;
	rat_media_type_t media_file_type;
	char FileName[RAT_MAX_FULL_PATH_LEN];
	__s32 item_index;
	reg_root_para_t *root_para = NULL;	
	root_para = dsk_reg_get_para_by_app(REG_APP_ROOT);

#ifdef USB_BROSWER_ALBUM_ARTIST_SORT
    if(music_para->menu_win == MUSIC_MENU_WIN_BROWSE)
    {        
        //if(music_para->into_list_sort_flag==1)
        {
            if(music_para->list_sort_layer==0)
            {
                if(0 == index)
                {
                    ret = MUSIC_THEME_COM_BRO_ALBUM_BMP;
                }
                else if(1 == index)
                {
                    ret = MUSIC_THEME_COM_BRO_ART_BMP;
                }
                else if(2 == index)
                {
                    ret = MUSIC_THEME_COM_BRO_SONG_BMP;
                }
            }
            else
            {
                if(music_para->song_layer_flag==1)
                {
                    if(index==0)
                    {
                        if(focus)
                        {
                            ret = MUSIC_THEME_COM_BRO_FOLDER_BMP;
                        }
                        else
                            ret = MUSIC_THEME_COM_BRO_FOLDER_BMP;
                    }
                    else
                    {
                        if(music_para->list_sort_type==0)
                        {
                            eLIBs_memset(FileName,0,sizeof(FileName));  
                    #ifdef USB_BROSWER_SAVE_INDEX_SORT
                            ret = get_list_album_music_list(music_para->album_list_hdle,music_para->cur_album_index,index-1);
                            GetListItemFileFullPath(&music_para->ui_list_para, ret, FileName);  
                            //GetListItemFileFullPath(&music_para->ui_list_para, ret, FileName);
                    #else
                            ret = get_list_album_music_list(music_para->album_list_hdle,music_para->cur_album_index,index-1,FileName);
                    #endif
                            if(0 == eLIBs_strcmp( FileName, music_para->cur_music_path))
                            {
                                 __cedar_status_t  fsm_sta;
                                 fsm_sta = robin_get_fsm_status();
                                if(fsm_sta == CEDAR_STAT_PLAY || fsm_sta == CEDAR_STAT_FF || fsm_sta == CEDAR_STAT_RR)
                                {
                                    if(focus)
                                        ret = MUSIC_TEMP_COM_MEDIA_PLAY_BMP;
                                    else
                                        ret = MUSIC_TEMP_COM_MEDIA_PLAY_BMP;
                                }
                                else
                                {
                                    if(focus)
                                        ret = MUSIC_TEMP_COM_MEDIA_PAUSE_BMP;
                                    else
                                        ret = MUSIC_TEMP_COM_MEDIA_PAUSE_BMP;
                                }
                            }
                            else
                            {
                                if(focus)
                                    ret = MUSIC_THEME_COM_BRO_SONG_BMP;
                                else
                                    ret = MUSIC_THEME_COM_BRO_SONG_BMP;
                            }
                            
                        }
                        else if(music_para->list_sort_type==1)
                        {
                            eLIBs_memset(FileName,0,sizeof(FileName));  
                    #ifdef USB_BROSWER_SAVE_INDEX_SORT
                            ret = get_list_artist_music_list(music_para->artist_list_hdle,music_para->cur_artist_index,index-1);
                            GetListItemFileFullPath(&music_para->ui_list_para, ret, FileName);  
                    #else
                            ret = get_list_artist_music_list(music_para->artist_list_hdle,music_para->cur_artist_index,index-1,FileName);
                    #endif
                            if(0 == eLIBs_strcmp( FileName, music_para->cur_music_path))
                            {
                                 __cedar_status_t  fsm_sta;
                                 fsm_sta = robin_get_fsm_status();
                                if(fsm_sta == CEDAR_STAT_PLAY || fsm_sta == CEDAR_STAT_FF || fsm_sta == CEDAR_STAT_RR)
                                {
                                    if(focus)
                                        ret = MUSIC_TEMP_COM_MEDIA_PLAY_BMP;
                                    else
                                        ret = MUSIC_TEMP_COM_MEDIA_PLAY_BMP;
                                }
                                else
                                {
                                    if(focus)
                                        ret = MUSIC_TEMP_COM_MEDIA_PAUSE_BMP;
                                    else
                                        ret = MUSIC_TEMP_COM_MEDIA_PAUSE_BMP;
                                }
                            }
                            else
                            {
                                if(focus)
                                    ret = MUSIC_THEME_COM_BRO_SONG_BMP;
                                else
                                    ret = MUSIC_THEME_COM_BRO_SONG_BMP;
                            }
                            
                        }
                        else if(music_para->list_sort_type==2)
                        {
                            eLIBs_memset(FileName,0,sizeof(FileName));  
                    #ifdef USB_BROSWER_SAVE_INDEX_SORT
                            ret = get_list_title_list(music_para->title_list_hdle,index-1);
                            GetListItemFileFullPath(&music_para->ui_list_para, ret, FileName);  
                    #else
                            ret = get_list_title_list(music_para->title_list_hdle,index-1,FileName);
                    #endif
                            if(0 == eLIBs_strcmp( FileName, music_para->cur_music_path))
                            {
                                 __cedar_status_t  fsm_sta;
                                 fsm_sta = robin_get_fsm_status();
                                if(fsm_sta == CEDAR_STAT_PLAY || fsm_sta == CEDAR_STAT_FF || fsm_sta == CEDAR_STAT_RR)
                                {
                                    if(focus)
                                        ret = MUSIC_TEMP_COM_MEDIA_PLAY_BMP;
                                    else
                                        ret = MUSIC_TEMP_COM_MEDIA_PLAY_BMP;
                                }
                                else
                                {
                                    if(focus)
                                        ret = MUSIC_TEMP_COM_MEDIA_PAUSE_BMP;
                                    else
                                        ret = MUSIC_TEMP_COM_MEDIA_PAUSE_BMP;
                                }
                            }
                            else
                            {
                                if(focus)
                                    ret = MUSIC_THEME_COM_BRO_SONG_BMP;
                                else
                                    ret = MUSIC_THEME_COM_BRO_SONG_BMP;
                            }
                            
                        }   
                    }
                }
                else
                {
                    if(index==0)
                    {
                        if(focus)
                        {
                            ret = MUSIC_THEME_COM_BRO_FOLDER_BMP;
                        }
                        else
                            ret = MUSIC_THEME_COM_BRO_FOLDER_BMP;
                    }
                    else
                    {
                        if(focus)
                            ret = MUSIC_THEME_COM_BRO_FOLDER_BMP;
                        else
                            ret = MUSIC_THEME_COM_BRO_FOLDER_BMP;
                    }
                    
                }
            }
        }
    }
    else 
#endif
    {
    	item_index = index;
    	media_file_type = GetListItemFileMediaType(music_para->ui_list_para.rat.handle, item_index);

    	if(music_para->menu_media_type == RAT_MEDIA_TYPE_AUDIO)
    	{
    		//__music_msg("item_index = %d, root_para->last_music_index_ud = %d\n", item_index, root_para->last_music_index_ud);
    		if((media_file_type == RAT_MEDIA_TYPE_AUDIO) && (item_index == root_para->last_music_index_ud))
    		{
    			eLIBs_memset(FileName,0,sizeof(FileName));	
    			ret = GetListItemFileFullPath(&music_para->ui_list_para, item_index, FileName);
    			//__music_msg("index = %d, FileName:%s, root_para->last_music_path_ud:%s\n", item_index, FileName, root_para->last_music_path_ud);
    			if(0 == eLIBs_strcmp( FileName, root_para->last_music_path_ud))
    			{
    				__cedar_status_t  fsm_sta;
    				fsm_sta = robin_get_fsm_status();
    				if(fsm_sta == CEDAR_STAT_PLAY || fsm_sta == CEDAR_STAT_FF || fsm_sta == CEDAR_STAT_RR)
                    {
                        if(focus)
                            ret = MUSIC_TEMP_COM_MEDIA_PLAY_BMP;
                        else
                            ret = MUSIC_TEMP_COM_MEDIA_PLAY_BMP;
                    }
                    else
                    {
                        if(focus)
                            ret = MUSIC_TEMP_COM_MEDIA_PAUSE_BMP;
                        else
                            ret = MUSIC_TEMP_COM_MEDIA_PAUSE_BMP;
                    }
    			}
    			else
    			{
    				//if(focus)
    				//	ret = ID_EXP_MUSIC_ICON_FOCUSED;
    				//else
    					ret = MUSIC_THEME_COM_BRO_SONG_BMP;//ID_EXP_MUSIC_ICON_UNFOCUSED;
    			}
    		}
    		else
    		{
    			if(RAT_MEDIA_TYPE_AUDIO != media_file_type)
    			{
    				if(RAT_MEDIA_TYPE_FOLDER == media_file_type)
    				{
    					//if(focus)
    					//{
    					//	ret = ID_MUSIC_BROWSE_SELBAR_BG;
    					//	icon2 = ID_EXP_MAIN_FOLDER_ICON_UNFOCUSED;
    					//}
    					//else
    						ret = MUSIC_THEME_COM_BRO_FOLDER_BMP;//ID_EXP_MAIN_FOLDER_ICON_UNFOCUSED;
    				}
    				else
    				{
    					//if(focus)
    					//	ret = ID_EXP_FOLDER_ICON_FOCUSED;
    					//else
    						ret = MUSIC_THEME_COM_BRO_FOLDER_BMP;//ID_EXP_FOLDER_ICON_UNFOCUSED;
    				}
    			}
    			else
    			{
    				//if(focus)
    				//	ret = ID_MUSIC_USB_NP_B_S_BMP;//ID_EXP_MUSIC_ICON_FOCUSED;
    				//else
    					ret = MUSIC_THEME_COM_BRO_SONG_BMP;//ID_EXP_MUSIC_ICON_UNFOCUSED;
    			}
    		}
    	}
    	else if(music_para->menu_media_type == RAT_MEDIA_TYPE_VIDEO)
    	{
    		//__music_msg("media_file_type = %d, index = %d, root_para->last_movie_index_ud = %d\n", media_file_type, index, root_para->last_movie_index_ud);
    		/*if((media_file_type == RAT_MEDIA_TYPE_VIDEO) && (index == root_para->last_movie_index_ud))
    		{
    			eLIBs_memset(FileName,0,sizeof(FileName));	
    			ret = GetListItemFileFullPath(&music_para->ui_list_para, index, FileName);	
    			if(0 == eLIBs_strcmp( FileName, root_para->last_movie_path_ud))
    			{
    				 __cedar_status_t  fsm_sta;
    				 fsm_sta = robin_get_fsm_status();
    				if(fsm_sta == CEDAR_STAT_PLAY || fsm_sta == CEDAR_STAT_FF || fsm_sta == CEDAR_STAT_RR)
    				{
    					if(focus)
    						ret = ID_EXP_PLAYING_ICON_FOCUSED;
    					else
    						ret = ID_EXP_PLAYING_ICON_UNFOCUSED;
    				}
    				else
    				{
    					if(focus)
    						ret = ID_EXP_PAUSE_ICON_FOCUSED;
    					else
    						ret = ID_EXP_PAUSE_ICON_UNFOCUSED;
    				}
    			}
    			else
    			{
    				if(focus)
    					ret = ID_EXP_MOVIE_ICON_FOCUSED;
    				else
    					ret = ID_EXP_MOVIE_ICON_UNFOCUSED;
    			}
    		}
    		else*/
    		{
    			if(RAT_MEDIA_TYPE_VIDEO != media_file_type)
    			{
    				if(RAT_MEDIA_TYPE_FOLDER == media_file_type)
    				{
    					//if(focus)
    					//{
    					//	ret = ID_MUSIC_BROWSE_SELBAR_BG;
    					//	icon2 = ID_EXP_MAIN_FOLDER_ICON_UNFOCUSED;
    					//}
    					//else
    						ret = ID_EXP_MAIN_FOLDER_ICON_UNFOCUSED;
    				}
    				else
    				{
    					//if(focus)
    					//	ret = ID_EXP_FOLDER_ICON_FOCUSED;
    					//else
    						ret = ID_EXP_FOLDER_ICON_UNFOCUSED;
    				}
    			}
    			else
    			{
    				//if(focus)
    				//	ret = ID_EXP_MOVIE_ICON_FOCUSED;
    				//else
    					ret = ID_EXP_MOVIE_ICON_UNFOCUSED;
    			}
    		}
    	}
    }

    lv_img_set_src(icon, ui_music_get_res(music_para, ret));
    
    /*if(focus)
    {
        lv_obj_set_align(icon, LV_ALIGN_TOP_LEFT);
        lv_obj_set_x(icon, 0);
    }
    else*/
    {
        //lv_obj_set_align(icon, LV_ALIGN_LEFT_MID);
        //__music_msg("icon:media_file_type = %d\n", media_file_type);
        //if(RAT_MEDIA_TYPE_FOLDER == media_file_type)
        //  lv_obj_set_x(icon, 2);
        //else
        //  lv_obj_set_x(icon, 7);
    }
    
    if(icon2 != MAX_MUSIC_BMP_ITEM)
    {
        if(lv_obj_get_child_cnt(icon) == 0)
            ui_list_item_icon2 = lv_img_create(icon);
        else
            ui_list_item_icon2 = lv_obj_get_child(icon, 0);
        lv_obj_clear_flag(ui_list_item_icon2, LV_OBJ_FLAG_HIDDEN);
        lv_obj_set_align(ui_list_item_icon2, LV_ALIGN_LEFT_MID);
        //lv_obj_set_x(ui_list_item_icon2, 4);
        lv_img_set_src(ui_list_item_icon2, ui_music_get_res(music_para, icon2));
    }
    else
    {
        if(lv_obj_get_child_cnt(icon) != 0)
        {
            ui_list_item_icon2 = lv_obj_get_child(icon, 0);
            lv_obj_add_flag(ui_list_item_icon2, LV_OBJ_FLAG_HIDDEN);
        }
    }

	return ret;
}

static __s32 ui_music_view_list_draw_listview_item(__lv_draw_para_t *draw_param)
{
	__s32 ret = SOURCE_ITEM_TYPE_MUSIC;

	return ret; 
}


static __s32 ui_music_view_func_draw_listview_item_text(__lv_draw_para_t *draw_param, lv_obj_t * text, __u8 index)
{
	int ret;
	__u8 ucStringBuf[GUI_NAME_MAX + 1]={0};
	ui_music_para_t *music_para;
    reg_aux_para_t* last_aux_para;
    
    last_aux_para = (reg_aux_para_t*)dsk_reg_get_para_by_app(REG_APP_AUX);
	music_para = (ui_music_para_t *)draw_param->attr;		

    if(((music_para->party_work_source == PARTY_D_SOURCE_USB) || (music_para->party_work_source == PARTY_D_SOURCE_BT_AUDIO)))
    {
        if(index == SOURCE_ITEM_TEXT_TYPE_TEXT)
        {
            dsk_langres_get_menu_text(music_broadcast_res_func_item_string[draw_param->index], ucStringBuf, GUI_NAME_MAX);

            __music_msg("ucStringBuf = %s\n",ucStringBuf);
            lv_label_set_text(text, ucStringBuf);
        }
        else if(index == SOURCE_ITEM_TEXT_TYPE_CONTENT)
        {
            switch(draw_param->index)
            {
                case MUSIC_BROADCAST_MENU_FUNC_BROADCAST:
                {
                    dsk_langres_get_menu_text(STRING_ROOT_BROADCAST, ucStringBuf, GUI_NAME_MAX);

                    lv_label_set_text(text, ucStringBuf);
                }
                break;

                case MUSIC_BROADCAST_MENU_FUNC_SOURCE:
                {
                    if(music_para->work_source == ID_WORK_SOURCE_BT || (music_para->party_work_source == PARTY_D_SOURCE_BT_AUDIO))
                        lv_label_set_text(text, "Bluetooth");
                    else
                        lv_label_set_text(text, "USB");
                }
                break;
            
                case MUSIC_BROADCAST_MENU_FUNC_DEVICE_NAME:
                {
                    lv_label_set_text(text, music_para->source_para->party_info.own_info.name);
                }
                break;

#if 0
                case MUSIC_BROADCAST_MENU_FUNC_REPEAT:
                {
                    if(music_para->repeat_mode == MUSIC_MENU_REPEAT_ONE)
                        lv_label_set_text(text, "One");
                    else if(music_para->repeat_mode == MUSIC_MENU_REPEAT_FOLDER)
                        lv_label_set_text(text, "Folder");
                    else
                        lv_label_set_text(text, "All");
                }
                break;
            
                case MUSIC_BROADCAST_MENU_FUNC_SHUFFLE:
                {
                    if(music_para->random_mode == MUSIC_MENU_SHUFFLE_OFF)
                        lv_label_set_text(text, "Off");
                    else if(music_para->random_mode == MUSIC_MENU_SHUFFLE_FOLDER)
                        lv_label_set_text(text, "Folder");
                    else
                        lv_label_set_text(text, "Off");
                }
                break;
#endif
            }
        }
#if 0
        else if(index == SOURCE_ITEM_TEXT_TYPE_ROLLER)
        {
            switch(draw_param->index)
            {            
                case MUSIC_BROADCAST_MENU_FUNC_REPEAT:
                {
                    lv_roller_set_options(text, "One\nFolder\nAll", LV_ROLLER_MODE_NORMAL);
                    lv_roller_set_selected(text, music_para->repeat_mode, LV_ANIM_OFF);
                }
                break;
            
                case MUSIC_BROADCAST_MENU_FUNC_SHUFFLE:
                {
                    lv_roller_set_options(text, "Off\nFolder", LV_ROLLER_MODE_NORMAL);
                    lv_roller_set_selected(text, music_para->random_mode, LV_ANIM_OFF);
                }
                break;
            }
        }
#endif
    }
    else
    {
        if(index == SOURCE_ITEM_TEXT_TYPE_TEXT)
        {
            dsk_langres_get_menu_text(music_res_func_item_string[draw_param->index], ucStringBuf, GUI_NAME_MAX);

            __music_msg("ucStringBuf = %s\n",ucStringBuf);
            lv_label_set_text(text, ucStringBuf);
        }
        else if(index == SOURCE_ITEM_TEXT_TYPE_CONTENT)
        {
            switch(draw_param->index)
            {
                case MUSIC_MENU_FUNC_REPEAT:
                {
                    if(music_para->repeat_mode == MUSIC_MENU_REPEAT_ONE)
                        lv_label_set_text(text, "One");
                    else if(music_para->repeat_mode == MUSIC_MENU_REPEAT_FOLDER)
                        lv_label_set_text(text, "Folder");
                    else
                        lv_label_set_text(text, "All");
                }
                break;
            
                case MUSIC_MENU_FUNC_SHUFFLE:
                {
                    if(music_para->random_mode == MUSIC_MENU_SHUFFLE_OFF)
                        lv_label_set_text(text, "Off");
                    else if(music_para->random_mode == MUSIC_MENU_SHUFFLE_FOLDER)
                        lv_label_set_text(text, "Folder");
                    else
                        lv_label_set_text(text, "Off");
                }
                break;
            }
        }
        else if(index == SOURCE_ITEM_TEXT_TYPE_ROLLER)
        {
            switch(draw_param->index)
            {
                case MUSIC_MENU_FUNC_REPEAT:
                {
                    lv_roller_set_options(text, "One\nFolder\nAll", LV_ROLLER_MODE_NORMAL);
                    lv_roller_set_selected(text, music_para->repeat_mode, LV_ANIM_OFF);
                }
                break;
            
                case MUSIC_MENU_FUNC_SHUFFLE:
                {
                    lv_roller_set_options(text, "Off\nFolder", LV_ROLLER_MODE_NORMAL);
                    lv_roller_set_selected(text, music_para->random_mode, LV_ANIM_OFF);
                }
                break;
            }
        }
    }
	
	return EPDK_OK;
}

static __s32 ui_music_view_func_draw_listview_item(__lv_draw_para_t *draw_param)
{
	__s32 ret = SOURCE_ITEM_TYPE_ENTER;
	ui_music_para_t *music_para;
    reg_aux_para_t* last_aux_para;
    
    last_aux_para = (reg_aux_para_t*)dsk_reg_get_para_by_app(REG_APP_AUX);
	music_para = (ui_music_para_t *)draw_param->attr;		

    if(((music_para->party_work_source == PARTY_D_SOURCE_USB) || (music_para->party_work_source == PARTY_D_SOURCE_BT_AUDIO)))
    {
        switch(draw_param->index)
        {
            case MUSIC_BROADCAST_MENU_FUNC_BROADCAST:
            {
                ret = SOURCE_ITEM_TYPE_TEXT_ENTER;
            }
            break;
        
            case MUSIC_BROADCAST_MENU_FUNC_SOURCE:
            {
                ret = SOURCE_ITEM_TYPE_TEXT_ENTER;
            }
            break;

            case MUSIC_BROADCAST_MENU_FUNC_DEVICE_NAME:
            {
                ret = SOURCE_ITEM_TYPE_TEXT_ENTER;
            }
            break;
#if 0
            case MUSIC_BROADCAST_MENU_FUNC_REPEAT:
            {
                ret = SOURCE_ITEM_TYPE_TEXT_ENTER;//SOURCE_ITEM_TYPE_ROLLER;
            }
            break;

            case MUSIC_BROADCAST_MENU_FUNC_SHUFFLE:
            {
                ret = SOURCE_ITEM_TYPE_TEXT_ENTER;//SOURCE_ITEM_TYPE_ROLLER;
            }
            break;
#endif
        }
    }
    else
    {
        switch(draw_param->index)
        {
            case MUSIC_MENU_FUNC_REPEAT:
            {
                ret = SOURCE_ITEM_TYPE_TEXT_ENTER;//SOURCE_ITEM_TYPE_ROLLER;
            }
            break;
        
            case MUSIC_MENU_FUNC_SHUFFLE:
            {
                ret = SOURCE_ITEM_TYPE_TEXT_ENTER;//SOURCE_ITEM_TYPE_ROLLER;
            }
            break;
        }
    }

	return ret; 
}


static __s32 ui_music_view_draw_listview_item_text(__lv_draw_para_t *draw_param, lv_obj_t * text, __u8 index)
{
	__s32 ret = 0;
	ui_music_para_t * music_para;
	
	music_para = (ui_music_para_t *)draw_param->attr;				//for draw the picture  in different media type

	switch(music_para->menu_win)
	{
		case MUSIC_MENU_WIN_LIST:
		case MUSIC_MENU_WIN_BROWSE:
		{
		    ret = ui_music_draw_listview_item_text(draw_param, text, index);
		}
		break;
	
		case MUSIC_MENU_WIN_FUNC:
		{
			ret = ui_music_view_func_draw_listview_item_text(draw_param, text, index);
		}
		break;
	}

	return EPDK_OK;	
}

static __s32 ui_music_draw_listview_item(__lv_draw_para_t *draw_param)
{
	__s32 ret = 0;
	ui_music_para_t * music_para;
	
	music_para = (ui_music_para_t *)draw_param->attr;				//for draw the picture  in different media type

	switch(music_para->menu_win)
	{
		case MUSIC_MENU_WIN_LIST:
		case MUSIC_MENU_WIN_BROWSE:
		{
			 ret = ui_music_view_list_draw_listview_item(draw_param);
		}
		break;
	
		case MUSIC_MENU_WIN_FUNC:
		{
			ret = ui_music_view_func_draw_listview_item(draw_param);
		}
		break;
	}

	return ret;	
}

static void * ui_music_draw_item(__lv_draw_para_t *draw_param)
{	
	__s32 ret;
    __s32 item_type;
	ui_music_para_t * music_para = (ui_music_para_t *)draw_param->attr;
	
	music_para->menu_media_type = draw_param->type;

	//__music_msg("draw_param->index = %d, item_obj = 0x%x\n", draw_param->index, music_para->ui_list_para.item_obj);
	lv_obj_t * ui_list_item = lv_obj_create(music_para->ui_list_para.item_obj);
    lv_obj_remove_style_all(ui_list_item);
	lv_obj_set_width(ui_list_item, music_para->ui_list_para.item_width);
	lv_obj_set_height(ui_list_item, music_para->ui_list_para.item_height);
	lv_obj_clear_flag(ui_list_item, LV_OBJ_FLAG_SCROLLABLE);	  /// Flags
    lv_obj_add_flag(ui_list_item, LV_OBJ_FLAG_EVENT_BUBBLE | LV_OBJ_FLAG_GESTURE_BUBBLE);      /// Flags
	lv_obj_set_style_radius(ui_list_item, 0, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_bg_color(ui_list_item, lv_color_hex(0x000000), LV_PART_MAIN | LV_STATE_DEFAULT);
	lv_obj_set_style_bg_opa(ui_list_item, 255, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_border_width(ui_list_item, 2, LV_PART_MAIN | LV_STATE_FOCUSED);
    lv_obj_set_style_border_color(ui_list_item, lv_color_hex(0xff0000), LV_PART_MAIN | LV_STATE_FOCUSED);
    lv_obj_set_style_border_opa(ui_list_item, 255, LV_PART_MAIN | LV_STATE_FOCUSED);

    lv_obj_t * ui_list_item_panel = lv_obj_create(ui_list_item);
    lv_obj_remove_style_all(ui_list_item_panel);
    lv_obj_set_x(ui_list_item_panel, lv_pct(1));
    lv_obj_set_height(ui_list_item_panel, lv_pct(95));
    lv_obj_set_width(ui_list_item_panel, lv_pct(97));
	lv_obj_set_align(ui_list_item_panel, LV_ALIGN_LEFT_MID);
    lv_obj_add_flag(ui_list_item_panel, LV_OBJ_FLAG_EVENT_BUBBLE | LV_OBJ_FLAG_GESTURE_BUBBLE);      /// Flags
    lv_obj_clear_flag(ui_list_item_panel, LV_OBJ_FLAG_CLICKABLE | LV_OBJ_FLAG_SCROLLABLE);      /// Flags
    lv_obj_set_style_bg_color(ui_list_item_panel, lv_color_hex(0x303030), LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_bg_opa(ui_list_item_panel, 255, LV_PART_MAIN | LV_STATE_DEFAULT);
    //lv_obj_set_style_bg_image_src(ui_list_item_panel, ui_music_get_res(music_para, MUSIC_THEME_TEMP_COM_LIST_BG_BMP), LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_border_width(ui_list_item_panel, 0, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_bg_color(ui_list_item_panel, lv_color_hex(0x303030), LV_PART_MAIN | LV_STATE_FOCUSED);
    lv_obj_set_style_bg_opa(ui_list_item_panel, 255, LV_PART_MAIN | LV_STATE_FOCUSED);
    lv_obj_set_style_bg_grad_color(ui_list_item_panel, lv_color_hex(0xD90011), LV_PART_MAIN | LV_STATE_FOCUSED);
    lv_obj_set_style_bg_main_stop(ui_list_item_panel, 150, LV_PART_MAIN | LV_STATE_FOCUSED);
    lv_obj_set_style_bg_grad_stop(ui_list_item_panel, 255, LV_PART_MAIN | LV_STATE_FOCUSED);
    lv_obj_set_style_bg_grad_dir(ui_list_item_panel, LV_GRAD_DIR_VER, LV_PART_MAIN | LV_STATE_FOCUSED);
    //lv_obj_set_style_bg_opa(ui_list_item_panel, 0, LV_PART_MAIN | LV_STATE_DEFAULT);
    //lv_obj_set_style_bg_color(ui_list_item_panel, lv_color_hex(0xd90011), LV_PART_MAIN | LV_STATE_PRESSED);
    //lv_obj_set_style_bg_opa(ui_list_item_panel, 255, LV_PART_MAIN | LV_STATE_PRESSED);
    //lv_obj_set_style_radius(ui_list_item_panel, 5, LV_PART_MAIN | LV_STATE_DEFAULT);

    item_type = ui_music_draw_listview_item(draw_param);

    if(item_type == SOURCE_ITEM_TYPE_MUSIC)
    {
        lv_obj_t * ui_list_item_icon_panel = lv_obj_create(ui_list_item_panel);
        lv_obj_remove_style_all(ui_list_item_icon_panel);
        lv_obj_set_height(ui_list_item_icon_panel, lv_pct(100));
        lv_obj_set_width(ui_list_item_icon_panel, lv_pct(14));
    	lv_obj_set_align(ui_list_item_icon_panel, LV_ALIGN_LEFT_MID);
        lv_obj_clear_flag(ui_list_item_icon_panel, LV_OBJ_FLAG_CLICKABLE | LV_OBJ_FLAG_SCROLLABLE);      /// Flags
        lv_obj_set_style_bg_opa(ui_list_item_icon_panel, 0, LV_PART_MAIN | LV_STATE_DEFAULT);

    	lv_obj_t * ui_list_item_icon = lv_img_create(ui_list_item_icon_panel);
    	//lv_obj_set_x(ui_list_item_icon, lv_pct(1));
    	lv_obj_set_width(ui_list_item_icon, LV_SIZE_CONTENT);
    	lv_obj_set_height(ui_list_item_icon, LV_SIZE_CONTENT);
    	lv_obj_set_align(ui_list_item_icon, LV_ALIGN_CENTER);
    	ui_music_draw_listview_item_icon(music_para, ui_list_item_icon, draw_param->index, 1);

        lv_obj_t * ui_list_item_text_panel = lv_obj_create(ui_list_item_panel);
        lv_obj_remove_style_all(ui_list_item_text_panel);
    	lv_obj_set_x(ui_list_item_text_panel, lv_pct(14));
        lv_obj_set_height(ui_list_item_text_panel, lv_pct(100));
        lv_obj_set_width(ui_list_item_text_panel, lv_pct(86));
    	lv_obj_set_align(ui_list_item_text_panel, LV_ALIGN_LEFT_MID);
        lv_obj_clear_flag(ui_list_item_text_panel, LV_OBJ_FLAG_CLICKABLE | LV_OBJ_FLAG_SCROLLABLE);      /// Flags
        lv_obj_set_style_bg_opa(ui_list_item_text_panel, 0, LV_PART_MAIN | LV_STATE_DEFAULT);
        
    	lv_obj_t * ui_list_item_text = lv_label_create(ui_list_item_text_panel);
    	lv_obj_set_width(ui_list_item_text, lv_pct(100));//lv_pct(89)
    	lv_obj_set_height(ui_list_item_text, LV_SIZE_CONTENT);	  /// 0
    	lv_obj_set_align(ui_list_item_text, LV_ALIGN_LEFT_MID);
    	lv_label_set_long_mode(ui_list_item_text, LV_LABEL_LONG_CLIP);
    	lv_obj_set_style_text_color(ui_list_item_text, lv_color_hex(0xFFFFFF), LV_PART_MAIN | LV_STATE_DEFAULT);
    	lv_obj_set_style_text_opa(ui_list_item_text, 255, LV_PART_MAIN | LV_STATE_DEFAULT);
    	lv_obj_set_style_text_font(ui_list_item_text, lv_font_small.font, LV_PART_MAIN | LV_STATE_DEFAULT);
    	ui_music_draw_listview_item_text(draw_param, ui_list_item_text, 0);	

        lv_obj_add_event_cb(ui_list_item, ui_event_music_menu_item, LV_EVENT_ALL, music_para);
    }
    else
    {
        lv_obj_t * ui_list_item_text_panel = lv_obj_create(ui_list_item_panel);
        lv_obj_remove_style_all(ui_list_item_text_panel);
    	lv_obj_set_x(ui_list_item_text_panel, lv_pct(3));
        lv_obj_set_height(ui_list_item_text_panel, lv_pct(100));
        lv_obj_set_width(ui_list_item_text_panel, lv_pct(60));
    	lv_obj_set_align(ui_list_item_text_panel, LV_ALIGN_LEFT_MID);
        lv_obj_clear_flag(ui_list_item_text_panel, LV_OBJ_FLAG_CLICKABLE | LV_OBJ_FLAG_SCROLLABLE);      /// Flags
        lv_obj_set_style_bg_opa(ui_list_item_text_panel, 0, LV_PART_MAIN | LV_STATE_DEFAULT);
        
    	lv_obj_t * ui_list_item_text = lv_label_create(ui_list_item_text_panel);
    	lv_obj_set_width(ui_list_item_text, lv_pct(100));//lv_pct(89)
    	lv_obj_set_height(ui_list_item_text, LV_SIZE_CONTENT);	  /// 0
    	lv_obj_set_align(ui_list_item_text, LV_ALIGN_LEFT_MID);
    	lv_label_set_long_mode(ui_list_item_text, LV_LABEL_LONG_CLIP);
    	lv_obj_set_style_text_color(ui_list_item_text, lv_color_hex(0xFFFFFF), LV_PART_MAIN | LV_STATE_DEFAULT);
    	lv_obj_set_style_text_opa(ui_list_item_text, 255, LV_PART_MAIN | LV_STATE_DEFAULT);
    	lv_obj_set_style_text_font(ui_list_item_text, lv_font_small.font, LV_PART_MAIN | LV_STATE_DEFAULT);
    	ui_music_view_draw_listview_item_text(draw_param, ui_list_item_text, SOURCE_ITEM_TEXT_TYPE_TEXT);	

        lv_obj_t * ui_list_item_content_panel = lv_obj_create(ui_list_item_panel);
        lv_obj_remove_style_all(ui_list_item_content_panel);
        lv_obj_set_width(ui_list_item_content_panel, lv_pct(40));
        lv_obj_set_height(ui_list_item_content_panel, lv_pct(100));
        lv_obj_set_align(ui_list_item_content_panel, LV_ALIGN_RIGHT_MID);
        lv_obj_clear_flag(ui_list_item_content_panel, LV_OBJ_FLAG_CLICKABLE | LV_OBJ_FLAG_SCROLLABLE);        /// Flags
        lv_obj_add_flag(ui_list_item_content_panel, LV_OBJ_FLAG_EVENT_BUBBLE | LV_OBJ_FLAG_GESTURE_BUBBLE);    /// Flags
        lv_obj_set_style_bg_opa(ui_list_item_content_panel, 0, LV_PART_MAIN | LV_STATE_DEFAULT);
        lv_obj_set_style_pad_right(ui_list_item_content_panel, 5, LV_PART_MAIN | LV_STATE_DEFAULT);

        switch(item_type)
        {
            case SOURCE_ITEM_TYPE_ENTER:
            {
                lv_obj_t * ui_list_item_enter = lv_image_create(ui_list_item_content_panel);
                lv_image_set_src(ui_list_item_enter, ui_music_get_res(music_para, MUSIC_TEMP_COM_MENU_ENTER_BMP));
                lv_obj_set_width(ui_list_item_enter, LV_SIZE_CONTENT);   /// 1
                lv_obj_set_height(ui_list_item_enter, LV_SIZE_CONTENT);    /// 1
                lv_obj_set_x(ui_list_item_enter, lv_pct(30));    /// 1
                lv_obj_set_align(ui_list_item_enter, LV_ALIGN_RIGHT_MID);
                lv_obj_remove_flag(ui_list_item_enter, LV_OBJ_FLAG_SCROLLABLE);      /// Flags
    		}
            break;

            case SOURCE_ITEM_TYPE_ARROW:
            {
                lv_obj_clear_flag(ui_list_item, LV_OBJ_FLAG_CLICK_FOCUSABLE);      /// Flags
                
    		    lv_obj_t * ui_list_item_arrow = lv_obj_create(ui_list_item_content_panel);
                lv_obj_set_width(ui_list_item_arrow, 118);
                lv_obj_set_height(ui_list_item_arrow, 69);
                lv_obj_set_align(ui_list_item_arrow, LV_ALIGN_RIGHT_MID);
                lv_obj_remove_flag(ui_list_item_arrow, LV_OBJ_FLAG_CLICKABLE | LV_OBJ_FLAG_SCROLLABLE);      /// Flags
                lv_obj_add_flag(ui_list_item_arrow, LV_OBJ_FLAG_CHECKABLE);      /// Flags
                lv_obj_set_style_bg_color(ui_list_item_arrow, lv_color_hex(0xFFFFFF), LV_PART_MAIN | LV_STATE_DEFAULT);
                lv_obj_set_style_bg_opa(ui_list_item_arrow, 0, LV_PART_MAIN | LV_STATE_DEFAULT);
                lv_obj_set_style_bg_image_src(ui_list_item_arrow, ui_music_get_res(music_para, MUSIC_THEME_TEMP_COM_LIST_OFF_BMP),LV_PART_MAIN | LV_STATE_DEFAULT);
                lv_obj_set_style_border_width(ui_list_item_arrow, 0, LV_PART_MAIN | LV_STATE_DEFAULT);
                lv_obj_set_style_bg_image_src(ui_list_item_arrow, ui_music_get_res(music_para, MUSIC_THEME_TEMP_COM_LIST_ON_BMP),LV_PART_MAIN | LV_STATE_CHECKED);
                lv_obj_set_style_border_width(ui_list_item_arrow, 2, LV_PART_MAIN | LV_STATE_FOCUSED);
                lv_obj_set_style_border_color(ui_list_item_arrow, lv_color_hex(0xff0000), LV_PART_MAIN | LV_STATE_FOCUSED);
                lv_obj_set_style_border_opa(ui_list_item_arrow, 255, LV_PART_MAIN | LV_STATE_FOCUSED);
                lv_obj_add_state(ui_list_item_arrow, LV_STATE_CHECKED);

                lv_obj_t * ui_list_item_arrow_switch = lv_switch_create(ui_list_item_arrow);
                lv_obj_set_width(ui_list_item_arrow_switch, 90);
                lv_obj_set_height(ui_list_item_arrow_switch, 45);
                lv_obj_set_align(ui_list_item_arrow_switch, LV_ALIGN_CENTER);
                lv_obj_add_flag(ui_list_item_arrow_switch, LV_OBJ_FLAG_EVENT_BUBBLE);    /// Flags
                lv_obj_set_style_bg_color(ui_list_item_arrow_switch, lv_color_hex(0x272727), LV_PART_MAIN | LV_STATE_DEFAULT);
                lv_obj_set_style_bg_opa(ui_list_item_arrow_switch, 0, LV_PART_MAIN | LV_STATE_DEFAULT);
                lv_obj_set_style_bg_color(ui_list_item_arrow_switch, lv_color_hex(0x222121), LV_PART_MAIN | LV_STATE_CHECKED);
                lv_obj_set_style_bg_opa(ui_list_item_arrow_switch, 0, LV_PART_MAIN | LV_STATE_CHECKED);
                
                lv_obj_set_style_bg_color(ui_list_item_arrow_switch, lv_color_hex(0x272727), LV_PART_INDICATOR | LV_STATE_DEFAULT);
                lv_obj_set_style_bg_opa(ui_list_item_arrow_switch, 0, LV_PART_INDICATOR | LV_STATE_DEFAULT);
                lv_obj_set_style_bg_color(ui_list_item_arrow_switch, lv_color_hex(0x222121), LV_PART_INDICATOR | LV_STATE_CHECKED);
                lv_obj_set_style_bg_opa(ui_list_item_arrow_switch, 0, LV_PART_INDICATOR | LV_STATE_CHECKED);
                
                lv_obj_set_style_bg_color(ui_list_item_arrow_switch, lv_color_hex(0x575757), LV_PART_KNOB | LV_STATE_DEFAULT);
                lv_obj_set_style_bg_opa(ui_list_item_arrow_switch, 255, LV_PART_KNOB | LV_STATE_DEFAULT);
                lv_obj_set_style_bg_color(ui_list_item_arrow_switch, lv_color_hex(0xFFFFFF), LV_PART_KNOB | LV_STATE_CHECKED);
                lv_obj_set_style_bg_opa(ui_list_item_arrow_switch, 255, LV_PART_KNOB | LV_STATE_CHECKED);
                lv_obj_add_state(ui_list_item_arrow_switch, LV_STATE_CHECKED);

                lv_group_remove_obj(ui_list_item_arrow_switch);
    		}
            break;

            case SOURCE_ITEM_TYPE_TEXT_ENTER:
            {                
                lv_obj_t * ui_list_item_content_text = lv_label_create(ui_list_item_content_panel);
                lv_obj_set_width(ui_list_item_content_text, LV_SIZE_CONTENT);   /// 1
                lv_obj_set_height(ui_list_item_content_text, LV_SIZE_CONTENT);    /// 1
                lv_obj_set_x(ui_list_item_content_text, -15);
                lv_obj_set_align(ui_list_item_content_text, LV_ALIGN_RIGHT_MID);
                lv_obj_set_style_text_color(ui_list_item_content_text, lv_color_hex(0xFFFFFF), LV_PART_MAIN | LV_STATE_DEFAULT);
                lv_obj_set_style_text_opa(ui_list_item_content_text, 255, LV_PART_MAIN | LV_STATE_DEFAULT);
                lv_obj_set_style_text_font(ui_list_item_content_text, lv_font_small.font, LV_PART_MAIN | LV_STATE_DEFAULT);
                
                ui_music_view_draw_listview_item_text(draw_param, ui_list_item_content_text, SOURCE_ITEM_TEXT_TYPE_CONTENT);  

                lv_obj_t * ui_list_item_enter = lv_image_create(ui_list_item_content_panel);
                lv_image_set_src(ui_list_item_enter, ui_music_get_res(music_para, MUSIC_TEMP_COM_MENU_ENTER_BMP));
                lv_obj_set_width(ui_list_item_enter, LV_SIZE_CONTENT);   /// 1
                lv_obj_set_height(ui_list_item_enter, LV_SIZE_CONTENT);    /// 1
                //lv_obj_set_x(ui_list_item_enter, lv_pct(30));    /// 1
                lv_obj_set_align(ui_list_item_enter, LV_ALIGN_RIGHT_MID);
                lv_obj_remove_flag(ui_list_item_enter, LV_OBJ_FLAG_SCROLLABLE);      /// Flags
            }
            break;

            case SOURCE_ITEM_TYPE_ROLLER:
            {
                lv_obj_t * ui_list_item_content_roller = lv_roller_create(ui_list_item_content_panel);
                lv_obj_set_height(ui_list_item_content_roller, lv_pct(100));
                lv_obj_set_width(ui_list_item_content_roller, lv_pct(100));
                lv_obj_set_align(ui_list_item_content_roller, LV_ALIGN_RIGHT_MID);
                lv_obj_remove_flag(ui_list_item_content_roller, LV_OBJ_FLAG_CLICKABLE);
                lv_obj_set_style_text_color(ui_list_item_content_roller, lv_color_hex(0xFFFFFF), LV_PART_MAIN | LV_STATE_DEFAULT);
                lv_obj_set_style_text_opa(ui_list_item_content_roller, 255, LV_PART_MAIN | LV_STATE_DEFAULT);
                lv_obj_set_style_text_font(ui_list_item_content_roller, lv_font_small.font, LV_PART_MAIN | LV_STATE_DEFAULT);
                lv_obj_set_style_bg_color(ui_list_item_content_roller, lv_color_hex(0xFFFFFF), LV_PART_MAIN | LV_STATE_DEFAULT);
                lv_obj_set_style_bg_opa(ui_list_item_content_roller, 0, LV_PART_MAIN | LV_STATE_DEFAULT);
                lv_obj_set_style_border_width(ui_list_item_content_roller, 0, LV_PART_MAIN | LV_STATE_DEFAULT);        
                lv_obj_set_style_bg_color(ui_list_item_content_roller, lv_color_hex(0x252525), LV_PART_SELECTED | LV_STATE_DEFAULT);
                lv_obj_set_style_bg_opa(ui_list_item_content_roller, 0, LV_PART_SELECTED | LV_STATE_DEFAULT);
                //lv_obj_set_style_text_align(ui_list_item_content_roller, LV_TEXT_ALIGN_RIGHT, LV_PART_SELECTED | LV_STATE_DEFAULT);

                ui_music_view_draw_listview_item_text(draw_param, ui_list_item_content_roller, SOURCE_ITEM_TEXT_TYPE_ROLLER);  
    		}
            break;
        }

        lv_obj_add_event_cb(ui_list_item, ui_event_music_func_item, LV_EVENT_ALL, music_para);
    }
    
	return (void *)ui_list_item;
}

static void * ui_music_item_group_update(ui_music_para_t * music_para, __u8 flag)
{
    lv_obj_t * child = NULL;
    int32_t index = 0;

    if(music_para->menu_win == MUSIC_MENU_WIN_LIST || music_para->menu_win == MUSIC_MENU_WIN_BROWSE)
    {
        if(music_para->ui_list_para.item_obj == NULL)
        {
            return;
        }
        
        if(flag == 0)
        {
            child = lv_obj_get_child(music_para->ui_list_para.item_obj, music_para->ui_list_para.page_focus_id);
            
            __music_msg("page_focus_id=%d, child=%p\n", music_para->ui_list_para.page_focus_id, child);
        }
        else if(flag == 3)
        {
            music_para->menu_group_obj = NULL;
            //ui_source_menu_sel_group_update(music_para->source_para, &music_para->ui_list_para, ui_music_draw_listview_item, music_para, SOURCE_MENU_GROUP_REMOVE_FLAG, 0);
            ui_source_menu_sel_group_update(music_para->source_para, &music_para->ui_list_para, ui_music_draw_listview_item, music_para, SOURCE_MENU_GROUP_REMOVE_ALL_FLAG_ALL, 0);
        }
        else 
        {
            //if(music_para->source_para->menu_group_flag == SOURCE_MENU_GROUP_ADD)
            {
                if(flag == 1)
                {
                    index = lv_obj_get_index(music_para->menu_group_obj);
                    
                    index--;
                    if(index >= 0)
                    {
                        child = lv_obj_get_child(music_para->ui_list_para.item_obj, index);
                    
                        __music_msg("index=%d, child=%p\n", index, child);
                    }
                }
                else if(flag == 2)
                {
                    index = lv_obj_get_index(music_para->menu_group_obj);
                    
                    index++;
                    //if(index > 0)
                    {
                        child = lv_obj_get_child(music_para->ui_list_para.item_obj, index);
                    
                        __music_msg("index=%d, child=%p\n", index, child);
                    }
                }
            }
        }
        
        if(child)
        {
            lv_group_add_obj(lv_group_get_default(), child);
            lv_obj_add_flag(child, LV_OBJ_FLAG_SCROLL_ON_FOCUS);
            lv_group_focus_obj(child);
            if(music_para->menu_group_obj)
                lv_group_remove_obj(music_para->menu_group_obj);
            music_para->menu_group_obj = child;
        }
    }
    else if(music_para->menu_win != MUSIC_MENU_WIN_NONE)
    {
        if(flag == 0)
        {
            if(music_para->menu_win == MUSIC_MENU_WIN_FUNC)
            {
                ui_source_menu_sel_group_update(music_para->source_para, &music_para->ui_list_para, ui_music_draw_listview_item, music_para, SOURCE_MENU_GROUP_ADD, 0);
            }
            else
            {
                ui_source_menu_sel_group_update(music_para->source_para, &music_para->ui_list_para, ui_music_draw_listview_item, music_para, SOURCE_MENU_GROUP_ADD_SUB, 0);
            }
        }
        else if(flag == 3)
        {
            //ui_source_menu_sel_group_update(music_para->source_para, &music_para->ui_list_para, ui_music_draw_listview_item, music_para, SOURCE_MENU_GROUP_REMOVE_FLAG, 0);
            ui_source_menu_sel_group_update(music_para->source_para, &music_para->ui_list_para, ui_music_draw_listview_item, music_para, SOURCE_MENU_GROUP_REMOVE_ALL_FLAG_ALL, 0);
        }
        else if(flag == 4)
        {
            ui_source_menu_sel_group_update(music_para->source_para, &music_para->ui_list_para, ui_music_draw_listview_item, music_para, SOURCE_MENU_GROUP_REMOVE, 0);
        }
    }
}

static void music_view_funtion_listbar_init(ui_music_para_t * music_para)
{		
	if(!music_para->ui_list_para.item_obj)
	{
		__music_msg("menu_win=%d\n", music_para->menu_win);
        if(music_para->menu_win == MUSIC_MENU_WIN_LIST || music_para->menu_win == MUSIC_MENU_WIN_BROWSE)
        {
            lv_obj_add_flag(ui_music_play_screen, LV_OBJ_FLAG_HIDDEN);     /// Flags
		    music_para->music_menu_obj = ui_menu_create(&music_para->ui_list_para, APP_MUSIC_ID);
        }
        else
            music_para->music_menu_obj = ui_menu_create(&music_para->ui_list_para, APP_HOME_ID);

        ui_music_item_group_update(music_para, 0);

        if(music_para->theme > THEME_NIGHT)
            traverse_children_with_callback(music_para, music_para->music_menu_obj, music_para->theme_bmp_start, music_para->theme_bmp_end, music_para->theme_bmp_start, music_para->theme_bmp_end, music_para->ui_music_res_para.lv_music_icon, ui_music_get_res, NULL, 0);
#ifdef USE_MENU_ANIM
        ui_menu_on_anim(&music_para->ui_list_para, music_para->menu_anim);
#endif
		__music_msg("lv_obj_get_parent(music_para->ui_list_para.item_obj)=0x%x\n", lv_obj_get_parent(music_para->ui_list_para.item_obj));
	}
}

static void music_view_funtion_listbar_uninit(ui_music_para_t * music_para)
{
	if(music_para->ui_list_para.item_obj != NULL)
	{
		__music_msg("music_view_funtion_listbar_uninit:item_obj=0x%x\n", music_para->ui_list_para.item_obj);
		//lv_obj_clean(music_para->ui_list_para.item_obj);//删除父对象下的子对象，不删除本身
        lv_obj_clear_flag(ui_music_play_screen, LV_OBJ_FLAG_HIDDEN);     /// Flags
#ifdef USE_MENU_ANIM
        ui_menu_off_anim(music_para->menu_anim);
#endif
        ui_music_item_group_update(music_para, 3);

		ui_menu_destroy(music_para->music_menu_obj);
		music_para->ui_list_para.item_obj = NULL;
	}
}

static void ui_list_folder_reinit(ui_music_para_t * music_para)
{
	//music_para->list_scroll_flag = 0;
	//music_para->list_track_id = 0;
	music_view_funtion_listbar_uninit(music_para);
	music_view_funtion_listbar_init(music_para);
}

static void music_funtiton_win_listbar_init(ui_music_para_t * music_para)
{
#ifdef USB_BROSWER_ALBUM_ARTIST_SORT	
	__s32 index;

    if(music_para->menu_win == MUSIC_MENU_WIN_BROWSE)
    {        
    	music_para->ui_list_para.menu_obj = ui_music_menu;//ui_music_menu_panel;
        music_para->ui_list_para.list_attr = (void *)music_para;
    	music_para->ui_list_para.lbar_draw = (__lv_draw_item)ui_music_draw_item;
    	music_para->ui_list_para.rat.handle = music_para->rat_handle;
        
        //if(music_para->into_list_sort_flag==1)
        {
            
#ifdef USB_BROSWER_SORT_AT_BACKGROUND
            if(get_music_browser_flag()==1)
#endif
            {
                music_para->album_list_hdle=get_music_album_list();
                music_para->artist_list_hdle=get_music_artist_list();
                music_para->title_list_hdle=get_music_title_list();
                music_para->album_total=get_music_album_total();
                music_para->artist_total=get_music_artist_total();
                music_para->title_total=get_music_title_total();
                music_para->ui_list_para.album_list_hdle=music_para->album_list_hdle;
                music_para->ui_list_para.artist_list_hdle=music_para->artist_list_hdle;
                    music_para->ui_list_para.title_list_hdle=music_para->title_list_hdle;
                music_para->ui_list_para.album_total=music_para->album_total;
                music_para->ui_list_para.artist_total=music_para->artist_total;
                music_para->ui_list_para.title_total=music_para->title_total;
                __music_msg("music_para->into_list_sort_flag=%d\n", music_para->into_list_sort_flag);
                __music_msg("music_para->select_play_flag=%d\n", music_para->select_play_flag);
                __music_msg("music_para->list_sort_type=%d\n", music_para->list_sort_type);
                __music_msg("music_para->cur_music_path=%s\n", music_para->cur_music_path);
                if(music_para->select_play_flag==1)
                {
                    music_para->song_layer_flag = 1;
                    music_para->list_sort_layer = 1;
                    //robin_npl_get_cur();
                    index = robin_npl_get_cur();//GetListItemFileSerial(music_para->ui_list_para.rat.handle, robin_npl_get_cur());
                        __music_msg("index=%d\n", index);
                    if(music_para->list_sort_type==0)
                    {
                        
                        __music_msg("music_para->album_total=%d\n", music_para->album_total);
                        //music_para->cur_album_index = get_list_album_index(music_para->album_list_hdle,music_para->play_file_info.album,music_para->album_total);
				#ifdef USB_BROSWER_SAVE_INDEX_SORT
                        music_para->cur_album_index = get_list_cur_album(music_para->album_list_hdle,index,music_para->album_total);
				#else
                        music_para->cur_album_index = get_list_cur_album(music_para->album_list_hdle,music_para->cur_music_path,music_para->album_total);
				#endif
                        __music_msg("music_para->cur_album_index=%d\n", music_para->cur_album_index);
                        music_para->ui_list_para.start_id = 0;
                        music_para->ui_list_para.item_cnt = get_list_album_music_total(music_para->album_list_hdle,music_para->cur_album_index)+1;
				#ifdef USB_BROSWER_SAVE_INDEX_SORT
                        __music_msg("index=%d\n", index);
                        music_para->ui_list_para.focus_id  =get_list_album_music_index(music_para->album_list_hdle,music_para->cur_album_index,index)+1;
				#else
                        music_para->ui_list_para.focus_id  =get_list_album_music_index(music_para->album_list_hdle,music_para->cur_album_index,music_para->cur_music_path)+1;
				#endif
                    }
                    else if(music_para->list_sort_type==1)
                    {
                        
                        __music_msg("music_para->artist_total=%d\n", music_para->artist_total);
                        __music_msg("music_para->play_file_info.artist=%s\n", music_para->play_file_info.artist);
				#ifdef USB_BROSWER_SAVE_INDEX_SORT
                        music_para->cur_artist_index=get_list_cur_artist(music_para->artist_list_hdle,index,music_para->artist_total);
				#else
                        music_para->cur_artist_index=get_list_cur_artist(music_para->artist_list_hdle,music_para->cur_music_path,music_para->artist_total);
				#endif
                        //music_para->cur_artist_index = get_list_artist_index(music_para->artist_list_hdle,music_para->play_file_info.artist,music_para->artist_total);
                        music_para->ui_list_para.start_id = 0;
                        __music_msg("music_para->cur_artist_index=%d\n", music_para->cur_artist_index);
                        music_para->ui_list_para.item_cnt = get_list_artist_music_total(music_para->artist_list_hdle,music_para->cur_artist_index)+1;
				#ifdef USB_BROSWER_SAVE_INDEX_SORT
                        music_para->ui_list_para.focus_id =get_list_artist_music_index(music_para->artist_list_hdle,music_para->cur_artist_index,index)+1;  
				#else
                        music_para->ui_list_para.focus_id =get_list_artist_music_index(music_para->artist_list_hdle,music_para->cur_artist_index,music_para->cur_music_path)+1;
				#endif
                    }
                    else if(music_para->list_sort_type==2)
                    {
                        music_para->ui_list_para.start_id = 0;
                        music_para->ui_list_para.item_cnt = music_para->title_total+1;
				#ifdef USB_BROSWER_SAVE_INDEX_SORT
                        music_para->ui_list_para.focus_id =get_list_title_index(music_para->title_list_hdle,index,music_para->title_total)+1;       
				#else
                        music_para->ui_list_para.focus_id =get_list_title_index(music_para->title_list_hdle,music_para->cur_music_path,music_para->title_total)+1;  
				#endif
                    }   
                    
                    __music_msg("music_para->ui_list_para.focus_id=%d\n", music_para->ui_list_para.focus_id);
                }
                else
                {
                    if(music_para->list_sort_layer==0)
                    {
                        music_para->ui_list_para.start_id = 0;
                        music_para->ui_list_para.item_cnt = 3;
                        music_para->ui_list_para.focus_id  =0;
                    }
                    else
                    {
                        if(music_para->song_layer_flag==1)
                        {
                            if(music_para->list_sort_type==0)
                            {
                                music_para->ui_list_para.start_id = 0;
                                music_para->ui_list_para.item_cnt = get_list_album_music_total(music_para->album_list_hdle,music_para->cur_album_index)+1;
                                music_para->ui_list_para.focus_id  =0;
                            }
                            else if(music_para->list_sort_type==1)
                            {
                                music_para->ui_list_para.start_id = 0;
                                music_para->ui_list_para.item_cnt = get_list_artist_music_total(music_para->artist_list_hdle,music_para->cur_artist_index)+1;
                                music_para->ui_list_para.focus_id  =0;
                            }
                            else if(music_para->list_sort_type==2)
                            {
                                music_para->ui_list_para.start_id = 0;
                                music_para->ui_list_para.item_cnt = music_para->title_total+1;
                                music_para->ui_list_para.focus_id  =0;      
                            }   
                        }
                        else
                        {
                            if(music_para->list_sort_type==0)
                            {
                                music_para->ui_list_para.start_id = 0;
                                music_para->ui_list_para.item_cnt = music_para->album_total+1;
                                music_para->ui_list_para.focus_id  =0;
                            }
                            else if(music_para->list_sort_type==1)
                            {
                                music_para->ui_list_para.start_id = 0;
                                music_para->ui_list_para.item_cnt = music_para->artist_total+1;
                                music_para->ui_list_para.focus_id  =0;
                            }
                            else if(music_para->list_sort_type==2)
                            {
                                                
                            }   
                        }
                    }
                }
            }
            else
            {
                music_para->ui_list_para.start_id = 0;
                music_para->ui_list_para.item_cnt = 3;
                music_para->ui_list_para.focus_id = 0;
            }
        }
        __music_msg("rat.handle=0x%x, last_focused_id=%d, start_id=%d, item_cnt=%d, media_type=%d\n", music_para->ui_list_para.rat.handle, music_para->last_focused_id, music_para->ui_list_para.start_id, music_para->ui_list_para.item_cnt, music_para->media_type);
    }
    else
#endif
    {
    	music_para->ui_list_para.menu_obj = ui_music_menu;//ui_music_menu_panel;
        music_para->ui_list_para.list_attr = (void *)music_para;
    	music_para->ui_list_para.lbar_draw = (__lv_draw_item)ui_music_draw_item;
    	music_para->ui_list_para.rat.handle = music_para->rat_handle;
    	music_para->ui_list_para.root_type = music_para->root_type;
    	music_para->ui_list_para.media_type = music_para->media_type;
    	music_para->ui_list_para.start_id = GetListItemDirIndex(music_para->ui_list_para.rat.handle, music_para->last_focused_id);
    	music_para->ui_list_para.item_cnt = GetListItemDirFileTotal(music_para->ui_list_para.rat.handle,music_para->ui_list_para.start_id);
    	music_para->ui_list_para.focus_id = music_para->last_focused_id;
    	__music_msg("rat.handle=0x%x, last_focused_id=%d, start_id=%d, item_cnt=%d, media_type=%d\n", music_para->ui_list_para.rat.handle, music_para->last_focused_id, music_para->ui_list_para.start_id, music_para->ui_list_para.item_cnt, music_para->media_type);
    }

#ifdef USE_MENU_ANIM
    music_para->ui_list_para.use_anim = 1;
#endif

}

static void music_func_win_listbar_init(ui_music_para_t * music_para)
{
    reg_aux_para_t* last_aux_para;
    
    last_aux_para = (reg_aux_para_t*)dsk_reg_get_para_by_app(REG_APP_AUX);

    music_para->ui_list_para.menu_obj = music_para->ui_func_menu;
    music_para->ui_list_para.list_attr = (void *)music_para;
	music_para->ui_list_para.lbar_draw = (__lv_draw_item)ui_music_draw_item;
	music_para->ui_list_para.start_id = 0;
	music_para->ui_list_para.focus_id = 0;

    if(((music_para->party_work_source == PARTY_D_SOURCE_USB) || (music_para->party_work_source == PARTY_D_SOURCE_BT_AUDIO)))
        music_para->ui_list_para.item_cnt = MUSIC_BROADCAST_MENU_FUNC_MAX;
    else
        music_para->ui_list_para.item_cnt = MUSIC_MENU_FUNC_MAX;
	__music_msg("rat.handle=0x%x, last_focused_id=%d, start_id=%d, item_cnt=%d, media_type=%d\n", music_para->ui_list_para.rat.handle, music_para->last_focused_id, music_para->ui_list_para.start_id, music_para->ui_list_para.item_cnt, music_para->media_type);

}

static void ui_music_bottom_group_update(ui_music_para_t * music_para, __u8 flag, __u8 index, __u8 force)
{
    if(music_para->bottom_fouse_flag == flag && !force)
    {
        __music_msg("ui_music_item_group_update cur is flag=%d, force=%d, not need change!!!!\n", flag, force);
        
        return;
    }
    
    music_para->bottom_fouse_flag = flag;
    __music_msg("music_para->bottom_fouse_flag=%d, index=%d\n", music_para->bottom_fouse_flag, index);

    if(flag == 1)
    {
        ui_source_sel_group_update(music_para->source_para->ui_bottom, music_para->bottom_fouse_index, 1);
        ui_source_group_update(music_para->source_para, 0);
        music_para->bottom_fouse_index = 0;
    }
    else if(flag == 2)
    {
        music_para->bottom_fouse_index = index;
        ui_source_group_update(music_para->source_para, 0);
        ui_source_sel_group_update(music_para->source_para->ui_bottom, 0, 0);
    }
    else
    {
        ui_source_group_update(music_para->source_para, 1);
        ui_source_sel_group_update(music_para->source_para->ui_bottom, 0, 0);
        music_para->bottom_fouse_index = 0;
    }
}

static void ui_music_normal_win_open(ui_music_para_t * music_para)
{
    music_para->menu_win = MUSIC_MENU_WIN_NONE;

    ui_music_source_title(music_para, NULL, 1);
    if(music_para->bottom_fouse_flag == 0)
        ui_source_group_update(music_para->source_para, 1);
}

static void ui_music_normal_win_close(ui_music_para_t * music_para)
{

}

static void ui_music_list_win_open(ui_music_para_t * music_para)
{
    reg_root_para_t* last_root_para;
    
    last_root_para = (reg_root_para_t*)dsk_reg_get_para_by_app(REG_APP_ROOT);
    
    music_para->menu_win = MUSIC_MENU_WIN_LIST;
        
    if((music_para->work_source == ID_WORK_SOURCE_USB || (music_para->party_work_source == PARTY_D_SOURCE_USB)) && music_para->root_type != RAT_USBIPOD)
    {
        music_para->menu_win = MUSIC_MENU_WIN_LIST;

        ui_source_title(music_para->source_para, "Songs", 0);
        ui_source_sub_open(music_para->source_para);
        ui_music_menu_screen_init(music_para);
        music_funtiton_win_listbar_init(music_para);
        music_view_funtion_listbar_init(music_para);

        ui_music_bottom_group_update(music_para, 2, 0, 0);
    }
}

static void ui_music_list_win_close(ui_music_para_t * music_para)
{
    ui_music_bottom_group_update(music_para, 1, 0, 0);

    music_view_funtion_listbar_uninit(music_para);
    ui_music_menu_screen_uninit(music_para);

    ui_source_sub_close(music_para->source_para);
}

static void ui_music_browse_win_open(ui_music_para_t * music_para)
{
    reg_root_para_t* last_root_para;
    
    last_root_para = (reg_root_para_t*)dsk_reg_get_para_by_app(REG_APP_ROOT);
    
    music_para->menu_win = MUSIC_MENU_WIN_BROWSE;
        
    if((music_para->work_source == ID_WORK_SOURCE_USB || (music_para->party_work_source == PARTY_D_SOURCE_USB)) && music_para->root_type != RAT_USBIPOD)
    {
        music_para->menu_win = MUSIC_MENU_WIN_BROWSE;

        ui_source_title(music_para->source_para, "Category List", 0);
        ui_source_sub_open(music_para->source_para);
        ui_music_menu_screen_init(music_para);
        music_funtiton_win_listbar_init(music_para);
        music_view_funtion_listbar_init(music_para);

        ui_music_bottom_group_update(music_para, 2, 1, 0);
    }
}

static void ui_music_browse_win_close(ui_music_para_t * music_para)
{
    ui_music_bottom_group_update(music_para, 1, 0, 0);

    music_view_funtion_listbar_uninit(music_para);
    ui_music_menu_screen_uninit(music_para);

    ui_source_sub_close(music_para->source_para);
}

static void ui_music_func_win_open(ui_music_para_t * music_para)
{
    reg_aux_para_t* last_aux_para;

    last_aux_para = (reg_aux_para_t*)dsk_reg_get_para_by_app(REG_APP_AUX);

    music_para->menu_win = MUSIC_MENU_WIN_FUNC;

    ui_source_sub_open(music_para->source_para);
    
    ui_music_source_title(music_para, "Menu", 0);
    ui_music_bottom_group_update(music_para, 0, 0, 0);
    ui_music_func_menu_screen_init(music_para);
    
    music_func_win_listbar_init(music_para);
    music_view_funtion_listbar_init(music_para);
}

static void ui_music_func_win_close(ui_music_para_t * music_para)
{
    music_view_funtion_listbar_uninit(music_para);
    ui_music_func_menu_screen_uninit(music_para);

    ui_source_sub_close(music_para->source_para);
}

static void ui_music_broadcast_source_win_open(ui_music_para_t * music_para)
{
    music_para->menu_win = MUSIC_MENU_WIN_FUNC_BROADCAST_SOURCE;
    ui_music_broadcast_source_screen_init(music_para);
}

static void ui_music_broadcast_source_win_close(ui_music_para_t * music_para)
{
    ui_music_broadcast_source_screen_uninit(music_para);
}

static void ui_music_broadcast_name_win_open(ui_music_para_t * music_para)
{
    music_para->menu_win = MUSIC_MENU_WIN_FUNC_BROADCAST_DEVICE_NAME;
    lv_obj_clear_flag(music_para->ui_keyboard_panel, LV_OBJ_FLAG_HIDDEN);     /// Flags
    lv_group_focus_obj(music_para->ui_keyboar);
    lv_group_set_editing(lv_group_get_default(), true);
    ui_source_top_set_flag(music_para->source_para, LV_OBJ_FLAG_HIDDEN, 1);
}

static void ui_music_broadcast_name_win_close(ui_music_para_t * music_para)
{
    lv_obj_add_flag(music_para->ui_keyboard_panel, LV_OBJ_FLAG_HIDDEN);     /// Flags
    ui_source_top_set_flag(music_para->source_para, LV_OBJ_FLAG_HIDDEN, 0);
}

void ui_music_switch_win(ui_music_para_t * music_para, __u32 menu_win)
{
	__s32 ret;
    __u32 menu_win_old = music_para->menu_win;

    __music_msg("music_para->menu_win=%d, menu_win=%d\n", music_para->menu_win, menu_win);
    if(music_para->menu_win == menu_win)
    {
        __music_msg("cur is menu_win %d, not need change!!!!\n", menu_win);
        return;
    }
        
    switch(music_para->menu_win)
    {
        case MUSIC_MENU_WIN_NONE:
        {
            ui_music_normal_win_close(music_para);
        }
        break;
    
        case MUSIC_MENU_WIN_LIST:
        {
            ui_music_list_win_close(music_para);
        }
        break;
        
        case MUSIC_MENU_WIN_BROWSE:
        {
            ui_music_browse_win_close(music_para);
        }
        break;
        
        case MUSIC_MENU_WIN_FUNC:
        {
            ui_music_func_win_close(music_para);
        }
        break;
    
        case MUSIC_MENU_WIN_FUNC_BROADCAST_SOURCE:
        {
            ui_music_broadcast_source_win_close(music_para);
        }
        break;
        
        case MUSIC_MENU_WIN_FUNC_BROADCAST_DEVICE_NAME:
        {
            ui_music_broadcast_name_win_close(music_para);
        }
        break;
    }

    switch(menu_win)
    {
        case MUSIC_MENU_WIN_NONE:
        {
            ui_music_normal_win_open(music_para);
        }
        break;
    
        case MUSIC_MENU_WIN_LIST:
        {
            ui_music_list_win_open(music_para);
        }
        break;

        case MUSIC_MENU_WIN_BROWSE:
        {
            ui_music_browse_win_open(music_para);
        }
        break;

        case MUSIC_MENU_WIN_FUNC:
        {
            ui_music_func_win_open(music_para);
        }
        break;
    
        case MUSIC_MENU_WIN_FUNC_BROADCAST_SOURCE:
        {
            ui_music_broadcast_source_win_open(music_para);
        }
        break;

        case MUSIC_MENU_WIN_FUNC_BROADCAST_DEVICE_NAME:
        {
            ui_music_broadcast_name_win_open(music_para);
        }
        break;
    }

#if 0
    if(menu_win_old == MUSIC_MENU_WIN_NONE && menu_win != MUSIC_MENU_WIN_LIST)
    {
        ui_music_item_group_update(music_para, 4);
    }
    else
    {
        ui_music_item_group_update(music_para, 0);
    }
#endif
}

///////////////////// FUNCTIONS ////////////////////
void ui_music_item_click(lv_obj_t * btn, ui_music_para_t * music_para)
{
	__s32 ret;
	rat_media_type_t media_type;
    uint32_t idx = lv_obj_get_index(btn);
	//uint32_t idx = lv_obj_get_user_data(btn);
	__u32 focused_id;

#ifdef USB_BROSWER_ALBUM_ARTIST_SORT
	focused_id = music_para->ui_list_para.page_start_id + idx;

    __music_msg("menu_win=%d, focused_id=%d\n",music_para->menu_win, focused_id);
    if(music_para->menu_win == MUSIC_MENU_WIN_BROWSE)
    {        
        __u16 index;
        //if(music_para->into_list_sort_flag==1)
        {
            
            music_para->album_list_hdle=get_music_album_list();
            music_para->artist_list_hdle=get_music_artist_list();
            music_para->title_list_hdle=get_music_title_list();
            music_para->album_total=get_music_album_total();
            music_para->artist_total=get_music_artist_total();
            music_para->title_total=get_music_title_total();
            music_para->ui_list_para.album_list_hdle=music_para->album_list_hdle;
            music_para->ui_list_para.artist_list_hdle=music_para->artist_list_hdle;
            music_para->ui_list_para.title_list_hdle=music_para->title_list_hdle;
            music_para->ui_list_para.album_total=music_para->album_total;
            music_para->ui_list_para.artist_total=music_para->artist_total;
            music_para->ui_list_para.title_total=music_para->title_total;
            __music_msg("11music_para->album_total=%d\n",music_para->album_total);
            __music_msg("11music_para->artist_total=%d\n",music_para->artist_total);
            __music_msg("music_para->list_sort_layer=%d\n",music_para->list_sort_layer);
            if(music_para->list_sort_layer==0)
            {
                if(focused_id==0)
                {
                    music_para->list_sort_layer =1;
                    music_para->list_sort_type=0;
                    music_para->song_layer_flag=0;
                    //music_para->cur_flod_start_id = 0;
                }
                else if(focused_id==1)
                {
                    music_para->list_sort_layer =1;
                    music_para->list_sort_type=1;
                    music_para->song_layer_flag=0;
                    //music_para->cur_flod_start_id = 0;
                }
                else if(focused_id==2)
                {
                    music_para->list_sort_layer =1;
                    music_para->list_sort_type=2;
                    music_para->song_layer_flag=1;
                }
                ui_music_explorer_Jumpto_sort_dir(music_para, &music_para->ui_list_para);
                ui_list_folder_reinit(music_para);
            }
            else 
            {
                __music_msg("music_para->list_sort_type=%d\n",music_para->list_sort_type);
                if(music_para->list_sort_type==0)
                {
                    __music_msg("music_para->song_layer_flag=%d\n",music_para->song_layer_flag);
                    if(music_para->song_layer_flag==0)
                    {
                        if(focused_id==0)
                        {
                            music_para->list_sort_layer =0;
                            music_para->list_sort_type=0;
                            music_para->song_layer_flag=0;
                            music_para->ui_list_para.start_id = 0;
                            music_para->ui_list_para.item_cnt = 3;
                            music_para->ui_list_para.focus_id  =0;
                            ui_list_folder_reinit(music_para);
                        }
                        else
                        {
                            music_para->cur_album_index=focused_id-1;
                            music_para->list_sort_layer = 2;
                            music_para->song_layer_flag=1;
                            //music_para->cur_flod_start_id = music_para->ui_list_para.start_id;
                            ui_music_explorer_Jumpto_sort_dir(music_para, &music_para->ui_list_para);
                            ui_list_folder_reinit(music_para);
                        }
                    }
                    else
                    {
                        if(focused_id==0)
                        {
                            music_para->list_sort_layer =1;
                            music_para->list_sort_type=0;
                            music_para->song_layer_flag=0;
                            ui_music_explorer_Jumpto_sort_dir(music_para, &music_para->ui_list_para);
                            ui_list_folder_reinit(music_para);
                        }
                        else
                        {
                            char *filename = music_para->play_file_info.filename;
    					#ifdef USB_BROSWER_SAVE_INDEX_SORT
                            index = get_list_album_music_list(music_para->album_list_hdle,music_para->cur_album_index,focused_id-1);
                            ret = GetListItemFileFullPath(&music_para->ui_list_para, index, filename);  
                            //ret=GetListItemFileFullPath(&music_para->ui_list_para, ret, filename);
                            if(music_para->play_index != index)
                            {
                                music_para->play_index = index;
                                if(ret != EPDK_FAIL)//mllee 120815
                                {
                                    music_para->select_play_flag  =1;
                                    //into_id3_list_select_play(music_para->select_play_flag);
                                    ui_music_switch_win(music_para, MUSIC_MENU_WIN_NONE);
                                    music_play_file(music_para, filename);  
                                }   
                            }
                            else
                            {
                                music_para->select_play_flag  =1;
                                //into_id3_list_select_play(music_para->select_play_flag);
                                ui_music_switch_win(music_para, MUSIC_MENU_WIN_NONE);
                                DrawMusicRootTypeorAlbumTagPic(music_para);
                            }   
    					#else
                            ret=get_list_album_music_list(music_para->album_list_hdle,music_para->cur_album_index,focused_id-1,filename);
                            if(ret != EPDK_FAIL)//mllee 120815
                            {
                                music_para->select_play_flag  =1;
                                //into_id3_list_select_play(music_para->select_play_flag);
                                ui_music_switch_win(music_para, MUSIC_MENU_WIN_NONE);
                                music_play_file(music_para, filename);  
                            }   
    					#endif
                            
                        }
                    }
                    
                }
                else if(music_para->list_sort_type==1)
                {
                    if(music_para->song_layer_flag==0)
                    {
                        if(focused_id==0)
                        {
                            music_para->list_sort_layer =0;
                            music_para->list_sort_type=0;
                            music_para->song_layer_flag=0;
                            music_para->ui_list_para.start_id = 0;
                            music_para->ui_list_para.item_cnt = 3;
                            music_para->ui_list_para.focus_id  =0;
                            ui_list_folder_reinit(music_para);
                        }
                        else
                        {
                            
                            music_para->cur_artist_index=focused_id-1;
                            music_para->list_sort_layer = 2;
                            music_para->song_layer_flag=1;
                            //music_para->cur_flod_start_id = music_para->ui_list_para.start_id;
                            ui_music_explorer_Jumpto_sort_dir(music_para, &music_para->ui_list_para);
                            ui_list_folder_reinit(music_para);
                        }
                    }
                    else
                    {
                        if(focused_id==0)
                        {
                            music_para->list_sort_layer =1;
                            music_para->list_sort_type=1;
                            music_para->song_layer_flag=0;
                            ui_music_explorer_Jumpto_sort_dir(music_para, &music_para->ui_list_para);
                            ui_list_folder_reinit(music_para);
                        }
                        else
                        {
                            char *filename = music_para->play_file_info.filename;
    					#ifdef USB_BROSWER_SAVE_INDEX_SORT
                            index = get_list_artist_music_list(music_para->artist_list_hdle,music_para->cur_artist_index,focused_id-1);
                            ret = GetListItemFileFullPath(&music_para->ui_list_para, index, filename);
                            if(music_para->play_index != index)
                            {
                                music_para->play_index = index;
                                if(ret != EPDK_FAIL)//mllee 120815
                                {
                                    music_para->select_play_flag  =1;
                                    //into_id3_list_select_play(music_para->select_play_flag); 
                                    ui_music_switch_win(music_para, MUSIC_MENU_WIN_NONE);
                                    music_play_file(music_para, filename);  
                                }   
                            }
                            else
                            {
                                music_para->select_play_flag  =1;
                                //into_id3_list_select_play(music_para->select_play_flag);
                                ui_music_switch_win(music_para, MUSIC_MENU_WIN_NONE);
                                DrawMusicRootTypeorAlbumTagPic(music_para);
                            }   
    					#else
                            ret=get_list_artist_music_list(music_para->artist_list_hdle,music_para->cur_artist_index,focused_id-1,filename);
                            if(ret != EPDK_FAIL)//mllee 120815
                            {
                                music_para->select_play_flag  =1;
                                //into_id3_list_select_play(music_para->select_play_flag);
                                ui_music_switch_win(music_para, MUSIC_MENU_WIN_NONE);
                                music_play_file(music_para, filename);  
                            }   
    					#endif
                            
                        }
                    }
                    
                }
                else if(music_para->list_sort_type==2)
                {
                    if(music_para->song_layer_flag==1)
                    {
                        if(focused_id==0)
                        {
                            music_para->list_sort_layer =0;
                            music_para->list_sort_type=0;
                            music_para->song_layer_flag=0;
                            music_para->ui_list_para.start_id = 0;
                            music_para->ui_list_para.item_cnt = 3;
                            music_para->ui_list_para.focus_id  =0;
                            ui_list_folder_reinit(music_para);
                        }
                        else
                        {
                            char *filename = music_para->play_file_info.filename;
    					#ifdef USB_BROSWER_SAVE_INDEX_SORT
                            index = get_list_title_list(music_para->title_list_hdle,focused_id-1);
                            ret = GetListItemFileFullPath(&music_para->ui_list_para, index, filename);
                            if(music_para->play_index != index)
                            {
                                music_para->play_index = index;
                                if(ret != EPDK_FAIL)//mllee 120815
                                {
                                    music_para->select_play_flag  =1;
                                    //into_id3_list_select_play(music_para->select_play_flag);
                                    ui_music_switch_win(music_para, MUSIC_MENU_WIN_NONE);
                                    music_play_file(music_para, filename);  
                                }   
                            }
                            else
                            {
                                music_para->select_play_flag  =1;
                                //into_id3_list_select_play(music_para->select_play_flag);
                                ui_music_switch_win(music_para, MUSIC_MENU_WIN_NONE);
                                DrawMusicRootTypeorAlbumTagPic(music_para);
                            }   
    					#else
                            ret=get_list_title_list(music_para->title_list_hdle,focused_id-1,filename);
                            if(ret != EPDK_FAIL)//mllee 120815
                            {
                                music_para->select_play_flag  =1;
                                //into_id3_list_select_play(music_para->select_play_flag);
                                ui_music_switch_win(music_para, MUSIC_MENU_WIN_NONE);
                                music_play_file(music_para, filename);  
                            }   
    					#endif
                            
                        }
                    }
                    
                }
            }
        }
    }
    else
#endif
    {
        music_para->select_play_flag = 0;
    	focused_id = music_para->ui_list_para.page_start_id + idx;
    	media_type = GetListItemFileMediaType(music_para->ui_list_para.rat.handle, focused_id);
    	__music_msg("idx = %d, focused_id = %d, media_type=%d\n", idx, focused_id, media_type);
    	if(RAT_MEDIA_TYPE_FOLDER == media_type)
    	{
    		if(0 == focused_id)
    		{
    			__music_msg("root dir!!!!!!\n");
    		}
    		else
    		{
    			ui_explorer_JumpToMainDir(&music_para->ui_list_para, focused_id);
    			ui_list_folder_reinit(music_para);
    		}
    	}
    	else if(RAT_MEDIA_TYPE_SUB_FOLDER == media_type)//
    	{
    		ui_explorer_JumpToSubDir(&music_para->ui_list_para, focused_id);
    		ui_list_folder_reinit(music_para);
    	}
    	else
    	{
    		__music_msg("music_para->menu_media_type=%d\n", music_para->menu_media_type);
    		if(music_para->menu_media_type == RAT_MEDIA_TYPE_AUDIO)
    		{
    			__music_msg("music_para->play_index=%d\n", music_para->play_index);
    			if(music_para->play_index != focused_id)
    			{
    				char *filename = music_para->play_file_info.filename;

    				music_para->play_index = focused_id;
    				ret = MusicGetPlayerItemFileFullPath(music_para->rat_handle, music_para->play_index, filename);
    				if(ret != EPDK_FAIL)//mllee 120815
    				{
                        ui_source_panel_close(music_para->source_para, LV_ANIM_ON);
    					music_view_funtion_listbar_uninit(music_para);
                        //if(music_para->source_para->rig_select_flag == RIG_FUNC)
                        //{
                        //    ui_music_bottom_menu_event_handler(music_para);
                        //}
                        ui_music_switch_win(music_para, MUSIC_MENU_WIN_NONE);
    					music_play_file(music_para, filename);	
    				}
    			}
    			else
    			{
                    ui_source_panel_close(music_para->source_para, LV_ANIM_ON);
    				music_view_funtion_listbar_uninit(music_para);
                    //if(music_para->source_para->rig_select_flag == RIG_FUNC)
                    //{
                    //    ui_music_bottom_menu_event_handler(music_para);
                    //}
                    ui_music_switch_win(music_para, MUSIC_MENU_WIN_NONE);
    				DrawMusicRootTypeorAlbumTagPic(music_para);
    			}
    		}
    		else if(music_para->menu_media_type == RAT_MEDIA_TYPE_VIDEO)
    		{
    			music_cmd2parent(NULL, SWITCH_TO_MOVIE, 0, focused_id);
    		}
        }
    }
}

static __s32 ui_music_key_proc(ui_music_para_t * music_para)
{
	__u16 index = 0;
	__u8 manual_cancel_auto_pty_seek = 0;
	static int last_key = 0;
	
	__music_msg("music_para->indev_data->event_code=%d..\n",music_para->indev_data->event_code);
	__music_msg("music_para->indev_data->key=%d..\n",music_para->indev_data->key);

	if( KEY_DOWN_ACTION == music_para->indev_data->event_code )
	{
		last_key = music_para->indev_data->key;
		
		switch(music_para->indev_data->key)
		{
			case GUI_MSG_KEY_LEFT:
			{
				
			}
			break;
			case GUI_MSG_KEY_RIGHT:
			{
				
			}
			break;
			default:
				break;
		}
	}
	else if( KEY_REPEAT_ACTION == music_para->indev_data->event_code )
	{
		switch(music_para->indev_data->key)
		{
			case GUI_MSG_KEY_LONGRIGHT:
			{
                if((music_para->work_source == ID_WORK_SOURCE_USB || (music_para->party_work_source == PARTY_D_SOURCE_USB)) && music_para->root_type != RAT_USBIPOD)
                    ui_music_player_ctrl_ff_rr_press(music_para, 1);
			}
            break;
            
			case GUI_MSG_KEY_LONGLEFT:
			{
                if((music_para->work_source == ID_WORK_SOURCE_USB || (music_para->party_work_source == PARTY_D_SOURCE_USB)) && music_para->root_type != RAT_USBIPOD)
                    ui_music_player_ctrl_ff_rr_press(music_para, 0);
			}
			break;	
            
            case GUI_MSG_KEY_LONGMENU:
            {
                if(GUI_MSG_KEY_MENU == last_key)
                {
                    if(music_para->menu_win == MUSIC_MENU_WIN_NONE)
                    {
                        ui_music_open_bar_menu_event_handler(music_para);
                    }
                    else
                    {
                        ui_music_top_right_event_handler(music_para);
                    }
                }
            }
            break;
		}
		
		last_key = music_para->indev_data->key;
	}
	else if( KEY_UP_ACTION == music_para->indev_data->event_code )
	{
		switch(music_para->indev_data->key)
		{
            case GUI_MSG_KEY_ENTER:
            {
                if(music_para->menu_win == MUSIC_MENU_WIN_NONE && music_para->source_para->menu_win == SOURCE_MENU_WIN_NONE && music_para->bottom_fouse_flag == 0)
                {
                    ui_music_bottom_group_update(music_para, 1, 0, 0);
                }
            }
            break;

            case GUI_MSG_KEY_UP:
            {
                ui_music_item_group_update(music_para, 1);
            }
            break;
            
            case GUI_MSG_KEY_DOWN:
            {
                ui_music_item_group_update(music_para, 2);
            }
            break;

			case GUI_MSG_KEY_RIGHT:
			{
				if(GUI_MSG_KEY_RIGHT == last_key)
				{
                    if((music_para->work_source == ID_WORK_SOURCE_USB || (music_para->party_work_source == PARTY_D_SOURCE_USB)) && music_para->root_type != RAT_USBIPOD)
                        ui_music_player_ctrl_prev_next(music_para, 1);
                    else
                        app_root_send2mcu_key(KEY_TOUCH_NEXT);
				}
			}
			break;

			case GUI_MSG_KEY_LONGRIGHT:
			{
				if(GUI_MSG_KEY_LONGRIGHT == last_key)
				{
                    if(music_para->work_source == ID_WORK_SOURCE_USB)
                        ui_music_player_ctrl_ff_rr_release(music_para, 1);
				}
			}
			break;
			
			case GUI_MSG_KEY_LEFT:
			{
				if(GUI_MSG_KEY_LEFT == last_key)
				{
                    if((music_para->work_source == ID_WORK_SOURCE_USB || (music_para->party_work_source == PARTY_D_SOURCE_USB)) && music_para->root_type != RAT_USBIPOD)
                        ui_music_player_ctrl_prev_next(music_para, 0);
                    else
                        app_root_send2mcu_key(KEY_TOUCH_PREV);
				}
			}
			break;

            case GUI_MSG_KEY_LONGLEFT:
			{
				if(GUI_MSG_KEY_LONGLEFT == last_key)
                {
                    if((music_para->work_source == ID_WORK_SOURCE_USB || (music_para->party_work_source == PARTY_D_SOURCE_USB)) && music_para->root_type != RAT_USBIPOD)
                        ui_music_player_ctrl_ff_rr_release(music_para, 0);
                }
			}
			break;

            case GUI_MSG_KEY_MENU:
            {
                if(GUI_MSG_KEY_MENU == last_key)
                {
                    root_app_para_t * root_app_para = app_root_get_para();
                    
                    eLIBs_printf("menu_win=%d, source_para->menu_win = %d, root_app_para->home_store=%d\n", music_para->menu_win, music_para->source_para->menu_win, root_app_para->home_store);
                    if(music_para->menu_win == MUSIC_MENU_WIN_NONE 
                    && music_para->source_para->menu_win == SOURCE_MENU_WIN_NONE 
                    && (root_app_para->home_store == 1))
                    {
                        if(music_para->bottom_fouse_flag == 1)
                        {
                            ui_music_bottom_group_update(music_para, 0, 0, 0);
                        }
                        else
                        {
                            ui_music_source_event_handler(music_para);
                        }
                    }
                    else
                    {
                        if(music_para->menu_win > MUSIC_MENU_WIN_NONE 
                        && music_para->menu_win <= MUSIC_MENU_WIN_FUNC 
                        && music_para->source_para->menu_group_flag == SOURCE_MENU_GROUP_ADD 
                        && music_para->source_para->menu_win != SOURCE_MENU_WIN_MODE)
                        {
                            ui_source_menu_sel_group_update(music_para->source_para, &music_para->ui_list_para, ui_music_draw_listview_item, music_para, SOURCE_MENU_GROUP_REMOVE_SUB, 0);
                        }
                        else
                        {
                            ui_music_top_left_event_handler(music_para);
                        }
                    }
                }
            }
            break;

            case GUI_MSG_KEY_PLAY_PAUSE:
            {
                if(GUI_MSG_KEY_PLAY_PAUSE == last_key)
                {
                    if((music_para->work_source == ID_WORK_SOURCE_USB || (music_para->party_work_source == PARTY_D_SOURCE_USB)) && music_para->root_type != RAT_USBIPOD)
                        ui_music_player_ctrl_play_pause(music_para);
                    else
                        app_root_send2mcu_key(KEY_TOUCH_PLAYPAUSE);
                }
            }
            break;
						
			default:
				break;
		}
		
		last_key = 0;
	}
	
	return EPDK_OK;
}

void __ui_music_power_on(ui_music_para_t * music_para)
{
	if(/*!(is_force_mute()) && */robin_get_fsm_status() == CEDAR_STAT_PAUSE)
	{
		robin_set_cmd_play();
	

		music_para->playsta = music_stashow_play;
		//music_playsta_scene_create(this);
	}
}

void __ui_music_power_off(ui_music_para_t * music_para)
{
	if(/*is_force_mute() && */robin_get_fsm_status() == CEDAR_STAT_PLAY)
	{
		robin_set_cmd_pause();
	
		music_para->playsta = music_stashow_pause;
		//music_playsta_scene_create(this);
	}
}

static void ui_music_set_obj_theme_src(void * para, lv_obj_t * obj, __u32 theme)
{
    ui_music_para_t * music_para = (ui_music_para_t *)para;
    __u32 i, bmp_cnt, icon;
    __u32 bmp_start, bmp_end;
    __u32 color = 0;
    void * style_bg_image_release_src = NULL;
    void * style_bg_image_pressed_src = NULL;
    void * image_src = NULL;
    void * imagebutton_release_src = NULL;
    void * imagebutton_pressed_src = NULL;
    void * imagebutton_checked_release_src = NULL;
    void * imagebutton_checked_pressed_src = NULL;

    bmp_start = music_para->theme_bmp_start;
    bmp_end = music_para->theme_bmp_end;
    bmp_cnt = bmp_end - bmp_start;
    
    //eLIBs_printf("bmp_start=%d, bmp_end=%d, bmp_cnt=%d\n", bmp_start, bmp_end, bmp_cnt);
    
    style_bg_image_release_src = lv_obj_get_style_bg_image_src(obj, LV_PART_MAIN | LV_STATE_DEFAULT);
    style_bg_image_pressed_src = lv_obj_get_style_bg_image_src(obj, LV_PART_MAIN | LV_STATE_PRESSED);
    color = -(lv_color_to_int(lv_obj_get_style_bg_color(obj, LV_PART_MAIN | LV_STATE_DEFAULT)));
    lv_obj_set_style_bg_color(obj, lv_color_hex(color), LV_PART_MAIN | LV_STATE_DEFAULT);
    
    color = -(lv_color_to_int(lv_obj_get_style_bg_grad_color(obj, LV_PART_MAIN | LV_STATE_DEFAULT)));
    lv_obj_set_style_bg_grad_color(obj, lv_color_hex(color), LV_PART_MAIN | LV_STATE_DEFAULT);
    
    if(eLIBs_strcmp(obj->class_p->name,"label") == 0)
    {
        color = -(lv_color_to_int(lv_obj_get_style_text_color(obj, LV_PART_MAIN | LV_STATE_DEFAULT)));
        lv_obj_set_style_text_color(obj, lv_color_hex(color), LV_PART_MAIN | LV_STATE_DEFAULT);
    }
    else if(eLIBs_strcmp(obj->class_p->name,"image") == 0)
    {
        image_src = lv_img_get_src(obj);
    }
    else if(eLIBs_strcmp(obj->class_p->name,"imagebutton") == 0)
    {
        imagebutton_release_src = lv_imagebutton_get_src_middle(obj, LV_IMAGEBUTTON_STATE_RELEASED);
        imagebutton_pressed_src = lv_imagebutton_get_src_middle(obj, LV_IMAGEBUTTON_STATE_PRESSED);
        imagebutton_checked_release_src = lv_imagebutton_get_src_middle(obj, LV_IMAGEBUTTON_STATE_CHECKED_RELEASED);
        imagebutton_checked_pressed_src = lv_imagebutton_get_src_middle(obj, LV_IMAGEBUTTON_STATE_CHECKED_PRESSED);
    }
    else if(eLIBs_strcmp(obj->class_p->name,"slider") == 0)
    {
        color = -(lv_color_to_int(lv_obj_get_style_bg_color(obj, LV_PART_INDICATOR | LV_STATE_DEFAULT)));
        lv_obj_set_style_bg_color(obj, lv_color_hex(color), LV_PART_INDICATOR | LV_STATE_DEFAULT);        

        color = -(lv_color_to_int(lv_obj_get_style_bg_color(obj, LV_PART_KNOB | LV_STATE_DEFAULT)));
        lv_obj_set_style_bg_color(obj, lv_color_hex(color), LV_PART_KNOB | LV_STATE_DEFAULT);
    }

    for(i = bmp_start; i <= bmp_end; i++)
    {
        if(style_bg_image_release_src && style_bg_image_release_src == &music_para->ui_music_res_para.lv_music_icon[i])
        {
            icon = MUSIC_THEME_TEMP_MEDIA_SETUP_REPEAT_B_BMP + (i - bmp_start);

            if(icon <= MUSIC_THEME_TEMP_COM_RIG_FUNC_ICON_BMP)
            {
                lv_obj_set_style_bg_image_src(obj, ui_music_get_res(music_para, icon), LV_PART_MAIN | LV_STATE_DEFAULT);
            }
        }

        if(style_bg_image_pressed_src && style_bg_image_pressed_src == &music_para->ui_music_res_para.lv_music_icon[i])
        {
            icon = MUSIC_THEME_TEMP_MEDIA_SETUP_REPEAT_B_BMP + (i - bmp_start);

            if(icon <= MUSIC_THEME_TEMP_COM_RIG_FUNC_ICON_BMP)
            {
                lv_obj_set_style_bg_image_src(obj, ui_music_get_res(music_para, icon), LV_PART_MAIN | LV_STATE_PRESSED);
            }
        }

        if(image_src && image_src == &music_para->ui_music_res_para.lv_music_icon[i])
        {
            icon = MUSIC_THEME_TEMP_MEDIA_SETUP_REPEAT_B_BMP + (i - bmp_start);
        
            if(icon <= MUSIC_THEME_TEMP_COM_RIG_FUNC_ICON_BMP)
            {
                lv_img_set_src(obj, ui_music_get_res(music_para, icon));
            }
        }

        if(imagebutton_release_src && imagebutton_release_src == &music_para->ui_music_res_para.lv_music_icon[i])
        {
            icon = MUSIC_THEME_TEMP_MEDIA_SETUP_REPEAT_B_BMP + (i - bmp_start);
        
            if(icon <= MUSIC_THEME_TEMP_COM_RIG_FUNC_ICON_BMP)
            {
                lv_imagebutton_set_src(obj, LV_IMAGEBUTTON_STATE_RELEASED, NULL, ui_music_get_res(music_para, icon), NULL);
            }
        }

        if(imagebutton_pressed_src && imagebutton_pressed_src == &music_para->ui_music_res_para.lv_music_icon[i])
        {
            icon = MUSIC_THEME_TEMP_MEDIA_SETUP_REPEAT_B_BMP + (i - bmp_start);
        
            if(icon <= MUSIC_THEME_TEMP_COM_RIG_FUNC_ICON_BMP)
            {
                lv_imagebutton_set_src(obj, LV_IMAGEBUTTON_STATE_PRESSED, NULL, ui_music_get_res(music_para, icon), NULL);
            }
        }
        
        if(imagebutton_checked_release_src && imagebutton_checked_release_src == &music_para->ui_music_res_para.lv_music_icon[i])
        {
            icon = MUSIC_THEME_TEMP_MEDIA_SETUP_REPEAT_B_BMP + (i - bmp_start);
        
            if(icon <= MUSIC_THEME_TEMP_COM_RIG_FUNC_ICON_BMP)
            {
                lv_imagebutton_set_src(obj, LV_IMAGEBUTTON_STATE_CHECKED_RELEASED, NULL, ui_music_get_res(music_para, icon), NULL);
            }
        }

        if(imagebutton_checked_pressed_src && imagebutton_checked_pressed_src == &music_para->ui_music_res_para.lv_music_icon[i])
        {
            icon = MUSIC_THEME_TEMP_MEDIA_SETUP_REPEAT_B_BMP + (i - bmp_start);
        
            if(icon <= MUSIC_THEME_TEMP_COM_RIG_FUNC_ICON_BMP)
            {
                lv_imagebutton_set_src(obj, LV_IMAGEBUTTON_STATE_CHECKED_PRESSED, NULL, ui_music_get_res(music_para, icon), NULL);
            }
        }
    }
}

static void ui_music_night_theme(ui_music_para_t * music_para, __s32 theme)
{
	__music_msg("ui_music_night_theme\n");

    if(music_para->theme == theme)
    {
        __music_msg("cur is theme %d, not need change!!!!\n", theme);
        return;
    }
    
    if(theme == THEME_NIGHT)
    {
        MUSIC_THEME_TEMP_MEDIA_SETUP_REPEAT_B_BMP        = MUSIC_TEMP_NIGHT_NIG_MEDIA_SETUP_REPEAT_B_BMP;  
        MUSIC_THEME_USB_ICON_BMP                         = MUSIC_NIG_USB_ICON_BMP;
        MUSIC_THEME_BT_ICON_BMP                          = MUSIC_NIG_BT_ICON_BMP;
        MUSIC_THEME_COM_BOT_BG_BMP                    = MUSIC_COMMON_NIG_COM_BOT_BG_BMP;  
        MUSIC_THEME_COM_BOT_ICON_BG_A_N_BMP           = MUSIC_COMMON_NIG_COM_BOT_ICON_BG_A_N_BMP;  
        MUSIC_THEME_COM_BOT_ICON_BG_A_P_S_BMP         = MUSIC_COMMON_NIG_COM_BOT_ICON_BG_A_P_S_BMP; 
        MUSIC_THEME_COM_BOT_ICON_BG_B_N_BMP           = MUSIC_COMMON_NIG_COM_BOT_ICON_BG_B_N_BMP;   
        MUSIC_THEME_COM_BOT_ICON_BG_B_P_S_BMP         = MUSIC_COMMON_NIG_COM_BOT_ICON_BG_B_P_S_BMP; 
        MUSIC_THEME_COM_BOT_MEDIA_PAUSE_BMP           = MUSIC_COMMON_NIG_COM_BOT_MEDIA_PAUSE_BMP;   
        MUSIC_THEME_COM_BOT_MEDIA_PAUSE_D_BMP         = MUSIC_COMMON_NIG_COM_BOT_MEDIA_PAUSE_D_BMP;
        MUSIC_THEME_COM_BOT_MEDIA_PLAY_BMP            = MUSIC_COMMON_NIG_COM_BOT_MEDIA_PLAY_BMP;    
        MUSIC_THEME_COM_BOT_MEDIA_PLAY_D_BMP          = MUSIC_COMMON_NIG_COM_BOT_MEDIA_PLAY_D_BMP;
        MUSIC_THEME_COM_BOT_MEDIA_DOWN_N_BMP          = MUSIC_COMMON_NIG_COM_BOT_MEDIA_DOWN_N_BMP;  
        MUSIC_THEME_COM_BOT_MEDIA_DOWN_P_S_BMP        = MUSIC_COMMON_NIG_COM_BOT_MEDIA_DOWN_P_S_BMP;
        MUSIC_THEME_COM_BOT_MEDIA_UP_N_BMP            = MUSIC_COMMON_NIG_COM_BOT_MEDIA_UP_N_BMP;    
        MUSIC_THEME_COM_BOT_MEDIA_UP_P_S_BMP          = MUSIC_COMMON_NIG_COM_BOT_MEDIA_UP_P_S_BMP;

        MUSIC_THEME_COM_BOT_ICON_BG_A_L_N_BMP         = MUSIC_COMMON_NIG_COM_BOT_ICON_BG_A_L_N_BMP;
        MUSIC_THEME_COM_BOT_ICON_BG_A_L_S_BMP         = MUSIC_COMMON_NIG_COM_BOT_ICON_BG_A_L_S_BMP;
        
        MUSIC_THEME_COM_BOT_LIST_N_BMP                = MUSIC_COMMON_NIG_COM_BOT_LIST_N_BMP;
        MUSIC_THEME_COM_BOT_LIST_D_BMP                = MUSIC_COMMON_NIG_COM_BOT_LIST_D_BMP;
        
        MUSIC_THEME_COM_BOT_BROWSE_N_BMP              = MUSIC_COMMON_NIG_COM_BOT_BROWSE_N_BMP;
        MUSIC_THEME_COM_BOT_BROWSE_D_BMP              = MUSIC_COMMON_NIG_COM_BOT_BROWSE_D_BMP;
        
        MUSIC_THEME_COM_BOT_DIRECT_N_BMP              = MUSIC_COMMON_NIG_COM_BOT_DIRECT_N_BMP;
        MUSIC_THEME_COM_BOT_DIRECT_D_BMP              = MUSIC_COMMON_NIG_COM_BOT_DIRECT_D_BMP;
        
        MUSIC_THEME_COM_BOT_PHONE_A_N_BMP             = MUSIC_COMMON_NIG_COM_BOT_PHONE_A_N_BMP;
        MUSIC_THEME_COM_BOT_PHONE_B_N_BMP             = MUSIC_COMMON_NIG_COM_BOT_PHONE_B_N_BMP;
        
        MUSIC_THEME_COM_BOT_BT_CONNECT_N_BMP          = MUSIC_COMMON_NIG_COM_BOT_BT_CONNECT_N_BMP;
        
        MUSIC_THEME_COM_BOT_RANDOM_A_N_BMP            = MUSIC_COMMON_NIG_COM_BOT_RANDOM_A_N_BMP;
        MUSIC_THEME_COM_BOT_RANDOM_A_D_BMP            = MUSIC_COMMON_NIG_COM_BOT_RANDOM_A_D_BMP;
        MUSIC_THEME_COM_BOT_RANDOM_B_N_BMP            = MUSIC_COMMON_NIG_COM_BOT_RANDOM_B_N_BMP;
        MUSIC_THEME_COM_BOT_RANDOM_B_D_BMP            = MUSIC_COMMON_NIG_COM_BOT_RANDOM_B_D_BMP;
        
        MUSIC_THEME_COM_BOT_REPEAT_A_N_BMP            = MUSIC_COMMON_NIG_COM_BOT_REPEAT_A_N_BMP;
        MUSIC_THEME_COM_BOT_REPEAT_A_D_BMP            = MUSIC_COMMON_NIG_COM_BOT_REPEAT_A_D_BMP;
        MUSIC_THEME_COM_BOT_REPEAT_B_N_BMP            = MUSIC_COMMON_NIG_COM_BOT_REPEAT_B_N_BMP;
        MUSIC_THEME_COM_BOT_REPEAT_B_D_BMP            = MUSIC_COMMON_NIG_COM_BOT_REPEAT_B_D_BMP;
        MUSIC_THEME_COM_BOT_REPEAT_C_N_BMP            = MUSIC_COMMON_NIG_COM_BOT_REPEAT_C_N_BMP;
        MUSIC_THEME_COM_BOT_REPEAT_C_D_BMP            = MUSIC_COMMON_NIG_COM_BOT_REPEAT_C_D_BMP;

        MUSIC_THEME_COM_BRO_ALBUM_BMP                   = MUSIC_COMMON_NIG_COM_BRO_ALBUM_BMP;
        MUSIC_THEME_COM_BRO_ART_BMP                     = MUSIC_COMMON_NIG_COM_BRO_ART_BMP;
        MUSIC_THEME_COM_BRO_FOLDER_BMP                  = MUSIC_COMMON_NIG_COM_BRO_FOLDER_BMP;
        MUSIC_THEME_COM_BRO_GENRES_BMP                  = MUSIC_COMMON_NIG_COM_BRO_GENRES_BMP;
        MUSIC_THEME_COM_BRO_SONG_BMP                    = MUSIC_COMMON_NIG_COM_BRO_SONG_BMP;

        MUSIC_THEME_TEMP_COM_LIST_OFF_BMP                = MUSIC_TEMP_NIGHT_NIG_COM_LIST_OFF_BMP;          
        MUSIC_THEME_TEMP_MEDIA_DUAL_PHONE_A_BMP          = MUSIC_TEMP_NIGHT_NIG_MEDIA_DUAL_PHONE_A_BMP;    
        MUSIC_THEME_TEMP_COM_RIG_BG_A_BMP                = MUSIC_TEMP_NIGHT_NIG_COM_RIG_BG_A_BMP;          
        MUSIC_THEME_TEMP_COM_RIG_ARROW_A_BMP             = MUSIC_TEMP_NIGHT_NIG_COM_RIG_ARROW_A_BMP;       
        MUSIC_THEME_TEMP_MEDIA_SETUP_REPEAT_A_BMP        = MUSIC_TEMP_NIGHT_NIG_MEDIA_SETUP_REPEAT_A_BMP;  
        MUSIC_THEME_TEMP_COM_RIG_AUDIO_ICON_S_BMP        = MUSIC_TEMP_NIGHT_NIG_COM_RIG_AUDIO_ICON_S_BMP;  
        MUSIC_THEME_TEMP_COM_RIG_AUDIO_ICON_N_BMP        = MUSIC_TEMP_NIGHT_NIG_COM_RIG_AUDIO_ICON_N_BMP;  
        MUSIC_THEME_TEMP_MEDIA_DUAL_PHONE_B_BMP          = MUSIC_TEMP_NIGHT_NIG_MEDIA_DUAL_PHONE_B_BMP;    
        MUSIC_THEME_TEMP_COM_TOP_BACK_ICON_BMP           = MUSIC_TEMP_NIGHT_NIG_COM_TOP_BACK_ICON_BMP;     
        MUSIC_THEME_TEMP_COM_MEDIA_ALBUM_BG_BMP          = MUSIC_TEMP_NIGHT_NIG_COM_MEDIA_ALBUM_BG_BMP;    
        MUSIC_THEME_TEMP_MEDIA_SETUP_RANDOM_A_BMP        = MUSIC_TEMP_NIGHT_NIG_MEDIA_SETUP_RANDOM_A_BMP;  
        MUSIC_THEME_TEMP_COM_RIG_FUNC_ICON_S_BMP         = MUSIC_TEMP_NIGHT_NIG_COM_RIG_FUNC_ICON_S_BMP;   
        MUSIC_THEME_TEMP_COM_RIG_ARROW_B_BMP             = MUSIC_TEMP_NIGHT_NIG_COM_RIG_ARROW_B_BMP;       
        MUSIC_THEME_TEMP_COM_LOW_VOLTAGE_BMP             = MUSIC_TEMP_NIGHT_NIG_COM_LOW_VOLTAGE_BMP;       
        MUSIC_THEME_TEMP_COM_RIG_BG_C_BMP                = MUSIC_TEMP_NIGHT_NIG_COM_RIG_BG_C_BMP;          
        MUSIC_THEME_TEMP_COM_TOP_ICON_BG_N_BMP           = MUSIC_TEMP_NIGHT_NIG_COM_TOP_ICON_BG_N_BMP;     
        MUSIC_THEME_TEMP_COM_LIST_ON_BMP                 = MUSIC_TEMP_NIGHT_NIG_COM_LIST_ON_BMP;           
        MUSIC_THEME_TEMP_COM_LIST_BG_BMP                 = MUSIC_TEMP_NIGHT_NIG_COM_LIST_BG_BMP;           
        MUSIC_THEME_TEMP_COM_RIG_BG_B_BMP                = MUSIC_TEMP_NIGHT_NIG_COM_RIG_BG_B_BMP;          
        MUSIC_THEME_TEMP_COM_RIG_ICON_BG_N_BMP           = MUSIC_TEMP_NIGHT_NIG_COM_RIG_ICON_BG_N_BMP;     
        MUSIC_THEME_TEMP_MEDIA_SETUP_RANDOM_B_BMP        = MUSIC_TEMP_NIGHT_NIG_MEDIA_SETUP_RANDOM_B_BMP;  
        MUSIC_THEME_TEMP_COM_RIG_ZONE_ICON_BMP           = MUSIC_TEMP_NIGHT_NIG_COM_RIG_ZONE_ICON_BMP;     
        MUSIC_THEME_TEMP_COM_RIG_ICON_BG_P_BMP           = MUSIC_TEMP_NIGHT_NIG_COM_RIG_ICON_BG_P_BMP;     
        MUSIC_THEME_TEMP_COM_BG_BMP                      = MUSIC_TEMP_NIGHT_NIG_COM_BG_BMP;                
        MUSIC_THEME_TEMP_COM_PD_CHARGE_BMP               = MUSIC_TEMP_NIGHT_NIG_COM_PD_CHARGE_BMP;         
        MUSIC_THEME_TEMP_MEDIA_SETUP_REPEAT_C_BMP        = MUSIC_TEMP_NIGHT_NIG_MEDIA_SETUP_REPEAT_C_BMP;  
        MUSIC_THEME_TEMP_COM_PUNCH_EQ_BMP                = MUSIC_TEMP_NIGHT_NIG_COM_PUNCH_EQ_BMP;          
        MUSIC_THEME_TEMP_COM_TOP_SOURCE_ICON_BMP         = MUSIC_TEMP_NIGHT_NIG_COM_TOP_SOURCE_ICON_BMP;   
        MUSIC_THEME_TEMP_COM_RIG_ARROW_C_BMP             = MUSIC_TEMP_NIGHT_NIG_COM_RIG_ARROW_C_BMP;       
        MUSIC_THEME_TEMP_COM_TOP_BG_BMP                  = MUSIC_TEMP_NIGHT_NIG_COM_TOP_BG_BMP;            
        MUSIC_THEME_TEMP_COM_RIG_ZONE_ICON_S_BMP         = MUSIC_TEMP_NIGHT_NIG_COM_RIG_ZONE_ICON_S_BMP;   
        MUSIC_THEME_TEMP_COM_RIG_ZONE_ICON_N_BMP         = MUSIC_TEMP_NIGHT_NIG_COM_RIG_ZONE_ICON_N_BMP;   
        MUSIC_THEME_TEMP_COM_RIG_AUDIO_ICON_BMP          = MUSIC_TEMP_NIGHT_NIG_COM_RIG_AUDIO_ICON_BMP;    
        MUSIC_THEME_TEMP_COM_TOP_LIST_ICON_BMP           = MUSIC_TEMP_NIGHT_NIG_COM_TOP_LIST_ICON_BMP;     
        MUSIC_THEME_TEMP_MEDIA_DUAL_PHONE_D_BMP          = MUSIC_TEMP_NIGHT_NIG_MEDIA_DUAL_PHONE_D_BMP;    
        MUSIC_THEME_TEMP_COM_RIG_FUNC_ICON_N_BMP         = MUSIC_TEMP_NIGHT_NIG_COM_RIG_FUNC_ICON_N_BMP;   
        MUSIC_THEME_TEMP_COM_TOP_ICON_BG_P_S_BMP         = MUSIC_TEMP_NIGHT_NIG_COM_TOP_ICON_BG_P_S_BMP;   
        MUSIC_THEME_TEMP_COM_RIG_FUNC_ICON_BMP           = MUSIC_TEMP_NIGHT_NIG_COM_RIG_FUNC_ICON_BMP;  
    }
    else if(theme == THEME_DAY)
    {
        MUSIC_THEME_TEMP_MEDIA_SETUP_REPEAT_B_BMP        = MUSIC_TEMP_DAY_DAY_MEDIA_SETUP_REPEAT_B_BMP;  
        MUSIC_THEME_USB_ICON_BMP                         = MUSIC_DAY_USB_ICON_BMP;
        MUSIC_THEME_BT_ICON_BMP                          = MUSIC_DAY_BT_ICON_BMP;

        MUSIC_THEME_COM_BOT_BG_BMP                    = MUSIC_COMMON_DAY_COM_BOT_BG_BMP;  
        MUSIC_THEME_COM_BOT_ICON_BG_A_N_BMP           = MUSIC_COMMON_DAY_COM_BOT_ICON_BG_A_N_BMP;  
        MUSIC_THEME_COM_BOT_ICON_BG_A_P_S_BMP         = MUSIC_COMMON_DAY_COM_BOT_ICON_BG_A_P_S_BMP; 
        MUSIC_THEME_COM_BOT_ICON_BG_B_N_BMP           = MUSIC_COMMON_DAY_COM_BOT_ICON_BG_B_N_BMP;   
        MUSIC_THEME_COM_BOT_ICON_BG_B_P_S_BMP         = MUSIC_COMMON_DAY_COM_BOT_ICON_BG_B_P_S_BMP; 
        MUSIC_THEME_COM_BOT_MEDIA_PAUSE_BMP           = MUSIC_COMMON_DAY_COM_BOT_MEDIA_PAUSE_BMP;   
        MUSIC_THEME_COM_BOT_MEDIA_PAUSE_D_BMP         = MUSIC_COMMON_DAY_COM_BOT_MEDIA_PAUSE_D_BMP;
        MUSIC_THEME_COM_BOT_MEDIA_PLAY_BMP            = MUSIC_COMMON_DAY_COM_BOT_MEDIA_PLAY_BMP;    
        MUSIC_THEME_COM_BOT_MEDIA_PLAY_D_BMP          = MUSIC_COMMON_DAY_COM_BOT_MEDIA_PLAY_D_BMP;
        MUSIC_THEME_COM_BOT_MEDIA_DOWN_N_BMP          = MUSIC_COMMON_DAY_COM_BOT_MEDIA_DOWN_N_BMP;  
        MUSIC_THEME_COM_BOT_MEDIA_DOWN_P_S_BMP        = MUSIC_COMMON_DAY_COM_BOT_MEDIA_DOWN_P_S_BMP;
        MUSIC_THEME_COM_BOT_MEDIA_UP_N_BMP            = MUSIC_COMMON_DAY_COM_BOT_MEDIA_UP_N_BMP;    
        MUSIC_THEME_COM_BOT_MEDIA_UP_P_S_BMP          = MUSIC_COMMON_DAY_COM_BOT_MEDIA_UP_P_S_BMP;

        MUSIC_THEME_COM_BOT_ICON_BG_A_L_N_BMP         = MUSIC_COMMON_DAY_COM_BOT_ICON_BG_A_L_N_BMP;
        MUSIC_THEME_COM_BOT_ICON_BG_A_L_S_BMP         = MUSIC_COMMON_DAY_COM_BOT_ICON_BG_A_L_S_BMP;
        
        MUSIC_THEME_COM_BOT_LIST_N_BMP                = MUSIC_COMMON_DAY_COM_BOT_LIST_N_BMP;
        MUSIC_THEME_COM_BOT_LIST_D_BMP                = MUSIC_COMMON_DAY_COM_BOT_LIST_D_BMP;
        
        MUSIC_THEME_COM_BOT_BROWSE_N_BMP              = MUSIC_COMMON_DAY_COM_BOT_BROWSE_N_BMP;
        MUSIC_THEME_COM_BOT_BROWSE_D_BMP              = MUSIC_COMMON_DAY_COM_BOT_BROWSE_D_BMP;
        
        MUSIC_THEME_COM_BOT_DIRECT_N_BMP              = MUSIC_COMMON_DAY_COM_BOT_DIRECT_N_BMP;
        MUSIC_THEME_COM_BOT_DIRECT_D_BMP              = MUSIC_COMMON_DAY_COM_BOT_DIRECT_D_BMP;
        
        MUSIC_THEME_COM_BOT_PHONE_A_N_BMP             = MUSIC_COMMON_DAY_COM_BOT_PHONE_A_N_BMP;
        MUSIC_THEME_COM_BOT_PHONE_B_N_BMP             = MUSIC_COMMON_DAY_COM_BOT_PHONE_B_N_BMP;
        
        MUSIC_THEME_COM_BOT_BT_CONNECT_N_BMP          = MUSIC_COMMON_DAY_COM_BOT_BT_CONNECT_N_BMP;
        
        MUSIC_THEME_COM_BOT_RANDOM_A_N_BMP            = MUSIC_COMMON_DAY_COM_BOT_RANDOM_A_N_BMP;
        MUSIC_THEME_COM_BOT_RANDOM_A_D_BMP            = MUSIC_COMMON_DAY_COM_BOT_RANDOM_A_D_BMP;
        MUSIC_THEME_COM_BOT_RANDOM_B_N_BMP            = MUSIC_COMMON_DAY_COM_BOT_RANDOM_B_N_BMP;
        MUSIC_THEME_COM_BOT_RANDOM_B_D_BMP            = MUSIC_COMMON_DAY_COM_BOT_RANDOM_B_D_BMP;
        
        MUSIC_THEME_COM_BOT_REPEAT_A_N_BMP            = MUSIC_COMMON_DAY_COM_BOT_REPEAT_A_N_BMP;
        MUSIC_THEME_COM_BOT_REPEAT_A_D_BMP            = MUSIC_COMMON_DAY_COM_BOT_REPEAT_A_D_BMP;
        MUSIC_THEME_COM_BOT_REPEAT_B_N_BMP            = MUSIC_COMMON_DAY_COM_BOT_REPEAT_B_N_BMP;
        MUSIC_THEME_COM_BOT_REPEAT_B_D_BMP            = MUSIC_COMMON_DAY_COM_BOT_REPEAT_B_D_BMP;
        MUSIC_THEME_COM_BOT_REPEAT_C_N_BMP            = MUSIC_COMMON_DAY_COM_BOT_REPEAT_C_N_BMP;
        MUSIC_THEME_COM_BOT_REPEAT_C_D_BMP            = MUSIC_COMMON_DAY_COM_BOT_REPEAT_C_D_BMP;

        MUSIC_THEME_COM_BRO_ALBUM_BMP                   = MUSIC_COMMON_DAY_COM_BRO_ALBUM_BMP;
        MUSIC_THEME_COM_BRO_ART_BMP                     = MUSIC_COMMON_DAY_COM_BRO_ART_BMP;
        MUSIC_THEME_COM_BRO_FOLDER_BMP                  = MUSIC_COMMON_DAY_COM_BRO_FOLDER_BMP;
        MUSIC_THEME_COM_BRO_GENRES_BMP                  = MUSIC_COMMON_DAY_COM_BRO_GENRES_BMP;
        MUSIC_THEME_COM_BRO_SONG_BMP                    = MUSIC_COMMON_DAY_COM_BRO_SONG_BMP;

        MUSIC_THEME_TEMP_COM_LIST_OFF_BMP                = MUSIC_TEMP_DAY_DAY_COM_LIST_OFF_BMP;          
        MUSIC_THEME_TEMP_MEDIA_DUAL_PHONE_A_BMP          = MUSIC_TEMP_DAY_DAY_MEDIA_DUAL_PHONE_A_BMP;    
        MUSIC_THEME_TEMP_COM_RIG_BG_A_BMP                = MUSIC_TEMP_DAY_DAY_COM_RIG_BG_A_BMP;          
        MUSIC_THEME_TEMP_COM_RIG_ARROW_A_BMP             = MUSIC_TEMP_DAY_DAY_COM_RIG_ARROW_A_BMP;       
        MUSIC_THEME_TEMP_MEDIA_SETUP_REPEAT_A_BMP        = MUSIC_TEMP_DAY_DAY_MEDIA_SETUP_REPEAT_A_BMP;  
        MUSIC_THEME_TEMP_COM_RIG_AUDIO_ICON_S_BMP        = MUSIC_TEMP_DAY_DAY_COM_RIG_AUDIO_ICON_S_BMP;  
        MUSIC_THEME_TEMP_COM_RIG_AUDIO_ICON_N_BMP        = MUSIC_TEMP_DAY_DAY_COM_RIG_AUDIO_ICON_N_BMP;  
        MUSIC_THEME_TEMP_MEDIA_DUAL_PHONE_B_BMP          = MUSIC_TEMP_DAY_DAY_MEDIA_DUAL_PHONE_B_BMP;    
        MUSIC_THEME_TEMP_COM_TOP_BACK_ICON_BMP           = MUSIC_TEMP_DAY_DAY_COM_TOP_BACK_ICON_BMP;     
        MUSIC_THEME_TEMP_COM_MEDIA_ALBUM_BG_BMP          = MUSIC_TEMP_DAY_DAY_COM_MEDIA_ALBUM_BG_BMP;    
        MUSIC_THEME_TEMP_MEDIA_SETUP_RANDOM_A_BMP        = MUSIC_TEMP_DAY_DAY_MEDIA_SETUP_RANDOM_A_BMP;  
        MUSIC_THEME_TEMP_COM_RIG_FUNC_ICON_S_BMP         = MUSIC_TEMP_DAY_DAY_COM_RIG_FUNC_ICON_S_BMP;   
        MUSIC_THEME_TEMP_COM_RIG_ARROW_B_BMP             = MUSIC_TEMP_DAY_DAY_COM_RIG_ARROW_B_BMP;       
        MUSIC_THEME_TEMP_COM_LOW_VOLTAGE_BMP             = MUSIC_TEMP_DAY_DAY_COM_LOW_VOLTAGE_BMP;       
        MUSIC_THEME_TEMP_COM_RIG_BG_C_BMP                = MUSIC_TEMP_DAY_DAY_COM_RIG_BG_C_BMP;          
        MUSIC_THEME_TEMP_COM_TOP_ICON_BG_N_BMP           = MUSIC_TEMP_DAY_DAY_COM_TOP_ICON_BG_N_BMP;     
        MUSIC_THEME_TEMP_COM_LIST_ON_BMP                 = MUSIC_TEMP_DAY_DAY_COM_LIST_ON_BMP;           
        MUSIC_THEME_TEMP_COM_LIST_BG_BMP                 = MUSIC_TEMP_DAY_DAY_COM_LIST_BG_BMP;           
        MUSIC_THEME_TEMP_COM_RIG_BG_B_BMP                = MUSIC_TEMP_DAY_DAY_COM_RIG_BG_B_BMP;          
        MUSIC_THEME_TEMP_COM_RIG_ICON_BG_N_BMP           = MUSIC_TEMP_DAY_DAY_COM_RIG_ICON_BG_N_BMP;     
        MUSIC_THEME_TEMP_MEDIA_SETUP_RANDOM_B_BMP        = MUSIC_TEMP_DAY_DAY_MEDIA_SETUP_RANDOM_B_BMP;  
        MUSIC_THEME_TEMP_COM_RIG_ZONE_ICON_BMP           = MUSIC_TEMP_DAY_DAY_COM_RIG_ZONE_ICON_BMP;     
        MUSIC_THEME_TEMP_COM_RIG_ICON_BG_P_BMP           = MUSIC_TEMP_DAY_DAY_COM_RIG_ICON_BG_P_BMP;     
        MUSIC_THEME_TEMP_COM_BG_BMP                      = MUSIC_TEMP_DAY_DAY_COM_BG_BMP;                
        MUSIC_THEME_TEMP_COM_PD_CHARGE_BMP               = MUSIC_TEMP_DAY_DAY_COM_PD_CHARGE_BMP;         
        MUSIC_THEME_TEMP_MEDIA_SETUP_REPEAT_C_BMP        = MUSIC_TEMP_DAY_DAY_MEDIA_SETUP_REPEAT_C_BMP;  
        MUSIC_THEME_TEMP_COM_PUNCH_EQ_BMP                = MUSIC_TEMP_DAY_DAY_COM_PUNCH_EQ_BMP;          
        MUSIC_THEME_TEMP_COM_TOP_SOURCE_ICON_BMP         = MUSIC_TEMP_DAY_DAY_COM_TOP_SOURCE_ICON_BMP;   
        MUSIC_THEME_TEMP_COM_RIG_ARROW_C_BMP             = MUSIC_TEMP_DAY_DAY_COM_RIG_ARROW_C_BMP;       
        MUSIC_THEME_TEMP_COM_TOP_BG_BMP                  = MUSIC_TEMP_DAY_DAY_COM_TOP_BG_BMP;            
        MUSIC_THEME_TEMP_COM_RIG_ZONE_ICON_S_BMP         = MUSIC_TEMP_DAY_DAY_COM_RIG_ZONE_ICON_S_BMP;   
        MUSIC_THEME_TEMP_COM_RIG_ZONE_ICON_N_BMP         = MUSIC_TEMP_DAY_DAY_COM_RIG_ZONE_ICON_N_BMP;   
        MUSIC_THEME_TEMP_COM_RIG_AUDIO_ICON_BMP          = MUSIC_TEMP_DAY_DAY_COM_RIG_AUDIO_ICON_BMP;    
        MUSIC_THEME_TEMP_COM_TOP_LIST_ICON_BMP           = MUSIC_TEMP_DAY_DAY_COM_TOP_LIST_ICON_BMP;     
        MUSIC_THEME_TEMP_MEDIA_DUAL_PHONE_D_BMP          = MUSIC_TEMP_DAY_DAY_MEDIA_DUAL_PHONE_D_BMP;    
        MUSIC_THEME_TEMP_COM_RIG_FUNC_ICON_N_BMP         = MUSIC_TEMP_DAY_DAY_COM_RIG_FUNC_ICON_N_BMP;   
        MUSIC_THEME_TEMP_COM_TOP_ICON_BG_P_S_BMP         = MUSIC_TEMP_DAY_DAY_COM_TOP_ICON_BG_P_S_BMP;   
        MUSIC_THEME_TEMP_COM_RIG_FUNC_ICON_BMP           = MUSIC_TEMP_DAY_DAY_COM_RIG_FUNC_ICON_BMP;   
    }
    
    if(music_para->theme > THEME_NONE)
    {
        //lv_obj_set_style_bg_image_src(ui_music, ui_music_get_res(music_para, MUSIC_THEME_TEMP_COM_BG_BMP), LV_PART_MAIN | LV_STATE_DEFAULT);
        traverse_children_with_callback(music_para, ui_music, music_para->theme_bmp_start, music_para->theme_bmp_end, MUSIC_THEME_TEMP_MEDIA_SETUP_REPEAT_B_BMP, MUSIC_THEME_TEMP_COM_RIG_FUNC_ICON_BMP, music_para->ui_music_res_para.lv_music_icon, ui_music_get_res, NULL, 0);
        ui_source_theme_switch(music_para->source_obj, theme);
    }

    music_para->theme = theme;
    music_para->theme_bmp_start = MUSIC_THEME_TEMP_MEDIA_SETUP_REPEAT_B_BMP;
    music_para->theme_bmp_end = MUSIC_THEME_TEMP_COM_RIG_FUNC_ICON_BMP;
}

static __a_audio_fonttype_e get_audio_enconding(__u8 src_en)
{
	if(src_en == 2)
		return A_AUDIO_FONTTYPE_UTF_16LE;
	else if(src_en == 3)
		return A_AUDIO_FONTTYPE_UTF_8;
	else
		return A_AUDIO_FONTTYPE_ISOIEC8859_1;
}

static __bool is_all_blank( const char *str, __u32 len )
{
	__u32   i;

	for( i = 0;  i < len;  i++ )
	{
		if( str[i] != ' ' )
			return EPDK_FALSE;
	}

	return EPDK_TRUE;
}

static __epdk_charset_enm_e   charset_convert( const char *src, __u32 src_size, __s32 src_encoding, char *dst, __u32 dst_size, __u32 *len_p )
{
	__u32  len;
#ifdef ID3_CHARSET_ISO88591_CONVERT_BY_SMENU_LANGUAGE //mllee 121019
	reg_system_para_t* para;
	para = (reg_system_para_t*)dsk_reg_get_para_by_app(REG_APP_SYSTEM);
#endif
	if( src == NULL || src_size == 0 || dst == NULL || dst_size == 0 )
	{
		*len_p = 0;
		return EPDK_CHARSET_ENM_UNKNOWN;
	}

	len = min( src_size, dst_size - 1 );
	switch( src_encoding )
	{
		case A_AUDIO_FONTTYPE_ISOIEC8859_1 :
#ifdef ID3_CHARSET_ISO88591_CONVERT_GBK2UTF8 //mllee 120805
			len = eLIBs_charset_convert( EPDK_CHARSET_ENM_GBK, EPDK_CHARSET_ENM_UTF8,
			                             (__u8 *)src, src_size, (__u8 *)dst, dst_size - 1 );
			dst[len] = '\0';
			*len_p = len;
			return EPDK_CHARSET_ENM_UTF8;
#elif defined ID3_CHARSET_ISO88591_CONVERT_BY_SMENU_LANGUAGE //mllee 121019
			if(para->language == EPDK_LANGUAGE_ENM_PORTUGUESE)
			{
				len = eLIBs_charset_convert( EPDK_CHARSET_ENM_ISO_8859_1, EPDK_CHARSET_ENM_UTF8,
			                             (__u8 *)src, src_size, (__u8 *)dst, dst_size - 1 );
			}
			else if(para->language == EPDK_LANGUAGE_ENM_RUSSIAN)
			{
				len = eLIBs_charset_convert( EPDK_CHARSET_ENM_CP1251, EPDK_CHARSET_ENM_UTF8,
			                             (__u8 *)src, src_size, (__u8 *)dst, dst_size - 1 );
			}
			else
			{
				len = eLIBs_charset_convert( EPDK_CHARSET_ENM_GBK, EPDK_CHARSET_ENM_UTF8,
			                             (__u8 *)src, src_size, (__u8 *)dst, dst_size - 1 );
			}
			dst[len] = '\0';
			*len_p = len;
			return EPDK_CHARSET_ENM_UTF8;
#else
			g_strncpy( (char *)dst, (char *)src, len );
			dst[len] = '\0';
			*len_p = len;
			return EPDK_CHARSET_ENM_UNKNOWN;			
#endif
#if 1
		case A_AUDIO_FONTTYPE_UTF_16LE:
			if(*src == 0xFF && *(src+1) == 0xFE) //LE
			{
				len = eLIBs_charset_convert( EPDK_CHARSET_ENM_UTF16LE, EPDK_CHARSET_ENM_UTF8,
			                             (__u8 *)src+2, src_size-2, (__u8 *)dst, dst_size - 1 );
			}
			else
			{	
				len = eLIBs_charset_convert( EPDK_CHARSET_ENM_UTF16BE, EPDK_CHARSET_ENM_UTF8,
			                             (__u8 *)src+2, src_size-2, (__u8 *)dst, dst_size - 1 );
			}
			dst[len] = '\0';
			*len_p = len;
			return EPDK_CHARSET_ENM_UTF8;
#else
		case A_AUDIO_FONTTYPE_UTF_16LE     :
			len = eLIBs_charset_convert( EPDK_CHARSET_ENM_UTF16LE, EPDK_CHARSET_ENM_UTF8,
			                             (__u8 *)src, src_size, (__u8 *)dst, dst_size - 1 );
			dst[len] = '\0';
			*len_p = len;
			return EPDK_CHARSET_ENM_UTF8;
		case A_AUDIO_FONTTYPE_UTF_16BE     :
			len = eLIBs_charset_convert( EPDK_CHARSET_ENM_UTF16BE, EPDK_CHARSET_ENM_UTF8,
			                             (__u8 *)src, src_size, (__u8 *)dst, dst_size - 1 );
			dst[len] = '\0';
			*len_p = len;
			return EPDK_CHARSET_ENM_UTF8;
#endif
		case A_AUDIO_FONTTYPE_UTF_8        :
			g_strncpy( (char *)dst, (char *)src, len );
			dst[len] = '\0';
			*len_p = len;
			return EPDK_CHARSET_ENM_UTF8;
		default  :
			g_strncpy( (char *)dst, (char *)src, len );
			dst[len] = '\0';
			*len_p = len;
			return EPDK_CHARSET_ENM_UNKNOWN;
	}
}

static __s32  get_audio_info_album_from_reg(ui_music_para_t * music_para)
{
	__u32               len;
	__s32 		src_encond;

	if(music_para == NULL)
		return EPDK_FAIL;

    //album
	src_encond = get_audio_enconding(music_para->ui_music_data_para.Album[0]);
	music_para->play_file_info.album_charset = charset_convert( &music_para->ui_music_data_para.Album[1], 67,
		                                     src_encond, music_para->play_file_info.album, 128, &len );

    __music_msg("music_para->ui_music_data_para.album[0]=%d, &music_para->ui_music_data_para.album[1]=%s,  len=%d\n", music_para->ui_music_data_para.Album[0], &music_para->ui_music_data_para.Album[1], len);
	if( is_all_blank(music_para->play_file_info.album, len ) )
		music_para->play_file_info.album[0] = '\0';
	
	return EPDK_OK;
}

static __s32  get_audio_info_title_from_reg(ui_music_para_t * music_para)
{
	__u32               len;
	__s32 		src_encond;

	if(music_para == NULL)
		return EPDK_FAIL;

	//title
	src_encond = get_audio_enconding(music_para->ui_music_data_para.Title[0]);
	music_para->play_file_info.title_charset = charset_convert( &music_para->ui_music_data_para.Title[1], 67,
		                                     src_encond, music_para->play_file_info.filename, 128, &len );

    __music_msg("music_para->ui_music_data_para.Title[0]=%d, &music_para->ui_music_data_para.Title[1]=%s,  len=%d\n", music_para->ui_music_data_para.Title[0], &music_para->ui_music_data_para.Title[1], len);
	if( is_all_blank(music_para->play_file_info.filename, len ) )
		music_para->play_file_info.filename[0] = '\0';
	
	return EPDK_OK;
}

static __s32  get_audio_info_artist_from_reg(ui_music_para_t * music_para)
{
	__u32               len;
	__s32 		src_encond;

	if(music_para == NULL)
		return EPDK_FAIL;

	//artist
	src_encond = get_audio_enconding(music_para->ui_music_data_para.Artist[0]);
	music_para->play_file_info.title_charset = charset_convert( &music_para->ui_music_data_para.Artist[1], 67,
		                                     src_encond, music_para->play_file_info.artist, 128, &len );

    __music_msg("music_para->ui_music_data_para.Artist[0]=%d, &music_para->ui_music_data_para.Artist[1]=%s,  len=%d\n", music_para->ui_music_data_para.Artist[0], &music_para->ui_music_data_para.Artist[1], len);
	if( is_all_blank(music_para->play_file_info.artist, len ) )
		music_para->play_file_info.artist[0] = '\0';
	
	return EPDK_OK;
}

static __s32  get_audio_info_from_reg(ui_music_para_t * music_para)
{
	//file name
    //get_audio_info_file_from_reg();
		
	//album
    get_audio_info_album_from_reg(music_para);
    
	//title
    get_audio_info_title_from_reg(music_para);
    
	//artist
    get_audio_info_artist_from_reg(music_para);
	
	return EPDK_OK;
}

static __s32 music_get_audio_file_info_external(ui_music_para_t * music_para)
{
	__s32 ret; 
	get_audio_info_from_reg(music_para);
	return EPDK_OK;
}

static void ui_music_source_title(ui_music_para_t * music_para, __u8 * str, __u8 source_flag)
{
	char title_str[128] = {0};
    reg_aux_para_t* last_aux_para;

    last_aux_para = (reg_aux_para_t*)dsk_reg_get_para_by_app(REG_APP_AUX);

#if 1
    if(str)
    {
        eLIBs_strcpy(title_str, str);    
    }
    else if(music_para->work_source == ID_WORK_SOURCE_BT || (music_para->party_work_source == PARTY_D_SOURCE_BT_AUDIO))
    {
        eLIBs_strcpy(title_str, "Bluetooth");    
    }
    else if(music_para->work_source == ID_WORK_SOURCE_USB && music_para->usb_device == USB_DEVICE_IPOD)
    {
        eLIBs_strcpy(title_str, "IPHONE");
    }
    else
    {
        eLIBs_strcpy(title_str, "USB");
    }
#else
    if(music_para->source_para->party_info.role == LEA_ROLE_BROADCAST && music_para->source_para->party_info.bc_info.source == music_para->work_source)
    {
        eLIBs_strcpy(title_str, "Broadcast");
    }
    else if(music_para->work_source == ID_WORK_SOURCE_BT)
    {
        eLIBs_strcpy(title_str, "Bluetooth");    
        
    }
    else if(music_para->work_source == ID_WORK_SOURCE_USB && music_para->usb_device == USB_DEVICE_IPOD)
    {
        eLIBs_strcpy(title_str, "IPHONE");
    }
    else
    {
        eLIBs_strcpy(title_str, "USB");
    }

    if(str)
    {
        eLIBs_strcat(title_str, ":");
        eLIBs_strcat(title_str, str);
    }
#endif

    ui_source_title(music_para->source_para, title_str, source_flag);
}

static void ui_music_change_mode(ui_music_para_t * music_para)
{
	root_app_para_t * root_para = app_root_get_para();
    reg_root_para_t* last_root_para;
    reg_aux_para_t* last_aux_para;
	reg_music_para_t *music_last_para = NULL;	

	music_last_para = dsk_reg_get_para_by_app(REG_APP_MUSIC);	
    last_aux_para = (reg_aux_para_t*)dsk_reg_get_para_by_app(REG_APP_AUX);
    last_root_para = (reg_root_para_t*)dsk_reg_get_para_by_app(REG_APP_ROOT);

    __music_msg("music_para->work_source = 0x%x, music_para->source_para->party_info.bc_info.source=%d\n", music_para->work_source, music_para->source_para->party_info.bc_info.source);

    ui_music_source_title(music_para, NULL, 1);
    
    if(((music_para->party_work_source == PARTY_D_SOURCE_USB) || (music_para->party_work_source == PARTY_D_SOURCE_BT_AUDIO)))
    {
        ui_source_rig_bar_set_flag(music_para->source_para, LV_OBJ_FLAG_HIDDEN, 0);
        lv_obj_set_width(ui_music_play_bottom, lv_pct(93));
    }
    else
    {
        ui_source_rig_bar_set_flag(music_para->source_para, LV_OBJ_FLAG_HIDDEN, 1);
        lv_obj_set_width(ui_music_play_bottom, lv_pct(100));
    }

    if(music_para->work_source == ID_WORK_SOURCE_BT || (music_para->party_work_source == PARTY_D_SOURCE_BT_AUDIO))
    {
        ui_source_nosupport_browser(ui_music_para->source_para, 1);
        //lv_obj_remove_flag(music_para->source_para->ui_top_right, LV_OBJ_FLAG_CLICKABLE);      /// Flags
        lv_img_set_src(ui_music_play_logo, ui_music_get_res(music_para, MUSIC_THEME_BT_ICON_BMP));
        //lv_obj_clear_flag(ui_music_play_logo_text, LV_OBJ_FLAG_HIDDEN);     /// Flags
        lv_label_set_text(ui_music_play_logo_text, "Blutooth");
        music_player_ui_update_timer_proc(ui_music_para);
        if(__music_draw_bt_status(ui_music_para) == 1)
        {
            __music_msg("root_para->frist_start_bt=%d\n", root_para->frist_start_bt);
            if(root_para->frist_start_bt)
            {
                music_get_audio_file_info_external(ui_music_para);   
                __DrawMusicRootTypeorAlbumTagPic(ui_music_para);
                __DrawMusicPlayerFileInfo(ui_music_para,&(ui_music_para->play_file_info)); 
            }
        }
    
        root_para->frist_start_bt = 0;
    }
    else if(music_para->work_source == ID_WORK_SOURCE_USB && music_para->usb_device == USB_DEVICE_IPOD)
    {
        ui_source_nosupport_browser(ui_music_para->source_para, 1);
        //lv_obj_remove_flag(music_para->source_para->ui_top_right, LV_OBJ_FLAG_CLICKABLE);      /// Flags
        lv_img_set_src(ui_music_play_logo, ui_music_get_res(music_para, MUSIC_THEME_USB_ICON_BMP));
        //lv_obj_add_flag(ui_music_play_logo_text, LV_OBJ_FLAG_HIDDEN);     /// Flags
        lv_label_set_text(ui_music_play_logo_text, "Iphone");

        music_player_ui_update_timer_proc(ui_music_para);
        /*if(__music_draw_bt_status(ui_music_para) == 1)
        {
            __music_msg("root_para->frist_start_bt=%d\n", root_para->frist_start_bt);
            if(root_para->frist_start_bt)
            {
                music_get_audio_file_info_external(ui_music_para);   
                __DrawMusicRootTypeorAlbumTagPic(ui_music_para);
                __DrawMusicPlayerFileInfo(ui_music_para,&(ui_music_para->play_file_info)); 
            }
        }*/
    
        root_para->frist_start_bt = 0;
    }
    else if(music_para->work_source == ID_WORK_SOURCE_USB || (music_para->party_work_source == PARTY_D_SOURCE_USB))
    {
        lv_img_set_src(ui_music_play_logo, ui_music_get_res(music_para, MUSIC_THEME_USB_ICON_BMP));
        //lv_obj_add_flag(ui_music_play_logo_text, LV_OBJ_FLAG_HIDDEN);     /// Flags
        lv_label_set_text(ui_music_play_logo_text, "USB");
    }
}

static __s32 ui_music_internal_para_update(ui_music_para_t * music_para)
{
    reg_root_para_t* last_root_para;
    reg_music_para_t* music_last_para;
	reg_system_para_t*sys_para;
    
	sys_para = (reg_system_para_t*)dsk_reg_get_para_by_app(REG_APP_SYSTEM);
        
    music_last_para = (reg_music_para_t*)dsk_reg_get_para_by_app(REG_APP_MUSIC);
    last_root_para = (reg_root_para_t*)dsk_reg_get_para_by_app(REG_APP_ROOT);

    music_para->work_source = app_root_mcu_get_source();
    music_para->usb_device = music_last_para->usb_device;
    music_para->theme_bmp_start = MUSIC_TEMP_NIGHT_NIG_MEDIA_SETUP_REPEAT_B_BMP;
    music_para->theme_bmp_end = MUSIC_TEMP_NIGHT_NIG_COM_RIG_FUNC_ICON_BMP;
    music_para->source_obj = ui_source_content;//(lv_obj_t *)ui_source_create(ui_source);
    music_para->source_para = (ui_source_para_t *)lv_obj_get_user_data(music_para->source_obj);
    ui_party_comms_sync_party_own_info(&music_para->source_para->party_info);
    
    __music_msg("broadcast_source=%d\n",music_para->source_para->party_info.bc_info.broadcast_source);
    
    if(music_para->source_para->party_info.bc_info.broadcast_source && music_para->source_para->party_info.bc_info.source == PARTY_D_SOURCE_USB)
    {
        music_para->party_work_source = PARTY_D_SOURCE_USB;
    }
    else if(music_para->source_para->party_info.bc_info.broadcast_source && music_para->source_para->party_info.bc_info.source == PARTY_D_SOURCE_BT_AUDIO)
    {
        music_para->party_work_source = PARTY_D_SOURCE_BT_AUDIO;
    }
    else
    {
        music_para->party_work_source = 0;
    }
    __music_msg("work_source=%d, usb_device=%d, source=%d, role=%d\n",music_para->work_source, music_para->usb_device, music_para->source_para->party_info.bc_info.source, music_para->source_para->party_info.role);
}

static __s32 music_player_ui_update_timer_proc(ui_music_para_t * music_para)
{
	__s32 ret = EPDK_OK;
	reg_music_para_t *music_last_para = NULL;	
	reg_aux_para_t* aux_para;
	reg_system_para_t *sys_para;
	reg_root_para_t *last_root_para = NULL;	
    
	last_root_para = dsk_reg_get_para_by_app(REG_APP_ROOT);
	sys_para = (reg_system_para_t*)dsk_reg_get_para_by_app(REG_APP_SYSTEM);
	aux_para = (reg_aux_para_t*)dsk_reg_get_para_by_app(REG_APP_AUX);	
	music_last_para = dsk_reg_get_para_by_app(REG_APP_MUSIC);	

    //__music_msg("bt_status=%d %d\n", music_para->ui_music_data_para.bt_status, aux_para->bt_status);    
    if(music_para->ui_music_data_para.cur_play_state != music_last_para->cur_play_state)
    {
        music_para->ui_music_data_para.cur_play_state = music_last_para->cur_play_state;

        DrawMusicPlayPause(music_para);
    }
    
    if(music_para->ui_music_data_para.cur_play_mode != music_last_para->cur_play_mode)
    {
        music_para->ui_music_data_para.cur_play_mode = music_last_para->cur_play_mode;

    }

    if(music_para->ui_music_data_para.repeat_mode != music_last_para->repeat_mode)
    {
        music_para->ui_music_data_para.repeat_mode = music_last_para->repeat_mode;

        DrawMusicRepeat(music_para);
    }
    
    if(music_para->ui_music_data_para.random_mode != music_last_para->random_mode)
    {
        music_para->ui_music_data_para.random_mode = music_last_para->random_mode;

        DrawMusicRandom(music_para);
    }

    //__music_msg("total_playtime=%d %d\n", music_para->ui_music_data_para.total_playtime, music_last_para->total_playtime);
    if(music_para->ui_music_data_para.total_playtime != music_last_para->total_playtime)
    {
        music_para->ui_music_data_para.total_playtime = music_last_para->total_playtime;

    }

    //__music_msg("cur_playtime=%d %d\n", music_para->ui_music_data_para.cur_playtime, music_last_para->cur_playtime);
    if(music_para->ui_music_data_para.cur_playtime != music_last_para->cur_playtime)
    {
        music_para->ui_music_data_para.cur_playtime = music_last_para->cur_playtime;

        DrawMusicPlayingTime(music_para->ui_music_data_para.cur_playtime);
        DrawMusicProgressBar(music_para->ui_music_data_para.total_playtime, music_para->ui_music_data_para.cur_playtime); 
    }

    if(music_para->ui_music_data_para.artwork_buf != last_root_para->artwork_buf)
    {
        music_para->ui_music_data_para.artwork_buf = last_root_para->artwork_buf;

    }
    
    if(music_para->ui_music_data_para.artwork_width != last_root_para->artwork_width)
    {
        music_para->ui_music_data_para.artwork_width = last_root_para->artwork_width;
    }
    
    if(music_para->ui_music_data_para.artwork_height != last_root_para->artwork_height)
    {
        music_para->ui_music_data_para.artwork_height = last_root_para->artwork_height;
    }

    if(music_para->ui_music_data_para.artwork_size != last_root_para->artwork_size)
    {
        music_para->ui_music_data_para.artwork_size = last_root_para->artwork_size;
    }

    if(music_para->ui_music_data_para.artwork_status != last_root_para->artwork_status)
    {
        music_para->ui_music_data_para.artwork_status = last_root_para->artwork_status;
    }

    if(music_para->ui_music_data_para.artwork_type != last_root_para->artwork_type)
    {
        music_para->ui_music_data_para.artwork_type = last_root_para->artwork_type;
    }
    
    if(strlen(music_para->ui_music_data_para.Artist) != strlen(music_last_para->Artist) || eLIBs_memcmp(music_para->ui_music_data_para.Artist, music_last_para->Artist, sizeof(music_para->ui_music_data_para.Artist)) == EPDK_NO)
    {
        eLIBs_memcpy(music_para->ui_music_data_para.Artist, music_last_para->Artist, sizeof(music_para->ui_music_data_para.Artist));

        get_audio_info_artist_from_reg(music_para);
        DrawMusicFileArtist(music_para,music_para->play_file_info.artist,music_para->play_file_info.artist_charset);
    }

    if(strlen(music_para->ui_music_data_para.Title) != strlen(music_last_para->Title) || eLIBs_memcmp(music_para->ui_music_data_para.Title, music_last_para->Title, sizeof(music_para->ui_music_data_para.Title)) == EPDK_NO)
    {
        eLIBs_memcpy(music_para->ui_music_data_para.Title, music_last_para->Title, sizeof(music_para->ui_music_data_para.Title));

        get_audio_info_title_from_reg(music_para);
        DrawMusicFileName(music_para,music_para->play_file_info.filename,music_para->play_file_info.title_charset);
    }

    if(strlen(music_para->ui_music_data_para.Album) != strlen(music_last_para->Album) || eLIBs_memcmp(music_para->ui_music_data_para.Album, music_last_para->Album, sizeof(music_para->ui_music_data_para.Album)) == EPDK_NO)
    {
        eLIBs_memcpy(music_para->ui_music_data_para.Album, music_last_para->Album, sizeof(music_para->ui_music_data_para.Album));

        get_audio_info_album_from_reg(music_para);
        DrawMusicFileAlbum(music_para,music_para->play_file_info.album,music_para->play_file_info.album_charset);
    }

    if(music_para->work_source == ID_WORK_SOURCE_BT || (music_para->party_work_source == PARTY_D_SOURCE_BT_AUDIO))
    {
        if(music_para->ui_music_data_para.bt_status != aux_para->bt_status)
        {
            music_para->ui_music_data_para.bt_status = aux_para->bt_status;
        
            music_draw_bt_status(music_para);
        }
        
        if(strlen(music_para->ui_music_data_para.btaudio_phone_name) != strlen(sys_para->btaudio_phone_name) || eLIBs_memcmp(music_para->ui_music_data_para.btaudio_phone_name, sys_para->btaudio_phone_name, sizeof(music_para->ui_music_data_para.btaudio_phone_name)) == EPDK_NO)
        {
            eLIBs_memcpy(music_para->ui_music_data_para.btaudio_phone_name, sys_para->btaudio_phone_name, sizeof(music_para->ui_music_data_para.btaudio_phone_name));

            music_draw_bt_phone_text(music_para);
        }
    }
    
    if(0)
    {
        music_update_low_voltage(music_para, 0);
    }

    if(0)
    {
        music_update_charge(music_para, 0);
    }

    if(0)
    {
        music_update_eq(music_para, 1);
    }

    return ret;
}

static __s32 ui_music_party_source_update(ui_music_para_t * music_para)
{
	root_app_para_t * root_para = app_root_get_para();//pmsg->p_arg;

    if(music_para->source_para->party_info.role == PARTY_D_ROLE_BROADCAST)
    {
        if(music_para->source_para->party_info.bc_info.source == PARTY_D_SOURCE_USB)
        {
            if(music_para->party_work_source == PARTY_D_SOURCE_USB)
            {
                ui_music_top_right_event_handler(music_para);
            }
            else
            {
                music_para->source_para->party_info.bc_info.broadcast_source = 1;
                app_root_source_create(root_para,ID_WORK_SOURCE_USB);
            }
        }
        else if(music_para->source_para->party_info.bc_info.source == PARTY_D_SOURCE_BT_AUDIO)
        {
            if(music_para->party_work_source == PARTY_D_SOURCE_BT_AUDIO)
            {
                ui_music_top_right_event_handler(music_para);
            }
            else
            {
                music_para->source_para->party_info.bc_info.broadcast_source = 1;
                app_root_source_create(root_para,ID_WORK_SOURCE_BT);
            }
        }
        else if(music_para->source_para->party_info.bc_info.source == PARTY_D_SOURCE_AUXIN)
        {
            music_para->source_para->party_info.bc_info.broadcast_source = 1;
            app_root_source_create(root_para,ID_WORK_SOURCE_AUXIN);
        } 
    }
    else if(music_para->source_para->party_info.role == PARTY_D_ROLE_RECEIVER)
    {
        app_root_source_create(root_para,ID_WORK_SOURCE_PARTY);
    }
}

static __s32 music_player_ui_update_proc(ui_music_para_t * music_para, __gui_msg_t *msg)
{
	__s32 ret = EPDK_FAIL;
	reg_music_para_t *music_last_para = NULL;	
	reg_root_para_t* last_root_para;
	reg_aux_para_t* para;
	reg_system_para_t* sys_para;

	sys_para = (reg_system_para_t*)dsk_reg_get_para_by_app(REG_APP_SYSTEM);
	music_last_para = dsk_reg_get_para_by_app(REG_APP_MUSIC);
	last_root_para = (reg_root_para_t*)dsk_reg_get_para_by_app(REG_APP_ROOT);
	para = (reg_aux_para_t*)dsk_reg_get_para_by_app(REG_APP_AUX);	
	

	if(music_para == NULL)
		return EPDK_FAIL;

    __music_msg("music_para->work_source=%d, party_work_source=%d\n",music_para->work_source, music_para->party_work_source);
    __music_msg("music_player_ui_update_proc: msg->dwAddData1=%d!\n",msg->dwAddData1);
    __music_msg("music_para->root_type=%d, usb_device=%d\n",music_para->root_type, music_para->usb_device);

    if((music_para->work_source == ID_WORK_SOURCE_BT || (music_para->party_work_source == PARTY_D_SOURCE_BT_AUDIO)) || (music_para->work_source == ID_WORK_SOURCE_USB && music_para->usb_device == USB_DEVICE_IPOD) || music_para->root_type==RAT_USB_REMOTE)
    {
	    switch(msg->dwAddData1)
        {
        	case GUI_MSG_UI_USB_PLAY_TRACK:
        	{	
        		__music_msg("get paly track update: %d!!\n",music_last_para->cur_file);
        		if(music_para->visiable)
        		{
        			__u8 ChangePlaySlide = 0;
        			reg_music_para_t *music_last_para = NULL;	
        			music_last_para = dsk_reg_get_para_by_app(REG_APP_MUSIC);
        			music_para->play_index = music_last_para->cur_file;	
        			ChangePlaySlide = music_last_para->RotateChangePlaySlide; // 1: next  2: prev 0: no slide
        			DrawMusicPlaySongNum(music_para,music_para->play_index);
        			if(ChangePlaySlide == 1) //next
        			{
        				//last_root_para->artwork_status = ARTWORK_STATUS_NONE;
        				MusicPlayInfoSlideShow(music_para,1);
        			}
        			else if(ChangePlaySlide == 2)
        			{
        				//last_root_para->artwork_status = ARTWORK_STATUS_NONE;
        				MusicPlayInfoSlideShow(music_para,0); //prev
        			}
        			music_last_para->RotateChangePlaySlide = 0;//reset this flag
        		}

        	}
        		break;
        	case GUI_MSG_UI_USB_PLAY_STATE:
        	{	
        		__music_msg("play state update: %d\n",music_last_para->cur_play_state);
        		if(music_para->visiable)
        		{
        			reg_music_para_t *music_last_para = NULL;	
        			music_last_para = dsk_reg_get_para_by_app(REG_APP_MUSIC);
        			music_para->playsta = music_last_para->cur_play_state;
                    music_para->ui_music_data_para.cur_play_state = music_last_para->cur_play_state;
                    DrawMusicBottom(music_para);
        		}

        	}
        		break;
        	case GUI_MSG_UI_USB_PLAY_MODE:
        	{	
        		reg_music_para_t *music_last_para = NULL;	
        		reg_system_para_t*sy_para;
        		sy_para = (reg_system_para_t*)dsk_reg_get_para_by_app(REG_APP_SYSTEM);
        		music_last_para = dsk_reg_get_para_by_app(REG_APP_MUSIC);
        		__music_msg("play mode update[%d]: %d\n",music_para->play_mode,music_last_para->cur_play_mode);

        		if(music_para->visiable)
        		{
        			music_para->play_mode = music_last_para->cur_play_mode;
                    music_para->ui_music_data_para.cur_play_mode = music_last_para->cur_play_mode;
                    DrawMusicBottom(music_para);
        		}
        	}
        		break;
            
			case GUI_MSG_UI_USB_TRACK_INFO:
				{
					__music_msg("\n run update track info \n");
					/*if(music_para->work_source == ID_WORK_SOURCE_USB && music_para->usb_device == USB_DEVICE_EXT2REMOTE && music_para->root_type == RAT_USB_REMOTE)
					{
						last_root_para->artwork_status = ARTWORK_STATUS_NONE;
					}*/
    				DrawMusicPlayerFileInfo(music_para,&(music_para->play_file_info));	
				}
				break;

        	case GUI_MSG_UI_USB_PLAY_ID3:
        	{	
        		__music_msg("ID3 update !!\n");
        		if(music_para->visiable)
        		{
        			reg_music_para_t *music_last_para = NULL;	
        			
        			music_last_para = dsk_reg_get_para_by_app(REG_APP_MUSIC);

        			__music_msg("\n run update id3 \n");
                    music_player_ui_update_timer_proc(music_para);
                    //music_get_audio_file_info_external(music_para);   
                    DrawMusicRootTypeorAlbumTagPic(music_para);
    				DrawMusicPlayerFileInfo(music_para,&(music_para->play_file_info));	
    				//231212 added for ID_WORK_SOURCE_USB for protect BT IAP2 album art clear bug
        		}
        	}
        		break;
        /*
        	case GUI_MSG_UI_USB_PLAY_FOLDER:
        	{	
        		if(music_para->visiable)
        		{
        			__s32 		src_encond;
        			__u32               len;
        			char folder_name[128] = {0};
        			reg_music_para_t *music_last_para = NULL;	
        			music_last_para = dsk_reg_get_para_by_app(REG_APP_MUSIC);
        			GUI_LyrWinSel(this->music_layer);
        			src_encond = get_audio_enconding(music_last_para->Folder[0]);
        			charset_convert( &music_last_para->Folder[1], 67,src_encond, folder_name, RAT_MAX_FULL_PATH_LEN, &len );
        			if( is_all_blank(folder_name, len))
        				folder_name[0] = '\0';
        			DrawMusicFilePath(this,folder_name,0);
        		}

        	}
        		break;
        */
#if 0
        	case GUI_MSG_UI_USB_PLAY_TIME:
        		if(music_para->visiable)
        		{
        			__u32 total_time = 0, cur_time = 0;
        			reg_music_para_t *music_last_para = NULL;	
        			music_last_para = dsk_reg_get_para_by_app(REG_APP_MUSIC);
        			total_time = music_last_para->total_playtime;
        			cur_time = music_last_para->cur_playtime;
        			if( cur_time > total_time )
        			{
        				cur_time = total_time ;
        			}
                    DrawMusicPlayingTime(cur_time);	
    				DrawMusicProgressBar(total_time, cur_time);	
        		}
        		break;
#endif
        	case GUI_MSG_UI_ARTWORK_READY:
        	{	
        		__music_msg("music play---update artwork pic\n");
        		if(music_para->visiable)
        		{
        			reg_music_para_t *music_last_para = NULL;	
        			music_last_para = dsk_reg_get_para_by_app(REG_APP_MUSIC);
                    music_player_ui_update_timer_proc(music_para);
                    DrawMusicRootTypeorAlbumTagPic(music_para);
    				DrawMusicPlayerFileInfo(music_para,&(music_para->play_file_info)); //new added 250122	
        		}

        	}
        		break;

        	case GUI_MSG_UI_ARTWORK_CLEAR:
        		__music_msg("clear artwork !!\n");
        		if(music_para->visiable)
        		{
        			reg_music_para_t *music_last_para = NULL;	
        			music_last_para = dsk_reg_get_para_by_app(REG_APP_MUSIC);
                    music_player_ui_update_timer_proc(music_para);
        			DrawMusicRootTypeorAlbumTagPic(music_para);
        		}
        		break;
#if 0	
        	case GUI_MSG_UI_ARTWORK_CLEAR: //mllee 221031 new added
        		if(music_para->visiable && this->have_album_tags == 1)
        		{
        			reg_music_para_t *music_last_para = NULL;	
        			music_last_para = dsk_reg_get_para_by_app(REG_APP_MUSIC);
        			GUI_LyrWinSel(this->music_layer); //mllee 130201
        			DrawMusicRootTypeorAlbumTagPic(this);
        		}
        		break;
#endif
        		
        	case GUI_MSG_UI_BT_STATUS:
        		if(music_para->visiable)
        		{
                    music_para->ui_music_data_para.bt_status = para->bt_status;
        			music_draw_bt_status(music_para);
                    DrawMusicRootTypeorAlbumTagPic(music_para);
        		}
        		break;

        	default:
        		break;
        }
    }
    //else if(music_para->work_source == ID_WORK_SOURCE_USB || (music_para->party_work_source == PARTY_D_SOURCE_USB))
    {
        switch(msg->dwAddData1)
        {
            case GUI_MSG_UI_VOLUME_ADJUST:
            case GUI_MSG_UI_SUB_VOL:
            {
                ui_source_send_event(music_para->source_para, LV_EVENT_MSG_RECEIVED, (void *)msg);
            }
            break;

            //role 
            case GUI_MSG_UI_PARTY_ROLE_UPDATE:
                //if(app_root_mcu_get_current_source() == ID_WORK_SOURCE_PARTY)
                {
                    ui_party_comms_sync_party_own_info(&music_para->source_para->party_info);
                    __music_msg("Role update[%d]\n",music_para->source_para->party_info.role);
                    if(music_para->source_para->party_info.role == PARTY_ROLE_RECEIVER)
                        ui_music_party_source_update(music_para);
                }
            break;

            //broadcast source selected
            case GUI_MSG_UI_PARTY_BROADCAST_SOURCE:
                //if(app_root_mcu_get_current_source() == ID_WORK_SOURCE_PARTY)
                {
                    ui_party_comms_sync_broadcast_info(&music_para->source_para->party_info);
                    __music_msg("Broadcast source[%d]\n",music_para->source_para->party_info.bc_info.source);
                    if(music_para->source_para->party_info.role == PARTY_ROLE_BROADCAST)
                        ui_music_party_source_update(music_para);
                }
            break;

            case GUI_MSG_UI_USB_DEVICE: //for USB-iPod exist or not
            {
                __music_msg("GUI_MSG_UI_USB_DEVICE\n");
                ui_music_internal_para_update(music_para);
                ui_music_change_mode(music_para);
                __music_cmd2parent(NULL, CLOSE_SHOW_LOADING, 0, 0);
            }
            break;

            // MCU Protocol v2 Data Update Messages
            case GUI_MSG_UI_NEW_USB_UPDATE:
            {
                __music_msg("MCU USB data update received\n");
                if (music_para->source_para && reg_app_para) {
                    __u8 current_source = reg_app_para->system_para.g_sys_para.current_source;
                    if (current_source == SYS_SOURCE_USB) {
                        UI_MCU_HANDLE_UPDATE(&reg_app_para->usb_para.sync, music_para->mcu_ops, music_para);
                    }
                }
            }
            break;

            case GUI_MSG_UI_NEW_BT_AUDIO_UPDATE:
            {
                __music_msg("MCU BT Audio data update received\n");
                if (music_para->source_para && reg_app_para) {
                    __u8 current_source = reg_app_para->system_para.g_sys_para.current_source;
                    if (current_source == SYS_SOURCE_BT_AUDIO) {
                        UI_MCU_HANDLE_UPDATE(&reg_app_para->bt_audio_para.sync, music_para->mcu_ops, music_para);
                    }
                }
            }
            break;

            default:
                break;
        }
    }
}

void __ui_music_remove_hidden(ui_music_para_t * music_para)
{
	__music_msg("ui_bt_remove_hidden\n");
	if(!ui_music_timer)
	{
		ui_music_timer = lv_timer_create(ui_event_music_timer_cb, MUSIC_TIMER_INTERVAL*10, music_para);
	}
	_ui_flag_modify(ui_music, LV_OBJ_FLAG_HIDDEN, _UI_MODIFY_FLAG_REMOVE);
#ifdef LV_ADD_ANIM_SCREEN
	Opacity_Increase_Animation(ui_music, 0, NULL);
#endif
}

void __ui_music_add_hidden(ui_music_para_t * music_para)
{
	__music_msg("ui_music_add_hidden\n");
	_ui_flag_modify(ui_music, LV_OBJ_FLAG_HIDDEN, _UI_MODIFY_FLAG_ADD);
	/*if(ui_music_timer)
	{
		lv_timer_del(ui_music_timer);
		ui_music_timer = NULL;
	}*/
#ifdef LV_ADD_ANIM_SCREEN
	lv_obj_set_style_opa(ui_music, 0, 0);
#endif
}

void __ui_music_layer_on(ui_music_para_t * music_para)
{
	if(music_para->mode_store == 0)
	{
		return EPDK_FAIL;
	}

	music_para->mode_store = 0;
	music_para->visiable = EPDK_TRUE;

	app_root_msg_send_opn(GUI_MSG_AUX_CHANNEL,AUX_AUDIO_CHANNEL,0,0,0,0,0);
	ui_music_remove_hidden(music_para);
}

void __ui_music_layer_sleep(ui_music_para_t * music_para)
{
	if(music_para->mode_store == 1)
	{
		return EPDK_FAIL;
	}

	music_para->mode_store = 1;

	music_player_long_string_stop_roll(music_para);//20131213
	music_para->visiable = EPDK_FALSE;
	ui_music_add_hidden(music_para);
}

__s32 ui_music_refresh_handler(void * para, __gui_msg_t * pmsg)
{
	__s32 ret = EPDK_OK;
	__gui_msg_t msg;
	ui_music_para_t * music_para = ui_music_para;//(ui_music_para_t *)para;
	root_app_para_t * root_para = app_root_get_para();//pmsg->p_arg;

	if(!music_para)
	{
		return EPDK_FAIL;
	}
	
    memset(&msg, 0x00, sizeof(msg));

    
	//__music_msg("LOWORD(pmsg->id)=0x%x\n", LOWORD(pmsg->id));
    if(LOWORD(pmsg->id) == GUI_MSG_PAINT)
    {
		//__music_msg("HIWORD(pmsg->id)=0x%x, GUI_MSG_REFRESH_INIT=0x%x\n", HIWORD(pmsg->id), GUI_MSG_REFRESH_INIT);
		switch(HIWORD(pmsg->id))
		{
			case GUI_MSG_REFRESH_INIT:
			{
                __music_msg("GUI_MSG_REFRESH_INIT\n");
                ui_music_night_theme(ui_music_para, THEME_NIGHT);

                ui_source_switch_clean(ui_music_para->source_para);

                music_slider_anim_flag = LV_ANIM_ON;
                music_slider_value = 0;
				ui_music_screen_init(ui_music_para);

                if(music_para->work_source == ID_WORK_SOURCE_USB && music_para->root_type == RAT_USBIPOD && music_para->usb_device != USB_DEVICE_IPOD)
                {

                }
                else
                {
                    ui_music_change_mode(ui_music_para);

    				__music_cmd2parent(NULL, CLOSE_SHOW_LOADING, 0, 0);
                }
                
				ui_music_para->music_main_obj = ui_music;
#ifdef LV_ADD_ANIM_SCREEN
				Opacity_Increase_Animation(ui_music, 0, NULL);
#endif
				root_para->app_interface_music->info.src_win = ui_music;
                __music_msg("GUI_MSG_REFRESH_INIT end\n");
			}
			break;

			case GUI_MSG_REFRESH_UNINIT:
			{
                __music_msg("GUI_MSG_REFRESH_UNINIT ui_music_timer=%p\n", ui_music_timer);
				if(ui_music_timer)
				{
					lv_timer_del(ui_music_timer);
					ui_music_timer = NULL;
				}
				ui_music_screen_uninit(ui_music_para);
				ui_music_uninit_res(ui_music_para);
				esMEMS_Bfree(ui_music_para, sizeof(ui_music_para_t));
				ui_music = NULL;
                ui_music_menu = NULL;
                ui_music_bottom_list = NULL;
                ui_music_bottom_browse = NULL;
                ui_music_bottom_repeat = NULL;
                ui_music_bottom_random = NULL;
				ui_music_para = NULL;
                
                __music_msg("GUI_MSG_REFRESH_UNINIT end\n");
			}
			break;

			case GUI_MSG_REFRESH_HIDDEN:
			{
				__u32 flag = (__u32)pmsg->dwAddData1;

				if(flag == 0)
				{
					__ui_music_remove_hidden(music_para);
				}
				else
				{
					__ui_music_add_hidden(music_para);
				}
			}
			break;
			
			case GUI_MSG_REFRESH_WIN:
			{
				__u8 win_type = (__u8)pmsg->dwAddData1;
				__u32 flag = (__u32)pmsg->dwAddData2;

			}
			break;
			
			case GUI_MSG_REFRESH_LIST:
			{
			}
			break;
			
			case GUI_MSG_REFRESH_PART:
			{
				if(pmsg->p_arg == (void *)__music_tips_dialog_create)
				{
                    __s32 title_id = (__s32)pmsg->dwAddData1;
                    __s32 content_id = (__s32)pmsg->dwAddData2;
                    __u32 time_out = LOWORD(pmsg->dwReserved);
                    __s32 id = HIWORD(pmsg->dwReserved);
					
					__music_tips_dialog_create(music_para, title_id, content_id, time_out, id);
				}
				else if(pmsg->p_arg == (void *)__music_tips_dialog_cancel)
				{					
					__music_tips_dialog_cancel(music_para);
				}
				else if(pmsg->p_arg == (void *)__music_tips_dialog_confirm)
				{					
					__music_tips_dialog_confirm(music_para);
				}
				else if(pmsg->p_arg == (void *)__music_update_low_voltage)
				{
					__u8 update = (__u8)pmsg->dwAddData1;
					
					__music_update_low_voltage(music_para, update);
				}
				else if(pmsg->p_arg == (void *)__music_update_charge)
				{
					bool update = (bool)pmsg->dwAddData1;
					
					__music_update_charge(music_para, update);
				}
				else if(pmsg->p_arg == (void *)__music_update_eq)
				{
					bool update = (bool)pmsg->dwAddData1;
					
					__music_update_eq(music_para, update);
				}
				else if(pmsg->p_arg == (void *)__DrawMusicDrawPic)
				{
					__u32 bmp = (__u32)pmsg->dwAddData1;
					
					__DrawMusicDrawPic(music_para, bmp);
				}
				else if(pmsg->p_arg == (void *)__DrawMusicBottom)
				{
					__DrawMusicBottom(music_para);
				}
				else if(pmsg->p_arg == (void *)__DrawMusicPlaySongNum)
				{
					__u32 song_num = (__u32)pmsg->dwAddData1;
					
					__DrawMusicPlaySongNum(music_para, song_num);
				}
				else if(pmsg->p_arg == (void *)__DrawMusicFileArtist)
				{
					char *name = (char *)pmsg->dwAddData1;
					__epdk_charset_enm_e charset = (__epdk_charset_enm_e)pmsg->dwAddData2;
					
					__DrawMusicFileArtist(music_para, name, charset);
				}
				else if(pmsg->p_arg == (void *)__DrawMusicFileAlbum)
				{
					char *name = (char *)pmsg->dwAddData1;
					__epdk_charset_enm_e charset = (__epdk_charset_enm_e)pmsg->dwAddData2;
					
					__DrawMusicFileAlbum(music_para, name, charset);
				}
				else if(pmsg->p_arg == (void *)__DrawMusicFileName)
				{
					char *name = (char *)pmsg->dwAddData1;
					__epdk_charset_enm_e charset = (__epdk_charset_enm_e)pmsg->dwAddData2;
					
					__DrawMusicFileName(music_para, name, charset);
				}
				else if(pmsg->p_arg == (void *)__DrawMusicPlayingTime)
				{
					__u32 time = (__u32)pmsg->dwAddData1;

					__DrawMusicPlayingTime(time);
				}
				else if(pmsg->p_arg == (void *)__CleanMusicTotalTime)
				{
					__CleanMusicTotalTime();
				}
				else if(pmsg->p_arg == (void *)__DrawMusicProgressBarPoint)
				{
					__u32 total = (__u32)pmsg->dwAddData1;
					__u32 cur_value = (__u32)pmsg->dwAddData2;
					
					__DrawMusicProgressBarPoint(total, cur_value);
				}
				else if(pmsg->p_arg == (void *)__DrawMusicPlayerFileInfo)
				{
					audio_file_info_t *file_info = (audio_file_info_t *)pmsg->dwAddData1;
					
					__DrawMusicPlayerFileInfo(music_para, file_info);
				}
				else if(pmsg->p_arg == (void *)__music_player_long_string_stop_roll)
				{
					__music_player_long_string_stop_roll(music_para);
				}
#ifdef PREV_NEXT_SONG_SLIDE_OUT
				else if(pmsg->p_arg == (void *)__MusicPlayInfoSlideShow)
				{
					__u8 prev_next = (__u8)pmsg->dwAddData1;
					
					__MusicPlayInfoSlideShow(music_para, prev_next);
				}
#endif
                else if(pmsg->p_arg == (void *)__music_draw_bt_status)
				{
					__music_draw_bt_status(music_para);
				}
                else if(pmsg->p_arg == (void *)__music_draw_bt_phone_text)
				{
					__music_draw_bt_phone_text(music_para);
				}
                else if(pmsg->p_arg == (void *)__DrawMusicRandom)
				{
					__DrawMusicRandom(music_para);
				}
                else if(pmsg->p_arg == (void *)__DrawMusicRepeat)
				{
					__DrawMusicRepeat(music_para);
				}
                else if(pmsg->p_arg == (void *)__DrawMusicPlayPause)
				{
					__DrawMusicPlayPause(music_para);
				}
			}
			break;
			
			case GUI_MSG_REFRESH_MSG:
			{
				__s32 id = (__s32)pmsg->dwAddData1;
				__s32 data1 = (__s32)pmsg->dwAddData2;
				__s32 data2 = (__s32)pmsg->dwReserved;
				
				__music_cmd2parent(NULL, id, data1, data2);
			}
			break;
			
			default:
			{
				ret = EPDK_FAIL;
			}
			break;
		}
	
    }
	else
	{
		ret = EPDK_FAIL;
	}
	
	return ret;
}

static void ui_music_source_event_handler(ui_music_para_t * music_para)
{    
    ui_music_bottom_group_update(music_para, 0, 0, 0);
    ui_source_title(music_para->source_para, "Source", 0);
    ui_source_sub_open(music_para->source_para);
    music_cmd2parent(NULL, SWITCH_TO_MMENU, 0, 0); 
    lv_label_set_long_mode(ui_music_id3_text_panel_artist_text, LV_LABEL_LONG_CLIP);
    lv_label_set_long_mode(ui_music_id3_text_panel_song_text, LV_LABEL_LONG_CLIP);
    lv_label_set_long_mode(ui_music_id3_text_panel_album_text, LV_LABEL_LONG_CLIP);
}

static void ui_music_top_left_event_handler(ui_music_para_t * music_para)
{
    if(music_para->broadcast_source_flag == 1)
    {
        ui_music_broadcast_source_screen_uninit(music_para);

        return;
    }
    
    switch(music_para->menu_win)
    {    
        case MUSIC_MENU_WIN_NONE:
        {
            if(music_para->bottom_fouse_flag == 1)
            {
                ui_music_bottom_group_update(music_para, 0, 0, 0);
            }
            ui_home_restore_switch_source();
        }
        break;

        case MUSIC_MENU_WIN_LIST:
        {
            if(music_para->ui_list_para.start_id != 0)
            {
                ui_explorer_JumpToMainDir(&music_para->ui_list_para, music_para->ui_list_para.focus_id);
                ui_list_folder_reinit(music_para);
            }
            else
            {
                ui_music_switch_win(music_para, MUSIC_MENU_WIN_NONE);
            }
        }
        break;

#ifdef USB_BROSWER_ALBUM_ARTIST_SORT
        case MUSIC_MENU_WIN_BROWSE:
        {
            //if(music_para->into_list_sort_flag==1)
            {
                if(music_para->list_sort_layer==0)
                {
                    ui_music_switch_win(music_para, MUSIC_MENU_WIN_NONE);
                    DrawMusicRootTypeorAlbumTagPic(music_para);
                }
                else
                {
                    if(music_para->song_layer_flag==0)
                    {
                        music_para->list_sort_layer = 0;
                        music_para->ui_list_para.start_id = 0;
                        music_para->ui_list_para.item_cnt = 3;
                        music_para->ui_list_para.focus_id  =0;
                        ui_list_folder_reinit(music_para);
                        
                    }
                    else 
                    {
                        if(music_para->list_sort_type==0|music_para->list_sort_type==1)
                        {
                            music_para->song_layer_flag = 0;
                            ui_music_explorer_Jumpto_sort_dir(music_para, &music_para->ui_list_para);
                            ui_list_folder_reinit(music_para);
                        }
                        else if(music_para->list_sort_type==2)
                        {
                            music_para->song_layer_flag = 0;
                            music_para->list_sort_layer  =0;
                            music_para->ui_list_para.start_id = 0;
                            music_para->ui_list_para.item_cnt = 3;
                            music_para->ui_list_para.focus_id  =0;
                            ui_list_folder_reinit(music_para);
                        }
                    }
                }
            }
        }
        break;
#endif

        case MUSIC_MENU_WIN_FUNC:
        {
            ui_music_switch_win(music_para, MUSIC_MENU_WIN_NONE);
        }
        break;
    
        case MUSIC_MENU_WIN_FUNC_BROADCAST_SOURCE:
        {
            ui_music_switch_win(music_para, MUSIC_MENU_WIN_FUNC);
        }
        break;
        
        case MUSIC_MENU_WIN_FUNC_BROADCAST_DEVICE_NAME:
        {
            ui_music_switch_win(music_para, MUSIC_MENU_WIN_FUNC);
        }
        break;
    }
}

static void ui_music_top_right_event_handler(ui_music_para_t * music_para)
{
    if(music_para->broadcast_source_flag == 1)
    {
        ui_music_broadcast_source_screen_uninit(music_para);

        return;
    }
    
    if(music_para->menu_win == MUSIC_MENU_WIN_NONE)
    {
        if(music_para->bottom_fouse_flag == 1)
        {
            ui_music_bottom_group_update(music_para, 0, 0, 0);
        }
        ui_home_restore_switch_source();
    }
    else
    {
        ui_music_switch_win(music_para, MUSIC_MENU_WIN_NONE);
    }
    #if 0
    eLIBs_printf("ui_music_top_right_event_handler music_para->menu_win=%d\n", music_para->menu_win);
    
    if(music_para->menu_win > MUSIC_MENU_WIN_FUNC)
    {
        ui_music_switch_win(music_para, MUSIC_MENU_WIN_FUNC);
    }
    else if(music_para->menu_win == MUSIC_MENU_WIN_LIST)
    {
        if(music_para->source_para->rig_open_flag == 0 || ((music_para->source_para->rig_open_flag == 1) && music_para->source_para->menu_win != SOURCE_MENU_WIN_FUNC))
            ui_music_switch_win(music_para, MUSIC_MENU_WIN_NONE);
        else
            ui_music_switch_win(music_para, MUSIC_MENU_WIN_FUNC);
    }
    else if(music_para->source_para->old_menu_win <= SOURCE_MENU_WIN_AUDIO || music_para->source_para->old_menu_win == SOURCE_MENU_WIN_FUNC)
    {
        ui_music_switch_win(music_para, MUSIC_MENU_WIN_LIST);
    }
    #endif
}

static void ui_music_open_bar_menu_event_handler(ui_music_para_t * music_para)
{
    ui_music_switch_win(music_para, MUSIC_MENU_WIN_NONE);

    __music_msg("save_rig_select_flag=%d, rig_select_flag=%d, nosupport_browser=%d\n", music_para->source_para->save_rig_select_flag, music_para->source_para->rig_select_flag, music_para->source_para->nosupport_browser);
    
    if(music_para->source_para->save_rig_select_flag == SOURCE_BOTTOM || music_para->source_para->nosupport_browser)
    {
        ui_music_bottom_menu_event_handler(music_para);
    }
    else
    {
        ui_music_top_menu_event_handler(music_para);
    }
}

static void ui_music_top_menu_event_handler(ui_music_para_t * music_para)
{
    ui_music_switch_win(music_para, MUSIC_MENU_WIN_LIST);
}

static void ui_music_bottom_menu_event_handler(ui_music_para_t * music_para)
{
    ui_music_switch_win(music_para, MUSIC_MENU_WIN_FUNC);
}

static void ui_music_func_broadcast_source_event_handler(ui_music_para_t * music_para, uint32_t selected)
{
    reg_aux_para_t* last_aux_para;
    
    last_aux_para = (reg_aux_para_t*)dsk_reg_get_para_by_app(REG_APP_AUX);

    //ui_music_top_right_event_handler(music_para);

	switch(selected)
	{
		case PARTY_SOURCE_USB:
		{            
            if(music_para->source_para->party_info.bc_info.source == PARTY_D_SOURCE_USB)
            {
                ui_music_top_right_event_handler(music_para);
            }
            else
                ui_party_comms_set_bc_source((void *)&music_para->source_para->party_info,PARTY_D_SOURCE_USB);
                //app_root_send2mcu_party(UART_SEND_PARTY_MODE_WORK_SOURCE, ID_WORK_SOURCE_USB);
		}		
		break;

		case PARTY_SOURCE_BT:
		{
            if(music_para->source_para->party_info.bc_info.source == PARTY_D_SOURCE_BT_AUDIO)
            {
                ui_music_top_right_event_handler(music_para);
            }
            else
                ui_party_comms_set_bc_source((void *)&music_para->source_para->party_info,PARTY_D_SOURCE_BT_AUDIO);
                //app_root_send2mcu_party(UART_SEND_PARTY_MODE_WORK_SOURCE, ID_WORK_SOURCE_BT);
		}		
		break;

        case PARTY_SOURCE_AUX:
		{
            if(music_para->source_para->party_info.bc_info.source == PARTY_D_SOURCE_AUXIN)
            {
                ui_music_top_right_event_handler(music_para);
            }
            else
                ui_party_comms_set_bc_source((void *)&music_para->source_para->party_info,PARTY_D_SOURCE_AUXIN);
                //app_root_send2mcu_party(UART_SEND_PARTY_MODE_WORK_SOURCE, ID_WORK_SOURCE_AUXIN);
		}		
		break;
	}
}

static void ui_music_func_item_repeat_handler(ui_music_para_t * music_para, lv_obj_t * obj, __u32 index)
{
    //eLIBs_printf("MUSIC_MENU_FUNC_REPEAT\n");
#if 1
    lv_obj_t * list_item_shuffle = lv_obj_get_child(music_para->ui_list_para.item_obj, index);
    lv_obj_t * list_item_shuffle_panel = lv_obj_get_child(list_item_shuffle, 0);
    lv_obj_t * list_item_shuffle_content_panel = lv_obj_get_child(list_item_shuffle_panel, 1);
#if 1
    lv_obj_t * list_item_shuffle_content_text = lv_obj_get_child(list_item_shuffle_content_panel, 0);
    //eLIBs_printf("list_item_content_roller=%p, list_item_shuffle_content_roller=%p\n", list_item_content_roller, list_item_shuffle_content_roller);

    if(music_para->repeat_mode == MUSIC_MENU_REPEAT_ONE)
    {
        lv_label_set_text(obj, "Folder");
        lv_label_set_text(list_item_shuffle_content_text, "Off");
        ui_music_player_ctrl_repeat_by_mode(music_para, MUSIC_MENU_REPEAT_FOLDER);
    }
    else if(music_para->repeat_mode == MUSIC_MENU_REPEAT_FOLDER)
    {
        lv_label_set_text(obj, "All");
        lv_label_set_text(list_item_shuffle_content_text, "Off");
        ui_music_player_ctrl_repeat_by_mode(music_para, MUSIC_MENU_REPEAT_ALL);
    }
    else
    {
        lv_label_set_text(obj, "One");
        lv_label_set_text(list_item_shuffle_content_text, "Off");
        ui_music_player_ctrl_repeat_by_mode(music_para, MUSIC_MENU_REPEAT_ONE);
    }
#else
    lv_obj_t * list_item_shuffle_content_roller = lv_obj_get_child(list_item_shuffle_content_panel, 0);
    
    //eLIBs_printf("list_item_content_roller=%p, list_item_shuffle_content_roller=%p\n", list_item_content_roller, list_item_shuffle_content_roller);
    if(lv_roller_get_selected(obj) == MUSIC_MENU_REPEAT_ONE)
    {
        lv_roller_set_selected(obj, MUSIC_MENU_REPEAT_FOLDER, LV_ANIM_OFF);
        lv_roller_set_selected(list_item_shuffle_content_roller, MUSIC_MENU_SHUFFLE_OFF, LV_ANIM_OFF);
        ui_music_player_ctrl_repeat_by_mode(music_para, MUSIC_MENU_REPEAT_FOLDER);
    }
    else if(lv_roller_get_selected(obj) == MUSIC_MENU_REPEAT_FOLDER)
    {
        lv_roller_set_selected(obj, MUSIC_MENU_REPEAT_ALL, LV_ANIM_OFF);
        lv_roller_set_selected(list_item_shuffle_content_roller, MUSIC_MENU_SHUFFLE_OFF, LV_ANIM_OFF);
        ui_music_player_ctrl_repeat_by_mode(music_para, MUSIC_MENU_REPEAT_ALL);
    }
    else
    {
        lv_roller_set_selected(obj, MUSIC_MENU_REPEAT_ONE, LV_ANIM_OFF);
        lv_roller_set_selected(list_item_shuffle_content_roller, MUSIC_MENU_SHUFFLE_OFF, LV_ANIM_OFF);
        ui_music_player_ctrl_repeat_by_mode(music_para, MUSIC_MENU_REPEAT_ONE);
    }
#endif
#else
    lv_imagebutton_set_state(music_para->source_para->ui_top_right, LV_IMAGEBUTTON_STATE_CHECKED_RELEASED);
    //lv_obj_clear_flag(music_para->ui_func_menu_repeat, LV_OBJ_FLAG_HIDDEN);     /// Flags
    //lv_obj_add_flag(music_para->ui_func_menu_shuffle, LV_OBJ_FLAG_HIDDEN);     /// Flags
    DrawMusicRepeat(music_para);
    music_para->source_para->menu_win = SOURCE_MENU_WIN_FUNC;
    music_para->menu_win = MUSIC_MENU_WIN_FUNC_REPEAT;
#endif
}

static void ui_music_func_item_shuffle_handler(ui_music_para_t * music_para, lv_obj_t * obj, __u32 index)
{
#if 1
    lv_obj_t * list_item_repeat = lv_obj_get_child(music_para->ui_list_para.item_obj, index);
    lv_obj_t * list_item_repeat_panel = lv_obj_get_child(list_item_repeat, 0);
    lv_obj_t * list_item_repeat_content_panel = lv_obj_get_child(list_item_repeat_panel, 1);
#if 1
    lv_obj_t * list_item_repeat_content_text = lv_obj_get_child(list_item_repeat_content_panel, 0);
    //eLIBs_printf("list_item_content_roller=%p, list_item_shuffle_content_roller=%p\n", list_item_content_roller, list_item_shuffle_content_roller);

    if(music_para->random_mode == MUSIC_MENU_SHUFFLE_OFF)
    {
        lv_label_set_text(obj, "Folder");
        lv_label_set_text(list_item_repeat_content_text, "All");
        ui_music_player_ctrl_random_by_mode(music_para, MUSIC_MENU_SHUFFLE_FOLDER);
    }
    else
    {
        lv_label_set_text(obj, "Off");
        ui_music_player_ctrl_random_by_mode(music_para, MUSIC_MENU_SHUFFLE_OFF);
    }
#else
    lv_obj_t * list_item_repeat_content_roller = lv_obj_get_child(list_item_repeat_content_panel, 0);

    //eLIBs_printf("lv_roller_get_selected(list_item_content_roller)=%d\n", lv_roller_get_selected(list_item_content_roller));
    if(lv_roller_get_selected(obj) == MUSIC_MENU_SHUFFLE_OFF)
    {
        lv_roller_set_selected(obj, MUSIC_MENU_SHUFFLE_FOLDER, LV_ANIM_OFF);
        lv_roller_set_selected(list_item_repeat_content_roller, MUSIC_MENU_REPEAT_ALL, LV_ANIM_OFF);
        ui_music_player_ctrl_random_by_mode(music_para, MUSIC_MENU_SHUFFLE_FOLDER);
    }
    else
    {
        lv_roller_set_selected(obj, MUSIC_MENU_SHUFFLE_OFF, LV_ANIM_OFF);
        ui_music_player_ctrl_random_by_mode(music_para, MUSIC_MENU_SHUFFLE_OFF);
    }
#endif
#else
    lv_imagebutton_set_state(music_para->source_para->ui_top_right, LV_IMAGEBUTTON_STATE_CHECKED_RELEASED);
    //lv_obj_add_flag(music_para->ui_func_menu_repeat, LV_OBJ_FLAG_HIDDEN);     /// Flags
    //lv_obj_clear_flag(music_para->ui_func_menu_shuffle, LV_OBJ_FLAG_HIDDEN);     /// Flags
    DrawMusicRandom(music_para);
    music_para->source_para->menu_win = SOURCE_MENU_WIN_FUNC;
    music_para->menu_win = MUSIC_MENU_WIN_FUNC_SHUFFLE;
#endif
}

static void ui_music_func_item_event_handler(ui_music_para_t * music_para, lv_obj_t * obj)
{
	int32_t index = lv_obj_get_index(obj);
    lv_obj_t * list_item_panel = lv_obj_get_child(obj, 0);
    lv_obj_t * list_item_content_panel = lv_obj_get_child(list_item_panel, 1);
    lv_obj_t * list_item_content_roller = lv_obj_get_child(list_item_content_panel, 0);
    reg_aux_para_t* last_aux_para;
    
    last_aux_para = (reg_aux_para_t*)dsk_reg_get_para_by_app(REG_APP_AUX);

    //eLIBs_printf("ui_music_func_item_click index=%d\n", index);

    if(((music_para->party_work_source == PARTY_D_SOURCE_USB) || (music_para->party_work_source == PARTY_D_SOURCE_BT_AUDIO)))
    {
        switch(index)
        {
            case MUSIC_BROADCAST_MENU_FUNC_BROADCAST:
            {
                //eLIBs_printf("MUSIC_MENU_FUNC_REPEAT\n");

                music_tips_dialog_create(music_para, STRING_ROOT_CHANGE_RECEIVER_MODE, 0, 0, ADLG_YESNO);
            }
            break;
            
            case MUSIC_BROADCAST_MENU_FUNC_SOURCE:
            {
                //eLIBs_printf("MUSIC_MENU_FUNC_REPEAT\n");
                //ui_music_switch_win(music_para, MUSIC_MENU_WIN_FUNC_BROADCAST_SOURCE);
                ui_music_broadcast_source_screen_init(music_para);
            }
            break;
        
            case MUSIC_BROADCAST_MENU_FUNC_DEVICE_NAME:
            {
                //eLIBs_printf("MUSIC_MENU_FUNC_REPEAT\n");
                ui_music_switch_win(music_para, MUSIC_MENU_WIN_FUNC_BROADCAST_DEVICE_NAME);
            }
            break;

#if 0
            case MUSIC_BROADCAST_MENU_FUNC_REPEAT:
            {
                //eLIBs_printf("MUSIC_MENU_FUNC_REPEAT\n");
                 if(music_para->work_source == ID_WORK_SOURCE_USB && music_para->usb_device != USB_DEVICE_IPOD)
                    ui_music_func_item_repeat_handler(music_para, list_item_content_roller, MUSIC_BROADCAST_MENU_FUNC_SHUFFLE);
            }
            break;

            case MUSIC_BROADCAST_MENU_FUNC_SHUFFLE:
            {
                //eLIBs_printf("MUSIC_MENU_FUNC_SHUFFLE\n");
                if(music_para->work_source == ID_WORK_SOURCE_USB && music_para->usb_device != USB_DEVICE_IPOD)
                    ui_music_func_item_shuffle_handler(music_para, list_item_content_roller, MUSIC_BROADCAST_MENU_FUNC_REPEAT);
            }
            break;
#endif
        }
    }
    else
    {
        switch(index)
        {
            case MUSIC_MENU_FUNC_REPEAT:
            {
                //eLIBs_printf("MUSIC_MENU_FUNC_REPEAT\n");
                if((music_para->work_source == ID_WORK_SOURCE_USB || (music_para->party_work_source == PARTY_D_SOURCE_USB)) && music_para->root_type != RAT_USBIPOD)
                    ui_music_func_item_repeat_handler(music_para, list_item_content_roller, MUSIC_MENU_FUNC_SHUFFLE);
            }
            break;

            case MUSIC_MENU_FUNC_SHUFFLE:
            {
                //eLIBs_printf("MUSIC_MENU_FUNC_SHUFFLE\n");
                if((music_para->work_source == ID_WORK_SOURCE_USB || (music_para->party_work_source == PARTY_D_SOURCE_USB)) && music_para->root_type != RAT_USBIPOD)
                    ui_music_func_item_shuffle_handler(music_para, list_item_content_roller, MUSIC_MENU_FUNC_REPEAT);
            }
            break;
        }
    }
}

///////////////////// EVENT FUNCTIONS ////////////////////
static void ui_event_music_mode(lv_event_t * e)
{
	ui_music_para_t * music_para = lv_event_get_user_data(e);
    lv_event_code_t event_code = lv_event_get_code(e);
    lv_obj_t * target = lv_event_get_target(e);
    lv_obj_t * current_target = lv_event_get_current_target(e);
    
    if(event_code == LV_EVENT_MSG_RECEIVED)
    {
    	//uint32_t msg = *((uint32_t *)lv_event_get_param(e));
		//__music_msg("ui_event_music_mode:msg=%d\n", msg);
		__gui_msg_t * msg = (__gui_msg_t *)lv_event_get_param(e);
		//__music_msg("ui_event_music_mode:msg->id=%d\n", msg->id);
		if((lv_obj_has_flag(current_target, LV_OBJ_FLAG_HIDDEN))
			&& (music_para->mode_store == 1)
			&& (LOWORD(msg->id) != GUI_MSG_SET_APP_LAYER_SUSPEND)
			&& (LOWORD(msg->id) != GUI_MSG_SET_APP_LAYER_ON)
			&& (LOWORD(msg->id) != GUI_MSG_SET_APP_LAYER_SLEEP)
			&& (LOWORD(msg->id) != GUI_MSG_SET_APP_LAYER_OFF)
			&& (LOWORD(msg->id) != GUI_MSG_SET_APP_STORE_LAYER_SUSPEND)
			&& (LOWORD(msg->id) != GUI_MSG_SET_APP_STORE_LAYER_ON)
			&& (LOWORD(msg->id) != GUI_MSG_SET_APP_STORE_LAYER_SLEEP)
			&& (LOWORD(msg->id) != GUI_MSG_SET_APP_STORE_LAYER_OFF)
			&& (HIWORD(msg->id) != GUI_MSG_REFRESH_UNINIT)
			)
		{
			__music_msg("msg->id=0x%x, hid_flag=%d, mode_store=%d\n", msg->id, lv_obj_has_flag(current_target, LV_OBJ_FLAG_HIDDEN), music_para->mode_store);
			return;
		}
		if((music_para->mode_uninit_flag == 1) && (HIWORD(msg->id) != GUI_MSG_REFRESH_UNINIT))
		{
			eLIBs_printf("music_para->mode_uninit_flag == 1 HIWORD(msg->id)=%d!!!!!!!!!\n", HIWORD(msg->id));
			return;
		}
    	switch(LOWORD(msg->id)) 
    	{
			case GUI_MSG_SET_APP_STORE_LAYER_SLEEP:
			case GUI_MSG_SET_APP_LAYER_SUSPEND:
			{
				ui_music_layer_sleep(music_para);
			}
			break;

			case GUI_MSG_SET_APP_STORE_LAYER_ON:
			case GUI_MSG_SET_APP_LAYER_ON:
			{
				ui_music_layer_on(music_para);
			}
			break;

			case GUI_MSG_PAINT:
			{
				ui_music_refresh_handler(music_para, msg);
			}
			break;
			
			case DSK_MSG_HFP_STATUS:
			if(music_para->menu_mode == MUSIC_EXPLORER_MODE)
			{

			}
			break;
#ifdef AOA_WALLPAPER_ENTER
		case DSK_MSG_AOA_APP_CHANGE_BG:
			if(music_para->menu_mode == MUSIC_EXPLORER_MODE)
			{
				gscene_bgd_set_img_src(BG_INDEX_AUTO);
			}
			break;
#endif

			case DSK_MSG_ADB_DEVICE_PLUGOUT:
			case DSK_MSG_ADB_DEVICE_PLUGIN:
				/*if(this->menu_mode == MUSIC_EXPLORER_MODE)
				{
					DrawMusicPlayerBottomIcon(this,ID_MUSIC_FELIX_BMP);
				}*/
				break;
			case DSK_MSG_DVR_CONNECT_STATUS:
				if(music_para->menu_mode == MUSIC_EXPLORER_MODE)
				{

				}
				break;	
			case GUI_MSG_SET_MUSIC_PLAY_FROCE_MUTE:
			{
				reg_aux_para_t* para;
				para = (reg_aux_para_t*)dsk_reg_get_para_by_app(REG_APP_AUX);

				music_para->player_froce_mute = (!para->mute_status);
				__msg("music_para->player_froce_mute=%d..\n",music_para->player_froce_mute);
			}
			break;

			case GUI_MSG_MUTE_STATUS_CHANGE:
			{
				if(music_para == NULL)
					return EPDK_FAIL;
					
				if(lv_obj_has_flag(current_target, LV_OBJ_FLAG_HIDDEN))
					return EPDK_OK;

			}
			break;

			case DSK_MSG_POWER_ON_PROG:
			{
				ui_music_power_on(music_para);
			}
			break;
			
			case DSK_MSG_POWER_OFF_PROG:
			{
				ui_music_power_off(music_para);
			}
			break;

            case GUI_MSG_MUSIC_DIALOG_CONFIRM:
            {
                __music_msg("GUI_MSG_MUSIC_DIALOG_CONFIRM\n");
				//send mcu to broadcast role
				ui_party_comms_set_device_role(&music_para->source_para->party_info,PARTY_D_ROLE_RECEIVER);
                music_tips_dialog_confirm(music_para);
            }
            break;
            
            case GUI_MSG_MUSIC_DIALOG_CANCEL:
            {
                __music_msg("GUI_MSG_MUSIC_DIALOG_CANCEL\n");
                music_tips_dialog_cancel(music_para);
            }
            break;

            case DSK_MSG_HOME_ANIM_STOP:
            {                
                ui_source_sub_close(music_para->source_para);
                ui_music_source_title(music_para, NULL, 0);
                
                if(ui_music_id3_text_panel_artist_text)
                    lv_label_set_long_mode(ui_music_id3_text_panel_artist_text, LV_LABEL_LONG_SCROLL_CIRCULAR);
                
                if(ui_music_id3_text_panel_song_text)
                    lv_label_set_long_mode(ui_music_id3_text_panel_song_text, LV_LABEL_LONG_SCROLL_CIRCULAR);

                if(ui_music_id3_text_panel_album_text)
                    lv_label_set_long_mode(ui_music_id3_text_panel_album_text, LV_LABEL_LONG_SCROLL_CIRCULAR);
            }
            break;

            case GUI_MSG_UI_UPDATE:
            {
                music_player_ui_update_proc(music_para, msg);
            }
            break;

            case DSK_MSG_VOL_ENCODER_UP:
            {
                ui_music_item_group_update(music_para, 1);
            }
            break;
            
            case DSK_MSG_VOL_ENCODER_DOWN:
            {
                ui_music_item_group_update(music_para, 2);
            }
            break;

            case DSK_MSG_USB_NO_DEVICE:
            {
                if(music_para->party_work_source == PARTY_D_SOURCE_BT_AUDIO)
                    ui_party_comms_set_bc_source((void *)&music_para->source_para->party_info,PARTY_D_SOURCE_BT_AUDIO);
            }
            break;

			default:
				__music_msg("msg->id: %d\n", msg->id);
			break;
		}
    }
    else if(event_code == LV_EVENT_KEY)
    {
    	music_para->indev_data = lv_event_get_param(e);
		ui_music_key_proc(music_para);
        __music_msg("bottom_fouse_flag=%d, key=%d\n", music_para->bottom_fouse_flag, music_para->indev_data->key);
        if(!music_para->bottom_fouse_flag || (music_para->indev_data->key != GUI_MSG_KEY_UP && music_para->indev_data->key != GUI_MSG_KEY_DOWN))
        {
            ui_source_send_event(music_para->source_para, LV_EVENT_KEY, music_para->indev_data);
        }
    }
}

static void ui_event_music_mute(lv_event_t * e)
{
	ui_music_para_t * music_para = lv_event_get_user_data(e);
    lv_event_code_t event_code = lv_event_get_code(e);
    lv_obj_t * target = lv_event_get_target(e);
    lv_obj_t * current_target = lv_event_get_current_target(e);

	if(event_code == LV_EVENT_PRESSED)
    {
		if(app_root_mute_key_get_status())
		{
			app_root_msg_send_opn(GUI_MSG_BEEP_SETTING,SEND_MCU_BEEP_SETTING_TIME,0,0,0,0,0);
		}
    }
    else if(event_code == LV_EVENT_SHORT_CLICKED)
	{
		ui_music_set_mute(music_para);
	}
}

static void ui_event_music_progress(lv_event_t * e)
{
	ui_music_para_t * music_para = lv_event_get_user_data(e);
    lv_event_code_t event_code = lv_event_get_code(e);
    lv_obj_t * target = lv_event_get_target(e);
    lv_obj_t * current_target = lv_event_get_current_target(e);

    if(event_code < LV_EVENT_HOVER_LEAVE)
        __music_msg("event_code=%d\n", event_code);
    
	if(event_code == LV_EVENT_PRESSED)
	{
		app_root_msg_send_opn(GUI_MSG_BEEP_SETTING,SEND_MCU_BEEP_SETTING_TIME,0,0,0,0,0);
        __music_msg("music_slider_value=%d\n", music_slider_value);
	    //lv_slider_set_value(ui_music_play_bottom_bar, music_slider_value, LV_ANIM_OFF);
        music_slider_anim_flag = LV_ANIM_OFF;
	}
	else if(event_code == LV_EVENT_RELEASED)
	{
	    int32_t slider_value = lv_slider_get_value(current_target);
	    
		__music_msg("slider_value=%d\n", slider_value);
		music_player_ctrl_ratio_time(music_para, slider_value);
        music_slider_anim_flag = LV_ANIM_ON;
    }
}

static void ui_event_music_prve(lv_event_t * e)
{
	ui_music_para_t * music_para = lv_event_get_user_data(e);
    lv_event_code_t event_code = lv_event_get_code(e);
    lv_obj_t * target = lv_event_get_target(e);
    lv_obj_t * current_target = lv_event_get_current_target(e);
	reg_root_para_t* root_para;
    
	root_para = (reg_root_para_t*)dsk_reg_get_para_by_app(REG_APP_ROOT);

    if(event_code == LV_EVENT_PRESSED)
    {
        //ui_music_night_theme(music_para, THEME_DAY);
        //ui_music_keystripe_anim(music_para);
        app_root_msg_send_opn(GUI_MSG_BEEP_SETTING,SEND_MCU_BEEP_SETTING_TIME,0,0,0,0,0);
    }
    else if(event_code == LV_EVENT_SHORT_CLICKED)
    {
        if((music_para->work_source == ID_WORK_SOURCE_USB || (music_para->party_work_source == PARTY_D_SOURCE_USB)) && music_para->root_type != RAT_USBIPOD)
            ui_music_player_ctrl_prev_next(music_para, 0);
        else
    		app_root_send2mcu_key(KEY_TOUCH_PREV);
    }
    else if(event_code == LV_EVENT_LONG_CLICKED)
    {
        if((music_para->work_source == ID_WORK_SOURCE_USB || (music_para->party_work_source == PARTY_D_SOURCE_USB)) && music_para->root_type != RAT_USBIPOD)
            ui_music_player_ctrl_ff_rr_release(music_para, 0);
    }
    else if(event_code == LV_EVENT_LONG_PRESSED_REPEAT)
    {
        if((music_para->work_source == ID_WORK_SOURCE_USB || (music_para->party_work_source == PARTY_D_SOURCE_USB)) && music_para->root_type != RAT_USBIPOD)
            ui_music_player_ctrl_ff_rr_press(music_para, 0);
    }
}

static void ui_event_music_play_pause(lv_event_t * e)
{
	ui_music_para_t * music_para = lv_event_get_user_data(e);
    lv_event_code_t event_code = lv_event_get_code(e);
    lv_obj_t * target = lv_event_get_target(e);
    lv_obj_t * current_target = lv_event_get_current_target(e);
	reg_root_para_t* root_para;
    
	root_para = (reg_root_para_t*)dsk_reg_get_para_by_app(REG_APP_ROOT);

    if(event_code == LV_EVENT_PRESSED)
    {
        //ui_music_keystripe_anim(music_para);
        app_root_msg_send_opn(GUI_MSG_BEEP_SETTING,SEND_MCU_BEEP_SETTING_TIME,0,0,0,0,0);
    }
    else if(event_code == LV_EVENT_SHORT_CLICKED)
    {
        __music_msg("tp_enter_key\n");
        if((music_para->work_source == ID_WORK_SOURCE_USB || (music_para->party_work_source == PARTY_D_SOURCE_USB)) && music_para->root_type != RAT_USBIPOD)
            ui_music_player_ctrl_play_pause(music_para);
        else
            app_root_send2mcu_key(KEY_TOUCH_PLAYPAUSE);
    }
}

static void ui_event_music_next(lv_event_t * e)
{
	ui_music_para_t * music_para = lv_event_get_user_data(e);
    lv_event_code_t event_code = lv_event_get_code(e);
    lv_obj_t * target = lv_event_get_target(e);
    lv_obj_t * current_target = lv_event_get_current_target(e);
	reg_music_para_t *music_last_para;
	reg_bt_para_t*	para;
	reg_system_para_t *sys_para;
	reg_root_para_t* root_para;
	root_para = (reg_root_para_t*)dsk_reg_get_para_by_app(REG_APP_ROOT);
	sys_para = (reg_system_para_t*)dsk_reg_get_para_by_app(REG_APP_SYSTEM);
	para = (reg_bt_para_t*)dsk_reg_get_para_by_app(REG_APP_BT);
	music_last_para = dsk_reg_get_para_by_app(REG_APP_MUSIC);	

    if(event_code == LV_EVENT_PRESSED)
    {
        //ui_music_night_theme(music_para, THEME_NIGHT);
        //ui_music_keystripe_anim(music_para);
        app_root_msg_send_opn(GUI_MSG_BEEP_SETTING,SEND_MCU_BEEP_SETTING_TIME,0,0,0,0,0);
    }
    else if(event_code == LV_EVENT_SHORT_CLICKED)
    {
        if((music_para->work_source == ID_WORK_SOURCE_USB || (music_para->party_work_source == PARTY_D_SOURCE_USB)) && music_para->root_type != RAT_USBIPOD)
            ui_music_player_ctrl_prev_next(music_para, 1);
        else
            app_root_send2mcu_key(KEY_TOUCH_NEXT);
    }
    else if(event_code == LV_EVENT_LONG_CLICKED)
    {
        if((music_para->work_source == ID_WORK_SOURCE_USB || (music_para->party_work_source == PARTY_D_SOURCE_USB)) && music_para->root_type != RAT_USBIPOD)
            ui_music_player_ctrl_ff_rr_release(music_para, 1);
    }
    else if(event_code == LV_EVENT_LONG_PRESSED_REPEAT)
    {
        if((music_para->work_source == ID_WORK_SOURCE_USB || (music_para->party_work_source == PARTY_D_SOURCE_USB)) && music_para->root_type != RAT_USBIPOD)
            ui_music_player_ctrl_ff_rr_press(music_para, 1);
    }
}

static void ui_event_music_menu_item(lv_event_t * e)
{
	ui_music_para_t * music_para = lv_event_get_user_data(e);
    lv_event_code_t event_code = lv_event_get_code(e);
    lv_obj_t * target = lv_event_get_target(e);
    lv_obj_t * current_target = lv_event_get_current_target(e);
    lv_obj_t * list_item_panel = lv_obj_get_child(current_target, 0);
    lv_obj_t * item_icon = lv_obj_get_child(list_item_panel, 0);
    lv_obj_t * item_text_panel = lv_obj_get_child(list_item_panel, 1);
    lv_obj_t * item_shadow_text = lv_obj_get_child(item_text_panel, 0);
    lv_obj_t * item_main_text = lv_obj_get_child(item_text_panel, 1);
	__u32 index = lv_obj_get_index(current_target);
    static __u8 move_flag = 0;

	if(event_code == LV_EVENT_PRESSED)
	{		
        ui_source_menu_sel_group_update(music_para->source_para, &music_para->ui_list_para, ui_music_draw_listview_item, music_para, SOURCE_MENU_GROUP_ADD_FLAG, 0);
        move_flag = 0;
		app_root_msg_send_opn(GUI_MSG_BEEP_SETTING,SEND_MCU_BEEP_SETTING_TIME,0,0,0,0,0);
		//index += music_para->ui_list_para.page_start_id;
		//ui_music_draw_listview_item_icon(music_para, item_icon, index, 1);
		//music_para->list_scroll_flag = 1;
        ui_press_anim(music_para, list_item_panel, LV_STATE_FOCUSED, 150, 50, 100, 0);
	}
	else if(event_code == LV_EVENT_SHORT_CLICKED)
	{
        if(move_flag)
        {
            return;
        }

		//index += music_para->ui_list_para.page_start_id;
		//ui_music_draw_listview_item_icon(music_para, item_icon, index, 1);
		ui_music_item_click(current_target, music_para);
	}
	else if(event_code == LV_EVENT_LONG_PRESSED)
	{
		lv_label_set_long_mode(item_shadow_text, LV_LABEL_LONG_SCROLL_ONCE);
		lv_label_set_long_mode(item_main_text, LV_LABEL_LONG_SCROLL_ONCE);
	}
	else if(event_code == LV_EVENT_RELEASED)
	{		
		lv_label_set_long_mode(item_shadow_text, LV_LABEL_LONG_CLIP);
		lv_label_set_long_mode(item_main_text, LV_LABEL_LONG_CLIP);
		//index += music_para->ui_list_para.page_start_id;
		//ui_music_draw_listview_item_icon(music_para, item_icon, index, 0);
	}
    else if(event_code == LV_EVENT_PRESS_MOVEING)
	{		
        move_flag = 1;
	}
	else if(event_code == LV_EVENT_CLICKED)
	{		
        move_flag = 0;
	}
}

void ui_event_music_play_logo_panel(lv_event_t * e)
{
    lv_event_code_t event_code = lv_event_get_code(e);
	ui_music_para_t * music_para = lv_event_get_user_data(e);

    if(event_code == LV_EVENT_PRESSED)
	{
		app_root_msg_send_opn(GUI_MSG_BEEP_SETTING,SEND_MCU_BEEP_SETTING_TIME,0,0,0,0,0);
	}
	else if(event_code == LV_EVENT_SHORT_CLICKED) 
    {
        ui_music_source_event_handler(music_para);
	}
}

void ui_event_music_top_left(lv_event_t * e)
{
    lv_event_code_t event_code = lv_event_get_code(e);
	ui_music_para_t * music_para = lv_event_get_user_data(e);

    if(event_code == LV_EVENT_PRESSED)
	{
		app_root_msg_send_opn(GUI_MSG_BEEP_SETTING,SEND_MCU_BEEP_SETTING_TIME,0,0,0,0,0);
	}
	else if(event_code == LV_EVENT_SHORT_CLICKED) 
    {
        ui_music_top_left_event_handler(music_para);
	}
}

void ui_event_music_top_right(lv_event_t * e)
{
    lv_event_code_t event_code = lv_event_get_code(e);
	ui_music_para_t * music_para = lv_event_get_user_data(e);

    if(event_code == LV_EVENT_PRESSED)
	{
		app_root_msg_send_opn(GUI_MSG_BEEP_SETTING,SEND_MCU_BEEP_SETTING_TIME,0,0,0,0,0);
	}
	else if(event_code == LV_EVENT_SHORT_CLICKED) 
    {
        ui_music_top_right_event_handler(music_para);
	}
}

void ui_event_music_open_bar(lv_event_t * e)
{
    lv_event_code_t event_code = lv_event_get_code(e);
	ui_music_para_t * music_para = lv_event_get_user_data(e);

    if(event_code == LV_EVENT_PRESSED)
	{
		app_root_msg_send_opn(GUI_MSG_BEEP_SETTING,SEND_MCU_BEEP_SETTING_TIME,0,0,0,0,0);
	}
	else if(event_code == LV_EVENT_SHORT_CLICKED) 
    {
        //ui_music_open_bar_menu_event_handler(music_para);
        ui_music_switch_win(music_para, MUSIC_MENU_WIN_FUNC);
	}
}

void ui_event_music_top_menu(lv_event_t * e)
{
    lv_event_code_t event_code = lv_event_get_code(e);
	ui_music_para_t * music_para = lv_event_get_user_data(e);

    if(event_code == LV_EVENT_PRESSED)
	{
		app_root_msg_send_opn(GUI_MSG_BEEP_SETTING,SEND_MCU_BEEP_SETTING_TIME,0,0,0,0,0);
	}
	else if(event_code == LV_EVENT_SHORT_CLICKED) 
    {
        if(music_para->menu_win > MUSIC_MENU_WIN_NONE 
        && music_para->menu_win <= MUSIC_MENU_WIN_FUNC 
        && music_para->source_para->menu_group_flag == SOURCE_MENU_GROUP_ADD 
        && music_para->source_para->menu_win != SOURCE_MENU_WIN_MODE)
        {
            ui_source_menu_sel_group_update(music_para->source_para, &music_para->ui_list_para, ui_music_draw_listview_item, music_para, SOURCE_MENU_GROUP_REMOVE_SUB, 0);
        }
        ui_music_top_menu_event_handler(music_para);
        ui_source_menu_sel_group_update(music_para->source_para, &music_para->ui_list_para, ui_music_draw_listview_item, music_para, SOURCE_MENU_GROUP_ADD_FLAG, 0);
        ui_music_item_group_update(music_para, 0);
    }
}

void ui_event_music_bottom_menu(lv_event_t * e)
{
    lv_event_code_t event_code = lv_event_get_code(e);
	ui_music_para_t * music_para = lv_event_get_user_data(e);

    if(event_code == LV_EVENT_PRESSED)
	{
		app_root_msg_send_opn(GUI_MSG_BEEP_SETTING,SEND_MCU_BEEP_SETTING_TIME,0,0,0,0,0);
	}
	else if(event_code == LV_EVENT_SHORT_CLICKED) 
    {
        if(music_para->menu_win > MUSIC_MENU_WIN_NONE 
        && music_para->menu_win <= MUSIC_MENU_WIN_FUNC 
        && music_para->source_para->menu_group_flag == SOURCE_MENU_GROUP_ADD 
        && music_para->source_para->menu_win != SOURCE_MENU_WIN_MODE)
        {
            ui_source_menu_sel_group_update(music_para->source_para, &music_para->ui_list_para, ui_music_draw_listview_item, music_para, SOURCE_MENU_GROUP_REMOVE_SUB, 0);
        }
        ui_music_bottom_menu_event_handler(music_para);
        ui_source_menu_sel_group_update(music_para->source_para, &music_para->ui_list_para, ui_music_draw_listview_item, music_para, SOURCE_MENU_GROUP_ADD, 0);
	}
}

static void ui_event_music_func_item(lv_event_t * e)
{
	ui_music_para_t * music_para = lv_event_get_user_data(e);
    lv_event_code_t event_code = lv_event_get_code(e);
    lv_obj_t * target = lv_event_get_target(e);
    lv_obj_t * current_target = lv_event_get_current_target(e);
    lv_obj_t * list_item_panel = lv_obj_get_child(current_target, 0);
    static __u8 move_flag = 0;

	if(event_code == LV_EVENT_PRESSED)
	{		
        ui_source_menu_sel_group_update(music_para->source_para, &music_para->ui_list_para, ui_music_draw_listview_item, music_para, SOURCE_MENU_GROUP_ADD, 0);
		app_root_msg_send_opn(GUI_MSG_BEEP_SETTING,SEND_MCU_BEEP_SETTING_TIME,0,0,0,0,0);
        ui_press_anim(music_para, list_item_panel, LV_STATE_FOCUSED, 150, 50, 100, 0);
	}
	else if(event_code == LV_EVENT_SHORT_CLICKED)
	{
        if(move_flag)
        {
            return;
        }
        ui_music_func_item_event_handler(music_para, current_target);
	}
	else if(event_code == LV_EVENT_LONG_PRESSED)
	{
	}
	else if(event_code == LV_EVENT_RELEASED)
	{		
	}
    else if(event_code == LV_EVENT_PRESS_MOVEING)
	{		
        move_flag = 1;
	}
	else if(event_code == LV_EVENT_CLICKED)
	{		
        move_flag = 0;
	}
}

static void ui_event_music_func_broadcast_source_cb(lv_event_t * e)
{
    lv_event_code_t event_code = lv_event_get_code(e);
	ui_music_para_t * music_para = lv_event_get_user_data(e);
    lv_obj_t * obj = lv_event_get_target(e);
    static int32_t selected = -1;

    //eLIBs_printf("event_code=%d\n", event_code);
    if(event_code == LV_EVENT_SHORT_CLICKED) 
    {
#if 1
        uint32_t cur_selected = lv_obj_get_index(obj);

        eLIBs_printf("cur_selected=%d\n", cur_selected);
        ui_music_func_broadcast_source_event_handler(music_para, cur_selected);
#else
        uint32_t cur_selected = lv_roller_get_selected(obj);

        if(selected != cur_selected)
        {
            selected = cur_selected;
        }
        else
        {
            //eLIBs_printf("ui_event_music_repeat_roller selected=%d\n", selected);
    		ui_music_func_broadcast_source_event_handler(music_para, selected);
            selected = -1;
        }
#endif
    }
}

static void ui_event_music_func_kb_event_cb(lv_event_t* e)
{
	ui_music_para_t * music_para = lv_event_get_user_data(e);
    lv_event_code_t code = lv_event_get_code(e);
    lv_obj_t* kb = lv_event_get_target(e);
    lv_obj_t* ta = lv_keyboard_get_textarea(kb);
    const char* text;

    if(code == LV_EVENT_READY || code == LV_EVENT_CANCEL) 
    {
        text = lv_textarea_get_text(ta);
        if(text != NULL)
            eLIBs_strcpy(music_para->source_para->party_info.own_info.name, text);
        
        ui_music_switch_win(music_para, MUSIC_MENU_WIN_FUNC);
    }
}

static void ui_event_music_func_repeat_roller(lv_event_t * e)
{
    lv_event_code_t event_code = lv_event_get_code(e);
	ui_music_para_t * music_para = lv_event_get_user_data(e);
    lv_obj_t * obj = lv_event_get_target(e);
    
    if(event_code == LV_EVENT_VALUE_CHANGED) 
    {
        uint32_t selected = lv_roller_get_selected(obj);
        
        //eLIBs_printf("ui_event_music_repeat_roller selected=%d\n", selected);
        ui_music_player_ctrl_repeat_by_mode(music_para, selected);
    }
}

static void ui_event_music_func_shuffle_roller(lv_event_t * e)
{
    lv_event_code_t event_code = lv_event_get_code(e);
	ui_music_para_t * music_para = lv_event_get_user_data(e);
    lv_obj_t * obj = lv_event_get_target(e);
    
    if(event_code == LV_EVENT_VALUE_CHANGED) 
    {
        uint32_t selected = lv_roller_get_selected(obj);

        //eLIBs_printf("ui_event_music_shuffle_roller selected=%d\n", selected);
        ui_music_player_ctrl_random_by_mode(music_para, selected);
    }
}

void ui_event_music_bottom_list(lv_event_t * e)
{
    lv_event_code_t event_code = lv_event_get_code(e);
	ui_music_para_t * music_para = lv_event_get_user_data(e);

    if(event_code == LV_EVENT_PRESSED)
	{
		app_root_msg_send_opn(GUI_MSG_BEEP_SETTING,SEND_MCU_BEEP_SETTING_TIME,0,0,0,0,0);
	}
	else if(event_code == LV_EVENT_SHORT_CLICKED) 
    {
        ui_music_switch_win(music_para, MUSIC_MENU_WIN_LIST);
	}
}

void ui_event_music_bottom_browse(lv_event_t * e)
{
    lv_event_code_t event_code = lv_event_get_code(e);
	ui_music_para_t * music_para = lv_event_get_user_data(e);

    if(event_code == LV_EVENT_PRESSED)
	{
		app_root_msg_send_opn(GUI_MSG_BEEP_SETTING,SEND_MCU_BEEP_SETTING_TIME,0,0,0,0,0);
	}
	else if(event_code == LV_EVENT_SHORT_CLICKED) 
    {
        ui_music_switch_win(music_para, MUSIC_MENU_WIN_BROWSE);
	}
}

void ui_event_music_bottom_repeat(lv_event_t * e)
{
    lv_event_code_t event_code = lv_event_get_code(e);
	ui_music_para_t * music_para = lv_event_get_user_data(e);

    if(event_code == LV_EVENT_PRESSED)
	{
		app_root_msg_send_opn(GUI_MSG_BEEP_SETTING,SEND_MCU_BEEP_SETTING_TIME,0,0,0,0,0);
	}
	else if(event_code == LV_EVENT_SHORT_CLICKED) 
    {
		ui_music_player_ctrl_repeat(music_para);
	}
}

void ui_event_music_bottom_random(lv_event_t * e)
{
    lv_event_code_t event_code = lv_event_get_code(e);
	ui_music_para_t * music_para = lv_event_get_user_data(e);

    if(event_code == LV_EVENT_PRESSED)
	{
		app_root_msg_send_opn(GUI_MSG_BEEP_SETTING,SEND_MCU_BEEP_SETTING_TIME,0,0,0,0,0);
	}
	else if(event_code == LV_EVENT_SHORT_CLICKED) 
    {
		ui_music_player_ctrl_random(music_para);
	}
}

static void ui_event_music_timer_cb(lv_timer_t * t)
{	
	ui_music_para_t * music_para = lv_timer_get_user_data(t);

    if((music_para->work_source == ID_WORK_SOURCE_USB || (music_para->party_work_source == PARTY_D_SOURCE_USB)) && (music_para->root_type != RAT_USBIPOD || music_para->usb_device == USB_DEVICE_LOCAL))
    {
    	music_player_win_on_timer(music_para);
    }
    else// if(music_para->work_source == ID_WORK_SOURCE_BT)
    {
        music_player_ui_update_timer_proc(music_para);
    }
}

///////////////////// SCREENS ////////////////////
static void ui_music_menu_screen_init(ui_music_para_t * music_para)
{
#if 1
    if(ui_music_menu)
    {
        lv_obj_remove_flag(ui_music_menu, LV_OBJ_FLAG_HIDDEN);     /// Flags
        return;
    }

    ui_music_menu = lv_obj_create(music_para->source_para->ui_menu_panel);
    lv_obj_remove_style_all(ui_music_menu);
    lv_obj_set_width(ui_music_menu, lv_pct(100));
    lv_obj_set_height(ui_music_menu, lv_pct(100));
    //lv_obj_add_flag(ui_music_menu, LV_OBJ_FLAG_HIDDEN);     /// Flags
    lv_obj_clear_flag(ui_music_menu, LV_OBJ_FLAG_SCROLLABLE);      /// Flags
    lv_obj_set_style_bg_color(ui_music_menu, lv_color_hex(0x000000), LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_bg_opa(ui_music_menu, 255, LV_PART_MAIN | LV_STATE_DEFAULT);
#else
    if(ui_music_menu == NULL || lv_obj_is_valid(ui_music_menu))
    {
        ui_music_menu = lv_obj_create(music_para->source_para->ui_rig_list_panel);//ui_fm
        lv_obj_remove_style_all(ui_music_menu);
        lv_obj_set_width(ui_music_menu, lv_pct(100));
        lv_obj_set_height(ui_music_menu, lv_pct(100));
        //lv_obj_add_flag(ui_music_menu, LV_OBJ_FLAG_HIDDEN);     /// Flags
        lv_obj_clear_flag(ui_music_menu, LV_OBJ_FLAG_SCROLLABLE);      /// Flags
        lv_obj_set_style_bg_color(ui_music_menu, lv_color_hex(0x000000), LV_PART_MAIN | LV_STATE_DEFAULT);
        lv_obj_set_style_bg_opa(ui_music_menu, 0, LV_PART_MAIN | LV_STATE_DEFAULT);
    }
#endif
}

static void ui_music_menu_screen_uninit(ui_music_para_t * music_para)
{
    if(ui_music_menu)
    {
        lv_obj_add_flag(ui_music_menu, LV_OBJ_FLAG_HIDDEN);     /// Flags
    }
}

static void ui_music_broadcast_source_screen_init(ui_music_para_t * music_para)
{
    __u8 i;
    lv_obj_t *ui_source;
    lv_obj_t *ui_source_img;
    lv_obj_t *ui_source_img_s;

    if(music_para->broadcast_source_flag)
    {
        return;
    }
    
    music_para->broadcast_source_flag = 1;
        
    if(music_para->ui_func_broadcast_select_source == NULL)
    {
#if 1
        music_para->ui_func_broadcast_select_source = lv_obj_create(ui_source_top_dialog);
        lv_obj_remove_style_all(music_para->ui_func_broadcast_select_source);
        lv_obj_set_width(music_para->ui_func_broadcast_select_source, lv_pct(100));
        lv_obj_set_height(music_para->ui_func_broadcast_select_source, lv_pct(86));
        lv_obj_set_x(music_para->ui_func_broadcast_select_source, lv_pct(0));
        lv_obj_set_y(music_para->ui_func_broadcast_select_source, lv_pct(14));
        lv_obj_remove_flag(music_para->ui_func_broadcast_select_source, LV_OBJ_FLAG_SCROLLABLE);      /// Flags
        lv_obj_set_style_bg_color(music_para->ui_func_broadcast_select_source, lv_color_hex(0x000000), LV_PART_MAIN | LV_STATE_DEFAULT);
        lv_obj_set_style_bg_opa(music_para->ui_func_broadcast_select_source, 0, LV_PART_MAIN | LV_STATE_DEFAULT);

        music_para->ui_func_broadcast_select_source_blur_panel = lv_obj_create(music_para->ui_func_broadcast_select_source);
        lv_obj_remove_style_all(music_para->ui_func_broadcast_select_source_blur_panel);
        lv_obj_set_width(music_para->ui_func_broadcast_select_source_blur_panel, lv_pct(100));
        lv_obj_set_height(music_para->ui_func_broadcast_select_source_blur_panel, lv_pct(100));
        lv_obj_remove_flag(music_para->ui_func_broadcast_select_source_blur_panel, LV_OBJ_FLAG_SCROLLABLE);      /// Flags
        lv_obj_set_style_bg_color(music_para->ui_func_broadcast_select_source_blur_panel, lv_color_hex(0x000000), LV_PART_MAIN | LV_STATE_DEFAULT);
        lv_obj_set_style_bg_opa(music_para->ui_func_broadcast_select_source_blur_panel, 0, LV_PART_MAIN | LV_STATE_DEFAULT);

        music_para->ui_func_broadcast_select_source_panel = lv_obj_create(music_para->ui_func_broadcast_select_source);
        lv_obj_remove_style_all(music_para->ui_func_broadcast_select_source_panel);
        lv_obj_set_width(music_para->ui_func_broadcast_select_source_panel, lv_pct(100));
        lv_obj_set_height(music_para->ui_func_broadcast_select_source_panel, lv_pct(100));
        lv_obj_set_flex_flow(music_para->ui_func_broadcast_select_source_panel, LV_FLEX_FLOW_ROW_WRAP);
        lv_obj_set_flex_align(music_para->ui_func_broadcast_select_source_panel, LV_FLEX_ALIGN_SPACE_EVENLY, LV_FLEX_ALIGN_START, LV_FLEX_ALIGN_START);
        lv_obj_remove_flag(music_para->ui_func_broadcast_select_source_panel, LV_OBJ_FLAG_SCROLLABLE);      /// Flags
        lv_obj_set_style_bg_color(music_para->ui_func_broadcast_select_source_panel, lv_color_hex(0x000000), LV_PART_MAIN | LV_STATE_DEFAULT);
        lv_obj_set_style_bg_opa(music_para->ui_func_broadcast_select_source_panel, 0, LV_PART_MAIN | LV_STATE_DEFAULT);
        
        ui_source_sel_screen_init(music_para, music_para->ui_func_broadcast_select_source_panel, ui_music_get_res, MUSIC_TEMP_SOU_USB_BMP, MUSIC_TEMP_SOU_P_L_BMP, MUSIC_TEMP_SOU_S_BMP, &source_sel_map[ID_SOURCE_USB], 0);
        ui_source_sel_screen_init(music_para, music_para->ui_func_broadcast_select_source_panel, ui_music_get_res, MUSIC_TEMP_SOU_BT_BMP, MUSIC_TEMP_SOU_P_L_BMP, MUSIC_TEMP_SOU_S_BMP, &source_sel_map[ID_SOURCE_BT], 0);
        ui_source_sel_screen_init(music_para, music_para->ui_func_broadcast_select_source_panel, ui_music_get_res, MUSIC_TEMP_SOU_AUXIN_BMP, MUSIC_TEMP_SOU_P_L_BMP, MUSIC_TEMP_SOU_S_BMP, &source_sel_map[ID_SOURCE_AUX], 0);

        lv_obj_add_event_cb(music_para->ui_func_broadcast_select_source_panel, ui_event_music_func_broadcast_source_cb, LV_EVENT_ALL, music_para);

        for (i = 0; i < lv_obj_get_child_cnt(music_para->ui_func_broadcast_select_source_panel); i++) 
        {
            ui_source = lv_obj_get_child(music_para->ui_func_broadcast_select_source_panel, i);
            ui_source_img = lv_obj_get_child(ui_source, 0);
            ui_source_img_s = lv_obj_get_child(ui_source_img, 0);

            if((i == 0) && (music_para->source_para->party_info.bc_info.source == PARTY_D_SOURCE_USB))
            {
                lv_obj_remove_flag(ui_source_img_s, LV_OBJ_FLAG_HIDDEN);
                //lv_image_set_src(ui_source_img, ui_music_get_res(music_para, MUSIC_TEMP_SOU_S_BMP));
                //lv_imagebutton_set_state(ui_source_img, LV_IMAGEBUTTON_STATE_CHECKED_RELEASED);
            }
            else if((i == 1) && (music_para->source_para->party_info.bc_info.source == PARTY_D_SOURCE_BT_AUDIO))
            {
                lv_obj_remove_flag(ui_source_img_s, LV_OBJ_FLAG_HIDDEN);
                //lv_image_set_src(ui_source_img, ui_music_get_res(music_para, MUSIC_TEMP_SOU_S_BMP));
                //lv_imagebutton_set_state(ui_source_img, LV_IMAGEBUTTON_STATE_CHECKED_RELEASED);
            }
            else if((i == 2) && (music_para->source_para->party_info.bc_info.source == PARTY_D_SOURCE_AUXIN))
            {
                lv_obj_remove_flag(ui_source_img_s, LV_OBJ_FLAG_HIDDEN);
                //lv_image_set_src(ui_source_img, ui_music_get_res(music_para, MUSIC_TEMP_SOU_S_BMP));
                //lv_imagebutton_set_state(ui_source_img, LV_IMAGEBUTTON_STATE_CHECKED_RELEASED);
            }
            else
            {
                lv_obj_add_flag(ui_source_img_s, LV_OBJ_FLAG_HIDDEN);
                //lv_image_set_src(ui_source_img, NULL);
                //lv_imagebutton_set_state(ui_source_img, LV_IMAGEBUTTON_STATE_RELEASED);
            }
        }
#else
        music_para->ui_func_broadcast_select_source = lv_obj_create(music_para->source_para->ui_rig);
        lv_obj_remove_style_all(music_para->ui_func_broadcast_select_source);
        lv_obj_set_width(music_para->ui_func_broadcast_select_source, lv_pct(100));
        lv_obj_set_height(music_para->ui_func_broadcast_select_source, lv_pct(100));
        lv_obj_add_flag(music_para->ui_func_broadcast_select_source, LV_OBJ_FLAG_HIDDEN);     /// Flags
        lv_obj_clear_flag(music_para->ui_func_broadcast_select_source, LV_OBJ_FLAG_SCROLLABLE);      /// Flags
        lv_obj_set_style_bg_color(music_para->ui_func_broadcast_select_source, lv_color_hex(0x000000), LV_PART_MAIN | LV_STATE_DEFAULT);
        lv_obj_set_style_bg_opa(music_para->ui_func_broadcast_select_source, 240, LV_PART_MAIN | LV_STATE_DEFAULT);
        lv_obj_set_style_bg_grad_color(music_para->ui_func_broadcast_select_source, lv_color_hex(0x202020), LV_PART_MAIN | LV_STATE_DEFAULT);
        lv_obj_set_style_bg_grad_dir(music_para->ui_func_broadcast_select_source, LV_GRAD_DIR_VER, LV_PART_MAIN | LV_STATE_DEFAULT);

        music_para->ui_func_broadcast_select_source_roller = lv_roller_create(music_para->ui_func_broadcast_select_source);
        lv_roller_set_options(music_para->ui_func_broadcast_select_source_roller, "USB\nBluetooth\nAUX IN", LV_ROLLER_MODE_NORMAL);
        lv_obj_set_width(music_para->ui_func_broadcast_select_source_roller, lv_pct(70));
        lv_obj_set_height(music_para->ui_func_broadcast_select_source_roller, lv_pct(65));
        lv_obj_set_align(music_para->ui_func_broadcast_select_source_roller, LV_ALIGN_CENTER);
        lv_obj_set_style_text_color(music_para->ui_func_broadcast_select_source_roller, lv_color_hex(0x808080), LV_PART_MAIN | LV_STATE_DEFAULT);
        lv_obj_set_style_text_opa(music_para->ui_func_broadcast_select_source_roller, 255, LV_PART_MAIN | LV_STATE_DEFAULT);
        lv_obj_set_style_text_font(music_para->ui_func_broadcast_select_source_roller, lv_font_xlarge.font, LV_PART_MAIN | LV_STATE_DEFAULT);
        lv_obj_set_style_bg_color(music_para->ui_func_broadcast_select_source_roller, lv_color_hex(0x898989), LV_PART_MAIN | LV_STATE_DEFAULT);
        lv_obj_set_style_bg_opa(music_para->ui_func_broadcast_select_source_roller, 0, LV_PART_MAIN | LV_STATE_DEFAULT);
        lv_obj_set_style_border_width(music_para->ui_func_broadcast_select_source_roller, 0, LV_PART_MAIN | LV_STATE_DEFAULT);
        lv_obj_set_style_radius(music_para->ui_func_broadcast_select_source_roller, 10, LV_PART_SELECTED | LV_STATE_DEFAULT);
        lv_obj_set_style_bg_color(music_para->ui_func_broadcast_select_source_roller, lv_color_hex(0x252525), LV_PART_SELECTED | LV_STATE_DEFAULT);
        lv_obj_set_style_bg_opa(music_para->ui_func_broadcast_select_source_roller, 255, LV_PART_SELECTED | LV_STATE_DEFAULT);

        
        lv_obj_add_event_cb(music_para->ui_func_broadcast_select_source_roller, ui_event_music_func_broadcast_source_cb, LV_EVENT_ALL, music_para);
#endif
    }
    else
    {
        lv_obj_clear_flag(music_para->ui_func_broadcast_select_source, LV_OBJ_FLAG_HIDDEN);     /// Flags
    }
    
    //ui_source_sub_open(music_para->source_para);

    //add broadcast source group
    if((music_para->source_para->party_info.bc_info.source == PARTY_D_SOURCE_USB))
        ui_source_sel_group_update(music_para->ui_func_broadcast_select_source_panel, 0, 1);
    else if((music_para->source_para->party_info.bc_info.source == PARTY_D_SOURCE_BT_AUDIO))
        ui_source_sel_group_update(music_para->ui_func_broadcast_select_source_panel, 1, 1);
    else
        ui_source_sel_group_update(music_para->ui_func_broadcast_select_source_panel, 2, 1);

    //lv_group_focus_obj(music_para->ui_func_broadcast_select_source_roller);
    //lv_group_set_editing(lv_group_get_default(), true);

    //remove menu list group
    ui_source_menu_group_update(music_para->source_para, &music_para->ui_list_para, ui_music_draw_listview_item, music_para, SOURCE_MENU_GROUP_REMOVE_SUB, 0);

    if(music_para->ui_func_broadcast_select_source_blur == NULL)
        music_para->ui_func_broadcast_select_source_blur = ui_canvas_blur_init(music_para->ui_func_broadcast_select_source_blur_panel, music_para->source_para->ui_menu_panel, 20);
}

static void ui_music_broadcast_source_screen_uninit(ui_music_para_t * music_para)
{
    if(music_para->broadcast_source_flag == 0)
    {
        return;
    }

    //add menu list group
    ui_music_item_group_update(music_para, 1);
    
    music_para->broadcast_source_flag = 0;
    
    if(music_para->ui_func_broadcast_select_source)
    {
        //ui_source_sub_close(music_para->source_para);
        ui_source_sel_group_update(music_para->ui_func_broadcast_select_source, 0, 0);
        lv_obj_add_flag(music_para->ui_func_broadcast_select_source, LV_OBJ_FLAG_HIDDEN);     /// Flags

        //delete blur
        ui_canvas_blur_uninit(music_para->ui_func_broadcast_select_source_blur);
        music_para->ui_func_broadcast_select_source_blur = NULL;
    }
}

static void ui_music_func_menu_screen_init(ui_music_para_t * music_para)
{
#if 1
    if(music_para->ui_func_menu)
    {
        lv_obj_remove_flag(music_para->ui_func_menu, LV_OBJ_FLAG_HIDDEN);     /// Flags
        return;
    }

    music_para->ui_func_menu = lv_obj_create(music_para->source_para->ui_menu_panel);
    lv_obj_remove_style_all(music_para->ui_func_menu);
    lv_obj_set_width(music_para->ui_func_menu, lv_pct(100));
    lv_obj_set_height(music_para->ui_func_menu, lv_pct(100));
    //lv_obj_add_flag(music_para->ui_func_menu, LV_OBJ_FLAG_HIDDEN);     /// Flags
    lv_obj_clear_flag(music_para->ui_func_menu, LV_OBJ_FLAG_SCROLLABLE);      /// Flags
    lv_obj_set_style_bg_color(music_para->ui_func_menu, lv_color_hex(0x000000), LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_bg_opa(music_para->ui_func_menu, 255, LV_PART_MAIN | LV_STATE_DEFAULT);
    
    music_para->ui_keyboard_panel = lv_obj_create(music_para->source_para->ui_source);
    lv_obj_remove_style_all(music_para->ui_keyboard_panel);
    lv_obj_set_width(music_para->ui_keyboard_panel, lv_pct(100));
    lv_obj_set_height(music_para->ui_keyboard_panel, lv_pct(100));
    lv_obj_add_flag(music_para->ui_keyboard_panel, LV_OBJ_FLAG_HIDDEN);      /// Flags
    lv_obj_clear_flag(music_para->ui_keyboard_panel, LV_OBJ_FLAG_SCROLLABLE);      /// Flags
    lv_obj_set_style_bg_color(music_para->ui_keyboard_panel, lv_color_hex(0x000000), LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_bg_opa(music_para->ui_keyboard_panel, 255, LV_PART_MAIN | LV_STATE_DEFAULT);
    
    music_para->ui_keyboar = ui_keyboard_init(music_para->ui_keyboard_panel);
    lv_group_focus_obj(music_para->ui_keyboar);
    lv_group_set_editing(lv_group_get_default(), true);
    
    lv_obj_add_event_cb(music_para->ui_keyboar, ui_event_music_func_kb_event_cb, LV_EVENT_ALL, music_para);
#else
    if(music_para->ui_func_menu == NULL || !lv_obj_is_valid(music_para->ui_func_menu))
    {        
        music_para->ui_func_menu = lv_obj_create(music_para->source_para->ui_rig_func_panel);
        lv_obj_remove_style_all(music_para->ui_func_menu);
        lv_obj_set_width(music_para->ui_func_menu, lv_pct(100));
        lv_obj_set_height(music_para->ui_func_menu, lv_pct(100));
        //lv_obj_add_flag(music_para->ui_func_menu, LV_OBJ_FLAG_HIDDEN);     /// Flags
        lv_obj_clear_flag(music_para->ui_func_menu, LV_OBJ_FLAG_SCROLLABLE);      /// Flags
        lv_obj_set_style_bg_color(music_para->ui_func_menu, lv_color_hex(0x000000), LV_PART_MAIN | LV_STATE_DEFAULT);
        lv_obj_set_style_bg_opa(music_para->ui_func_menu, 0, LV_PART_MAIN | LV_STATE_DEFAULT);

        music_para->ui_keyboard_panel = lv_obj_create(music_para->source_para->ui_source);
        lv_obj_remove_style_all(music_para->ui_keyboard_panel);
        lv_obj_set_width(music_para->ui_keyboard_panel, lv_pct(100));
        lv_obj_set_height(music_para->ui_keyboard_panel, lv_pct(100));
        lv_obj_add_flag(music_para->ui_keyboard_panel, LV_OBJ_FLAG_HIDDEN);      /// Flags
        lv_obj_clear_flag(music_para->ui_keyboard_panel, LV_OBJ_FLAG_SCROLLABLE);      /// Flags
        lv_obj_set_style_bg_color(music_para->ui_keyboard_panel, lv_color_hex(0x000000), LV_PART_MAIN | LV_STATE_DEFAULT);
        lv_obj_set_style_bg_opa(music_para->ui_keyboard_panel, 255, LV_PART_MAIN | LV_STATE_DEFAULT);
        
        music_para->ui_keyboar = ui_keyboard_init(music_para->ui_keyboard_panel);
        lv_group_focus_obj(music_para->ui_keyboar);
        lv_group_set_editing(lv_group_get_default(), true);

        lv_obj_add_event_cb(music_para->ui_keyboar, ui_event_music_func_kb_event_cb, LV_EVENT_ALL, music_para);
#if 0
        music_para->ui_func_menu_repeat = lv_obj_create(music_para->source_para->ui_rig);
        lv_obj_remove_style_all(music_para->ui_func_menu_repeat);
        lv_obj_set_width(music_para->ui_func_menu_repeat, lv_pct(100));
        lv_obj_set_height(music_para->ui_func_menu_repeat, lv_pct(100));
        lv_obj_add_flag(music_para->ui_func_menu_repeat, LV_OBJ_FLAG_HIDDEN | LV_OBJ_FLAG_IGNORE_LAYOUT);     /// Flags
        lv_obj_clear_flag(music_para->ui_func_menu_repeat, LV_OBJ_FLAG_SCROLLABLE);      /// Flags
        lv_obj_set_style_bg_color(music_para->ui_func_menu_repeat, lv_color_hex(0x000000), LV_PART_MAIN | LV_STATE_DEFAULT);
        lv_obj_set_style_bg_opa(music_para->ui_func_menu_repeat, 240, LV_PART_MAIN | LV_STATE_DEFAULT);
        lv_obj_set_style_bg_grad_color(music_para->ui_func_menu_repeat, lv_color_hex(0x202020), LV_PART_MAIN | LV_STATE_DEFAULT);
        lv_obj_set_style_bg_grad_dir(music_para->ui_func_menu_repeat, LV_GRAD_DIR_VER, LV_PART_MAIN | LV_STATE_DEFAULT);

        music_para->ui_func_menu_repeat_roller = lv_roller_create(music_para->ui_func_menu_repeat);
        lv_roller_set_options(music_para->ui_func_menu_repeat_roller, "One\nFolder\nAll", LV_ROLLER_MODE_INFINITE);
        lv_obj_set_height(music_para->ui_func_menu_repeat_roller, lv_pct(65));
        lv_obj_set_width(music_para->ui_func_menu_repeat_roller, lv_pct(70));
        lv_obj_set_align(music_para->ui_func_menu_repeat_roller, LV_ALIGN_CENTER);
        lv_obj_set_style_text_color(music_para->ui_func_menu_repeat_roller, lv_color_hex(0x808080), LV_PART_MAIN | LV_STATE_DEFAULT);
        lv_obj_set_style_text_opa(music_para->ui_func_menu_repeat_roller, 255, LV_PART_MAIN | LV_STATE_DEFAULT);
        lv_obj_set_style_text_font(music_para->ui_func_menu_repeat_roller, lv_font_xxlarge.font, LV_PART_MAIN | LV_STATE_DEFAULT);
        lv_obj_set_style_bg_color(music_para->ui_func_menu_repeat_roller, lv_color_hex(0xFFFFFF), LV_PART_MAIN | LV_STATE_DEFAULT);
        lv_obj_set_style_bg_opa(music_para->ui_func_menu_repeat_roller, 0, LV_PART_MAIN | LV_STATE_DEFAULT);
        lv_obj_set_style_border_width(music_para->ui_func_menu_repeat_roller, 0, LV_PART_MAIN | LV_STATE_DEFAULT);        
        lv_obj_set_style_radius(music_para->ui_func_menu_repeat_roller, 5, LV_PART_SELECTED | LV_STATE_DEFAULT);
        lv_obj_set_style_bg_color(music_para->ui_func_menu_repeat_roller, lv_color_hex(0x252525), LV_PART_SELECTED | LV_STATE_DEFAULT);
        lv_obj_set_style_bg_opa(music_para->ui_func_menu_repeat_roller, 255, LV_PART_SELECTED | LV_STATE_DEFAULT);
        
        music_para->ui_func_menu_shuffle = lv_obj_create(music_para->source_para->ui_rig);
        lv_obj_remove_style_all(music_para->ui_func_menu_shuffle);
        lv_obj_set_width(music_para->ui_func_menu_shuffle, lv_pct(100));
        lv_obj_set_height(music_para->ui_func_menu_shuffle, lv_pct(100));
        lv_obj_add_flag(music_para->ui_func_menu_shuffle, LV_OBJ_FLAG_HIDDEN | LV_OBJ_FLAG_IGNORE_LAYOUT);     /// Flags
        lv_obj_clear_flag(music_para->ui_func_menu_shuffle, LV_OBJ_FLAG_SCROLLABLE);      /// Flags
        lv_obj_set_style_bg_color(music_para->ui_func_menu_shuffle, lv_color_hex(0x000000), LV_PART_MAIN | LV_STATE_DEFAULT);
        lv_obj_set_style_bg_opa(music_para->ui_func_menu_shuffle, 240, LV_PART_MAIN | LV_STATE_DEFAULT);
        lv_obj_set_style_bg_grad_color(music_para->ui_func_menu_shuffle, lv_color_hex(0x202020), LV_PART_MAIN | LV_STATE_DEFAULT);
        lv_obj_set_style_bg_grad_dir(music_para->ui_func_menu_shuffle, LV_GRAD_DIR_VER, LV_PART_MAIN | LV_STATE_DEFAULT);

        music_para->ui_func_menu_shuffle_roller = lv_roller_create(music_para->ui_func_menu_shuffle);
        lv_roller_set_options(music_para->ui_func_menu_shuffle_roller, "Off\nFolder", LV_ROLLER_MODE_NORMAL);
        lv_obj_set_height(music_para->ui_func_menu_shuffle_roller, lv_pct(65));
        lv_obj_set_width(music_para->ui_func_menu_shuffle_roller, lv_pct(70));
        lv_obj_set_align(music_para->ui_func_menu_shuffle_roller, LV_ALIGN_CENTER);
        lv_obj_remove_flag(music_para->ui_func_menu_shuffle, LV_OBJ_FLAG_SCROLL_ELASTIC | LV_OBJ_FLAG_CLICK_FOCUSABLE);      /// Flags
        lv_obj_set_style_text_color(music_para->ui_func_menu_shuffle_roller, lv_color_hex(0x808080), LV_PART_MAIN | LV_STATE_DEFAULT);
        lv_obj_set_style_text_opa(music_para->ui_func_menu_shuffle_roller, 255, LV_PART_MAIN | LV_STATE_DEFAULT);
        lv_obj_set_style_text_font(music_para->ui_func_menu_shuffle_roller, lv_font_xxlarge.font, LV_PART_MAIN | LV_STATE_DEFAULT);
        lv_obj_set_style_bg_color(music_para->ui_func_menu_shuffle_roller, lv_color_hex(0xFFFFFF), LV_PART_MAIN | LV_STATE_DEFAULT);
        lv_obj_set_style_bg_opa(music_para->ui_func_menu_shuffle_roller, 0, LV_PART_MAIN | LV_STATE_DEFAULT);
        lv_obj_set_style_border_width(music_para->ui_func_menu_shuffle_roller, 0, LV_PART_MAIN | LV_STATE_DEFAULT);
        lv_obj_set_style_radius(music_para->ui_func_menu_shuffle_roller, 5, LV_PART_SELECTED | LV_STATE_DEFAULT);
        lv_obj_set_style_bg_color(music_para->ui_func_menu_shuffle_roller, lv_color_hex(0x252525), LV_PART_SELECTED | LV_STATE_DEFAULT);
        lv_obj_set_style_bg_opa(music_para->ui_func_menu_shuffle_roller, 255, LV_PART_SELECTED | LV_STATE_DEFAULT);

        lv_obj_add_event_cb(music_para->ui_func_menu_repeat_roller, ui_event_music_func_repeat_roller, LV_EVENT_ALL, music_para);
        lv_obj_add_event_cb(music_para->ui_func_menu_shuffle_roller, ui_event_music_func_shuffle_roller, LV_EVENT_ALL, music_para);
#endif
    }
#endif
}

static void ui_music_func_menu_screen_uninit(ui_music_para_t * music_para)
{
    if(music_para->ui_func_menu)
    {
        lv_obj_add_flag(music_para->ui_func_menu, LV_OBJ_FLAG_HIDDEN);     /// Flags
    }
}

static void ui_music_screen_init(ui_music_para_t * music_para)
{
    ui_music = lv_obj_create(music_para->source_para->ui_play);//ui_source
	lv_obj_set_width(ui_music, lv_pct(100));
	lv_obj_set_height(ui_music, lv_pct(100));
	lv_obj_set_align(ui_music, LV_ALIGN_CENTER);
	//lv_obj_add_flag(ui_music, LV_OBJ_FLAG_HIDDEN);	 /// Flags
	lv_obj_clear_flag(ui_music, LV_OBJ_FLAG_SCROLLABLE);		/// Flags
	lv_obj_set_style_radius(ui_music, 0, LV_PART_MAIN | LV_STATE_DEFAULT);
	lv_obj_set_style_bg_color(ui_music, lv_color_hex(0x171717), LV_PART_MAIN | LV_STATE_DEFAULT);
	lv_obj_set_style_bg_opa(ui_music, 0, LV_PART_MAIN | LV_STATE_DEFAULT);
	lv_obj_set_style_bg_grad_color(ui_music, lv_color_hex(0x000000), LV_PART_MAIN | LV_STATE_DEFAULT);
	lv_obj_set_style_bg_grad_dir(ui_music, LV_GRAD_DIR_VER, LV_PART_MAIN | LV_STATE_DEFAULT);
    //lv_obj_set_style_bg_image_src(ui_music, ui_music_get_res(music_para, MUSIC_THEME_TEMP_COM_BG_BMP), LV_PART_MAIN | LV_STATE_DEFAULT);
	lv_obj_set_style_border_width(ui_music, 0, LV_PART_MAIN | LV_STATE_DEFAULT);
	lv_obj_set_style_pad_left(ui_music, 0, LV_PART_MAIN | LV_STATE_DEFAULT);
	lv_obj_set_style_pad_right(ui_music, 0, LV_PART_MAIN | LV_STATE_DEFAULT);
	lv_obj_set_style_pad_top(ui_music, 0, LV_PART_MAIN | LV_STATE_DEFAULT);
	lv_obj_set_style_pad_bottom(ui_music, 0, LV_PART_MAIN | LV_STATE_DEFAULT);
	lv_obj_set_style_pad_row(ui_music, 0, LV_PART_MAIN | LV_STATE_DEFAULT);
	lv_obj_set_style_pad_column(ui_music, 0, LV_PART_MAIN | LV_STATE_DEFAULT);

    ui_music_play_screen = lv_obj_create(ui_music);
    lv_obj_remove_style_all(ui_music_play_screen);
    lv_obj_set_width(ui_music_play_screen, lv_pct(100));
    lv_obj_set_height(ui_music_play_screen, lv_pct(100));
    lv_obj_clear_flag(ui_music_play_screen, LV_OBJ_FLAG_SCROLLABLE);      /// Flags
	lv_obj_set_style_bg_opa(ui_music_play_screen, 0, LV_PART_MAIN | LV_STATE_DEFAULT);

	ui_music_bg = lv_img_create(ui_music_play_screen);
	lv_obj_set_width(ui_music_bg, lv_pct(90));
	lv_obj_set_height(ui_music_bg, lv_pct(100));
	lv_obj_set_align(ui_music_bg, LV_ALIGN_TOP_LEFT);
	lv_obj_add_flag(ui_music_bg, LV_OBJ_FLAG_HIDDEN);	 /// Flags
	lv_obj_clear_flag(ui_music_bg, LV_OBJ_FLAG_SCROLLABLE);	  /// Flags
	lv_obj_set_style_bg_color(ui_music_bg, lv_color_hex(0x202020), LV_PART_MAIN | LV_STATE_DEFAULT);
	lv_obj_set_style_bg_opa(ui_music_bg, 255, LV_PART_MAIN | LV_STATE_DEFAULT);
	lv_obj_set_style_bg_grad_color(ui_music_bg, lv_color_hex(0x000000), LV_PART_MAIN | LV_STATE_DEFAULT);
	lv_obj_set_style_bg_grad_dir(ui_music_bg, LV_GRAD_DIR_HOR, LV_PART_MAIN | LV_STATE_DEFAULT);

    ui_music_bg_fg = lv_obj_create(ui_music_bg);
    //lv_obj_remove_style_all(ui_music_bg_fg);
    lv_obj_set_width(ui_music_bg_fg, lv_pct(100));
    lv_obj_set_height(ui_music_bg_fg, lv_pct(100));
    lv_obj_add_flag(ui_music_bg_fg, LV_OBJ_FLAG_HIDDEN);     /// Flags
    lv_obj_clear_flag(ui_music_bg_fg, LV_OBJ_FLAG_SCROLLABLE);      /// Flags
	//lv_obj_set_style_bg_color(ui_music_bg_fg, lv_color_hex(0x000000), LV_PART_MAIN | LV_STATE_DEFAULT);
	//lv_obj_set_style_bg_opa(ui_music_bg_fg, 220, LV_PART_MAIN | LV_STATE_DEFAULT);
	//lv_obj_set_style_bg_grad_color(ui_music_bg_fg, lv_color_hex(0x000000), LV_PART_MAIN | LV_STATE_DEFAULT);
	//lv_obj_set_style_bg_grad_dir(ui_music_bg_fg, LV_GRAD_DIR_HOR, LV_PART_MAIN | LV_STATE_DEFAULT);
    // 创建渐变描述符
    static lv_grad_dsc_t grad;
    grad.dir = LV_GRAD_DIR_HOR;  // 水平方向渐变
    grad.stops_count = 2;        // 两个渐变点

    // 设置左边颜色(完全透明)
    grad.stops[0].color = lv_color_black();
    grad.stops[0].opa = LV_OPA_TRANSP;  // 0
    grad.stops[0].frac = 0;            // 起始位置

    // 设置右边颜色(完全不透明)
    grad.stops[1].color = lv_color_black();
    grad.stops[1].opa = LV_OPA_COVER; // 255
    grad.stops[1].frac = 255;          // 结束位置

    // 应用渐变
    lv_obj_set_style_bg_grad(ui_music_bg_fg, &grad, 0);
    lv_obj_set_style_bg_opa(ui_music_bg_fg, LV_OPA_COVER, 0);
    lv_obj_set_style_border_width(ui_music_bg_fg, 0, 0);
	lv_obj_set_style_radius(ui_music_bg_fg, 0, LV_PART_MAIN | LV_STATE_DEFAULT);

	ui_music_play = lv_obj_create(ui_music_play_screen);
    lv_obj_set_width(ui_music_play, lv_pct(100));
    lv_obj_set_height(ui_music_play, lv_pct(100));//70
    //lv_obj_set_x(ui_music_play, 0);
    //lv_obj_set_y(ui_music_play, lv_pct(14));
	lv_obj_clear_flag(ui_music_play, LV_OBJ_FLAG_SCROLLABLE); 	 /// Flags
	lv_obj_set_style_radius(ui_music_play, 0, LV_PART_MAIN | LV_STATE_DEFAULT);
	lv_obj_set_style_bg_color(ui_music_play, lv_color_hex(0x000000), LV_PART_MAIN | LV_STATE_DEFAULT);
	lv_obj_set_style_bg_opa(ui_music_play, 0, LV_PART_MAIN | LV_STATE_DEFAULT);
	lv_obj_set_style_border_width(ui_music_play, 0, LV_PART_MAIN | LV_STATE_DEFAULT);
	lv_obj_set_style_pad_left(ui_music_play, 0, LV_PART_MAIN | LV_STATE_DEFAULT);
	lv_obj_set_style_pad_right(ui_music_play, 0, LV_PART_MAIN | LV_STATE_DEFAULT);
	lv_obj_set_style_pad_top(ui_music_play, 0, LV_PART_MAIN | LV_STATE_DEFAULT);
	lv_obj_set_style_pad_bottom(ui_music_play, 0, LV_PART_MAIN | LV_STATE_DEFAULT);
	lv_obj_set_style_pad_row(ui_music_play, 0, LV_PART_MAIN | LV_STATE_DEFAULT);
	lv_obj_set_style_pad_column(ui_music_play, 0, LV_PART_MAIN | LV_STATE_DEFAULT);

    ui_music_play_broadcast_bg = lv_image_create(ui_music_play);
    lv_img_set_src(ui_music_play_broadcast_bg, ui_music_get_res(music_para, MUSIC_DAY_NIG_PM_BC_BG_BMP));
    lv_obj_set_width(ui_music_play_broadcast_bg, LV_SIZE_CONTENT);   /// 214
    lv_obj_set_height(ui_music_play_broadcast_bg, LV_SIZE_CONTENT);    /// 214
    lv_obj_set_align(ui_music_play_broadcast_bg, LV_ALIGN_LEFT_MID);
    lv_obj_clear_flag(ui_music_play_broadcast_bg, LV_OBJ_FLAG_SCROLLABLE);      /// Flags
    if((music_para->party_work_source != PARTY_D_SOURCE_USB) && (music_para->party_work_source != PARTY_D_SOURCE_BT_AUDIO))
        lv_obj_add_flag(ui_music_play_broadcast_bg, LV_OBJ_FLAG_HIDDEN);     /// Flags

	ui_music_play_top = lv_obj_create(ui_music_play);
	lv_obj_set_width(ui_music_play_top, lv_pct(88));
	lv_obj_set_height(ui_music_play_top, lv_pct(20));
    lv_obj_set_flex_flow(ui_music_play_top, LV_FLEX_FLOW_ROW);
    lv_obj_set_flex_align(ui_music_play_top, LV_FLEX_ALIGN_END, LV_FLEX_ALIGN_CENTER, LV_FLEX_ALIGN_CENTER);
    lv_obj_remove_flag(ui_music_play_top, LV_OBJ_FLAG_SCROLLABLE);      /// Flags
    lv_obj_set_style_radius(ui_music_play_top, 0, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_bg_color(ui_music_play_top, lv_color_hex(0xFFFFFF), LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_bg_opa(ui_music_play_top, 0, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_border_width(ui_music_play_top, 0, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_pad_left(ui_music_play_top, 0, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_pad_right(ui_music_play_top, 0, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_pad_top(ui_music_play_top, 0, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_pad_bottom(ui_music_play_top, 0, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_pad_row(ui_music_play_top, 0, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_pad_column(ui_music_play_top, 20, LV_PART_MAIN | LV_STATE_DEFAULT);

    ui_music_play_top_low = lv_image_create(ui_music_play_top);
    lv_image_set_src(ui_music_play_top_low, ui_music_get_res(music_para, MUSIC_THEME_TEMP_COM_LOW_VOLTAGE_BMP));
    lv_obj_set_width(ui_music_play_top_low, LV_SIZE_CONTENT);   /// 1
    lv_obj_set_height(ui_music_play_top_low, LV_SIZE_CONTENT);    /// 1
    lv_obj_set_align(ui_music_play_top_low, LV_ALIGN_CENTER);
    lv_obj_add_flag(ui_music_play_top_low, LV_OBJ_FLAG_HIDDEN | LV_OBJ_FLAG_CLICKABLE);     /// Flags
    lv_obj_remove_flag(ui_music_play_top_low, LV_OBJ_FLAG_SCROLLABLE);      /// Flags

    ui_music_play_top_charge = lv_image_create(ui_music_play_top);
    lv_image_set_src(ui_music_play_top_charge, ui_music_get_res(music_para, MUSIC_THEME_TEMP_COM_PD_CHARGE_BMP));
    lv_obj_set_width(ui_music_play_top_charge, LV_SIZE_CONTENT);   /// 1
    lv_obj_set_height(ui_music_play_top_charge, LV_SIZE_CONTENT);    /// 1
    lv_obj_set_align(ui_music_play_top_charge, LV_ALIGN_CENTER);
    lv_obj_add_flag(ui_music_play_top_charge, LV_OBJ_FLAG_HIDDEN | LV_OBJ_FLAG_CLICKABLE);     /// Flags
    lv_obj_remove_flag(ui_music_play_top_charge, LV_OBJ_FLAG_SCROLLABLE);      /// Flags

    ui_music_play_top_eq = lv_image_create(ui_music_play_top);
    lv_image_set_src(ui_music_play_top_eq, ui_music_get_res(music_para, MUSIC_THEME_TEMP_COM_PUNCH_EQ_BMP));
    lv_obj_set_width(ui_music_play_top_eq, LV_SIZE_CONTENT);   /// 1
    lv_obj_set_height(ui_music_play_top_eq, LV_SIZE_CONTENT);    /// 1
    lv_obj_set_align(ui_music_play_top_eq, LV_ALIGN_CENTER);
    lv_obj_add_flag(ui_music_play_top_eq, LV_OBJ_FLAG_CLICKABLE);     /// Flags
    lv_obj_remove_flag(ui_music_play_top_eq, LV_OBJ_FLAG_SCROLLABLE);      /// Flags

    ui_music_play_mid = lv_obj_create(ui_music_play);
    lv_obj_set_width(ui_music_play_mid, lv_pct(92));//lv_pct(88)
    lv_obj_set_height(ui_music_play_mid, lv_pct(60));
    //lv_obj_set_x(ui_music_play_mid, lv_pct(0));
    //lv_obj_set_y(ui_music_play_mid, lv_pct(20));
    lv_obj_set_align(ui_music_play_mid, LV_ALIGN_LEFT_MID);
    lv_obj_remove_flag(ui_music_play_mid, LV_OBJ_FLAG_SCROLLABLE);      /// Flags
    lv_obj_set_style_radius(ui_music_play_mid, 0, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_bg_color(ui_music_play_mid, lv_color_hex(0xFFFFFF), LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_bg_opa(ui_music_play_mid, 0, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_border_width(ui_music_play_mid, 0, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_pad_left(ui_music_play_mid, 0, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_pad_right(ui_music_play_mid, 0, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_pad_top(ui_music_play_mid, 0, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_pad_bottom(ui_music_play_mid, 0, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_pad_row(ui_music_play_mid, 0, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_pad_column(ui_music_play_mid, 0, LV_PART_MAIN | LV_STATE_DEFAULT);

    ui_music_play_logo_panel = lv_obj_create(ui_music_play);
    lv_obj_set_width(ui_music_play_logo_panel, lv_pct(35));//LV_SIZE_CONTENT
    lv_obj_set_height(ui_music_play_logo_panel, lv_pct(100));//LV_SIZE_CONTENT
    lv_obj_set_y(ui_music_play_logo_panel, -6);
	lv_obj_set_align(ui_music_play_logo_panel, LV_ALIGN_LEFT_MID);
    lv_obj_remove_flag(ui_music_play_logo_panel, LV_OBJ_FLAG_SCROLLABLE);      /// Flags
    lv_obj_set_style_radius(ui_music_play_logo_panel, 0, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_bg_color(ui_music_play_logo_panel, lv_color_hex(0xFFFFFF), LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_bg_opa(ui_music_play_logo_panel, 0, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_border_width(ui_music_play_logo_panel, 0, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_pad_left(ui_music_play_logo_panel, 0, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_pad_right(ui_music_play_logo_panel, 0, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_pad_top(ui_music_play_logo_panel, 0, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_pad_bottom(ui_music_play_logo_panel, 0, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_pad_row(ui_music_play_logo_panel, 0, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_pad_column(ui_music_play_logo_panel, 0, LV_PART_MAIN | LV_STATE_DEFAULT);

	ui_music_play_logo = lv_img_create(ui_music_play_logo_panel);
	lv_img_set_src(ui_music_play_logo, ui_music_get_res(music_para, MUSIC_THEME_USB_ICON_BMP));
	lv_obj_set_width(ui_music_play_logo, LV_SIZE_CONTENT);   /// 214
	lv_obj_set_height(ui_music_play_logo, LV_SIZE_CONTENT);	 /// 214
    lv_obj_set_x(ui_music_play_logo, lv_pct(2));
    lv_obj_set_y(ui_music_play_logo, lv_pct(0));
	lv_obj_set_align(ui_music_play_logo, LV_ALIGN_CENTER);//LV_ALIGN_LEFT_MID
	lv_obj_add_flag(ui_music_play_logo, LV_OBJ_FLAG_ADV_HITTEST); 	/// Flags
	lv_obj_clear_flag(ui_music_play_logo, LV_OBJ_FLAG_SCROLLABLE);	  /// Flags

	ui_music_play_logo_text = lv_label_create(ui_music_play_logo);
	lv_obj_set_width(ui_music_play_logo_text, LV_SIZE_CONTENT);	/// 1
	lv_obj_set_height(ui_music_play_logo_text, LV_SIZE_CONTENT);	  /// 1
    lv_obj_set_y(ui_music_play_logo_text, lv_pct(-10));
    lv_obj_set_align(ui_music_play_logo_text, LV_ALIGN_BOTTOM_MID);
    //lv_obj_add_flag(ui_music_play_logo_text, LV_OBJ_FLAG_HIDDEN);     /// Flags
	//lv_label_set_text(ui_music_play_logo_text, "USB");
	lv_obj_set_style_text_color(ui_music_play_logo_text, lv_color_hex(0xFFFFFF), LV_PART_MAIN | LV_STATE_DEFAULT);
	lv_obj_set_style_text_opa(ui_music_play_logo_text, 255, LV_PART_MAIN | LV_STATE_DEFAULT);
	lv_obj_set_style_text_font(ui_music_play_logo_text, lv_font_small.font, LV_PART_MAIN | LV_STATE_DEFAULT);

    ui_music_logo_panel = lv_obj_create(ui_music_play_logo_panel);
    lv_obj_remove_style_all(ui_music_logo_panel);
	lv_obj_set_width(ui_music_logo_panel, 182);   //186 // 214 //220
	lv_obj_set_height(ui_music_logo_panel, 182);	 //186 // 214 //220
    lv_obj_set_x(ui_music_logo_panel, lv_pct(2));
	lv_obj_set_align(ui_music_logo_panel, LV_ALIGN_CENTER);
    lv_obj_remove_flag(ui_music_logo_panel, LV_OBJ_FLAG_CLICKABLE | LV_OBJ_FLAG_SCROLLABLE);      /// Flags
	lv_obj_set_style_radius(ui_music_logo_panel, 20, LV_PART_MAIN | LV_STATE_DEFAULT);//LV_RADIUS_CIRCLE
	lv_obj_set_style_clip_corner(ui_music_logo_panel, true, 0);
    lv_obj_set_style_bg_color(ui_music_logo_panel, lv_color_hex(0xFFFFFF), LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_bg_opa(ui_music_logo_panel, 0, LV_PART_MAIN | LV_STATE_DEFAULT);

	ui_music_logo = lv_img_create(ui_music_logo_panel);
	lv_obj_set_width(ui_music_logo, lv_pct(100));   /// 214 //220
	lv_obj_set_height(ui_music_logo, lv_pct(100));	 /// 214 //220
	//lv_obj_set_x(ui_music_logo, lv_pct(1));
	//lv_obj_set_y(ui_music_logo, lv_pct(0));
	//lv_obj_set_align(ui_music_logo, LV_ALIGN_LEFT_MID);
	lv_obj_set_align(ui_music_logo, LV_ALIGN_CENTER);
	//lv_obj_set_style_radius(ui_music_logo, 20, LV_PART_MAIN | LV_STATE_DEFAULT);//LV_RADIUS_CIRCLE
	//lv_obj_set_style_clip_corner(ui_music_logo, true, 0);
	//lv_obj_add_flag(ui_music_logo, LV_OBJ_FLAG_HIDDEN); 	/// Flags
	lv_obj_clear_flag(ui_music_logo, LV_OBJ_FLAG_SCROLLABLE);	  /// Flags

    ui_music_play_disp = lv_obj_create(ui_music_play_mid);
    lv_obj_remove_style_all(ui_music_play_disp);
    lv_obj_set_width(ui_music_play_disp, lv_pct(60));
    lv_obj_set_height(ui_music_play_disp, lv_pct(100));
    lv_obj_set_align(ui_music_play_disp, LV_ALIGN_RIGHT_MID);
    lv_obj_remove_flag(ui_music_play_disp, LV_OBJ_FLAG_SCROLLABLE);      /// Flags
    lv_obj_set_style_bg_color(ui_music_play_disp, lv_color_hex(0xFFFFFF), LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_bg_opa(ui_music_play_disp, 0, LV_PART_MAIN | LV_STATE_DEFAULT);

    ui_music_id3 = lv_obj_create(ui_music_play_disp);
    lv_obj_set_width(ui_music_id3, lv_pct(100));
    lv_obj_set_height(ui_music_id3, lv_pct(100));
    lv_obj_set_align(ui_music_id3, LV_ALIGN_RIGHT_MID);
    lv_obj_remove_flag(ui_music_id3, LV_OBJ_FLAG_SCROLLABLE);      /// Flags
    lv_obj_set_style_radius(ui_music_id3, 0, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_bg_color(ui_music_id3, lv_color_hex(0x000000), LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_bg_opa(ui_music_id3, 0, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_border_width(ui_music_id3, 0, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_pad_left(ui_music_id3, 0, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_pad_right(ui_music_id3, 0, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_pad_top(ui_music_id3, 0, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_pad_bottom(ui_music_id3, 0, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_pad_row(ui_music_id3, 0, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_pad_column(ui_music_id3, 0, LV_PART_MAIN | LV_STATE_DEFAULT);

	ui_music_id3_text = lv_obj_create(ui_music_id3);
	lv_obj_set_width(ui_music_id3_text, lv_pct(100));
	lv_obj_set_height(ui_music_id3_text, lv_pct(90));
	lv_obj_clear_flag(ui_music_id3_text, LV_OBJ_FLAG_SCROLLABLE); 	 /// Flags
	lv_obj_set_style_radius(ui_music_id3_text, 0, LV_PART_MAIN | LV_STATE_DEFAULT);
	lv_obj_set_style_bg_color(ui_music_id3_text, lv_color_hex(0x000000), LV_PART_MAIN | LV_STATE_DEFAULT);
	lv_obj_set_style_bg_opa(ui_music_id3_text, 0, LV_PART_MAIN | LV_STATE_DEFAULT);
	lv_obj_set_style_border_width(ui_music_id3_text, 0, LV_PART_MAIN | LV_STATE_DEFAULT);
	lv_obj_set_style_pad_left(ui_music_id3_text, 0, LV_PART_MAIN | LV_STATE_DEFAULT);
	lv_obj_set_style_pad_right(ui_music_id3_text, 0, LV_PART_MAIN | LV_STATE_DEFAULT);
	lv_obj_set_style_pad_top(ui_music_id3_text, 0, LV_PART_MAIN | LV_STATE_DEFAULT);
	lv_obj_set_style_pad_bottom(ui_music_id3_text, 0, LV_PART_MAIN | LV_STATE_DEFAULT);
	lv_obj_set_style_pad_row(ui_music_id3_text, 0, LV_PART_MAIN | LV_STATE_DEFAULT);
	lv_obj_set_style_pad_column(ui_music_id3_text, 0, LV_PART_MAIN | LV_STATE_DEFAULT);

	ui_music_id3_text_panel = lv_obj_create(ui_music_id3_text);
	lv_obj_set_width(ui_music_id3_text_panel, lv_pct(100));
	lv_obj_set_height(ui_music_id3_text_panel, lv_pct(100));
	lv_obj_set_flex_flow(ui_music_id3_text_panel, LV_FLEX_FLOW_COLUMN);
	lv_obj_set_flex_align(ui_music_id3_text_panel, LV_FLEX_ALIGN_SPACE_BETWEEN, LV_FLEX_ALIGN_CENTER, LV_FLEX_ALIGN_CENTER);
	lv_obj_clear_flag(ui_music_id3_text_panel, LV_OBJ_FLAG_SCROLLABLE);	   /// Flags
	lv_obj_set_style_radius(ui_music_id3_text_panel, 0, LV_PART_MAIN | LV_STATE_DEFAULT);
	lv_obj_set_style_bg_color(ui_music_id3_text_panel, lv_color_hex(0x000000), LV_PART_MAIN | LV_STATE_DEFAULT);
	lv_obj_set_style_bg_opa(ui_music_id3_text_panel, 0, LV_PART_MAIN | LV_STATE_DEFAULT);
	lv_obj_set_style_border_width(ui_music_id3_text_panel, 0, LV_PART_MAIN | LV_STATE_DEFAULT);
	lv_obj_set_style_pad_left(ui_music_id3_text_panel, 0, LV_PART_MAIN | LV_STATE_DEFAULT);
	lv_obj_set_style_pad_right(ui_music_id3_text_panel, 0, LV_PART_MAIN | LV_STATE_DEFAULT);
	lv_obj_set_style_pad_top(ui_music_id3_text_panel, 0, LV_PART_MAIN | LV_STATE_DEFAULT);
	lv_obj_set_style_pad_bottom(ui_music_id3_text_panel, 0, LV_PART_MAIN | LV_STATE_DEFAULT);
	lv_obj_set_style_pad_row(ui_music_id3_text_panel, 0, LV_PART_MAIN | LV_STATE_DEFAULT);
	lv_obj_set_style_pad_column(ui_music_id3_text_panel, 0, LV_PART_MAIN | LV_STATE_DEFAULT);

	ui_music_id3_text_panel_album = lv_obj_create(ui_music_id3_text_panel);
	lv_obj_set_width(ui_music_id3_text_panel_album, lv_pct(100));
	lv_obj_set_height(ui_music_id3_text_panel_album, LV_SIZE_CONTENT);	/// 30
	lv_obj_clear_flag(ui_music_id3_text_panel_album, LV_OBJ_FLAG_SCROLLABLE); 	 /// Flags
	lv_obj_set_style_radius(ui_music_id3_text_panel_album, 0, LV_PART_MAIN | LV_STATE_DEFAULT);
	lv_obj_set_style_bg_color(ui_music_id3_text_panel_album, lv_color_hex(0x000000), LV_PART_MAIN | LV_STATE_DEFAULT);
	lv_obj_set_style_bg_opa(ui_music_id3_text_panel_album, 0, LV_PART_MAIN | LV_STATE_DEFAULT);
	lv_obj_set_style_border_width(ui_music_id3_text_panel_album, 0, LV_PART_MAIN | LV_STATE_DEFAULT);
	lv_obj_set_style_pad_left(ui_music_id3_text_panel_album, 0, LV_PART_MAIN | LV_STATE_DEFAULT);
	lv_obj_set_style_pad_right(ui_music_id3_text_panel_album, 0, LV_PART_MAIN | LV_STATE_DEFAULT);
	lv_obj_set_style_pad_top(ui_music_id3_text_panel_album, 0, LV_PART_MAIN | LV_STATE_DEFAULT);
	lv_obj_set_style_pad_bottom(ui_music_id3_text_panel_album, 0, LV_PART_MAIN | LV_STATE_DEFAULT);
	lv_obj_set_style_pad_row(ui_music_id3_text_panel_album, 0, LV_PART_MAIN | LV_STATE_DEFAULT);
	lv_obj_set_style_pad_column(ui_music_id3_text_panel_album, 0, LV_PART_MAIN | LV_STATE_DEFAULT);

	ui_music_id3_text_panel_album_text = lv_label_create(ui_music_id3_text_panel_album);
	lv_obj_set_width(ui_music_id3_text_panel_album_text, lv_pct(100));   /// 1
	lv_obj_set_height(ui_music_id3_text_panel_album_text, LV_SIZE_CONTENT);	 /// 1
    lv_obj_add_flag(ui_music_id3_text_panel_album_text, LV_OBJ_FLAG_CLICKABLE);
    lv_label_set_long_mode(ui_music_id3_text_panel_album_text, LV_LABEL_LONG_SCROLL_CIRCULAR);
	lv_label_set_text(ui_music_id3_text_panel_album_text, "Unknown Album");
	lv_obj_set_style_text_color(ui_music_id3_text_panel_album_text, lv_color_hex(0xCCCCCC), LV_PART_MAIN | LV_STATE_DEFAULT);
	lv_obj_set_style_text_opa(ui_music_id3_text_panel_album_text, 255, LV_PART_MAIN | LV_STATE_DEFAULT);
	lv_obj_set_style_text_font(ui_music_id3_text_panel_album_text, lv_font_medium.font, LV_PART_MAIN | LV_STATE_DEFAULT);

	ui_music_id3_text_panel_song = lv_obj_create(ui_music_id3_text_panel);
	lv_obj_set_width(ui_music_id3_text_panel_song, lv_pct(100));
	lv_obj_set_height(ui_music_id3_text_panel_song, LV_SIZE_CONTENT);    /// 30
	lv_obj_clear_flag(ui_music_id3_text_panel_song, LV_OBJ_FLAG_SCROLLABLE);		/// Flags
	lv_obj_set_style_radius(ui_music_id3_text_panel_song, 0, LV_PART_MAIN | LV_STATE_DEFAULT);
	lv_obj_set_style_bg_color(ui_music_id3_text_panel_song, lv_color_hex(0x000000), LV_PART_MAIN | LV_STATE_DEFAULT);
	lv_obj_set_style_bg_opa(ui_music_id3_text_panel_song, 0, LV_PART_MAIN | LV_STATE_DEFAULT);
	lv_obj_set_style_border_width(ui_music_id3_text_panel_song, 0, LV_PART_MAIN | LV_STATE_DEFAULT);
	lv_obj_set_style_pad_left(ui_music_id3_text_panel_song, 0, LV_PART_MAIN | LV_STATE_DEFAULT);
	lv_obj_set_style_pad_right(ui_music_id3_text_panel_song, 0, LV_PART_MAIN | LV_STATE_DEFAULT);
	lv_obj_set_style_pad_top(ui_music_id3_text_panel_song, 0, LV_PART_MAIN | LV_STATE_DEFAULT);
	lv_obj_set_style_pad_bottom(ui_music_id3_text_panel_song, 0, LV_PART_MAIN | LV_STATE_DEFAULT);
	lv_obj_set_style_pad_row(ui_music_id3_text_panel_song, 0, LV_PART_MAIN | LV_STATE_DEFAULT);
	lv_obj_set_style_pad_column(ui_music_id3_text_panel_song, 0, LV_PART_MAIN | LV_STATE_DEFAULT);

	ui_music_id3_text_panel_song_text = lv_label_create(ui_music_id3_text_panel_song);
	lv_obj_set_width(ui_music_id3_text_panel_song_text, lv_pct(100));   /// 1
	lv_obj_set_height(ui_music_id3_text_panel_song_text, LV_SIZE_CONTENT);	/// 1
    lv_obj_add_flag(ui_music_id3_text_panel_song_text, LV_OBJ_FLAG_CLICKABLE);
    lv_label_set_long_mode(ui_music_id3_text_panel_song_text, LV_LABEL_LONG_SCROLL_CIRCULAR);
	lv_label_set_text(ui_music_id3_text_panel_song_text, "Unknown Title");
	lv_obj_set_style_text_color(ui_music_id3_text_panel_song_text, lv_color_hex(0xFFFFFF), LV_PART_MAIN | LV_STATE_DEFAULT);
	lv_obj_set_style_text_opa(ui_music_id3_text_panel_song_text, 255, LV_PART_MAIN | LV_STATE_DEFAULT);
	lv_obj_set_style_text_font(ui_music_id3_text_panel_song_text, lv_font_large.font, LV_PART_MAIN | LV_STATE_DEFAULT);

	ui_music_id3_text_panel_artist = lv_obj_create(ui_music_id3_text_panel);
	lv_obj_set_width(ui_music_id3_text_panel_artist, lv_pct(100));
	lv_obj_set_height(ui_music_id3_text_panel_artist, LV_SIZE_CONTENT);	 /// 30
	lv_obj_clear_flag(ui_music_id3_text_panel_artist, LV_OBJ_FLAG_SCROLLABLE);	  /// Flags
	lv_obj_set_style_radius(ui_music_id3_text_panel_artist, 0, LV_PART_MAIN | LV_STATE_DEFAULT);
	lv_obj_set_style_bg_color(ui_music_id3_text_panel_artist, lv_color_hex(0x000000), LV_PART_MAIN | LV_STATE_DEFAULT);
	lv_obj_set_style_bg_opa(ui_music_id3_text_panel_artist, 0, LV_PART_MAIN | LV_STATE_DEFAULT);
	lv_obj_set_style_border_width(ui_music_id3_text_panel_artist, 0, LV_PART_MAIN | LV_STATE_DEFAULT);
	lv_obj_set_style_pad_left(ui_music_id3_text_panel_artist, 0, LV_PART_MAIN | LV_STATE_DEFAULT);
	lv_obj_set_style_pad_right(ui_music_id3_text_panel_artist, 0, LV_PART_MAIN | LV_STATE_DEFAULT);
	lv_obj_set_style_pad_top(ui_music_id3_text_panel_artist, 0, LV_PART_MAIN | LV_STATE_DEFAULT);
	lv_obj_set_style_pad_bottom(ui_music_id3_text_panel_artist, 0, LV_PART_MAIN | LV_STATE_DEFAULT);
	lv_obj_set_style_pad_row(ui_music_id3_text_panel_artist, 0, LV_PART_MAIN | LV_STATE_DEFAULT);
	lv_obj_set_style_pad_column(ui_music_id3_text_panel_artist, 0, LV_PART_MAIN | LV_STATE_DEFAULT);

	ui_music_id3_text_panel_artist_text = lv_label_create(ui_music_id3_text_panel_artist);
	lv_obj_set_width(ui_music_id3_text_panel_artist_text, lv_pct(100));	/// 1
	lv_obj_set_height(ui_music_id3_text_panel_artist_text, LV_SIZE_CONTENT);	  /// 1
    lv_obj_add_flag(ui_music_id3_text_panel_artist_text, LV_OBJ_FLAG_CLICKABLE);
    lv_label_set_long_mode(ui_music_id3_text_panel_artist_text, LV_LABEL_LONG_SCROLL_CIRCULAR);
	lv_label_set_text(ui_music_id3_text_panel_artist_text, "Unknown Arist");
	lv_obj_set_style_text_color(ui_music_id3_text_panel_artist_text, lv_color_hex(0xB3B3B3), LV_PART_MAIN | LV_STATE_DEFAULT);
	lv_obj_set_style_text_opa(ui_music_id3_text_panel_artist_text, 255, LV_PART_MAIN | LV_STATE_DEFAULT);
	lv_obj_set_style_text_font(ui_music_id3_text_panel_artist_text, lv_font_xsmall.font, LV_PART_MAIN | LV_STATE_DEFAULT);

    ui_music_bt_status = lv_obj_create(ui_music_play_disp);
    lv_obj_remove_style_all(ui_music_bt_status);
    lv_obj_set_width(ui_music_bt_status, lv_pct(100));
    lv_obj_set_height(ui_music_bt_status, lv_pct(100));
    lv_obj_set_align(ui_music_bt_status, LV_ALIGN_RIGHT_MID);
    lv_obj_add_flag(ui_music_bt_status, LV_OBJ_FLAG_HIDDEN);      /// Flags
    lv_obj_remove_flag(ui_music_bt_status, LV_OBJ_FLAG_SCROLLABLE);      /// Flags
    lv_obj_set_style_bg_color(ui_music_bt_status, lv_color_hex(0x000000), LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_bg_opa(ui_music_bt_status, 0, LV_PART_MAIN | LV_STATE_DEFAULT);

    ui_music_bt_status_text = lv_label_create(ui_music_bt_status);
    lv_obj_set_width(ui_music_bt_status_text, lv_pct(100));
    lv_obj_set_height(ui_music_bt_status_text, LV_SIZE_CONTENT);    /// 100
    lv_obj_set_align(ui_music_bt_status_text, LV_ALIGN_CENTER);
    lv_label_set_text(ui_music_bt_status_text, "Bluetooth is\n disconnected!");
    lv_obj_set_style_text_color(ui_music_bt_status_text, lv_color_hex(0xFFFFFF), LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_text_opa(ui_music_bt_status_text, 255, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_text_letter_space(ui_music_bt_status_text, 0, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_text_line_space(ui_music_bt_status_text, 20, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_text_align(ui_music_bt_status_text, LV_TEXT_ALIGN_CENTER, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_text_font(ui_music_bt_status_text, lv_font_medium.font, LV_PART_MAIN | LV_STATE_DEFAULT);

	ui_music_play_bottom = lv_obj_create(ui_music_play);
	lv_obj_set_width(ui_music_play_bottom, lv_pct(93));
	lv_obj_set_height(ui_music_play_bottom, lv_pct(20));
	lv_obj_set_align(ui_music_play_bottom, LV_ALIGN_BOTTOM_LEFT);
	lv_obj_clear_flag(ui_music_play_bottom, LV_OBJ_FLAG_SCROLLABLE);		/// Flags
	lv_obj_set_style_radius(ui_music_play_bottom, 0, LV_PART_MAIN | LV_STATE_DEFAULT);
	lv_obj_set_style_bg_color(ui_music_play_bottom, lv_color_hex(0xFFFFFF), LV_PART_MAIN | LV_STATE_DEFAULT);
	lv_obj_set_style_bg_opa(ui_music_play_bottom, 0, LV_PART_MAIN | LV_STATE_DEFAULT);
	lv_obj_set_style_border_width(ui_music_play_bottom, 0, LV_PART_MAIN | LV_STATE_DEFAULT);
	lv_obj_set_style_pad_left(ui_music_play_bottom, 0, LV_PART_MAIN | LV_STATE_DEFAULT);
	lv_obj_set_style_pad_right(ui_music_play_bottom, 0, LV_PART_MAIN | LV_STATE_DEFAULT);
	lv_obj_set_style_pad_top(ui_music_play_bottom, 0, LV_PART_MAIN | LV_STATE_DEFAULT);
	lv_obj_set_style_pad_bottom(ui_music_play_bottom, 0, LV_PART_MAIN | LV_STATE_DEFAULT);
	lv_obj_set_style_pad_row(ui_music_play_bottom, 0, LV_PART_MAIN | LV_STATE_DEFAULT);
	lv_obj_set_style_pad_column(ui_music_play_bottom, 0, LV_PART_MAIN | LV_STATE_DEFAULT);

    ui_music_play_bottom_current = lv_label_create(ui_music_play_bottom);
    lv_obj_set_width(ui_music_play_bottom_current, LV_SIZE_CONTENT);   /// 1
    lv_obj_set_height(ui_music_play_bottom_current, LV_SIZE_CONTENT);    /// 1
    lv_obj_set_x(ui_music_play_bottom_current, lv_pct(84));
    lv_obj_set_y(ui_music_play_bottom_current, lv_pct(-29));
    lv_obj_set_align(ui_music_play_bottom_current, LV_ALIGN_BOTTOM_LEFT);
    lv_label_set_text(ui_music_play_bottom_current, " ");
    lv_obj_set_style_text_color(ui_music_play_bottom_current, lv_color_hex(0x808080), LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_text_opa(ui_music_play_bottom_current, 255, LV_PART_MAIN | LV_STATE_DEFAULT);
	lv_obj_set_style_text_font(ui_music_play_bottom_current, lv_font_xsmall.font, LV_PART_MAIN | LV_STATE_DEFAULT);

    ui_music_play_bottom_bar = lv_slider_create(ui_music_play_bottom);
    lv_slider_set_value(ui_music_play_bottom_bar, 0, LV_ANIM_OFF);
    if(lv_slider_get_mode(ui_music_play_bottom_bar) == LV_SLIDER_MODE_RANGE) lv_slider_set_left_value(
            ui_music_play_bottom_bar, 0, LV_ANIM_OFF);
    lv_obj_set_height(ui_music_play_bottom_bar, 9);
    lv_obj_set_width(ui_music_play_bottom_bar, lv_pct(100));
    lv_obj_set_align(ui_music_play_bottom_bar, LV_ALIGN_BOTTOM_LEFT);
    lv_obj_set_style_radius(ui_music_play_bottom_bar, 0, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_bg_color(ui_music_play_bottom_bar, lv_color_hex(0x262626), LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_bg_opa(ui_music_play_bottom_bar, 255, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_anim_duration(ui_music_play_bottom_bar, 1000, LV_PART_MAIN | LV_STATE_DEFAULT);

    lv_obj_set_style_radius(ui_music_play_bottom_bar, 0, LV_PART_INDICATOR | LV_STATE_DEFAULT);
    lv_obj_set_style_bg_color(ui_music_play_bottom_bar, lv_color_hex(0xFFFFFF), LV_PART_INDICATOR | LV_STATE_DEFAULT);
    lv_obj_set_style_bg_opa(ui_music_play_bottom_bar, 255, LV_PART_INDICATOR | LV_STATE_DEFAULT);

    lv_obj_set_style_bg_color(ui_music_play_bottom_bar, lv_color_hex(0xFFFFFF), LV_PART_KNOB | LV_STATE_DEFAULT);
    lv_obj_set_style_bg_opa(ui_music_play_bottom_bar, 0, LV_PART_KNOB | LV_STATE_DEFAULT);

    //Compensating for LVGL9.1 draw crash with bar/slider max value when top-padding is nonzero and right-padding is 0
    if(lv_obj_get_style_pad_top(ui_music_play_bottom_bar,
                                LV_PART_MAIN) > 0) lv_obj_set_style_pad_right(ui_music_play_bottom_bar,
                                                                                  lv_obj_get_style_pad_right(ui_music_play_bottom_bar, LV_PART_MAIN) + 1, LV_PART_MAIN);

    ui_music_play_bottom_dual_phone_panel = lv_obj_create(ui_music_play_bottom);
    lv_obj_set_width(ui_music_play_bottom_dual_phone_panel, lv_pct(68));
    lv_obj_set_height(ui_music_play_bottom_dual_phone_panel, LV_SIZE_CONTENT);    /// 100
    lv_obj_add_flag(ui_music_play_bottom_dual_phone_panel, LV_OBJ_FLAG_HIDDEN);      /// Flags
    lv_obj_remove_flag(ui_music_play_bottom_dual_phone_panel,
                       LV_OBJ_FLAG_CLICKABLE | LV_OBJ_FLAG_SCROLLABLE);      /// Flags
    lv_obj_set_style_radius(ui_music_play_bottom_dual_phone_panel, 0, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_bg_color(ui_music_play_bottom_dual_phone_panel, lv_color_hex(0xFFFFFF),
                              LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_bg_opa(ui_music_play_bottom_dual_phone_panel, 0, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_border_width(ui_music_play_bottom_dual_phone_panel, 0, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_pad_left(ui_music_play_bottom_dual_phone_panel, 0, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_pad_right(ui_music_play_bottom_dual_phone_panel, 0, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_pad_top(ui_music_play_bottom_dual_phone_panel, 0, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_pad_bottom(ui_music_play_bottom_dual_phone_panel, 0, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_pad_row(ui_music_play_bottom_dual_phone_panel, 0, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_pad_column(ui_music_play_bottom_dual_phone_panel, 0, LV_PART_MAIN | LV_STATE_DEFAULT);

    ui_music_play_bottom_dual_phone = lv_image_create(ui_music_play_bottom_dual_phone_panel);
    lv_image_set_src(ui_music_play_bottom_dual_phone, ui_music_get_res(music_para, MUSIC_THEME_TEMP_MEDIA_DUAL_PHONE_A_BMP));
    lv_obj_set_width(ui_music_play_bottom_dual_phone, LV_SIZE_CONTENT);   /// 49
    lv_obj_set_height(ui_music_play_bottom_dual_phone, LV_SIZE_CONTENT);    /// 52
    lv_obj_set_y(ui_music_play_bottom_dual_phone, 0);
    lv_obj_set_x(ui_music_play_bottom_dual_phone, lv_pct(4));
    lv_obj_add_flag(ui_music_play_bottom_dual_phone, LV_OBJ_FLAG_CLICKABLE);     /// Flags
    lv_obj_remove_flag(ui_music_play_bottom_dual_phone, LV_OBJ_FLAG_SCROLLABLE);      /// Flags

    ui_music_play_bottom_dual_phone_text = lv_label_create(ui_music_play_bottom_dual_phone_panel);
    lv_obj_set_width(ui_music_play_bottom_dual_phone_text, lv_pct(80));
    lv_obj_set_height(ui_music_play_bottom_dual_phone_text, LV_SIZE_CONTENT);    /// 1
    lv_obj_set_y(ui_music_play_bottom_dual_phone_text, 0);
    lv_obj_set_x(ui_music_play_bottom_dual_phone_text, lv_pct(20));
    lv_obj_set_align(ui_music_play_bottom_dual_phone_text, LV_ALIGN_BOTTOM_LEFT);
    lv_label_set_long_mode(ui_music_play_bottom_dual_phone_text, LV_LABEL_LONG_SCROLL_CIRCULAR);
    lv_label_set_text(ui_music_play_bottom_dual_phone_text, " ");
    lv_obj_set_style_text_color(ui_music_play_bottom_dual_phone_text, lv_color_hex(0xFFFFFF),
                                LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_text_opa(ui_music_play_bottom_dual_phone_text, 255, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_text_font(ui_music_play_bottom_dual_phone_text, lv_font_small.font,
                               LV_PART_MAIN | LV_STATE_DEFAULT);

#if 0
    ui_music_play_bottom_repeat_random = lv_obj_create(ui_music_play_bottom);
    lv_obj_set_width(ui_music_play_bottom_repeat_random, lv_pct(13));
    lv_obj_set_height(ui_music_play_bottom_repeat_random, lv_pct(70));
    lv_obj_set_x(ui_music_play_bottom_repeat_random, lv_pct(-18));
    lv_obj_set_y(ui_music_play_bottom_repeat_random, lv_pct(0));
    lv_obj_set_align(ui_music_play_bottom_repeat_random, LV_ALIGN_BOTTOM_RIGHT);
    lv_obj_set_flex_flow(ui_music_play_bottom_repeat_random, LV_FLEX_FLOW_ROW);
    lv_obj_set_flex_align(ui_music_play_bottom_repeat_random, LV_FLEX_ALIGN_SPACE_BETWEEN, LV_FLEX_ALIGN_START,
                          LV_FLEX_ALIGN_START);
    lv_obj_remove_flag(ui_music_play_bottom_repeat_random, LV_OBJ_FLAG_CLICKABLE | LV_OBJ_FLAG_SCROLLABLE);      /// Flags
    lv_obj_set_style_radius(ui_music_play_bottom_repeat_random, 0, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_bg_color(ui_music_play_bottom_repeat_random, lv_color_hex(0xFFFFFF), LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_bg_opa(ui_music_play_bottom_repeat_random, 0, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_border_width(ui_music_play_bottom_repeat_random, 0, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_pad_left(ui_music_play_bottom_repeat_random, 0, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_pad_right(ui_music_play_bottom_repeat_random, 0, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_pad_top(ui_music_play_bottom_repeat_random, 0, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_pad_bottom(ui_music_play_bottom_repeat_random, 0, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_pad_row(ui_music_play_bottom_repeat_random, 0, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_pad_column(ui_music_play_bottom_repeat_random, 0, LV_PART_MAIN | LV_STATE_DEFAULT);

    ui_music_play_bottom_repeat = lv_image_create(ui_music_play_bottom_repeat_random);
    lv_image_set_src(ui_music_play_bottom_repeat, ui_music_get_res(music_para, MUSIC_THEME_TEMP_MEDIA_SETUP_REPEAT_A_BMP));
    lv_obj_set_width(ui_music_play_bottom_repeat, LV_SIZE_CONTENT);   /// 36
    lv_obj_set_height(ui_music_play_bottom_repeat, LV_SIZE_CONTENT);    /// 31
    lv_obj_set_align(ui_music_play_bottom_repeat, LV_ALIGN_BOTTOM_RIGHT);
    lv_obj_add_flag(ui_music_play_bottom_repeat, LV_OBJ_FLAG_ADV_HITTEST);     /// Flags
    lv_obj_remove_flag(ui_music_play_bottom_repeat, LV_OBJ_FLAG_SCROLLABLE);      /// Flags

    ui_music_play_bottom_random = lv_image_create(ui_music_play_bottom_repeat_random);
    lv_image_set_src(ui_music_play_bottom_random, ui_music_get_res(music_para, MUSIC_THEME_TEMP_MEDIA_SETUP_RANDOM_A_BMP));
    lv_obj_set_width(ui_music_play_bottom_random, LV_SIZE_CONTENT);   /// 36
    lv_obj_set_height(ui_music_play_bottom_random, LV_SIZE_CONTENT);    /// 31
    lv_obj_set_align(ui_music_play_bottom_random, LV_ALIGN_BOTTOM_RIGHT);
    lv_obj_add_flag(ui_music_play_bottom_random, LV_OBJ_FLAG_ADV_HITTEST);     /// Flags
    lv_obj_remove_flag(ui_music_play_bottom_random, LV_OBJ_FLAG_SCROLLABLE);      /// Flags
#endif

    ui_music_bottom_list = lv_obj_create(music_para->source_para->ui_bottom);
    lv_obj_remove_style_all(ui_music_bottom_list);
    lv_obj_set_width(ui_music_bottom_list, 161);
    lv_obj_set_height(ui_music_bottom_list, 74);
    lv_obj_set_align(ui_music_bottom_list, LV_ALIGN_BOTTOM_LEFT);
    //lv_obj_add_flag(ui_music_bottom_list, LV_OBJ_FLAG_HIDDEN);      /// Flags
    lv_obj_clear_flag(ui_music_bottom_list, LV_OBJ_FLAG_SCROLLABLE);      /// Flags
    lv_obj_set_style_bg_color(ui_music_bottom_list, lv_color_hex(0x000000), LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_bg_opa(ui_music_bottom_list, 0, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_bg_img_src(ui_music_bottom_list, ui_music_get_res(music_para, MUSIC_THEME_COM_BOT_ICON_BG_A_L_N_BMP), LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_bg_img_src(ui_music_bottom_list, ui_music_get_res(music_para, MUSIC_THEME_COM_BOT_ICON_BG_A_L_S_BMP), LV_PART_MAIN | LV_STATE_PRESSED);
    lv_obj_set_style_bg_img_src(ui_music_bottom_list, ui_music_get_res(music_para, MUSIC_THEME_COM_BOT_ICON_BG_A_L_S_BMP), LV_PART_MAIN | LV_STATE_FOCUSED);

    ui_music_bottom_list_img = lv_img_create(ui_music_bottom_list);
    lv_img_set_src(ui_music_bottom_list_img, ui_music_get_res(music_para, MUSIC_THEME_COM_BOT_LIST_N_BMP));
    lv_obj_set_width(ui_music_bottom_list_img, LV_SIZE_CONTENT);   /// 42
    lv_obj_set_height(ui_music_bottom_list_img, LV_SIZE_CONTENT);    /// 34
    lv_obj_set_align(ui_music_bottom_list_img, LV_ALIGN_CENTER);
    lv_obj_add_flag(ui_music_bottom_list_img, LV_OBJ_FLAG_ADV_HITTEST);
    lv_obj_clear_flag(ui_music_bottom_list_img, LV_OBJ_FLAG_SCROLLABLE);

    ui_music_bottom_browse = lv_obj_create(music_para->source_para->ui_bottom);
    lv_obj_remove_style_all(ui_music_bottom_browse);
    lv_obj_set_width(ui_music_bottom_browse, 161);
    lv_obj_set_height(ui_music_bottom_browse, 74);
    lv_obj_set_align(ui_music_bottom_browse, LV_ALIGN_BOTTOM_LEFT);
    //lv_obj_add_flag(ui_music_bottom_browse, LV_OBJ_FLAG_HIDDEN);      /// Flags
    lv_obj_clear_flag(ui_music_bottom_browse, LV_OBJ_FLAG_SCROLLABLE);      /// Flags
    lv_obj_set_style_bg_color(ui_music_bottom_browse, lv_color_hex(0x000000), LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_bg_opa(ui_music_bottom_browse, 0, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_bg_img_src(ui_music_bottom_browse, ui_music_get_res(music_para, MUSIC_THEME_COM_BOT_ICON_BG_A_L_N_BMP), LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_bg_img_src(ui_music_bottom_browse, ui_music_get_res(music_para, MUSIC_THEME_COM_BOT_ICON_BG_A_L_S_BMP), LV_PART_MAIN | LV_STATE_PRESSED);
    lv_obj_set_style_bg_img_src(ui_music_bottom_browse, ui_music_get_res(music_para, MUSIC_THEME_COM_BOT_ICON_BG_A_L_S_BMP), LV_PART_MAIN | LV_STATE_FOCUSED);

    ui_music_bottom_browse_img = lv_img_create(ui_music_bottom_browse);
    lv_img_set_src(ui_music_bottom_browse_img, ui_music_get_res(music_para, MUSIC_THEME_COM_BOT_BROWSE_N_BMP));
    lv_obj_set_width(ui_music_bottom_browse_img, LV_SIZE_CONTENT);   /// 42
    lv_obj_set_height(ui_music_bottom_browse_img, LV_SIZE_CONTENT);    /// 34
    lv_obj_set_align(ui_music_bottom_browse_img, LV_ALIGN_CENTER);
    lv_obj_add_flag(ui_music_bottom_browse_img, LV_OBJ_FLAG_ADV_HITTEST);
    lv_obj_clear_flag(ui_music_bottom_browse_img, LV_OBJ_FLAG_SCROLLABLE);

    ui_music_bottom_repeat = lv_obj_create(music_para->source_para->ui_bottom);
    lv_obj_remove_style_all(ui_music_bottom_repeat);
    lv_obj_set_width(ui_music_bottom_repeat, 161);
    lv_obj_set_height(ui_music_bottom_repeat, 74);
    lv_obj_set_align(ui_music_bottom_repeat, LV_ALIGN_BOTTOM_LEFT);
    //lv_obj_add_flag(ui_music_bottom_repeat, LV_OBJ_FLAG_HIDDEN);      /// Flags
    lv_obj_clear_flag(ui_music_bottom_repeat, LV_OBJ_FLAG_SCROLLABLE);      /// Flags
    lv_obj_set_style_bg_color(ui_music_bottom_repeat, lv_color_hex(0x000000), LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_bg_opa(ui_music_bottom_repeat, 0, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_bg_img_src(ui_music_bottom_repeat, ui_music_get_res(music_para, MUSIC_THEME_COM_BOT_ICON_BG_A_L_N_BMP), LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_bg_img_src(ui_music_bottom_repeat, ui_music_get_res(music_para, MUSIC_THEME_COM_BOT_ICON_BG_A_L_S_BMP), LV_PART_MAIN | LV_STATE_PRESSED);
    lv_obj_set_style_bg_img_src(ui_music_bottom_repeat, ui_music_get_res(music_para, MUSIC_THEME_COM_BOT_ICON_BG_A_L_S_BMP), LV_PART_MAIN | LV_STATE_FOCUSED);

    ui_music_bottom_repeat_img = lv_img_create(ui_music_bottom_repeat);
    lv_img_set_src(ui_music_bottom_repeat_img, ui_music_get_res(music_para, MUSIC_THEME_COM_BOT_REPEAT_A_N_BMP));
    lv_obj_set_width(ui_music_bottom_repeat_img, LV_SIZE_CONTENT);   /// 42
    lv_obj_set_height(ui_music_bottom_repeat_img, LV_SIZE_CONTENT);    /// 34
    lv_obj_set_align(ui_music_bottom_repeat_img, LV_ALIGN_CENTER);
    lv_obj_add_flag(ui_music_bottom_repeat_img, LV_OBJ_FLAG_ADV_HITTEST);
    lv_obj_clear_flag(ui_music_bottom_repeat_img, LV_OBJ_FLAG_SCROLLABLE);

    ui_music_bottom_random = lv_obj_create(music_para->source_para->ui_bottom);
    lv_obj_remove_style_all(ui_music_bottom_random);
    lv_obj_set_width(ui_music_bottom_random, 161);
    lv_obj_set_height(ui_music_bottom_random, 74);
    lv_obj_set_align(ui_music_bottom_random, LV_ALIGN_BOTTOM_LEFT);
    //lv_obj_add_flag(ui_music_bottom_random, LV_OBJ_FLAG_HIDDEN);      /// Flags
    lv_obj_clear_flag(ui_music_bottom_random, LV_OBJ_FLAG_SCROLLABLE);      /// Flags
    lv_obj_set_style_bg_color(ui_music_bottom_random, lv_color_hex(0x000000), LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_bg_opa(ui_music_bottom_random, 0, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_bg_img_src(ui_music_bottom_random, ui_music_get_res(music_para, MUSIC_THEME_COM_BOT_ICON_BG_A_L_N_BMP), LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_bg_img_src(ui_music_bottom_random, ui_music_get_res(music_para, MUSIC_THEME_COM_BOT_ICON_BG_A_L_S_BMP), LV_PART_MAIN | LV_STATE_PRESSED);
    lv_obj_set_style_bg_img_src(ui_music_bottom_random, ui_music_get_res(music_para, MUSIC_THEME_COM_BOT_ICON_BG_A_L_S_BMP), LV_PART_MAIN | LV_STATE_FOCUSED);

    ui_music_bottom_random_img = lv_img_create(ui_music_bottom_random);
    lv_img_set_src(ui_music_bottom_random_img, ui_music_get_res(music_para, MUSIC_THEME_COM_BOT_RANDOM_A_N_BMP));
    lv_obj_set_width(ui_music_bottom_random_img, LV_SIZE_CONTENT);   /// 42
    lv_obj_set_height(ui_music_bottom_random_img, LV_SIZE_CONTENT);    /// 34
    lv_obj_set_align(ui_music_bottom_random_img, LV_ALIGN_CENTER);
    lv_obj_add_flag(ui_music_bottom_random_img, LV_OBJ_FLAG_ADV_HITTEST);
    lv_obj_clear_flag(ui_music_bottom_random_img, LV_OBJ_FLAG_SCROLLABLE);

    if(music_para->work_source == ID_WORK_SOURCE_USB || (music_para->party_work_source == PARTY_D_SOURCE_USB))
        ui_music_timer = lv_timer_create(ui_event_music_timer_cb, MUSIC_TIMER_INTERVAL*10, music_para);
    else
        ui_music_timer = lv_timer_create(ui_event_music_timer_cb, 100, music_para);
    
	lv_obj_add_event_cb(ui_music, ui_event_music_mode, LV_EVENT_ALL, music_para);
    
	lv_obj_add_event_cb(music_para->source_para->ui_top_left, ui_event_music_top_left, LV_EVENT_ALL, music_para);
	//lv_obj_add_event_cb(music_para->source_para->ui_top_left, ui_event_music_back, LV_EVENT_ALL, music_para);
	lv_obj_add_event_cb(music_para->source_para->ui_top_right, ui_event_music_top_right, LV_EVENT_ALL, music_para);
    lv_obj_add_event_cb(music_para->source_para->ui_rig_bar_bg, ui_event_music_open_bar, LV_EVENT_ALL, music_para);
    lv_obj_add_event_cb(music_para->source_para->ui_rig_panel_button_top, ui_event_music_top_menu, LV_EVENT_ALL, music_para);
    lv_obj_add_event_cb(music_para->source_para->ui_rig_panel_button_bottom, ui_event_music_bottom_menu, LV_EVENT_ALL, music_para);
	//lv_obj_add_event_cb(music_para->source_para->ui_bottom_up, ui_event_music_prve, LV_EVENT_ALL, music_para);
	//lv_obj_add_event_cb(music_para->source_para->ui_bottom_play, ui_event_music_play_pause, LV_EVENT_ALL, music_para);
	//lv_obj_add_event_cb(music_para->source_para->ui_bottom_down, ui_event_music_next, LV_EVENT_ALL, music_para);
	lv_obj_add_event_cb(ui_music_play_logo_panel, ui_event_music_play_logo_panel, LV_EVENT_ALL, music_para);
	lv_obj_add_event_cb(ui_music_play_bottom_bar, ui_event_music_progress, LV_EVENT_ALL, music_para);
	lv_obj_add_event_cb(ui_music_bottom_list, ui_event_music_bottom_list, LV_EVENT_ALL, music_para);
	lv_obj_add_event_cb(ui_music_bottom_browse, ui_event_music_bottom_browse, LV_EVENT_ALL, music_para);
	lv_obj_add_event_cb(ui_music_bottom_repeat, ui_event_music_bottom_repeat, LV_EVENT_ALL, music_para);
	lv_obj_add_event_cb(ui_music_bottom_random, ui_event_music_bottom_random, LV_EVENT_ALL, music_para);

    lv_group_remove_obj(ui_music_play_bottom_bar);
}

static void ui_music_screen_uninit(ui_music_para_t * music_para)
{
    music_view_funtion_listbar_uninit(music_para);
#ifdef USE_ALBUM_BG_FG
    if(music_para->snapshot)
    {
        lv_snapshot_free(music_para->snapshot);
        music_para->snapshot = NULL;
    }
#endif

	lv_obj_remove_event_cb(music_para->source_para->ui_top_left, ui_event_music_top_left);
	//lv_obj_remove_event_cb(music_para->source_para->ui_top_left, ui_event_music_back);
	lv_obj_remove_event_cb(music_para->source_para->ui_top_right, ui_event_music_top_right);
    lv_obj_remove_event_cb(music_para->source_para->ui_rig_bar_bg, ui_event_music_open_bar);
    lv_obj_remove_event_cb(music_para->source_para->ui_rig_panel_button_top, ui_event_music_top_menu);
    lv_obj_remove_event_cb(music_para->source_para->ui_rig_panel_button_bottom, ui_event_music_bottom_menu);
	//lv_obj_remove_event_cb(music_para->source_para->ui_bottom_up, ui_event_music_prve);
	//lv_obj_remove_event_cb(music_para->source_para->ui_bottom_play, ui_event_music_play_pause);
	//lv_obj_remove_event_cb(music_para->source_para->ui_bottom_down, ui_event_music_next);
    if(ui_music_menu)
	    lv_obj_del(ui_music_menu);

    if(music_para->ui_func_menu)
        lv_obj_del(music_para->ui_func_menu);
    if(music_para->ui_func_broadcast_select_source)
        lv_obj_del(music_para->ui_func_broadcast_select_source);
    if(music_para->ui_keyboard_panel)
        lv_obj_del(music_para->ui_keyboard_panel);
    if(music_para->ui_func_menu_repeat)
        lv_obj_del(music_para->ui_func_menu_repeat);
    if(music_para->ui_func_menu_shuffle)
        lv_obj_del(music_para->ui_func_menu_shuffle);
    if(music_para->music_main_obj)
	    lv_obj_del(music_para->music_main_obj);
}


void ui_music_init(void * para)
{
	if(!ui_music_para)
	{		
		ui_music_para = (ui_music_para_t *)esMEMS_Balloc(sizeof(ui_music_para_t));
        
		if(ui_music_para == NULL)
		{
			//eLIBs_printf("esMEMS_Balloc() fail!\n");
			return;
		}
		eLIBs_memset((void*)ui_music_para, 0, sizeof(ui_music_para_t));	

        ui_music_internal_para_update(ui_music_para);

        // Initialize MCU Protocol v2 communication interface
        if (ui_music_para->source_para) {
            ui_music_para->mcu_ops.sync_all = ui_mcu_music_sync_all;
            ui_music_para->mcu_ops.sync_dirty = ui_mcu_music_sync_dirty;
            ui_music_para->mcu_ops.ops = &ui_music_para->source_para->mcu_v2_ops;
            
            // Execute initial full data sync
            if (ui_music_para->mcu_ops.sync_all) {
                ui_music_para->mcu_ops.sync_all(ui_music_para);
            }
        }

	    ui_music_init_res(ui_music_para);
		ui_music_send_srv_msg(GUI_MSG_REFRESH_INIT,NULL,NULL,0,0,0,0);
	    ui_music_helpers_init(ui_music_para, para);
    }
}


void ui_music_uninit(void * para)
{
	__music_msg("========ui_music_uninit=======\n");
	if(ui_music_para)
	{
		ui_music_para->mode_uninit_flag = 1;
		__music_msg("ui_music_uninit cur_task_is_opn_handler:%d.\n",cur_task_is_opn_handler());
		__music_msg("ui_music_uninit cur_task_is_lvgl_handler:%d.\n",cur_task_is_lvgl_handler());

		ui_music_helpers_uninit(ui_music_para);
		ui_music_send_srv_msg(GUI_MSG_REFRESH_UNINIT,0,0,0,0,1,1);
	}
}

