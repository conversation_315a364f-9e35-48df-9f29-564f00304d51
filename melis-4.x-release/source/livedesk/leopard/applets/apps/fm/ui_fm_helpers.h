// SquareLine LVGL GENERATED FILE
// EDITOR VERSION: SquareLine Studio 1.2.0
// LVGL VERSION: 8.3.4
// PROJECT: SquareLine_Project

#ifndef _UI_FM_HELPERS_H
#define _UI_FM_HELPERS_H

#ifdef __cplusplus
extern "C" {
#endif

#include "app_root_helpers.h"
#include "ui_fm_res.h"
#include "../lib/source/ui_source_communication_hub.h"

//#define MAX_FM_BAND	3
#define NUM_SAVE_CHANNEL //mllee 10920

#define APP_FM_MOD   			"APP FM MOD"

//Tuner Band Preset limit
#define MAX_PRESET			30 //0~29

#define MAX_BAND		5
#define MAX_BAND_FM		3 //mllee 121108
#define MAX_BAND_AM		2 //mllee 121108
#define MAX_BAND_FM_AM	5 //mllee 121108

#ifdef NO_FM3_AM2_BAND
#define dMAX_BAND			1
#else
#define dMAX_BAND			3
#endif


//europe
#define FM_MIN_FRQ_EUROPE	87500
#define FM_MAX_FRQ_EUROPE	108000
#define AM_MIN_FRQ_EUROPE	531
#define AM_MAX_FRQ_EUROPE	1611
#define FM_LARK_SEARCH_STEP_EUROPE	50
#define AM_LARK_SEARCH_STEP_EUROPE	9
#define FM_TUNING_STEP_EUROPE			50
#define AM_TUNING_STEP_EUROPE			AM_LARK_SEARCH_STEP_EUROPE


//europe1
#define FM_MIN_FRQ_EUROPE1	87500
#define FM_MAX_FRQ_EUROPE1	108000
#define AM_MIN_FRQ_EUROPE1	531
#define AM_MAX_FRQ_EUROPE1	1611
#define FM_LARK_SEARCH_STEP_EUROPE1	50
#define AM_LARK_SEARCH_STEP_EUROPE1	9
#define FM_TUNING_STEP_EUROPE1			50
#define AM_TUNING_STEP_EUROPE1			AM_LARK_SEARCH_STEP_EUROPE1


//europe2
#define FM_MIN_FRQ_EUROPE2	87500
#define FM_MAX_FRQ_EUROPE2	108000
#define AM_MIN_FRQ_EUROPE2	531
#define AM_MAX_FRQ_EUROPE2	1611
#define FM_LARK_SEARCH_STEP_EUROPE2	50
#define AM_LARK_SEARCH_STEP_EUROPE2	9
#define FM_TUNING_STEP_EUROPE2			50
#define AM_TUNING_STEP_EUROPE2			AM_LARK_SEARCH_STEP_EUROPE2



//usa
#define FM_MIN_FRQ_USA		87500
#define FM_MAX_FRQ_USA	107900
#define AM_MIN_FRQ_USA		530
#define AM_MAX_FRQ_USA 1710
#define FM_LARK_SEARCH_STEP_USA	200
#define AM_LARK_SEARCH_STEP_USA	10
#define FM_TUNING_STEP_USA		200
#define AM_TUNING_STEP_USA			AM_LARK_SEARCH_STEP_USA

//s.america
#define FM_MIN_FRQ_USA		87500
#define FM_MAX_FRQ_USA	108000
#define AM_MIN_FRQ_USA		530
#define AM_MAX_FRQ_USA	1700//1720
#define FM_LARK_SEARCH_STEP_USA	50
#define AM_LARK_SEARCH_STEP_USA	10
#define FM_TUNING_STEP_USA		50
#define AM_TUNING_STEP_USA			AM_LARK_SEARCH_STEP_USA

//others
#define FM_MIN_FRQ_USA		87500
#define FM_MAX_FRQ_USA	108000
#define AM_MIN_FRQ_USA		531
#define AM_MAX_FRQ_USA	1611//1720
#define FM_LARK_SEARCH_STEP_USA	50
#define AM_LARK_SEARCH_STEP_USA	9
#define FM_TUNING_STEP_USA		50
#define AM_TUNING_STEP_USA			AM_LARK_SEARCH_STEP_USA


//oirt
#define FM_MIN_FRQ_OIRT		87500
#define FM_MAX_FRQ_OIRT		108000
#define FM3_MIN_FRQ_OIRT		65000		
#define FM3_MAX_FRQ_OIRT		74000
#define AM_MIN_FRQ_OIRT		531
#define AM_MAX_FRQ_OIRT		1629
#define FM_LARK_SEARCH_STEP_OIRT	100
#define AM_LARK_SEARCH_STEP_OIRT	9
#define FM_TUNING_STEP_OIRT		50
#define AM_TUNING_STEP_OIRT			AM_LARK_SEARCH_STEP_OIRT
//latin
#define FM_MIN_FRQ_LATIN		87500
#define FM_MAX_FRQ_LATIN		108000
#define AM_MIN_FRQ_LATIN		530
#define AM_MAX_FRQ_LATIN		1710
#define FM_LARK_SEARCH_STEP_LATIN		100
#define AM_LARK_SEARCH_STEP_LATIN	10
#define FM_TUNING_STEP_LATIN			100
#define AM_TUNING_STEP_LATIN			AM_LARK_SEARCH_STEP_LATIN
//asia
#define FM_MIN_FRQ_ASIA		87500
#define FM_MAX_FRQ_ASIA		108000
#define AM_MIN_FRQ_ASIA		522
#define AM_MAX_FRQ_ASIA		1620
#define FM_LARK_SEARCH_STEP_ASIA		100
#define AM_LARK_SEARCH_STEP_ASIA		9
#define FM_TUNING_STEP_ASIA			100
#define AM_TUNING_STEP_ASIA			AM_LARK_SEARCH_STEP_ASIA
//japan
#define FM_MIN_FRQ_JAP			76000
#define FM_MAX_FRQ_JAP			90000
#define AM_MIN_FRQ_JAP			531
#define AM_MAX_FRQ_JAP			1638
#define FM_LARK_SEARCH_STEP_JAP		100
#define AM_LARK_SEARCH_STEP_JAP		9
#define FM_TUNING_STEP_JAP			100
#define AM_TUNING_STEP_JAP		AM_LARK_SEARCH_STEP_JAP


//middle east
#define FM_MIN_FRQ_MIDDLE_EAST		87500
#define FM_MAX_FRQ_MIDDLE_EAST		108000
#define AM_MIN_FRQ_MIDDLE_EAST		522
#define AM_MAX_FRQ_MIDDLE_EAST		1620
#define FM_LARK_SEARCH_STEP_MIDDLE_EAST		100
#define AM_LARK_SEARCH_STEP_MIDDLE_EAST		9
#define FM_TUNING_STEP_MIDDLE_EAST			100
#define AM_TUNING_STEP_MIDDLE_EAST			AM_LARK_SEARCH_STEP_MIDDLE_EAST


//Oceania
#define FM_MIN_FRQ_OCEANIA		87500
#define FM_MAX_FRQ_OCEANIA		108000
#define AM_MIN_FRQ_OCEANIA		522
#define AM_MAX_FRQ_OCEANIA		1710
#define FM_LARK_SEARCH_STEP_OCEANIA		100
#define AM_LARK_SEARCH_STEP_OCEANIA		9
#define FM_TUNING_STEP_OCEANIA			100
#define AM_TUNING_STEP_OCEANIA			AM_LARK_SEARCH_STEP_OCEANIA

//Hong Kong
#define FM_MIN_FRQ_HONG_KONG		87500
#define FM_MAX_FRQ_HONG_KONG		108000
#define AM_MIN_FRQ_HONG_KONG		522
#define AM_MAX_FRQ_HONG_KONG		1620
#define FM_LARK_SEARCH_STEP_HONG_KONG		100
#define AM_LARK_SEARCH_STEP_HONG_KONG		9
#define FM_TUNING_STEP_HONG_KONG			100
#define AM_TUNING_STEP_HONG_KONG			AM_LARK_SEARCH_STEP_HONG_KONG


#define FM_TIMER_UPDATE_SEARCH_FREQ_TIMES	6
#define FM_FINETUNE_DELAY_TIMES		(5 * (100 / FM_TIMER_UPDATE_SEARCH_FREQ_TIMES))//5S

typedef struct fm_res_para
{
	HTHEME lv_fm_bmp[MAX_FM_BMP_ITEM];
	lv_img_dsc_t lv_fm_icon[MAX_FM_BMP_ITEM];

}ui_fm_res_para_t;

typedef struct {
    __u32 frequency;                        /* Stored frequency in KHz */
    __u8  ps_valid;                         /* PS name validity flag */
    char  ps_name[9];                       /* PS name (8 chars + null terminator) */
} list_fm_t;

typedef struct {
    __u32 frequency;                        /* Stored frequency in KHz */
} list_am_wb_t;

typedef struct {
    __u8  ps_status;                        /* PS data status */
    char  ps_name[9];                       /* PS name (8 chars + null terminator) */
    __u8  rt_status;                        /* RT data status */
    char  rt_text[65];                      /* RT text (64 chars + null terminator) */
    __u8  pty_code;                         /* PTY code (0-31) */
    __u8  af_status;                        /* AF status */
    __u8  ta_status;                        /* TA status */
} rds_data_t;

typedef struct fm_data_para
{
	__u32 last_work_source;
    __u16 cur_band; //play band
    __u32 cur_freq;
    
    __u8 cur_pty_type; //mllee 120702
    __u8 cur_rds_status; //mllee 120702
	__u8 local_seek_on_off;
    
	char ps_data[8+1];
	//__u32 FM1_3_AM1_2_freq[6][PRESET_PRE_BAND_MAX];	//Ƶ�α���//add by Kingvan

	//define para match for mcu data
	//base
	__u32 frequency;                        /* Current frequency in KHz */
    __u8  tuner_state;                      /* Tuner state: 0=playing, 1=manual_seek, 2=auto_seek, 3=preset_seek, 4=auto_store */
    __u8  preset_number;                    /* Current preset number (1-18, 0=none) */
	//status and switch
	__u8  scan_mode;                        /* Scan mode: 0=none, 1=manual_up, 2=manual_down, etc. */
    __u8  stereo_status;                    /* Stereo status: 0=mono, 1=stereo */
    __u8  local_seek;                       /* Local seek setting: 0=off, 1=on */
    __u8  rds_switch;                       /* RDS switch: 0=off, 1=on */
	/* RDS information */
    rds_data_t rds_data;                 /* RDS data */
	//list
	/* Preset management */
    list_fm_t fm_presets[10];               /* FM preset list (1-18) */
    list_am_wb_t am_presets[10];            /* AM preset list (1-12) */
    list_am_wb_t wb_presets[7];             /* WB preset list (1-7) */
}ui_fm_data_para_t;

typedef struct fm_para
{  
	__u8 mode_store;
    	
	__u32 fmchannelfreq;	//indicate current frequence
	__u32 fmchanneltotal;	//indicate total channel
	
	__bool fmchannelbrowse;

	__u16 curFM1_3AM1_2_id;
	__u16 curFM1_3AM1_2_index;
	__bool fine_tune_mode;

#ifdef NUM_SAVE_CHANNEL //mllee 10920
	__u8	number_play;
	__u8	number_save;
	__u32	numberSaveFlashCnt;
#endif

	//area, band limit, tuning step
	__u32	area;
	__u32	fm_frq_min;
	__u32	fm_frq_max;
	__u32	am_frq_min;
	__u32	am_frq_max;
	__u8	fm_tuning_step;
	__u8	am_tuning_step;
	
	__u8 mute_status;
#ifdef SWITCH_BAND_TIPS
	H_WIN h_frm_loading;
#endif
	H_WIN h_frm_app_tips;

	__u8 switch_band;

    __u8 low_voltage_flag;
    __u8 charge_flag;
    __u8 eq_flag;

    __u8 local_onoff;
    __u8 rds_onoff;
    __u8 af_onoff;

    __u32 theme;//__THEME_TYPE
    __u32 theme_bmp_start;
    __u32 theme_bmp_end;

    lv_obj_t * source_obj;
    ui_source_para_t * source_para;
    ui_fm_data_para_t ui_fm_data_para;
    // MCU data operations interface
    mcu_data_ops_t mcu_ops;
	
    __u32 menu_win;
	__u32 menu_group_flag; // 0: SOURCE_MENU_GROUP_NONE

    __u32 bottom_fouse_flag;
    __u32 bottom_fouse_index;

	lv_indev_data_t * indev_data;
	lv_obj_t * fm_main_obj;
	lv_obj_t * fm_menu_obj;
    lv_anim_t * menu_anim[6];
	ui_fm_res_para_t ui_fm_res_para;
	explr_list_para_t ui_list_para;
	lv_timer_t * tips_tmr;
	__gui_msg_t fm_msg;
	bool mode_uninit_flag;
}ui_fm_para_t;

extern ui_fm_para_t * ui_fm_para;

//ui_fm_helpers.c
extern void __fm_cmd2parent(H_WIN hwin, __s32 id, __s32 data1, __s32 data2);
extern void __ui_fm_func_auto_store(ui_fm_para_t * fm_para);
extern void __ui_fm_func_pty_seek(ui_fm_para_t * fm_para);
extern void __ui_fm_stop_seek(ui_fm_para_t * fm_para);
extern void __ui_fm_ctrl_stop_seek(ui_fm_para_t * fm_para);
extern void __ui_fm_ctrl_prev(ui_fm_para_t * fm_para);
extern void __ui_fm_ctrl_long_prev(ui_fm_para_t * fm_para);
extern void __ui_fm_ctrl_next(ui_fm_para_t * fm_para);
extern void __ui_fm_ctrl_long_next(ui_fm_para_t * fm_para);
extern void __ui_fm_ctrl_fmband(ui_fm_para_t * fm_para);
extern void __ui_fm_ctrl_amband(ui_fm_para_t * fm_para);
extern void __ui_fm_ctrl_preset(ui_fm_para_t * fm_para, __u32 preset_index);
extern void __ui_fm_ctrl_long_preset(ui_fm_para_t * fm_para, __u32 preset_index);
extern void __ui_fm_set_mute(ui_fm_para_t * fm_para);
extern ui_fm_para_t * ui_fm_get_para(void);
extern __s32 ui_fm_helpers_init(ui_fm_para_t * fm_para, void * para);
extern __s32 ui_fm_helpers_uninit(ui_fm_para_t * fm_para);


//ui_fm_res.c
extern lv_img_dsc_t * ui_fm_get_res(ui_fm_para_t * fm_para, __u32 icon);
extern void ui_fm_init_res(ui_fm_para_t * fm_para);
extern void ui_fm_uninit_res(ui_fm_para_t * fm_para);


//ui_fm.c
extern void __fm_view_update_low_voltage(ui_fm_para_t * fm_para, __u8 update);
extern void __fm_view_update_charge(ui_fm_para_t * fm_para, __u8 update);
extern void __fm_view_update_eq(ui_fm_para_t * fm_para, __u8 update);
extern void __fm_view_update_preset(ui_fm_para_t * fm_para, __u8 update);
extern void __fm_view_update_loc(ui_fm_para_t * fm_para, __bool update);
extern void __fm_view_update_ST(ui_fm_para_t * fm_para, __bool update);
extern void __fm_view_update_af(ui_fm_para_t * fm_para, __bool update);
extern void __fm_view_update_ta(ui_fm_para_t * fm_para, __bool update);
extern void __fm_view_show_rds_pty(ui_fm_para_t * fm_para,__bool show_status);
extern void __fm_view_show_rds_ps(ui_fm_para_t * fm_para,__bool show_status);
extern void __fm_view_update_fmam_band(ui_fm_para_t * fm_para);
extern void __fm_view_update_frq(ui_fm_para_t * fm_para);
extern void __fm_view_update_Mhz_Khz(ui_fm_para_t * fm_para);
extern void fm_view_update_status_bar(ui_fm_para_t * fm_para);
extern void __fm_view_update_seek_string(ui_fm_para_t * fm_para, __u8 show_status);
extern void __fm_view_update_manual_string(ui_fm_para_t * fm_para, __u8 show_status);
extern void __fm_view_clear_rds_status(ui_fm_para_t * fm_para);
extern void __fm_view_clear_pty_status(ui_fm_para_t * fm_para);
extern void fmplay_set_area(ui_fm_para_t * fm_para, __u8 band_area);
#ifdef SWITCH_BAND_TIPS
extern __s32 __create_fm_tips_dialog(ui_fm_para_t * fm_para,__s32 title_id,__s32 content_id,__u32 time_out);
extern __s32 __fm_tips_dialog_cancel(ui_fm_para_t * fm_para);
#endif
extern __s32 __create_fm_app_tips_dialog(ui_fm_para_t *fm_para,__s32 title_id,__s32 content_id,__u32 time_out);
extern __s32 __fm_app_tips_dialog_cancel(ui_fm_para_t *fm_para);
extern void __ui_fm_tips_tmr_create(ui_fm_para_t * fm_para);
extern void __ui_fm_remove_hidden(ui_fm_para_t * fm_para);
extern void __ui_fm_add_hidden(ui_fm_para_t * fm_para);
extern void __ui_fm_layer_on(ui_fm_para_t * fm_para, __u8 store);
extern void __ui_fm_layer_sleep(ui_fm_para_t * fm_para, __u8 store);


//ui_fm_events.c
extern __s32 ui_fm_opn_msg(__gui_msg_t * pmsg, __u32 sync);
extern __s32 ui_fm_refresh_msg(__gui_msg_t * pmsg, __u32 sync);
extern __s32 ui_fm_send_srv_msg(__s32 id, __u64 data1, __u64 data2, __u64 dwReserved, void *p_arg, __u32 flag, __u32 sync);
extern __s32 ui_fm_send_opn_msg(__s32 id, __u64 data1, __u64 data2, __u64 dwReserved, void *p_arg, __u32 flag, __u32 sync);
extern __s32 fm_view_update_low_voltage(ui_fm_para_t * fm_para, __u8 update);
extern __s32 fm_view_update_charge(ui_fm_para_t * fm_para, __u8 update);
extern __s32 fm_view_update_eq(ui_fm_para_t * fm_para, __u8 update);
extern __s32 fm_view_update_preset(ui_fm_para_t * fm_para, __u8 update);
extern __s32 fm_view_update_loc(ui_fm_para_t * fm_para, __bool update);
extern __s32 fm_view_update_ST(ui_fm_para_t * fm_para, __bool update);
extern __s32 fm_view_update_af(ui_fm_para_t * fm_para, __bool update);
extern __s32 fm_view_update_ta(ui_fm_para_t * fm_para, __bool update);
extern __s32 fm_view_show_rds_pty(ui_fm_para_t * fm_para,__bool show_status);
extern __s32 fm_view_show_rds_ps(ui_fm_para_t * fm_para,__bool show_status);
extern __s32 fm_view_update_fmam_band(ui_fm_para_t * fm_para);
extern __s32 fm_view_update_frq(ui_fm_para_t * fm_para);
extern __s32 fm_view_update_Mhz_Khz(ui_fm_para_t * fm_para);
extern __s32 fm_view_update_seek_string(ui_fm_para_t * fm_para, __u8 show_status);
extern __s32 fm_view_update_manual_string(ui_fm_para_t * fm_para, __u8 show_status);
extern __s32 fm_view_clear_rds_status(ui_fm_para_t * fm_para);
extern __s32 fm_view_clear_pty_status(ui_fm_para_t * fm_para);
#ifdef SWITCH_BAND_TIPS
extern __s32 create_fm_tips_dialog(ui_fm_para_t * fm_para,__s32 title_id,__s32 content_id,__u32 time_out);
extern __s32 fm_tips_dialog_cancel(ui_fm_para_t * fm_para);
#endif
extern __s32 create_fm_app_tips_dialog(ui_fm_para_t *fm_para,__s32 title_id,__s32 content_id,__u32 time_out);
extern __s32 fm_app_tips_dialog_cancel(ui_fm_para_t *fm_para);
extern __s32 ui_fm_tips_tmr_create(ui_fm_para_t * fm_para);
extern __s32 ui_fm_remove_hidden(ui_fm_para_t * fm_para);
extern __s32 ui_fm_add_hidden(ui_fm_para_t * fm_para);
extern __s32 ui_fm_layer_on(ui_fm_para_t * fm_para, __u8 store);
extern __s32 ui_fm_layer_sleep(ui_fm_para_t * fm_para, __u8 store);
extern __s32 fm_cmd2parent(H_WIN hwin, __s32 id, __s32 data1, __s32 data2);
extern __s32 ui_fm_func_auto_store(ui_fm_para_t * fm_para);
extern __s32 ui_fm_func_pty_seek(ui_fm_para_t * fm_para);
extern __s32 ui_fm_stop_seek(ui_fm_para_t * fm_para);
extern __s32 ui_fm_ctrl_stop_seek(ui_fm_para_t * fm_para);
extern __s32 ui_fm_ctrl_prev(ui_fm_para_t * fm_para);
extern __s32 ui_fm_ctrl_long_prev(ui_fm_para_t * fm_para);
extern __s32 ui_fm_ctrl_next(ui_fm_para_t * fm_para);
extern __s32 ui_fm_ctrl_long_next(ui_fm_para_t * fm_para);
extern __s32 ui_fm_ctrl_fmband(ui_fm_para_t * fm_para);
extern __s32 ui_fm_ctrl_amband(ui_fm_para_t * fm_para);
extern __s32 ui_fm_ctrl_preset(ui_fm_para_t * fm_para, __u32 preset_index);
extern __s32 ui_fm_ctrl_long_preset(ui_fm_para_t * fm_para, __u32 preset_index);
extern __s32 ui_fm_set_mute(ui_fm_para_t * fm_para);

#ifdef __cplusplus
} /*extern "C"*/
#endif

#endif
