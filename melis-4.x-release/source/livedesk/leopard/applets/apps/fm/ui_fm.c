// SquareLine LVGL GENERATED FILE
// EDITOR VERSION: SquareLine Studio 1.2.0
// LVGL VERSION: 8.3.4
// PROJECT: SquareLine_Project

#include "ui_fm.h"
#include "ui_helpers.h"
#include <char_enc.h>
#include "../lib/source/ui_source_communication_hub.h"

#if USE_LOG_PRINT
	#define __fm_msg(...)			(eLIBs_printf("FM MSG:L%d:", __LINE__),				 \
													  eLIBs_printf(__VA_ARGS__)) 
#else
	#define __fm_msg(...)
#endif


///////////////////// VARIABLES ////////////////////
ui_fm_para_t * ui_fm_para = NULL;
static void ui_event_fm_menu_item_screen(lv_event_t * e);
static void ui_event_fm_menu_arrow_screen(lv_event_t * e);
static void ui_event_fm_back_screen(lv_event_t * e);
static void ui_event_fm_timer_cb(lv_timer_t * t);
static void ui_fm_source_event_handler(ui_fm_para_t * fm_para);
static void ui_fm_open_bar_menu_event_handler(ui_fm_para_t * fm_para);
static void ui_fm_top_left_event_handler(ui_fm_para_t * fm_para);
static void ui_fm_top_right_event_handler(ui_fm_para_t * fm_para);
static void ui_fm_top_menu_event_handler(ui_fm_para_t * fm_para);
static void ui_fm_bottom_menu_event_handler(ui_fm_para_t * fm_para);
static void ui_fm_source_title(ui_fm_para_t * fm_para, __u8 * str, __u8 source_flag);


// SCREEN: ui_fm
static void ui_fm_menu_screen_init(ui_fm_para_t * fm_para);
static void ui_fm_menu_screen_uninit(ui_fm_para_t * fm_para);
static void ui_fm_func_menu_screen_init(ui_fm_para_t * fm_para);
static void ui_fm_direct_screen_init(ui_fm_para_t * fm_para);
static void ui_fm_direct_screen_uninit(ui_fm_para_t * fm_para);
static void ui_fm_screen_init(ui_fm_para_t * fm_para);
static void ui_fm_screen_uninit(ui_fm_para_t * fm_para);
static void ui_event_fm_preset_screen(lv_event_t * e);
static void ui_event_fm_item_screen(lv_event_t * e);
static void ui_fm_bottom_menu_event_handler(ui_fm_para_t * fm_para);
static void ui_fm_night_theme(ui_fm_para_t * fm_para, __u32 theme);
static void ui_fm_set_obj_theme_src(void * para, lv_obj_t * obj, __u32 theme);

lv_obj_t * ui_fm = NULL;
lv_obj_t * ui_fm_play = NULL;
lv_obj_t * ui_fm_menu = NULL;
lv_obj_t * ui_fm_play_top = NULL;
lv_obj_t * ui_fm_play_top_low = NULL;
lv_obj_t * ui_fm_play_top_charge = NULL;
lv_obj_t * ui_fm_play_top_eq = NULL;
lv_obj_t * ui_fm_play_mid = NULL;
lv_obj_t * ui_fm_play_logo_panel = NULL;
lv_obj_t * ui_fm_play_logo = NULL;
lv_obj_t * ui_fm_play_logo_text = NULL;
lv_obj_t * ui_fm_play_disp = NULL;
lv_obj_t * ui_fm_play_disp_top = NULL;
lv_obj_t * ui_fm_play_disp_seek = NULL;
lv_obj_t * ui_fm_play_disp_preset = NULL;
lv_obj_t * ui_fm_play_disp_freq = NULL;
lv_obj_t * ui_fm_play_disp_freq_num = NULL;
lv_obj_t * ui_fm_play_disp_freq_unit_panel = NULL;
lv_obj_t * ui_fm_play_disp_freq_loc = NULL;
lv_obj_t * ui_fm_play_disp_freq_unit = NULL;
lv_obj_t * ui_fm_play_disp_bottom = NULL;
lv_obj_t * ui_fm_play_disp_pty = NULL;
lv_obj_t * ui_fm_bottom_list = NULL;
lv_obj_t * ui_fm_bottom_list_img = NULL;
lv_obj_t * ui_fm_bottom_auto = NULL;
lv_obj_t * ui_fm_bottom_auto_label = NULL;
lv_obj_t * ui_fm_bottom_local = NULL;
lv_obj_t * ui_fm_bottom_local_label = NULL;
lv_obj_t * ui_fm_bottom_direct = NULL;
lv_obj_t * ui_fm_bottom_direct_img = NULL;

lv_obj_t * ui_fm_func_menu = NULL;

lv_obj_t * ui_fm_code_keyboard_panel = NULL;
lv_obj_t * ui_fm_code_keyboard = NULL;
lv_obj_t * ui_fm_code_textarea = NULL;

__u32 FM_THMEM_TEMP_MEDIA_SETUP_REPEAT_B_BMP = 0;  
__u32 FM_THMEM_FM_ICON_BMP = 0; 
__u32 FM_THMEM_AM_ICON_BMP = 0; 
__u32 FM_THMEM_WB_ICON_BMP = 0; 

__u32 FM_THEME_COM_BOT_BG_BMP = 0;  

__u32 FM_THEME_COM_BOT_ICON_BG_A_L_N_BMP = 0;
__u32 FM_THEME_COM_BOT_ICON_BG_A_L_S_BMP = 0;

__u32 FM_THEME_COM_BOT_LIST_N_BMP = 0;
__u32 FM_THEME_COM_BOT_LIST_D_BMP = 0;

__u32 FM_THEME_COM_BOT_DIRECT_N_BMP = 0;
__u32 FM_THEME_COM_BOT_DIRECT_D_BMP = 0;

__u32 FM_THMEM_TEMP_COM_LIST_OFF_BMP = 0;          
__u32 FM_THMEM_TEMP_MEDIA_DUAL_PHONE_A_BMP = 0;    
__u32 FM_THMEM_TEMP_COM_RIG_BG_A_BMP = 0;          
__u32 FM_THMEM_TEMP_COM_RIG_ARROW_A_BMP = 0;       
__u32 FM_THMEM_TEMP_MEDIA_SETUP_REPEAT_A_BMP = 0;  
__u32 FM_THMEM_TEMP_COM_RIG_AUDIO_ICON_S_BMP = 0;  
__u32 FM_THMEM_TEMP_COM_RIG_AUDIO_ICON_N_BMP = 0;  
__u32 FM_THMEM_TEMP_MEDIA_DUAL_PHONE_B_BMP = 0;    
__u32 FM_THMEM_TEMP_COM_TOP_BACK_ICON_BMP = 0;     
__u32 FM_THMEM_TEMP_COM_MEDIA_ALBUM_BG_BMP = 0;    
__u32 FM_THMEM_TEMP_MEDIA_SETUP_RANDOM_A_BMP = 0;  
__u32 FM_THMEM_TEMP_COM_RIG_FUNC_ICON_S_BMP = 0;   
__u32 FM_THMEM_TEMP_COM_RIG_ARROW_B_BMP = 0;       
__u32 FM_THMEM_TEMP_COM_LOW_VOLTAGE_BMP = 0;       
__u32 FM_THMEM_TEMP_COM_RIG_BG_C_BMP = 0;          
__u32 FM_THMEM_TEMP_COM_TOP_ICON_BG_N_BMP = 0;     
__u32 FM_THMEM_TEMP_COM_LIST_ON_BMP = 0;           
__u32 FM_THMEM_TEMP_COM_LIST_BG_BMP = 0;           
__u32 FM_THMEM_TEMP_COM_RIG_BG_B_BMP = 0;          
__u32 FM_THMEM_TEMP_COM_RIG_ICON_BG_N_BMP = 0;     
__u32 FM_THMEM_TEMP_MEDIA_SETUP_RANDOM_B_BMP = 0;  
__u32 FM_THMEM_TEMP_COM_RIG_ZONE_ICON_BMP = 0;     
__u32 FM_THMEM_TEMP_COM_RIG_ICON_BG_P_BMP = 0;     
__u32 FM_THMEM_TEMP_COM_BG_BMP = 0;                
__u32 FM_THMEM_TEMP_COM_PD_CHARGE_BMP = 0;         
__u32 FM_THMEM_TEMP_MEDIA_SETUP_REPEAT_C_BMP = 0;  
__u32 FM_THMEM_TEMP_COM_PUNCH_EQ_BMP = 0;          
__u32 FM_THMEM_TEMP_COM_TOP_SOURCE_ICON_BMP = 0;   
__u32 FM_THMEM_TEMP_COM_RIG_ARROW_C_BMP = 0;       
__u32 FM_THMEM_TEMP_COM_TOP_BG_BMP = 0;            
__u32 FM_THMEM_TEMP_COM_RIG_ZONE_ICON_S_BMP = 0;   
__u32 FM_THMEM_TEMP_COM_RIG_ZONE_ICON_N_BMP = 0;   
__u32 FM_THMEM_TEMP_COM_RIG_AUDIO_ICON_BMP = 0;    
__u32 FM_THMEM_TEMP_COM_TOP_LIST_ICON_BMP = 0;     
__u32 FM_THMEM_TEMP_MEDIA_DUAL_PHONE_D_BMP = 0;    
__u32 FM_THMEM_TEMP_COM_RIG_FUNC_ICON_N_BMP = 0;   
__u32 FM_THMEM_TEMP_COM_TOP_ICON_BG_P_S_BMP = 0;   
__u32 FM_THMEM_TEMP_COM_RIG_FUNC_ICON_BMP = 0;  


lv_timer_t  * ui_fm_timer;

static __u32 fmchannelfreq = 0;

#ifdef TUNER_PRINT_ALL_INFO_WIN
static const __u16 pschar_unicode_tbl[]= {
//0x00 ~ 0x0F
0x0000, 0x0118, 0x012E, 0x0172, 0x0102, 0x0116, 0x010E, 0x0218, 0x021A, 0x010A, 0x0000, 0x0000, 0x0120, 0x0139, 0x017B, 0x0143,
//0x10 ~ 0x1F	
0x0105, 0x0119, 0x012F, 0x0173, 0x0103, 0x0117, 0x010F, 0x0219, 0x021B, 0x010B, 0x0147, 0x011A, 0x0121, 0x013A, 0x017C, 0x0000,
//0x20 ~ 0x2F
0x0020, 0x0021, 0x0022, 0x0023, 0x0142, 0x0025, 0x0026, 0x0027, 0x0028, 0x0029, 0x002A, 0x002B, 0x002C, 0x002D, 0x002E, 0x002F,
//0x30 ~ 0x3F
0x0030, 0x0031, 0x0032, 0x0033, 0x0034, 0x0035, 0x0036, 0x0037, 0x0038, 0x0039, 0x003A, 0x003B, 0x003C, 0x003D, 0x003E, 0x003F,
//0x40 ~ 0x4F
0x0040, 0x0041, 0x0042, 0x0043, 0x0044, 0x0045, 0x0046, 0x0047, 0x0048, 0x0049, 0x004A, 0x004B, 0x004C, 0x004D, 0x004E, 0x004F,
//0x50 ~ 0x5F
0x0050, 0x0051, 0x0052, 0x0053, 0x0054, 0x0055, 0x0056, 0x0057, 0x0058, 0x0059, 0x005A, 0x005B, 0x016E, 0x005D, 0x0141, 0x005F,
//0x60 ~ 0x6F
0x0104, 0x0061, 0x0062, 0x0063, 0x0064, 0x0065, 0x0066, 0x0067, 0x0068, 0x0069, 0x006A, 0x006B, 0x006C, 0x006D, 0x006E, 0x006F,
//0x70 ~ 0x7F
0x0070, 0x0071, 0x0072, 0x0073, 0x0074, 0x0075, 0x0076, 0x0077, 0x0078, 0x0079, 0x007A, 0x00AB, 0x016F, 0x00BB, 0x013D, 0x0126,
//0x80 ~ 0x8F
0x00E1, 0x00E0, 0x00E9, 0x00E8, 0x00ED, 0x00EC, 0x00F3, 0x00F2, 0x00FA, 0x00F9, 0x00D1, 0x00C7, 0x015E, 0x00DF, 0x00A1, 0x0178,
//0x90 ~ 0x9F
0x00E2, 0x00E4, 0x00EA, 0x00EB, 0x00EE, 0x00EF, 0x00F4, 0x00F6, 0x00FB, 0x00FC, 0x00F1, 0x00E7, 0x015F, 0x011F, 0x0131, 0x00FF,
//0xA0 ~ 0xAF
0x0136, 0x0145, 0x00A9, 0x0122, 0x011E, 0x011B, 0x0148, 0x0151, 0x0150, 0x20AC, 0x00A3, 0x0024, 0x0100, 0x0112, 0x012A, 0x016A,
//0xB0 ~ 0xBF
0x0137, 0x0146, 0x013B, 0x0123, 0x013C, 0x0130, 0x0144, 0x0171, 0x0170, 0x00BF, 0x013E, 0x00B0, 0x0101, 0x0113, 0x012B, 0x016B,
//0xC0 ~ 0xCF
0x00C1, 0x00C0, 0x00C9, 0x00C8, 0x00CD, 0x00CC, 0x00D3, 0x00D2, 0x00DA, 0x00D9, 0x0158, 0x010C, 0x0160, 0x017D, 0x00D0, 0x013F,
//0xD0 ~ 0xDF
0x00C2, 0x00C4, 0x00CA, 0x00CB, 0x00CE, 0x00CF, 0x00D4, 0x00D6, 0x00DB, 0x00DC, 0x0159, 0x010D, 0x0161, 0x017E, 0x0111, 0x0140,
//0xE0 ~ 0xEF
0x00C3, 0x00C5, 0x00C6, 0x0152, 0x0177, 0x00DD, 0x00D5, 0x00D8, 0x00DE, 0x014A, 0x0154, 0x0106, 0x015A, 0x0179, 0x0164, 0x00F0,
//0xF0 ~ 0xFF
0x00E3, 0x00E5, 0x00E6, 0x0153, 0x0175, 0x00FD, 0x00F5, 0x00F8, 0x00FE, 0x014B, 0x0155, 0x0107, 0x015B, 0x017A, 0x0165, 0x0127
};



static const __u16 pschar_rbds_unicode_tbl[]= {
//0x00 ~ 0x0F
0x0000, 0x0118, 0x012E, 0x0172, 0x0102, 0x0116, 0x010E, 0x0218, 0x021A, 0x010A, 0x0000, 0x0000, 0x0120, 0x0139, 0x017B, 0x0143,
//0x10 ~ 0x1F	
0x0105, 0x0119, 0x012F, 0x0173, 0x0103, 0x0117, 0x010F, 0x0219, 0x021B, 0x010B, 0x0147, 0x011A, 0x0121, 0x013A, 0x017C, 0x0000,
//0x20 ~ 0x2F
0x0020, 0x0021, 0x0022, 0x0023, 0x00A4, 0x0025, 0x0026, 0x0027, 0x0028, 0x0029, 0x002A, 0x002B, 0x002C, 0x002D, 0x002E, 0x002F,
//0x30 ~ 0x3F
0x0030, 0x0031, 0x0032, 0x0033, 0x0034, 0x0035, 0x0036, 0x0037, 0x0038, 0x0039, 0x003A, 0x003B, 0x003C, 0x003D, 0x003E, 0x003F,
//0x40 ~ 0x4F
0x0040, 0x0041, 0x0042, 0x0043, 0x0044, 0x0045, 0x0046, 0x0047, 0x0048, 0x0049, 0x004A, 0x004B, 0x004C, 0x004D, 0x004E, 0x004F,
//0x50 ~ 0x5F
0x0050, 0x0051, 0x0052, 0x0053, 0x0054, 0x0055, 0x0056, 0x0057, 0x0058, 0x0059, 0x005A, 0x005B, 0x005C, 0x005D, 0x2015, 0x005F,
//0x60 ~ 0x6F
0x0104, 0x0061, 0x0062, 0x0063, 0x0064, 0x0065, 0x0066, 0x0067, 0x0068, 0x0069, 0x006A, 0x006B, 0x006C, 0x006D, 0x006E, 0x006F,
//0x70 ~ 0x7F
0x0070, 0x0071, 0x0072, 0x0073, 0x0074, 0x0075, 0x0076, 0x0077, 0x0078, 0x0079, 0x007A, 0x00AB, 0x016F, 0x00BB, 0x013D, 0x0126,
//0x80 ~ 0x8F
0x00E1, 0x00E0, 0x00E9, 0x00E8, 0x00ED, 0x00EC, 0x00F3, 0x00F2, 0x00FA, 0x00F9, 0x00D1, 0x00C7, 0x015E, 0x00DF, 0x00A1, 0x0178,
//0x90 ~ 0x9F
0x00E2, 0x00E4, 0x00EA, 0x00EB, 0x00EE, 0x00EF, 0x00F4, 0x00F6, 0x00FB, 0x00FC, 0x00F1, 0x00E7, 0x015F, 0x011F, 0x0131, 0x00FF,
//0xA0 ~ 0xAF
0x0136, 0x0145, 0x00A9, 0x0122, 0x011E, 0x011B, 0x0148, 0x0151, 0x0150, 0x20AC, 0x00A3, 0x0024, 0x0100, 0x0112, 0x012A, 0x016A,
//0xB0 ~ 0xBF
0x0137, 0x0146, 0x013B, 0x0123, 0x013C, 0x0130, 0x0144, 0x0171, 0x0170, 0x00BF, 0x013E, 0x00B0, 0x0101, 0x0113, 0x012B, 0x016B,
//0xC0 ~ 0xCF
0x00C1, 0x00C0, 0x00C9, 0x00C8, 0x00CD, 0x00CC, 0x00D3, 0x00D2, 0x00DA, 0x00D9, 0x0158, 0x010C, 0x0160, 0x017D, 0x00D0, 0x013F,
//0xD0 ~ 0xDF
0x00C2, 0x00C4, 0x00CA, 0x00CB, 0x00CE, 0x00CF, 0x00D4, 0x00D6, 0x00DB, 0x00DC, 0x0159, 0x010D, 0x0161, 0x017E, 0x0111, 0x0140,
//0xE0 ~ 0xEF
0x00C3, 0x00C5, 0x00C6, 0x0152, 0x0177, 0x00DD, 0x00D5, 0x00D8, 0x00DE, 0x014A, 0x0154, 0x0106, 0x015A, 0x0179, 0x0164, 0x00F0,
//0xF0 ~ 0xFF
0x00E3, 0x00E5, 0x00E6, 0x0153, 0x0175, 0x00FD, 0x00F5, 0x00F8, 0x00FE, 0x014B, 0x0155, 0x0107, 0x015B, 0x017A, 0x0165, 0x0127
};
#endif

///////////////////// TEST LVGL SETTINGS ////////////////////
#if LV_COLOR_DEPTH != 32
    #error "LV_COLOR_DEPTH should be 32bit to match SquareLine Studio's settings"
#endif
#if LV_COLOR_16_SWAP !=0
    #error "LV_COLOR_16_SWAP should be 0 to match SquareLine Studio's settings"
#endif

///////////////////// STATIC FUNCTION ////////////////////

///////////////////// EXTERN FUNCTION ////////////////////
void __fm_view_update_low_voltage(ui_fm_para_t * fm_para, __u8 update)
{		
    if(fm_para->low_voltage_flag == update)
    {
        return;
    }

    fm_para->low_voltage_flag = update;
    
	if(fm_para->low_voltage_flag)
	{
		if(lv_obj_has_flag(ui_fm_play_top_low, LV_OBJ_FLAG_HIDDEN))
		{
			lv_obj_clear_flag(ui_fm_play_top_low, LV_OBJ_FLAG_HIDDEN);
		}
	}
	else
	{
		if(!lv_obj_has_flag(ui_fm_play_top_low, LV_OBJ_FLAG_HIDDEN))
			lv_obj_add_flag(ui_fm_play_top_low, LV_OBJ_FLAG_HIDDEN);
	}
}

void __fm_view_update_charge(ui_fm_para_t * fm_para, __u8 update)
{		
    if(fm_para->charge_flag == update)
    {
        return;
    }

    fm_para->charge_flag = update;
    
	if(fm_para->charge_flag)
	{
		if(lv_obj_has_flag(ui_fm_play_top_charge, LV_OBJ_FLAG_HIDDEN))
		{
			lv_obj_clear_flag(ui_fm_play_top_charge, LV_OBJ_FLAG_HIDDEN);
		}
	}
	else
	{
		if(!lv_obj_has_flag(ui_fm_play_top_charge, LV_OBJ_FLAG_HIDDEN))
			lv_obj_add_flag(ui_fm_play_top_charge, LV_OBJ_FLAG_HIDDEN);
	}
}

void __fm_view_update_eq(ui_fm_para_t * fm_para, __u8 update)
{		
    if(fm_para->eq_flag == update)
    {
        return;
    }

    fm_para->eq_flag = update;
    
	if(fm_para->eq_flag)
	{
		if(lv_obj_has_flag(ui_fm_play_top_eq, LV_OBJ_FLAG_HIDDEN))
		{
			lv_obj_clear_flag(ui_fm_play_top_eq, LV_OBJ_FLAG_HIDDEN);
		}
	}
	else
	{
		if(!lv_obj_has_flag(ui_fm_play_top_eq, LV_OBJ_FLAG_HIDDEN))
			lv_obj_add_flag(ui_fm_play_top_eq, LV_OBJ_FLAG_HIDDEN);
	}
}

//P1    P2    P3   P4   P5    P6
void __fm_view_update_preset(ui_fm_para_t * fm_para, __u8 update)
{
	__u8 i=0, j=0, index=0;
	__u8 str[256];
	lv_obj_t * ui_list_item;
	lv_obj_t * ui_list_item_panel;
    lv_obj_t * ui_list_item_text_panel;
	lv_obj_t * ui_list_item_text;
	lv_obj_t * ui_list_item_text_content;
    lv_obj_t * ui_list_item_text_unit;
    __u32 frq_number;    
    __u8 frq_int_str[8]={0};
    __u8 frq_dec_str[8]={0};
	reg_fm_para_t* para;
	
	para = (reg_fm_para_t*)dsk_reg_get_para_by_app(REG_APP_FM);

	if(update && fm_para->ui_list_para.item_obj)
    {
        for(j=0;j<PRESET_PRE_BAND_MAX / 2;j++)
        {
            ui_list_item = lv_obj_get_child(fm_para->ui_list_para.item_obj, j);
            ui_list_item_panel = lv_obj_get_child(ui_list_item, 0);

            for(i=0;i<2;i++)
            {
                index = (j * 2 + i);
                ui_list_item_text_panel = lv_obj_get_child(ui_list_item_panel, i);
                ui_list_item_text_content = lv_obj_get_child(ui_list_item_text_panel, 1);
                
                //eLIBs_printf("curFM1_3AM1_2_index=%d, index=%d, fmchannelfreq=%d, FM1_3_AM1_2_freq=%d, curFM1_3AM1_2_id=%d\n", fm_para->curFM1_3AM1_2_index, index, fm_para->fmchannelfreq, para->FM1_3_AM1_2_freq[fm_para->curFM1_3AM1_2_id][index], fm_para->curFM1_3AM1_2_id);
                
                if(fm_para->curFM1_3AM1_2_id<MAX_FM_BAND)
                {
                    if(para->FM1_3_AM1_2_freq[fm_para->curFM1_3AM1_2_id][index] == 0)
                    {
                        sprintf(str, "+");
                        lv_obj_set_style_text_font(ui_list_item_text_content, lv_font_medium.font, LV_PART_MAIN | LV_STATE_DEFAULT);
                    }
                    else
                    {
                        //sprintf(str, "%d", para->FM1_3_AM1_2_freq[fm_para->curFM1_3AM1_2_id][index]);
                        frq_number = para->FM1_3_AM1_2_freq[fm_para->curFM1_3AM1_2_id][index]/(1000);
                        eLIBs_uint2str_dec(frq_number, frq_int_str);
                        frq_number = (para->FM1_3_AM1_2_freq[fm_para->curFM1_3AM1_2_id][index]%(1000))/100;
                        eLIBs_uint2str_dec(frq_number, frq_dec_str);
                        eLIBs_strcat(frq_int_str,".");
                        eLIBs_strcat(frq_int_str,frq_dec_str);
                        eLIBs_strcpy(str, frq_int_str);
                        lv_obj_set_style_text_font(ui_list_item_text_content, lv_font_large.font, LV_PART_MAIN | LV_STATE_DEFAULT);
                    }
                }
                else if(fm_para->curFM1_3AM1_2_id<MAX_AM_BAND)//AM/MW to do
                {
                    if(para->FM1_3_AM1_2_freq[fm_para->curFM1_3AM1_2_id][index] == 0)
                    {
                        sprintf(str, "+");
                        lv_obj_set_style_text_font(ui_list_item_text_content, lv_font_medium.font, LV_PART_MAIN | LV_STATE_DEFAULT);
                    }
                    else
                    {
                        sprintf(str, "%d", para->FM1_3_AM1_2_freq[fm_para->curFM1_3AM1_2_id][index]);
                        lv_obj_set_style_text_font(ui_list_item_text_content, lv_font_large.font, LV_PART_MAIN | LV_STATE_DEFAULT);
                    }
                }
                else
                {
                    if(para->FM1_3_AM1_2_freq[fm_para->curFM1_3AM1_2_id][index] == 0)
                    {
                        sprintf(str, "+");
                        lv_obj_set_style_text_font(ui_list_item_text_content, lv_font_medium.font, LV_PART_MAIN | LV_STATE_DEFAULT);
                    }
                    else
                    {
                        //sprintf(str, "%d", para->FM1_3_AM1_2_freq[fm_para->curFM1_3AM1_2_id][index]);
                        frq_number = para->FM1_3_AM1_2_freq[fm_para->curFM1_3AM1_2_id][index]/(1000);
                        eLIBs_uint2str_dec(frq_number, frq_int_str);
                        frq_number = (para->FM1_3_AM1_2_freq[fm_para->curFM1_3AM1_2_id][index]%(1000))/100;
                        eLIBs_uint2str_dec(frq_number, frq_dec_str);
                        eLIBs_strcat(frq_int_str,".");
                        eLIBs_strcat(frq_int_str,frq_dec_str);
                        frq_number = (para->FM1_3_AM1_2_freq[fm_para->curFM1_3AM1_2_id][index]%(1000))%(100)/10;
                        eLIBs_uint2str_dec(frq_number, frq_dec_str);
                        eLIBs_strcat(frq_int_str,frq_dec_str);
                        frq_number = (para->FM1_3_AM1_2_freq[fm_para->curFM1_3AM1_2_id][index]%(1000))%(100)%(10);
                        eLIBs_uint2str_dec(frq_number, frq_dec_str);
                        eLIBs_strcat(frq_int_str,frq_dec_str);   
                        eLIBs_strcpy(str, frq_int_str);
                        lv_obj_set_style_text_font(ui_list_item_text_content, lv_font_medium.font, LV_PART_MAIN | LV_STATE_DEFAULT);
                    }
                }
                
                lv_label_set_text(ui_list_item_text_content, str);

                if(fm_para->fmchannelfreq == para->FM1_3_AM1_2_freq[fm_para->curFM1_3AM1_2_id][index])
                {
                    sprintf(str, "P%d", index + 1);
                    //eLIBs_printf("ui_fm_play_disp_preset str=%s\n", str);
                    lv_obj_clear_flag(ui_fm_play_disp_preset, LV_OBJ_FLAG_HIDDEN);     /// Flags
                    lv_label_set_text(ui_fm_play_disp_preset, str);
                }
            }
        }
    }
}

void __fm_view_update_loc(ui_fm_para_t * fm_para, __bool update)
{
	char temp_string[16] = {0};
	reg_init_para_t* para;
	para= (reg_init_para_t*)dsk_reg_get_para_by_app(REG_APP_INIT);

	if(ui_fm_play_disp_freq_loc == NULL)
		return;
		
	if(update)
	{
		if(para->local_seek_on_off)
		{
			if(lv_obj_has_flag(ui_fm_play_disp_freq_loc, LV_OBJ_FLAG_HIDDEN))
			{
				lv_obj_clear_flag(ui_fm_play_disp_freq_loc, LV_OBJ_FLAG_HIDDEN);
			}
		}
		else
		{
			if(!lv_obj_has_flag(ui_fm_play_disp_freq_loc, LV_OBJ_FLAG_HIDDEN))
				lv_obj_add_flag(ui_fm_play_disp_freq_loc, LV_OBJ_FLAG_HIDDEN);
		}
		
	}
}

void __fm_view_update_ST(ui_fm_para_t * fm_para, __bool update)
{

}

void __fm_view_update_af(ui_fm_para_t * fm_para, __bool update)
{

}

void __fm_view_update_ta(ui_fm_para_t * fm_para, __bool update)
{

}

void __fm_view_show_rds_pty(ui_fm_para_t * fm_para,__bool show_status)
{
	char pty_data[64] = {0};
	reg_init_para_t* init_para;
	reg_fm_para_t* para;
	
	init_para= (reg_init_para_t*)dsk_reg_get_para_by_app(REG_APP_INIT);
	para = (reg_fm_para_t*)dsk_reg_get_para_by_app(REG_APP_FM);

	if(fm_para->fine_tune_mode)
		return;
		
	if(para->cur_rds_status&RDS_IS_TRAFFIC) 
	{
		if(para->cur_pty_type == 0x1F)
		{
			if(init_para->area_type==TUNER_TYPE_K)
			{

				if(init_para->area==dUSA1_BAND)
				{
					eLIBs_strcpy(pty_data,fm_res_rbds_pty_string[para->cur_pty_type]);	
				}
				else
				{
					eLIBs_strcpy(pty_data,fm_res_rds_pty_string[para->cur_pty_type]);
				}
			}	
			else if(init_para->area_type==TUNER_TYPE_E)
				eLIBs_strcpy(pty_data,fm_res_rds_pty_string[para->cur_pty_type]);
			else if(init_para->area_type==TUNER_TYPE_M)
			{

				if(init_para->area==dUSA1_BAND)
				{
					eLIBs_strcpy(pty_data,fm_res_rbds_pty_string[para->cur_pty_type]);	
				}
				else
				{
					eLIBs_strcpy(pty_data,fm_res_rds_pty_string[para->cur_pty_type]);
				}
			}
		}
		else
			dsk_langres_get_menu_text(STRING_FM_TRAFFIC, pty_data, GUI_NAME_MAX);
	}
	else if(init_para->rds_on_off  && fm_para->curFM1_3AM1_2_id < dMAX_BAND/*MAX_FM_BAND*/)
	{
		if(init_para->area_type==TUNER_TYPE_K)
		{
			if(init_para->area==dOTHERS_BAND)
			{
				eLIBs_strcpy(pty_data,fm_res_rds_pty_string[para->cur_pty_type]);
			}
			else if(init_para->area==dUSA1_BAND)
			{
				eLIBs_strcpy(pty_data,fm_res_rbds_pty_string[para->cur_pty_type]);	
			}
			else
			{
				eLIBs_strcpy(pty_data,fm_res_rds_pty_string[para->cur_pty_type]);
			}
		}	
		else if(init_para->area_type==TUNER_TYPE_E)
			eLIBs_strcpy(pty_data,fm_res_rds_pty_string[para->cur_pty_type]);
		else if(init_para->area_type==TUNER_TYPE_M)
		{
			if(init_para->area==dOTHERS_BAND)
			{
				eLIBs_strcpy(pty_data,fm_res_rds_pty_string[para->cur_pty_type]);
			}
			else if(init_para->area==dUSA1_BAND)
			{
				eLIBs_strcpy(pty_data,fm_res_rbds_pty_string[para->cur_pty_type]);	
			}
			else
			{
				eLIBs_strcpy(pty_data,fm_res_rds_pty_string[para->cur_pty_type]);
			}
		}
	}
	else
		show_status = 0;
	
	if(show_status)
	{
		//lv_label_set_text(ui_fm_pty, pty_data);
	}
	
}

int trimSpace(char *inbuf, char *outbuf)
{
	char *in = inbuf;
	char *out = outbuf;
	int ret = 0;
	int i;
	int inLen = strlen(in);
	if (!inbuf || !outbuf)
	{
		ret = -1;
		__msg("func trimSpace(char *inbuf, char *outbuf) err: ret = %d->function parameter is null.\n", ret);
		return ret;
	}
	else
	{
		ret = 0;
		for (i = 0; i < inLen; i++)
		{
			if (in[i] != ' ')
			{
				*out++ = in[i];
			}
		}
	}
	return ret;
}

void __fm_view_show_rds_ps(ui_fm_para_t * fm_para,__bool show_status)
{
	char ps_data[32] = {0};
	char pty_data[32] = {0};
	char temp_data[32] = {0};
	int i;
	reg_init_para_t* init_para;
	reg_fm_para_t* para;
	init_para= (reg_init_para_t*)dsk_reg_get_para_by_app(REG_APP_INIT);
    para = (reg_fm_para_t*)dsk_reg_get_para_by_app(REG_APP_FM);
		
	if(init_para->rds_on_off  && fm_para->curFM1_3AM1_2_id < dMAX_BAND)
	{
		if(para->cur_rds_status&RDS_PS_OK)
		{
			__u8 i=0,j=0;
			for(i=0;i<8;i++)
			{
				if(para->ps_data[i] != 0x20)
					j=i;
			}
			eLIBs_strcpy(ps_data,para->ps_data);
			ps_data[j+1] = '\0';
			
			__fm_msg("para->ps_data=%s\n",para->ps_data);
			if( fm_para->number_play)
			{
				if(para->FM1_3_AM1_2_ps[fm_para->curFM1_3AM1_2_id][fm_para->curFM1_3AM1_2_index].ps_existed == 0)
				{
					para->FM1_3_AM1_2_ps[fm_para->curFM1_3AM1_2_id][fm_para->curFM1_3AM1_2_index].ps_existed = 1;
					eLIBs_strcpy(para->FM1_3_AM1_2_ps[fm_para->curFM1_3AM1_2_id][fm_para->curFM1_3AM1_2_index].ps, ps_data);
				}
			}
		}
	}
	else  
		show_status = 0; 

	
	if(show_status)
	{
		trimSpace(ps_data,temp_data);
		//lv_label_set_text(ui_fm_ps, temp_data);
	}
	
}

void __fm_view_update_fmam_band(ui_fm_para_t * fm_para)
{
	
	return;
}

void __fm_view_update_frq(ui_fm_para_t * fm_para)
{
	__u32 frq_number;
	__u8 i=0;
	
	reg_fm_para_t* para;
	
	para = (reg_fm_para_t*)dsk_reg_get_para_by_app(REG_APP_FM);

	__u8 frq_int_str[8]={0};
	__u8 frq_dec_str[8]={0};

	if(fmchannelfreq != fm_para->fmchannelfreq)
    {
		//__fm_msg("fm_para->fmchannelfreq=%d\n", fm_para->fmchannelfreq);
		fmchannelfreq = fm_para->fmchannelfreq;
        
		//__fm_msg("fm_para->curFM1_3AM1_2_id=%d, MAX_FM_BAND=%d\n", fm_para->curFM1_3AM1_2_id, MAX_FM_BAND);
		if(fm_para->curFM1_3AM1_2_id<MAX_FM_BAND)
		{
			frq_number = fm_para->fmchannelfreq/(1000);
			eLIBs_uint2str_dec(frq_number, frq_int_str);
			frq_number = (fm_para->fmchannelfreq%(1000))/100;
			eLIBs_uint2str_dec(frq_number, frq_dec_str);
			eLIBs_strcat(frq_int_str,".");
			eLIBs_strcat(frq_int_str,frq_dec_str);
            		lv_obj_set_style_text_font(ui_fm_play_disp_freq_num, lv_font_xxxlarge.font, LV_PART_MAIN | LV_STATE_DEFAULT);//&ui_font_Archivo_80
			lv_label_set_text(ui_fm_play_disp_freq_num, frq_int_str);
		}
		else if(fm_para->curFM1_3AM1_2_id<MAX_AM_BAND)//AM/MW to do
		{
			eLIBs_uint2str_dec(fm_para->fmchannelfreq, frq_int_str);
            		lv_obj_set_style_text_font(ui_fm_play_disp_freq_num, lv_font_xxxlarge.font, LV_PART_MAIN | LV_STATE_DEFAULT);//&ui_font_Archivo_80
			lv_label_set_text(ui_fm_play_disp_freq_num, frq_int_str);
		}
        	else
	        {
				frq_number = fm_para->fmchannelfreq/(1000);
				eLIBs_uint2str_dec(frq_number, frq_int_str);
				frq_number = (fm_para->fmchannelfreq%(1000))/100;
				eLIBs_uint2str_dec(frq_number, frq_dec_str);
				eLIBs_strcat(frq_int_str,".");
				eLIBs_strcat(frq_int_str,frq_dec_str);
				frq_number = (fm_para->fmchannelfreq%(1000))%(100)/10;
	    			eLIBs_uint2str_dec(frq_number, frq_dec_str);
	    			eLIBs_strcat(frq_int_str,frq_dec_str);
	    			frq_number = (fm_para->fmchannelfreq%(1000))%(100)%(10);
	        		eLIBs_uint2str_dec(frq_number, frq_dec_str);
	        		eLIBs_strcat(frq_int_str,frq_dec_str);            
	            		lv_obj_set_style_text_font(ui_fm_play_disp_freq_num, lv_font_xlarge_b.font, LV_PART_MAIN | LV_STATE_DEFAULT);//&ui_font_Archivo_80
				lv_label_set_text(ui_fm_play_disp_freq_num, frq_int_str);
	        }

        for(i = 0; i < PRESET_PRE_BAND_MAX; i++)
        {
            if(fm_para->fmchannelfreq == para->FM1_3_AM1_2_freq[fm_para->curFM1_3AM1_2_id][i])
            {
                sprintf(frq_dec_str, "P%d", i + 1);
                lv_obj_clear_flag(ui_fm_play_disp_preset, LV_OBJ_FLAG_HIDDEN);     /// Flags
                lv_label_set_text(ui_fm_play_disp_preset, frq_dec_str);
                break;
            }
        }

        if(i >= PRESET_PRE_BAND_MAX)
        {
            if(!lv_obj_has_flag(ui_fm_play_disp_preset, LV_OBJ_FLAG_HIDDEN))
                lv_obj_add_flag(ui_fm_play_disp_preset, LV_OBJ_FLAG_HIDDEN);     /// Flags
        }
    }
}

void __fm_view_update_Mhz_Khz(ui_fm_para_t * fm_para)
{
	char ucStringBuf[50] = {0};

	if(ui_fm_play_disp_freq_unit == NULL)
		return;

	    if(fm_para->curFM1_3AM1_2_id<MAX_FM_BAND)
	    {
	        eLIBs_strcpy(ucStringBuf, "MHz");
	    }
	    else if(fm_para->curFM1_3AM1_2_id<MAX_AM_BAND)
	    {
	        eLIBs_strcpy(ucStringBuf, "KHz");
	    }
	    else
	        eLIBs_strcpy(ucStringBuf, "MHz");

    	lv_label_set_text(ui_fm_play_disp_freq_unit, ucStringBuf);

}

void __fm_view_update_seek_string(ui_fm_para_t * fm_para, __u8 show_status)
{
	
}

void __fm_view_update_manual_string(ui_fm_para_t * fm_para, __u8 show_status)
{

}

void __fm_view_clear_rds_status(ui_fm_para_t * fm_para) //mllee 130325
{

}

void __fm_view_clear_pty_status(ui_fm_para_t * fm_para) //mllee 130325
{

}

void fmplay_set_area(ui_fm_para_t * fm_para, __u8 band_area)
{
	fm_para->area = band_area;
	switch(band_area)
	{
		case dUSA1_BAND:
			fm_para->fm_frq_min = FM_MIN_FRQ_USA;
			fm_para->fm_frq_max = FM_MAX_FRQ_USA;
			fm_para->am_frq_min = AM_MIN_FRQ_USA;
			fm_para->am_frq_max = AM_MAX_FRQ_USA;
			fm_para->fm_tuning_step = FM_TUNING_STEP_USA;
			fm_para->am_tuning_step = AM_TUNING_STEP_USA;
			break;

	/*	case BAND_AREA_OIRT:
#ifdef SPECIAL_OIRT_FM3_FRQ_LIMITED_ENABLE
			if(fm_para->curFM1_3AM1_2_id == 2)
			{
				fm_para->fm_frq_min = FM3_MIN_FRQ_OIRT;
				fm_para->fm_frq_max = FM3_MAX_FRQ_OIRT;
			}
			else
			{
				fm_para->fm_frq_min = FM_MIN_FRQ_OIRT;
				fm_para->fm_frq_max = FM_MAX_FRQ_OIRT;
			}
#else
			fm_para->fm_frq_min = FM_MIN_FRQ_OIRT;
			fm_para->fm_frq_max = FM_MAX_FRQ_OIRT;
#endif
			fm_para->am_frq_min = AM_MIN_FRQ_OIRT;
			fm_para->am_frq_max = AM_MAX_FRQ_OIRT;
			fm_para->fm_tuning_step = FM_TUNING_STEP_OIRT;
			fm_para->am_tuning_step = AM_TUNING_STEP_OIRT;
			break;*/

		case dLATAM1_BAND:
			fm_para->fm_frq_min = FM_MIN_FRQ_LATIN;
			fm_para->fm_frq_max = FM_MAX_FRQ_LATIN;
			fm_para->am_frq_min = AM_MIN_FRQ_LATIN;
			fm_para->am_frq_max = AM_MAX_FRQ_LATIN;
			fm_para->fm_tuning_step = FM_TUNING_STEP_LATIN;
			fm_para->am_tuning_step = AM_TUNING_STEP_LATIN;
			break;
		case dOTHERS_BAND:
		case dASIA_BAND:
			fm_para->fm_frq_min = FM_MIN_FRQ_ASIA;
			fm_para->fm_frq_max = FM_MAX_FRQ_ASIA;
			fm_para->am_frq_min = AM_MIN_FRQ_ASIA;
			fm_para->am_frq_max = AM_MAX_FRQ_ASIA;
			fm_para->fm_tuning_step = FM_TUNING_STEP_ASIA;
			fm_para->am_tuning_step = AM_TUNING_STEP_ASIA;
			break;

		case dJAPAN_BAND:
			fm_para->fm_frq_min = FM_MIN_FRQ_JAP;
			fm_para->fm_frq_max = FM_MAX_FRQ_JAP;
			fm_para->am_frq_min = AM_MIN_FRQ_JAP;
			fm_para->am_frq_max = AM_MAX_FRQ_JAP;
			fm_para->fm_tuning_step = FM_TUNING_STEP_OIRT;
			fm_para->am_tuning_step = AM_TUNING_STEP_OIRT;
			break;

		case dMID_EAST_BAND:
			fm_para->fm_frq_min = FM_MIN_FRQ_MIDDLE_EAST;
			fm_para->fm_frq_max = FM_MAX_FRQ_MIDDLE_EAST;
			fm_para->am_frq_min = AM_MIN_FRQ_MIDDLE_EAST;
			fm_para->am_frq_max = AM_MAX_FRQ_MIDDLE_EAST;
			fm_para->fm_tuning_step = FM_TUNING_STEP_MIDDLE_EAST;
			fm_para->am_tuning_step = AM_TUNING_STEP_MIDDLE_EAST;
			break;
			
		case dEUROPE1_BAND:
			fm_para->fm_frq_min = FM_MIN_FRQ_EUROPE1;
			fm_para->fm_frq_max = FM_MAX_FRQ_EUROPE1;
			fm_para->am_frq_min = AM_MIN_FRQ_EUROPE1;
			fm_para->am_frq_max = AM_MAX_FRQ_EUROPE1;
			fm_para->fm_tuning_step = FM_TUNING_STEP_EUROPE1;
			fm_para->am_tuning_step = AM_TUNING_STEP_EUROPE1;
			break;
		
		case dEUROPE2_BAND:	
			fm_para->fm_frq_min = FM_MIN_FRQ_EUROPE2;
			fm_para->fm_frq_max = FM_MAX_FRQ_EUROPE2;
			fm_para->am_frq_min = AM_MIN_FRQ_EUROPE2;
			fm_para->am_frq_max = AM_MAX_FRQ_EUROPE2;
			fm_para->fm_tuning_step = FM_TUNING_STEP_EUROPE2;
			fm_para->am_tuning_step = AM_TUNING_STEP_EUROPE2;
			break;

		case dOCEANIA_BAND:	
			fm_para->fm_frq_min = FM_MIN_FRQ_OCEANIA	;
			fm_para->fm_frq_max = FM_MAX_FRQ_OCEANIA	;
			fm_para->am_frq_min = AM_MIN_FRQ_OCEANIA	;
			fm_para->am_frq_max = AM_MAX_FRQ_OCEANIA	;
			fm_para->fm_tuning_step = FM_TUNING_STEP_OCEANIA	;
			fm_para->am_tuning_step = AM_TUNING_STEP_OCEANIA	;
			break;	

		case dHONGKONG_BAND:	
			fm_para->fm_frq_min = FM_MIN_FRQ_HONG_KONG;
			fm_para->fm_frq_max = FM_MAX_FRQ_HONG_KONG;
			fm_para->am_frq_min = AM_MIN_FRQ_HONG_KONG;
			fm_para->am_frq_max = AM_MAX_FRQ_HONG_KONG;
			fm_para->fm_tuning_step = FM_TUNING_STEP_HONG_KONG;
			fm_para->am_tuning_step = AM_TUNING_STEP_HONG_KONG;
			break;		
			default:
				fm_para->fm_frq_min = FM_MIN_FRQ_EUROPE;
				fm_para->fm_frq_max = FM_MAX_FRQ_EUROPE;
				fm_para->am_frq_min = AM_MIN_FRQ_EUROPE;
				fm_para->am_frq_max = AM_MAX_FRQ_EUROPE;
				fm_para->fm_tuning_step = FM_TUNING_STEP_EUROPE;
				fm_para->am_tuning_step = AM_TUNING_STEP_EUROPE;
				break;
	}
}

static __s32 fm_view_list_draw_listview_item_text(__lv_draw_para_t *draw_param, lv_obj_t * text, __u8 index)
{
	int ret;
    __s32 item_index;
    __u8 text_index;
	__u8 ucStringBuf[GUI_NAME_MAX + 1]={0};
	ui_fm_para_t *fm_para;
	reg_fm_para_t* para;
	
	para = (reg_fm_para_t*)dsk_reg_get_para_by_app(REG_APP_FM);
	fm_para = (ui_fm_para_t *)draw_param->attr;		

	//__fm_msg("curFM1_3AM1_2_id = %d\n",fm_para->curFM1_3AM1_2_id);
	//__fm_msg("draw_param->index = %d\n",draw_param->index);

    item_index = draw_param->index * 2 + index / 2;
    text_index = index % 2;

	if(text_index == 0)
	{
		sprintf(ucStringBuf, "%d", item_index + 1);
		//__fm_msg("str = %s\n",ucStringBuf);

		if((fm_para->curFM1_3AM1_2_index == item_index) && (fm_para->fmchannelfreq == para->FM1_3_AM1_2_freq[fm_para->curFM1_3AM1_2_id][item_index]))
		{
			//lv_obj_set_style_text_color(text, lv_color_hex(0x555555), LV_PART_MAIN | LV_STATE_DEFAULT);
		}
		else
		{
			//lv_obj_set_style_text_color(text, lv_color_hex(0x333333), LV_PART_MAIN | LV_STATE_DEFAULT);
		}

		lv_label_set_text(text, ucStringBuf);
	}
    else if(text_index == 1)
    {
        __u32 frq_number;    
        __u8 frq_int_str[8]={0};
        __u8 frq_dec_str[8]={0};
        
    	if((fm_para->curFM1_3AM1_2_id<MAX_FM_BAND))
    	{
            if(para->FM1_3_AM1_2_freq[fm_para->curFM1_3AM1_2_id][item_index] == 0)
            {
                sprintf(ucStringBuf, "+");
                lv_obj_set_style_text_font(text, lv_font_medium.font, LV_PART_MAIN | LV_STATE_DEFAULT);
            }
            else
            {
    		    //sprintf(ucStringBuf, "%d", para->FM1_3_AM1_2_freq[fm_para->curFM1_3AM1_2_id][item_index]);

    			frq_number = para->FM1_3_AM1_2_freq[fm_para->curFM1_3AM1_2_id][item_index]/(1000);
    			eLIBs_uint2str_dec(frq_number, frq_int_str);
    			frq_number = (para->FM1_3_AM1_2_freq[fm_para->curFM1_3AM1_2_id][item_index]%(1000))/100;
    			eLIBs_uint2str_dec(frq_number, frq_dec_str);
    			eLIBs_strcat(frq_int_str,".");
    			eLIBs_strcat(frq_int_str,frq_dec_str);
                eLIBs_strcpy(ucStringBuf, frq_int_str);
                lv_obj_set_style_text_font(text, lv_font_large.font, LV_PART_MAIN | LV_STATE_DEFAULT);
            }
    	}
		else if(fm_para->curFM1_3AM1_2_id<MAX_AM_BAND)//AM/MW to do
    	{
            if(para->FM1_3_AM1_2_freq[fm_para->curFM1_3AM1_2_id][item_index] == 0)
            {
                sprintf(ucStringBuf, "+");
                lv_obj_set_style_text_font(text, lv_font_medium.font, LV_PART_MAIN | LV_STATE_DEFAULT);
            }
            else
            {
    		    sprintf(ucStringBuf, "%d", para->FM1_3_AM1_2_freq[fm_para->curFM1_3AM1_2_id][item_index]);
                lv_obj_set_style_text_font(text, lv_font_large.font, LV_PART_MAIN | LV_STATE_DEFAULT);
            }
    	}
        else
        {
            if(para->FM1_3_AM1_2_freq[fm_para->curFM1_3AM1_2_id][item_index] == 0)
            {
                sprintf(ucStringBuf, "+");
                lv_obj_set_style_text_font(text, lv_font_medium.font, LV_PART_MAIN | LV_STATE_DEFAULT);
            }
            else
            {
                frq_number = para->FM1_3_AM1_2_freq[fm_para->curFM1_3AM1_2_id][item_index]/(1000);
                eLIBs_uint2str_dec(frq_number, frq_int_str);
                frq_number = (para->FM1_3_AM1_2_freq[fm_para->curFM1_3AM1_2_id][item_index]%(1000))/100;
                eLIBs_uint2str_dec(frq_number, frq_dec_str);
                eLIBs_strcat(frq_int_str,".");
                eLIBs_strcat(frq_int_str,frq_dec_str);
                frq_number = (para->FM1_3_AM1_2_freq[fm_para->curFM1_3AM1_2_id][item_index]%(1000))%(100)/10;
                eLIBs_uint2str_dec(frq_number, frq_dec_str);
                eLIBs_strcat(frq_int_str,frq_dec_str);
                frq_number = (para->FM1_3_AM1_2_freq[fm_para->curFM1_3AM1_2_id][item_index]%(1000))%(100)%(10);
                eLIBs_uint2str_dec(frq_number, frq_dec_str);
                eLIBs_strcat(frq_int_str,frq_dec_str);   
                eLIBs_strcpy(ucStringBuf, frq_int_str);
    		    //sprintf(ucStringBuf, "%d", para->FM1_3_AM1_2_freq[fm_para->curFM1_3AM1_2_id][item_index]);
                lv_obj_set_style_text_font(text, lv_font_large.font, LV_PART_MAIN | LV_STATE_DEFAULT);
            }
    	}

    	if((fm_para->curFM1_3AM1_2_index == item_index) && (fm_para->fmchannelfreq == para->FM1_3_AM1_2_freq[fm_para->curFM1_3AM1_2_id][item_index]))
    	{
    		//lv_obj_set_style_text_color(text, lv_color_hex(0xffffff), LV_PART_MAIN | LV_STATE_DEFAULT);
    	}
    	else
    	{
    		//lv_obj_set_style_text_color(text, lv_color_hex(0x555555), LV_PART_MAIN | LV_STATE_DEFAULT);
    	}
    }
    else  if(text_index == 2)
    {
    	if((fm_para->curFM1_3AM1_2_id<MAX_FM_BAND))
        {
            if(para->FM1_3_AM1_2_freq[fm_para->curFM1_3AM1_2_id][item_index] == 0)
                sprintf(ucStringBuf, " ");
            else
                sprintf(ucStringBuf, "MHz");
        }
		else if(fm_para->curFM1_3AM1_2_id<MAX_AM_BAND)//AM/MW to do
        {
            if(para->FM1_3_AM1_2_freq[fm_para->curFM1_3AM1_2_id][item_index] == 0)
                sprintf(ucStringBuf, " ");
            else
                sprintf(ucStringBuf, "KHz");
        }
        else
        {
            if(para->FM1_3_AM1_2_freq[fm_para->curFM1_3AM1_2_id][item_index] == 0)
                sprintf(ucStringBuf, " ");
            else
                sprintf(ucStringBuf, "MHz");
        }

        if((fm_para->curFM1_3AM1_2_index == item_index) && (fm_para->fmchannelfreq == para->FM1_3_AM1_2_freq[fm_para->curFM1_3AM1_2_id][item_index]))
        {
            //lv_obj_set_style_text_color(text, lv_color_hex(0xffffff), LV_PART_MAIN | LV_STATE_DEFAULT);
        }
        else
        {
            //lv_obj_set_style_text_color(text, lv_color_hex(0x555555), LV_PART_MAIN | LV_STATE_DEFAULT);
        }
    }

	__fm_msg("ucStringBuf = %s\n",ucStringBuf);
    lv_label_set_text(text, ucStringBuf);
	
	return EPDK_OK;
}

static __s32 fm_view_list_draw_listview_item(__lv_draw_para_t *draw_param)
{
	__s32 ret = SOURCE_ITEM_TYPE_PRESET;

	return ret; 
}

static __s32 fm_view_func_draw_listview_item_switch(__lv_draw_para_t *draw_param, lv_obj_t * obj)
{
	int ret;
	__s32 index;
	ui_fm_para_t *fm_para;
	
	fm_para = (ui_fm_para_t *)draw_param->attr;	
    
    index = draw_param->index;
    __fm_msg("fm_view_func_draw_listview_item_switch index=%d\n", index);

    switch(index)
    {    
        case FM_FUNC_LOCAL:
        {
            lv_obj_set_state(obj, LV_STATE_CHECKED, fm_para->local_onoff);
        }
        break;

        case FM_FUNC_RBDS:
        {
            lv_obj_set_state(obj, LV_STATE_CHECKED, fm_para->rds_onoff);
        }
        break;

        case FM_FUNC_AF:
        {
            lv_obj_t * ui_list_item_arrow = lv_obj_get_parent(obj);
            lv_obj_t * ui_list_item_content_panel = lv_obj_get_parent(ui_list_item_arrow);
            lv_obj_t * ui_list_item_panel = lv_obj_get_parent(ui_list_item_content_panel);
            lv_obj_t * ui_list_item = lv_obj_get_parent(ui_list_item_panel);
            
            lv_obj_update_flag(ui_list_item, LV_OBJ_FLAG_HIDDEN, fm_para->rds_onoff ? 0 : 1);
            lv_obj_set_state(obj, LV_STATE_CHECKED, fm_para->af_onoff);
            __fm_msg("fm_para->rds_onoff=%d\n", fm_para->rds_onoff);
        }
        break;
    }
	
	return EPDK_OK;
}

static __s32 fm_view_func_draw_listview_item_text(__lv_draw_para_t *draw_param, lv_obj_t * text, __u8 index)
{
	int ret;
	__u8 ucStringBuf[GUI_NAME_MAX + 1]={0};
	ui_fm_para_t *fm_para;
	
	fm_para = (ui_fm_para_t *)draw_param->attr;		

    dsk_langres_get_menu_text(fm_res_func_item_string[draw_param->index], ucStringBuf, GUI_NAME_MAX);

	__fm_msg("ucStringBuf = %s\n",ucStringBuf);
    lv_label_set_text(text, ucStringBuf);
	
	return EPDK_OK;
}

static __s32 fm_view_func_draw_listview_item(__lv_draw_para_t *draw_param)
{
	__s32 ret = SOURCE_ITEM_TYPE_ENTER;
	__s32 index = draw_param->index;

    switch(index)
    {
        case FM_FUNC_AUTO_STORE:
        {
            ret = SOURCE_ITEM_TYPE_ENTER;
        }
        break;
    
        case FM_FUNC_LOCAL:
        {
            ret = SOURCE_ITEM_TYPE_ARROW;
        }
        break;

        case FM_FUNC_RBDS:
        {
            ret = SOURCE_ITEM_TYPE_ARROW;
        }
        break;

        case FM_FUNC_AF:
        {
            ret = SOURCE_ITEM_TYPE_ARROW;
        }
        break;
    }

	return ret; 
}

static __s32 fm_view_draw_listview_item_text(__lv_draw_para_t *draw_param, lv_obj_t * text, __u8 index)
{
	__s32 ret = 0;
	ui_fm_para_t * fm_para;
	
	fm_para = (ui_fm_para_t *)draw_param->attr;				//for draw the picture  in different media type

	switch(fm_para->menu_win)
	{
		case FM_MENU_WIN_LIST:
		{
			 ret = fm_view_list_draw_listview_item_text(draw_param, text, index);
		}
		break;
	
		case FM_MENU_WIN_FUNC:
		{
			ret = fm_view_func_draw_listview_item_text(draw_param, text, index);
		}
		break;
	}

	return EPDK_OK;	
}

static __s32 fm_view_draw_listview_item(__lv_draw_para_t *draw_param)
{
	__s32 ret = 0;
	ui_fm_para_t * fm_para;
	
	fm_para = (ui_fm_para_t *)draw_param->attr;				//for draw the picture  in different media type

	switch(fm_para->menu_win)
	{
		case FM_MENU_WIN_LIST:
		{
			 ret = fm_view_list_draw_listview_item(draw_param);
		}
		break;
	
		case FM_MENU_WIN_FUNC:
		{
			ret = fm_view_func_draw_listview_item(draw_param);
		}
		break;
	}

	return ret;	
}

static void * ui_fm_draw_item(__lv_draw_para_t *draw_param)
{	
	__s32 ret;
	ui_fm_para_t * fm_para = (ui_fm_para_t *)draw_param->attr;

	//__fm_msg("fm_para->ui_list_para.item_obj=0x%x\n", fm_para->ui_list_para.item_obj);

    __s32 item_type;

	lv_obj_t * ui_list_item = lv_obj_create(fm_para->ui_list_para.item_obj);
    lv_obj_remove_style_all(ui_list_item);
	lv_obj_set_width(ui_list_item, fm_para->ui_list_para.item_width);
	lv_obj_set_height(ui_list_item, fm_para->ui_list_para.item_height);
    lv_obj_add_flag(ui_list_item, LV_OBJ_FLAG_EVENT_BUBBLE | LV_OBJ_FLAG_GESTURE_BUBBLE);      /// Flags
	lv_obj_clear_flag(ui_list_item, LV_OBJ_FLAG_SCROLLABLE);	  /// Flags
	lv_obj_set_style_radius(ui_list_item, 0, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_bg_color(ui_list_item, lv_color_hex(0x000000), LV_PART_MAIN | LV_STATE_DEFAULT);
	lv_obj_set_style_bg_opa(ui_list_item, 255, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_border_width(ui_list_item, 2, LV_PART_MAIN | LV_STATE_FOCUSED);
    lv_obj_set_style_border_color(ui_list_item, lv_color_hex(0xff0000), LV_PART_MAIN | LV_STATE_FOCUSED);
    lv_obj_set_style_border_opa(ui_list_item, 255, LV_PART_MAIN | LV_STATE_FOCUSED);

	//eLIBs_printf("ui_list_item=0x%x\n", ui_list_item);
    
    item_type = fm_view_draw_listview_item(draw_param);

    if(item_type == SOURCE_ITEM_TYPE_PRESET)
    {
        lv_obj_t * ui_list_item_panel = lv_obj_create(ui_list_item);
        lv_obj_remove_style_all(ui_list_item_panel);
        lv_obj_set_x(ui_list_item_panel, lv_pct(1));
        lv_obj_set_height(ui_list_item_panel, lv_pct(97));
        lv_obj_set_width(ui_list_item_panel, lv_pct(97));
        lv_obj_set_align(ui_list_item_panel, LV_ALIGN_LEFT_MID);
        lv_obj_set_flex_flow(ui_list_item_panel, LV_FLEX_FLOW_ROW);
		lv_obj_set_flex_align(ui_list_item_panel, LV_FLEX_ALIGN_SPACE_EVENLY, LV_FLEX_ALIGN_CENTER, LV_FLEX_ALIGN_CENTER);
        lv_obj_add_flag(ui_list_item_panel, LV_OBJ_FLAG_EVENT_BUBBLE | LV_OBJ_FLAG_GESTURE_BUBBLE);      /// Flags
        lv_obj_clear_flag(ui_list_item_panel, LV_OBJ_FLAG_CLICKABLE | LV_OBJ_FLAG_SCROLLABLE);      /// Flags
        lv_obj_set_style_bg_opa(ui_list_item_panel, 0, LV_PART_MAIN | LV_STATE_DEFAULT);
        
        for(__u8 i = 0; i < 2; i++)
        {
            lv_obj_t * ui_list_item_text_panel = lv_obj_create(ui_list_item_panel);
            //lv_obj_remove_style_all(ui_list_item_text_panel);
            lv_obj_set_height(ui_list_item_text_panel, lv_pct(100));
            lv_obj_set_width(ui_list_item_text_panel, lv_pct(49));
            lv_obj_set_align(ui_list_item_text_panel, LV_ALIGN_CENTER);
            lv_obj_clear_flag(ui_list_item_text_panel, LV_OBJ_FLAG_SCROLLABLE);      /// Flags
            lv_obj_add_flag(ui_list_item_text_panel, LV_OBJ_FLAG_EVENT_BUBBLE | LV_OBJ_FLAG_GESTURE_BUBBLE);      /// Flags
            lv_obj_set_style_bg_color(ui_list_item_text_panel, lv_color_hex(0x303030), LV_PART_MAIN | LV_STATE_DEFAULT);
            lv_obj_set_style_bg_opa(ui_list_item_text_panel, 255, LV_PART_MAIN | LV_STATE_DEFAULT);
            //lv_obj_set_style_bg_image_src(ui_list_item_text_panel, ui_fm_get_res(fm_para, FM_THMEM_TEMP_COM_LIST_BG_BMP), LV_PART_MAIN | LV_STATE_DEFAULT);
            lv_obj_set_style_border_width(ui_list_item_text_panel, 0, LV_PART_MAIN | LV_STATE_DEFAULT);
            lv_obj_set_style_bg_color(ui_list_item_text_panel, lv_color_hex(0x303030), LV_PART_MAIN | LV_STATE_PRESSED);
            lv_obj_set_style_bg_opa(ui_list_item_text_panel, 255, LV_PART_MAIN | LV_STATE_PRESSED);
            lv_obj_set_style_bg_grad_color(ui_list_item_text_panel, lv_color_hex(0xD90011), LV_PART_MAIN | LV_STATE_PRESSED);
            lv_obj_set_style_bg_main_stop(ui_list_item_text_panel, 150, LV_PART_MAIN | LV_STATE_PRESSED);
            lv_obj_set_style_bg_grad_stop(ui_list_item_text_panel, 255, LV_PART_MAIN | LV_STATE_PRESSED);
            lv_obj_set_style_bg_grad_dir(ui_list_item_text_panel, LV_GRAD_DIR_VER, LV_PART_MAIN | LV_STATE_PRESSED);
            //eLIBs_printf("ui_list_item_text_panel=0x%x\n", ui_list_item_text_panel);
            lv_obj_set_style_border_width(ui_list_item_text_panel, 2, LV_PART_MAIN | LV_STATE_FOCUSED);
            lv_obj_set_style_border_color(ui_list_item_text_panel, lv_color_hex(0xff0000), LV_PART_MAIN | LV_STATE_FOCUSED);
            lv_obj_set_style_border_opa(ui_list_item_text_panel, 255, LV_PART_MAIN | LV_STATE_FOCUSED);
            __fm_msg("ui_list_item_text_panel=%p\n", ui_list_item_text_panel);

            lv_obj_t * ui_list_item_text_index = lv_label_create(ui_list_item_text_panel);
            lv_obj_set_width(ui_list_item_text_index, LV_SIZE_CONTENT);
            lv_obj_set_height(ui_list_item_text_index, LV_SIZE_CONTENT);
            lv_obj_set_align(ui_list_item_text_index, LV_ALIGN_TOP_LEFT);
            lv_label_set_long_mode(ui_list_item_text_index, LV_LABEL_LONG_CLIP);
            lv_obj_set_style_text_color(ui_list_item_text_index, lv_color_hex(0xFFFFFF), LV_PART_MAIN | LV_STATE_DEFAULT);
            lv_obj_set_style_text_opa(ui_list_item_text_index, 255, LV_PART_MAIN | LV_STATE_DEFAULT);
            lv_obj_set_style_text_font(ui_list_item_text_index, lv_font_small.font, LV_PART_MAIN | LV_STATE_DEFAULT);
            fm_view_draw_listview_item_text(draw_param, ui_list_item_text_index, i * 2 + 0);   
            
            lv_obj_t * ui_list_item_text_content = lv_label_create(ui_list_item_text_panel);
            lv_obj_set_width(ui_list_item_text_content, LV_SIZE_CONTENT);
            lv_obj_set_height(ui_list_item_text_content, LV_SIZE_CONTENT);
            lv_obj_set_align(ui_list_item_text_content, LV_ALIGN_CENTER);
            lv_label_set_long_mode(ui_list_item_text_content, LV_LABEL_LONG_CLIP);
            lv_obj_set_style_text_color(ui_list_item_text_content, lv_color_hex(0xFFFFFF), LV_PART_MAIN | LV_STATE_DEFAULT);
            lv_obj_set_style_text_opa(ui_list_item_text_content, 255, LV_PART_MAIN | LV_STATE_DEFAULT);
            lv_obj_set_style_text_font(ui_list_item_text_content, lv_font_medium.font, LV_PART_MAIN | LV_STATE_DEFAULT);
            fm_view_draw_listview_item_text(draw_param, ui_list_item_text_content, i * 2 + 1); 
        }

        lv_obj_add_event_cb(ui_list_item, ui_event_fm_preset_screen, LV_EVENT_ALL, fm_para);
    }
    else
    {
        lv_obj_t * ui_list_item_panel = lv_obj_create(ui_list_item);
        lv_obj_remove_style_all(ui_list_item_panel);
        lv_obj_set_x(ui_list_item_panel, lv_pct(1));
        lv_obj_set_height(ui_list_item_panel, lv_pct(95));
        lv_obj_set_width(ui_list_item_panel, lv_pct(97));
        lv_obj_set_align(ui_list_item_panel, LV_ALIGN_LEFT_MID);
        lv_obj_add_flag(ui_list_item_panel, LV_OBJ_FLAG_EVENT_BUBBLE | LV_OBJ_FLAG_GESTURE_BUBBLE);      /// Flags
        lv_obj_clear_flag(ui_list_item_panel, LV_OBJ_FLAG_CLICKABLE | LV_OBJ_FLAG_SCROLLABLE);      /// Flags
        lv_obj_set_style_bg_color(ui_list_item_panel, lv_color_hex(0x303030), LV_PART_MAIN | LV_STATE_DEFAULT);
        lv_obj_set_style_bg_opa(ui_list_item_panel, 255, LV_PART_MAIN | LV_STATE_DEFAULT);
        //lv_obj_set_style_bg_image_src(ui_list_item_panel, ui_fm_get_res(fm_para, FM_THMEM_TEMP_COM_LIST_BG_BMP), LV_PART_MAIN | LV_STATE_DEFAULT);
        lv_obj_set_style_border_width(ui_list_item_panel, 0, LV_PART_MAIN | LV_STATE_DEFAULT);
        lv_obj_set_style_bg_color(ui_list_item_panel, lv_color_hex(0x303030), LV_PART_MAIN | LV_STATE_PRESSED);
        lv_obj_set_style_bg_opa(ui_list_item_panel, 255, LV_PART_MAIN | LV_STATE_PRESSED);
        lv_obj_set_style_bg_grad_color(ui_list_item_panel, lv_color_hex(0xD90011), LV_PART_MAIN | LV_STATE_PRESSED);
        lv_obj_set_style_bg_main_stop(ui_list_item_panel, 150, LV_PART_MAIN | LV_STATE_PRESSED);
        lv_obj_set_style_bg_grad_stop(ui_list_item_panel, 255, LV_PART_MAIN | LV_STATE_PRESSED);
        lv_obj_set_style_bg_grad_dir(ui_list_item_panel, LV_GRAD_DIR_VER, LV_PART_MAIN | LV_STATE_PRESSED);
        //lv_obj_set_style_bg_opa(ui_list_item_panel, 0, LV_PART_MAIN | LV_STATE_DEFAULT);
        //lv_obj_set_style_bg_color(ui_list_item_panel, lv_color_hex(0xd90011), LV_PART_MAIN | LV_STATE_PRESSED);
        //lv_obj_set_style_bg_opa(ui_list_item_panel, 255, LV_PART_MAIN | LV_STATE_PRESSED);
        //lv_obj_set_style_radius(ui_list_item_panel, 5, LV_PART_MAIN | LV_STATE_DEFAULT);

        lv_obj_t * ui_list_item_text_panel = lv_obj_create(ui_list_item_panel);
        lv_obj_remove_style_all(ui_list_item_text_panel);
    	lv_obj_set_x(ui_list_item_text_panel, lv_pct(3));
        lv_obj_set_height(ui_list_item_text_panel, lv_pct(100));
        lv_obj_set_width(ui_list_item_text_panel, lv_pct(75));
    	lv_obj_set_align(ui_list_item_text_panel, LV_ALIGN_LEFT_MID);
        lv_obj_clear_flag(ui_list_item_text_panel, LV_OBJ_FLAG_CLICKABLE | LV_OBJ_FLAG_SCROLLABLE);      /// Flags
        lv_obj_set_style_bg_opa(ui_list_item_text_panel, 0, LV_PART_MAIN | LV_STATE_DEFAULT);
        
    	lv_obj_t * ui_list_item_text = lv_label_create(ui_list_item_text_panel);
    	lv_obj_set_width(ui_list_item_text, lv_pct(100));//lv_pct(89)
    	lv_obj_set_height(ui_list_item_text, LV_SIZE_CONTENT);	  /// 0
    	lv_obj_set_align(ui_list_item_text, LV_ALIGN_LEFT_MID);
    	lv_label_set_long_mode(ui_list_item_text, LV_LABEL_LONG_CLIP);
    	lv_obj_set_style_text_color(ui_list_item_text, lv_color_hex(0xFFFFFF), LV_PART_MAIN | LV_STATE_DEFAULT);
    	lv_obj_set_style_text_opa(ui_list_item_text, 255, LV_PART_MAIN | LV_STATE_DEFAULT);
    	lv_obj_set_style_text_font(ui_list_item_text, lv_font_small.font, LV_PART_MAIN | LV_STATE_DEFAULT);
    	fm_view_draw_listview_item_text(draw_param, ui_list_item_text, SOURCE_ITEM_TEXT_TYPE_TEXT);	

        lv_obj_t * ui_list_item_content_panel = lv_obj_create(ui_list_item_panel);
        lv_obj_remove_style_all(ui_list_item_content_panel);
        lv_obj_set_width(ui_list_item_content_panel, lv_pct(25));
        lv_obj_set_height(ui_list_item_content_panel, lv_pct(100));
        lv_obj_set_align(ui_list_item_content_panel, LV_ALIGN_RIGHT_MID);
        lv_obj_clear_flag(ui_list_item_content_panel, LV_OBJ_FLAG_CLICKABLE | LV_OBJ_FLAG_SCROLLABLE);        /// Flags
        lv_obj_add_flag(ui_list_item_content_panel, LV_OBJ_FLAG_EVENT_BUBBLE | LV_OBJ_FLAG_GESTURE_BUBBLE);    /// Flags
        lv_obj_set_style_bg_opa(ui_list_item_content_panel, 0, LV_PART_MAIN | LV_STATE_DEFAULT);

        switch(item_type)
        {
            case SOURCE_ITEM_TYPE_ENTER:
            {
                lv_obj_t * ui_list_item_enter = lv_image_create(ui_list_item_content_panel);
                lv_image_set_src(ui_list_item_enter, ui_fm_get_res(fm_para, FM_TEMP_COM_MENU_ENTER_BMP));
                lv_obj_set_width(ui_list_item_enter, LV_SIZE_CONTENT);   /// 1
                lv_obj_set_height(ui_list_item_enter, LV_SIZE_CONTENT);    /// 1
                lv_obj_set_x(ui_list_item_enter, lv_pct(30));    /// 1
                lv_obj_set_align(ui_list_item_enter, LV_ALIGN_CENTER);
                lv_obj_remove_flag(ui_list_item_enter, LV_OBJ_FLAG_SCROLLABLE);      /// Flags
    		}
            break;

            case SOURCE_ITEM_TYPE_ARROW:
            {
                lv_obj_clear_flag(ui_list_item, LV_OBJ_FLAG_CLICK_FOCUSABLE);      /// Flags
                
    		    lv_obj_t * ui_list_item_arrow = lv_obj_create(ui_list_item_content_panel);
                lv_obj_set_width(ui_list_item_arrow, 118);
                lv_obj_set_height(ui_list_item_arrow, 69);
                lv_obj_set_align(ui_list_item_arrow, LV_ALIGN_CENTER);
                lv_obj_remove_flag(ui_list_item_arrow, LV_OBJ_FLAG_SCROLLABLE);      /// Flags
                lv_obj_add_flag(ui_list_item_arrow, LV_OBJ_FLAG_CHECKABLE);      /// Flags
                lv_obj_set_style_bg_color(ui_list_item_arrow, lv_color_hex(0xFFFFFF), LV_PART_MAIN | LV_STATE_DEFAULT);
                lv_obj_set_style_bg_opa(ui_list_item_arrow, 0, LV_PART_MAIN | LV_STATE_DEFAULT);
                lv_obj_set_style_bg_image_src(ui_list_item_arrow, ui_fm_get_res(fm_para, FM_THMEM_TEMP_COM_LIST_OFF_BMP),
                                              LV_PART_MAIN | LV_STATE_DEFAULT);
                lv_obj_set_style_border_width(ui_list_item_arrow, 0, LV_PART_MAIN | LV_STATE_DEFAULT);
                lv_obj_set_style_bg_image_src(ui_list_item_arrow, ui_fm_get_res(fm_para, FM_THMEM_TEMP_COM_LIST_ON_BMP),
                                      LV_PART_MAIN | LV_STATE_CHECKED);
                lv_obj_set_style_border_width(ui_list_item_arrow, 2, LV_PART_MAIN | LV_STATE_FOCUSED);
                lv_obj_set_style_border_color(ui_list_item_arrow, lv_color_hex(0xff0000), LV_PART_MAIN | LV_STATE_FOCUSED);
                lv_obj_set_style_border_opa(ui_list_item_arrow, 255, LV_PART_MAIN | LV_STATE_FOCUSED);

                lv_obj_t * ui_list_item_arrow_switch = lv_switch_create(ui_list_item_arrow);
                lv_obj_set_width(ui_list_item_arrow_switch, 90);
                lv_obj_set_height(ui_list_item_arrow_switch, 45);
                lv_obj_set_align(ui_list_item_arrow_switch, LV_ALIGN_CENTER);
                lv_obj_add_flag(ui_list_item_arrow_switch, LV_OBJ_FLAG_EVENT_BUBBLE);    /// Flags
                lv_obj_set_style_bg_color(ui_list_item_arrow_switch, lv_color_hex(0x272727), LV_PART_MAIN | LV_STATE_DEFAULT);
                lv_obj_set_style_bg_opa(ui_list_item_arrow_switch, 0, LV_PART_MAIN | LV_STATE_DEFAULT);
                lv_obj_set_style_bg_color(ui_list_item_arrow_switch, lv_color_hex(0x222121), LV_PART_MAIN | LV_STATE_CHECKED);
                lv_obj_set_style_bg_opa(ui_list_item_arrow_switch, 0, LV_PART_MAIN | LV_STATE_CHECKED);
                
                lv_obj_set_style_bg_color(ui_list_item_arrow_switch, lv_color_hex(0x272727), LV_PART_INDICATOR | LV_STATE_DEFAULT);
                lv_obj_set_style_bg_opa(ui_list_item_arrow_switch, 0, LV_PART_INDICATOR | LV_STATE_DEFAULT);
                lv_obj_set_style_bg_color(ui_list_item_arrow_switch, lv_color_hex(0x222121), LV_PART_INDICATOR | LV_STATE_CHECKED);
                lv_obj_set_style_bg_opa(ui_list_item_arrow_switch, 0, LV_PART_INDICATOR | LV_STATE_CHECKED);

                lv_obj_set_style_bg_color(ui_list_item_arrow_switch, lv_color_hex(0x575757), LV_PART_KNOB | LV_STATE_DEFAULT);
                lv_obj_set_style_bg_opa(ui_list_item_arrow_switch, 255, LV_PART_KNOB | LV_STATE_DEFAULT);
                lv_obj_set_style_bg_color(ui_list_item_arrow_switch, lv_color_hex(0xFFFFFF), LV_PART_KNOB | LV_STATE_CHECKED);
                lv_obj_set_style_bg_opa(ui_list_item_arrow_switch, 255, LV_PART_KNOB | LV_STATE_CHECKED);
                
                lv_group_remove_obj(ui_list_item_arrow_switch);

                __fm_msg("ui_list_item_arrow_switch=%p\n", ui_list_item_arrow_switch);
                fm_view_func_draw_listview_item_switch(draw_param, ui_list_item_arrow_switch);
                    
                lv_obj_add_event_cb(ui_list_item_arrow_switch, ui_event_fm_menu_arrow_screen, LV_EVENT_ALL, fm_para);
    		}
            break;
        }

        lv_obj_add_event_cb(ui_list_item, ui_event_fm_menu_item_screen, LV_EVENT_ALL, fm_para);
    }
    
	return (void *)ui_list_item;
}

static void fm_view_funtion_listbar_init(ui_fm_para_t * fm_para)
{
	if(!fm_para->ui_list_para.item_obj)
	{
	    fm_para->ui_list_para.list_attr = (void *)fm_para;
		fm_para->ui_list_para.lbar_draw = (__lv_draw_item)ui_fm_draw_item;
#ifdef USE_MENU_ANIM
        fm_para->ui_list_para.use_anim = 1;
#endif

		__fm_msg("ui_fm_draw_item=0x%x, fm_para->ui_list_para.menu_obj=0x%x, fm_para->menu_win=%d\n", ui_fm_draw_item, fm_para->ui_list_para.menu_obj, fm_para->menu_win);
        if(fm_para->menu_win == FM_MENU_WIN_LIST)
            fm_para->fm_menu_obj = ui_menu_create(&fm_para->ui_list_para, APP_FM_PRESET_MENU_ID);
        else
		    fm_para->fm_menu_obj = ui_menu_create(&fm_para->ui_list_para, APP_FM_ID);

        ui_source_menu_sel_group_update(fm_para->source_para, &fm_para->ui_list_para, fm_view_draw_listview_item, fm_para, SOURCE_MENU_GROUP_ADD, 0);
        ui_source_group_update(fm_para->source_para, 0);

        if(fm_para->theme > THEME_NIGHT)
            traverse_children_with_callback(fm_para, fm_para->fm_menu_obj, fm_para->theme_bmp_start, fm_para->theme_bmp_end, fm_para->theme_bmp_start, fm_para->theme_bmp_end, fm_para->ui_fm_res_para.lv_fm_icon, ui_fm_get_res, NULL, 0);
#ifdef USE_MENU_ANIM
        ui_menu_on_anim(&fm_para->ui_list_para, fm_para->menu_anim);
#endif
	}
}

static void fm_view_funtion_listbar_uninit(ui_fm_para_t * fm_para)
{
	if(fm_para->ui_list_para.item_obj != NULL)
	{
		__fm_msg("fm_view_funtion_listbar_uninit\n");
#ifdef USE_MENU_ANIM
        ui_menu_off_anim(fm_para->menu_anim);
#endif
        ui_source_menu_sel_group_update(fm_para->source_para, &fm_para->ui_list_para, fm_view_draw_listview_item, fm_para, SOURCE_MENU_GROUP_REMOVE_ALL_FLAG_ALL, 0);

		__fm_msg("fm_para->menu_anim[0]=%p\n", fm_para->menu_anim[0]);
		lv_obj_clean(fm_para->ui_list_para.item_obj);//删除父对象下的子对象，不删除本身
		ui_menu_destroy(fm_para->fm_menu_obj);
        fm_para->fm_menu_obj = NULL;
		fm_para->ui_list_para.item_obj = NULL;
	}
}

static void ui_fm_bottom_group_update(ui_fm_para_t * fm_para, __u8 flag, __u8 index, __u8 force)
{
    if(fm_para->bottom_fouse_flag == flag && !force)
    {
        __fm_msg("ui_fm_item_group_update cur is flag=%d, force=%d, not need change!!!!\n", flag, force);
        
        return;
    }
    
    fm_para->bottom_fouse_flag = flag;
    __fm_msg("fm_para->bottom_fouse_flag=%d, index=%d\n", fm_para->bottom_fouse_flag, index);

    if(flag == 1)
    {
        ui_source_sel_group_update(fm_para->source_para->ui_bottom, fm_para->bottom_fouse_index, 1);
        ui_source_group_update(fm_para->source_para, 0);
        fm_para->bottom_fouse_index = 0;
    }
    else if(flag == 2)
    {
        fm_para->bottom_fouse_index = index;
        ui_source_group_update(fm_para->source_para, 0);
        ui_source_sel_group_update(fm_para->source_para->ui_bottom, 0, 0);
    }
    else
    {
        ui_source_group_update(fm_para->source_para, 1);
        ui_source_sel_group_update(fm_para->source_para->ui_bottom, 0, 0);
        fm_para->bottom_fouse_index = 0;
    }
}

static void ui_fm_normal_win_open(ui_fm_para_t * fm_para)
{
    fm_para->menu_win = FM_MENU_WIN_NONE;
    
    ui_fm_source_title(fm_para, NULL, 1);
    if(fm_para->bottom_fouse_flag == 0)
        ui_source_group_update(fm_para->source_para, 1);
}

static void ui_fm_normal_win_close(ui_fm_para_t * fm_para)
{

}

static void ui_fm_list_win_open(ui_fm_para_t * fm_para)
{
    fm_para->menu_win = FM_MENU_WIN_LIST;

    ui_source_title(fm_para->source_para, "Favorite", 0);
    ui_source_sub_open(fm_para->source_para);
    
    ui_fm_menu_screen_init(fm_para);
    fm_para->ui_list_para.menu_obj = ui_fm_menu;
    fm_para->ui_list_para.item_cnt = PRESET_PRE_BAND_MAX / 2;
    fm_view_funtion_listbar_init(fm_para);
    ui_fm_bottom_group_update(fm_para, 2, 0, 0);
}

static void ui_fm_list_win_close(ui_fm_para_t * fm_para)
{
    ui_fm_bottom_group_update(fm_para, 1, 0, 0);
    fm_view_funtion_listbar_uninit(fm_para);
    ui_fm_menu_screen_uninit(fm_para);
    
    ui_source_sub_close(fm_para->source_para);
}

static void ui_fm_func_win_open(ui_fm_para_t * fm_para)
{
	reg_root_para_t *last_root_para;
	
	last_root_para = (reg_root_para_t*)dsk_reg_get_para_by_app(REG_APP_ROOT);
    
    fm_para->menu_win = FM_MENU_WIN_FUNC;

    ui_fm_source_title(fm_para, "Menu", 0);
    //if(fm_para->source_para->rig_select_flag == SOURCE_FUNC)
    //{
        ui_fm_func_menu_screen_init(fm_para);
        fm_para->ui_list_para.menu_obj = ui_fm_func_menu;

        if(app_root_mcu_get_source() == ID_WORK_SOURCE_FM)
            fm_para->ui_list_para.item_cnt = FM_FUNC_MAX;
        else if(app_root_mcu_get_source() == ID_WORK_SOURCE_AM)
            fm_para->ui_list_para.item_cnt = FM_FUNC_RBDS;
        else if(app_root_mcu_get_source() == ID_WORK_SOURCE_WB)
            fm_para->ui_list_para.item_cnt = FM_FUNC_LOCAL;
        
        fm_view_funtion_listbar_init(fm_para);
    //}
    //ui_source_sub_open(fm_para->source_para);
}

static void ui_fm_func_win_close(ui_fm_para_t * fm_para)
{
    fm_view_funtion_listbar_uninit(fm_para);
}

static void ui_fm_direct_win_open(ui_fm_para_t * fm_para)
{
    fm_para->menu_win = FM_MENU_WIN_DIRECT;

    ui_source_title(fm_para->source_para, "Direct Tuning", 0);
    ui_source_sub_open(fm_para->source_para);

    ui_fm_direct_screen_init(fm_para);
    ui_fm_bottom_group_update(fm_para, 2, 3, 0);
}

static void ui_fm_direct_win_close(ui_fm_para_t * fm_para)
{
    ui_fm_bottom_group_update(fm_para, 1, 0, 0);
    ui_fm_direct_screen_uninit(fm_para);
    ui_source_sub_close(fm_para->source_para);
}

void ui_fm_switch_win(ui_fm_para_t * fm_para, __u32 menu_win)
{
	__s32 ret;

    __fm_msg("fm_para->menu_win=%d, menu_win=%d\n", fm_para->menu_win, menu_win);

    if(fm_para->menu_win == menu_win)
    {
        __fm_msg("cur is menu_win %d, not need change!!!!\n", menu_win);
        return;
    }
        
    switch(fm_para->menu_win)
    {
        case FM_MENU_WIN_NONE:
        {
            ui_fm_normal_win_close(fm_para);
        }
        break;

        case FM_MENU_WIN_DIRECT:
        {
            ui_fm_direct_win_close(fm_para);
        }
        break;

        case FM_MENU_WIN_LIST:
        {
            ui_fm_list_win_close(fm_para);
        }
        break;
    
        case FM_MENU_WIN_FUNC:
        {
            ui_fm_func_win_close(fm_para);
        }
        break;
    }

    switch(menu_win)
    {
        case FM_MENU_WIN_NONE:
        {
            ui_fm_normal_win_open(fm_para);
        }
        break;

        case FM_MENU_WIN_DIRECT:
        {
            ui_fm_direct_win_open(fm_para);
        }
        break;

        case FM_MENU_WIN_LIST:
        {
            ui_fm_list_win_open(fm_para);
        }
        break;
    
        case FM_MENU_WIN_FUNC:
        {
            ui_fm_func_win_open(fm_para);
        }
        break;
    }
}

#ifdef SWITCH_BAND_TIPS
__s32 __create_fm_tips_dialog(ui_fm_para_t * fm_para,__s32 title_id,__s32 content_id,__u32 time_out)
{
	__s32 str[8] = {0};
	str[0] = title_id;
	str[1] = content_id;
	fm_para->switch_band = 1;
	if(fm_para->h_frm_loading==NULL && fm_para->h_frm_app_tips == NULL)
	{
		fm_para->h_frm_loading = ui_dialog_create(lv_layer_top(), FM_DIALOG_ID, ADLG_NOTHING, str,time_out, 0);	
	}
	return EPDK_OK;
}

__s32 __fm_tips_dialog_cancel(ui_fm_para_t * fm_para)
{
	__fm_msg("fm_tips_dialog_cancel\n");
	fm_para->switch_band = 0;
	if(fm_para->h_frm_loading)
	{
		ui_dialog_destroy(fm_para->h_frm_loading); 
		fm_para->h_frm_loading = NULL;
	}
	return EPDK_OK;
}
#endif

__s32 __create_fm_app_tips_dialog(ui_fm_para_t *fm_para,__s32 title_id,__s32 content_id,__u32 time_out)
{
	__s32 str[8] = {0};
	str[0] = title_id;
	str[1] = content_id;

	fm_tips_dialog_cancel(fm_para);

	if(fm_para->h_frm_app_tips==NULL)
	{
		fm_para->h_frm_app_tips = ui_dialog_create(lv_layer_top(), FM_DIALOG_ID, ADLG_APP_AVS_STATE, str,time_out, 0);
	}
	return EPDK_OK;
}


__s32 __fm_app_tips_dialog_cancel(ui_fm_para_t *fm_para)
{
	if(fm_para->h_frm_app_tips)
	{
		ui_dialog_destroy(fm_para->h_frm_app_tips); 
		fm_para->h_frm_app_tips = NULL;
	}
	return EPDK_OK;
}

static void ui_fm_tips_tmr_del(lv_timer_t* tmr)
{
	ui_fm_para_t * fm_para = (ui_fm_para_t *)lv_timer_get_user_data(tmr);
	reg_root_para_t *last_root_para;
	reg_system_para_t*system_para;
	
	last_root_para = (reg_root_para_t*)dsk_reg_get_para_by_app(REG_APP_ROOT);
	system_para = (reg_system_para_t*)dsk_reg_get_para_by_app(REG_APP_SYSTEM);
	__fm_msg("DSK_MSG_FM_SWITCH_BAND_DONE\n");
	
	fm_tips_dialog_cancel(fm_para);
	if(!last_root_para->is_mute_status && !system_para->power_off_status)
		app_root_unmute_switch_source();
	fm_para->tips_tmr = NULL;
}

void __ui_fm_tips_tmr_create(ui_fm_para_t * fm_para)
{
	if(fm_para->tips_tmr==NULL)
	{
		fm_para->tips_tmr = lv_timer_create(ui_fm_tips_tmr_del, 800, fm_para);
		lv_timer_set_repeat_count(fm_para->tips_tmr, 1);
	}
}

static __s32  ui_fm_key_proc(ui_fm_para_t * fm_para)
{
	__u16 index = 0;
	__u8 manual_cancel_auto_pty_seek = 0;
	static int last_key = 0;
	
	__fm_msg("fm_para->indev_data->event_code=%d..\n",fm_para->indev_data->event_code);
	__fm_msg("fm_para->indev_data->key=%d..\n",fm_para->indev_data->key);

	if(KEY_DOWN_ACTION == fm_para->indev_data->event_code)
	{
		last_key = fm_para->indev_data->key;
		
		switch(fm_para->indev_data->key)
		{
			case GUI_MSG_KEY_LEFT:
			{
				
			}
			break;
			case GUI_MSG_KEY_RIGHT:
			{
				
			}
			break;
			default:
				break;
		}
	}
	else if(KEY_REPEAT_ACTION == fm_para->indev_data->event_code)
	{
		switch(fm_para->indev_data->key)
		{
			case GUI_MSG_KEY_LONGRIGHT:
                break;
			case GUI_MSG_KEY_LONGLEFT:
				break;	
            case GUI_MSG_KEY_LONGMENU:
            {
                if(GUI_MSG_KEY_MENU == last_key)
                {
                    if(fm_para->menu_win == FM_MENU_WIN_NONE)
                    {
                        ui_fm_open_bar_menu_event_handler(fm_para);
                    }
                    else
                    {
                        ui_fm_top_right_event_handler(fm_para);
                    }
                }
            }
            break;
		}
		
		last_key = fm_para->indev_data->key;
	}
	else if(KEY_UP_ACTION == fm_para->indev_data->event_code)
	{
		switch(fm_para->indev_data->key)
		{
            case GUI_MSG_KEY_ENTER:
            {
                if(fm_para->menu_win == FM_MENU_WIN_NONE && fm_para->source_para->menu_win == SOURCE_MENU_WIN_NONE && fm_para->bottom_fouse_flag == 0)
                {
                    ui_fm_bottom_group_update(fm_para, 1, 0, 0);
                }
            }
            break;

			case GUI_MSG_KEY_RIGHT:
			{
				if(GUI_MSG_KEY_RIGHT == last_key)
				{
                    __fm_msg("fm_para->source_para->menu_win=%d..\n",fm_para->source_para->menu_win);
                    if(fm_para->source_para->menu_win != SOURCE_MENU_WIN_ZONE_AUDIO)
                        app_root_send2mcu_key(KEY_TOUCH_NEXT);
				}
			}
			break;
			
			case GUI_MSG_KEY_LEFT:
			{
				if(GUI_MSG_KEY_LEFT == last_key)
				{
                    __fm_msg("fm_para->source_para->menu_win=%d..\n",fm_para->source_para->menu_win);
                    if(fm_para->source_para->menu_win != SOURCE_MENU_WIN_ZONE_AUDIO)
                        app_root_send2mcu_key(KEY_TOUCH_PREV);
				}
			}
			break;

            case GUI_MSG_KEY_MENU:
            {
                if(GUI_MSG_KEY_MENU == last_key)
                {
                    root_app_para_t * root_app_para = app_root_get_para();
                    
                    eLIBs_printf("menu_win=%d root_app_para->home_store=%d\n", fm_para->menu_win, root_app_para->home_store);
                    if(fm_para->menu_win == FM_MENU_WIN_NONE 
                    && fm_para->source_para->menu_win == SOURCE_MENU_WIN_NONE 
                    && (root_app_para->home_store == 1))
                    {
                        if(fm_para->bottom_fouse_flag == 1)
                        {
                            ui_fm_bottom_group_update(fm_para, 0, 0, 0);
                        }
                        else
                        {
                            ui_fm_source_event_handler(fm_para);
                        }
                    }
                    else
                    {
                        if(fm_para->menu_win > FM_MENU_WIN_NONE 
                        && fm_para->menu_win <= FM_MENU_WIN_FUNC 
                        && fm_para->source_para->menu_group_flag == SOURCE_MENU_GROUP_ADD 
                        && fm_para->source_para->menu_win != SOURCE_MENU_WIN_MODE)
                        {
                            ui_source_menu_sel_group_update(fm_para->source_para, &fm_para->ui_list_para, fm_view_draw_listview_item, fm_para, SOURCE_MENU_GROUP_REMOVE_SUB, 0);
                        }
                        else
                        {
                            ui_fm_top_left_event_handler(fm_para);
                        }
                    }
                }
            }
            break;

            case GUI_MSG_KEY_PLAY_PAUSE:
            {
                if(GUI_MSG_KEY_PLAY_PAUSE == last_key)
                {

                }
            }
            break;
						
			default:
				break;
		}
		
		last_key = 0;
	}
	
	return EPDK_OK;
}

void ui_fm_set_obj_theme_src(void * para, lv_obj_t * obj, __u32 theme)
{
    ui_fm_para_t * fm_para = (ui_fm_para_t *)para;
    __u8 bg_change_flag = 1;
    __u32 i, bmp_cnt, icon;
    __u32 bmp_start, bmp_end;
    __u32 color = 0;
    void * style_bg_image_release_src = NULL;
    void * style_bg_image_pressed_src = NULL;
    void * image_src = NULL;
    void * imagebutton_release_src = NULL;
    void * imagebutton_pressed_src = NULL;
    void * imagebutton_checked_release_src = NULL;
    void * imagebutton_checked_pressed_src = NULL;

    bmp_start = fm_para->theme_bmp_start;
    bmp_end = fm_para->theme_bmp_end;
    bmp_cnt = bmp_end - bmp_start;
    
    //eLIBs_printf("obj=%p, bmp_start=%d, bmp_end=%d, bmp_cnt=%d\n", obj, bmp_start, bmp_end, bmp_cnt);
    
    style_bg_image_release_src = lv_obj_get_style_bg_image_src(obj, LV_PART_MAIN | LV_STATE_DEFAULT);
    style_bg_image_pressed_src = lv_obj_get_style_bg_image_src(obj, LV_PART_MAIN | LV_STATE_PRESSED);
    
    if(eLIBs_strcmp(obj->class_p->name,"label") == 0)
    {
        __fm_msg("0:label obj=%p, color=0x%x\n", obj, (lv_color_to_int(lv_obj_get_style_text_color(obj, LV_PART_MAIN | LV_STATE_DEFAULT))&0xFFFFFF));
        color = 0xFFFFFF - (lv_color_to_int(lv_obj_get_style_text_color(obj, LV_PART_MAIN | LV_STATE_DEFAULT))&0xFFFFFF);
        __fm_msg("1:label obj=%p, color=0x%x\n", obj, color);
        lv_obj_set_style_text_color(obj, lv_color_hex(color), LV_PART_MAIN | LV_STATE_DEFAULT);

        color = 0xFFFFFF - (lv_color_to_int(lv_obj_get_style_text_color(obj, LV_PART_MAIN | LV_STATE_CHECKED))&0xFFFFFF);
        lv_obj_set_style_text_color(obj, lv_color_hex(color), LV_PART_MAIN | LV_STATE_CHECKED);

    }
    else if(eLIBs_strcmp(obj->class_p->name,"image") == 0)
    {
        image_src = lv_img_get_src(obj);
    }
    else if(eLIBs_strcmp(obj->class_p->name,"imagebutton") == 0)
    {
        imagebutton_release_src = lv_imagebutton_get_src_middle(obj, LV_IMAGEBUTTON_STATE_RELEASED);
        imagebutton_pressed_src = lv_imagebutton_get_src_middle(obj, LV_IMAGEBUTTON_STATE_PRESSED);
        imagebutton_checked_release_src = lv_imagebutton_get_src_middle(obj, LV_IMAGEBUTTON_STATE_CHECKED_RELEASED);
        imagebutton_checked_pressed_src = lv_imagebutton_get_src_middle(obj, LV_IMAGEBUTTON_STATE_CHECKED_PRESSED);
    }
    else if(eLIBs_strcmp(obj->class_p->name,"slider") == 0)
    {
        color = 0xFFFFFF - (lv_color_to_int(lv_obj_get_style_bg_color(obj, LV_PART_INDICATOR | LV_STATE_DEFAULT))&0xFFFFFF);
        lv_obj_set_style_bg_color(obj, lv_color_hex(color), LV_PART_INDICATOR | LV_STATE_DEFAULT);        

        color = 0xFFFFFF - (lv_color_to_int(lv_obj_get_style_bg_color(obj, LV_PART_KNOB | LV_STATE_DEFAULT))&0xFFFFFF);
        lv_obj_set_style_bg_color(obj, lv_color_hex(color), LV_PART_KNOB | LV_STATE_DEFAULT);
    }

    if(bg_change_flag)
    {
        color = 0xFFFFFF - (lv_color_to_int(lv_obj_get_style_bg_color(obj, LV_PART_MAIN | LV_STATE_DEFAULT))&0xFFFFFF);
        lv_obj_set_style_bg_color(obj, lv_color_hex(color), LV_PART_MAIN | LV_STATE_DEFAULT);

        color = 0xFFFFFF - (lv_color_to_int(lv_obj_get_style_bg_grad_color(obj, LV_PART_MAIN | LV_STATE_DEFAULT))&0xFFFFFF);
        lv_obj_set_style_bg_grad_color(obj, lv_color_hex(color), LV_PART_MAIN | LV_STATE_DEFAULT);
    }

    for(i = bmp_start; i <= bmp_end; i++)
    {
        if(style_bg_image_release_src && style_bg_image_release_src == &fm_para->ui_fm_res_para.lv_fm_icon[i])
        {
            icon = FM_THMEM_TEMP_MEDIA_SETUP_REPEAT_B_BMP + (i - bmp_start);

            if(icon <= FM_THMEM_TEMP_COM_RIG_FUNC_ICON_BMP)
            {
                lv_obj_set_style_bg_image_src(obj, ui_fm_get_res(fm_para, icon), LV_PART_MAIN | LV_STATE_DEFAULT);
            }
        }

        if(style_bg_image_pressed_src && style_bg_image_pressed_src == &fm_para->ui_fm_res_para.lv_fm_icon[i])
        {
            icon = FM_THMEM_TEMP_MEDIA_SETUP_REPEAT_B_BMP + (i - bmp_start);

            if(icon <= FM_THMEM_TEMP_COM_RIG_FUNC_ICON_BMP)
            {
                lv_obj_set_style_bg_image_src(obj, ui_fm_get_res(fm_para, icon), LV_PART_MAIN | LV_STATE_PRESSED);
            }
        }

        if(image_src && image_src == &fm_para->ui_fm_res_para.lv_fm_icon[i])
        {
            icon = FM_THMEM_TEMP_MEDIA_SETUP_REPEAT_B_BMP + (i - bmp_start);
        
            if(icon <= FM_THMEM_TEMP_COM_RIG_FUNC_ICON_BMP)
            {
                lv_img_set_src(obj, ui_fm_get_res(fm_para, icon));
            }
        }

        if(imagebutton_release_src && imagebutton_release_src == &fm_para->ui_fm_res_para.lv_fm_icon[i])
        {
            icon = FM_THMEM_TEMP_MEDIA_SETUP_REPEAT_B_BMP + (i - bmp_start);
        
            if(icon <= FM_THMEM_TEMP_COM_RIG_FUNC_ICON_BMP)
            {
                lv_imagebutton_set_src(obj, LV_IMAGEBUTTON_STATE_RELEASED, NULL, ui_fm_get_res(fm_para, icon), NULL);
            }
        }

        if(imagebutton_pressed_src && imagebutton_pressed_src == &fm_para->ui_fm_res_para.lv_fm_icon[i])
        {
            icon = FM_THMEM_TEMP_MEDIA_SETUP_REPEAT_B_BMP + (i - bmp_start);
        
            if(icon <= FM_THMEM_TEMP_COM_RIG_FUNC_ICON_BMP)
            {
                lv_imagebutton_set_src(obj, LV_IMAGEBUTTON_STATE_PRESSED, NULL, ui_fm_get_res(fm_para, icon), NULL);
            }
        }
        
        if(imagebutton_checked_release_src && imagebutton_checked_release_src == &fm_para->ui_fm_res_para.lv_fm_icon[i])
        {
            icon = FM_THMEM_TEMP_MEDIA_SETUP_REPEAT_B_BMP + (i - bmp_start);
        
            if(icon <= FM_THMEM_TEMP_COM_RIG_FUNC_ICON_BMP)
            {
                lv_imagebutton_set_src(obj, LV_IMAGEBUTTON_STATE_CHECKED_RELEASED, NULL, ui_fm_get_res(fm_para, icon), NULL);
            }
        }

        if(imagebutton_checked_pressed_src && imagebutton_checked_pressed_src == &fm_para->ui_fm_res_para.lv_fm_icon[i])
        {
            icon = FM_THMEM_TEMP_MEDIA_SETUP_REPEAT_B_BMP + (i - bmp_start);
        
            if(icon <= FM_THMEM_TEMP_COM_RIG_FUNC_ICON_BMP)
            {
                lv_imagebutton_set_src(obj, LV_IMAGEBUTTON_STATE_CHECKED_PRESSED, NULL, ui_fm_get_res(fm_para, icon), NULL);
            }
        }
    }
}

static void ui_fm_night_theme(ui_fm_para_t * fm_para, __u32 theme)
{
	__fm_msg("ui_fm_night_theme\n");

    if(fm_para->theme == theme)
    {
        __fm_msg("cur is theme %d, not need change!!!!\n", theme);
        return;
    }
    
    if(theme == THEME_NIGHT)
    {
        FM_THMEM_TEMP_MEDIA_SETUP_REPEAT_B_BMP        = FM_TEMP_NIGHT_NIG_MEDIA_SETUP_REPEAT_B_BMP;  
        FM_THMEM_FM_ICON_BMP                          = FM_NIG_FM_ICON_BMP;
        FM_THMEM_AM_ICON_BMP                          = FM_NIG_AM_ICON_BMP;
        FM_THMEM_WB_ICON_BMP                          = FM_NIG_WB_ICON_BMP;

        FM_THEME_COM_BOT_BG_BMP                       = FM_COMMON_NIG_COM_BOT_BG_BMP;  

        FM_THEME_COM_BOT_ICON_BG_A_L_N_BMP            = FM_COMMON_NIG_COM_BOT_ICON_BG_A_L_N_BMP;
        FM_THEME_COM_BOT_ICON_BG_A_L_S_BMP            = FM_COMMON_NIG_COM_BOT_ICON_BG_A_L_S_BMP;
        
        FM_THEME_COM_BOT_LIST_N_BMP                   = FM_COMMON_NIG_COM_BOT_LIST_N_BMP;
        FM_THEME_COM_BOT_LIST_D_BMP                   = FM_COMMON_NIG_COM_BOT_LIST_D_BMP;
                
        FM_THEME_COM_BOT_DIRECT_N_BMP                 = FM_COMMON_NIG_COM_BOT_DIRECT_N_BMP;
        FM_THEME_COM_BOT_DIRECT_D_BMP                 = FM_COMMON_NIG_COM_BOT_DIRECT_D_BMP;

        FM_THMEM_TEMP_COM_LIST_OFF_BMP                = FM_TEMP_NIGHT_NIG_COM_LIST_OFF_BMP;          
        FM_THMEM_TEMP_MEDIA_DUAL_PHONE_A_BMP          = FM_TEMP_NIGHT_NIG_MEDIA_DUAL_PHONE_A_BMP;    
        FM_THMEM_TEMP_COM_RIG_BG_A_BMP                = FM_TEMP_NIGHT_NIG_COM_RIG_BG_A_BMP;          
        FM_THMEM_TEMP_COM_RIG_ARROW_A_BMP             = FM_TEMP_NIGHT_NIG_COM_RIG_ARROW_A_BMP;       
        FM_THMEM_TEMP_MEDIA_SETUP_REPEAT_A_BMP        = FM_TEMP_NIGHT_NIG_MEDIA_SETUP_REPEAT_A_BMP;  
        FM_THMEM_TEMP_COM_RIG_AUDIO_ICON_S_BMP        = FM_TEMP_NIGHT_NIG_COM_RIG_AUDIO_ICON_S_BMP;  
        FM_THMEM_TEMP_COM_RIG_AUDIO_ICON_N_BMP        = FM_TEMP_NIGHT_NIG_COM_RIG_AUDIO_ICON_N_BMP;  
        FM_THMEM_TEMP_MEDIA_DUAL_PHONE_B_BMP          = FM_TEMP_NIGHT_NIG_MEDIA_DUAL_PHONE_B_BMP;    
        FM_THMEM_TEMP_COM_TOP_BACK_ICON_BMP           = FM_TEMP_NIGHT_NIG_COM_TOP_BACK_ICON_BMP;     
        FM_THMEM_TEMP_COM_MEDIA_ALBUM_BG_BMP          = FM_TEMP_NIGHT_NIG_COM_MEDIA_ALBUM_BG_BMP;    
        FM_THMEM_TEMP_MEDIA_SETUP_RANDOM_A_BMP        = FM_TEMP_NIGHT_NIG_MEDIA_SETUP_RANDOM_A_BMP;  
        FM_THMEM_TEMP_COM_RIG_FUNC_ICON_S_BMP         = FM_TEMP_NIGHT_NIG_COM_RIG_FUNC_ICON_S_BMP;   
        FM_THMEM_TEMP_COM_RIG_ARROW_B_BMP             = FM_TEMP_NIGHT_NIG_COM_RIG_ARROW_B_BMP;       
        FM_THMEM_TEMP_COM_LOW_VOLTAGE_BMP             = FM_TEMP_NIGHT_NIG_COM_LOW_VOLTAGE_BMP;       
        FM_THMEM_TEMP_COM_RIG_BG_C_BMP                = FM_TEMP_NIGHT_NIG_COM_RIG_BG_C_BMP;          
        FM_THMEM_TEMP_COM_TOP_ICON_BG_N_BMP           = FM_TEMP_NIGHT_NIG_COM_TOP_ICON_BG_N_BMP;     
        FM_THMEM_TEMP_COM_LIST_ON_BMP                 = FM_TEMP_NIGHT_NIG_COM_LIST_ON_BMP;           
        FM_THMEM_TEMP_COM_LIST_BG_BMP                 = FM_TEMP_NIGHT_NIG_COM_LIST_BG_BMP;           
        FM_THMEM_TEMP_COM_RIG_BG_B_BMP                = FM_TEMP_NIGHT_NIG_COM_RIG_BG_B_BMP;          
        FM_THMEM_TEMP_COM_RIG_ICON_BG_N_BMP           = FM_TEMP_NIGHT_NIG_COM_RIG_ICON_BG_N_BMP;     
        FM_THMEM_TEMP_MEDIA_SETUP_RANDOM_B_BMP        = FM_TEMP_NIGHT_NIG_MEDIA_SETUP_RANDOM_B_BMP;  
        FM_THMEM_TEMP_COM_RIG_ZONE_ICON_BMP           = FM_TEMP_NIGHT_NIG_COM_RIG_ZONE_ICON_BMP;     
        FM_THMEM_TEMP_COM_RIG_ICON_BG_P_BMP           = FM_TEMP_NIGHT_NIG_COM_RIG_ICON_BG_P_BMP;     
        FM_THMEM_TEMP_COM_BG_BMP                      = FM_TEMP_NIGHT_NIG_COM_BG_BMP;                
        FM_THMEM_TEMP_COM_PD_CHARGE_BMP               = FM_TEMP_NIGHT_NIG_COM_PD_CHARGE_BMP;         
        FM_THMEM_TEMP_MEDIA_SETUP_REPEAT_C_BMP        = FM_TEMP_NIGHT_NIG_MEDIA_SETUP_REPEAT_C_BMP;  
        FM_THMEM_TEMP_COM_PUNCH_EQ_BMP                = FM_TEMP_NIGHT_NIG_COM_PUNCH_EQ_BMP;          
        FM_THMEM_TEMP_COM_TOP_SOURCE_ICON_BMP         = FM_TEMP_NIGHT_NIG_COM_TOP_SOURCE_ICON_BMP;   
        FM_THMEM_TEMP_COM_RIG_ARROW_C_BMP             = FM_TEMP_NIGHT_NIG_COM_RIG_ARROW_C_BMP;       
        FM_THMEM_TEMP_COM_TOP_BG_BMP                  = FM_TEMP_NIGHT_NIG_COM_TOP_BG_BMP;            
        FM_THMEM_TEMP_COM_RIG_ZONE_ICON_S_BMP         = FM_TEMP_NIGHT_NIG_COM_RIG_ZONE_ICON_S_BMP;   
        FM_THMEM_TEMP_COM_RIG_ZONE_ICON_N_BMP         = FM_TEMP_NIGHT_NIG_COM_RIG_ZONE_ICON_N_BMP;   
        FM_THMEM_TEMP_COM_RIG_AUDIO_ICON_BMP          = FM_TEMP_NIGHT_NIG_COM_RIG_AUDIO_ICON_BMP;    
        FM_THMEM_TEMP_COM_TOP_LIST_ICON_BMP           = FM_TEMP_NIGHT_NIG_COM_TOP_LIST_ICON_BMP;     
        FM_THMEM_TEMP_MEDIA_DUAL_PHONE_D_BMP          = FM_TEMP_NIGHT_NIG_MEDIA_DUAL_PHONE_D_BMP;    
        FM_THMEM_TEMP_COM_RIG_FUNC_ICON_N_BMP         = FM_TEMP_NIGHT_NIG_COM_RIG_FUNC_ICON_N_BMP;   
        FM_THMEM_TEMP_COM_TOP_ICON_BG_P_S_BMP         = FM_TEMP_NIGHT_NIG_COM_TOP_ICON_BG_P_S_BMP;   
        FM_THMEM_TEMP_COM_RIG_FUNC_ICON_BMP           = FM_TEMP_NIGHT_NIG_COM_RIG_FUNC_ICON_BMP;  
    }
    else  if(theme == THEME_DAY)
    {
        FM_THMEM_TEMP_MEDIA_SETUP_REPEAT_B_BMP        = FM_TEMP_DAY_DAY_MEDIA_SETUP_REPEAT_B_BMP;  
        FM_THMEM_FM_ICON_BMP                          = FM_DAY_FM_ICON_BMP;
        FM_THMEM_AM_ICON_BMP                          = FM_DAY_AM_ICON_BMP;
        FM_THMEM_WB_ICON_BMP                          = FM_DAY_WB_ICON_BMP;

        FM_THEME_COM_BOT_BG_BMP                       = FM_COMMON_DAY_COM_BOT_BG_BMP;  

        FM_THEME_COM_BOT_ICON_BG_A_L_N_BMP            = FM_COMMON_DAY_COM_BOT_ICON_BG_A_L_N_BMP;
        FM_THEME_COM_BOT_ICON_BG_A_L_S_BMP            = FM_COMMON_DAY_COM_BOT_ICON_BG_A_L_S_BMP;
        
        FM_THEME_COM_BOT_LIST_N_BMP                   = FM_COMMON_DAY_COM_BOT_LIST_N_BMP;
        FM_THEME_COM_BOT_LIST_D_BMP                   = FM_COMMON_DAY_COM_BOT_LIST_D_BMP;
                
        FM_THEME_COM_BOT_DIRECT_N_BMP                 = FM_COMMON_DAY_COM_BOT_DIRECT_N_BMP;
        FM_THEME_COM_BOT_DIRECT_D_BMP                 = FM_COMMON_DAY_COM_BOT_DIRECT_D_BMP;
        
        FM_THMEM_TEMP_COM_LIST_OFF_BMP                = FM_TEMP_DAY_DAY_COM_LIST_OFF_BMP;          
        FM_THMEM_TEMP_MEDIA_DUAL_PHONE_A_BMP          = FM_TEMP_DAY_DAY_MEDIA_DUAL_PHONE_A_BMP;    
        FM_THMEM_TEMP_COM_RIG_BG_A_BMP                = FM_TEMP_DAY_DAY_COM_RIG_BG_A_BMP;          
        FM_THMEM_TEMP_COM_RIG_ARROW_A_BMP             = FM_TEMP_DAY_DAY_COM_RIG_ARROW_A_BMP;       
        FM_THMEM_TEMP_MEDIA_SETUP_REPEAT_A_BMP        = FM_TEMP_DAY_DAY_MEDIA_SETUP_REPEAT_A_BMP;  
        FM_THMEM_TEMP_COM_RIG_AUDIO_ICON_S_BMP        = FM_TEMP_DAY_DAY_COM_RIG_AUDIO_ICON_S_BMP;  
        FM_THMEM_TEMP_COM_RIG_AUDIO_ICON_N_BMP        = FM_TEMP_DAY_DAY_COM_RIG_AUDIO_ICON_N_BMP;  
        FM_THMEM_TEMP_MEDIA_DUAL_PHONE_B_BMP          = FM_TEMP_DAY_DAY_MEDIA_DUAL_PHONE_B_BMP;    
        FM_THMEM_TEMP_COM_TOP_BACK_ICON_BMP           = FM_TEMP_DAY_DAY_COM_TOP_BACK_ICON_BMP;     
        FM_THMEM_TEMP_COM_MEDIA_ALBUM_BG_BMP          = FM_TEMP_DAY_DAY_COM_MEDIA_ALBUM_BG_BMP;    
        FM_THMEM_TEMP_MEDIA_SETUP_RANDOM_A_BMP        = FM_TEMP_DAY_DAY_MEDIA_SETUP_RANDOM_A_BMP;  
        FM_THMEM_TEMP_COM_RIG_FUNC_ICON_S_BMP         = FM_TEMP_DAY_DAY_COM_RIG_FUNC_ICON_S_BMP;   
        FM_THMEM_TEMP_COM_RIG_ARROW_B_BMP             = FM_TEMP_DAY_DAY_COM_RIG_ARROW_B_BMP;       
        FM_THMEM_TEMP_COM_LOW_VOLTAGE_BMP             = FM_TEMP_DAY_DAY_COM_LOW_VOLTAGE_BMP;       
        FM_THMEM_TEMP_COM_RIG_BG_C_BMP                = FM_TEMP_DAY_DAY_COM_RIG_BG_C_BMP;          
        FM_THMEM_TEMP_COM_TOP_ICON_BG_N_BMP           = FM_TEMP_DAY_DAY_COM_TOP_ICON_BG_N_BMP;     
        FM_THMEM_TEMP_COM_LIST_ON_BMP                 = FM_TEMP_DAY_DAY_COM_LIST_ON_BMP;           
        FM_THMEM_TEMP_COM_LIST_BG_BMP                 = FM_TEMP_DAY_DAY_COM_LIST_BG_BMP;           
        FM_THMEM_TEMP_COM_RIG_BG_B_BMP                = FM_TEMP_DAY_DAY_COM_RIG_BG_B_BMP;          
        FM_THMEM_TEMP_COM_RIG_ICON_BG_N_BMP           = FM_TEMP_DAY_DAY_COM_RIG_ICON_BG_N_BMP;     
        FM_THMEM_TEMP_MEDIA_SETUP_RANDOM_B_BMP        = FM_TEMP_DAY_DAY_MEDIA_SETUP_RANDOM_B_BMP;  
        FM_THMEM_TEMP_COM_RIG_ZONE_ICON_BMP           = FM_TEMP_DAY_DAY_COM_RIG_ZONE_ICON_BMP;     
        FM_THMEM_TEMP_COM_RIG_ICON_BG_P_BMP           = FM_TEMP_DAY_DAY_COM_RIG_ICON_BG_P_BMP;     
        FM_THMEM_TEMP_COM_BG_BMP                      = FM_TEMP_DAY_DAY_COM_BG_BMP;                
        FM_THMEM_TEMP_COM_PD_CHARGE_BMP               = FM_TEMP_DAY_DAY_COM_PD_CHARGE_BMP;         
        FM_THMEM_TEMP_MEDIA_SETUP_REPEAT_C_BMP        = FM_TEMP_DAY_DAY_MEDIA_SETUP_REPEAT_C_BMP;  
        FM_THMEM_TEMP_COM_PUNCH_EQ_BMP                = FM_TEMP_DAY_DAY_COM_PUNCH_EQ_BMP;          
        FM_THMEM_TEMP_COM_TOP_SOURCE_ICON_BMP         = FM_TEMP_DAY_DAY_COM_TOP_SOURCE_ICON_BMP;   
        FM_THMEM_TEMP_COM_RIG_ARROW_C_BMP             = FM_TEMP_DAY_DAY_COM_RIG_ARROW_C_BMP;       
        FM_THMEM_TEMP_COM_TOP_BG_BMP                  = FM_TEMP_DAY_DAY_COM_TOP_BG_BMP;            
        FM_THMEM_TEMP_COM_RIG_ZONE_ICON_S_BMP         = FM_TEMP_DAY_DAY_COM_RIG_ZONE_ICON_S_BMP;   
        FM_THMEM_TEMP_COM_RIG_ZONE_ICON_N_BMP         = FM_TEMP_DAY_DAY_COM_RIG_ZONE_ICON_N_BMP;   
        FM_THMEM_TEMP_COM_RIG_AUDIO_ICON_BMP          = FM_TEMP_DAY_DAY_COM_RIG_AUDIO_ICON_BMP;    
        FM_THMEM_TEMP_COM_TOP_LIST_ICON_BMP           = FM_TEMP_DAY_DAY_COM_TOP_LIST_ICON_BMP;     
        FM_THMEM_TEMP_MEDIA_DUAL_PHONE_D_BMP          = FM_TEMP_DAY_DAY_MEDIA_DUAL_PHONE_D_BMP;    
        FM_THMEM_TEMP_COM_RIG_FUNC_ICON_N_BMP         = FM_TEMP_DAY_DAY_COM_RIG_FUNC_ICON_N_BMP;   
        FM_THMEM_TEMP_COM_TOP_ICON_BG_P_S_BMP         = FM_TEMP_DAY_DAY_COM_TOP_ICON_BG_P_S_BMP;   
        FM_THMEM_TEMP_COM_RIG_FUNC_ICON_BMP           = FM_TEMP_DAY_DAY_COM_RIG_FUNC_ICON_BMP;  
    }
    
    if(fm_para->theme > THEME_NONE)
    {
        //lv_obj_set_style_bg_image_src(ui_fm, ui_fm_get_res(fm_para, FM_THMEM_TEMP_COM_BG_BMP), LV_PART_MAIN | LV_STATE_DEFAULT);
        traverse_children_with_callback(fm_para, ui_fm, fm_para->theme_bmp_start, fm_para->theme_bmp_end, FM_THMEM_TEMP_MEDIA_SETUP_REPEAT_B_BMP, FM_THMEM_TEMP_COM_RIG_FUNC_ICON_BMP, fm_para->ui_fm_res_para.lv_fm_icon, ui_fm_get_res, NULL, 0);
        ui_source_theme_switch(fm_para->source_obj, theme);
    }

    fm_para->theme = theme;
    fm_para->theme_bmp_start = FM_THMEM_TEMP_MEDIA_SETUP_REPEAT_B_BMP;
    fm_para->theme_bmp_end = FM_THMEM_TEMP_COM_RIG_FUNC_ICON_BMP;
}

static void ui_fm_source_title(ui_fm_para_t * fm_para, __u8 * str, __u8 source_flag)
{
	char title_str[128] = {0};
    reg_root_para_t* last_root_para;
    
    last_root_para = (reg_root_para_t*)dsk_reg_get_para_by_app(REG_APP_ROOT);
    
    if(app_root_mcu_get_source() == ID_WORK_SOURCE_FM)
    {
        eLIBs_strcpy(title_str, "FM");    
    }
    else if(app_root_mcu_get_source() == ID_WORK_SOURCE_AM)
    {
        eLIBs_strcpy(title_str, "AM");
    }
    else if(app_root_mcu_get_source() == ID_WORK_SOURCE_WB)
    {
        eLIBs_strcpy(title_str, "WB");
    }

    if(str)
    {
        eLIBs_strcat(title_str, ":");
        eLIBs_strcat(title_str, str);
    }
    
    ui_source_title(fm_para->source_para, title_str, source_flag);
}

static void ui_fm_change_mode(ui_fm_para_t * fm_para)
{
    reg_root_para_t* last_root_para;
    
    last_root_para = (reg_root_para_t*)dsk_reg_get_para_by_app(REG_APP_ROOT);

    ui_source_bottom_play(fm_para->source_para, SOURCE_BOTTOM_PLAY_NONE);

    ui_fm_source_title(fm_para, NULL, 1);

    __fm_msg("app_root_mcu_get_source() = 0x%x\n", app_root_mcu_get_source());
    if(app_root_mcu_get_source() == ID_WORK_SOURCE_FM)
    {
        lv_img_set_src(ui_fm_play_logo, ui_fm_get_res(fm_para, FM_THMEM_FM_ICON_BMP));
        lv_label_set_text(ui_fm_play_logo_text, "FM");
    }
    else if(app_root_mcu_get_source() == ID_WORK_SOURCE_AM)
    {
        lv_img_set_src(ui_fm_play_logo, ui_fm_get_res(fm_para, FM_THMEM_AM_ICON_BMP));
        lv_label_set_text(ui_fm_play_logo_text, "AM");
    }
    else if(app_root_mcu_get_source() == ID_WORK_SOURCE_WB)
    {
        lv_img_set_src(ui_fm_play_logo, ui_fm_get_res(fm_para, FM_THMEM_WB_ICON_BMP));
        lv_label_set_text(ui_fm_play_logo_text, "WB");
    }
}

static __s32 ui_fm_update_timer_proc(ui_fm_para_t * fm_para)
{
	__s32 ret = EPDK_OK;
	reg_music_para_t *music_last_para = NULL;	
	reg_aux_para_t* aux_para;
	reg_system_para_t *sys_para;
	reg_root_para_t *last_root_para = NULL;	
	reg_fm_para_t* para_fm;
	reg_init_para_t* para_init;
	
	para_init = (reg_init_para_t*)dsk_reg_get_para_by_app(REG_APP_INIT);
	para_fm = (reg_fm_para_t*)dsk_reg_get_para_by_app(REG_APP_FM);
	last_root_para = dsk_reg_get_para_by_app(REG_APP_ROOT);
	sys_para = (reg_system_para_t*)dsk_reg_get_para_by_app(REG_APP_SYSTEM);
	aux_para = (reg_aux_para_t*)dsk_reg_get_para_by_app(REG_APP_AUX);	
	music_last_para = dsk_reg_get_para_by_app(REG_APP_MUSIC);	

    root_app_para_t * root_app_para = app_root_get_para();
    
    if((root_app_para->home_store == 1) && (fm_para->ui_fm_data_para.last_work_source != app_root_mcu_get_source()))
    {
        fm_para->ui_fm_data_para.last_work_source = app_root_mcu_get_source();
        ui_fm_change_mode(fm_para);
    }
    
    if(fm_para->ui_fm_data_para.cur_band != para_fm->cur_band)
    {
        fm_para->ui_fm_data_para.cur_band = para_fm->cur_band;
        fm_para->curFM1_3AM1_2_id = para_fm->cur_band;
    }
    
    if(fm_para->ui_fm_data_para.cur_freq != para_fm->cur_freq)
    {
        fm_para->ui_fm_data_para.cur_freq = para_fm->cur_freq;
        fm_para->fmchannelfreq = para_fm->cur_freq;

        fm_view_update_frq(fm_para);
		fm_view_update_Mhz_Khz(fm_para);
    }

    if(fm_para->ui_fm_data_para.cur_rds_status != para_fm->cur_rds_status)
    {
        fm_para->ui_fm_data_para.cur_rds_status = para_fm->cur_rds_status;

    }

    if(fm_para->ui_fm_data_para.cur_pty_type != para_fm->cur_pty_type)
    {
        fm_para->ui_fm_data_para.cur_pty_type = para_fm->cur_pty_type;

        fm_view_show_rds_pty(fm_para, 1);
    }
    
    if(fm_para->ui_fm_data_para.local_seek_on_off != para_init->local_seek_on_off)
    {
        fm_para->ui_fm_data_para.local_seek_on_off = para_init->local_seek_on_off;

        fm_view_update_loc(fm_para, 1);
    }
    
    if(eLIBs_memcmp(fm_para->ui_fm_data_para.ps_data, para_fm->ps_data, sizeof(fm_para->ui_fm_data_para.ps_data)) == EPDK_NO)
    {
        eLIBs_memcpy(fm_para->ui_fm_data_para.ps_data, para_fm->ps_data, sizeof(fm_para->ui_fm_data_para.ps_data));

        fm_view_show_rds_ps(fm_para, 1);
    }

    if(0)
    {
        fm_view_update_low_voltage(fm_para, 0);
    }

    if(0)
    {
        fm_view_update_charge(fm_para, 0);
    }

    if(0)
    {
        fm_view_update_eq(fm_para, 1);
    }
    
    return ret;
}

static __s32 ui_fm_ui_update_proc(ui_fm_para_t * fm_para, __gui_msg_t *msg)
{
	__s32 ret = EPDK_FAIL;
	reg_root_para_t* last_root_para;
	reg_aux_para_t* para;
	reg_system_para_t* sys_para;
	reg_fm_para_t* tuner_para;

	sys_para = (reg_system_para_t*)dsk_reg_get_para_by_app(REG_APP_SYSTEM);
	last_root_para = (reg_root_para_t*)dsk_reg_get_para_by_app(REG_APP_ROOT);
	para = (reg_aux_para_t*)dsk_reg_get_para_by_app(REG_APP_AUX);	
	tuner_para = (reg_fm_para_t*)dsk_reg_get_para_by_app(REG_APP_FM);	

	if(fm_para == NULL)
		return EPDK_FAIL;

    //eLIBs_printf("ui_fm_ui_update_proc: msg->dwAddData1=%d, GUI_MSG_UI_VOLUME_ADJUST=%d\n",msg->dwAddData1, GUI_MSG_UI_VOLUME_ADJUST);
	
	switch(msg->dwAddData1)
    {
    	case GUI_MSG_UI_VOLUME_ADJUST:
        case GUI_MSG_UI_SUB_VOL:
    	{
            	ui_source_send_event(fm_para->source_para, LV_EVENT_MSG_RECEIVED, (void *)msg);
    	}
    		break;

	case GUI_MSG_UI_NEW_TUNER_UPDATE:
	{
		// Add error handling for null pointers
		if (!sys_para || !tuner_para || !fm_para) {
			break;
		}
		
		mcu_protocol_sync_t *sync_ptr = NULL;
		if(sys_para->g_sys_para.current_source == SYS_SOURCE_AM)
			sync_ptr = &tuner_para->g_am_para.sync;
		else if(sys_para->g_sys_para.current_source == SYS_SOURCE_WB)
			sync_ptr = &tuner_para->g_wb_para.sync;
		else
			sync_ptr = &tuner_para->g_fm_para.sync;
		
		// Process MCU Protocol v2 Tuner data synchronization first
		__u32 changed_mask = 0;
		if (fm_para->mcu_ops.sync_dirty) {
			changed_mask = UI_MCU_SYNC_PROCESS(sync_ptr);  // Get and clear pending
			if (changed_mask != 0) {
				__fm_msg("MCU Tuner Update: source=%d, mask=0x%08X\n", 
						sys_para->g_sys_para.current_source, changed_mask);
				fm_para->mcu_ops.sync_dirty(fm_para, changed_mask);
			}
		}
		
		if (!changed_mask)
			break;

		// Update UI by change_mask

		//frequency
		if(changed_mask&FM_DATA_FREQUENCY)
		{

			changed_mask &= (~FM_DATA_FREQUENCY);
			if(!changed_mask)
				break;
		}
		//preset 
		if(changed_mask&FM_DATA_PRESET_NUMBER)
		{

			changed_mask &= (~FM_DATA_PRESET_NUMBER);
			if(!changed_mask)
				break;
		}
		//state: play, auto seek, manual seek,auto store...
		if(changed_mask&FM_DATA_TUNER_STATE)
		{

			changed_mask &= (~FM_DATA_TUNER_STATE);
			if(!changed_mask)
				break;
		}

		//function switch
		if(changed_mask&FM_DATA_LOCAL_SEEK)
		{


			changed_mask &= (~FM_DATA_LOCAL_SEEK);
			if(!changed_mask)
				break;
		}


		//RDS
		if(changed_mask&FM_DATA_RDS_SWITCH)
		{


			changed_mask &= (~FM_DATA_RDS_SWITCH);
			if(!changed_mask)
				break;
		}

		if(changed_mask&FM_DATA_RDS_AF_SWITCH)
		{


			changed_mask &= (~FM_DATA_RDS_AF_SWITCH);
			if(!changed_mask)
				break;
		}

		if(changed_mask&FM_DATA_RDS_PS)
		{

			changed_mask &= (~FM_DATA_RDS_PS);
			if(!changed_mask)
				break;
		}

		//preset list
		if(changed_mask&FM_DATA_PRESET_LIST)
		{


			changed_mask &= (~FM_DATA_PRESET_LIST);
			if(!changed_mask)
				break;
		}
	}
		break;

    	default:
    		break;
    }
}

void __ui_fm_remove_hidden(ui_fm_para_t * fm_para)
{
	__fm_msg("ui_ipod_remove_hidden\n");
	if(!ui_fm_timer)
	{
		ui_fm_timer = lv_timer_create(ui_event_fm_timer_cb, FM_TIMER_UPDATE_SEARCH_FREQ_TIMES*10, fm_para);
	}
	_ui_flag_modify(ui_fm, LV_OBJ_FLAG_HIDDEN, _UI_MODIFY_FLAG_REMOVE);
    ui_fm_change_mode(fm_para);
}

void __ui_fm_add_hidden(ui_fm_para_t * fm_para)
{
	__fm_msg("__ui_fm_add_hidden\n");
	_ui_flag_modify(ui_fm, LV_OBJ_FLAG_HIDDEN, _UI_MODIFY_FLAG_ADD);
	if(ui_fm_timer)
	{
		lv_timer_del(ui_fm_timer);
		ui_fm_timer = NULL;
	}
}

void __ui_fm_layer_on(ui_fm_para_t * fm_para, __u8 store)
{
	if(fm_para == NULL)
		return EPDK_FAIL;
	__fm_msg("fm_para->mode_store=%d\n",fm_para->mode_store);
	if(fm_para->mode_store == 0)
	{
		return EPDK_FAIL;
	}

	fm_para->mode_store = 0;

	ui_fm_remove_hidden(fm_para);
}

void __ui_fm_layer_sleep(ui_fm_para_t * fm_para, __u8 store)
{
	if(fm_para == NULL)
		return EPDK_FAIL;
	__fm_msg("fm_para->mode_store=%d\n",fm_para->mode_store);
	if(fm_para->mode_store == 1)
	{
		return EPDK_FAIL;
	}

	fm_para->mode_store = 1;

	ui_fm_add_hidden(fm_para);
}

static __s32 ui_fm_internal_para_update(ui_fm_para_t * fm_para)
{    
    fm_para->local_onoff = 0;
    fm_para->rds_onoff = 0;
    fm_para->af_onoff = 0;

    fm_para->theme_bmp_start = FM_TEMP_NIGHT_NIG_MEDIA_SETUP_REPEAT_B_BMP;
    fm_para->theme_bmp_end = FM_TEMP_NIGHT_NIG_COM_RIG_FUNC_ICON_BMP;
    
    fm_para->source_obj = ui_source_content;//(lv_obj_t *)ui_source_create(ui_source);
    fm_para->source_para = (ui_source_para_t *)lv_obj_get_user_data(fm_para->source_obj);
}

__s32 ui_fm_refresh_handler(void * para, __gui_msg_t * pmsg)
{
	__s32 ret = EPDK_OK;
	__gui_msg_t msg;
	ui_fm_para_t * fm_para = ui_fm_para;//(ui_fm_para_t *)para;
	root_app_para_t * root_para = app_root_get_para();//pmsg->p_arg;

	if(!fm_para)
		return EPDK_FAIL;
		
    memset(&msg, 0x00, sizeof(msg));

	//__fm_msg("LOWORD(pmsg->id)=0x%x\n", LOWORD(pmsg->id));
    if(LOWORD(pmsg->id) == GUI_MSG_PAINT)
    {
		//__fm_msg("HIWORD(pmsg->id)=0x%x, GUI_MSG_FM_REFRESH_INIT=0x%x\n", HIWORD(pmsg->id), GUI_MSG_FM_REFRESH_INIT);
		switch(HIWORD(pmsg->id))
		{
			case GUI_MSG_FM_REFRESH_INIT:
			{
                ui_fm_night_theme(ui_fm_para, THEME_NIGHT);
                
                ui_source_switch_clean(ui_fm_para->source_para);
                ui_source_rig_bar_set_flag(ui_fm_para->source_para, LV_OBJ_FLAG_HIDDEN, 1);
                    
				ui_fm_screen_init(ui_fm_para);
                ui_fm_change_mode(ui_fm_para);
				gscene_bgd_set_img_src(BG_INDEX_AUTO);
				printf("ui_fm_screen_init\n");
				ui_fm_para->fm_main_obj = ui_fm;
				root_para->app_interface_fm->info.src_win = ui_fm;
			}
			break;

			case GUI_MSG_FM_REFRESH_UNINIT:
			{
				if(fm_para->tips_tmr)
					lv_timer_del(fm_para->tips_tmr);
				if(ui_fm_timer)
				{
					lv_timer_del(ui_fm_timer);
					ui_fm_timer = NULL;
				}
				ui_fm_screen_uninit(fm_para);
                //ui_source_destroy(ui_fm_para->source_obj);
				ui_fm_uninit_res(fm_para);
				esMEMS_Bfree(fm_para, sizeof(ui_fm_para_t));
				ui_fm = NULL;
				ui_fm_para = NULL;
                ui_fm_menu = NULL;
                ui_fm_func_menu = NULL;
                ui_fm_bottom_list = NULL;
                ui_fm_bottom_auto = NULL;
                ui_fm_bottom_local = NULL;
                ui_fm_bottom_direct = NULL;
				fmchannelfreq = 0;
			}
			break;
			
			case GUI_MSG_FM_REFRESH_HIDDEN:
			{
				__u32 flag = (__u32)pmsg->dwAddData1;

				if(flag == 0)
				{
					__ui_fm_remove_hidden(fm_para);
				}
				else
				{
					__ui_fm_add_hidden(fm_para);
				}
			}
			break;

			case GUI_MSG_FM_REFRESH_MSG:
			{
				__s32 id = (__s32)pmsg->dwAddData1;
				__s32 data1 = (__s32)pmsg->dwAddData2;
				__s32 data2 = (__s32)pmsg->dwReserved;
				
				__fm_cmd2parent(NULL, id, data1, data2);
			}
			break;

			case GUI_MSG_FM_REFRESH_DIALOG:
			{
				__s32 title_id = (__s32)pmsg->dwAddData1;
				__s32 content_id = (__s32)pmsg->dwAddData2;
				__u32 time_out = LOWORD(pmsg->dwReserved);
				__s32 style = HIWORD(pmsg->dwReserved);
				
				__fm_msg("pmsg->p_arg = 0x%x\n", pmsg->p_arg);
				if(pmsg->p_arg == (void *)__create_fm_tips_dialog)
				{
					__create_fm_tips_dialog(ui_fm_para, title_id, content_id, time_out);
				}
				else if(pmsg->p_arg == (void *)__fm_tips_dialog_cancel)
				{
					__fm_tips_dialog_cancel(ui_fm_para);
				}
				else if(pmsg->p_arg == (void *)__create_fm_app_tips_dialog)
				{
					__create_fm_app_tips_dialog(ui_fm_para, title_id, content_id, time_out);
				}
				else if(pmsg->p_arg == (void *)__fm_app_tips_dialog_cancel)
				{
					__fm_app_tips_dialog_cancel(ui_fm_para);
				}
				else if(pmsg->p_arg == (void *)__ui_fm_tips_tmr_create)
				{
					__ui_fm_tips_tmr_create(ui_fm_para);
				}
			}
			break;

			case GUI_MSG_FM_REFRESH_PART:
			{
				if(pmsg->p_arg == (void *)__fm_view_update_low_voltage)
				{
					__u8 update = (__u8)pmsg->dwAddData1;
					
					__fm_view_update_low_voltage(ui_fm_para, update);
				}
				else if(pmsg->p_arg == (void *)__fm_view_update_charge)
				{
					bool update = (bool)pmsg->dwAddData1;
					
					__fm_view_update_charge(ui_fm_para, update);
				}
				else if(pmsg->p_arg == (void *)__fm_view_update_eq)
				{
					bool update = (bool)pmsg->dwAddData1;
					
					__fm_view_update_eq(ui_fm_para, update);
				}
				else if(pmsg->p_arg == (void *)__fm_view_update_preset)
				{
					__u8 update = (__u8)pmsg->dwAddData1;
					
					__fm_view_update_preset(ui_fm_para, update);
				}
				else if(pmsg->p_arg == (void *)__fm_view_update_loc)
				{
					bool update = (bool)pmsg->dwAddData1;
					
					__fm_view_update_loc(ui_fm_para, update);
				}
				else if(pmsg->p_arg == (void *)__fm_view_update_ta)
				{
					bool update = (bool)pmsg->dwAddData1;
					
					__fm_view_update_ta(ui_fm_para, update);
				}
				else if(pmsg->p_arg == (void *)__fm_view_update_ST)
				{
					bool update = (bool)pmsg->dwAddData1;
					
					__fm_view_update_ST(ui_fm_para, update);
				}
				else if(pmsg->p_arg == (void *)__fm_view_update_af)
				{
					bool update = (bool)pmsg->dwAddData1;
					
					__fm_view_update_af(ui_fm_para, update);
				}
				else if(pmsg->p_arg == (void *)__fm_view_show_rds_pty)
				{
					bool show_status = (bool)pmsg->dwAddData1;
					
					__fm_view_show_rds_pty(ui_fm_para, show_status);
				}
				else if(pmsg->p_arg == (void *)__fm_view_update_seek_string)
				{
					bool show_status = (bool)pmsg->dwAddData1;
					
					__fm_view_update_seek_string(ui_fm_para, show_status);
				}
				else if(pmsg->p_arg == (void *)__fm_view_update_manual_string)
				{
					bool show_status = (bool)pmsg->dwAddData1;
					
					__fm_view_update_manual_string(ui_fm_para, show_status);
				}
				else if(pmsg->p_arg == (void *)__fm_view_clear_pty_status)
				{
					__fm_view_clear_pty_status(ui_fm_para);
				}
				else if(pmsg->p_arg == (void *)__fm_view_show_rds_ps)
				{
					bool show_status = (bool)pmsg->dwAddData1;
					
					__fm_view_show_rds_ps(ui_fm_para, show_status);
				}
				else if(pmsg->p_arg == (void *)__fm_view_clear_rds_status)
				{
					__fm_view_clear_rds_status(ui_fm_para);
				}
				else if(pmsg->p_arg == (void *)__fm_view_update_fmam_band)
				{
					__fm_view_update_fmam_band(ui_fm_para);
				}
				else if(pmsg->p_arg == (void *)__fm_view_update_frq)
				{
					__fm_view_update_frq(ui_fm_para);
				}
				else if(pmsg->p_arg == (void *)__fm_view_update_Mhz_Khz)
				{
					__fm_view_update_Mhz_Khz(ui_fm_para);
				}
			}
			break;

			case GUI_MSG_FM_REFRESH_WIN:
			{

			}
			break;
						
			default:
			{
				ret = EPDK_FAIL;
			}
			break;
		}
	
}
	else
	{
		ret = EPDK_FAIL;
	}
	
	return ret;
}

static void ui_fm_source_event_handler(ui_fm_para_t * fm_para)
{    
    ui_fm_bottom_group_update(fm_para, 0, 0, 0);
    ui_source_title(fm_para->source_para, "Source", 0);
    ui_source_sub_open(fm_para->source_para);
    fm_cmd2parent(NULL,SWITCH_TO_MMENU,0,0);
}

static void ui_fm_top_left_event_handler(ui_fm_para_t * fm_para)
{    
    __fm_msg("fm_para->menu_win=%d\n", fm_para->menu_win);
    switch(fm_para->menu_win)
    {
        case FM_MENU_WIN_NONE:
        {
            if(fm_para->bottom_fouse_flag == 1)
            {
                ui_fm_bottom_group_update(fm_para, 0, 0, 0);
            }
            ui_home_restore_switch_source();
        }
        break;

        case FM_MENU_WIN_DIRECT:
        {
            ui_fm_switch_win(fm_para, FM_MENU_WIN_NONE);
        }
        break;

        case FM_MENU_WIN_LIST:
        {
            ui_fm_switch_win(fm_para, FM_MENU_WIN_NONE);
        }
        break;
    
        case FM_MENU_WIN_FUNC:
        {
            ui_fm_switch_win(fm_para, FM_MENU_WIN_NONE);
        }
        break;
    }

    //fm_cmd2parent(NULL,SWITCH_TO_SMENU,0,0);
}

static void ui_fm_top_right_event_handler(ui_fm_para_t * fm_para)
{    
    __fm_msg("fm_para->menu_win=%d\n", fm_para->menu_win);
    if(fm_para->menu_win > FM_MENU_WIN_FUNC)
    {
        ui_fm_switch_win(fm_para, FM_MENU_WIN_FUNC);
    }
    else if(fm_para->menu_win == FM_MENU_WIN_NONE)
    {
        if(fm_para->bottom_fouse_flag == 1)
        {
            ui_fm_bottom_group_update(fm_para, 0, 0, 0);
        }
        ui_home_restore_switch_source();
    }
    else
    {
        ui_fm_switch_win(fm_para, FM_MENU_WIN_NONE);
    }
}

static void ui_fm_open_bar_menu_event_handler(ui_fm_para_t * fm_para)
{
    ui_fm_switch_win(fm_para, FM_MENU_WIN_NONE);

    __fm_msg("save_rig_select_flag=%d, rig_select_flag=%d\n", fm_para->source_para->save_rig_select_flag, fm_para->source_para->rig_select_flag);
    if(fm_para->source_para->save_rig_select_flag == SOURCE_BOTTOM)
    {
        ui_fm_bottom_menu_event_handler(fm_para);
    }
    else
    {
        ui_fm_top_menu_event_handler(fm_para);
    }
}

static void ui_fm_top_menu_event_handler(ui_fm_para_t * fm_para)
{
    ui_fm_switch_win(fm_para, FM_MENU_WIN_LIST);
}

static void ui_fm_bottom_menu_event_handler(ui_fm_para_t * fm_para)
{
    ui_fm_switch_win(fm_para, FM_MENU_WIN_FUNC);
}

static void ui_fm_menu_arrow_handler(ui_fm_para_t * fm_para, lv_obj_t * item, lv_obj_t * target)
{
    int32_t item_index = lv_obj_get_index(item);
    lv_obj_t * next_item = lv_obj_get_child(fm_para->ui_list_para.item_obj, item_index + 1);
    
    switch(item_index)
    {    
        case FM_FUNC_LOCAL:
        {
            fm_para->local_onoff = lv_obj_has_state(target, LV_STATE_CHECKED);
            __fm_msg("fm_para->local_onoff=%d\n", fm_para->local_onoff);
        }
        break;
    
        case FM_FUNC_RBDS:
        {
            fm_para->rds_onoff = lv_obj_has_state(target, LV_STATE_CHECKED);
            lv_obj_update_flag(next_item, LV_OBJ_FLAG_HIDDEN, fm_para->rds_onoff ? 0 : 1);
            __fm_msg("fm_para->rds_onoff=%d\n", fm_para->rds_onoff);
        }
        break;
    
        case FM_FUNC_AF:
        {
            fm_para->af_onoff = lv_obj_has_state(target, LV_STATE_CHECKED);
            __fm_msg("fm_para->af_onoff=%d\n", fm_para->af_onoff);
        }
        break;
    }
}

///////////////////// FUNCTIONS ////////////////////
static void ui_event_fm_screen(lv_event_t * e)
{
	ui_fm_para_t * fm_para = lv_event_get_user_data(e);
    lv_event_code_t event_code = lv_event_get_code(e);
    lv_obj_t * target = lv_event_get_target(e);
    lv_obj_t * current_target = lv_event_get_current_target(e);
    
    if(event_code == LV_EVENT_MSG_RECEIVED)
    {
		__gui_msg_t * msg = (__gui_msg_t *)lv_event_get_param(e);
		//__fm_msg("ui_event_fm_screen:msg->id=%d\n", msg->id);
		if((lv_obj_has_flag(current_target, LV_OBJ_FLAG_HIDDEN))
			&& (fm_para->mode_store == 1)
			&& (LOWORD(msg->id) != GUI_MSG_SET_APP_LAYER_SUSPEND)
			&& (LOWORD(msg->id) != GUI_MSG_SET_APP_LAYER_ON)
			&& (LOWORD(msg->id) != GUI_MSG_SET_APP_LAYER_SLEEP)
			&& (LOWORD(msg->id) != GUI_MSG_SET_APP_LAYER_OFF)
			&& (LOWORD(msg->id) != GUI_MSG_SET_APP_STORE_LAYER_SUSPEND)
			&& (LOWORD(msg->id) != GUI_MSG_SET_APP_STORE_LAYER_ON)
			&& (LOWORD(msg->id) != GUI_MSG_SET_APP_STORE_LAYER_SLEEP)
			&& (LOWORD(msg->id) != GUI_MSG_SET_APP_STORE_LAYER_OFF)
			&& (HIWORD(msg->id) != GUI_MSG_FM_REFRESH_UNINIT)

			)
		{
			return;
		}
		
		if((ui_fm_para->mode_uninit_flag == 1) && (HIWORD(msg->id) != GUI_MSG_FM_REFRESH_UNINIT))
		{
			eLIBs_printf("ui_fm_para->mode_uninit_flag == 1 HIWORD(msg->id)=%d!!!!!!!!!\n", HIWORD(msg->id));
			return;
		}

    	switch(LOWORD(msg->id)) 
    	{			
			case GUI_MSG_SET_APP_LAYER_SUSPEND:
			{
				ui_fm_layer_sleep(fm_para, 0);
			}
			break;

			case GUI_MSG_SET_APP_LAYER_ON:
			{
				__fm_msg("GUI_MSG_SET_APP_LAYER_ON\n");	
				ui_fm_layer_on(fm_para, 0);
			}
			break;

			case GUI_MSG_SET_APP_STORE_LAYER_SLEEP:
			{
				__fm_msg("GUI_MSG_SET_APP_STORE_LAYER_SLEEP\n");
				ui_fm_layer_sleep(fm_para, 1);
			}
			break;

			case GUI_MSG_SET_APP_STORE_LAYER_ON:
			{
				__fm_msg("GUI_MSG_SET_APP_STORE_LAYER_ON\n");					
				ui_fm_layer_on(fm_para, 1);
			}
			break;

			case GUI_MSG_PAINT:
			{
				ui_fm_refresh_handler(fm_para, msg);
			}
			break;

			case DSK_MSG_FM_UPDATE_PRESET:	
			{
				reg_fm_para_t* para;
				para = (reg_fm_para_t*)dsk_reg_get_para_by_app(REG_APP_FM);
				fm_para->fmchannelfreq = para->cur_freq;
				fm_view_update_preset(fm_para,1);
			}	
			break;
			
#ifdef SWITCH_BAND_TIPS
			case DSK_MSG_FM_SWITCH_BAND_DONE:
			{
				ui_fm_tips_tmr_create(fm_para);
			}
			break;
		
			case GUI_MSG_FM_CLOSE_DIALOG_TPIS:
			{
				reg_root_para_t *last_root_para;
				reg_system_para_t*system_para;
				last_root_para = (reg_root_para_t*)dsk_reg_get_para_by_app(REG_APP_ROOT);
				system_para = (reg_system_para_t*)dsk_reg_get_para_by_app(REG_APP_SYSTEM);
				fm_tips_dialog_cancel(fm_para);
				__fm_msg("GUI_MSG_FM_CLOSE_DIALOG_TPIS\n");
				if(!last_root_para->is_mute_status && !system_para->power_off_status)
					app_root_unmute_switch_source();
			}
			break;
			
#endif

			case GUI_MSG_MUTE_STATUS_CHANGE:
			{				
			}
			break;
						
            case DSK_MSG_HOME_ANIM_STOP:
            {
                ui_source_sub_close(fm_para->source_para);
                ui_fm_source_title(fm_para, NULL, 0);
            }
            break;

            case GUI_MSG_UI_UPDATE:
            {
                ui_fm_ui_update_proc(fm_para, msg);
            }
            break;

		    default:
				__fm_msg("msg->id: %d\n", msg->id);
		    break;
	    }
    }
    else if(event_code == LV_EVENT_KEY)
    {
    	fm_para->indev_data = lv_event_get_param(e);
    	ui_fm_key_proc(fm_para);
        if(!fm_para->bottom_fouse_flag || (fm_para->indev_data->key != GUI_MSG_KEY_UP && fm_para->indev_data->key != GUI_MSG_KEY_DOWN))
        {
            ui_source_send_event(fm_para->source_para, LV_EVENT_KEY, fm_para->indev_data);
        }
    }
    else
    {
    }
}

static void ui_event_fm_mute_screen(lv_event_t * e)
{
	ui_fm_para_t * fm_para = lv_event_get_user_data(e);
    lv_event_code_t event_code = lv_event_get_code(e);
    lv_obj_t * target = lv_event_get_target(e);
    lv_obj_t * current_target = lv_event_get_current_target(e);
    
	if(event_code == LV_EVENT_PRESSED)
    {
		if(app_root_mute_key_get_status())
		{
			app_root_msg_send_opn(GUI_MSG_BEEP_SETTING,SEND_MCU_BEEP_SETTING_TIME,0,0,0,0,0);
		}
    }
    else if(event_code == LV_EVENT_SHORT_CLICKED)
	{
		ui_fm_set_mute(fm_para);
	}
}

static void ui_event_fm_back_screen(lv_event_t * e)
{
	ui_fm_para_t * fm_para = lv_event_get_user_data(e);
    lv_event_code_t event_code = lv_event_get_code(e);
    lv_obj_t * target = lv_event_get_target(e);
    lv_obj_t * current_target = lv_event_get_current_target(e);
    static __s8 last_key_type = 0;
	if(event_code == LV_EVENT_PRESSED)
    {
		app_root_msg_send_opn(GUI_MSG_BEEP_SETTING,SEND_MCU_BEEP_SETTING_TIME,0,0,0,0,0);
    }
    else if(event_code == LV_EVENT_SHORT_CLICKED)
	{
		if(last_key_type==-1)
		{
			last_key_type = 0;
			return;
		}

        root_app_para_t * root_app_para = app_root_get_para();

        if(root_app_para->home_store == 0)
        {
            lv_imgbtn_set_state(fm_para->source_para->ui_top_left, LV_IMAGEBUTTON_STATE_RELEASED);
            ui_home_para_t * home_para = ui_home_get_para();
            
            ui_home_switch_source(home_para->cur_focus_item);
            //app_home_cmd2root(NULL, RESTORE_APP, NULL, NULL);
        }
        else
        {
            lv_imgbtn_set_state(fm_para->source_para->ui_top_left, LV_IMAGEBUTTON_STATE_PRESSED);
            fm_cmd2parent(NULL,SWITCH_TO_MMENU,0,0);
        }
	}
	else if(event_code == LV_EVENT_PRESS_MOVEING)
	{
		last_key_type = -1;
	}
}

static void ui_event_fm_prve_screen(lv_event_t * e)
{
	static __s32 last_touch_action = -1;
	ui_fm_para_t * fm_para = lv_event_get_user_data(e);
    lv_event_code_t event_code = lv_event_get_code(e);
    lv_obj_t * target = lv_event_get_target(e);
    lv_obj_t * current_target = lv_event_get_current_target(e);
	static __s8 last_key_type = 0;

	if(event_code == LV_EVENT_PRESSED)
	{
		//ui_fm_keystripe_anim(fm_para);
		app_root_msg_send_opn(GUI_MSG_BEEP_SETTING,SEND_MCU_BEEP_SETTING_TIME,0,0,0,0,0);
	}
	else if(event_code == LV_EVENT_SHORT_CLICKED)
	{
		app_root_send2mcu_key(KEY_TOUCH_PREV);
        //ui_fm_night_theme(fm_para, THEME_DAY);
	}
}

static void ui_event_fm_next_screen(lv_event_t * e)
{
	static __s32 last_touch_action = -1;
	ui_fm_para_t * fm_para = lv_event_get_user_data(e);
	lv_event_code_t event_code = lv_event_get_code(e);
	lv_obj_t * target = lv_event_get_target(e);
	lv_obj_t * current_target = lv_event_get_current_target(e);
	static __s8 last_key_type = 0;

	if(event_code == LV_EVENT_PRESSED)
	{
		//ui_fm_keystripe_anim(fm_para);
		app_root_msg_send_opn(GUI_MSG_BEEP_SETTING,SEND_MCU_BEEP_SETTING_TIME,0,0,0,0,0);
	}
	else if(event_code == LV_EVENT_SHORT_CLICKED)
	{
		app_root_send2mcu_key(KEY_TOUCH_NEXT);
        //ui_fm_night_theme(fm_para, THEME_NIGHT);
	}
}

static void ui_event_fm_fmband_screen(lv_event_t * e)
{
	ui_fm_para_t * fm_para = lv_event_get_user_data(e);
    lv_event_code_t event_code = lv_event_get_code(e);
    lv_obj_t * target = lv_event_get_target(e);
    lv_obj_t * current_target = lv_event_get_current_target(e);
      static __s8 last_key_type = 0;
	if(event_code == LV_EVENT_PRESSED)
	{
		app_root_msg_send_opn(GUI_MSG_BEEP_SETTING,SEND_MCU_BEEP_SETTING_TIME,0,0,0,0,0);
	}
	else if(event_code == LV_EVENT_SHORT_CLICKED)
	{
		if(last_key_type==-1)
		{
			last_key_type = 0;
			return;
		}
		if(fm_para->switch_band)
			return;
		ui_fm_ctrl_fmband(fm_para);
	}
	else if(event_code == LV_EVENT_PRESS_MOVEING)
	{
		last_key_type = -1;
	}
	
}

static void ui_event_fm_amband_screen(lv_event_t * e)
{
	ui_fm_para_t * fm_para = lv_event_get_user_data(e);
	lv_event_code_t event_code = lv_event_get_code(e);
	lv_obj_t * target = lv_event_get_target(e);
	lv_obj_t * current_target = lv_event_get_current_target(e);
	 static __s8 last_key_type = 0;	
	if(event_code == LV_EVENT_PRESSED)
	{
		app_root_msg_send_opn(GUI_MSG_BEEP_SETTING,SEND_MCU_BEEP_SETTING_TIME,0,0,0,0,0);
	}
	else if(event_code == LV_EVENT_SHORT_CLICKED)
	{
		if(last_key_type==-1)
		{
			last_key_type = 0;
			return;
		}
		if(fm_para->switch_band)
			return;
		ui_fm_ctrl_amband(fm_para);
	}
	else if(event_code == LV_EVENT_PRESS_MOVEING)
	{
		last_key_type = -1;
	}
}

static void ui_event_fm_preset_screen(lv_event_t * e)
{
	__u32 i;
	ui_fm_para_t * fm_para = lv_event_get_user_data(e);
	lv_event_code_t event_code = lv_event_get_code(e);
	lv_obj_t * target = lv_event_get_target(e);
	lv_obj_t * current_target = lv_event_get_current_target(e);
	static __u32 preset_index;
	static __s8 last_key_type = 0;	
    static __u8 move_flag = 0;
    reg_fm_para_t* para = (reg_fm_para_t*)dsk_reg_get_para_by_app(REG_APP_FM);

	if((current_target == target) || lv_obj_has_flag(current_target, LV_OBJ_FLAG_HIDDEN))
	{
		return;
	}

	if(event_code == LV_EVENT_PRESSED)
	{
        ui_source_menu_sel_group_update(fm_para->source_para, &fm_para->ui_list_para, fm_view_draw_listview_item, fm_para, SOURCE_MENU_GROUP_ADD, 0);
        move_flag = 0;
		app_root_msg_send_opn(GUI_MSG_BEEP_SETTING,SEND_MCU_BEEP_SETTING_TIME,0,0,0,0,0);		
	}
	else if(event_code == LV_EVENT_SHORT_CLICKED)
	{
        if(move_flag)
        {
            return;
        }
        preset_index = lv_obj_get_index(current_target) * 2 + lv_obj_get_index(target);

        if(para->FM1_3_AM1_2_freq[fm_para->curFM1_3AM1_2_id][preset_index] == 0)
        {
            para->FM1_3_AM1_2_freq[fm_para->curFM1_3AM1_2_id][preset_index] = fm_para->fmchannelfreq;
            fm_view_update_preset(fm_para, 1);
        }
        else
        {
            ui_fm_switch_win(fm_para, FM_MENU_WIN_NONE);
            ui_source_panel_close(fm_para->source_para, LV_ANIM_ON);
            //lv_imgbtn_set_state(fm_para->source_para->ui_top_right, LV_IMGBTN_STATE_RELEASED);
            //lv_obj_add_flag(fm_para->source_para->ui_rig_list_panel, LV_OBJ_FLAG_HIDDEN);
            //lv_obj_remove_flag(fm_para->source_para->ui_bottom, LV_OBJ_FLAG_HIDDEN);      /// Flags
        }
	}
	else if(event_code == LV_EVENT_LONG_PRESSED)
	{
#if 1
        preset_index = lv_obj_get_index(current_target) * 2 + lv_obj_get_index(target);
        //eLIBs_printf("preset_index=%d\n", preset_index);
        
        para->FM1_3_AM1_2_freq[fm_para->curFM1_3AM1_2_id][preset_index] = fm_para->fmchannelfreq;
#else
		preset_index = lv_obj_get_index(current_target);
		__fm_msg("target=0x%x, preset_index=%d\n", target, preset_index);

        para->FM1_3_AM1_2_freq[fm_para->curFM1_3AM1_2_id][preset_index] = fm_para->fmchannelfreq;
#endif
        fm_view_update_preset(fm_para, 1);
	}
    else if(event_code == LV_EVENT_PRESS_MOVEING)
	{		
        move_flag = 1;
	}
	else if(event_code == LV_EVENT_CLICKED)
	{		
        move_flag = 0;
	}
    else if(event_code == LV_EVENT_FOCUSED)
	{		
        __fm_msg("ui_event_fm_preset_screen LV_EVENT_FOCUSED target=%p", target);
	}
}

static void ui_event_fm_menu_item_screen(lv_event_t * e)
{
	__u32 i;
	ui_fm_para_t * fm_para = lv_event_get_user_data(e);
	lv_event_code_t event_code = lv_event_get_code(e);
	lv_obj_t * target = lv_event_get_target(e);
	lv_obj_t * current_target = lv_event_get_current_target(e);
    static __u8 move_flag = 0;

	if(event_code == LV_EVENT_PRESSED)
	{
        ui_source_menu_sel_group_update(fm_para->source_para, &fm_para->ui_list_para, fm_view_draw_listview_item, fm_para, SOURCE_MENU_GROUP_ADD, 0);
		app_root_msg_send_opn(GUI_MSG_BEEP_SETTING,SEND_MCU_BEEP_SETTING_TIME,0,0,0,0,0);		
	}
	else if(event_code == LV_EVENT_SHORT_CLICKED)
	{
        if(move_flag)
        {
            return;
        }
        
	}
    else if(event_code == LV_EVENT_PRESS_MOVEING)
	{		
        move_flag = 1;
	}
	else if(event_code == LV_EVENT_CLICKED)
	{		
        move_flag = 0;
	}
}

static void ui_event_fm_menu_arrow_screen(lv_event_t * e)
{
	__u32 index, item_index;
	ui_fm_para_t * fm_para = lv_event_get_user_data(e);
    lv_event_code_t event_code = lv_event_get_code(e);
    lv_obj_t * target = lv_event_get_target(e);
    lv_obj_t * current_target = lv_event_get_current_target(e);
    lv_obj_t * ui_list_item_arrow = lv_obj_get_parent(target);
    lv_obj_t * ui_list_item_content_panel = lv_obj_get_parent(ui_list_item_arrow);
    lv_obj_t * ui_list_item_panel = lv_obj_get_parent(ui_list_item_content_panel);
    lv_obj_t * ui_list_item = lv_obj_get_parent(ui_list_item_panel);
	
	if(event_code == LV_EVENT_PRESSED)
	{
        ui_source_menu_sel_group_update(fm_para->source_para, &fm_para->ui_list_para, fm_view_draw_listview_item, fm_para, SOURCE_MENU_GROUP_ADD, 0);
	}
	else if(event_code == LV_EVENT_VALUE_CHANGED)
	{
        ui_fm_menu_arrow_handler(fm_para, ui_list_item, target);
	}
}

void ui_event_fm_play_logo_panel(lv_event_t * e)
{
    lv_event_code_t event_code = lv_event_get_code(e);
	ui_fm_para_t * fm_para = lv_event_get_user_data(e);

    if(event_code == LV_EVENT_PRESSED)
	{
		//ui_fm_keystripe_anim(fm_para);
		app_root_msg_send_opn(GUI_MSG_BEEP_SETTING,SEND_MCU_BEEP_SETTING_TIME,0,0,0,0,0);
	}
	else if(event_code == LV_EVENT_SHORT_CLICKED) {
        ui_fm_source_event_handler(fm_para);
	}
}

void ui_event_fm_bottom_list_panel(lv_event_t * e)
{
    lv_event_code_t event_code = lv_event_get_code(e);
	ui_fm_para_t * fm_para = lv_event_get_user_data(e);

    if(event_code == LV_EVENT_PRESSED)
	{
		//ui_fm_keystripe_anim(fm_para);
		app_root_msg_send_opn(GUI_MSG_BEEP_SETTING,SEND_MCU_BEEP_SETTING_TIME,0,0,0,0,0);
	}
	else if(event_code == LV_EVENT_SHORT_CLICKED) 
    {
        ui_fm_switch_win(fm_para, FM_MENU_WIN_LIST);
	}
}

void ui_event_fm_bottom_auto_panel(lv_event_t * e)
{
    lv_event_code_t event_code = lv_event_get_code(e);
	ui_fm_para_t * fm_para = lv_event_get_user_data(e);

    if(event_code == LV_EVENT_PRESSED)
	{
		//ui_fm_keystripe_anim(fm_para);
		app_root_msg_send_opn(GUI_MSG_BEEP_SETTING,SEND_MCU_BEEP_SETTING_TIME,0,0,0,0,0);
	}
	else if(event_code == LV_EVENT_SHORT_CLICKED) 
    {
        ui_fm_func_auto_store(fm_para);
	}
}

void ui_event_fm_bottom_local_panel(lv_event_t * e)
{
    lv_event_code_t event_code = lv_event_get_code(e);
	ui_fm_para_t * fm_para = lv_event_get_user_data(e);

    if(event_code == LV_EVENT_PRESSED)
	{
		//ui_fm_keystripe_anim(fm_para);
		app_root_msg_send_opn(GUI_MSG_BEEP_SETTING,SEND_MCU_BEEP_SETTING_TIME,0,0,0,0,0);
	}
	else if(event_code == LV_EVENT_SHORT_CLICKED) 
    {
        if(fm_para->local_onoff == 1)
            fm_para->local_onoff = 0;
        else
            fm_para->local_onoff = 1;
	}
}

void ui_event_fm_bottom_direct_panel(lv_event_t * e)
{
    static __u8 pressed_flag = 0;
    lv_event_code_t event_code = lv_event_get_code(e);
	ui_fm_para_t * fm_para = lv_event_get_user_data(e);

    if(event_code == LV_EVENT_PRESSED)
	{
        pressed_flag = 1;
		app_root_msg_send_opn(GUI_MSG_BEEP_SETTING,SEND_MCU_BEEP_SETTING_TIME,0,0,0,0,0);
	}
	else if(event_code == LV_EVENT_SHORT_CLICKED) 
    {
        if(pressed_flag)
            ui_fm_switch_win(fm_para, FM_MENU_WIN_DIRECT);
	}
    else if(event_code == LV_EVENT_CLICKED)
    {
        pressed_flag = 0;
    }
}

void ui_event_fm_direct_code_keyboard(lv_event_t * e)
{
    char * text;
    lv_event_code_t event_code = lv_event_get_code(e);
	ui_fm_para_t * fm_para = lv_event_get_user_data(e);

    if(event_code == LV_EVENT_READY || event_code == LV_EVENT_CANCEL)
    {
        text = lv_textarea_get_text(ui_fm_code_textarea);
    
        ui_fm_switch_win(fm_para, FM_MENU_WIN_NONE);
	}
}

void ui_event_fm_top_left(lv_event_t * e)
{
    lv_event_code_t event_code = lv_event_get_code(e);
	ui_fm_para_t * fm_para = lv_event_get_user_data(e);

    if(event_code == LV_EVENT_PRESSED)
	{
		//ui_fm_keystripe_anim(fm_para);
		app_root_msg_send_opn(GUI_MSG_BEEP_SETTING,SEND_MCU_BEEP_SETTING_TIME,0,0,0,0,0);
	}
	else if(event_code == LV_EVENT_SHORT_CLICKED) {
        ui_fm_top_left_event_handler(fm_para);
	}
}

void ui_event_fm_top_right(lv_event_t * e)
{
    lv_event_code_t event_code = lv_event_get_code(e);
	ui_fm_para_t * fm_para = lv_event_get_user_data(e);

    if(event_code == LV_EVENT_PRESSED)
	{
		//ui_fm_keystripe_anim(fm_para);
		app_root_msg_send_opn(GUI_MSG_BEEP_SETTING,SEND_MCU_BEEP_SETTING_TIME,0,0,0,0,0);
	}
	else if(event_code == LV_EVENT_SHORT_CLICKED) {
        ui_fm_top_right_event_handler(fm_para);
	}
}

void ui_event_fm_open_bar(lv_event_t * e)
{
    lv_event_code_t event_code = lv_event_get_code(e);
	ui_fm_para_t * fm_para = lv_event_get_user_data(e);

    if(event_code == LV_EVENT_PRESSED)
	{
		//ui_fm_keystripe_anim(fm_para);
		app_root_msg_send_opn(GUI_MSG_BEEP_SETTING,SEND_MCU_BEEP_SETTING_TIME,0,0,0,0,0);
	}
	else if(event_code == LV_EVENT_SHORT_CLICKED) {
        ui_fm_open_bar_menu_event_handler(fm_para);
	}
}

void ui_event_fm_top_menu(lv_event_t * e)
{
    lv_event_code_t event_code = lv_event_get_code(e);
	ui_fm_para_t * fm_para = lv_event_get_user_data(e);

    if(event_code == LV_EVENT_PRESSED)
	{
		//ui_fm_keystripe_anim(fm_para);
		app_root_msg_send_opn(GUI_MSG_BEEP_SETTING,SEND_MCU_BEEP_SETTING_TIME,0,0,0,0,0);
	}
	else if(event_code == LV_EVENT_SHORT_CLICKED) {
        if(fm_para->source_para->rig_setting_flag == 0)
        {
            if(fm_para->menu_win > FM_MENU_WIN_NONE 
            && fm_para->menu_win <= FM_MENU_WIN_FUNC 
            && fm_para->source_para->menu_group_flag == SOURCE_MENU_GROUP_ADD 
            && fm_para->source_para->menu_win != SOURCE_MENU_WIN_MODE)
            {
                ui_source_menu_sel_group_update(fm_para->source_para, &fm_para->ui_list_para, fm_view_draw_listview_item, fm_para, SOURCE_MENU_GROUP_REMOVE_SUB, 0);
            }
            ui_fm_top_menu_event_handler(fm_para);
            ui_source_menu_sel_group_update(fm_para->source_para, &fm_para->ui_list_para, fm_view_draw_listview_item, fm_para, SOURCE_MENU_GROUP_ADD, 0);
        }
	}
}

void ui_event_fm_bottom_menu(lv_event_t * e)
{
    lv_event_code_t event_code = lv_event_get_code(e);
	ui_fm_para_t * fm_para = lv_event_get_user_data(e);

    if(event_code == LV_EVENT_PRESSED)
	{
		//ui_fm_keystripe_anim(fm_para);
		app_root_msg_send_opn(GUI_MSG_BEEP_SETTING,SEND_MCU_BEEP_SETTING_TIME,0,0,0,0,0);
	}
	else if(event_code == LV_EVENT_SHORT_CLICKED) {
        if(fm_para->source_para->rig_setting_flag == 0)
        {
            if(fm_para->menu_win > FM_MENU_WIN_NONE 
            && fm_para->menu_win <= FM_MENU_WIN_FUNC 
            && fm_para->source_para->menu_group_flag == SOURCE_MENU_GROUP_ADD 
            && fm_para->source_para->menu_win != SOURCE_MENU_WIN_MODE)
            {
                ui_source_menu_sel_group_update(fm_para->source_para, &fm_para->ui_list_para, fm_view_draw_listview_item, fm_para, SOURCE_MENU_GROUP_REMOVE_SUB, 0);
            }
            ui_fm_bottom_menu_event_handler(fm_para);
            ui_source_menu_sel_group_update(fm_para->source_para, &fm_para->ui_list_para, fm_view_draw_listview_item, fm_para, SOURCE_MENU_GROUP_ADD, 0);
        }
	}
}

static void ui_event_fm_timer_cb(lv_timer_t * t)//(ui_home_para_t * home_para)
{
    //LV_UNUSED(t);
	ui_fm_para_t * fm_para = (ui_fm_para_t *)lv_timer_get_user_data(t);
	reg_fm_para_t* para;
	para = (reg_fm_para_t*)dsk_reg_get_para_by_app(REG_APP_FM);

	if(ui_fm_para == NULL)
	{
		eLIBs_printf("ui_fm_para == NULL ui_event_fm_timer_cb!!!!!!!!!\n");
		return;
	}
	//__fm_msg("fm_para->win_type=%d\n", fm_para->win_type);

    ui_fm_update_timer_proc(fm_para);
}

///////////////////// SCREENS ////////////////////
static void ui_fm_menu_screen_init(ui_fm_para_t * fm_para)
{
#if 1
    if(ui_fm_menu)
    {
        lv_obj_remove_flag(ui_fm_menu, LV_OBJ_FLAG_HIDDEN);     /// Flags
        return;
    }

    ui_fm_menu = lv_obj_create(fm_para->source_para->ui_menu_panel);
    lv_obj_remove_style_all(ui_fm_menu);
    lv_obj_set_width(ui_fm_menu, lv_pct(100));
    lv_obj_set_height(ui_fm_menu, lv_pct(100));
    //lv_obj_add_flag(ui_fm_menu, LV_OBJ_FLAG_HIDDEN);     /// Flags
    lv_obj_clear_flag(ui_fm_menu, LV_OBJ_FLAG_SCROLLABLE);      /// Flags
    lv_obj_set_style_bg_color(ui_fm_menu, lv_color_hex(0x000000), LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_bg_opa(ui_fm_menu, 255, LV_PART_MAIN | LV_STATE_DEFAULT);
#else
    if(ui_fm_menu == NULL || lv_obj_is_valid(ui_fm_menu))
    {
        ui_fm_menu = lv_obj_create(fm_para->source_para->ui_rig_list_panel);
        lv_obj_remove_style_all(ui_fm_menu);
        lv_obj_set_width(ui_fm_menu, lv_pct(100));
        lv_obj_set_height(ui_fm_menu, lv_pct(100));
        //lv_obj_add_flag(ui_fm_menu, LV_OBJ_FLAG_HIDDEN);     /// Flags
        lv_obj_clear_flag(ui_fm_menu, LV_OBJ_FLAG_SCROLLABLE);      /// Flags
        lv_obj_set_style_bg_color(ui_fm_menu, lv_color_hex(0x000000), LV_PART_MAIN | LV_STATE_DEFAULT);
        lv_obj_set_style_bg_opa(ui_fm_menu, 0, LV_PART_MAIN | LV_STATE_DEFAULT);
    }
#endif
}

static void ui_fm_menu_screen_uninit(ui_fm_para_t * fm_para)
{
    if(ui_fm_menu)
    {
        lv_obj_add_flag(ui_fm_menu, LV_OBJ_FLAG_HIDDEN);     /// Flags
    }
}

static void ui_fm_func_menu_screen_init(ui_fm_para_t * fm_para)
{
    if(ui_fm_func_menu == NULL || lv_obj_is_valid(ui_fm_func_menu))
    {
        ui_fm_func_menu = lv_obj_create(fm_para->source_para->ui_menu_panel);//fm_para->source_para->ui_rig_func_panel
        lv_obj_remove_style_all(ui_fm_func_menu);
        lv_obj_set_width(ui_fm_func_menu, lv_pct(100));
        lv_obj_set_height(ui_fm_func_menu, lv_pct(100));
        //lv_obj_add_flag(ui_fm_func_menu, LV_OBJ_FLAG_HIDDEN);     /// Flags
        lv_obj_clear_flag(ui_fm_func_menu, LV_OBJ_FLAG_SCROLLABLE);      /// Flags
        lv_obj_set_style_bg_color(ui_fm_func_menu, lv_color_hex(0x000000), LV_PART_MAIN | LV_STATE_DEFAULT);
        lv_obj_set_style_bg_opa(ui_fm_func_menu, 0, LV_PART_MAIN | LV_STATE_DEFAULT);
    }
}

static void ui_fm_direct_screen_init(ui_fm_para_t * fm_para)
{
    if(ui_fm_code_keyboard_panel)
    {
        lv_obj_remove_flag(ui_fm_code_keyboard_panel, LV_OBJ_FLAG_HIDDEN);     /// Flags
        lv_group_focus_obj(ui_fm_code_keyboard);
        lv_group_set_editing(lv_group_get_default(), true);
        lv_obj_set_state(ui_fm_code_keyboard_panel, LV_STATE_PRESSED, 0);
        return;
    }
    
    ui_fm_code_keyboard_panel = lv_obj_create(fm_para->source_para->ui_menu_panel);
    lv_obj_remove_style_all(ui_fm_code_keyboard_panel);
    lv_obj_set_width(ui_fm_code_keyboard_panel, lv_pct(100));
    lv_obj_set_height(ui_fm_code_keyboard_panel, lv_pct(100));
    lv_obj_clear_flag(ui_fm_code_keyboard_panel, LV_OBJ_FLAG_SCROLLABLE);      /// Flags
    lv_obj_set_style_bg_color(ui_fm_code_keyboard_panel, lv_color_hex(0x000000), LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_bg_opa(ui_fm_code_keyboard_panel, 255, LV_PART_MAIN | LV_STATE_DEFAULT);

    ui_fm_code_keyboard = ui_keyboard_code_init(ui_fm_code_keyboard_panel);
    ui_fm_code_textarea = lv_keyboard_get_textarea(ui_fm_code_keyboard);

    lv_obj_add_event_cb(ui_fm_code_keyboard, ui_event_fm_direct_code_keyboard, LV_EVENT_ALL, fm_para);

    lv_group_focus_obj(ui_fm_code_keyboard);
    lv_group_set_editing(lv_group_get_default(), true);
}

static void ui_fm_direct_screen_uninit(ui_fm_para_t * fm_para)
{
    if(ui_fm_code_keyboard_panel)
    {
        lv_obj_add_flag(ui_fm_code_keyboard_panel, LV_OBJ_FLAG_HIDDEN);     /// Flags
    }
}

static void ui_fm_screen_init(ui_fm_para_t * fm_para)
{
	reg_root_para_t* last_root_para;

	last_root_para = (reg_root_para_t*)dsk_reg_get_para_by_app(REG_APP_ROOT);
    
    ui_fm = lv_obj_create(fm_para->source_para->ui_play);//ui_source
    lv_obj_set_width(ui_fm, lv_pct(100));
    lv_obj_set_height(ui_fm, lv_pct(100));
    lv_obj_set_align(ui_fm, LV_ALIGN_CENTER);
    lv_obj_clear_flag(ui_fm, LV_OBJ_FLAG_CLICKABLE | LV_OBJ_FLAG_SCROLLABLE);      /// Flags
    lv_obj_set_style_radius(ui_fm, 0, LV_PART_MAIN | LV_STATE_DEFAULT);
    //lv_obj_set_style_bg_color(ui_fm, lv_color_hex(0x171717), LV_PART_MAIN | LV_STATE_DEFAULT);//0x171717
    lv_obj_set_style_bg_opa(ui_fm, 0, LV_PART_MAIN | LV_STATE_DEFAULT);
    //lv_obj_set_style_bg_grad_color(ui_fm, lv_color_hex(0x000000), LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_bg_grad_dir(ui_fm, LV_GRAD_DIR_NONE, LV_PART_MAIN | LV_STATE_DEFAULT);//LV_GRAD_DIR_VER
    //lv_obj_set_style_bg_image_src(ui_fm, ui_fm_get_res(fm_para, FM_THMEM_TEMP_COM_BG_BMP), LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_border_width(ui_fm, 0, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_pad_left(ui_fm, 0, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_pad_right(ui_fm, 0, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_pad_top(ui_fm, 0, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_pad_bottom(ui_fm, 0, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_pad_row(ui_fm, 0, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_pad_column(ui_fm, 0, LV_PART_MAIN | LV_STATE_DEFAULT);

    ui_fm_play = lv_obj_create(ui_fm);
    lv_obj_set_width(ui_fm_play, lv_pct(100));
    lv_obj_set_height(ui_fm_play, lv_pct(100));//lv_pct(69)
    //lv_obj_set_x(ui_fm_play, 0);
    //lv_obj_set_y(ui_fm_play, lv_pct(14));
    lv_obj_set_flex_flow(ui_fm_play, LV_FLEX_FLOW_COLUMN);
    lv_obj_set_flex_align(ui_fm_play, LV_FLEX_ALIGN_START, LV_FLEX_ALIGN_START, LV_FLEX_ALIGN_START);
    lv_obj_remove_flag(ui_fm_play, LV_OBJ_FLAG_SCROLLABLE);      /// Flags
    lv_obj_set_style_radius(ui_fm_play, 0, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_bg_color(ui_fm_play, lv_color_hex(0x000000), LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_bg_opa(ui_fm_play, 0, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_border_width(ui_fm_play, 0, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_pad_left(ui_fm_play, 0, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_pad_right(ui_fm_play, 0, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_pad_top(ui_fm_play, 0, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_pad_bottom(ui_fm_play, 0, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_pad_row(ui_fm_play, 0, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_pad_column(ui_fm_play, 0, LV_PART_MAIN | LV_STATE_DEFAULT);

    ui_fm_play_top = lv_obj_create(ui_fm_play);
    lv_obj_set_width(ui_fm_play_top, lv_pct(88));
    lv_obj_set_height(ui_fm_play_top, lv_pct(20));
    lv_obj_set_flex_flow(ui_fm_play_top, LV_FLEX_FLOW_ROW);
    lv_obj_set_flex_align(ui_fm_play_top, LV_FLEX_ALIGN_END, LV_FLEX_ALIGN_CENTER, LV_FLEX_ALIGN_CENTER);
    lv_obj_remove_flag(ui_fm_play_top, LV_OBJ_FLAG_SCROLLABLE);      /// Flags
    lv_obj_set_style_radius(ui_fm_play_top, 0, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_bg_color(ui_fm_play_top, lv_color_hex(0xFFFFFF), LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_bg_opa(ui_fm_play_top, 0, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_border_width(ui_fm_play_top, 0, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_pad_left(ui_fm_play_top, 0, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_pad_right(ui_fm_play_top, 0, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_pad_top(ui_fm_play_top, 0, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_pad_bottom(ui_fm_play_top, 0, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_pad_row(ui_fm_play_top, 0, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_pad_column(ui_fm_play_top, 20, LV_PART_MAIN | LV_STATE_DEFAULT);

    ui_fm_play_top_low = lv_image_create(ui_fm_play_top);
    lv_image_set_src(ui_fm_play_top_low, ui_fm_get_res(fm_para, FM_THMEM_TEMP_COM_LOW_VOLTAGE_BMP));
    lv_obj_set_width(ui_fm_play_top_low, LV_SIZE_CONTENT);   /// 1
    lv_obj_set_height(ui_fm_play_top_low, LV_SIZE_CONTENT);    /// 1
    lv_obj_set_x(ui_fm_play_top_low, -93);
    lv_obj_set_y(ui_fm_play_top_low, 1);
    lv_obj_set_align(ui_fm_play_top_low, LV_ALIGN_RIGHT_MID);
    lv_obj_add_flag(ui_fm_play_top_low, LV_OBJ_FLAG_HIDDEN | LV_OBJ_FLAG_CLICKABLE);     /// Flags
    lv_obj_remove_flag(ui_fm_play_top_low, LV_OBJ_FLAG_SCROLLABLE);      /// Flags

    ui_fm_play_top_charge = lv_image_create(ui_fm_play_top);
    lv_image_set_src(ui_fm_play_top_charge, ui_fm_get_res(fm_para, FM_THMEM_TEMP_COM_PD_CHARGE_BMP));
    lv_obj_set_width(ui_fm_play_top_charge, LV_SIZE_CONTENT);   /// 1
    lv_obj_set_height(ui_fm_play_top_charge, LV_SIZE_CONTENT);    /// 1
    lv_obj_set_align(ui_fm_play_top_charge, LV_ALIGN_RIGHT_MID);
    lv_obj_add_flag(ui_fm_play_top_charge, LV_OBJ_FLAG_HIDDEN | LV_OBJ_FLAG_CLICKABLE);     /// Flags
    lv_obj_remove_flag(ui_fm_play_top_charge, LV_OBJ_FLAG_SCROLLABLE);      /// Flags

    ui_fm_play_top_eq = lv_image_create(ui_fm_play_top);
    lv_image_set_src(ui_fm_play_top_eq, ui_fm_get_res(fm_para, FM_THMEM_TEMP_COM_PUNCH_EQ_BMP));
    lv_obj_set_width(ui_fm_play_top_eq, LV_SIZE_CONTENT);   /// 1
    lv_obj_set_height(ui_fm_play_top_eq, LV_SIZE_CONTENT);    /// 1
    lv_obj_set_align(ui_fm_play_top_eq, LV_ALIGN_RIGHT_MID);
    lv_obj_add_flag(ui_fm_play_top_eq, LV_OBJ_FLAG_CLICKABLE);     /// Flags
    lv_obj_remove_flag(ui_fm_play_top_eq, LV_OBJ_FLAG_SCROLLABLE);      /// Flags

    ui_fm_play_mid = lv_obj_create(ui_fm_play);
    lv_obj_set_width(ui_fm_play_mid, lv_pct(92));
    lv_obj_set_height(ui_fm_play_mid, lv_pct(60));
    lv_obj_set_x(ui_fm_play_mid, lv_pct(-86));
    lv_obj_set_y(ui_fm_play_mid, lv_pct(0));
    lv_obj_set_flex_flow(ui_fm_play_mid, LV_FLEX_FLOW_ROW);
    lv_obj_set_flex_align(ui_fm_play_mid, LV_FLEX_ALIGN_START, LV_FLEX_ALIGN_CENTER, LV_FLEX_ALIGN_CENTER);
    lv_obj_remove_flag(ui_fm_play_mid, LV_OBJ_FLAG_CLICKABLE | LV_OBJ_FLAG_SCROLLABLE);      /// Flags
    lv_obj_set_style_radius(ui_fm_play_mid, 0, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_bg_color(ui_fm_play_mid, lv_color_hex(0xB54444), LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_bg_opa(ui_fm_play_mid, 0, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_border_width(ui_fm_play_mid, 0, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_pad_left(ui_fm_play_mid, 0, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_pad_right(ui_fm_play_mid, 0, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_pad_top(ui_fm_play_mid, 0, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_pad_bottom(ui_fm_play_mid, 0, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_pad_row(ui_fm_play_mid, 0, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_pad_column(ui_fm_play_mid, 0, LV_PART_MAIN | LV_STATE_DEFAULT);

    ui_fm_play_logo_panel = lv_obj_create(ui_fm_play_mid);
    lv_obj_set_width(ui_fm_play_logo_panel, lv_pct(40));
    lv_obj_set_height(ui_fm_play_logo_panel, lv_pct(100));
    lv_obj_set_align(ui_fm_play_logo_panel, LV_ALIGN_CENTER);
    lv_obj_remove_flag(ui_fm_play_logo_panel, LV_OBJ_FLAG_SCROLLABLE);      /// Flags
    lv_obj_set_style_radius(ui_fm_play_logo_panel, 0, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_bg_color(ui_fm_play_logo_panel, lv_color_hex(0xFFFFFF), LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_bg_opa(ui_fm_play_logo_panel, 0, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_border_width(ui_fm_play_logo_panel, 0, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_pad_left(ui_fm_play_logo_panel, 0, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_pad_right(ui_fm_play_logo_panel, 0, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_pad_top(ui_fm_play_logo_panel, 0, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_pad_bottom(ui_fm_play_logo_panel, 0, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_pad_row(ui_fm_play_logo_panel, 0, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_pad_column(ui_fm_play_logo_panel, 0, LV_PART_MAIN | LV_STATE_DEFAULT);

    ui_fm_play_logo = lv_image_create(ui_fm_play_logo_panel);
    lv_img_set_src(ui_fm_play_logo, ui_fm_get_res(fm_para, FM_THMEM_FM_ICON_BMP));
    lv_obj_set_width(ui_fm_play_logo, LV_SIZE_CONTENT);   /// 214
    lv_obj_set_height(ui_fm_play_logo, LV_SIZE_CONTENT);    /// 214
    lv_obj_set_x(ui_fm_play_logo, lv_pct(2));
    lv_obj_set_y(ui_fm_play_logo, lv_pct(0));
    lv_obj_set_align(ui_fm_play_logo, LV_ALIGN_CENTER);
    lv_obj_add_flag(ui_fm_play_logo, LV_OBJ_FLAG_ADV_HITTEST);     /// Flags
    lv_obj_clear_flag(ui_fm_play_logo, LV_OBJ_FLAG_SCROLLABLE);      /// Flags

    ui_fm_play_logo_text = lv_label_create(ui_fm_play_logo);
    lv_obj_set_width(ui_fm_play_logo_text, LV_SIZE_CONTENT);   /// 1
    lv_obj_set_height(ui_fm_play_logo_text, LV_SIZE_CONTENT);    /// 1
    lv_obj_set_y(ui_fm_play_logo_text, lv_pct(-10));
    lv_obj_set_align(ui_fm_play_logo_text, LV_ALIGN_BOTTOM_MID);
    //lv_obj_add_flag(ui_fm_play_logo_text, LV_OBJ_FLAG_HIDDEN);     /// Flags
    if(app_root_mcu_get_source() == ID_WORK_SOURCE_FM)
        lv_label_set_text(ui_fm_play_logo_text, "FM");
    else if(app_root_mcu_get_source() == ID_WORK_SOURCE_AM)
        lv_label_set_text(ui_fm_play_logo_text, "AM");
    else if(app_root_mcu_get_source() == ID_WORK_SOURCE_WB)
        lv_label_set_text(ui_fm_play_logo_text, "WB");
    lv_obj_set_style_text_color(ui_fm_play_logo_text, lv_color_hex(0xFFFFFF), LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_text_opa(ui_fm_play_logo_text, 255, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_text_font(ui_fm_play_logo_text, lv_font_small.font, LV_PART_MAIN | LV_STATE_DEFAULT);//&ui_font_Archivo_56

    ui_fm_play_disp = lv_obj_create(ui_fm_play_mid);
    lv_obj_set_width(ui_fm_play_disp, lv_pct(60));
    lv_obj_set_height(ui_fm_play_disp, lv_pct(90));
    lv_obj_set_align(ui_fm_play_disp, LV_ALIGN_RIGHT_MID);
    lv_obj_remove_flag(ui_fm_play_disp, LV_OBJ_FLAG_SCROLLABLE);      /// Flags
    lv_obj_set_style_radius(ui_fm_play_disp, 0, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_bg_color(ui_fm_play_disp, lv_color_hex(0x000000), LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_bg_opa(ui_fm_play_disp, 0, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_border_width(ui_fm_play_disp, 0, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_pad_left(ui_fm_play_disp, 0, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_pad_right(ui_fm_play_disp, 0, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_pad_top(ui_fm_play_disp, 0, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_pad_bottom(ui_fm_play_disp, 0, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_pad_row(ui_fm_play_disp, 0, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_pad_column(ui_fm_play_disp, 0, LV_PART_MAIN | LV_STATE_DEFAULT);

    ui_fm_play_disp_top = lv_obj_create(ui_fm_play_disp);
    lv_obj_set_width(ui_fm_play_disp_top, lv_pct(100));
    lv_obj_set_height(ui_fm_play_disp_top, lv_pct(25));
    lv_obj_set_flex_flow(ui_fm_play_disp_top, LV_FLEX_FLOW_ROW);
    lv_obj_set_flex_align(ui_fm_play_disp_top, LV_FLEX_ALIGN_START, LV_FLEX_ALIGN_CENTER, LV_FLEX_ALIGN_CENTER);
    lv_obj_remove_flag(ui_fm_play_disp_top, LV_OBJ_FLAG_SCROLLABLE);      /// Flags
    lv_obj_set_style_radius(ui_fm_play_disp_top, 0, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_bg_color(ui_fm_play_disp_top, lv_color_hex(0xFFFFFF), LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_bg_opa(ui_fm_play_disp_top, 0, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_border_width(ui_fm_play_disp_top, 0, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_pad_left(ui_fm_play_disp_top, 8, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_pad_right(ui_fm_play_disp_top, 0, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_pad_top(ui_fm_play_disp_top, 0, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_pad_bottom(ui_fm_play_disp_top, 0, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_pad_row(ui_fm_play_disp_top, 0, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_pad_column(ui_fm_play_disp_top, 40, LV_PART_MAIN | LV_STATE_DEFAULT);

    ui_fm_play_disp_preset = lv_label_create(ui_fm_play_disp_top);
    lv_obj_set_width(ui_fm_play_disp_preset, LV_SIZE_CONTENT);   /// 1
    lv_obj_set_height(ui_fm_play_disp_preset, LV_SIZE_CONTENT);    /// 1
    lv_obj_set_x(ui_fm_play_disp_preset, 8);
    lv_obj_set_y(ui_fm_play_disp_preset, 0);
    lv_obj_set_align(ui_fm_play_disp_preset, LV_ALIGN_LEFT_MID);
    lv_obj_add_flag(ui_fm_play_disp_preset, LV_OBJ_FLAG_HIDDEN);     /// Flags
    //lv_label_set_text(ui_fm_play_disp_preset, "P4");
    lv_obj_set_style_text_color(ui_fm_play_disp_preset, lv_color_hex(0x000000), LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_text_opa(ui_fm_play_disp_preset, 255, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_text_font(ui_fm_play_disp_preset, lv_font_xxxsmall.font, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_radius(ui_fm_play_disp_preset, 4, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_bg_color(ui_fm_play_disp_preset, lv_color_hex(0x808080), LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_bg_opa(ui_fm_play_disp_preset, 255, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_pad_left(ui_fm_play_disp_preset, 3, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_pad_right(ui_fm_play_disp_preset, 3, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_pad_top(ui_fm_play_disp_preset, 3, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_pad_bottom(ui_fm_play_disp_preset, 0, LV_PART_MAIN | LV_STATE_DEFAULT);

    ui_fm_play_disp_seek = lv_label_create(ui_fm_play_disp_top);
    lv_obj_set_width(ui_fm_play_disp_seek, LV_SIZE_CONTENT);   /// 1
    lv_obj_set_height(ui_fm_play_disp_seek, LV_SIZE_CONTENT);    /// 1
    lv_obj_set_x(ui_fm_play_disp_seek, 8);
    lv_obj_set_y(ui_fm_play_disp_seek, 0);
    lv_obj_set_align(ui_fm_play_disp_seek, LV_ALIGN_LEFT_MID);
    lv_obj_add_flag(ui_fm_play_disp_seek, LV_OBJ_FLAG_HIDDEN);     /// Flags
    lv_label_set_text(ui_fm_play_disp_seek, "seek");
    lv_obj_set_style_text_color(ui_fm_play_disp_seek, lv_color_hex(0x808080), LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_text_opa(ui_fm_play_disp_seek, 255, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_text_font(ui_fm_play_disp_seek, lv_font_small.font, LV_PART_MAIN | LV_STATE_DEFAULT);

    ui_fm_play_disp_freq = lv_obj_create(ui_fm_play_disp);
    lv_obj_set_width(ui_fm_play_disp_freq, lv_pct(100));
    lv_obj_set_height(ui_fm_play_disp_freq, lv_pct(50));    /// 71
    lv_obj_set_align(ui_fm_play_disp_freq, LV_ALIGN_LEFT_MID);
    lv_obj_set_flex_flow(ui_fm_play_disp_freq, LV_FLEX_FLOW_ROW_WRAP);
    lv_obj_set_flex_align(ui_fm_play_disp_freq, LV_FLEX_ALIGN_START, LV_FLEX_ALIGN_END, LV_FLEX_ALIGN_CENTER);
    lv_obj_remove_flag(ui_fm_play_disp_freq, LV_OBJ_FLAG_SCROLLABLE);      /// Flags
    lv_obj_set_style_radius(ui_fm_play_disp_freq, 0, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_bg_color(ui_fm_play_disp_freq, lv_color_hex(0x000000), LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_bg_opa(ui_fm_play_disp_freq, 0, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_border_width(ui_fm_play_disp_freq, 0, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_pad_left(ui_fm_play_disp_freq, 0, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_pad_right(ui_fm_play_disp_freq, 0, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_pad_top(ui_fm_play_disp_freq, 0, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_pad_bottom(ui_fm_play_disp_freq, 0, LV_PART_MAIN | LV_STATE_DEFAULT);

    ui_fm_play_disp_freq_num = lv_label_create(ui_fm_play_disp_freq);
    lv_obj_set_width(ui_fm_play_disp_freq_num, LV_SIZE_CONTENT);   /// 1
    lv_obj_set_height(ui_fm_play_disp_freq_num, LV_SIZE_CONTENT);    /// 1
    lv_obj_set_align(ui_fm_play_disp_freq_num, LV_ALIGN_RIGHT_MID);
    //lv_label_set_text(ui_fm_play_disp_freq_num, "104.7");
    lv_obj_set_style_text_color(ui_fm_play_disp_freq_num, lv_color_hex(0xFFFFFF), LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_text_opa(ui_fm_play_disp_freq_num, 255, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_text_font(ui_fm_play_disp_freq_num, lv_font_xxxlarge.font, LV_PART_MAIN | LV_STATE_DEFAULT);//&ui_font_Archivo_80
    __fm_msg("ui_fm_play_disp_freq_num=%p\n", ui_fm_play_disp_freq_num);

    ui_fm_play_disp_freq_unit_panel = lv_obj_create(ui_fm_play_disp_freq);
    lv_obj_set_width(ui_fm_play_disp_freq_unit_panel, LV_SIZE_CONTENT);   /// 100
    lv_obj_set_height(ui_fm_play_disp_freq_unit_panel, LV_SIZE_CONTENT);    /// 96
    lv_obj_set_align(ui_fm_play_disp_freq_unit_panel, LV_ALIGN_LEFT_MID);
    lv_obj_set_flex_flow(ui_fm_play_disp_freq_unit_panel, LV_FLEX_FLOW_COLUMN_WRAP);
    lv_obj_set_flex_align(ui_fm_play_disp_freq_unit_panel, LV_FLEX_ALIGN_START, LV_FLEX_ALIGN_START, LV_FLEX_ALIGN_START);
    lv_obj_remove_flag(ui_fm_play_disp_freq_unit_panel, LV_OBJ_FLAG_SCROLLABLE);      /// Flags
    lv_obj_set_style_radius(ui_fm_play_disp_freq_unit_panel, 0, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_bg_color(ui_fm_play_disp_freq_unit_panel, lv_color_hex(0x000000), LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_bg_opa(ui_fm_play_disp_freq_unit_panel, 0, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_border_width(ui_fm_play_disp_freq_unit_panel, 0, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_pad_left(ui_fm_play_disp_freq_unit_panel, 0, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_pad_right(ui_fm_play_disp_freq_unit_panel, 0, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_pad_top(ui_fm_play_disp_freq_unit_panel, 0, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_pad_bottom(ui_fm_play_disp_freq_unit_panel, 0, LV_PART_MAIN | LV_STATE_DEFAULT);

    ui_fm_play_disp_freq_loc = lv_label_create(ui_fm_play_disp_freq_unit_panel);
    lv_obj_set_width(ui_fm_play_disp_freq_loc, LV_SIZE_CONTENT);   /// 1
    lv_obj_set_height(ui_fm_play_disp_freq_loc, LV_SIZE_CONTENT);    /// 1
    lv_obj_set_x(ui_fm_play_disp_freq_loc, lv_pct(-6));
    lv_obj_set_y(ui_fm_play_disp_freq_loc, lv_pct(11));
    lv_obj_set_align(ui_fm_play_disp_freq_loc, LV_ALIGN_TOP_RIGHT);
    lv_obj_add_flag(ui_fm_play_disp_freq_loc, LV_OBJ_FLAG_HIDDEN);     /// Flags
    lv_label_set_text(ui_fm_play_disp_freq_loc, "LOC");
    lv_obj_set_style_text_color(ui_fm_play_disp_freq_loc, lv_color_hex(0x808080), LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_text_opa(ui_fm_play_disp_freq_loc, 255, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_text_font(ui_fm_play_disp_freq_loc, lv_font_xxsmall.font, LV_PART_MAIN | LV_STATE_DEFAULT);

    ui_fm_play_disp_freq_unit = lv_label_create(ui_fm_play_disp_freq_unit_panel);
    lv_obj_set_width(ui_fm_play_disp_freq_unit, LV_SIZE_CONTENT);   /// 1
    lv_obj_set_height(ui_fm_play_disp_freq_unit, LV_SIZE_CONTENT);    /// 1
    lv_obj_set_align(ui_fm_play_disp_freq_unit, LV_ALIGN_RIGHT_MID);
    //lv_label_set_text(ui_fm_play_disp_freq_unit, "MHz");
    lv_obj_set_style_text_color(ui_fm_play_disp_freq_unit, lv_color_hex(0xFFFFFF), LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_text_opa(ui_fm_play_disp_freq_unit, 255, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_text_font(ui_fm_play_disp_freq_unit, lv_font_xsmall.font, LV_PART_MAIN | LV_STATE_DEFAULT);//&ui_font_Archivo_26
    lv_obj_set_style_pad_left(ui_fm_play_disp_freq_unit, 0, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_pad_right(ui_fm_play_disp_freq_unit, 0, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_pad_top(ui_fm_play_disp_freq_unit, 0, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_pad_bottom(ui_fm_play_disp_freq_unit, 10, LV_PART_MAIN | LV_STATE_DEFAULT);

    ui_fm_play_disp_bottom = lv_obj_create(ui_fm_play_disp);
    lv_obj_set_width(ui_fm_play_disp_bottom, lv_pct(100));
    lv_obj_set_height(ui_fm_play_disp_bottom, lv_pct(25));
    lv_obj_set_align(ui_fm_play_disp_bottom, LV_ALIGN_BOTTOM_LEFT);
    lv_obj_remove_flag(ui_fm_play_disp_bottom, LV_OBJ_FLAG_SCROLLABLE);      /// Flags
    lv_obj_set_style_radius(ui_fm_play_disp_bottom, 0, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_bg_color(ui_fm_play_disp_bottom, lv_color_hex(0xFFFFFF), LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_bg_opa(ui_fm_play_disp_bottom, 0, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_border_width(ui_fm_play_disp_bottom, 0, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_pad_left(ui_fm_play_disp_bottom, 0, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_pad_right(ui_fm_play_disp_bottom, 0, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_pad_top(ui_fm_play_disp_bottom, 0, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_pad_bottom(ui_fm_play_disp_bottom, 0, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_pad_row(ui_fm_play_disp_bottom, 0, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_pad_column(ui_fm_play_disp_bottom, 0, LV_PART_MAIN | LV_STATE_DEFAULT);

    ui_fm_play_disp_pty = lv_label_create(ui_fm_play_disp_bottom);
    lv_obj_set_width(ui_fm_play_disp_pty, LV_SIZE_CONTENT);   /// 1
    lv_obj_set_height(ui_fm_play_disp_pty, LV_SIZE_CONTENT);    /// 1
    lv_obj_set_x(ui_fm_play_disp_pty, 6);
    lv_obj_set_y(ui_fm_play_disp_pty, 0);
    lv_obj_set_align(ui_fm_play_disp_pty, LV_ALIGN_LEFT_MID);
    lv_obj_add_flag(ui_fm_play_disp_pty, LV_OBJ_FLAG_HIDDEN);     /// Flags
    lv_label_set_text(ui_fm_play_disp_pty, "PTY");
    lv_obj_set_style_text_color(ui_fm_play_disp_pty, lv_color_hex(0xCCCCCC), LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_text_opa(ui_fm_play_disp_pty, 255, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_text_font(ui_fm_play_disp_pty, lv_font_medium.font, LV_PART_MAIN | LV_STATE_DEFAULT);
    
    ui_fm_bottom_list = lv_obj_create(fm_para->source_para->ui_bottom);
    lv_obj_remove_style_all(ui_fm_bottom_list);
    lv_obj_set_width(ui_fm_bottom_list, 161);
    lv_obj_set_height(ui_fm_bottom_list, 74);
    lv_obj_set_align(ui_fm_bottom_list, LV_ALIGN_BOTTOM_LEFT);
    //lv_obj_add_flag(ui_fm_bottom_list, LV_OBJ_FLAG_HIDDEN);      /// Flags
    lv_obj_clear_flag(ui_fm_bottom_list, LV_OBJ_FLAG_SCROLLABLE);      /// Flags
    lv_obj_set_style_bg_color(ui_fm_bottom_list, lv_color_hex(0x000000), LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_bg_opa(ui_fm_bottom_list, 0, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_bg_img_src(ui_fm_bottom_list, ui_fm_get_res(fm_para, FM_THEME_COM_BOT_ICON_BG_A_L_N_BMP), LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_bg_img_src(ui_fm_bottom_list, ui_fm_get_res(fm_para, FM_THEME_COM_BOT_ICON_BG_A_L_S_BMP), LV_PART_MAIN | LV_STATE_PRESSED);
    lv_obj_set_style_bg_img_src(ui_fm_bottom_list, ui_fm_get_res(fm_para, FM_THEME_COM_BOT_ICON_BG_A_L_S_BMP), LV_PART_MAIN | LV_STATE_FOCUSED);

    ui_fm_bottom_list_img = lv_img_create(ui_fm_bottom_list);
    lv_img_set_src(ui_fm_bottom_list_img, ui_fm_get_res(fm_para, FM_THEME_COM_BOT_LIST_N_BMP));
    lv_obj_set_width(ui_fm_bottom_list_img, LV_SIZE_CONTENT);   /// 42
    lv_obj_set_height(ui_fm_bottom_list_img, LV_SIZE_CONTENT);    /// 34
    lv_obj_set_align(ui_fm_bottom_list_img, LV_ALIGN_CENTER);
    lv_obj_add_flag(ui_fm_bottom_list_img, LV_OBJ_FLAG_ADV_HITTEST);
    lv_obj_clear_flag(ui_fm_bottom_list_img, LV_OBJ_FLAG_SCROLLABLE);

    ui_fm_bottom_auto = lv_obj_create(fm_para->source_para->ui_bottom);
    lv_obj_remove_style_all(ui_fm_bottom_auto);
    lv_obj_set_width(ui_fm_bottom_auto, 161);
    lv_obj_set_height(ui_fm_bottom_auto, 74);
    lv_obj_set_align(ui_fm_bottom_auto, LV_ALIGN_BOTTOM_LEFT);
    //lv_obj_add_flag(ui_fm_bottom_auto, LV_OBJ_FLAG_HIDDEN);      /// Flags
    lv_obj_clear_flag(ui_fm_bottom_auto, LV_OBJ_FLAG_SCROLLABLE);      /// Flags
    lv_obj_set_style_bg_color(ui_fm_bottom_auto, lv_color_hex(0x000000), LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_bg_opa(ui_fm_bottom_auto, 0, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_bg_img_src(ui_fm_bottom_auto, ui_fm_get_res(fm_para, FM_THEME_COM_BOT_ICON_BG_A_L_N_BMP), LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_bg_img_src(ui_fm_bottom_auto, ui_fm_get_res(fm_para, FM_THEME_COM_BOT_ICON_BG_A_L_S_BMP), LV_PART_MAIN | LV_STATE_PRESSED);
    lv_obj_set_style_bg_img_src(ui_fm_bottom_auto, ui_fm_get_res(fm_para, FM_THEME_COM_BOT_ICON_BG_A_L_S_BMP), LV_PART_MAIN | LV_STATE_FOCUSED);

	ui_fm_bottom_auto_label = lv_label_create(ui_fm_bottom_auto);
	lv_obj_set_width(ui_fm_bottom_auto_label, lv_pct(100));//lv_pct(89)
	lv_obj_set_height(ui_fm_bottom_auto_label, LV_SIZE_CONTENT);	  /// 0
	lv_obj_set_align(ui_fm_bottom_auto_label, LV_ALIGN_CENTER);
    lv_label_set_text(ui_fm_bottom_auto_label, "Auto");
	lv_label_set_long_mode(ui_fm_bottom_auto_label, LV_LABEL_LONG_CLIP);
	lv_obj_set_style_text_color(ui_fm_bottom_auto_label, lv_color_hex(0xFFFFFF), LV_PART_MAIN | LV_STATE_DEFAULT);
	lv_obj_set_style_text_opa(ui_fm_bottom_auto_label, 255, LV_PART_MAIN | LV_STATE_DEFAULT);
	lv_obj_set_style_text_font(ui_fm_bottom_auto_label, lv_font_medium.font, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_text_align(ui_fm_bottom_auto_label, LV_TEXT_ALIGN_CENTER, LV_PART_MAIN | LV_STATE_DEFAULT);

	lv_obj_set_style_text_color(ui_fm_bottom_auto_label, lv_color_hex(0x808080), LV_PART_MAIN | LV_STATE_PRESSED);

    ui_fm_bottom_local = lv_obj_create(fm_para->source_para->ui_bottom);
    lv_obj_remove_style_all(ui_fm_bottom_local);
    lv_obj_set_width(ui_fm_bottom_local, 161);
    lv_obj_set_height(ui_fm_bottom_local, 74);
    lv_obj_set_align(ui_fm_bottom_local, LV_ALIGN_BOTTOM_LEFT);
    //lv_obj_add_flag(ui_fm_bottom_local, LV_OBJ_FLAG_HIDDEN);      /// Flags
    lv_obj_clear_flag(ui_fm_bottom_local, LV_OBJ_FLAG_SCROLLABLE);      /// Flags
    lv_obj_set_style_bg_color(ui_fm_bottom_local, lv_color_hex(0x000000), LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_bg_opa(ui_fm_bottom_local, 0, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_bg_img_src(ui_fm_bottom_local, ui_fm_get_res(fm_para, FM_THEME_COM_BOT_ICON_BG_A_L_N_BMP), LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_bg_img_src(ui_fm_bottom_local, ui_fm_get_res(fm_para, FM_THEME_COM_BOT_ICON_BG_A_L_S_BMP), LV_PART_MAIN | LV_STATE_PRESSED);
    lv_obj_set_style_bg_img_src(ui_fm_bottom_local, ui_fm_get_res(fm_para, FM_THEME_COM_BOT_ICON_BG_A_L_S_BMP), LV_PART_MAIN | LV_STATE_FOCUSED);

	ui_fm_bottom_local_label = lv_label_create(ui_fm_bottom_local);
	lv_obj_set_width(ui_fm_bottom_local_label, lv_pct(100));//lv_pct(89)
	lv_obj_set_height(ui_fm_bottom_local_label, LV_SIZE_CONTENT);	  /// 0
	lv_obj_set_align(ui_fm_bottom_local_label, LV_ALIGN_CENTER);
    lv_label_set_text(ui_fm_bottom_local_label, "Local");
	lv_label_set_long_mode(ui_fm_bottom_local_label, LV_LABEL_LONG_CLIP);
	lv_obj_set_style_text_color(ui_fm_bottom_local_label, lv_color_hex(0xFFFFFF), LV_PART_MAIN | LV_STATE_DEFAULT);
	lv_obj_set_style_text_opa(ui_fm_bottom_local_label, 255, LV_PART_MAIN | LV_STATE_DEFAULT);
	lv_obj_set_style_text_font(ui_fm_bottom_local_label, lv_font_medium.font, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_text_align(ui_fm_bottom_local_label, LV_TEXT_ALIGN_CENTER, LV_PART_MAIN | LV_STATE_DEFAULT);

	lv_obj_set_style_text_color(ui_fm_bottom_local_label, lv_color_hex(0x808080), LV_PART_MAIN | LV_STATE_PRESSED);

    ui_fm_bottom_direct = lv_obj_create(fm_para->source_para->ui_bottom);
    lv_obj_remove_style_all(ui_fm_bottom_direct);
    lv_obj_set_width(ui_fm_bottom_direct, 161);
    lv_obj_set_height(ui_fm_bottom_direct, 74);
    lv_obj_set_align(ui_fm_bottom_direct, LV_ALIGN_BOTTOM_LEFT);
    //lv_obj_add_flag(ui_fm_bottom_direct, LV_OBJ_FLAG_HIDDEN);      /// Flags
    lv_obj_clear_flag(ui_fm_bottom_direct, LV_OBJ_FLAG_SCROLLABLE);      /// Flags
    lv_obj_set_style_bg_color(ui_fm_bottom_direct, lv_color_hex(0x000000), LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_bg_opa(ui_fm_bottom_direct, 0, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_bg_img_src(ui_fm_bottom_direct, ui_fm_get_res(fm_para, FM_THEME_COM_BOT_ICON_BG_A_L_N_BMP), LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_bg_img_src(ui_fm_bottom_direct, ui_fm_get_res(fm_para, FM_THEME_COM_BOT_ICON_BG_A_L_S_BMP), LV_PART_MAIN | LV_STATE_PRESSED);
    lv_obj_set_style_bg_img_src(ui_fm_bottom_direct, ui_fm_get_res(fm_para, FM_THEME_COM_BOT_ICON_BG_A_L_S_BMP), LV_PART_MAIN | LV_STATE_FOCUSED);

    ui_fm_bottom_direct_img = lv_img_create(ui_fm_bottom_direct);
    lv_img_set_src(ui_fm_bottom_direct_img, ui_fm_get_res(fm_para, FM_THEME_COM_BOT_DIRECT_N_BMP));
    lv_obj_set_width(ui_fm_bottom_direct_img, LV_SIZE_CONTENT);   /// 42
    lv_obj_set_height(ui_fm_bottom_direct_img, LV_SIZE_CONTENT);    /// 34
    lv_obj_set_align(ui_fm_bottom_direct_img, LV_ALIGN_CENTER);
    lv_obj_add_flag(ui_fm_bottom_direct_img, LV_OBJ_FLAG_ADV_HITTEST);
    lv_obj_clear_flag(ui_fm_bottom_direct_img, LV_OBJ_FLAG_SCROLLABLE);

	//lv_obj_add_event_cb(ui_fm_bottom_list, ui_event_party_mode_bottom_list_cb, LV_EVENT_ALL, fm_para);
	//lv_obj_add_event_cb(ui_fm_bottom_auto, ui_event_party_mode_bottom_browse_cb, LV_EVENT_ALL, fm_para);
	//lv_obj_add_event_cb(ui_fm_bottom_local, ui_event_party_mode_bottom_receiver_cb, LV_EVENT_ALL, fm_para);
	//lv_obj_add_event_cb(ui_fm_bottom_direct, ui_event_party_mode_bottom_receiver_cb, LV_EVENT_ALL, fm_para);

    ui_fm_timer = lv_timer_create(ui_event_fm_timer_cb, FM_TIMER_UPDATE_SEARCH_FREQ_TIMES*10, fm_para);//FM_TIMER_UPDATE_SEARCH_FREQ_TIMES

	lv_obj_add_event_cb(ui_fm, ui_event_fm_screen, LV_EVENT_ALL, fm_para);
    lv_obj_add_event_cb(ui_fm_play_logo_panel, ui_event_fm_play_logo_panel, LV_EVENT_ALL, fm_para);
    lv_obj_add_event_cb(ui_fm_bottom_list, ui_event_fm_bottom_list_panel, LV_EVENT_ALL, fm_para);
    lv_obj_add_event_cb(ui_fm_bottom_auto, ui_event_fm_bottom_auto_panel, LV_EVENT_ALL, fm_para);
    lv_obj_add_event_cb(ui_fm_bottom_local, ui_event_fm_bottom_local_panel, LV_EVENT_ALL, fm_para);
    lv_obj_add_event_cb(ui_fm_bottom_direct, ui_event_fm_bottom_direct_panel, LV_EVENT_ALL, fm_para);

    lv_obj_add_event_cb(fm_para->source_para->ui_top_left, ui_event_fm_top_left, LV_EVENT_ALL, fm_para);
	//lv_obj_add_event_cb(fm_para->source_para->ui_top_left, ui_event_fm_back_screen, LV_EVENT_ALL, fm_para);
	lv_obj_add_event_cb(fm_para->source_para->ui_top_right, ui_event_fm_top_right, LV_EVENT_ALL, fm_para);
    lv_obj_add_event_cb(fm_para->source_para->ui_rig_bar_bg, ui_event_fm_open_bar, LV_EVENT_ALL, fm_para);
    lv_obj_add_event_cb(fm_para->source_para->ui_rig_panel_button_top, ui_event_fm_top_menu, LV_EVENT_ALL, fm_para);
    lv_obj_add_event_cb(fm_para->source_para->ui_rig_panel_button_bottom, ui_event_fm_bottom_menu, LV_EVENT_ALL, fm_para);
	//lv_obj_add_event_cb(fm_para->source_para->ui_bottom_up, ui_event_fm_prve_screen, LV_EVENT_ALL, fm_para);
	//lv_obj_add_event_cb(fm_para->source_para->ui_bottom_down, ui_event_fm_next_screen, LV_EVENT_ALL, fm_para);
}

static void ui_fm_screen_uninit(ui_fm_para_t * fm_para)
{
#ifdef SWITCH_BAND_TIPS
	__fm_tips_dialog_cancel(fm_para);
#endif
	__fm_app_tips_dialog_cancel(fm_para);

    lv_obj_remove_event_cb(fm_para->source_para->ui_top_left, ui_event_fm_top_left);
	//lv_obj_remove_event_cb(fm_para->source_para->ui_top_left, ui_event_fm_back_scree);
	lv_obj_remove_event_cb(fm_para->source_para->ui_top_right, ui_event_fm_top_right);
    lv_obj_remove_event_cb(fm_para->source_para->ui_rig_bar_bg, ui_event_fm_open_bar);
    lv_obj_remove_event_cb(fm_para->source_para->ui_rig_panel_button_top, ui_event_fm_top_menu);
    lv_obj_remove_event_cb(fm_para->source_para->ui_rig_panel_button_bottom, ui_event_fm_bottom_menu);
	//lv_obj_remove_event_cb(fm_para->source_para->ui_bottom_up, ui_event_fm_prve_screen);
	//lv_obj_remove_event_cb(fm_para->source_para->ui_bottom_down, ui_event_fm_next_screen);
    if(ui_fm_menu)
	    lv_obj_del(ui_fm_menu);
    if(ui_fm_func_menu)
        lv_obj_del(ui_fm_func_menu);
    if(ui_fm_code_keyboard_panel)
        lv_obj_del(ui_fm_code_keyboard_panel);
    //if(ui_fm_bottom_list)
    //    lv_obj_del(ui_fm_bottom_list);
    //if(ui_fm_bottom_auto)
    //    lv_obj_del(ui_fm_bottom_auto);
    //if(ui_fm_bottom_local)
    //    lv_obj_del(ui_fm_bottom_local);
    //if(ui_fm_bottom_direct)
    //    lv_obj_del(ui_fm_bottom_direct);
    if(fm_para->fm_main_obj)
	    lv_obj_del(fm_para->fm_main_obj);
}

void ui_fm_init(void * para)
{
	if(!ui_fm_para)
	{
		ui_fm_para = (ui_fm_para_t *)esMEMS_Balloc(sizeof(ui_fm_para_t));
		eLIBs_memset((void*)ui_fm_para, 0, sizeof(ui_fm_para_t));

        	ui_fm_internal_para_update(ui_fm_para);
        
	        // Map MCU data operations interface
	        ui_fm_para->mcu_ops.sync_all = ui_mcu_tuner_sync_all;
	        ui_fm_para->mcu_ops.sync_dirty = ui_mcu_tuner_sync_dirty;
	        ui_fm_para->mcu_ops.ops = &ui_fm_para->source_para->mcu_v2_ops;
	        
	        // Execute initial full data sync
	        if (ui_fm_para->mcu_ops.sync_all) {
	            ui_fm_para->mcu_ops.sync_all(&ui_fm_para->ui_fm_data_para);
	        }
                
		__fm_msg("ui_fm_init\n");
		ui_fm_init_res(ui_fm_para);
		ui_fm_send_srv_msg(GUI_MSG_FM_REFRESH_INIT,NULL,NULL,APP_FM_ID,ui_fm_para,0,0);
		ui_fm_helpers_init(ui_fm_para, para);
    }
}

void ui_fm_uninit(void * para)
{
	#if 1
	if(ui_fm_para)
	{
		ui_fm_para->mode_uninit_flag = 1;
		__fm_msg("ui_fm_uninit cur_task_is_opn_handler:%d.\n",cur_task_is_opn_handler());
		__fm_msg("ui_fm_uninit cur_task_is_lvgl_handler:%d.\n",cur_task_is_lvgl_handler());
		
		ui_fm_helpers_uninit(ui_fm_para);
		ui_fm_send_srv_msg(GUI_MSG_FM_REFRESH_UNINIT,0,0,0,0,1,1);
	}
	#endif
}

