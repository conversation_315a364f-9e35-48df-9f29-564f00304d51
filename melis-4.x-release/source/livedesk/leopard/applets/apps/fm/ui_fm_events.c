// SquareLine LVGL GENERATED FILE
// EDITOR VERSION: SquareLine Studio 1.2.0
// LVGL VERSION: 8.3.4
// PROJECT: SquareLine_Project

#include "ui_fm_helpers.h"

#if USE_LOG_PRINT
	#define __fm_events_msg(...)			(eLIBs_printf("FM EVENTS MSG:L%d:", __LINE__),				 \
													  eLIBs_printf(__VA_ARGS__)) 
#else
	#define __fm_events_msg(...)
#endif


///////////////////// VARIABLES ////////////////////

///////////////////// FUNCTION ////////////////////
__s32 ui_fm_opn_msg(__gui_msg_t * pmsg, __u32 sync)
{
	__s32 ret = EPDK_OK;
	__gui_msg_t msg;

	if(!ui_fm_para)
		return;

	if(!ui_fm_para->mode_uninit_flag || (pmsg->flag > 0))
	{
	    memset(&msg, 0x00, sizeof(msg));
	    
	    __fm_events_msg("fm:pmsg->id=0x%x\n", pmsg->id);
	    
		msg.id = pmsg->id;
		msg.h_deswin	= ui_fm_para->fm_main_obj;
		msg.h_srcwin	= pmsg->h_srcwin;
		msg.dwAddData1 = (unsigned long)pmsg->dwAddData1;
		msg.dwAddData2 = (unsigned long)pmsg->dwAddData2;
		msg.dwReserved = (unsigned long)pmsg->dwReserved;
		msg.p_arg		= ui_fm_para;
		msg.app_id		= APP_FM_ID;
		msg.flag = pmsg->flag;

		if(sync == 1)
			lvgl_opn_msg_srv_send_sync_message(&msg);
		else
			lvgl_opn_msg_srv_send_message(&msg);
	}
	
	return ret;
}

__s32 ui_fm_refresh_msg(__gui_msg_t * pmsg, __u32 sync)
{
	__s32 ret = EPDK_OK;
	__gui_msg_t msg;

	if(!ui_fm_para->mode_uninit_flag || (pmsg->flag > 0))
    {
	    memset(&msg, 0x00, sizeof(msg));

		msg.id = MAKELONG(GUI_MSG_PAINT, pmsg->id);
		msg.h_deswin	= ui_fm_para->fm_main_obj ? ui_fm_para->fm_main_obj : lv_layer_top();
		msg.h_srcwin	= pmsg->h_srcwin;
		msg.dwAddData1 = (unsigned long)pmsg->dwAddData1;
		msg.dwAddData2 = (unsigned long)pmsg->dwAddData2;
		msg.dwReserved = (unsigned long)pmsg->dwReserved;
		msg.p_arg		= pmsg->p_arg;
		msg.app_id		= APP_FM_ID;

		if(sync == 1)
			lvgl_msg_srv_send_sync_message(&msg);
		else
			lvgl_msg_srv_send_message(&msg);
	}
	
	return ret;
}

__s32 ui_fm_send_srv_msg(__s32 id, __u64 data1, __u64 data2, __u64 dwReserved, void *p_arg, __u32 flag, __u32 sync)
{
	__gui_msg_t msg;
	
	memset(&msg, 0x00, sizeof(__gui_msg_t));
	msg.id = id;
	msg.dwAddData1 = data1;
	msg.dwAddData2 = data2;
	msg.dwReserved = dwReserved;
	msg.p_arg = p_arg;
	msg.flag = flag;
	
	return ui_fm_refresh_msg(&msg, sync);
}

__s32 ui_fm_send_opn_msg(__s32 id, __u64 data1, __u64 data2, __u64 dwReserved, void *p_arg, __u32 flag, __u32 sync)
{
	__gui_msg_t msg;
	
	memset(&msg, 0x00, sizeof(__gui_msg_t));
	msg.id = id;
	msg.dwAddData1 = data1;
	msg.dwAddData2 = data2;
	msg.dwReserved = dwReserved;
	msg.p_arg = p_arg;
	msg.flag = flag;
	
	return ui_fm_opn_msg(&msg, sync);
}

__s32 fm_cmd2parent(H_WIN hwin, __s32 id, __s32 data1, __s32 data2)
{
	__s32 ret = EPDK_OK;
	
	ret = ui_fm_send_srv_msg(GUI_MSG_FM_REFRESH_MSG,id,data1,data2,__fm_cmd2parent,0,0);
	
	return ret;
}

__s32 fm_view_update_low_voltage(ui_fm_para_t * fm_para, __u8 update)
{
	__s32 ret = EPDK_OK;
	
	ret = ui_fm_send_srv_msg(GUI_MSG_FM_REFRESH_PART,update,0,0,__fm_view_update_low_voltage,0,0);

	return ret;
}

__s32 fm_view_update_charge(ui_fm_para_t * fm_para, __u8 update)
{
	__s32 ret = EPDK_OK;
	
	ret = ui_fm_send_srv_msg(GUI_MSG_FM_REFRESH_PART,update,0,0,__fm_view_update_charge,0,0);

	return ret;
}

__s32 fm_view_update_eq(ui_fm_para_t * fm_para, __u8 update)
{
	__s32 ret = EPDK_OK;
	
	ret = ui_fm_send_srv_msg(GUI_MSG_FM_REFRESH_PART,update,0,0,__fm_view_update_eq,0,0);

	return ret;
}

__s32 fm_view_update_preset(ui_fm_para_t * fm_para, __u8 update)
{
	__s32 ret = EPDK_OK;
	
	ret = ui_fm_send_srv_msg(GUI_MSG_FM_REFRESH_PART,update,0,0,__fm_view_update_preset,0,0);

	return ret;
}

__s32 fm_view_update_loc(ui_fm_para_t * fm_para, __bool update)
{
	__s32 ret = EPDK_OK;
	
	ret = ui_fm_send_srv_msg(GUI_MSG_FM_REFRESH_PART,update,0,0,__fm_view_update_loc,0,0);

	return ret;
}

__s32 fm_view_update_ST(ui_fm_para_t * fm_para, __bool update)
{
	__s32 ret = EPDK_OK;
	
	ret = ui_fm_send_srv_msg(GUI_MSG_FM_REFRESH_PART,update,0,0,__fm_view_update_ST,0,0);

	return ret;
}

__s32 fm_view_update_af(ui_fm_para_t * fm_para, __bool update)
{
	__s32 ret = EPDK_OK;
	
	ret = ui_fm_send_srv_msg(GUI_MSG_FM_REFRESH_PART,update,0,0,__fm_view_update_af,0,0);

	return ret;
}

__s32 fm_view_update_ta(ui_fm_para_t * fm_para, __bool update)
{
	__s32 ret = EPDK_OK;
	
	ret = ui_fm_send_srv_msg(GUI_MSG_FM_REFRESH_PART,update,0,0,__fm_view_update_ta,0,0);

	return ret;
}

__s32 fm_view_show_rds_pty(ui_fm_para_t * fm_para,__bool show_status)
{
	__s32 ret = EPDK_OK;
	
	ret = ui_fm_send_srv_msg(GUI_MSG_FM_REFRESH_PART,show_status,0,0,__fm_view_show_rds_pty,0,0);

	return ret;
}

__s32 fm_view_show_rds_ps(ui_fm_para_t * fm_para,__bool show_status)
{
	__s32 ret = EPDK_OK;
	
	ret = ui_fm_send_srv_msg(GUI_MSG_FM_REFRESH_PART,show_status,0,0,__fm_view_show_rds_ps,0,0);

	return ret;
}

__s32 fm_view_update_fmam_band(ui_fm_para_t * fm_para)
{
	__s32 ret = EPDK_OK;
	
	ret = ui_fm_send_srv_msg(GUI_MSG_FM_REFRESH_PART,0,0,0,__fm_view_update_fmam_band,0,0);

	return ret;
}

__s32 fm_view_update_frq(ui_fm_para_t * fm_para)
{
	__s32 ret = EPDK_OK;
	
	ret = ui_fm_send_srv_msg(GUI_MSG_FM_REFRESH_PART,0,0,0,__fm_view_update_frq,0,0);

	return ret;
}

__s32 fm_view_update_Mhz_Khz(ui_fm_para_t * fm_para)
{
	__s32 ret = EPDK_OK;
	
	ret = ui_fm_send_srv_msg(GUI_MSG_FM_REFRESH_PART,0,0,0,__fm_view_update_Mhz_Khz,0,0);

	return ret;
}

__s32 fm_view_update_seek_string(ui_fm_para_t * fm_para, __u8 show_status)
{
	__s32 ret = EPDK_OK;
	
	ret = ui_fm_send_srv_msg(GUI_MSG_FM_REFRESH_PART,show_status,0,0,__fm_view_update_seek_string,0,0);

	return ret;
}

__s32 fm_view_update_manual_string(ui_fm_para_t * fm_para, __u8 show_status)
{
	__s32 ret = EPDK_OK;
	
	ret = ui_fm_send_srv_msg(GUI_MSG_FM_REFRESH_PART,show_status,0,0,__fm_view_update_manual_string,0,0);

	return ret;
}

__s32 fm_view_clear_rds_status(ui_fm_para_t * fm_para) //mllee 130325
{
	__s32 ret = EPDK_OK;
	
	ret = ui_fm_send_srv_msg(GUI_MSG_FM_REFRESH_PART,0,0,0,__fm_view_clear_rds_status,0,0);

	return ret;
}

__s32 fm_view_clear_pty_status(ui_fm_para_t * fm_para) //mllee 130325
{
	__s32 ret = EPDK_OK;
	
	ret = ui_fm_send_srv_msg(GUI_MSG_FM_REFRESH_PART,0,0,0,__fm_view_clear_pty_status,0,0);

	return ret;
}


#ifdef SWITCH_BAND_TIPS
__s32 create_fm_tips_dialog(ui_fm_para_t * fm_para,__s32 title_id,__s32 content_id,__u32 time_out)
{
	__s32 ret = EPDK_OK;
	
	ret = ui_fm_send_srv_msg(GUI_MSG_FM_REFRESH_DIALOG,title_id,content_id, time_out,__create_fm_tips_dialog,0,0);

	return ret;
}

__s32 fm_tips_dialog_cancel(ui_fm_para_t * fm_para)
{
	__s32 ret = EPDK_OK;
	
	ret = ui_fm_send_srv_msg(GUI_MSG_FM_REFRESH_DIALOG,0,0,0,__fm_tips_dialog_cancel,0,0);

	return ret;
}
#endif

__s32 create_fm_app_tips_dialog(ui_fm_para_t *fm_para,__s32 title_id,__s32 content_id,__u32 time_out)
{
	__s32 ret = EPDK_OK;
	
	ret = ui_fm_send_srv_msg(GUI_MSG_FM_REFRESH_DIALOG,title_id,content_id,time_out,__create_fm_app_tips_dialog,0,0);

	return ret;
}


__s32 fm_app_tips_dialog_cancel(ui_fm_para_t *fm_para)
{
	__s32 ret = EPDK_OK;
	
	ret = ui_fm_send_srv_msg(GUI_MSG_FM_REFRESH_DIALOG,0,0,0,__fm_app_tips_dialog_cancel,0,0);

	return ret;
}

__s32 ui_fm_tips_tmr_create(ui_fm_para_t * fm_para)
{
	__s32 ret = EPDK_OK;
	
	ret = ui_fm_send_srv_msg(GUI_MSG_FM_REFRESH_DIALOG,0,0,0,__ui_fm_tips_tmr_create,0,0);

	return ret;
}

__s32 ui_fm_remove_hidden(ui_fm_para_t * fm_para)
{
	__s32 ret = EPDK_OK;
	
	ret = ui_fm_send_srv_msg(GUI_MSG_FM_REFRESH_HIDDEN,0,0,0,__ui_fm_remove_hidden,0,0);

	return ret;
}

__s32 ui_fm_add_hidden(ui_fm_para_t * fm_para)
{
	__s32 ret = EPDK_OK;
	
	ret = ui_fm_send_srv_msg(GUI_MSG_FM_REFRESH_HIDDEN,1,0,0,__ui_fm_add_hidden,0,0);

	return ret;
}

__s32 ui_fm_layer_on(ui_fm_para_t * fm_para, __u8 store)
{
	__s32 ret = EPDK_OK;
	
	ret = ui_fm_send_opn_msg(GUI_MSG_FM_OPN_LAYER,0,store,0,__ui_fm_layer_on,0,0);

	return ret;
}

__s32 ui_fm_layer_sleep(ui_fm_para_t * fm_para, __u8 store)
{
	__s32 ret = EPDK_OK;
	
	ret = ui_fm_send_opn_msg(GUI_MSG_FM_OPN_LAYER,1,store,0,__ui_fm_layer_sleep,0,0);

	return ret;
}

__s32 ui_fm_func_auto_store(ui_fm_para_t * fm_para)
{
	__s32 ret = EPDK_OK;
	
	ret = ui_fm_send_opn_msg(GUI_MSG_FM_FUNC_AUTO_STORE,0,0,0,__ui_fm_func_auto_store,0,0);

	return ret;
}


__s32 ui_fm_func_pty_seek(ui_fm_para_t * fm_para)
{
	__s32 ret = EPDK_OK;
	
	ret = ui_fm_send_opn_msg(GUI_MSG_FM_FUNC_PTY_SEEK,0,0,0,__ui_fm_func_pty_seek,0,0);

	return ret;
}


__s32 ui_fm_stop_seek(ui_fm_para_t * fm_para)
{
	__s32 ret = EPDK_OK;
	
	ret = ui_fm_send_opn_msg(GUI_MSG_FM_STOP_SEEK,0,0,0,__ui_fm_stop_seek,0,0);

	return ret;
}


__s32 ui_fm_ctrl_stop_seek(ui_fm_para_t * fm_para)
{
	__s32 ret = EPDK_OK;
	
	ret = ui_fm_send_opn_msg(GUI_MSG_FM_CTRL_STOP_SEEK,0,0,0,__ui_fm_ctrl_stop_seek,0,0);

	return ret;
}

__s32 ui_fm_ctrl_prev(ui_fm_para_t * fm_para)
{
	__s32 ret = EPDK_OK;
	
	ret = ui_fm_send_opn_msg(GUI_MSG_FM_PRVE,0,0,0,__ui_fm_ctrl_prev,0,0);

	return ret;
}

__s32 ui_fm_ctrl_long_prev(ui_fm_para_t * fm_para)
{
	__s32 ret = EPDK_OK;
	
	ret = ui_fm_send_opn_msg(GUI_MSG_FM_LONG_PRVE,0,0,0,__ui_fm_ctrl_long_prev,0,0);

	return ret;
}

__s32 ui_fm_ctrl_next(ui_fm_para_t * fm_para)
{
	__s32 ret = EPDK_OK;
	
	ret = ui_fm_send_opn_msg(GUI_MSG_FM_NEXT,0,0,0,__ui_fm_ctrl_next,0,0);

	return ret;
}

__s32 ui_fm_ctrl_long_next(ui_fm_para_t * fm_para)
{
	__s32 ret = EPDK_OK;
	
	ret = ui_fm_send_opn_msg(GUI_MSG_FM_LONG_NEXT,0,0,0,__ui_fm_ctrl_long_next,0,0);

	return ret;
}

__s32 ui_fm_ctrl_fmband(ui_fm_para_t * fm_para)
{
	__s32 ret = EPDK_OK;
	
	ret = ui_fm_send_opn_msg(GUI_MSG_FM_FM_BAND,0,0,0,__ui_fm_ctrl_fmband,0,0);

	return ret;
}

__s32 ui_fm_ctrl_amband(ui_fm_para_t * fm_para)
{
	__s32 ret = EPDK_OK;
	
	ret = ui_fm_send_opn_msg(GUI_MSG_FM_AM_BAND,0,0,0,__ui_fm_ctrl_amband,0,0);

	return ret;
}

__s32 ui_fm_ctrl_preset(ui_fm_para_t * fm_para, __u32 preset_index)
{
	__s32 ret = EPDK_OK;
	
	ret = ui_fm_send_opn_msg(GUI_MSG_FM_PRESET,preset_index,0,0,__ui_fm_ctrl_preset,0,0);

	return ret;
}

__s32 ui_fm_ctrl_long_preset(ui_fm_para_t * fm_para, __u32 preset_index)
{
	__s32 ret = EPDK_OK;
	
	ret = ui_fm_send_opn_msg(GUI_MSG_FM_SAVE_PRESET,preset_index,0,0,__ui_fm_ctrl_long_preset,0,0);

	return ret;
}

__s32 ui_fm_set_mute(ui_fm_para_t * fm_para)
{
	__s32 ret = EPDK_OK;
	
	ret = ui_fm_send_opn_msg(GUI_MSG_FM_MUTE,0,0,0,__ui_fm_set_mute,0,0);

	return ret;
}


