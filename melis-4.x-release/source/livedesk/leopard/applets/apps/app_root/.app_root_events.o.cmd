cmd_livedesk/leopard/applets/apps/app_root/app_root_events.o := /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/source/../toolchain/riscv64-elf-x86_64-20201104//bin/riscv64-unknown-elf-gcc -Wp,-MD,livedesk/leopard/applets/apps/app_root/.app_root_events.o.d  -isystem /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/bin/../lib/gcc/riscv64-unknown-elf/8.4.0/include  -Iinclude -Iinclude/melis -Iinclude/melis/common -Iinclude/melis/eboot -Iinclude/melis/eboard -Iinclude/melis/ekernel -Iinclude/melis/ekernel/arch -Iinclude/melis/ekernel/csp -Iinclude/melis/ekernel/drivers -Iinclude/melis/ekernel/filesystem -Iinclude/melis/ekernel/pthread -Iinclude/melis/elibrary -Iinclude/melis/elibrary/libc -Iinclude/melis/elibrary/libc/mediainfo -Iinclude/melis/elibrary/libc/misc -Iinclude/melis/elibrary/libc/misc/pub0 -Iinclude/melis/emodules -Iinclude/melis/ipc -Iinclude/melis/misc -Iinclude/melis/of -Iinclude/melis/sys -Iinclude/melis/video -Iekernel/components/thirdparty/finsh_cli -Iekernel/core/rt-thread/include -Iekernel/components/thirdparty/dfs/include -Iekernel/drivers/rtos-hal/hal/source -Iekernel/drivers/include/osal -Iinclude/generated -Iinclude/generated/uapi/melis -I./include/melis/elibrary -include ./include/melis/kconfig.h -Iinclude/melis/ekernel/arch/riscv -Iinclude/melis/arch/riscv -DARCH_CPU_64BIT -mcmodel=medany -mabi=lp64d -march=rv64gcxthead -falign-functions=4 -mcmodel=medany -mabi=lp64d -march=rv64gcxthead -Wall -Wundef -Wstrict-prototypes -Wno-trigraphs -fno-strict-aliasing -fno-common -Werror-implicit-function-declaration -Wno-format-security -fno-builtin-printf -D_SYS__PTHREADTYPES_H_ -fno-delete-null-pointer-checks -ffunction-sections -fdata-sections -fzero-initialized-in-bss -Wmaybe-uninitialized -Os -g -gdwarf-2 -gstrict-dwarf -Wstrict-prototypes -Wundef -Wno-unused-function -Wno-unused-variable -fshort-enums -Wsizeof-pointer-memaccess --param=allow-store-data-races=0 -Wframe-larger-than=8192 -fno-stack-protector -Wno-unused-but-set-variable -fomit-frame-pointer -fno-var-tracking-assignments -g -Wdeclaration-after-statement -Wno-pointer-sign -fno-strict-overflow -fconserve-stack -Werror=implicit-int -Werror=strict-prototypes -DCC_HAVE_ASM_GOTO -Wno-declaration-after-statement -mcmodel=medany -mabi=lp64d -march=rv64gcxthead -Wno-unused-label -Wextra -Wno-unused-parameter -Wno-old-style-declaration -Wno-sign-compare -I./ekernel/drivers/include/hal -I./include/melis/kernel/drivers -I./include/melis -I./include/melis/kernel -I./include/melis/misc -I./include/melis/libs/libc -I./include/melis/module -I./include/melis/elibrary/libc -I./elibrary/lib_lvgl -I./elibrary/freetype/include -I./livedesk/leopard/include -I./livedesk/leopard/include/elibs -I./livedesk/leopard/include/elibs/lib_ex -I./livedesk/leopard/include/elibs/lib_ex/scanfile -I./livedesk/leopard/include/mod_desktop/msg_srv -I./livedesk/leopard/include/mod_desktop -I./livedesk/leopard/applets/porting -I./ekernel/core/rt-thread/include -I./ekernel/drivers/rtos-hal/hal/source/g2d_rcq -I./ekernel/drivers/include/hal -I./ekernel/drivers/include/drv -I./elibrary/sft/sfte -I./livedesk/leopard/applets/porting/font -I./livedesk/leopard/applets/common -I./elibrary/lib_lvgl/sunxi -I./livedesk/leopard/applets/porting/font -I./livedesk/leopard/applets/porting/image -I./livedesk/leopard/applets/apps/app_root -I./livedesk/leopard/applets/apps/lib/top_state_bar -I./livedesk/leopard/applets/apps/lib/app_dialog -I./livedesk/leopard/applets/apps/lib/background -I./livedesk/leopard/applets/apps/lib/animation -I./livedesk/leopard/applets/apps/lib/fb_lib -I./livedesk/leopard/applets/apps/lib/menu -I./livedesk/leopard/applets/apps/lib/fifo_db -I./livedesk/leopard/applets/apps/lib/common -I./livedesk/leopard/applets/apps/lib/source -I./livedesk/leopard/applets/apps/home -I./livedesk/leopard/applets/apps/music -I./livedesk/leopard/applets/apps/movie -I./livedesk/leopard/applets/apps/movie/common -I./livedesk/leopard/applets/apps/bt -I./livedesk/leopard/applets/apps/ipod -I./livedesk/leopard/applets/apps/mirror_adb -I./livedesk/leopard/applets/apps/mirror_iphone -I./livedesk/leopard/applets/apps/wirless_mirror -I./livedesk/leopard/applets/apps/fm -I./livedesk/leopard/applets/apps/dab -I./livedesk/leopard/applets/apps/party_mode -I./livedesk/leopard/applets/apps/sxm -I./livedesk/leopard/applets/apps/aux_in -I./livedesk/leopard/applets/apps/av_in -I./livedesk/leopard/applets/apps/dvr -I./livedesk/leopard/applets/apps/audio_setting -I./livedesk/leopard/applets/apps/smenu -I./livedesk/leopard/applets/apps/smenu/smenu_bluetooth -I./livedesk/leopard/applets/apps/smenu/smenu_factory -I./livedesk/leopard/applets/apps/smenu/smenu_info -I./livedesk/leopard/applets/apps/smenu/smenu_main -I./livedesk/leopard/applets/apps/smenu/smenu_mirror -I./livedesk/leopard/applets/apps/smenu/smenu_parking -I./livedesk/leopard/applets/apps/smenu/smenu_security -I./livedesk/leopard/applets/apps/smenu/smenu_splash -I./livedesk/leopard/applets/apps/smenu/smenu_swc -I./livedesk/leopard/applets/apps/smenu/smenu_system -I./livedesk/leopard/applets/apps/smenu/smenu_time -I./livedesk/leopard/applets/apps/smenu/smenu_update -I./livedesk/leopard/applets/apps/smenu/smenu_wallpaper -I./livedesk/leopard/applets/apps/smenu/smenu_wifi -I./livedesk/leopard/applets/apps/smenu/smenu_wireless -I./livedesk/leopard/applets/apps/slideshow -I./livedesk/leopard/applets/apps/backcar -I./livedesk/leopard/applets/apps/clock -I./livedesk/leopard/applets/apps/volume -I./livedesk/leopard/applets/apps/fw_update -I./livedesk/leopard/applets/apps/explorer -DEPDK_DEBUG_LEVEL=EPDK_DEBUG_LEVEL_LOG_ALL -fshort-enums -std=c99 -DEPDK_DEBUG_LEVEL=EPDK_DEBUG_LEVEL_LOG_ALL -DUSED_BY_INIT -I/home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/source/livedesk/leopard/elibs/libapps/module_adapter -I/home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/source/livedesk/leopard/elibs/libapps/scene/listbar -I/home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/source/livedesk/leopard/elibs/lib_ex/anole -I/home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/source/livedesk/leopard/elibs/lib_ex/monkey -I/home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/source/livedesk/leopard/elibs/lib_ex/monkey/bookmark -I/home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/source/livedesk/leopard/elibs/lib_ex/monkey/formats -I/home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/source/livedesk/leopard/elibs/lib_ex/monkey/formats/encode_parser -I/home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/source/livedesk/leopard/elibs/lib_ex/monkey/formats/encode_parser/gbk -I/home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/source/livedesk/leopard/elibs/lib_ex/monkey/formats/encode_parser/utf16_big -I/home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/source/livedesk/leopard/elibs/lib_ex/monkey/formats/encode_parser/utf16_little -I/home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/source/livedesk/leopard/elibs/lib_ex/monkey/formats/encode_parser/utf8 -I/home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/source/livedesk/leopard/elibs/lib_ex/monkey/mnpl -I/home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/source/livedesk/leopard/elibs/lib_ex/monkey/regedit -I/home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/source/livedesk/leopard/elibs/lib_ex/robin -I/home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/source/livedesk/leopard/elibs/lib_ex/robin/foundation -I/home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/source/livedesk/leopard/elibs/lib_ex/robin/lib -I/home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/source/livedesk/leopard/include -I/home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/source/livedesk/leopard/include/applets -I/home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/source/livedesk/leopard/include/elibs -I/home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/source/livedesk/leopard/include/elibs/lib_ex -I/home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/source/livedesk/leopard/include/elibs/lib_ex/robin/ab -I/home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/source/livedesk/leopard/include/elibs/lib_ex/robin/channel -I/home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/source/livedesk/leopard/include/elibs/lib_ex/robin/disp_output -I/home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/source/livedesk/leopard/include/elibs/lib_ex/robin/eq -I/home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/source/livedesk/leopard/include/elibs/lib_ex/robin/ff_rr_speed -I/home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/source/livedesk/leopard/include/elibs/lib_ex/robin/fsm_ctrl -I/home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/source/livedesk/leopard/include/elibs/lib_ex/robin/lyric -I/home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/source/livedesk/leopard/include/elibs/lib_ex/robin/media_info -I/home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/source/livedesk/leopard/include/elibs/lib_ex/robin/npl -I/home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/source/livedesk/leopard/include/elibs/lib_ex/robin/palette -I/home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/source/livedesk/leopard/include/elibs/lib_ex/robin/play_mode -I/home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/source/livedesk/leopard/include/elibs/lib_ex/robin/play_speed -I/home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/source/livedesk/leopard/include/elibs/lib_ex/robin/spectrum -I/home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/source/livedesk/leopard/include/elibs/lib_ex/robin/star -I/home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/source/livedesk/leopard/include/elibs/lib_ex/robin/subtitle -I/home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/source/livedesk/leopard/include/elibs/lib_ex/robin/track -I/home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/source/livedesk/leopard/include/elibs/lib_ex/robin/video_layer -I/home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/source/livedesk/leopard/include/elibs/lib_ex/robin/video_win -I/home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/source/livedesk/leopard/include/elibs/lib_ex/robin/vision_effect -I/home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/source/livedesk/leopard/include/elibs/lib_ex/robin/volume -I/home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/source/livedesk/leopard/include/elibs/lib_ex/robin/zoom -I/home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/source/livedesk/leopard/include/elibs/lib_ex/scanfile -I/home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/source/livedesk/leopard/include/init -I/home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/source/livedesk/leopard/include/init/alarm -I/home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/source/livedesk/leopard/include/init/background -I/home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/source/livedesk/leopard/include/init/bookengine -I/home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/source/livedesk/leopard/include/init/display -I/home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/source/livedesk/leopard/include/init/headbar -I/home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/source/livedesk/leopard/include/init/mini_music -I/home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/source/livedesk/leopard/include/init/screen_lock -I/home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/source/livedesk/leopard/include/init/tp_adjust -I/home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/source/livedesk/leopard/include/init/ui/tp_adjust_scene -I/home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/source/livedesk/leopard/include/libapps -I/home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/source/livedesk/leopard/include/libapps/module_adapter -I/home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/source/livedesk/leopard/include/libapps/scene -I/home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/source/livedesk/leopard/include/libapps/srf -I/home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/source/livedesk/leopard/include/mod_desktop -I/home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/source/livedesk/leopard/init/ -I/home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/source/livedesk/leopard/init/background/fb_lib -I/home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/source/livedesk/leopard/init/headbar/ -I/home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/source/livedesk/leopard/init/headbar/prog_bar -I/home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/source/livedesk/leopard/init/mini_music -I/home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/source/livedesk/leopard/init/screen_lock -I/home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/source/livedesk/leopard/init/ui -I/home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/source/livedesk/leopard/init/ui/assistant_scene -I/home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/source/livedesk/leopard/init/ui/dialog_scene -I/home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/source/livedesk/leopard/init/ui/tp_adjust_scene -I/home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/source/livedesk/leopard/mod_desktop -I/home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/source/livedesk/leopard/mod_desktop/engine -I/home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/source/livedesk/leopard/mod_desktop/engine/radio -I/home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/source/livedesk/leopard/mod_desktop/engine/radio/receive -I/home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/source/livedesk/leopard/mod_desktop/engine/radio/send -I/home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/source/livedesk/leopard/mod_desktop/engine/walkman -I/home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/source/livedesk/leopard/mod_desktop/engine/walkman/foundation -I/home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/source/livedesk/leopard/mod_desktop/engine/walkman/lib -I/home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/source/livedesk/leopard/mod_desktop/framework -I/home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/source/livedesk/leopard/mod_desktop/functions -I/home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/source/livedesk/leopard/mod_desktop/include -I/home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/source/livedesk/leopard/mod_desktop/util -I/home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/source/livedesk/leopard/include/mod_desktop/msg_srv  -DMODULE  -D"KBUILD_STR(s)=\#s" -D"KBUILD_BASENAME=KBUILD_STR(app_root_events)"  -D"KBUILD_MODNAME=KBUILD_STR(leopard)" -c -o livedesk/leopard/applets/apps/app_root/.tmp_app_root_events.o livedesk/leopard/applets/apps/app_root/app_root_events.c

source_livedesk/leopard/applets/apps/app_root/app_root_events.o := livedesk/leopard/applets/apps/app_root/app_root_events.c

deps_livedesk/leopard/applets/apps/app_root/app_root_events.o := \
  include/melis/kconfig.h \
    $(wildcard include/config/h.h) \
  livedesk/leopard/applets/apps/app_root/app_root_helpers.h \
    $(wildcard include/config/drivers/usb/gadget/qvh.h) \
    $(wildcard include/config/drivers/usb/gadget/ncm.h) \
  elibrary/lib_lvgl/lv_conf.h \
  include/melis/elibrary/libc/elibs_stdio.h \
  include/melis/typedef.h \
  /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/lib/gcc/riscv64-unknown-elf/8.4.0/include/stddef.h \
  /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/lib/gcc/riscv64-unknown-elf/8.4.0/include/stdint.h \
  /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/riscv64-unknown-elf/include/stdint.h \
  /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/riscv64-unknown-elf/include/machine/_default_types.h \
  /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/riscv64-unknown-elf/include/sys/features.h \
  /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/riscv64-unknown-elf/include/_newlib_version.h \
  /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/riscv64-unknown-elf/include/sys/_intsup.h \
  /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/riscv64-unknown-elf/include/sys/_stdint.h \
  /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/lib/gcc/riscv64-unknown-elf/8.4.0/include/stdarg.h \
  /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/lib/gcc/riscv64-unknown-elf/8.4.0/include-fixed/limits.h \
  /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/lib/gcc/riscv64-unknown-elf/8.4.0/include-fixed/syslimits.h \
  /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/riscv64-unknown-elf/include/limits.h \
  /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/riscv64-unknown-elf/include/newlib.h \
  /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/riscv64-unknown-elf/include/sys/cdefs.h \
  include/melis/ekernel/kapi.h \
    $(wildcard include/config/melis/legacy/driver/man.h) \
    $(wildcard include/config/t.h) \
    $(wildcard include/config/kernel/use/sbi.h) \
  /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/riscv64-unknown-elf/include/errno.h \
  /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/riscv64-unknown-elf/include/sys/errno.h \
  /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/riscv64-unknown-elf/include/sys/reent.h \
  /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/riscv64-unknown-elf/include/_ansi.h \
  /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/riscv64-unknown-elf/include/sys/config.h \
    $(wildcard include/config/h//.h) \
  /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/riscv64-unknown-elf/include/machine/ieeefp.h \
  /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/riscv64-unknown-elf/include/sys/_types.h \
  /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/riscv64-unknown-elf/include/machine/_types.h \
  /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/riscv64-unknown-elf/include/sys/lock.h \
  include/melis/ekernel/kmsg.h \
    $(wildcard include/config/setting/update.h) \
  include/melis/eboot/boot.h \
  include/melis/ekernel/drivers/sys_charset.h \
  include/melis/ekernel/drivers/sys_clock.h \
  include/melis/ekernel/ktype.h \
  include/melis/ekernel/csp/csp_ccm_para.h \
    $(wildcard include/config/drivers/sunxi/clk.h) \
    $(wildcard include/config/drivers/sunxi/ccu.h) \
  include/melis/kconfig.h \
  include/melis/ekernel/drivers/sys_device.h \
    $(wildcard include/config/spinor/cmmb/data/offset.h) \
    $(wildcard include/config/spinor/cmmb/data/size.h) \
    $(wildcard include/config/spinor/apps/data/offset.h) \
    $(wildcard include/config/spinor/apps/data/size.h) \
    $(wildcard include/config/spinor/mcu/update/data/offset.h) \
    $(wildcard include/config/spinor/mcu/update/data/size.h) \
  include/melis/ekernel/drivers/sys_fsys.h \
    $(wildcard include/config/soc/sun20iw1.h) \
    $(wildcard include/config/soc/sun8iw20.h) \
    $(wildcard include/config/driver/spinor.h) \
  /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/lib/gcc/riscv64-unknown-elf/8.4.0/include/stdbool.h \
  include/melis/ekernel/drivers/sys_device.h \
  include/melis/ekernel/drivers/sys_hwsc.h \
  include/melis/ekernel/drivers/sys_input.h \
  include/melis/ekernel/drivers/sys_mems.h \
  include/melis/ekernel/csp/csp_dram_para.h \
    $(wildcard include/config/soc/sun3iw2.h) \
    $(wildcard include/config/soc/sun8i.h) \
    $(wildcard include/config/soc/sun20iw3.h) \
    $(wildcard include/config/soc/sun3iw1.h) \
  include/melis/ekernel/drivers/sys_pins.h \
  include/melis/script.h \
  include/melis/ekernel/drivers/sys_powerman.h \
  include/melis/ekernel/drivers/sys_svc.h \
  include/melis/ekernel/drivers/sys_time.h \
  include/melis/ekernel/csp/csp_dma_para.h \
    $(wildcard include/config/soc/sun8iw19.h) \
  include/melis/ekernel/csp/csp_int_para.h \
  elibrary/lib_lvgl/lvgl.h \
  elibrary/lib_lvgl/lv_version.h \
  elibrary/lib_lvgl/src/lv_init.h \
  elibrary/lib_lvgl/src/lv_conf_internal.h \
    $(wildcard include/config/ignore.h) \
    $(wildcard include/config/lv/conf/skip.h) \
    $(wildcard include/config/lv/color/depth.h) \
    $(wildcard include/config/present.h) \
    $(wildcard include/config/lv/use/stdlib/malloc.h) \
    $(wildcard include/config/lv/use/stdlib/string.h) \
    $(wildcard include/config/lv/use/stdlib/sprintf.h) \
    $(wildcard include/config/lv/stdint/include.h) \
    $(wildcard include/config/lv/stddef/include.h) \
    $(wildcard include/config/lv/stdbool/include.h) \
    $(wildcard include/config/lv/inttypes/include.h) \
    $(wildcard include/config/lv/limits/include.h) \
    $(wildcard include/config/lv/stdarg/include.h) \
    $(wildcard include/config/lv/mem/size.h) \
    $(wildcard include/config/lv/mem/pool/expand/size.h) \
    $(wildcard include/config/lv/mem/adr.h) \
    $(wildcard include/config/lv/mem/pool/include.h) \
    $(wildcard include/config/lv/mem/pool/alloc.h) \
    $(wildcard include/config/lv/def/refr/period.h) \
    $(wildcard include/config/lv/dpi/def.h) \
    $(wildcard include/config/lv/use/os.h) \
    $(wildcard include/config/lv/os/custom/include.h) \
    $(wildcard include/config/lv/use/freertos/task/notify.h) \
    $(wildcard include/config/lv/draw/buf/stride/align.h) \
    $(wildcard include/config/lv/draw/buf/align.h) \
    $(wildcard include/config/lv/draw/transform/use/matrix.h) \
    $(wildcard include/config/lv/draw/layer/simple/buf/size.h) \
    $(wildcard include/config/lv/draw/thread/stack/size.h) \
    $(wildcard include/config/lv/use/draw/sw.h) \
    $(wildcard include/config/lv/draw/sw/support/rgb565.h) \
    $(wildcard include/config/lv/draw/sw/support/rgb565a8.h) \
    $(wildcard include/config/lv/draw/sw/support/rgb888.h) \
    $(wildcard include/config/lv/draw/sw/support/xrgb8888.h) \
    $(wildcard include/config/lv/draw/sw/support/argb8888.h) \
    $(wildcard include/config/lv/draw/sw/support/l8.h) \
    $(wildcard include/config/lv/draw/sw/support/al88.h) \
    $(wildcard include/config/lv/draw/sw/support/a8.h) \
    $(wildcard include/config/lv/draw/sw/support/i1.h) \
    $(wildcard include/config/lv/draw/sw/draw/unit/cnt.h) \
    $(wildcard include/config/lv/use/draw/arm2d/sync.h) \
    $(wildcard include/config/lv/use/native/helium/asm.h) \
    $(wildcard include/config/lv/draw/sw/complex.h) \
    $(wildcard include/config/lv/draw/sw/shadow/cache/size.h) \
    $(wildcard include/config/lv/draw/sw/circle/cache/size.h) \
    $(wildcard include/config/lv/use/draw/sw/asm.h) \
    $(wildcard include/config/lv/draw/sw/asm/custom/include.h) \
    $(wildcard include/config/lv/use/draw/sw/complex/gradients.h) \
    $(wildcard include/config/lv/use/draw/vglite.h) \
    $(wildcard include/config/lv/use/vglite/blit/split.h) \
    $(wildcard include/config/lv/use/vglite/draw/thread.h) \
    $(wildcard include/config/lv/use/vglite/draw/async.h) \
    $(wildcard include/config/lv/use/vglite/assert.h) \
    $(wildcard include/config/lv/use/pxp.h) \
    $(wildcard include/config/lv/use/draw/pxp.h) \
    $(wildcard include/config/lv/use/rotate/pxp.h) \
    $(wildcard include/config/lv/use/pxp/draw/thread.h) \
    $(wildcard include/config/lv/use/pxp/assert.h) \
    $(wildcard include/config/lv/use/draw/dave2d.h) \
    $(wildcard include/config/lv/use/draw/sdl.h) \
    $(wildcard include/config/lv/use/draw/vg/lite.h) \
    $(wildcard include/config/lv/vg/lite/use/gpu/init.h) \
    $(wildcard include/config/lv/vg/lite/use/assert.h) \
    $(wildcard include/config/lv/vg/lite/flush/max/count.h) \
    $(wildcard include/config/lv/vg/lite/use/box/shadow.h) \
    $(wildcard include/config/lv/vg/lite/grad/cache/cnt.h) \
    $(wildcard include/config/lv/vg/lite/stroke/cache/cnt.h) \
    $(wildcard include/config/lv/use/log.h) \
    $(wildcard include/config/lv/log/level.h) \
    $(wildcard include/config/lv/log/printf.h) \
    $(wildcard include/config/lv/log/use/timestamp.h) \
    $(wildcard include/config/lv/log/use/file/line.h) \
    $(wildcard include/config/lv/log/trace/mem.h) \
    $(wildcard include/config/lv/log/trace/timer.h) \
    $(wildcard include/config/lv/log/trace/indev.h) \
    $(wildcard include/config/lv/log/trace/disp/refr.h) \
    $(wildcard include/config/lv/log/trace/event.h) \
    $(wildcard include/config/lv/log/trace/obj/create.h) \
    $(wildcard include/config/lv/log/trace/layout.h) \
    $(wildcard include/config/lv/log/trace/anim.h) \
    $(wildcard include/config/lv/log/trace/cache.h) \
    $(wildcard include/config/lv/use/assert/null.h) \
    $(wildcard include/config/lv/use/assert/malloc.h) \
    $(wildcard include/config/lv/use/assert/style.h) \
    $(wildcard include/config/lv/use/assert/mem/integrity.h) \
    $(wildcard include/config/lv/use/assert/obj.h) \
    $(wildcard include/config/lv/assert/handler/include.h) \
    $(wildcard include/config/lv/assert/handler.h) \
    $(wildcard include/config/lv/use/refr/debug.h) \
    $(wildcard include/config/lv/use/layer/debug.h) \
    $(wildcard include/config/lv/use/parallel/draw/debug.h) \
    $(wildcard include/config/lv/enable/global/custom.h) \
    $(wildcard include/config/lv/global/custom/include.h) \
    $(wildcard include/config/lv/cache/def/size.h) \
    $(wildcard include/config/lv/image/header/cache/def/cnt.h) \
    $(wildcard include/config/lv/gradient/max/stops.h) \
    $(wildcard include/config/lv/color/mix/round/ofs.h) \
    $(wildcard include/config/lv/obj/style/cache.h) \
    $(wildcard include/config/lv/use/obj/id.h) \
    $(wildcard include/config/lv/obj/id/auto/assign.h) \
    $(wildcard include/config/lv/use/obj/id/builtin.h) \
    $(wildcard include/config/lv/use/obj/property.h) \
    $(wildcard include/config/lv/use/obj/property/name.h) \
    $(wildcard include/config/lv/use/vg/lite/thorvg.h) \
    $(wildcard include/config/lv/vg/lite/thorvg/lvgl/blend/support.h) \
    $(wildcard include/config/lv/vg/lite/thorvg/yuv/support.h) \
    $(wildcard include/config/lv/vg/lite/thorvg/linear/gradient/ext/support.h) \
    $(wildcard include/config/lv/vg/lite/thorvg/16pixels/align.h) \
    $(wildcard include/config/lv/vg/lite/thorvg/buf/addr/align.h) \
    $(wildcard include/config/lv/vg/lite/thorvg/thread/render.h) \
    $(wildcard include/config/lv/big/endian/system.h) \
    $(wildcard include/config/lv/attribute/tick/inc.h) \
    $(wildcard include/config/lv/attribute/timer/handler.h) \
    $(wildcard include/config/lv/attribute/flush/ready.h) \
    $(wildcard include/config/lv/attribute/mem/align/size.h) \
    $(wildcard include/config/lv/attribute/mem/align.h) \
    $(wildcard include/config/lv/attribute/large/const.h) \
    $(wildcard include/config/lv/attribute/large/ram/array.h) \
    $(wildcard include/config/lv/attribute/fast/mem.h) \
    $(wildcard include/config/lv/export/const/int.h) \
    $(wildcard include/config/lv/attribute/extern/data.h) \
    $(wildcard include/config/lv/use/float.h) \
    $(wildcard include/config/lv/use/matrix.h) \
    $(wildcard include/config/lv/use/private/api.h) \
    $(wildcard include/config/lv/font/montserrat/8.h) \
    $(wildcard include/config/lv/font/montserrat/10.h) \
    $(wildcard include/config/lv/font/montserrat/12.h) \
    $(wildcard include/config/lv/font/montserrat/14.h) \
    $(wildcard include/config/lv/font/montserrat/16.h) \
    $(wildcard include/config/lv/font/montserrat/18.h) \
    $(wildcard include/config/lv/font/montserrat/20.h) \
    $(wildcard include/config/lv/font/montserrat/22.h) \
    $(wildcard include/config/lv/font/montserrat/24.h) \
    $(wildcard include/config/lv/font/montserrat/26.h) \
    $(wildcard include/config/lv/font/montserrat/28.h) \
    $(wildcard include/config/lv/font/montserrat/30.h) \
    $(wildcard include/config/lv/font/montserrat/32.h) \
    $(wildcard include/config/lv/font/montserrat/34.h) \
    $(wildcard include/config/lv/font/montserrat/36.h) \
    $(wildcard include/config/lv/font/montserrat/38.h) \
    $(wildcard include/config/lv/font/montserrat/40.h) \
    $(wildcard include/config/lv/font/montserrat/42.h) \
    $(wildcard include/config/lv/font/montserrat/44.h) \
    $(wildcard include/config/lv/font/montserrat/46.h) \
    $(wildcard include/config/lv/font/montserrat/48.h) \
    $(wildcard include/config/lv/font/montserrat/28/compressed.h) \
    $(wildcard include/config/lv/font/dejavu/16/persian/hebrew.h) \
    $(wildcard include/config/lv/font/simsun/14/cjk.h) \
    $(wildcard include/config/lv/font/simsun/16/cjk.h) \
    $(wildcard include/config/lv/font/unscii/8.h) \
    $(wildcard include/config/lv/font/unscii/16.h) \
    $(wildcard include/config/lv/font/custom/declare.h) \
    $(wildcard include/config/lv/font/default.h) \
    $(wildcard include/config/lv/font/fmt/txt/large.h) \
    $(wildcard include/config/lv/use/font/compressed.h) \
    $(wildcard include/config/lv/use/font/placeholder.h) \
    $(wildcard include/config/lv/txt/enc.h) \
    $(wildcard include/config/lv/txt/break/chars.h) \
    $(wildcard include/config/lv/txt/line/break/long/len.h) \
    $(wildcard include/config/lv/txt/line/break/long/pre/min/len.h) \
    $(wildcard include/config/lv/txt/line/break/long/post/min/len.h) \
    $(wildcard include/config/lv/use/bidi.h) \
    $(wildcard include/config/lv/bidi/base/dir/def.h) \
    $(wildcard include/config/lv/use/arabic/persian/chars.h) \
    $(wildcard include/config/lv/widgets/has/default/value.h) \
    $(wildcard include/config/lv/use/animimg.h) \
    $(wildcard include/config/lv/use/arc.h) \
    $(wildcard include/config/lv/use/bar.h) \
    $(wildcard include/config/lv/use/button.h) \
    $(wildcard include/config/lv/use/buttonmatrix.h) \
    $(wildcard include/config/lv/use/calendar.h) \
    $(wildcard include/config/lv/calendar/week/starts/monday.h) \
    $(wildcard include/config/lv/calendar/default/day/names.h) \
    $(wildcard include/config/lv/calendar/default/month/names.h) \
    $(wildcard include/config/lv/use/calendar/header/arrow.h) \
    $(wildcard include/config/lv/use/calendar/header/dropdown.h) \
    $(wildcard include/config/lv/use/calendar/chinese.h) \
    $(wildcard include/config/lv/use/canvas.h) \
    $(wildcard include/config/lv/use/chart.h) \
    $(wildcard include/config/lv/use/checkbox.h) \
    $(wildcard include/config/lv/use/dropdown.h) \
    $(wildcard include/config/lv/use/image.h) \
    $(wildcard include/config/lv/use/imagebutton.h) \
    $(wildcard include/config/lv/use/keyboard.h) \
    $(wildcard include/config/lv/use/label.h) \
    $(wildcard include/config/lv/label/text/selection.h) \
    $(wildcard include/config/lv/label/long/txt/hint.h) \
    $(wildcard include/config/lv/label/wait/char/count.h) \
    $(wildcard include/config/lv/use/led.h) \
    $(wildcard include/config/lv/use/line.h) \
    $(wildcard include/config/lv/use/list.h) \
    $(wildcard include/config/lv/use/lottie.h) \
    $(wildcard include/config/lv/use/menu.h) \
    $(wildcard include/config/lv/use/msgbox.h) \
    $(wildcard include/config/lv/use/roller.h) \
    $(wildcard include/config/lv/use/scale.h) \
    $(wildcard include/config/lv/use/slider.h) \
    $(wildcard include/config/lv/use/span.h) \
    $(wildcard include/config/lv/span/snippet/stack/size.h) \
    $(wildcard include/config/lv/use/spinbox.h) \
    $(wildcard include/config/lv/use/spinner.h) \
    $(wildcard include/config/lv/use/switch.h) \
    $(wildcard include/config/lv/use/textarea.h) \
    $(wildcard include/config/lv/textarea/def/pwd/show/time.h) \
    $(wildcard include/config/lv/use/table.h) \
    $(wildcard include/config/lv/use/tabview.h) \
    $(wildcard include/config/lv/use/tileview.h) \
    $(wildcard include/config/lv/use/win.h) \
    $(wildcard include/config/lv/use/theme/default.h) \
    $(wildcard include/config/lv/theme/default/dark.h) \
    $(wildcard include/config/lv/theme/default/grow.h) \
    $(wildcard include/config/lv/theme/default/transition/time.h) \
    $(wildcard include/config/lv/use/theme/simple.h) \
    $(wildcard include/config/lv/use/theme/mono.h) \
    $(wildcard include/config/lv/use/flex.h) \
    $(wildcard include/config/lv/use/grid.h) \
    $(wildcard include/config/lv/fs/default/drive/letter.h) \
    $(wildcard include/config/lv/use/fs/stdio.h) \
    $(wildcard include/config/lv/fs/stdio/letter.h) \
    $(wildcard include/config/lv/fs/stdio/path.h) \
    $(wildcard include/config/lv/fs/stdio/cache/size.h) \
    $(wildcard include/config/lv/use/fs/posix.h) \
    $(wildcard include/config/lv/fs/posix/letter.h) \
    $(wildcard include/config/lv/fs/posix/path.h) \
    $(wildcard include/config/lv/fs/posix/cache/size.h) \
    $(wildcard include/config/lv/use/fs/win32.h) \
    $(wildcard include/config/lv/fs/win32/letter.h) \
    $(wildcard include/config/lv/fs/win32/path.h) \
    $(wildcard include/config/lv/fs/win32/cache/size.h) \
    $(wildcard include/config/lv/use/fs/fatfs.h) \
    $(wildcard include/config/lv/fs/fatfs/letter.h) \
    $(wildcard include/config/lv/fs/fatfs/cache/size.h) \
    $(wildcard include/config/lv/use/fs/memfs.h) \
    $(wildcard include/config/lv/fs/memfs/letter.h) \
    $(wildcard include/config/lv/use/fs/littlefs.h) \
    $(wildcard include/config/lv/fs/littlefs/letter.h) \
    $(wildcard include/config/lv/use/fs/arduino/esp/littlefs.h) \
    $(wildcard include/config/lv/fs/arduino/esp/littlefs/letter.h) \
    $(wildcard include/config/lv/use/fs/arduino/sd.h) \
    $(wildcard include/config/lv/fs/arduino/sd/letter.h) \
    $(wildcard include/config/lv/use/lodepng.h) \
    $(wildcard include/config/lv/use/libpng.h) \
    $(wildcard include/config/lv/use/bmp.h) \
    $(wildcard include/config/lv/use/tjpgd.h) \
    $(wildcard include/config/lv/use/libjpeg/turbo.h) \
    $(wildcard include/config/lv/use/gif.h) \
    $(wildcard include/config/lv/gif/cache/decode/data.h) \
    $(wildcard include/config/lv/bin/decoder/ram/load.h) \
    $(wildcard include/config/lv/use/rle.h) \
    $(wildcard include/config/lv/use/qrcode.h) \
    $(wildcard include/config/lv/use/barcode.h) \
    $(wildcard include/config/lv/use/freetype.h) \
    $(wildcard include/config/lv/freetype/use/lvgl/port.h) \
    $(wildcard include/config/lv/freetype/cache/ft/glyph/cnt.h) \
    $(wildcard include/config/lv/use/tiny/ttf.h) \
    $(wildcard include/config/lv/tiny/ttf/file/support.h) \
    $(wildcard include/config/lv/tiny/ttf/cache/glyph/cnt.h) \
    $(wildcard include/config/lv/use/rlottie.h) \
    $(wildcard include/config/lv/use/vector/graphic.h) \
    $(wildcard include/config/lv/use/thorvg/internal.h) \
    $(wildcard include/config/lv/use/thorvg/external.h) \
    $(wildcard include/config/lv/use/lz4/internal.h) \
    $(wildcard include/config/lv/use/lz4/external.h) \
    $(wildcard include/config/lv/use/ffmpeg.h) \
    $(wildcard include/config/lv/ffmpeg/dump/format.h) \
    $(wildcard include/config/lv/use/snapshot.h) \
    $(wildcard include/config/lv/use/sysmon.h) \
    $(wildcard include/config/lv/sysmon/get/idle.h) \
    $(wildcard include/config/lv/use/perf/monitor.h) \
    $(wildcard include/config/lv/use/perf/monitor/pos.h) \
    $(wildcard include/config/lv/use/perf/monitor/log/mode.h) \
    $(wildcard include/config/lv/use/mem/monitor.h) \
    $(wildcard include/config/lv/use/mem/monitor/pos.h) \
    $(wildcard include/config/lv/use/profiler.h) \
    $(wildcard include/config/lv/use/profiler/builtin.h) \
    $(wildcard include/config/lv/profiler/builtin/buf/size.h) \
    $(wildcard include/config/lv/profiler/include.h) \
    $(wildcard include/config/lv/profiler/begin.h) \
    $(wildcard include/config/lv/profiler/end.h) \
    $(wildcard include/config/lv/profiler/begin/tag.h) \
    $(wildcard include/config/lv/profiler/end/tag.h) \
    $(wildcard include/config/lv/use/monkey.h) \
    $(wildcard include/config/lv/use/gridnav.h) \
    $(wildcard include/config/lv/use/fragment.h) \
    $(wildcard include/config/lv/use/imgfont.h) \
    $(wildcard include/config/lv/use/observer.h) \
    $(wildcard include/config/lv/use/ime/pinyin.h) \
    $(wildcard include/config/lv/ime/pinyin/use/default/dict.h) \
    $(wildcard include/config/lv/ime/pinyin/cand/text/num.h) \
    $(wildcard include/config/lv/ime/pinyin/use/k9/mode.h) \
    $(wildcard include/config/lv/ime/pinyin/k9/cand/text/num.h) \
    $(wildcard include/config/lv/use/file/explorer.h) \
    $(wildcard include/config/lv/file/explorer/path/max/len.h) \
    $(wildcard include/config/lv/file/explorer/quick/access.h) \
    $(wildcard include/config/lv/use/sdl.h) \
    $(wildcard include/config/lv/sdl/include/path.h) \
    $(wildcard include/config/lv/sdl/render/mode.h) \
    $(wildcard include/config/lv/sdl/buf/count.h) \
    $(wildcard include/config/lv/sdl/accelerated.h) \
    $(wildcard include/config/lv/sdl/fullscreen.h) \
    $(wildcard include/config/lv/sdl/direct/exit.h) \
    $(wildcard include/config/lv/sdl/mousewheel/mode.h) \
    $(wildcard include/config/lv/use/x11.h) \
    $(wildcard include/config/lv/x11/direct/exit.h) \
    $(wildcard include/config/lv/x11/double/buffer.h) \
    $(wildcard include/config/lv/x11/render/mode/partial.h) \
    $(wildcard include/config/lv/x11/render/mode/direct.h) \
    $(wildcard include/config/lv/x11/render/mode/full.h) \
    $(wildcard include/config/lv/use/wayland.h) \
    $(wildcard include/config/lv/wayland/window/decorations.h) \
    $(wildcard include/config/lv/wayland/wl/shell.h) \
    $(wildcard include/config/lv/use/linux/fbdev.h) \
    $(wildcard include/config/lv/linux/fbdev/bsd.h) \
    $(wildcard include/config/lv/linux/fbdev/render/mode.h) \
    $(wildcard include/config/lv/linux/fbdev/buffer/count.h) \
    $(wildcard include/config/lv/linux/fbdev/buffer/size.h) \
    $(wildcard include/config/lv/use/nuttx.h) \
    $(wildcard include/config/lv/use/nuttx/libuv.h) \
    $(wildcard include/config/lv/use/nuttx/custom/init.h) \
    $(wildcard include/config/lv/use/nuttx/lcd.h) \
    $(wildcard include/config/lv/nuttx/lcd/buffer/count.h) \
    $(wildcard include/config/lv/nuttx/lcd/buffer/size.h) \
    $(wildcard include/config/lv/use/nuttx/touchscreen.h) \
    $(wildcard include/config/lv/use/linux/drm.h) \
    $(wildcard include/config/lv/use/tft/espi.h) \
    $(wildcard include/config/lv/use/evdev.h) \
    $(wildcard include/config/lv/use/libinput.h) \
    $(wildcard include/config/lv/libinput/bsd.h) \
    $(wildcard include/config/lv/libinput/xkb.h) \
    $(wildcard include/config/lv/libinput/xkb/key/map.h) \
    $(wildcard include/config/lv/use/st7735.h) \
    $(wildcard include/config/lv/use/st7789.h) \
    $(wildcard include/config/lv/use/st7796.h) \
    $(wildcard include/config/lv/use/ili9341.h) \
    $(wildcard include/config/lv/use/generic/mipi.h) \
    $(wildcard include/config/lv/use/renesas/glcdc.h) \
    $(wildcard include/config/lv/use/windows.h) \
    $(wildcard include/config/lv/use/opengles.h) \
    $(wildcard include/config/lv/use/opengles/debug.h) \
    $(wildcard include/config/lv/use/qnx.h) \
    $(wildcard include/config/lv/qnx/buf/count.h) \
    $(wildcard include/config/lv/build/examples.h) \
    $(wildcard include/config/lv/use/demo/widgets.h) \
    $(wildcard include/config/lv/use/demo/keypad/and/encoder.h) \
    $(wildcard include/config/lv/use/demo/benchmark.h) \
    $(wildcard include/config/lv/use/demo/render.h) \
    $(wildcard include/config/lv/use/demo/stress.h) \
    $(wildcard include/config/lv/use/demo/music.h) \
    $(wildcard include/config/lv/demo/music/square.h) \
    $(wildcard include/config/lv/demo/music/landscape.h) \
    $(wildcard include/config/lv/demo/music/round.h) \
    $(wildcard include/config/lv/demo/music/large.h) \
    $(wildcard include/config/lv/demo/music/auto/play.h) \
    $(wildcard include/config/lv/use/demo/flex/layout.h) \
    $(wildcard include/config/lv/use/demo/multilang.h) \
    $(wildcard include/config/lv/use/demo/transform.h) \
    $(wildcard include/config/lv/use/demo/scroll.h) \
    $(wildcard include/config/lv/use/demo/vector/graphic.h) \
  elibrary/lib_lvgl/src/lv_conf_kconfig.h \
    $(wildcard include/config/external/include.h) \
    $(wildcard include/config/lvgl/version/major.h) \
    $(wildcard include/config/lvgl/version/minor.h) \
    $(wildcard include/config/lvgl/version/patch.h) \
    $(wildcard include/config/lv/use/builtin/malloc.h) \
    $(wildcard include/config/lv/use/clib/malloc.h) \
    $(wildcard include/config/lv/use/micropython/malloc.h) \
    $(wildcard include/config/lv/use/rtthread/malloc.h) \
    $(wildcard include/config/lv/use/custom/malloc.h) \
    $(wildcard include/config/lv/use/builtin/string.h) \
    $(wildcard include/config/lv/use/clib/string.h) \
    $(wildcard include/config/lv/use/micropython/string.h) \
    $(wildcard include/config/lv/use/rtthread/string.h) \
    $(wildcard include/config/lv/use/custom/string.h) \
    $(wildcard include/config/lv/use/builtin/sprintf.h) \
    $(wildcard include/config/lv/use/clib/sprintf.h) \
    $(wildcard include/config/lv/use/micropython/sprintf.h) \
    $(wildcard include/config/lv/use/rtthread/sprintf.h) \
    $(wildcard include/config/lv/use/custom/sprintf.h) \
    $(wildcard include/config/lv/mem/size/kilobytes.h) \
    $(wildcard include/config/lv/mem/pool/expand/size/kilobytes.h) \
    $(wildcard include/config/lv/perf/monitor/align/top/left.h) \
    $(wildcard include/config/lv/use/perf/monitor/align/top/mid.h) \
    $(wildcard include/config/lv/perf/monitor/align/top/right.h) \
    $(wildcard include/config/lv/perf/monitor/align/bottom/left.h) \
    $(wildcard include/config/lv/perf/monitor/align/bottom/mid.h) \
    $(wildcard include/config/lv/perf/monitor/align/bottom/right.h) \
    $(wildcard include/config/lv/perf/monitor/align/left/mid.h) \
    $(wildcard include/config/lv/perf/monitor/align/right/mid.h) \
    $(wildcard include/config/lv/perf/monitor/align/center.h) \
    $(wildcard include/config/lv/mem/monitor/align/top/left.h) \
    $(wildcard include/config/lv/use/mem/monitor/align/top/mid.h) \
    $(wildcard include/config/lv/mem/monitor/align/top/right.h) \
    $(wildcard include/config/lv/mem/monitor/align/bottom/left.h) \
    $(wildcard include/config/lv/mem/monitor/align/bottom/mid.h) \
    $(wildcard include/config/lv/mem/monitor/align/bottom/right.h) \
    $(wildcard include/config/lv/mem/monitor/align/left/mid.h) \
    $(wildcard include/config/lv/mem/monitor/align/right/mid.h) \
    $(wildcard include/config/lv/mem/monitor/align/center.h) \
    $(wildcard include/config/lv/font/default/.h) \
    $(wildcard include/config/lv/font/default/montserrat/8.h) \
    $(wildcard include/config/lv/font/default/montserrat/10.h) \
    $(wildcard include/config/lv/font/default/montserrat/12.h) \
    $(wildcard include/config/lv/font/default/montserrat/14.h) \
    $(wildcard include/config/lv/font/default/montserrat/16.h) \
    $(wildcard include/config/lv/font/default/montserrat/18.h) \
    $(wildcard include/config/lv/font/default/montserrat/20.h) \
    $(wildcard include/config/lv/font/default/montserrat/22.h) \
    $(wildcard include/config/lv/font/default/montserrat/24.h) \
    $(wildcard include/config/lv/font/default/montserrat/26.h) \
    $(wildcard include/config/lv/font/default/montserrat/28.h) \
    $(wildcard include/config/lv/font/default/montserrat/30.h) \
    $(wildcard include/config/lv/font/default/montserrat/32.h) \
    $(wildcard include/config/lv/font/default/montserrat/34.h) \
    $(wildcard include/config/lv/font/default/montserrat/36.h) \
    $(wildcard include/config/lv/font/default/montserrat/38.h) \
    $(wildcard include/config/lv/font/default/montserrat/40.h) \
    $(wildcard include/config/lv/font/default/montserrat/42.h) \
    $(wildcard include/config/lv/font/default/montserrat/44.h) \
    $(wildcard include/config/lv/font/default/montserrat/46.h) \
    $(wildcard include/config/lv/font/default/montserrat/48.h) \
    $(wildcard include/config/lv/font/default/montserrat/12/subpx.h) \
    $(wildcard include/config/lv/font/default/montserrat/28/compressed.h) \
    $(wildcard include/config/lv/font/default/dejavu/16/persian/hebrew.h) \
    $(wildcard include/config/lv/font/default/simsun/14/cjk.h) \
    $(wildcard include/config/lv/font/default/simsun/16/cjk.h) \
    $(wildcard include/config/lv/font/default/unscii/8.h) \
    $(wildcard include/config/lv/font/default/unscii/16.h) \
    $(wildcard include/config/lv/txt/enc/utf8.h) \
    $(wildcard include/config/lv/txt/enc/ascii.h) \
    $(wildcard include/config/lv/base/dir/ltr.h) \
    $(wildcard include/config/lv/base/dir/rtl.h) \
    $(wildcard include/config/lv/base/dir/auto.h) \
    $(wildcard include/config/lv/sdl/render/mode/partial.h) \
    $(wildcard include/config/lv/sdl/render/mode/direct.h) \
    $(wildcard include/config/lv/sdl/render/mode/full.h) \
    $(wildcard include/config/lv/linux/fbdev/render/mode/partial.h) \
    $(wildcard include/config/lv/linux/fbdev/render/mode/direct.h) \
    $(wildcard include/config/lv/linux/fbdev/render/mode/full.h) \
  elibrary/lib_lvgl/src/misc/lv_types.h \
  elibrary/lib_lvgl/src/misc/../lv_conf_internal.h \
  /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/riscv64-unknown-elf/include/inttypes.h \
  /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/riscv64-unknown-elf/include/_ansi.h \
  elibrary/lib_lvgl/src/stdlib/lv_mem.h \
  elibrary/lib_lvgl/src/stdlib/../lv_conf_internal.h \
  elibrary/lib_lvgl/src/stdlib/lv_string.h \
  elibrary/lib_lvgl/src/stdlib/../misc/lv_types.h \
  elibrary/lib_lvgl/src/stdlib/lv_string.h \
  elibrary/lib_lvgl/src/stdlib/lv_sprintf.h \
  elibrary/lib_lvgl/src/misc/lv_log.h \
  elibrary/lib_lvgl/src/misc/lv_types.h \
  elibrary/lib_lvgl/src/misc/lv_timer.h \
  elibrary/lib_lvgl/src/misc/../tick/lv_tick.h \
  elibrary/lib_lvgl/src/misc/../tick/../lv_conf_internal.h \
  elibrary/lib_lvgl/src/misc/../tick/../misc/lv_types.h \
  elibrary/lib_lvgl/src/misc/lv_ll.h \
  elibrary/lib_lvgl/src/misc/lv_math.h \
  elibrary/lib_lvgl/src/misc/lv_array.h \
  elibrary/lib_lvgl/src/misc/lv_async.h \
  elibrary/lib_lvgl/src/misc/lv_anim_timeline.h \
  elibrary/lib_lvgl/src/misc/lv_anim.h \
  elibrary/lib_lvgl/src/misc/lv_math.h \
  elibrary/lib_lvgl/src/misc/lv_timer.h \
  elibrary/lib_lvgl/src/misc/lv_profiler_builtin.h \
  elibrary/lib_lvgl/src/misc/lv_rb.h \
  elibrary/lib_lvgl/src/misc/lv_assert.h \
  elibrary/lib_lvgl/src/misc/lv_log.h \
  elibrary/lib_lvgl/src/misc/../stdlib/lv_mem.h \
  elibrary/lib_lvgl/src/misc/lv_utils.h \
  elibrary/lib_lvgl/src/misc/../draw/lv_draw_buf.h \
  elibrary/lib_lvgl/src/misc/../draw/../misc/lv_types.h \
  elibrary/lib_lvgl/src/misc/../draw/../misc/lv_area.h \
  elibrary/lib_lvgl/src/misc/../draw/../misc/../lv_conf_internal.h \
  elibrary/lib_lvgl/src/misc/../draw/../misc/lv_types.h \
  elibrary/lib_lvgl/src/misc/../draw/../misc/lv_math.h \
  elibrary/lib_lvgl/src/misc/../draw/../misc/lv_color.h \
  elibrary/lib_lvgl/src/misc/../draw/../misc/lv_assert.h \
  elibrary/lib_lvgl/src/misc/../draw/../misc/lv_palette.h \
  elibrary/lib_lvgl/src/misc/../draw/../misc/lv_color.h \
  elibrary/lib_lvgl/src/misc/../draw/../misc/lv_color_op.h \
  elibrary/lib_lvgl/src/misc/../draw/../stdlib/lv_string.h \
  elibrary/lib_lvgl/src/misc/../draw/lv_image_dsc.h \
  elibrary/lib_lvgl/src/misc/../draw/../lv_conf_internal.h \
  elibrary/lib_lvgl/src/tick/lv_tick.h \
  elibrary/lib_lvgl/src/core/lv_obj.h \
  elibrary/lib_lvgl/src/core/../lv_conf_internal.h \
  elibrary/lib_lvgl/src/core/../misc/lv_types.h \
  elibrary/lib_lvgl/src/core/../misc/lv_style.h \
  elibrary/lib_lvgl/src/core/../misc/../font/lv_font.h \
  elibrary/lib_lvgl/src/core/../misc/../font/../lv_conf_internal.h \
  elibrary/lib_lvgl/src/core/../misc/../font/../misc/lv_types.h \
  elibrary/lib_lvgl/src/core/../misc/../font/lv_symbol_def.h \
  elibrary/lib_lvgl/src/core/../misc/../font/../draw/lv_draw_buf.h \
  elibrary/lib_lvgl/src/core/../misc/../font/../misc/lv_area.h \
  elibrary/lib_lvgl/src/core/../misc/../font/../misc/cache/lv_cache.h \
  elibrary/lib_lvgl/src/core/../misc/../font/../misc/cache/lv_cache_entry.h \
  elibrary/lib_lvgl/src/core/../misc/../font/../misc/cache/../../osal/lv_os.h \
  elibrary/lib_lvgl/src/core/../misc/../font/../misc/cache/../../osal/../lv_conf_internal.h \
  elibrary/lib_lvgl/src/core/../misc/../font/../misc/cache/../../osal/../misc/lv_types.h \
  elibrary/lib_lvgl/src/core/../misc/../font/../misc/cache/../../osal/lv_os_none.h \
  elibrary/lib_lvgl/src/core/../misc/../font/../misc/cache/../lv_types.h \
  elibrary/lib_lvgl/src/core/../misc/../font/../misc/cache/lv_cache_private.h \
  elibrary/lib_lvgl/src/core/../misc/../font/../misc/cache/lv_cache_lru_rb.h \
  elibrary/lib_lvgl/src/core/../misc/../font/../misc/cache/lv_image_cache.h \
  elibrary/lib_lvgl/src/core/../misc/../font/../misc/cache/../../lv_conf_internal.h \
  elibrary/lib_lvgl/src/core/../misc/../font/../misc/cache/lv_image_header_cache.h \
  elibrary/lib_lvgl/src/core/../misc/lv_color.h \
  elibrary/lib_lvgl/src/core/../misc/lv_area.h \
  elibrary/lib_lvgl/src/core/../misc/lv_anim.h \
  elibrary/lib_lvgl/src/core/../misc/lv_text.h \
  elibrary/lib_lvgl/src/core/../misc/../lv_conf_internal.h \
  elibrary/lib_lvgl/src/core/../misc/lv_types.h \
  elibrary/lib_lvgl/src/core/../misc/../stdlib/lv_sprintf.h \
  elibrary/lib_lvgl/src/core/../misc/lv_assert.h \
  elibrary/lib_lvgl/src/core/../misc/lv_bidi.h \
  elibrary/lib_lvgl/src/core/../misc/../layouts/lv_layout.h \
  elibrary/lib_lvgl/src/core/../misc/../layouts/../lv_conf_internal.h \
  elibrary/lib_lvgl/src/core/../misc/../layouts/../misc/lv_types.h \
  elibrary/lib_lvgl/src/core/../misc/../layouts/flex/lv_flex.h \
  elibrary/lib_lvgl/src/core/../misc/../layouts/flex/../../lv_conf_internal.h \
  elibrary/lib_lvgl/src/core/../misc/../layouts/flex/../../misc/lv_area.h \
  elibrary/lib_lvgl/src/core/../misc/../layouts/grid/lv_grid.h \
  elibrary/lib_lvgl/src/core/../misc/../layouts/grid/../../lv_conf_internal.h \
  elibrary/lib_lvgl/src/core/../misc/../layouts/grid/../../misc/lv_area.h \
  elibrary/lib_lvgl/src/core/../misc/lv_style_gen.h \
  elibrary/lib_lvgl/src/core/../misc/lv_area.h \
  elibrary/lib_lvgl/src/core/../misc/lv_color.h \
  elibrary/lib_lvgl/src/core/../misc/lv_assert.h \
  elibrary/lib_lvgl/src/core/lv_obj_tree.h \
  elibrary/lib_lvgl/src/core/../misc/lv_anim.h \
  elibrary/lib_lvgl/src/core/../display/lv_display.h \
  elibrary/lib_lvgl/src/core/../display/../misc/lv_types.h \
  elibrary/lib_lvgl/src/core/../display/../misc/lv_timer.h \
  elibrary/lib_lvgl/src/core/../display/../misc/lv_event.h \
  elibrary/lib_lvgl/src/core/../display/../misc/lv_types.h \
  elibrary/lib_lvgl/src/core/../display/../misc/../lv_conf_internal.h \
  elibrary/lib_lvgl/src/core/../display/../misc/lv_array.h \
  elibrary/lib_lvgl/src/core/../display/../misc/lv_color.h \
  elibrary/lib_lvgl/src/core/../display/../draw/lv_draw.h \
  elibrary/lib_lvgl/src/core/../display/../draw/../lv_conf_internal.h \
  elibrary/lib_lvgl/src/core/../display/../draw/../misc/lv_types.h \
  elibrary/lib_lvgl/src/core/../display/../draw/../misc/lv_style.h \
  elibrary/lib_lvgl/src/core/../display/../draw/../misc/lv_text.h \
  elibrary/lib_lvgl/src/core/../display/../draw/../misc/lv_profiler.h \
  elibrary/lib_lvgl/src/core/../display/../draw/../misc/../lv_conf_internal.h \
  elibrary/lib_lvgl/src/core/../display/../draw/../misc/lv_matrix.h \
  elibrary/lib_lvgl/src/core/../display/../draw/../misc/lv_types.h \
  elibrary/lib_lvgl/src/core/../display/../draw/../misc/lv_area.h \
  elibrary/lib_lvgl/src/core/../display/../draw/lv_image_decoder.h \
  elibrary/lib_lvgl/src/core/../display/../draw/lv_draw_buf.h \
  elibrary/lib_lvgl/src/core/../display/../draw/../misc/lv_fs.h \
  elibrary/lib_lvgl/src/core/../display/../draw/../misc/lv_area.h \
  elibrary/lib_lvgl/src/core/../display/../draw/../misc/cache/lv_cache.h \
  elibrary/lib_lvgl/src/core/../display/../draw/../osal/lv_os.h \
  elibrary/lib_lvgl/src/core/lv_obj_pos.h \
  elibrary/lib_lvgl/src/core/lv_obj_scroll.h \
  elibrary/lib_lvgl/src/core/lv_obj_style.h \
  elibrary/lib_lvgl/src/core/../misc/lv_bidi.h \
  elibrary/lib_lvgl/src/core/lv_obj_style_gen.h \
  elibrary/lib_lvgl/src/core/../core/lv_obj_style.h \
  elibrary/lib_lvgl/src/core/lv_obj_draw.h \
  elibrary/lib_lvgl/src/core/../draw/lv_draw_rect.h \
  elibrary/lib_lvgl/src/core/../draw/lv_draw.h \
  elibrary/lib_lvgl/src/core/../draw/../misc/lv_color.h \
  elibrary/lib_lvgl/src/core/../draw/../misc/lv_area.h \
  elibrary/lib_lvgl/src/core/../draw/../misc/lv_style.h \
  elibrary/lib_lvgl/src/core/../draw/sw/lv_draw_sw_gradient.h \
  elibrary/lib_lvgl/src/core/../draw/sw/../../misc/lv_color.h \
  elibrary/lib_lvgl/src/core/../draw/sw/../../misc/lv_style.h \
  elibrary/lib_lvgl/src/core/../draw/lv_draw_label.h \
  elibrary/lib_lvgl/src/core/../draw/lv_draw_rect.h \
  elibrary/lib_lvgl/src/core/../draw/../misc/lv_bidi.h \
  elibrary/lib_lvgl/src/core/../draw/../misc/lv_text.h \
  elibrary/lib_lvgl/src/core/../draw/lv_draw_image.h \
  elibrary/lib_lvgl/src/core/../draw/lv_image_decoder.h \
  elibrary/lib_lvgl/src/core/../draw/lv_draw_buf.h \
  elibrary/lib_lvgl/src/core/../draw/lv_draw_line.h \
  elibrary/lib_lvgl/src/core/../draw/../lv_conf_internal.h \
  elibrary/lib_lvgl/src/core/../draw/lv_draw_arc.h \
  elibrary/lib_lvgl/src/core/lv_obj_class.h \
  elibrary/lib_lvgl/src/core/lv_obj_property.h \
  elibrary/lib_lvgl/src/core/lv_obj_event.h \
  elibrary/lib_lvgl/src/core/../misc/lv_event.h \
  elibrary/lib_lvgl/src/core/../indev/lv_indev.h \
  elibrary/lib_lvgl/src/core/../indev/../core/lv_group.h \
  elibrary/lib_lvgl/src/core/../indev/../core/../lv_conf_internal.h \
  elibrary/lib_lvgl/src/core/../indev/../core/../misc/lv_types.h \
  elibrary/lib_lvgl/src/core/../indev/../core/../misc/lv_ll.h \
  elibrary/lib_lvgl/src/core/../indev/../misc/lv_area.h \
  elibrary/lib_lvgl/src/core/../indev/../misc/lv_timer.h \
  elibrary/lib_lvgl/src/core/../indev/../misc/lv_event.h \
  elibrary/lib_lvgl/src/core/lv_group.h \
  elibrary/lib_lvgl/src/core/lv_group.h \
  elibrary/lib_lvgl/src/indev/lv_indev.h \
  elibrary/lib_lvgl/src/core/lv_refr.h \
  elibrary/lib_lvgl/src/core/lv_obj.h \
  elibrary/lib_lvgl/src/display/lv_display.h \
  elibrary/lib_lvgl/src/font/lv_font.h \
  elibrary/lib_lvgl/src/font/lv_binfont_loader.h \
  elibrary/lib_lvgl/src/font/lv_font_fmt_txt.h \
  elibrary/lib_lvgl/src/font/lv_font.h \
  elibrary/lib_lvgl/src/font/../misc/lv_types.h \
  elibrary/lib_lvgl/src/widgets/animimage/lv_animimage.h \
  elibrary/lib_lvgl/src/widgets/animimage/../image/lv_image.h \
  elibrary/lib_lvgl/src/widgets/animimage/../image/../../lv_conf_internal.h \
  elibrary/lib_lvgl/src/widgets/animimage/../image/../../core/lv_obj.h \
  elibrary/lib_lvgl/src/widgets/animimage/../image/../../misc/lv_fs.h \
  elibrary/lib_lvgl/src/widgets/animimage/../image/../../draw/lv_draw.h \
  elibrary/lib_lvgl/src/widgets/animimage/../../misc/lv_types.h \
  elibrary/lib_lvgl/src/widgets/arc/lv_arc.h \
  elibrary/lib_lvgl/src/widgets/arc/../../lv_conf_internal.h \
  elibrary/lib_lvgl/src/widgets/arc/../../core/lv_obj.h \
  elibrary/lib_lvgl/src/widgets/bar/lv_bar.h \
  elibrary/lib_lvgl/src/widgets/bar/../../lv_conf_internal.h \
  elibrary/lib_lvgl/src/widgets/bar/../../core/lv_obj.h \
  elibrary/lib_lvgl/src/widgets/bar/../../misc/lv_anim.h \
  elibrary/lib_lvgl/src/widgets/bar/../label/lv_label.h \
  elibrary/lib_lvgl/src/widgets/bar/../label/../../lv_conf_internal.h \
  elibrary/lib_lvgl/src/widgets/bar/../label/../../misc/lv_types.h \
  elibrary/lib_lvgl/src/widgets/bar/../label/../../core/lv_obj.h \
  elibrary/lib_lvgl/src/widgets/bar/../label/../../font/lv_font.h \
  elibrary/lib_lvgl/src/widgets/bar/../label/../../font/lv_symbol_def.h \
  elibrary/lib_lvgl/src/widgets/bar/../label/../../misc/lv_text.h \
  elibrary/lib_lvgl/src/widgets/bar/../label/../../draw/lv_draw.h \
  elibrary/lib_lvgl/src/widgets/button/lv_button.h \
  elibrary/lib_lvgl/src/widgets/button/../../lv_conf_internal.h \
  elibrary/lib_lvgl/src/widgets/button/../../core/lv_obj.h \
  elibrary/lib_lvgl/src/widgets/buttonmatrix/lv_buttonmatrix.h \
  elibrary/lib_lvgl/src/widgets/buttonmatrix/../../lv_conf_internal.h \
  elibrary/lib_lvgl/src/widgets/buttonmatrix/../../core/lv_obj.h \
  elibrary/lib_lvgl/src/widgets/calendar/lv_calendar.h \
  elibrary/lib_lvgl/src/widgets/calendar/../buttonmatrix/lv_buttonmatrix.h \
  elibrary/lib_lvgl/src/widgets/calendar/lv_calendar_header_arrow.h \
  elibrary/lib_lvgl/src/widgets/calendar/../../core/lv_obj.h \
  elibrary/lib_lvgl/src/widgets/calendar/lv_calendar_header_dropdown.h \
  elibrary/lib_lvgl/src/widgets/calendar/lv_calendar_chinese.h \
  elibrary/lib_lvgl/src/widgets/calendar/lv_calendar.h \
  elibrary/lib_lvgl/src/widgets/canvas/lv_canvas.h \
  elibrary/lib_lvgl/src/widgets/canvas/../../lv_conf_internal.h \
  elibrary/lib_lvgl/src/widgets/canvas/../image/lv_image.h \
  elibrary/lib_lvgl/src/widgets/canvas/../../draw/lv_draw_image.h \
  elibrary/lib_lvgl/src/widgets/chart/lv_chart.h \
  elibrary/lib_lvgl/src/widgets/chart/../../lv_conf_internal.h \
  elibrary/lib_lvgl/src/widgets/chart/../../core/lv_obj.h \
  elibrary/lib_lvgl/src/widgets/checkbox/lv_checkbox.h \
  elibrary/lib_lvgl/src/widgets/checkbox/../../lv_conf_internal.h \
  elibrary/lib_lvgl/src/widgets/checkbox/../../core/lv_obj.h \
  elibrary/lib_lvgl/src/widgets/dropdown/lv_dropdown.h \
  elibrary/lib_lvgl/src/widgets/dropdown/../../lv_conf_internal.h \
  elibrary/lib_lvgl/src/widgets/dropdown/../label/lv_label.h \
  elibrary/lib_lvgl/src/widgets/image/lv_image.h \
  elibrary/lib_lvgl/src/widgets/imagebutton/lv_imagebutton.h \
  elibrary/lib_lvgl/src/widgets/imagebutton/../../core/lv_obj.h \
  elibrary/lib_lvgl/src/widgets/keyboard/lv_keyboard.h \
  elibrary/lib_lvgl/src/widgets/keyboard/../buttonmatrix/lv_buttonmatrix.h \
  elibrary/lib_lvgl/src/widgets/label/lv_label.h \
  elibrary/lib_lvgl/src/widgets/led/lv_led.h \
  elibrary/lib_lvgl/src/widgets/led/../../core/lv_obj.h \
  elibrary/lib_lvgl/src/widgets/line/lv_line.h \
  elibrary/lib_lvgl/src/widgets/line/../../core/lv_obj.h \
  elibrary/lib_lvgl/src/widgets/list/lv_list.h \
  elibrary/lib_lvgl/src/widgets/list/../../core/lv_obj.h \
  elibrary/lib_lvgl/src/widgets/lottie/lv_lottie.h \
  elibrary/lib_lvgl/src/widgets/lottie/../../misc/lv_types.h \
  elibrary/lib_lvgl/src/widgets/menu/lv_menu.h \
  elibrary/lib_lvgl/src/widgets/menu/../../core/lv_obj.h \
  elibrary/lib_lvgl/src/widgets/msgbox/lv_msgbox.h \
  elibrary/lib_lvgl/src/widgets/msgbox/../../core/lv_obj.h \
  elibrary/lib_lvgl/src/widgets/roller/lv_roller.h \
  elibrary/lib_lvgl/src/widgets/roller/../../core/lv_obj.h \
  elibrary/lib_lvgl/src/widgets/roller/../label/lv_label.h \
  elibrary/lib_lvgl/src/widgets/scale/lv_scale.h \
  elibrary/lib_lvgl/src/widgets/scale/../../lv_conf_internal.h \
  elibrary/lib_lvgl/src/widgets/scale/../../core/lv_obj.h \
  elibrary/lib_lvgl/src/widgets/scale/../line/lv_line.h \
  elibrary/lib_lvgl/src/widgets/scale/../image/lv_image.h \
  elibrary/lib_lvgl/src/widgets/slider/lv_slider.h \
  elibrary/lib_lvgl/src/widgets/slider/../bar/lv_bar.h \
  elibrary/lib_lvgl/src/widgets/span/lv_span.h \
  elibrary/lib_lvgl/src/widgets/span/../../lv_conf_internal.h \
  elibrary/lib_lvgl/src/widgets/span/../../core/lv_obj.h \
  elibrary/lib_lvgl/src/widgets/spinbox/lv_spinbox.h \
  elibrary/lib_lvgl/src/widgets/spinbox/../textarea/lv_textarea.h \
  elibrary/lib_lvgl/src/widgets/spinbox/../textarea/../label/lv_label.h \
  elibrary/lib_lvgl/src/widgets/spinner/lv_spinner.h \
  elibrary/lib_lvgl/src/widgets/spinner/../../lv_conf_internal.h \
  elibrary/lib_lvgl/src/widgets/switch/lv_switch.h \
  elibrary/lib_lvgl/src/widgets/switch/../../lv_conf_internal.h \
  elibrary/lib_lvgl/src/widgets/switch/../../core/lv_obj.h \
  elibrary/lib_lvgl/src/widgets/table/lv_table.h \
  elibrary/lib_lvgl/src/widgets/table/../label/lv_label.h \
  elibrary/lib_lvgl/src/widgets/tabview/lv_tabview.h \
  elibrary/lib_lvgl/src/widgets/tabview/../../lv_conf_internal.h \
  elibrary/lib_lvgl/src/widgets/tabview/../../core/lv_obj.h \
  elibrary/lib_lvgl/src/widgets/textarea/lv_textarea.h \
  elibrary/lib_lvgl/src/widgets/tileview/lv_tileview.h \
  elibrary/lib_lvgl/src/widgets/tileview/../../core/lv_obj.h \
  elibrary/lib_lvgl/src/widgets/win/lv_win.h \
  elibrary/lib_lvgl/src/widgets/win/../../lv_conf_internal.h \
  elibrary/lib_lvgl/src/widgets/win/../../core/lv_obj.h \
  elibrary/lib_lvgl/src/others/snapshot/lv_snapshot.h \
  elibrary/lib_lvgl/src/others/snapshot/../../core/lv_obj.h \
  elibrary/lib_lvgl/src/others/sysmon/lv_sysmon.h \
  elibrary/lib_lvgl/src/others/sysmon/../../misc/lv_timer.h \
  elibrary/lib_lvgl/src/others/sysmon/../../others/observer/lv_observer.h \
  elibrary/lib_lvgl/src/others/sysmon/../../others/observer/../../core/lv_obj.h \
  elibrary/lib_lvgl/src/others/monkey/lv_monkey.h \
  elibrary/lib_lvgl/src/others/monkey/../../lv_conf_internal.h \
  elibrary/lib_lvgl/src/others/monkey/../../indev/lv_indev.h \
  elibrary/lib_lvgl/src/others/gridnav/lv_gridnav.h \
  elibrary/lib_lvgl/src/others/gridnav/../../core/lv_obj.h \
  elibrary/lib_lvgl/src/others/fragment/lv_fragment.h \
  elibrary/lib_lvgl/src/others/fragment/../../core/lv_obj.h \
  elibrary/lib_lvgl/src/others/imgfont/lv_imgfont.h \
  elibrary/lib_lvgl/src/others/imgfont/../../lv_conf_internal.h \
  elibrary/lib_lvgl/src/others/imgfont/../../font/lv_font.h \
  elibrary/lib_lvgl/src/others/observer/lv_observer.h \
  elibrary/lib_lvgl/src/others/ime/lv_ime_pinyin.h \
  elibrary/lib_lvgl/src/others/ime/../../lv_conf_internal.h \
  elibrary/lib_lvgl/src/others/ime/../../core/lv_obj.h \
  elibrary/lib_lvgl/src/others/file_explorer/lv_file_explorer.h \
  elibrary/lib_lvgl/src/others/file_explorer/../../lv_conf_internal.h \
  elibrary/lib_lvgl/src/others/file_explorer/../../core/lv_obj.h \
  elibrary/lib_lvgl/src/libs/barcode/lv_barcode.h \
  elibrary/lib_lvgl/src/libs/barcode/../../lv_conf_internal.h \
  elibrary/lib_lvgl/src/libs/barcode/../../misc/lv_types.h \
  elibrary/lib_lvgl/src/libs/barcode/../../misc/lv_color.h \
  elibrary/lib_lvgl/src/libs/barcode/../../widgets/canvas/lv_canvas.h \
  elibrary/lib_lvgl/src/libs/bin_decoder/lv_bin_decoder.h \
  elibrary/lib_lvgl/src/libs/bin_decoder/../../draw/lv_image_decoder.h \
  elibrary/lib_lvgl/src/libs/bmp/lv_bmp.h \
  elibrary/lib_lvgl/src/libs/bmp/../../lv_conf_internal.h \
  elibrary/lib_lvgl/src/libs/rle/lv_rle.h \
  elibrary/lib_lvgl/src/libs/rle/../../lv_conf_internal.h \
  elibrary/lib_lvgl/src/libs/fsdrv/lv_fsdrv.h \
  elibrary/lib_lvgl/src/libs/fsdrv/../../lv_conf_internal.h \
  elibrary/lib_lvgl/src/libs/lodepng/lv_lodepng.h \
  elibrary/lib_lvgl/src/libs/lodepng/../../lv_conf_internal.h \
  elibrary/lib_lvgl/src/libs/libpng/lv_libpng.h \
  elibrary/lib_lvgl/src/libs/libpng/../../lv_conf_internal.h \
  elibrary/lib_lvgl/src/libs/gif/lv_gif.h \
  elibrary/lib_lvgl/src/libs/gif/../../lv_conf_internal.h \
  elibrary/lib_lvgl/src/libs/gif/../../misc/lv_types.h \
  elibrary/lib_lvgl/src/libs/gif/../../draw/lv_draw_buf.h \
  elibrary/lib_lvgl/src/libs/gif/../../widgets/image/lv_image.h \
  elibrary/lib_lvgl/src/libs/gif/../../core/lv_obj_class.h \
  elibrary/lib_lvgl/src/libs/qrcode/lv_qrcode.h \
  elibrary/lib_lvgl/src/libs/qrcode/../../lv_conf_internal.h \
  elibrary/lib_lvgl/src/libs/qrcode/../../misc/lv_color.h \
  elibrary/lib_lvgl/src/libs/qrcode/../../misc/lv_types.h \
  elibrary/lib_lvgl/src/libs/qrcode/../../widgets/canvas/lv_canvas.h \
  elibrary/lib_lvgl/src/libs/tjpgd/lv_tjpgd.h \
  elibrary/lib_lvgl/src/libs/libjpeg_turbo/lv_libjpeg_turbo.h \
  elibrary/lib_lvgl/src/libs/libjpeg_turbo/../../lv_conf_internal.h \
  elibrary/lib_lvgl/src/libs/freetype/lv_freetype.h \
  elibrary/lib_lvgl/src/libs/freetype/../../lv_conf_internal.h \
  elibrary/lib_lvgl/src/libs/freetype/../../misc/lv_types.h \
  elibrary/lib_lvgl/src/libs/freetype/../../misc/lv_event.h \
  elibrary/lib_lvgl/src/libs/rlottie/lv_rlottie.h \
  elibrary/lib_lvgl/src/libs/rlottie/../../lv_conf_internal.h \
  elibrary/lib_lvgl/src/libs/ffmpeg/lv_ffmpeg.h \
  elibrary/lib_lvgl/src/libs/ffmpeg/../../lv_conf_internal.h \
  elibrary/lib_lvgl/src/libs/tiny_ttf/lv_tiny_ttf.h \
  elibrary/lib_lvgl/src/libs/tiny_ttf/../../lv_conf_internal.h \
  elibrary/lib_lvgl/src/layouts/lv_layout.h \
  elibrary/lib_lvgl/src/draw/lv_draw.h \
  elibrary/lib_lvgl/src/draw/lv_draw_buf.h \
  elibrary/lib_lvgl/src/draw/lv_draw_vector.h \
  elibrary/lib_lvgl/src/draw/../misc/lv_array.h \
  elibrary/lib_lvgl/src/draw/../misc/lv_matrix.h \
  elibrary/lib_lvgl/src/draw/lv_draw_image.h \
  elibrary/lib_lvgl/src/draw/sw/lv_draw_sw.h \
  elibrary/lib_lvgl/src/draw/sw/../lv_draw.h \
  elibrary/lib_lvgl/src/draw/sw/../../misc/lv_area.h \
  elibrary/lib_lvgl/src/draw/sw/../../misc/lv_color.h \
  elibrary/lib_lvgl/src/draw/sw/../../display/lv_display.h \
  elibrary/lib_lvgl/src/draw/sw/../../osal/lv_os.h \
  elibrary/lib_lvgl/src/draw/sw/../lv_draw_vector.h \
  elibrary/lib_lvgl/src/draw/sw/../lv_draw_triangle.h \
  elibrary/lib_lvgl/src/draw/sw/../lv_draw_rect.h \
  elibrary/lib_lvgl/src/draw/sw/../lv_draw_label.h \
  elibrary/lib_lvgl/src/draw/sw/../lv_draw_image.h \
  elibrary/lib_lvgl/src/draw/sw/../lv_draw_line.h \
  elibrary/lib_lvgl/src/draw/sw/../lv_draw_arc.h \
  elibrary/lib_lvgl/src/draw/sw/blend/lv_draw_sw_blend.h \
  elibrary/lib_lvgl/src/draw/sw/blend/../lv_draw_sw_mask.h \
  elibrary/lib_lvgl/src/draw/sw/blend/../../../misc/lv_area.h \
  elibrary/lib_lvgl/src/draw/sw/blend/../../../misc/lv_color.h \
  elibrary/lib_lvgl/src/draw/sw/blend/../../../misc/lv_math.h \
  elibrary/lib_lvgl/src/draw/sw/blend/../../../misc/lv_types.h \
  elibrary/lib_lvgl/src/draw/sw/blend/../../../misc/lv_color.h \
  elibrary/lib_lvgl/src/draw/sw/blend/../../../misc/lv_area.h \
  elibrary/lib_lvgl/src/draw/sw/blend/../../../misc/lv_style.h \
  elibrary/lib_lvgl/src/themes/lv_theme.h \
  elibrary/lib_lvgl/src/themes/../core/lv_obj.h \
  elibrary/lib_lvgl/src/themes/default/lv_theme_default.h \
  elibrary/lib_lvgl/src/themes/default/../lv_theme.h \
  elibrary/lib_lvgl/src/themes/mono/lv_theme_mono.h \
  elibrary/lib_lvgl/src/themes/mono/../lv_theme.h \
  elibrary/lib_lvgl/src/themes/simple/lv_theme_simple.h \
  elibrary/lib_lvgl/src/themes/simple/../lv_theme.h \
  elibrary/lib_lvgl/src/themes/simple/../../display/lv_display.h \
  elibrary/lib_lvgl/src/drivers/lv_drivers.h \
  elibrary/lib_lvgl/src/drivers/sdl/lv_sdl_window.h \
  elibrary/lib_lvgl/src/drivers/sdl/../../display/lv_display.h \
  elibrary/lib_lvgl/src/drivers/sdl/../../indev/lv_indev.h \
  elibrary/lib_lvgl/src/drivers/sdl/lv_sdl_mouse.h \
  elibrary/lib_lvgl/src/drivers/sdl/lv_sdl_window.h \
  elibrary/lib_lvgl/src/drivers/sdl/lv_sdl_mousewheel.h \
  elibrary/lib_lvgl/src/drivers/sdl/lv_sdl_keyboard.h \
  elibrary/lib_lvgl/src/drivers/x11/lv_x11.h \
  elibrary/lib_lvgl/src/drivers/x11/../../display/lv_display.h \
  elibrary/lib_lvgl/src/drivers/x11/../../indev/lv_indev.h \
  elibrary/lib_lvgl/src/drivers/display/drm/lv_linux_drm.h \
  elibrary/lib_lvgl/src/drivers/display/drm/../../../display/lv_display.h \
  elibrary/lib_lvgl/src/drivers/display/fb/lv_linux_fbdev.h \
  elibrary/lib_lvgl/src/drivers/display/fb/../../../display/lv_display.h \
  elibrary/lib_lvgl/src/drivers/display/tft_espi/lv_tft_espi.h \
  elibrary/lib_lvgl/src/drivers/display/tft_espi/../../../display/lv_display.h \
  elibrary/lib_lvgl/src/drivers/nuttx/lv_nuttx_entry.h \
  elibrary/lib_lvgl/src/drivers/nuttx/../../lv_conf_internal.h \
  elibrary/lib_lvgl/src/drivers/nuttx/../../display/lv_display.h \
  elibrary/lib_lvgl/src/drivers/nuttx/../../indev/lv_indev.h \
  elibrary/lib_lvgl/src/drivers/nuttx/lv_nuttx_fbdev.h \
  elibrary/lib_lvgl/src/drivers/nuttx/lv_nuttx_touchscreen.h \
  elibrary/lib_lvgl/src/drivers/nuttx/lv_nuttx_lcd.h \
  elibrary/lib_lvgl/src/drivers/nuttx/lv_nuttx_libuv.h \
  elibrary/lib_lvgl/src/drivers/evdev/lv_evdev.h \
  elibrary/lib_lvgl/src/drivers/evdev/../../indev/lv_indev.h \
  elibrary/lib_lvgl/src/drivers/libinput/lv_libinput.h \
  elibrary/lib_lvgl/src/drivers/libinput/../../indev/lv_indev.h \
  elibrary/lib_lvgl/src/drivers/windows/lv_windows_input.h \
  elibrary/lib_lvgl/src/drivers/windows/../../display/lv_display.h \
  elibrary/lib_lvgl/src/drivers/windows/../../indev/lv_indev.h \
  elibrary/lib_lvgl/src/drivers/windows/lv_windows_display.h \
  elibrary/lib_lvgl/src/drivers/glfw/lv_glfw_window.h \
  elibrary/lib_lvgl/src/drivers/glfw/../../lv_conf_internal.h \
  elibrary/lib_lvgl/src/drivers/glfw/lv_opengles_texture.h \
  elibrary/lib_lvgl/src/drivers/glfw/lv_opengles_driver.h \
  elibrary/lib_lvgl/src/drivers/qnx/lv_qnx.h \
  elibrary/lib_lvgl/src/drivers/qnx/../../display/lv_display.h \
  elibrary/lib_lvgl/src/drivers/qnx/../../indev/lv_indev.h \
  elibrary/lib_lvgl/src/drivers/wayland/lv_wayland.h \
  elibrary/lib_lvgl/src/drivers/wayland/../../display/lv_display.h \
  elibrary/lib_lvgl/src/drivers/wayland/../../indev/lv_indev.h \
  elibrary/lib_lvgl/src/lv_api_map_v8.h \
  elibrary/lib_lvgl/src/lv_api_map_v9_0.h \
  elibrary/lib_lvgl/src/lv_api_map_v9_1.h \
  elibrary/lib_lvgl/src/lvgl_private.h \
  elibrary/lib_lvgl/src/core/lv_global.h \
  elibrary/lib_lvgl/src/core/../draw/lv_draw.h \
  elibrary/lib_lvgl/src/core/../draw/sw/lv_draw_sw.h \
  elibrary/lib_lvgl/src/core/../misc/lv_color_op.h \
  elibrary/lib_lvgl/src/core/../misc/lv_ll.h \
  elibrary/lib_lvgl/src/core/../misc/lv_log.h \
  elibrary/lib_lvgl/src/core/../misc/lv_timer.h \
  elibrary/lib_lvgl/src/core/../osal/lv_os.h \
  elibrary/lib_lvgl/src/core/../others/sysmon/lv_sysmon.h \
  elibrary/lib_lvgl/src/core/../stdlib/builtin/lv_tlsf.h \
  elibrary/lib_lvgl/src/core/../stdlib/builtin/../../lv_conf_internal.h \
  elibrary/lib_lvgl/src/core/../stdlib/builtin/../../osal/lv_os.h \
  elibrary/lib_lvgl/src/core/../stdlib/builtin/../../misc/lv_ll.h \
  elibrary/lib_lvgl/src/core/../stdlib/builtin/../../misc/lv_types.h \
  elibrary/lib_lvgl/src/core/../tick/lv_tick.h \
  elibrary/lib_lvgl/src/core/../layouts/lv_layout.h \
  elibrary/lib_lvgl/src/core/../misc/lv_timer_private.h \
  elibrary/lib_lvgl/src/core/../misc/lv_timer.h \
  elibrary/lib_lvgl/src/core/../misc/lv_anim_private.h \
  elibrary/lib_lvgl/src/core/../tick/lv_tick_private.h \
  elibrary/lib_lvgl/src/core/../tick/lv_tick.h \
  elibrary/lib_lvgl/src/core/../draw/lv_draw_buf_private.h \
  elibrary/lib_lvgl/src/core/../draw/lv_draw_private.h \
  elibrary/lib_lvgl/src/core/../draw/sw/lv_draw_sw_private.h \
  elibrary/lib_lvgl/src/core/../draw/sw/lv_draw_sw.h \
  elibrary/lib_lvgl/src/core/../draw/sw/../lv_draw_private.h \
  elibrary/lib_lvgl/src/core/../draw/sw/lv_draw_sw_mask_private.h \
  elibrary/lib_lvgl/src/core/../draw/sw/lv_draw_sw_mask.h \
  elibrary/lib_lvgl/src/core/../stdlib/builtin/lv_tlsf_private.h \
  elibrary/lib_lvgl/src/core/../stdlib/builtin/lv_tlsf.h \
  elibrary/lib_lvgl/src/core/../others/sysmon/lv_sysmon_private.h \
  elibrary/lib_lvgl/src/core/../others/sysmon/lv_sysmon.h \
  elibrary/lib_lvgl/src/core/../layouts/lv_layout_private.h \
  elibrary/lib_lvgl/src/core/../layouts/lv_layout.h \
  elibrary/lib_lvgl/src/display/lv_display_private.h \
  elibrary/lib_lvgl/src/display/../misc/lv_types.h \
  elibrary/lib_lvgl/src/display/../core/lv_obj.h \
  elibrary/lib_lvgl/src/display/../draw/lv_draw.h \
  elibrary/lib_lvgl/src/display/lv_display.h \
  elibrary/lib_lvgl/src/indev/lv_indev_private.h \
  elibrary/lib_lvgl/src/indev/lv_indev.h \
  elibrary/lib_lvgl/src/indev/../misc/lv_anim.h \
  elibrary/lib_lvgl/src/misc/lv_text_private.h \
  elibrary/lib_lvgl/src/misc/lv_text.h \
  elibrary/lib_lvgl/src/misc/cache/lv_cache_entry_private.h \
  elibrary/lib_lvgl/src/misc/cache/../lv_types.h \
  elibrary/lib_lvgl/src/misc/cache/../../osal/lv_os.h \
  elibrary/lib_lvgl/src/misc/cache/../lv_profiler.h \
  elibrary/lib_lvgl/src/misc/cache/lv_cache_private.h \
  elibrary/lib_lvgl/src/layouts/lv_layout_private.h \
  elibrary/lib_lvgl/src/stdlib/lv_mem_private.h \
  elibrary/lib_lvgl/src/stdlib/lv_mem.h \
  elibrary/lib_lvgl/src/others/file_explorer/lv_file_explorer_private.h \
  elibrary/lib_lvgl/src/others/file_explorer/../../core/lv_obj_private.h \
  elibrary/lib_lvgl/src/others/file_explorer/../../core/lv_obj.h \
  elibrary/lib_lvgl/src/others/file_explorer/lv_file_explorer.h \
  elibrary/lib_lvgl/src/others/sysmon/lv_sysmon_private.h \
  elibrary/lib_lvgl/src/others/monkey/lv_monkey_private.h \
  elibrary/lib_lvgl/src/others/monkey/lv_monkey.h \
  elibrary/lib_lvgl/src/others/ime/lv_ime_pinyin_private.h \
  elibrary/lib_lvgl/src/others/ime/../../core/lv_obj_private.h \
  elibrary/lib_lvgl/src/others/ime/lv_ime_pinyin.h \
  elibrary/lib_lvgl/src/others/fragment/lv_fragment_private.h \
  elibrary/lib_lvgl/src/others/fragment/lv_fragment.h \
  elibrary/lib_lvgl/src/others/observer/lv_observer_private.h \
  elibrary/lib_lvgl/src/others/observer/lv_observer.h \
  elibrary/lib_lvgl/src/libs/qrcode/lv_qrcode_private.h \
  elibrary/lib_lvgl/src/libs/qrcode/../../widgets/canvas/lv_canvas_private.h \
  elibrary/lib_lvgl/src/libs/qrcode/../../widgets/canvas/../image/lv_image_private.h \
  elibrary/lib_lvgl/src/libs/qrcode/../../widgets/canvas/../image/../../core/lv_obj_private.h \
  elibrary/lib_lvgl/src/libs/qrcode/../../widgets/canvas/../image/lv_image.h \
  elibrary/lib_lvgl/src/libs/qrcode/../../widgets/canvas/lv_canvas.h \
  elibrary/lib_lvgl/src/libs/qrcode/lv_qrcode.h \
  elibrary/lib_lvgl/src/libs/barcode/lv_barcode_private.h \
  elibrary/lib_lvgl/src/libs/barcode/../../widgets/canvas/lv_canvas_private.h \
  elibrary/lib_lvgl/src/libs/barcode/lv_barcode.h \
  elibrary/lib_lvgl/src/libs/gif/lv_gif_private.h \
  elibrary/lib_lvgl/src/libs/gif/../../widgets/image/lv_image_private.h \
  elibrary/lib_lvgl/src/libs/gif/lv_gif.h \
  elibrary/lib_lvgl/src/draw/lv_draw_triangle_private.h \
  elibrary/lib_lvgl/src/draw/lv_draw_triangle.h \
  elibrary/lib_lvgl/src/draw/lv_draw_private.h \
  elibrary/lib_lvgl/src/draw/lv_draw_rect_private.h \
  elibrary/lib_lvgl/src/draw/lv_draw_rect.h \
  elibrary/lib_lvgl/src/draw/lv_draw_image_private.h \
  elibrary/lib_lvgl/src/draw/lv_image_decoder_private.h \
  elibrary/lib_lvgl/src/draw/lv_image_decoder.h \
  elibrary/lib_lvgl/src/draw/lv_draw_label_private.h \
  elibrary/lib_lvgl/src/draw/lv_draw_label.h \
  elibrary/lib_lvgl/src/draw/lv_draw_vector_private.h \
  elibrary/lib_lvgl/src/draw/lv_draw_vector.h \
  elibrary/lib_lvgl/src/draw/lv_draw_buf_private.h \
  elibrary/lib_lvgl/src/draw/lv_draw_mask_private.h \
  elibrary/lib_lvgl/src/draw/lv_draw_mask.h \
  elibrary/lib_lvgl/src/draw/../misc/lv_color.h \
  elibrary/lib_lvgl/src/draw/../misc/lv_area.h \
  elibrary/lib_lvgl/src/draw/../misc/lv_style.h \
  elibrary/lib_lvgl/src/draw/lv_draw.h \
  elibrary/lib_lvgl/src/draw/sw/lv_draw_sw_gradient_private.h \
  elibrary/lib_lvgl/src/draw/sw/lv_draw_sw_gradient.h \
  elibrary/lib_lvgl/src/draw/sw/lv_draw_sw_private.h \
  elibrary/lib_lvgl/src/draw/sw/lv_draw_sw_mask_private.h \
  elibrary/lib_lvgl/src/draw/sw/blend/lv_draw_sw_blend_private.h \
  elibrary/lib_lvgl/src/draw/sw/blend/lv_draw_sw_blend.h \
  elibrary/lib_lvgl/src/drivers/libinput/lv_xkb_private.h \
  elibrary/lib_lvgl/src/drivers/libinput/lv_xkb.h \
  elibrary/lib_lvgl/src/drivers/libinput/../../lv_conf_internal.h \
  elibrary/lib_lvgl/src/drivers/libinput/lv_libinput_private.h \
  elibrary/lib_lvgl/src/drivers/libinput/lv_libinput.h \
  elibrary/lib_lvgl/src/font/lv_font_fmt_txt_private.h \
  elibrary/lib_lvgl/src/font/lv_font_fmt_txt.h \
  elibrary/lib_lvgl/src/themes/lv_theme_private.h \
  elibrary/lib_lvgl/src/themes/lv_theme.h \
  elibrary/lib_lvgl/src/core/lv_refr_private.h \
  elibrary/lib_lvgl/src/core/lv_refr.h \
  elibrary/lib_lvgl/src/core/lv_obj_style_private.h \
  elibrary/lib_lvgl/src/core/lv_obj_private.h \
  elibrary/lib_lvgl/src/core/lv_obj_scroll_private.h \
  elibrary/lib_lvgl/src/core/lv_obj_draw_private.h \
  elibrary/lib_lvgl/src/core/lv_obj_class_private.h \
  elibrary/lib_lvgl/src/core/lv_group_private.h \
  elibrary/lib_lvgl/src/core/lv_obj_event_private.h \
  elibrary/lib_lvgl/src/misc/lv_timer_private.h \
  elibrary/lib_lvgl/src/misc/lv_area_private.h \
  elibrary/lib_lvgl/src/misc/lv_area.h \
  elibrary/lib_lvgl/src/misc/lv_fs_private.h \
  elibrary/lib_lvgl/src/misc/lv_fs.h \
  elibrary/lib_lvgl/src/misc/lv_profiler_builtin_private.h \
  elibrary/lib_lvgl/src/misc/lv_profiler_builtin.h \
  elibrary/lib_lvgl/src/misc/lv_event_private.h \
  elibrary/lib_lvgl/src/misc/lv_event.h \
  elibrary/lib_lvgl/src/misc/lv_bidi_private.h \
  elibrary/lib_lvgl/src/misc/lv_bidi.h \
  elibrary/lib_lvgl/src/misc/lv_rb_private.h \
  elibrary/lib_lvgl/src/misc/lv_rb.h \
  elibrary/lib_lvgl/src/misc/lv_style_private.h \
  elibrary/lib_lvgl/src/misc/lv_style.h \
  elibrary/lib_lvgl/src/misc/lv_color_op_private.h \
  elibrary/lib_lvgl/src/misc/lv_color_op.h \
  elibrary/lib_lvgl/src/misc/lv_anim_private.h \
  elibrary/lib_lvgl/src/widgets/msgbox/lv_msgbox_private.h \
  elibrary/lib_lvgl/src/widgets/msgbox/lv_msgbox.h \
  elibrary/lib_lvgl/src/widgets/msgbox/../../core/lv_obj_private.h \
  elibrary/lib_lvgl/src/widgets/buttonmatrix/lv_buttonmatrix_private.h \
  elibrary/lib_lvgl/src/widgets/buttonmatrix/../../core/lv_obj_private.h \
  elibrary/lib_lvgl/src/widgets/buttonmatrix/lv_buttonmatrix.h \
  elibrary/lib_lvgl/src/widgets/slider/lv_slider_private.h \
  elibrary/lib_lvgl/src/widgets/slider/../bar/lv_bar_private.h \
  elibrary/lib_lvgl/src/widgets/slider/../bar/../../core/lv_obj_private.h \
  elibrary/lib_lvgl/src/widgets/slider/../bar/lv_bar.h \
  elibrary/lib_lvgl/src/widgets/slider/lv_slider.h \
  elibrary/lib_lvgl/src/widgets/switch/lv_switch_private.h \
  elibrary/lib_lvgl/src/widgets/switch/lv_switch.h \
  elibrary/lib_lvgl/src/widgets/switch/../../core/lv_obj_private.h \
  elibrary/lib_lvgl/src/widgets/calendar/lv_calendar_private.h \
  elibrary/lib_lvgl/src/widgets/calendar/../../core/lv_obj_private.h \
  elibrary/lib_lvgl/src/widgets/imagebutton/lv_imagebutton_private.h \
  elibrary/lib_lvgl/src/widgets/imagebutton/lv_imagebutton.h \
  elibrary/lib_lvgl/src/widgets/imagebutton/../../core/lv_obj_private.h \
  elibrary/lib_lvgl/src/widgets/bar/lv_bar_private.h \
  elibrary/lib_lvgl/src/widgets/image/lv_image_private.h \
  elibrary/lib_lvgl/src/widgets/textarea/lv_textarea_private.h \
  elibrary/lib_lvgl/src/widgets/textarea/../../core/lv_obj_private.h \
  elibrary/lib_lvgl/src/widgets/textarea/lv_textarea.h \
  elibrary/lib_lvgl/src/widgets/table/lv_table_private.h \
  elibrary/lib_lvgl/src/widgets/table/lv_table.h \
  elibrary/lib_lvgl/src/widgets/table/../../core/lv_obj_private.h \
  elibrary/lib_lvgl/src/widgets/checkbox/lv_checkbox_private.h \
  elibrary/lib_lvgl/src/widgets/checkbox/lv_checkbox.h \
  elibrary/lib_lvgl/src/widgets/checkbox/../../core/lv_obj_private.h \
  elibrary/lib_lvgl/src/widgets/roller/lv_roller_private.h \
  elibrary/lib_lvgl/src/widgets/roller/lv_roller.h \
  elibrary/lib_lvgl/src/widgets/roller/../../core/lv_obj_private.h \
  elibrary/lib_lvgl/src/widgets/win/lv_win_private.h \
  elibrary/lib_lvgl/src/widgets/win/../../core/lv_obj_private.h \
  elibrary/lib_lvgl/src/widgets/win/lv_win.h \
  elibrary/lib_lvgl/src/widgets/keyboard/lv_keyboard_private.h \
  elibrary/lib_lvgl/src/widgets/keyboard/../buttonmatrix/lv_buttonmatrix_private.h \
  elibrary/lib_lvgl/src/widgets/keyboard/lv_keyboard.h \
  elibrary/lib_lvgl/src/widgets/line/lv_line_private.h \
  elibrary/lib_lvgl/src/widgets/line/../../core/lv_obj_private.h \
  elibrary/lib_lvgl/src/widgets/line/lv_line.h \
  elibrary/lib_lvgl/src/widgets/animimage/lv_animimage_private.h \
  elibrary/lib_lvgl/src/widgets/animimage/../image/lv_image_private.h \
  elibrary/lib_lvgl/src/widgets/animimage/../../misc/lv_anim_private.h \
  elibrary/lib_lvgl/src/widgets/animimage/lv_animimage.h \
  elibrary/lib_lvgl/src/widgets/dropdown/lv_dropdown_private.h \
  elibrary/lib_lvgl/src/widgets/dropdown/../../core/lv_obj_private.h \
  elibrary/lib_lvgl/src/widgets/dropdown/lv_dropdown.h \
  elibrary/lib_lvgl/src/widgets/menu/lv_menu_private.h \
  elibrary/lib_lvgl/src/widgets/menu/../../core/lv_obj_private.h \
  elibrary/lib_lvgl/src/widgets/menu/lv_menu.h \
  elibrary/lib_lvgl/src/widgets/chart/lv_chart_private.h \
  elibrary/lib_lvgl/src/widgets/chart/../../core/lv_obj_private.h \
  elibrary/lib_lvgl/src/widgets/chart/lv_chart.h \
  elibrary/lib_lvgl/src/widgets/button/lv_button_private.h \
  elibrary/lib_lvgl/src/widgets/button/../../core/lv_obj_private.h \
  elibrary/lib_lvgl/src/widgets/button/lv_button.h \
  elibrary/lib_lvgl/src/widgets/scale/lv_scale_private.h \
  elibrary/lib_lvgl/src/widgets/scale/lv_scale.h \
  elibrary/lib_lvgl/src/widgets/scale/../../core/lv_obj_private.h \
  elibrary/lib_lvgl/src/widgets/led/lv_led_private.h \
  elibrary/lib_lvgl/src/widgets/led/lv_led.h \
  elibrary/lib_lvgl/src/widgets/led/../../core/lv_obj_private.h \
  elibrary/lib_lvgl/src/widgets/arc/lv_arc_private.h \
  elibrary/lib_lvgl/src/widgets/arc/../../core/lv_obj_private.h \
  elibrary/lib_lvgl/src/widgets/arc/lv_arc.h \
  elibrary/lib_lvgl/src/widgets/tileview/lv_tileview_private.h \
  elibrary/lib_lvgl/src/widgets/tileview/../../core/lv_obj_private.h \
  elibrary/lib_lvgl/src/widgets/tileview/lv_tileview.h \
  elibrary/lib_lvgl/src/widgets/spinbox/lv_spinbox_private.h \
  elibrary/lib_lvgl/src/widgets/spinbox/../textarea/lv_textarea_private.h \
  elibrary/lib_lvgl/src/widgets/spinbox/lv_spinbox.h \
  elibrary/lib_lvgl/src/widgets/span/lv_span_private.h \
  elibrary/lib_lvgl/src/widgets/span/../../core/lv_obj_private.h \
  elibrary/lib_lvgl/src/widgets/span/lv_span.h \
  elibrary/lib_lvgl/src/widgets/label/lv_label_private.h \
  elibrary/lib_lvgl/src/widgets/label/../../draw/lv_draw_label_private.h \
  elibrary/lib_lvgl/src/widgets/label/../../core/lv_obj_private.h \
  elibrary/lib_lvgl/src/widgets/label/lv_label.h \
  elibrary/lib_lvgl/src/widgets/canvas/lv_canvas_private.h \
  elibrary/lib_lvgl/src/widgets/tabview/lv_tabview_private.h \
  elibrary/lib_lvgl/src/widgets/tabview/../../core/lv_obj_private.h \
  elibrary/lib_lvgl/src/widgets/tabview/lv_tabview.h \
  elibrary/lib_lvgl/src/tick/lv_tick_private.h \
  elibrary/lib_lvgl/src/stdlib/builtin/lv_tlsf_private.h \
  elibrary/lib_lvgl/src/libs/rlottie/lv_rlottie_private.h \
  elibrary/lib_lvgl/src/libs/rlottie/lv_rlottie.h \
  elibrary/lib_lvgl/src/libs/ffmpeg/lv_ffmpeg_private.h \
  elibrary/lib_lvgl/src/libs/ffmpeg/lv_ffmpeg.h \
  elibrary/lib_lvgl/src/widgets/lottie/lv_lottie_private.h \
  elibrary/lib_lvgl/src/widgets/lottie/../../lv_conf_internal.h \
  elibrary/lib_lvgl/src/osal/lv_os_private.h \
  elibrary/lib_lvgl/sunxi/sunxig2d.h \
  elibrary/lib_lvgl/sunxi/../lv_conf.h \
  elibrary/lib_lvgl/sunxi/../lvgl.h \
  ekernel/drivers/rtos-hal/hal/source/g2d_rcq/g2d_driver.h \
    $(wildcard include/config/kernel/freertos.h) \
  include/melis/elibrary/libc/misc/pub0.h \
  include/melis/elibrary/libc/misc/pub0/elibs_cacheop.h \
  include/melis/elibrary/libc/misc/pub0/elibs_reg.h \
  include/melis/ekernel/arch/riscv/arch.h \
  elibrary/lib_lvgl/sunxi/sunximem.h \
  livedesk/leopard/applets/common/leopard_init.h \
  include/melis/elibrary/libc/elibs_string.h \
  livedesk/leopard/include/gui_msg.h \
  livedesk/leopard/include/elibs/lib_ex.h \
  livedesk/leopard/include/elibs/lib_ex/anole.h \
  include/melis/emodules/mod_willow.h \
  include/melis/elibrary/libc.h \
  include/melis/elibrary/./libc/elibs_stdlib.h \
  include/melis/elibrary/./libc/elibs_string.h \
  include/melis/elibrary/./libc/elibs_stdio.h \
  include/melis/elibrary/./libc/elibs_misc.h \
  include/melis/elibrary/./libc/misc/pub0.h \
  include/melis/misc/fb.h \
  include/melis/emodules/mod_defs.h \
  include/melis/emodules/mod_display.h \
  include/melis/log.h \
    $(wildcard include/config/log/default/level.h) \
    $(wildcard include/config/log/release.h) \
    $(wildcard include/config/dynamic/log/level/support.h) \
    $(wildcard include/config/disable/all/debuglog.h) \
  include/melis/emodules/mod_defs.h \
  include/melis/video/sunxi_display2.h \
  livedesk/leopard/include/elibs/lib_ex/scanfile/OAL.h \
  include/melis/emodules/mod_defs.h \
  livedesk/leopard/include/elibs/lib_ex/scanfile/ScanStack.h \
  livedesk/leopard/include/elibs/lib_ex/scanfile/ScanStackPri.h \
  livedesk/leopard/include/elibs/lib_ex/scanfile/ScanStack.h \
  livedesk/leopard/include/elibs/lib_ex/scanfile/Scan.h \
  livedesk/leopard/include/elibs/lib_ex/scanfile/ScanPri.h \
  livedesk/leopard/include/elibs/lib_ex/scanfile/OAL.h \
  livedesk/leopard/include/elibs/lib_ex/scanfile/Scan.h \
  livedesk/leopard/include/elibs/lib_ex/rat_common.h \
  livedesk/leopard/include/elibs/lib_ex/rat_miniature.h \
  include/melis/emodules/mod_willow.h \
  livedesk/leopard/include/elibs/lib_ex/rat_common.h \
  include/melis/emodules/mod_cedar.h \
    $(wildcard include/config/video/lyr/ck/enable.h) \
  include/melis/misc/fb.h \
  include/melis/emodules/mod_raw_decoder.h \
  livedesk/leopard/include/elibs/lib_ex/rat.h \
  /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/riscv64-unknown-elf/include/string.h \
  /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/riscv64-unknown-elf/include/sys/string.h \
  livedesk/leopard/include/elibs/lib_ex/rat_npl.h \
  livedesk/leopard/include/elibs/lib_ex/rat_partition.h \
  livedesk/leopard/include/elibs/lib_ex/robin.h \
  livedesk/leopard/include/elibs/lib_ex/robin/ab/robin_ab.h \
  livedesk/leopard/include/elibs/lib_ex/robin/channel/robin_channel.h \
  include/melis/emodules/mod_audio.h \
    $(wildcard include/config/plan/sprite.h) \
  livedesk/leopard/include/elibs/lib_ex/robin/eq/robin_eq.h \
  include/melis/emodules/mod_cedar.h \
  livedesk/leopard/include/elibs/lib_ex/robin/ff_rr_speed/robin_ff_rr_speed.h \
  livedesk/leopard/include/elibs/lib_ex/robin/fsm_ctrl/robin_fsm_ctrl.h \
  livedesk/leopard/include/elibs/lib_ex/robin/lyric/robin_lyric.h \
  livedesk/leopard/include/elibs/lib_ex/robin/media_info/robin_media_info.h \
  include/melis/emodules/mod_charset.h \
  include/melis/elibrary/libc/mediainfo/mediafmt.h \
  livedesk/leopard/include/elibs/lib_ex/robin/npl/robin_npl.h \
  livedesk/leopard/include/elibs/lib_ex/robin/play_mode/robin_play_mode.h \
  livedesk/leopard/include/elibs/lib_ex/robin/play_speed/robin_play_speed.h \
  livedesk/leopard/include/elibs/lib_ex/robin/spectrum/robin_spectrum.h \
  livedesk/leopard/include/elibs/lib_ex/robin/star/robin_star.h \
  livedesk/leopard/include/elibs/lib_ex/robin/subtitle/robin_subtitle.h \
  livedesk/leopard/include/elibs/lib_ex/robin/track/robin_track.h \
  livedesk/leopard/include/elibs/lib_ex/robin/video_layer/robin_video_layer.h \
  livedesk/leopard/include/elibs/lib_ex/robin/video_win/robin_video_win.h \
  livedesk/leopard/include/elibs/lib_ex/robin/vision_effect/robin_vision_effect.h \
  livedesk/leopard/include/elibs/lib_ex/robin/volume/robin_volume.h \
  livedesk/leopard/include/elibs/lib_ex/robin/zoom/robin_zoom.h \
  livedesk/leopard/include/elibs/lib_ex/robin/disp_output/robin_disp_output.h \
  livedesk/leopard/include/elibs/lib_ex/robin/palette/robin_palette.h \
  livedesk/leopard/applets/porting/lv_porting.h \
  livedesk/leopard/applets/porting/display/lv_port_disp.h \
  livedesk/leopard/applets/porting/input_device/lv_port_indev.h \
  livedesk/leopard/include/mod_desktop/msg_srv/msg_emit.h \
  livedesk/leopard/applets/porting/filesystem/lv_port_fs.h \
  livedesk/leopard/applets/porting/font/ttf_interface.h \
  livedesk/leopard/applets/porting/font/ttf_config.h \
  livedesk/leopard/applets/porting/font/lv_font_sft.h \
  livedesk/leopard/applets/porting/fw_update/dsk_fw_update.h \
  livedesk/leopard/applets/porting/image/lzma_Parse_Picture.h \
  include/melis/emodules/mod_mcu.h \
  include/melis/emodules/mod_fm.h \
  include/melis/emodules/mod_dab.h \
  include/melis/emodules/mod_aux.h \
  include/melis/emodules/mod_bp1064.h \
  include/melis/emodules/mod_bt_moudel.h \
  include/melis/emodules/SaveRds.h \
  include/melis/emodules/mod_video_play.h \
    $(wildcard include/config/para.h) \
  include/melis/emodules/../../../emodules/drv_mcu_uart/core/uart_cmd_set.h \
  include/melis/emodules/../../../emodules/drv_mcu_uart/core/sxi_defs.h \
  include/melis/emodules/../../../emodules/drv_mcu_uart/core/handlers/protocol/mcu_new_protocol_handler.h \
  include/melis/emodules/../../../emodules/drv_mcu_uart/core/handlers/protocol/../../mcu_cmd_dispatcher.h \
  include/melis/emodules/../../../emodules/drv_mcu_uart/core/handlers/protocol/../../uart_cmd_set.h \
  include/melis/emodules/../../../emodules/drv_mcu_uart/core/handlers/protocol/system/mcu_system_category_handler.h \
  include/melis/emodules/../../../emodules/drv_mcu_uart/core/handlers/protocol/audio/mcu_audio_category_handler.h \
  include/melis/emodules/../../../emodules/drv_mcu_uart/core/handlers/protocol/tuner/mcu_tuner_category_handler.h \
  include/melis/emodules/../../../emodules/drv_mcu_uart/core/handlers/protocol/tuner/../mcu_protocol_sync.h \
  include/melis/emodules/../../../emodules/drv_mcu_uart/core/handlers/protocol/usb/mcu_usb_category_handler.h \
  include/melis/emodules/../../../emodules/drv_mcu_uart/core/handlers/protocol/party/mcu_party_category_handler.h \
  include/melis/emodules/../../../emodules/drv_mcu_uart/core/handlers/protocol/settings/mcu_settings_category_handler.h \
  include/melis/elibrary/libc/misc/pub0.h \
  include/melis/elibrary/libc/eLIBs_az100.h \
  livedesk/leopard/applets/porting/image/lzma_bmp.h \
  livedesk/leopard/include/mod_desktop/desktop.h \
  livedesk/leopard/include/mod_desktop/util/elibs_language.h \
  livedesk/leopard/include/mod_desktop/msg_srv/dsk_msg.h \
  livedesk/leopard/include/mod_desktop/msg_srv/msg_emit.h \
  livedesk/leopard/include/mod_desktop/msg_srv/msg_emit.h \
  livedesk/leopard/include/mod_desktop/functions/dsk_reg.h \
  livedesk/leopard/include/mod_desktop/functions/reg/reg_audio_output.h \
  livedesk/leopard/include/mod_desktop/functions/reg/reg_display.h \
  livedesk/leopard/include/mod_desktop/functions/reg/reg_lcd_bright.h \
  livedesk/leopard/include/mod_desktop/functions/dsk_display.h \
  livedesk/leopard/include/mod_desktop/functions/display/dsk_display_gamma.h \
  livedesk/leopard/include/mod_desktop/functions/display/dsk_display_misc.h \
  livedesk/leopard/include/mod_desktop/functions/display/dsk_display_output.h \
  livedesk/leopard/include/mod_desktop/functions/display/dsk_display_lcd.h \
  livedesk/leopard/include/mod_desktop/functions/dsk_volume.h \
  livedesk/leopard/include/mod_desktop/functions/dsk_keytone.h \
  livedesk/leopard/include/mod_desktop/functions/dsk_usbh.h \
  livedesk/leopard/include/mod_desktop/functions/dsk_audio_if.h \
  include/melis/emodules/mod_audio.h \
  livedesk/leopard/include/mod_desktop/functions/dsk_charset.h \
  include/melis/emodules/mod_charset.h \
  livedesk/leopard/include/mod_desktop/functions/dsk_langres.h \
  livedesk/leopard/include/mod_desktop/functions/dsk_theme.h \
  livedesk/leopard/include/mod_desktop/functions/dsk_aux.h \
  livedesk/leopard/include/mod_desktop/functions/dsk_mcu.h \
  livedesk/leopard/include/mod_desktop/functions/dsk_bluetooth.h \
  livedesk/leopard/include/mod_desktop/functions/dsk_dab.h \
  livedesk/leopard/include/mod_desktop/functions/dsk_usbd.h \
  livedesk/leopard/include/mod_desktop/functions/dsk_power.h \
  livedesk/leopard/include/mod_desktop/functions/dsk_orchid.h \
  livedesk/leopard/include/mod_desktop/functions/dsk_auto_off.h \
  livedesk/leopard/include/mod_desktop/desktop_api.h \
  livedesk/leopard/include/mod_desktop/framework/activity.h \
  livedesk/leopard/include/mod_desktop/framework/applet_info_manager.h \
  livedesk/leopard/include/mod_desktop/framework/activity_manager.h \
  livedesk/leopard/include/mod_desktop/framework/applet_info_manager.h \
  livedesk/leopard/include/mod_desktop/framework/globals.h \
  livedesk/leopard/include/mod_desktop/engine/engine.h \
  livedesk/leopard/include/mod_desktop/engine/dsk_walkman.h \
  livedesk/leopard/include/mod_desktop/engine/walkman//dsk_wkm_npl.h \
  livedesk/leopard/include/mod_desktop/engine/walkman//dsk_wkm_play_mode.h \
  include/melis/emodules/mod_orchid.h \
  livedesk/leopard/include/mod_desktop/engine/walkman//dsk_wkm_fsm_ctrl.h \
  livedesk/leopard/include/mod_desktop/engine/walkman//dsk_wkm_ff_rr_speed.h \
  livedesk/leopard/include/mod_desktop/engine/walkman//dsk_wkm_eq.h \
  livedesk/leopard/include/mod_desktop/engine/walkman//dsk_wkm_ab.h \
  livedesk/leopard/include/mod_desktop/engine/walkman//dsk_wkm_lyric.h \
  livedesk/leopard/include/mod_desktop/engine/walkman//dsk_wkm_spectrum.h \
  livedesk/leopard/include/mod_desktop/engine/walkman//dsk_wkm_media_info.h \
  livedesk/leopard/include/mod_desktop/engine/walkman//dsk_wkm_play_speed.h \
  livedesk/leopard/include/mod_desktop/engine/walkman//dsk_wkm_star.h \
  livedesk/leopard/include/mod_desktop/engine/walkman//dsk_wkm_monitor_gate.h \
  livedesk/leopard/include/mod_desktop/engine/dsk_radio.h \
  livedesk/leopard/include/mod_desktop/engine/radio/dsk_radio_receive.h \
  livedesk/leopard/include/apps.h \
  ekernel/components/thirdparty/dfs/include/dfs_posix.h \
  ekernel/components/thirdparty/dfs/include/dfs_file.h \
  ekernel/components/thirdparty/dfs/include/dfs.h \
  /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/riscv64-unknown-elf/include/stdio.h \
  /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/riscv64-unknown-elf/include/sys/types.h \
  /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/riscv64-unknown-elf/include/sys/_pthreadtypes.h \
  /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/riscv64-unknown-elf/include/machine/types.h \
  /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/riscv64-unknown-elf/include/sys/stdio.h \
  /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/riscv64-unknown-elf/include/stdlib.h \
  /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/riscv64-unknown-elf/include/machine/stdlib.h \
  /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/riscv64-unknown-elf/include/time.h \
  /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/riscv64-unknown-elf/include/machine/time.h \
  /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/riscv64-unknown-elf/include/sys/timespec.h \
  /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/riscv64-unknown-elf/include/sys/_timespec.h \
  ekernel/core/rt-thread/include/rtthread.h \
  ekernel/core/rt-thread/include/rtconfig.h \
    $(wildcard include/config/rt/name/max.h) \
    $(wildcard include/config/rt/align/size.h) \
    $(wildcard include/config/modules.h) \
    $(wildcard include/config/rt/thread/priority/max.h) \
    $(wildcard include/config/hz.h) \
    $(wildcard include/config/rt/debug.h) \
    $(wildcard include/config/rt/debug/init/config.h) \
    $(wildcard include/config/rt/debug/thread/config.h) \
    $(wildcard include/config/rt/debug/scheduler/config.h) \
    $(wildcard include/config/rt/debug/ipc/config.h) \
    $(wildcard include/config/rt/debug/timer/config.h) \
    $(wildcard include/config/rt/debug/irq/config.h) \
    $(wildcard include/config/rt/debug/mem/config.h) \
    $(wildcard include/config/rt/debug/slab/config.h) \
    $(wildcard include/config/rt/debug/memheap/config.h) \
    $(wildcard include/config/rt/debug/module/config.h) \
    $(wildcard include/config/rt/using/overflow/check.h) \
    $(wildcard include/config/rt/using/hook.h) \
    $(wildcard include/config/rt/using/idle/hook.h) \
    $(wildcard include/config/rt/using/timer/soft.h) \
    $(wildcard include/config/rt/timer/thread/prio.h) \
    $(wildcard include/config/rt/timer/thread/stack/size.h) \
    $(wildcard include/config/rt/using/semaphore.h) \
    $(wildcard include/config/rt/using/mutex.h) \
    $(wildcard include/config/rt/using/event.h) \
    $(wildcard include/config/rt/using/mailbox.h) \
    $(wildcard include/config/rt/using/messagequeue.h) \
    $(wildcard include/config/rt/using/pipe.h) \
    $(wildcard include/config/rt/using/ringbuffer.h) \
    $(wildcard include/config/rt/using/waitqueue.h) \
    $(wildcard include/config/rt/using/workqueue.h) \
    $(wildcard include/config/rt/using/completion.h) \
    $(wildcard include/config/rt/using/signals.h) \
    $(wildcard include/config/rt/using/mempool.h) \
    $(wildcard include/config/rt/using/memheap.h) \
    $(wildcard include/config/rt/using/noheap.h) \
    $(wildcard include/config/rt/using/small/mem.h) \
    $(wildcard include/config/rt/using/slab.h) \
    $(wildcard include/config/rt/using/memheap/as/heap.h) \
    $(wildcard include/config/rt/using/memtrace.h) \
    $(wildcard include/config/rt/using/heap.h) \
    $(wildcard include/config/rt/using/device.h) \
    $(wildcard include/config/rt/using/interrupt/info.h) \
    $(wildcard include/config/rt/using/console.h) \
    $(wildcard include/config/rt/consolebuf/size.h) \
    $(wildcard include/config/rt/console/device/name.h) \
    $(wildcard include/config/rt/using/finsh.h) \
    $(wildcard include/config/finsh/using/symtab.h) \
    $(wildcard include/config/finsh/thread/name.h) \
    $(wildcard include/config/finsh/using/history.h) \
    $(wildcard include/config/finsh/history/lines.h) \
    $(wildcard include/config/finsh/using/description.h) \
    $(wildcard include/config/finsh/thread/priority.h) \
    $(wildcard include/config/finsh/thread/stack/size.h) \
    $(wildcard include/config/finsh/cmd/size.h) \
    $(wildcard include/config/finsh/using/msh.h) \
    $(wildcard include/config/finsh/using/msh/default.h) \
    $(wildcard include/config/finsh/arg/max.h) \
    $(wildcard include/config/rt/using/dfs.h) \
  ekernel/core/rt-thread/include/rtdebug.h \
  ekernel/core/rt-thread/include/rtdef.h \
    $(wildcard include/config/arch/riscv.h) \
    $(wildcard include/config/arch/riscv/vector.h) \
  include/melis/ekernel/arch/riscv/excep.h \
    $(wildcard include/config/fpu/double.h) \
  ekernel/core/rt-thread/include/rtservice.h \
  ekernel/core/rt-thread/include/rtm.h \
  ekernel/components/thirdparty/finsh_cli/finsh_api.h \
  /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/riscv64-unknown-elf/include/sys/stat.h \
  /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/riscv64-unknown-elf/include/fcntl.h \
  /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/riscv64-unknown-elf/include/sys/fcntl.h \
  /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/riscv64-unknown-elf/include/sys/_default_fcntl.h \
  /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/toolchain/riscv64-elf-x86_64-20201104/riscv64-unknown-elf/include/sys/unistd.h \
  ekernel/components/thirdparty/dfs/include/dfs_fs.h \
  include/melis/ekernel/drivers/sys_time.h \
  include/melis/emodules/mod_orchid.h \
  include/melis/emodules/mod_adb.h \
  include/melis/emodules/mod_aoa.h \
  include/melis/emodules/mod_mcu.h \
  include/melis/emodules/mod_touchpanel.h \
  include/melis/emodules/mod_codec_cmd.h \
  livedesk/leopard/include/applets/app_load_para.h \
  livedesk/leopard/include/applets/tv_visible_area.h \
  livedesk/leopard/include/elibs/lib_ex.h \
  livedesk/leopard/include/mod_desktop/desktop.h \
  livedesk/leopard/include/res/lang.h \
  livedesk/leopard/include/res/theme.h \
  livedesk/leopard/include/res/karaoke_theme.h \
  livedesk/leopard/include/mod_desktop/engine/radio/dsk_radio_send.h \
  include/melis/emodules/mod_mixture.h \
  livedesk/leopard/applets/apps/app_root/app_root.h \
  livedesk/leopard/applets/apps/app_root/../../framework/list.h \
  livedesk/leopard/applets/apps/lib/fb_lib/fb_file.h \
    $(wildcard include/config/backgrd/jpg.h) \
  include/melis/ekernel/arch/riscv/const.h \
  livedesk/leopard/applets/apps/lib/animation/ui_animation.h \
  livedesk/leopard/applets/apps/home/<USER>
  livedesk/leopard/applets/apps/app_root/app_root_helpers.h \
  livedesk/leopard/applets/apps/home/<USER>
  livedesk/leopard/applets/apps/home/<USER>
  livedesk/leopard/applets/apps/lib/source/ui_source_res.h \
  livedesk/leopard/applets/apps/explorer/ui_explorer.h \
  livedesk/leopard/applets/apps/lib/top_state_bar/ui_top_state_bar.h \
  livedesk/leopard/applets/apps/lib/top_state_bar/ui_top_state_bar_res.h \
  livedesk/leopard/applets/apps/lib/app_dialog/ui_dialog.h \
  livedesk/leopard/applets/apps/lib/app_dialog/ui_dialog_res.h \
  livedesk/leopard/applets/apps/lib/menu/ui_menu.h \
  livedesk/leopard/applets/apps/lib/menu/ui_menu_res.h \
  livedesk/leopard/applets/apps/lib/source/ui_source.h \
  livedesk/leopard/applets/apps/lib/source/ui_source_res.h \
  livedesk/leopard/applets/apps/lib/source/ui_source_communication_hub.h \
  livedesk/leopard/applets/porting/font/../../framework/list.h \
  livedesk/leopard/applets/apps/lib/background/ui_background.h \
  livedesk/leopard/applets/apps/lib/background/ui_background_res.h \
  livedesk/leopard/applets/apps/lib/fifo_db/fifo_db.h \
  livedesk/leopard/applets/apps/lib/fifo_db/gp_def.h \
  livedesk/leopard/applets/apps/lib/common/misc.h \
  livedesk/leopard/applets/apps/mirror_adb/ui_adb.h \
  livedesk/leopard/applets/apps/mirror_adb/ui_adb_helpers.h \
  livedesk/leopard/applets/apps/mirror_adb/ui_adb_res.h \
  livedesk/leopard/applets/apps/mirror_iphone/ui_iphone.h \
  livedesk/leopard/applets/apps/mirror_iphone/ui_iphone_helpers.h \
  livedesk/leopard/applets/apps/mirror_iphone/ui_iphone_res.h \
  livedesk/leopard/applets/apps/fm/ui_fm.h \
  livedesk/leopard/applets/apps/fm/ui_fm_res.h \
  livedesk/leopard/applets/apps/fm/ui_fm_helpers.h \
  livedesk/leopard/applets/apps/dab/ui_dab.h \
  livedesk/leopard/applets/apps/dab/ui_dab_res.h \
  livedesk/leopard/applets/apps/dab/ui_dab_helpers.h \
  livedesk/leopard/applets/apps/music/ui_music.h \
  livedesk/leopard/applets/apps/music/ui_music_helpers.h \
  livedesk/leopard/applets/apps/music/ui_music_res.h \
  livedesk/leopard/applets/apps/party_mode/ui_party_mode.h \
  livedesk/leopard/applets/apps/party_mode/ui_party_mode_helpers.h \
  livedesk/leopard/applets/apps/party_mode/ui_party_mode_res.h \
  livedesk/leopard/applets/apps/sxm/ui_sxm.h \
  livedesk/leopard/applets/apps/sxm/ui_sxm_helpers.h \
  livedesk/leopard/applets/apps/sxm/ui_sxm_res.h \
  livedesk/leopard/applets/apps/aux_in/ui_aux_in.h \
  livedesk/leopard/applets/apps/aux_in/ui_aux_in_helpers.h \
  livedesk/leopard/applets/apps/aux_in/ui_aux_in_res.h \
  livedesk/leopard/applets/apps/av_in/ui_av_in.h \
  livedesk/leopard/applets/apps/av_in/ui_av_in_helpers.h \
  livedesk/leopard/applets/apps/av_in/ui_av_in_res.h \
  include/melis/emodules/mod_video_play.h \
  include/melis/emodules/mod_tvd.h \
  livedesk/leopard/applets/apps/dvr/ui_dvr.h \
  livedesk/leopard/applets/apps/dvr/ui_dvr_helpers.h \
  livedesk/leopard/applets/apps/dvr/ui_dvr_res.h \
  livedesk/leopard/applets/apps/smenu/ui_smenu.h \
  livedesk/leopard/applets/apps/smenu/ui_smenu_res.h \
  livedesk/leopard/applets/apps/smenu/ui_smenu_helpers.h \
  livedesk/leopard/applets/apps/slideshow/ui_slideshow.h \
  livedesk/leopard/applets/apps/slideshow/ui_slideshow_res.h \
  livedesk/leopard/applets/apps/slideshow/ui_slideshow_helpers.h \
  livedesk/leopard/applets/apps/backcar/ui_backcar.h \
  livedesk/leopard/applets/apps/backcar/ui_backcar_helpers.h \
  livedesk/leopard/applets/apps/backcar/ui_backcar_res.h \
  livedesk/leopard/applets/apps/clock/ui_clock.h \
  livedesk/leopard/applets/apps/clock/ui_clock_helpers.h \
  livedesk/leopard/applets/apps/clock/ui_clock_res.h \
  livedesk/leopard/applets/apps/volume/ui_volume.h \
  livedesk/leopard/applets/apps/volume/ui_volume_helpers.h \
  livedesk/leopard/applets/apps/volume/ui_volume_res.h \
  livedesk/leopard/applets/apps/fw_update/ui_fw_update.h \

livedesk/leopard/applets/apps/app_root/app_root_events.o: $(deps_livedesk/leopard/applets/apps/app_root/app_root_events.o)

$(deps_livedesk/leopard/applets/apps/app_root/app_root_events.o):
