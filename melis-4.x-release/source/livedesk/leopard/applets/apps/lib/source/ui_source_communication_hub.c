#include "ui_source_communication_protocol.h"
#include "ui_source.h"

#if 1
#define comms_debug(...)	(eLIBs_printf("Source MSG:L%d:", __LINE__),				 \
													  eLIBs_printf(__VA_ARGS__)) 
#else
#define comms_debug(...)
#endif

reg_app_para_t *reg_app_para = NULL;


__u8 xover_frequency_value2index(__u8 value);
__u8 xover_frequency_index2value(__u8 index);


/****************************************************************/
/**********************init and common part*************************/
/****************************************************************/

__s32 ui_source_comms_hub_init(ui_source_para_t *para)
{
	int fmcu;			
	__hdle paramter_hdle=NULL;
	reg_all_para_t* app_para = NULL;

	if(para == NULL)
		return EPDK_FAIL;
	
	fmcu = open("/dev/mcu",O_RDWR);
	if(fmcu >= 0)
	{
		paramter_hdle=ioctl(fmcu, MCU_GET_PARAMTER_HDLE, (void *)0);

		if(paramter_hdle)
		{
			app_para = (reg_all_para_t *)paramter_hdle;
			reg_app_para = (reg_app_para_t *)&app_para->para_current;
			para->mcu_app_para = reg_app_para;
			
			close(fmcu);
			fmcu=NULL;

			//init mcu v2 ops
			para->mcu_v2_ops.get_data = ui_mcu_ops_v2_get_data;
			para->mcu_v2_ops.set_control = ui_mcu_ops_v2_set_control;
			
			return EPDK_OK;
		}

		close(fmcu);
		fmcu=NULL;
	}
	
	return EPDK_FAIL;	
}

__s32 ui_source_comms_hub_deinit(ui_source_para_t *para)
{
	if(para == NULL)
		return EPDK_FAIL;
	if(para->mcu_app_para)
		para->mcu_app_para = NULL;
	reg_app_para = NULL;

	return EPDK_OK;
}

__s32 comms_send_category_get_pkg(__u8 category,__u8 *data, __u16 data_len)
{
	__s32 ret;
	__u8 dData[128];
	__u16 len = 0,i=0,j=0;

	if(data == NULL || data_len <= 0)
		return EPDK_FAIL;

	i+=2; //skip pkg len
	dData[i++] = category;
	dData[i++] = OPERATION_GET;
	for(j=0;j<data_len;j++)
		dData[i++] = *(data+j);
	len = i-2;
	dData[0] = (__u8)(len>>8);
	dData[1] = (__u8)len;

	ret = dsk_send_mcu_cmd(UART_SEND_PROTOCOL_V2_CMD,dData);

	return ret;
}

__s32 comms_send_category_set_pkg(__u8 category,__u8 *data, __u16 data_len)
{
	__s32 ret;
	__u8 dData[128];
	__u8 *pData = NULL;
	__u8 *p = NULL;
	__u16 len = 0,i=0,j=0;

	if(data == NULL || data_len <= 0)
		return EPDK_FAIL;
	if(data_len <= (128-4)) //128 - 2bytes pkg len-1byte category id  - 1byte operation id
		p = dData;
	else
	{
		pData = (__u8 *)eLIBs_malloc(data_len+4);
		if(pData == NULL)
			return EPDK_FAIL;
		p = pData;
	}
	i+=2; //skip pkg len
	p[i++] = category;
	p[i++] = OPERATION_SET;
	for(j=0;j<data_len;j++)
		p[i++] = *(data+j);
	len = i-2;
	p[0] = (__u8)(len>>8);
	p[1] = (__u8)len;

	ret = dsk_send_mcu_cmd(UART_SEND_PROTOCOL_V2_CMD,p);

	if(pData)
	{
		eLIBs_free(pData);
		pData = NULL;
	}
	return ret;
}

/************************audio category**************************/
__s32 comms_send_audio_get_pkg(__u8 *data, __u16 data_len)
{
	__s32 ret;
	__u8 dData[128];
	__u16 len = 0,i=0,j=0;

	if(data == NULL || data_len <= 0)
		return EPDK_FAIL;

	i+=2; //skip pkg len
	dData[i++] = CATEGORY_AUDIO;
	dData[i++] = OPERATION_GET;
	for(j=0;j<data_len;j++)
		dData[i++] = *(data+j);
	len = i-2;
	dData[0] = (__u8)(len>>8);
	dData[1] = (__u8)len;

	ret = dsk_send_mcu_cmd(UART_SEND_PROTOCOL_V2_CMD,dData);
	
	return ret;
}

__s32 comms_send_audio_set_pkg(__u8 *data, __u16 data_len)
{
	__s32 ret;
	__u8 dData[128];
	__u16 len = 0,i=0,j=0;

	if(data == NULL || data_len <= 0)
		return EPDK_FAIL;

	i+=2; //skip pkg len
	dData[i++] = CATEGORY_AUDIO;
	dData[i++] = OPERATION_SET;
	for(j=0;j<data_len;j++)
		dData[i++] = *(data+j);
	len = i-2;
	dData[0] = (__u8)(len>>8);
	dData[1] = (__u8)len;

	ret = dsk_send_mcu_cmd(UART_SEND_PROTOCOL_V2_CMD,dData);
	
	return ret;
}


/****************************************************************/
/**********************audio settings part***************************/
/****************************************************************/

__s32 ui_source_comms_hub_settings_para_init(ui_source_para_t *para)
{
	if(para ==NULL || para->mcu_app_para == NULL)
		return EPDK_FAIL;
	//language
	para->setting_para.language = para->mcu_app_para->system_para.g_sys_para.language;
	//tuner region TODO...
	
	//auto brightness/dimmer switch
	para->setting_para.brightness = para->mcu_app_para->system_para.g_sys_para.dimmer_switch;
	//theme
	para->setting_para.theme = para->mcu_app_para->system_para.g_sys_para.theme;

	return EPDK_OK;
}

__s32 ui_source_comms_hub_general_audio_para_init(ui_source_para_t *para)
{
	audio_status_t* audio_para = NULL;
	reg_aux_para_t* aux_para;

	if(para ==NULL || para->mcu_app_para == NULL)
		return EPDK_FAIL;
	audio_para = &para->mcu_app_para->aux_para.g_audio_para;
	if(audio_para == NULL)
		return EPDK_FAIL;
	aux_para = (reg_aux_para_t*)dsk_reg_get_para_by_app(REG_APP_AUX);

	//zone 1 enable and name
	para->audio_para.zone1_on_off = audio_para->zones_enable&0x01;
	eLIBs_strcpy(para->audio_para.zone1_name,audio_para->zone_status[0].name);

	//zone 2 enable and name
	para->audio_para.zone2_on_off = (audio_para->zones_enable>>1)&0x01;
	eLIBs_strcpy(para->audio_para.zone2_name,audio_para->zone_status[1].name);

	//favorite zone
	para->audio_para.favorite_zone = audio_para->favorite_zone;
	
	para->audio_para.defeat_on_off = audio_para->default_switch;
	para->audio_para.vol_offset_on_off = audio_para->volume_offset_switch;
	para->audio_para.phone_vol_sync = audio_para->phone_vol_sync;
	para->audio_para.subwoofer_off = audio_para->subwoofer_switch;
	para->audio_para.subwoofer_phase = audio_para->subwoofer_phase;

	//eLIBs_printf("[init(%x)] subw sw = %d, %d\n",&aux_para->g_audio_para.subwoofer_switch,para->audio_para.subwoofer_off,aux_para->g_audio_para.subwoofer_switch);
	return EPDK_OK;
}

__s32 ui_source_comms_hub_source_gain_para_update(ui_source_para_t *para)
{
	audio_status_t* audio_para = NULL;
	if(para ==NULL || para->mcu_app_para == NULL)
		return EPDK_FAIL;
	audio_para = &para->mcu_app_para->aux_para.g_audio_para;
	if(audio_para == NULL)
		return EPDK_FAIL;

	//FM
	para->source_gain_para.gain_para[SOURCE_GAIN_FM].gain_value = audio_para->source_gain.gain[SYS_SOURCE_FM-1];
	//AM
	para->source_gain_para.gain_para[SOURCE_GAIN_AM].gain_value = audio_para->source_gain.gain[SYS_SOURCE_AM-1];
	//WB
	para->source_gain_para.gain_para[SOURCE_GAIN_WB].gain_value = audio_para->source_gain.gain[SYS_SOURCE_WB-1];
	//USB
	para->source_gain_para.gain_para[SOURCE_GAIN_USB].gain_value = audio_para->source_gain.gain[SYS_SOURCE_USB-1];
	//BT
	para->source_gain_para.gain_para[SOURCE_GAIN_BT].gain_value = audio_para->source_gain.gain[SYS_SOURCE_BT_AUDIO-1];
	//SXM
	para->source_gain_para.gain_para[SOURCE_GAIN_SXM].gain_value = audio_para->source_gain.gain[SYS_SOURCE_SXM-1];
	//DAB TODO..

	//AUX
	para->source_gain_para.gain_para[SOURCE_GAIN_AUX].gain_value = audio_para->source_gain.gain[SYS_SOURCE_AUXIN-1];
	//party mode
	para->source_gain_para.gain_para[SOURCE_GAIN_PARTY].gain_value = audio_para->source_gain.gain[SYS_SOURCE_PARTY-1];

	//globe 
	para->source_gain_para.gain_para[SOURCE_GAIN_ALL].gain_value = audio_para->source_gain.gain[SOURCE_GAIN_GLOBAL-1];

	return EPDK_OK;

}

__s32 ui_soruce_comms_hub_zone_audio_eq_para_update(ui_source_para_t *para)
{
	int i,j;
	__u8 zone_index;
	audio_status_t* audio_para = NULL;
	if(para ==NULL || para->mcu_app_para == NULL)
		return EPDK_FAIL;
	audio_para = &para->mcu_app_para->aux_para.g_audio_para;
	if(audio_para == NULL)
		return EPDK_FAIL;
	zone_index = para->audio_zone_para.zone_index;
	//current active eq type
	if(audio_para->zone_status[zone_index].equalizer.eq_type == AUDIO_EQ_TYPE_CUSTOM)
		para->eq_para.eq_mode = EQ_CUSTOM;
	else
		para->eq_para.eq_mode = audio_para->zone_status[zone_index].equalizer.eq_type;

	//all type's all bands value
	for(i=0;i<AUDIO_EQ_MAX_TYPE;i++)
	{
		for(j=0;j<AUDIO_EQ_BANDS;j++)
		{
			para->eq_para.eq_band_value[i][j] = audio_para->zone_status[zone_index].equalizer.bands_gain[i][j];
		}
	}

	return EPDK_OK;
}

__s32 ui_source_comms_hub_zone_audio_fade_balance_update(ui_source_para_t *para)
{
	__u8 zone_index;
	audio_status_t* audio_para = NULL;
	if(para ==NULL || para->mcu_app_para == NULL)
		return EPDK_FAIL;
	audio_para = &para->mcu_app_para->aux_para.g_audio_para;
	if(audio_para == NULL)
		return EPDK_FAIL;
	zone_index = para->audio_zone_para.zone_index;

	//fade value
	para->fader_balance_para.fader_vaule = audio_para->zone_status[zone_index].fade + AUDIO_FADE_MAX;
	//balance
	para->fader_balance_para.balance_vaule = audio_para->zone_status[zone_index].balance + AUDIO_BALANCE_MAX;

	return EPDK_OK;
}

__s32 ui_soruce_comms_hub_zone_audio_crossover_update(ui_source_para_t *para)
{
	int i,j;
	__u8 zone_index;
	__u8 value,index;
	
	audio_status_t* audio_para = NULL;
	if(para ==NULL || para->mcu_app_para == NULL)
		return EPDK_FAIL;
	audio_para = &para->mcu_app_para->aux_para.g_audio_para;
	if(audio_para == NULL)
		return EPDK_FAIL;
	zone_index = para->audio_zone_para.zone_index;
    //eLIBs_printf("ui_soruce_comms_hub_zone_audio_crossover_update zone_index=%d\n", zone_index);

	//all type's all bands value
	for(i=0;i<(AUDIO_CROSSOVER_MAX_POSITION);i++)
	{
		//filter type
		value = audio_para->zone_status[zone_index].crossover[i].filter_type;
		if(value >=AUDIO_FILTER_APF && value < AUDIO_FILTER_MAX)
			para->crossover_para.crossover_mode_para[i].crossover_type = value;
        
		//freq
		value = audio_para->zone_status[zone_index].crossover[i].frequency;
		index = xover_frequency_value2index(value);
		if(index != 0xFF) //check support or not
			para->crossover_para.crossover_mode_para[i].crossover_freq = index;
        
		//slope
		value = audio_para->zone_status[zone_index].crossover[i].slope;
		if(value >= AUDIO_SLOPE_12DB && value < AUDIO_SLOPE_MAX)
			para->crossover_para.crossover_mode_para[i].crossover_slope = value;
		comms_debug("[%d][xover[%d]:%d,%d,%d\n",zone_index,i,para->crossover_para.crossover_mode_para[i].crossover_type,para->crossover_para.crossover_mode_para[i].crossover_freq,para->crossover_para.crossover_mode_para[i].crossover_slope);
	}

	return EPDK_OK;
}

__s32 ui_source_comms_hub_zone_audio_punch_eq_update(ui_source_para_t *para)
{
	__u8 zone_index;
	audio_status_t* audio_para = NULL;
	if(para ==NULL || para->mcu_app_para == NULL)
		return EPDK_FAIL;
	audio_para = &para->mcu_app_para->aux_para.g_audio_para;
	if(audio_para == NULL)
		return EPDK_FAIL;
	zone_index = para->audio_zone_para.zone_index;

	para->punch_eq_para.value = audio_para->zone_status[zone_index].punch_gain;

	return EPDK_OK;
}

__s32 ui_source_comms_hub_zone_audio_para_init(ui_source_para_t *para)
{
	ui_soruce_comms_hub_zone_audio_eq_para_update(para);
	ui_source_comms_hub_zone_audio_fade_balance_update(para);
	ui_soruce_comms_hub_zone_audio_crossover_update(para);
	ui_source_comms_hub_zone_audio_punch_eq_update(para);

	return EPDK_OK;
}

__s32 ui_source_comms_hub_audio_volume_update(ui_source_para_t *para)
{
	if(para ==NULL || para->mcu_app_para == NULL)
		return EPDK_FAIL;

	//zone 1: volume
	para->source_vol_para.vol_para[VOL_ZONE1].vol_value = para->mcu_app_para->aux_para.g_audio_para.zone_status[AUDIO_ZONE_ID_1-1].volume;
	//zone 2: volume
	para->source_vol_para.vol_para[VOL_ZONE2].vol_value = para->mcu_app_para->aux_para.g_audio_para.zone_status[AUDIO_ZONE_ID_2-1].volume;
	//subwoofer volume
	para->source_vol_para.vol_para[VOL_SUB].vol_value = para->mcu_app_para->aux_para.g_audio_para.subwoofer_volume;

	//mute
	para->source_vol_para.mute = para->mcu_app_para->aux_para.g_audio_para.mute_state;

    comms_debug("VOL_ZONE1=%d, VOL_ZONE2=%d, VOL_SUB=%d\n", para->source_vol_para.vol_para[VOL_ZONE1].vol_value, para->source_vol_para.vol_para[VOL_ZONE2].vol_value, para->source_vol_para.vol_para[VOL_SUB].vol_value);
        
	return EPDK_OK;
}

//set audio volume part: zone volume, subwoofer volume

//set zone volume and subwoofer volume
__s32 ui_source_comms_set_audio_zone_vs_subwoofer_volume(ui_source_para_t *para)
{
	__s32 ret;
	audio_status_t* audio_para = NULL;
	__u8 data_pkg[16],i=0;
	__u16 data_len = 0,pkg_len = 0;
	__u32 data_type = 0;
	
	if(para ==NULL || para->mcu_app_para == NULL)
		return EPDK_FAIL;

	audio_para = &para->mcu_app_para->aux_para.g_audio_para;

	data_type = AUDIO_DATA_ZONE_VOLUME|AUDIO_DATA_SUBWOOFER_VOLUME;
	data_len = 4+1;
	//4byte data type
	data_pkg[i++] = (__u8)(data_type>>24);
	data_pkg[i++] = (__u8)(data_type>>16);
	data_pkg[i++] = (__u8)(data_type>>8);
	data_pkg[i++] = (__u8)data_type;
	//2 byte data len
	data_pkg[i++] = (__u8)(data_len>>8);
	data_pkg[i++] = (__u8)data_len;
	//4 zones volume data 
	data_pkg[i++] = para->source_vol_para.vol_para[VOL_ZONE1].vol_value;//para->mcu_app_para->aux_para.g_audio_para.zone_status[AUDIO_ZONE_ID_1-1].volume;
	data_pkg[i++] = para->source_vol_para.vol_para[VOL_ZONE2].vol_value;//para->mcu_app_para->aux_para.g_audio_para.zone_status[AUDIO_ZONE_ID_2-1].volume;
	data_pkg[i++] = 0xFF; //not support ZONE3
	data_pkg[i++] = 0xFF; //not support ZONE4
	//1byte subwoffer
	data_pkg[i++] = para->source_vol_para.vol_para[VOL_SUB].vol_value;
	pkg_len = i;

	ret = comms_send_audio_set_pkg(data_pkg,pkg_len);
	if(ret == EPDK_OK)
	{
		audio_para->zone_status[AUDIO_ZONE_ID_1-1].volume = para->source_vol_para.vol_para[VOL_ZONE1].vol_value;
		audio_para->zone_status[AUDIO_ZONE_ID_2-1].volume = para->source_vol_para.vol_para[VOL_ZONE2].vol_value;
		audio_para->subwoofer_volume = para->source_vol_para.vol_para[VOL_SUB].vol_value;
	}
	
	return ret;
}

//set zone volume
__s32 ui_source_comms_set_audio_zone_volume(ui_source_para_t *para)
{
	__s32 ret;
	audio_status_t* audio_para = NULL;
	__u8 data_pkg[16],i=0;
	__u16 data_len = 0,pkg_len = 0;
	__u32 data_type = 0;
	
	if(para ==NULL || para->mcu_app_para == NULL)
		return EPDK_FAIL;

	audio_para = &para->mcu_app_para->aux_para.g_audio_para;

	data_type = AUDIO_DATA_ZONE_VOLUME;
	data_len = 4;
	//4byte data type
	data_pkg[i++] = (__u8)(data_type>>24);
	data_pkg[i++] = (__u8)(data_type>>16);
	data_pkg[i++] = (__u8)(data_type>>8);
	data_pkg[i++] = (__u8)data_type;
	//2 byte data len
	data_pkg[i++] = (__u8)(data_len>>8);
	data_pkg[i++] = (__u8)data_len;
	//4 zones volume data 
	data_pkg[i++] = para->source_vol_para.vol_para[VOL_ZONE1].vol_value;//para->mcu_app_para->aux_para.g_audio_para.zone_status[AUDIO_ZONE_ID_1-1].volume;
	data_pkg[i++] = para->source_vol_para.vol_para[VOL_ZONE2].vol_value;//para->mcu_app_para->aux_para.g_audio_para.zone_status[AUDIO_ZONE_ID_2-1].volume;
	data_pkg[i++] = 0xFF; //not support ZONE3
	data_pkg[i++] = 0xFF; //not support ZONE4
	pkg_len = i;

	ret = comms_send_audio_set_pkg(data_pkg,pkg_len);
	if(ret == EPDK_OK)
	{
		audio_para->zone_status[AUDIO_ZONE_ID_1-1].volume = para->source_vol_para.vol_para[VOL_ZONE1].vol_value;
		audio_para->zone_status[AUDIO_ZONE_ID_2-1].volume = para->source_vol_para.vol_para[VOL_ZONE2].vol_value;
	}

	return ret;
}

//set subwoofer volume
__s32 ui_source_comms_set_audio_subwoofer_volume(ui_source_para_t *para)
{
	__s32 ret;
	audio_status_t* audio_para = NULL;
	__u8 data_pkg[16],i=0;
	__u16 data_len = 0,pkg_len = 0;
	__u32 data_type = 0;
	
	if(para ==NULL || para->mcu_app_para == NULL)
		return EPDK_FAIL;

	audio_para = &para->mcu_app_para->aux_para.g_audio_para;

	data_type = AUDIO_DATA_SUBWOOFER_VOLUME;
	data_len = 1;
	//4byte data type
	data_pkg[i++] = (__u8)(data_type>>24);
	data_pkg[i++] = (__u8)(data_type>>16);
	data_pkg[i++] = (__u8)(data_type>>8);
	data_pkg[i++] = (__u8)data_type;
	//2 byte data len
	data_pkg[i++] = (__u8)(data_len>>8);
	data_pkg[i++] = (__u8)data_len;
	//1byte subwoffer
	data_pkg[i++] = para->source_vol_para.vol_para[VOL_SUB].vol_value;
	pkg_len = i;

	ret = comms_send_audio_set_pkg(data_pkg,pkg_len);
	if(ret == EPDK_OK)
	{
		audio_para->subwoofer_volume = para->source_vol_para.vol_para[VOL_SUB].vol_value;
	}

	return ret;
}
//subwoofer switch
__s32 ui_source_comms_set_audio_subwoofer_switch(ui_source_para_t *para)
{
	__s32 ret;
	audio_status_t* audio_para = NULL;
	__u8 data_pkg[16],i=0;
	__u16 data_len = 0,pkg_len = 0;
	__u32 data_type = 0;
	
	if(para ==NULL || para->mcu_app_para == NULL)
		return EPDK_FAIL;

	audio_para = &para->mcu_app_para->aux_para.g_audio_para;

	data_type = AUDIO_DATA_SUBWOOFER_SWITCH;
	data_len = 1;
	//4byte data type
	data_pkg[i++] = (__u8)(data_type>>24);
	data_pkg[i++] = (__u8)(data_type>>16);
	data_pkg[i++] = (__u8)(data_type>>8);
	data_pkg[i++] = (__u8)data_type;
	//2 byte data len
	data_pkg[i++] = (__u8)(data_len>>8);
	data_pkg[i++] = (__u8)data_len;
	//1byte subwoofer switch
	data_pkg[i++] = para->audio_para.subwoofer_off;
	pkg_len = i;

	ret = comms_send_audio_set_pkg(data_pkg,pkg_len);
	if(ret == EPDK_OK)
	{
		audio_para->subwoofer_switch = para->audio_para.subwoofer_off;
	}

	return ret;
}
//subwoofer phase
__s32 ui_source_comms_set_audio_subwoofer_phase(ui_source_para_t *para)
{
	__s32 ret;
	audio_status_t* audio_para = NULL;
	__u8 data_pkg[16],i=0;
	__u16 data_len = 0,pkg_len = 0;
	__u32 data_type = 0;
	
	if(para ==NULL || para->mcu_app_para == NULL)
		return EPDK_FAIL;

	audio_para = &para->mcu_app_para->aux_para.g_audio_para;

	data_type = AUDIO_DATA_SUBWOOFER_PHASE;
	data_len = 1;
	//4byte data type
	data_pkg[i++] = (__u8)(data_type>>24);
	data_pkg[i++] = (__u8)(data_type>>16);
	data_pkg[i++] = (__u8)(data_type>>8);
	data_pkg[i++] = (__u8)data_type;
	//2 byte data len
	data_pkg[i++] = (__u8)(data_len>>8);
	data_pkg[i++] = (__u8)data_len;
	//1byte subwoofer phase
	data_pkg[i++] = para->audio_para.subwoofer_phase;
	pkg_len = i;

	ret = comms_send_audio_set_pkg(data_pkg,pkg_len);
	if(ret == EPDK_OK)
	{
		audio_para->subwoofer_phase = para->audio_para.subwoofer_phase;
	}

	return ret;
}

//audio mute on/off
__s32 ui_source_comms_set_audio_mute(ui_source_para_t *para)
{
	__s32 ret;
	audio_status_t* audio_para = NULL;
	__u8 data_pkg[16],i=0;
	__u16 data_len = 0,pkg_len = 0;
	__u32 data_type = 0;
	
	if(para ==NULL || para->mcu_app_para == NULL)
		return EPDK_FAIL;

	audio_para = &para->mcu_app_para->aux_para.g_audio_para;

	data_type = AUDIO_DATA_MUTE;
	data_len = 1;
	//4byte data type
	data_pkg[i++] = (__u8)(data_type>>24);
	data_pkg[i++] = (__u8)(data_type>>16);
	data_pkg[i++] = (__u8)(data_type>>8);
	data_pkg[i++] = (__u8)data_type;
	//2 byte data len
	data_pkg[i++] = (__u8)(data_len>>8);
	data_pkg[i++] = (__u8)data_len;
	//1byte mute
	data_pkg[i++] = para->source_vol_para.mute;
	pkg_len = i;

	ret = comms_send_audio_set_pkg(data_pkg,pkg_len);
	if(ret == EPDK_OK)
	{
		audio_para->mute_state = para->source_vol_para.mute;
	}

	return ret;
}

//set audio general settings part: default on/of , source gain adj, volume offset on/off, phone volume sync, subwoofer on/off, subwoofer phase

//defeat on/off
__s32 ui_source_comms_set_audio_defeat(ui_source_para_t *para)
{
	__s32 ret;
	audio_status_t* audio_para = NULL;
	__u8 data_pkg[16],i=0;
	__u16 data_len = 0,pkg_len = 0;
	__u32 data_type = 0;
	
	if(para ==NULL || para->mcu_app_para == NULL)
		return EPDK_FAIL;

	audio_para = &para->mcu_app_para->aux_para.g_audio_para;

	data_type = AUDIO_DATA_DEFAULT_SWITCH;
	data_len = 1;
	//4byte data type
	data_pkg[i++] = (__u8)(data_type>>24);
	data_pkg[i++] = (__u8)(data_type>>16);
	data_pkg[i++] = (__u8)(data_type>>8);
	data_pkg[i++] = (__u8)data_type;
	//2 byte data len
	data_pkg[i++] = (__u8)(data_len>>8);
	data_pkg[i++] = (__u8)data_len;
	//1byte defeat switch
	data_pkg[i++] = para->audio_para.defeat_on_off;
	pkg_len = i;

	ret = comms_send_audio_set_pkg(data_pkg,pkg_len);
	if(ret == EPDK_OK)
	{
		audio_para->default_switch = para->audio_para.defeat_on_off;
	}

	return ret;
}

//source gain
__s32 ui_source_comms_set_audio_source_gain(ui_source_para_t *para)
{
	__s32 ret;
	audio_status_t* audio_para = NULL;
	__u8 data_pkg[32],i=0;
	__u16 data_len = 0,pkg_len = 0;
	__u32 data_type = 0;
	
	if(para ==NULL || para->mcu_app_para == NULL)
		return EPDK_FAIL;

	audio_para = &para->mcu_app_para->aux_para.g_audio_para;

	data_type = AUDIO_DATA_SOURCE_GAIN;
	data_len = 12;
	//4byte data type
	data_pkg[i++] = (__u8)(data_type>>24);
	data_pkg[i++] = (__u8)(data_type>>16);
	data_pkg[i++] = (__u8)(data_type>>8);
	data_pkg[i++] = (__u8)data_type;
	//2 byte data len
	data_pkg[i++] = (__u8)(data_len>>8);
	data_pkg[i++] = (__u8)data_len;
	//12byte source gain
	data_pkg[i++] = (__s8)para->source_gain_para.gain_para[SOURCE_GAIN_FM].gain_value;
	data_pkg[i++] = (__s8)para->source_gain_para.gain_para[SOURCE_GAIN_AM].gain_value;
	data_pkg[i++] = (__s8)para->source_gain_para.gain_para[SOURCE_GAIN_WB].gain_value;
	data_pkg[i++] = (__s8)para->source_gain_para.gain_para[SOURCE_GAIN_USB].gain_value;
	data_pkg[i++] = (__s8)para->source_gain_para.gain_para[SOURCE_GAIN_BT].gain_value;
	data_pkg[i++] = audio_para->source_gain.gain[SYS_SOURCE_DAB];//(__s8)para->source_gain_para.gain_para[SOURCE_GAIN_FM].gain_value;
	data_pkg[i++] = (__s8)para->source_gain_para.gain_para[SOURCE_GAIN_SXM].gain_value;
	data_pkg[i++] = (__s8)para->source_gain_para.gain_para[SOURCE_GAIN_AUX].gain_value;
	data_pkg[i++] = audio_para->source_gain.gain[SYS_SOURCE_CAMERA];
	data_pkg[i++] = (__s8)para->source_gain_para.gain_para[SOURCE_GAIN_PARTY].gain_value;
	data_pkg[i++] = audio_para->source_gain.gain[SYS_SOURCE_AUXIN2];
	data_pkg[i++] = (__s8)para->source_gain_para.gain_para[SOURCE_GAIN_ALL].gain_value;
	pkg_len = i;

	ret = comms_send_audio_set_pkg(data_pkg,pkg_len);
	if(ret == EPDK_OK)
	{
		audio_para->source_gain.gain[SYS_SOURCE_FM-1] = (__s8)para->source_gain_para.gain_para[SOURCE_GAIN_FM].gain_value;
		audio_para->source_gain.gain[SYS_SOURCE_AM-1] = (__s8)para->source_gain_para.gain_para[SOURCE_GAIN_AM].gain_value;
		audio_para->source_gain.gain[SYS_SOURCE_WB-1] = (__s8)para->source_gain_para.gain_para[SOURCE_GAIN_WB].gain_value;
		audio_para->source_gain.gain[SYS_SOURCE_USB-1] = (__s8)para->source_gain_para.gain_para[SOURCE_GAIN_USB].gain_value;
		audio_para->source_gain.gain[SYS_SOURCE_BT_AUDIO-1] = (__s8)para->source_gain_para.gain_para[SOURCE_GAIN_BT].gain_value;
		//data_pkg[i++] = audio_para->source_gain.gain[SYS_SOURCE_DAB];//(__s8)para->source_gain_para.gain_para[SOURCE_GAIN_FM].gain_value;
		audio_para->source_gain.gain[SYS_SOURCE_SXM-1] = (__s8)para->source_gain_para.gain_para[SOURCE_GAIN_SXM].gain_value;
		audio_para->source_gain.gain[SYS_SOURCE_AUXIN-1] = (__s8)para->source_gain_para.gain_para[SOURCE_GAIN_AUX].gain_value;
		//data_pkg[i++] = audio_para->source_gain.gain[SYS_SOURCE_CAMERA];
		audio_para->source_gain.gain[SYS_SOURCE_PARTY-1] = (__s8)para->source_gain_para.gain_para[SOURCE_GAIN_PARTY].gain_value;
		//data_pkg[i++] = audio_para->source_gain.gain[SYS_SOURCE_AUXIN2];
		audio_para->source_gain.gain[SOURCE_GAIN_GLOBAL-1] = (__s8)para->source_gain_para.gain_para[SOURCE_GAIN_ALL].gain_value;
	}

	return ret;
}

//volume offset
__s32 ui_source_comms_set_audio_volume_offset(ui_source_para_t *para)
{
	__s32 ret;
	audio_status_t* audio_para = NULL;
	__u8 data_pkg[16],i=0;
	__u16 data_len = 0,pkg_len = 0;
	__u32 data_type = 0;
	
	if(para ==NULL || para->mcu_app_para == NULL)
		return EPDK_FAIL;

	audio_para = &para->mcu_app_para->aux_para.g_audio_para;

	data_type = AUDIO_DATA_VOLUME_OFFSET_SWITCH;
	data_len = 1;
	//4byte data type
	data_pkg[i++] = (__u8)(data_type>>24);
	data_pkg[i++] = (__u8)(data_type>>16);
	data_pkg[i++] = (__u8)(data_type>>8);
	data_pkg[i++] = (__u8)data_type;
	//2 byte data len
	data_pkg[i++] = (__u8)(data_len>>8);
	data_pkg[i++] = (__u8)data_len;
	//1byte volume offset switch
	data_pkg[i++] = para->audio_para.vol_offset_on_off;
	pkg_len = i;

	ret = comms_send_audio_set_pkg(data_pkg,pkg_len);
	if(ret == EPDK_OK)
	{
		audio_para->volume_offset_switch = para->audio_para.vol_offset_on_off;
	}

	return ret;
}

//phone volume sync
__s32 ui_source_comms_set_audio_phone_volume_sync(ui_source_para_t *para)
{
	__s32 ret;
	audio_status_t* audio_para = NULL;
	__u8 data_pkg[16],i=0;
	__u16 data_len = 0,pkg_len = 0;
	__u32 data_type = 0;
	
	if(para ==NULL || para->mcu_app_para == NULL)
		return EPDK_FAIL;

	audio_para = &para->mcu_app_para->aux_para.g_audio_para;

	data_type = AUDIO_DATA_PHONE_VOL_SYNC;
	data_len = 1;
	//4byte data type
	data_pkg[i++] = (__u8)(data_type>>24);
	data_pkg[i++] = (__u8)(data_type>>16);
	data_pkg[i++] = (__u8)(data_type>>8);
	data_pkg[i++] = (__u8)data_type;
	//2 byte data len
	data_pkg[i++] = (__u8)(data_len>>8);
	data_pkg[i++] = (__u8)data_len;
	//1byte phone volume sync: 0 all 1 zone1 2 zone 2
	data_pkg[i++] = para->audio_para.phone_vol_sync;
	pkg_len = i;

	ret = comms_send_audio_set_pkg(data_pkg,pkg_len);
	if(ret == EPDK_OK)
	{
		audio_para->phone_vol_sync = para->audio_para.phone_vol_sync;
	}

	return ret;
}


//reset audio settings
__s32 ui_source_comms_set_audio_reset(ui_source_para_t *para)
{
	__s32 ret;
	audio_status_t* audio_para = NULL;
	__u8 data_pkg[16],i=0;
	__u16 data_len = 0,pkg_len = 0;
	__u32 data_type = 0;
	
	if(para ==NULL || para->mcu_app_para == NULL)
		return EPDK_FAIL;

	audio_para = &para->mcu_app_para->aux_para.g_audio_para;

	data_type = AUDIO_DATA_RESET_SETTINGS;
	data_len = 0;
	//4byte data type
	data_pkg[i++] = (__u8)(data_type>>24);
	data_pkg[i++] = (__u8)(data_type>>16);
	data_pkg[i++] = (__u8)(data_type>>8);
	data_pkg[i++] = (__u8)data_type;
	//2 byte data len
	data_pkg[i++] = (__u8)(data_len>>8);
	data_pkg[i++] = (__u8)data_len;
	//none data

	pkg_len = i;

	ret = comms_send_audio_set_pkg(data_pkg,pkg_len);
	if(ret == EPDK_OK)
	{
		
	}

	return ret;
}

//zone enable 
__s32 ui_source_comms_set_audio_zone_enable(ui_source_para_t *para)
{
	__s32 ret;
	audio_status_t* audio_para = NULL;
	__u8 data_pkg[16],i=0;
	__u16 data_len = 0,pkg_len = 0;
	__u32 data_type = 0;
	
	if(para ==NULL || para->mcu_app_para == NULL)
		return EPDK_FAIL;

	audio_para = &para->mcu_app_para->aux_para.g_audio_para;

	data_type = AUDIO_DATA_ZONES_ENABLE;
	data_len = 1;
	//4byte data type
	data_pkg[i++] = (__u8)(data_type>>24);
	data_pkg[i++] = (__u8)(data_type>>16);
	data_pkg[i++] = (__u8)(data_type>>8);
	data_pkg[i++] = (__u8)data_type;
	//2 byte data len
	data_pkg[i++] = (__u8)(data_len>>8);
	data_pkg[i++] = (__u8)data_len;
	//1byte zone enable, zone by bit
	data_pkg[i++] = (para->audio_para.zone1_on_off&0x01)|((para->audio_para.zone2_on_off<<1)&0x02);
	pkg_len = i;

	ret = comms_send_audio_set_pkg(data_pkg,pkg_len);
	if(ret == EPDK_OK)
	{
		audio_para->zones_enable = (para->audio_para.zone1_on_off&0x01)|((para->audio_para.zone2_on_off<<1)&0x02);
	}

	return ret;
	
}

//favorite zone  
__s32 ui_source_comms_set_audio_favorite_zone_enable(ui_source_para_t *para)
{
	__s32 ret;
	audio_status_t* audio_para = NULL;
	__u8 data_pkg[16],i=0;
	__u16 data_len = 0,pkg_len = 0;
	__u32 data_type = 0;
	
	if(para ==NULL || para->mcu_app_para == NULL)
		return EPDK_FAIL;

	audio_para = &para->mcu_app_para->aux_para.g_audio_para;

	data_type = AUDIO_DATA_FAVORITE_ZONE;
	data_len = 1;
	//4byte data type
	data_pkg[i++] = (__u8)(data_type>>24);
	data_pkg[i++] = (__u8)(data_type>>16);
	data_pkg[i++] = (__u8)(data_type>>8);
	data_pkg[i++] = (__u8)data_type;
	//2 byte data len
	data_pkg[i++] = (__u8)(data_len>>8);
	data_pkg[i++] = (__u8)data_len;
	//1byte favorite zone: 0 all zone 1 zone1 2 zone2
	data_pkg[i++] = para->audio_para.favorite_zone;
	pkg_len = i;

	ret = comms_send_audio_set_pkg(data_pkg,pkg_len);
	if(ret == EPDK_OK)
	{
		audio_para->favorite_zone = para->audio_para.favorite_zone;
	}

	return ret;
	
}


//below for zone's misc settings: eq, position, xover,punch eq, etc...
//set zone's eq
__s32 ui_source_comms_set_zone_audio_eq(ui_source_para_t *para)
{
	__s32 ret;
	audio_status_t* audio_para = NULL;
	__u8 data_pkg[32],i=0,j=0;
	__u16 data_len = 0,pkg_len = 0;
	__u32 data_type = 0;
	__u8 eq_type = 0,zone_index = 0;
	
	if(para ==NULL || para->mcu_app_para == NULL)
		return EPDK_FAIL;

	//check zone
	if(para->audio_zone_para.zone_index < 0 || para->audio_zone_para.zone_index > SOURCE_MENU_AUDIO_ZONE_2)
		return EPDK_FAIL;
	zone_index = para->audio_zone_para.zone_index;
	
	//check eq type
	if(para->eq_para.eq_mode < 0 || para->eq_para.eq_mode > EQ_CUSTOM)
		return EPDK_FAIL;

	audio_para = &para->mcu_app_para->aux_para.g_audio_para;

	data_type = AUDIO_DATA_EQUALIZER_ACTIVE;
	data_len = 1+1+7; //zone id + eq mode + 7band gain
	//4byte data type
	data_pkg[i++] = (__u8)(data_type>>24);
	data_pkg[i++] = (__u8)(data_type>>16);
	data_pkg[i++] = (__u8)(data_type>>8);
	data_pkg[i++] = (__u8)data_type;
	//2 byte data len
	data_pkg[i++] = (__u8)(data_len>>8);
	data_pkg[i++] = (__u8)data_len;
	//1byte zone id
	data_pkg[i++] = para->audio_zone_para.zone_index+1;
	//1byte eq mode
	if(para->eq_para.eq_mode == EQ_CUSTOM)
	{
		data_pkg[i++] = AUDIO_EQ_TYPE_CUSTOM;
		eq_type = AUDIO_EQ_TYPE_CUSTOM;
	}
	else 
	{
		data_pkg[i++] = para->eq_para.eq_mode;
		eq_type = para->eq_para.eq_mode;
	}
	//7bytes band gain
	for(j=0;j<AUDIO_EQ_BANDS;j++)
	{
		data_pkg[i++] = para->eq_para.eq_band_value[para->eq_para.eq_mode][j];
	}
	pkg_len = i;
	ret = comms_send_audio_set_pkg(data_pkg,pkg_len);
	if(ret == EPDK_OK)
	{
		audio_para->zone_status[zone_index].equalizer.eq_type = eq_type;
		if(eq_type == AUDIO_EQ_TYPE_CUSTOM)
		{
			for(j=0;j<AUDIO_EQ_BANDS;j++)
			{
				audio_para->zone_status[zone_index].equalizer.bands_gain[AUDIO_EQ_MAX_TYPE-1][j] = para->eq_para.eq_band_value[para->eq_para.eq_mode][j];
			}
		}
	}

	return ret;
}

//set zone's crossover, ps: zone 1: front, rear, subwoofer; zone 2: front only, no rear, no subwoofer
__s32 ui_source_comms_set_zone_audio_crossover(ui_source_para_t *para)

{
	__s32 ret;
	audio_status_t* audio_para = NULL;
	__u8 data_pkg[32],i=0,j=0;
	__u16 data_len = 0,pkg_len = 0;
	__u32 data_type = 0;
	__u8 zone_index = 0,position = 0,filter = 0,fc = 0,slop = 0;
	
	if(para ==NULL || para->mcu_app_para == NULL)
		return EPDK_FAIL;

	//check zone available
	if(para->audio_zone_para.zone_index < 0 || para->audio_zone_para.zone_index > SOURCE_MENU_AUDIO_ZONE_2)
		return EPDK_FAIL;
	zone_index = para->audio_zone_para.zone_index;
	
	//check position
	position = para->crossover_para.crossover_mode;
	if(zone_index == SOURCE_MENU_AUDIO_ZONE_2)
	{
		if(position != CROSSOVER_MODE_FRONT)
			return EPDK_FAIL;
	}
	else if(position < 0 || position > CROSSOVER_MODE_SUBWOOFER)
		return EPDK_FAIL;
	
	filter = para->crossover_para.crossover_mode_para[position].crossover_type;
	fc = xover_frequency_index2value(para->crossover_para.crossover_mode_para[position].crossover_freq);
	slop = para->crossover_para.crossover_mode_para[position].crossover_slope;

	audio_para = &para->mcu_app_para->aux_para.g_audio_para;

	data_type = AUDIO_DATA_CROSSOVER;
	data_len = 1+4; //zone id + position + filter + fc + slope
	//4byte data type
	data_pkg[i++] = (__u8)(data_type>>24);
	data_pkg[i++] = (__u8)(data_type>>16);
	data_pkg[i++] = (__u8)(data_type>>8);
	data_pkg[i++] = (__u8)data_type;
	//2 byte data len
	data_pkg[i++] = (__u8)(data_len>>8);
	data_pkg[i++] = (__u8)data_len;
	//1byte zone id
	data_pkg[i++] = para->audio_zone_para.zone_index+1;
	//1byte position: id = index+1
	data_pkg[i++] = position+1;
	//1byte filter
	data_pkg[i++] = filter;
	//1byte fc
	data_pkg[i++] = fc;
	//1byte slop
	data_pkg[i++] = slop;
	
	pkg_len = i;
	
	ret = comms_send_audio_set_pkg(data_pkg,pkg_len);
	if(ret == EPDK_OK)
	{
		audio_para->zone_status[zone_index].crossover[position].position = position+1;
		audio_para->zone_status[zone_index].crossover[position].filter_type = filter;
		audio_para->zone_status[zone_index].crossover[position].frequency = fc;
		audio_para->zone_status[zone_index].crossover[position].slope = slop;	
	}

	return ret;
}

//set zone's fade and balance

//set fade, zone 1 only
__s32 ui_source_comms_set_zone_audio_fade(ui_source_para_t *para)
{
	__s32 ret;
	audio_status_t* audio_para = NULL;
	__u8 data_pkg[32],i=0,j=0;
	__u16 data_len = 0,pkg_len = 0;
	__u32 data_type = 0;
	__u8 zone_index = 0;
	__s8 value = 0;
	
	if(para ==NULL || para->mcu_app_para == NULL)
		return EPDK_FAIL;

	audio_para = &para->mcu_app_para->aux_para.g_audio_para;

	//check zone available
	if(para->audio_zone_para.zone_index < 0 || para->audio_zone_para.zone_index > SOURCE_MENU_AUDIO_ZONE_2)
		return EPDK_FAIL;
	zone_index = para->audio_zone_para.zone_index;
	//check value
	value = (__s8)para->fader_balance_para.fader_vaule - AUDIO_FADE_MAX;
	if(value < AUDIO_FADE_MIN || value >AUDIO_FADE_MAX)
		return EPDK_FAIL;

	data_type = AUDIO_DATA_FADE;
	data_len = 1+1; //zone id + fade value(-12~12)
	//4byte data type
	data_pkg[i++] = (__u8)(data_type>>24);
	data_pkg[i++] = (__u8)(data_type>>16);
	data_pkg[i++] = (__u8)(data_type>>8);
	data_pkg[i++] = (__u8)data_type;
	//2 byte data len
	data_pkg[i++] = (__u8)(data_len>>8);
	data_pkg[i++] = (__u8)data_len;
	//1byte zone id
	data_pkg[i++] = para->audio_zone_para.zone_index+1;
	//1byte fade value
	data_pkg[i++] = value;
	pkg_len = i;
	
	ret = comms_send_audio_set_pkg(data_pkg,pkg_len);
	if(ret == EPDK_OK)
	{
		audio_para->zone_status[zone_index].fade = value;
	}

	return ret;
}
//set balance, zone1,zone2 
__s32 ui_source_comms_set_zone_audio_balance(ui_source_para_t *para)
{
	__s32 ret;
	audio_status_t* audio_para = NULL;
	__u8 data_pkg[32],i=0,j=0;
	__u16 data_len = 0,pkg_len = 0;
	__u32 data_type = 0;
	__u8 zone_index = 0;
	__s8 value = 0;
	
	if(para ==NULL || para->mcu_app_para == NULL)
		return EPDK_FAIL;

	audio_para = &para->mcu_app_para->aux_para.g_audio_para;

	//check zone available
	if(para->audio_zone_para.zone_index < 0 || para->audio_zone_para.zone_index > SOURCE_MENU_AUDIO_ZONE_2)
		return EPDK_FAIL;
	zone_index = para->audio_zone_para.zone_index;
	//check value
	value = (__s8)para->fader_balance_para.balance_vaule - AUDIO_BALANCE_MAX;
	if(value < AUDIO_BALANCE_MIN || value >AUDIO_BALANCE_MAX)
		return EPDK_FAIL;

	data_type = AUDIO_DATA_BALANCE;
	data_len = 1+1; //zone id + balance value(-12~12)
	//4byte data type
	data_pkg[i++] = (__u8)(data_type>>24);
	data_pkg[i++] = (__u8)(data_type>>16);
	data_pkg[i++] = (__u8)(data_type>>8);
	data_pkg[i++] = (__u8)data_type;
	//2 byte data len
	data_pkg[i++] = (__u8)(data_len>>8);
	data_pkg[i++] = (__u8)data_len;
	//1byte zone id
	data_pkg[i++] = para->audio_zone_para.zone_index+1;
	//1byte fade value
	data_pkg[i++] = value;
	pkg_len = i;
	
	ret = comms_send_audio_set_pkg(data_pkg,pkg_len);
	if(ret == EPDK_OK)
	{
		audio_para->zone_status[zone_index].balance = value;
	}

	return ret;
}

//set zone's punch eq
__s32 ui_source_comms_set_zone_audio_punch_eq(ui_source_para_t *para)
{
	__s32 ret;
	audio_status_t* audio_para = NULL;
	__u8 data_pkg[32],i=0,j=0;
	__u16 data_len = 0,pkg_len = 0;
	__u32 data_type = 0;
	__u8 zone_index = 0;
	__s8 value = 0;
	
	if(para ==NULL || para->mcu_app_para == NULL)
		return EPDK_FAIL;

	audio_para = &para->mcu_app_para->aux_para.g_audio_para;

	//check zone available
	if(para->audio_zone_para.zone_index < 0 || para->audio_zone_para.zone_index > SOURCE_MENU_AUDIO_ZONE_2)
		return EPDK_FAIL;
	zone_index = para->audio_zone_para.zone_index;
	//check value
	value = (__s8)para->punch_eq_para.value;
	if(value < AUDIO_PUNCH_EQ_GAIN_MIN || value >AUDIO_PUNCH_EQ_GAIN_MAX)
		return EPDK_FAIL;
	
	data_type = AUDIO_DATA_PUNCH_EQ;
	data_len = 1+1; //zone id + punch gain(0~12)
	//4byte data type
	data_pkg[i++] = (__u8)(data_type>>24);
	data_pkg[i++] = (__u8)(data_type>>16);
	data_pkg[i++] = (__u8)(data_type>>8);
	data_pkg[i++] = (__u8)data_type;
	//2 byte data len
	data_pkg[i++] = (__u8)(data_len>>8);
	data_pkg[i++] = (__u8)data_len;
	//1byte zone id
	data_pkg[i++] = para->audio_zone_para.zone_index+1;
	//1byte punch gain value
	data_pkg[i++] = value;
	pkg_len = i;
	
	ret = comms_send_audio_set_pkg(data_pkg,pkg_len);
	if(ret == EPDK_OK)
	{
		audio_para->zone_status[zone_index].punch_gain = value;
	}

	return ret;
}


//misc
__u8 xover_frequency_value2index(__u8 value)
{
	__u8 freq_index = 0xFF; //not support
	
	if(value>=30 && value<=100)
	{
		if(!(value%10))
			freq_index = (value-30)/10; //base from 30;			
	}
	else if(value == 120)
		freq_index = CROSSOVER_ADJUST_FREQ_120HZ;
	else if(value == 150)
		freq_index = CROSSOVER_ADJUST_FREQ_150HZ;
	else if(value == 180)
		freq_index = CROSSOVER_ADJUST_FREQ_180HZ;
	else if(value == 220)
		freq_index = CROSSOVER_ADJUST_FREQ_220HZ;
	else if(value == 250)
		freq_index = CROSSOVER_ADJUST_FREQ_250HZ;
	return freq_index;
}

__u8 xover_frequency_index2value(__u8 index)
{
	__u8 freq_value =0;
	if((index >= CROSSOVER_ADJUST_FREQ_30HZ) && (index <= CROSSOVER_ADJUST_FREQ_100HZ))
		freq_value = 30 + index*10;
	else if(index == CROSSOVER_ADJUST_FREQ_120HZ)
		freq_value = 120;
	else if(index == CROSSOVER_ADJUST_FREQ_150HZ)
		freq_value = 150;
	else if(index == CROSSOVER_ADJUST_FREQ_180HZ)
		freq_value = 180;
	else if(index == CROSSOVER_ADJUST_FREQ_220HZ)
		freq_value = 220;
	else if(index == CROSSOVER_ADJUST_FREQ_250HZ)
		freq_value = 250;

	return freq_value;
}

/************************system category**************************/
__s32 comms_send_system_get_pkg(__u8 *data, __u16 data_len)
{
	return comms_send_category_get_pkg(CATEGORY_SYSTEM,data,data_len);
}

__s32 comms_send_system_set_pkg(__u8 *data, __u16 data_len)
{
	return comms_send_category_set_pkg(CATEGORY_SYSTEM,data,data_len);
}

//work source
__s32 ui_source_comms_set_work_source(__u8 source_id)
{

#if 1
	SEND_SINGLE_DATA_BLOCK(CATEGORY_SYSTEM,OPERATION_SET,SYS_DATA_SOURCE,&source_id,1);
#else
	__s32 ret;
	system_status_t* sys_para = NULL;
	__u8 data_pkg[32],i=0,j=0;
	__u16 data_len = 0,pkg_len = 0;
	__u32 data_type = 0;
	__u16 source_mask = 1;

	if(reg_app_para == NULL)
		return EPDK_FAIL;

	sys_para = &reg_app_para->system_para.g_sys_para;

	if(sys_para == NULL)
		return EPDK_FAIL;

	//check source available
	if(source_id < SYS_SOURCE_FM || source_id >= SYS_SOURCE_MAX)
		return EPDK_FAIL;

	//check source enable 
	if(!((source_mask<<(source_id-1))&sys_para->source_enable_mask))
		return EPDK_FAIL;

	data_type = SYS_DATA_SOURCE;
	data_len = 1; //source id
	//4byte data type
	data_pkg[i++] = (__u8)(data_type>>24);
	data_pkg[i++] = (__u8)(data_type>>16);
	data_pkg[i++] = (__u8)(data_type>>8);
	data_pkg[i++] = (__u8)data_type;
	//2 byte data len
	data_pkg[i++] = (__u8)(data_len>>8);
	data_pkg[i++] = (__u8)data_len;
	//1byte source id
	data_pkg[i++] = source_id;
	pkg_len = i;

	ret = comms_send_system_set_pkg(data_pkg,pkg_len);

	return ret;
#endif
}

__bool is_comms_current_work_source(__u8 src_id)
{
	if(reg_app_para == NULL || src_id <= 0)
		return EPDK_FALSE;
	if(reg_app_para->system_para.g_sys_para.current_source == src_id)
		return EPDK_TRUE;

	return EPDK_FALSE;
}

/************************Tuner mode category**************************/
static inline void write_u32_be(__u8 out[4], __u32 v)
{
    out[0] = (__u8)(v >> 24);
    out[1] = (__u8)(v >> 16);
    out[2] = (__u8)(v >> 8);
    out[3] = (__u8)v;
}

static __u8 get_current_tuner_category(void)
{
    if (reg_app_para == NULL)
        return 0;
    __u8 src = reg_app_para->system_para.g_sys_para.current_source;
    switch (src)
    {
        case SYS_SOURCE_AM: return CATEGORY_AM;
        case SYS_SOURCE_WB: return CATEGORY_WB;
        case SYS_SOURCE_FM:
        default: return CATEGORY_FM;
    }
}

// Set exact frequency (kHz) for current tuner source
__s32 ui_fm_comms_set_frequency(__u32 khz)
{
    __s32 ret;
    __u8 category;
    __u8 payload[4];
    __u32 dtype;

    if (reg_app_para == NULL)
        return EPDK_FAIL;

    category = get_current_tuner_category();
    if (category == 0)
        return EPDK_FAIL;

    write_u32_be(payload, khz);

    if (category == CATEGORY_AM)
        dtype = AM_DATA_FREQUENCY;
    else if (category == CATEGORY_WB)
        dtype = WB_DATA_FREQUENCY;
    else
        dtype = FM_DATA_FREQUENCY;

    ret = comms_send_single_data_block(category, OPERATION_SET, dtype, payload, sizeof(payload));

    if (ret == EPDK_OK)
    {
        if (category == CATEGORY_AM)
            reg_app_para->fm_para.g_am_para.frequency = khz;
        else if (category == CATEGORY_WB)
            reg_app_para->fm_para.g_wb_para.frequency = khz;
        else
            reg_app_para->fm_para.g_fm_para.frequency = khz;
    }
    return ret;
}

// Scan/seek mode control for current tuner source
// mode: 0=stop, 1=manual_up, 2=manual_down, 3=auto_up, 4=auto_down, 5=preset_up, 6=preset_down, 7=auto_store
__s32 ui_fm_comms_set_scan_mode(__u8 mode)
{
    __s32 ret;
    __u8 category;
    __u8 payload[1];
    __u32 dtype;

    if (reg_app_para == NULL)
        return EPDK_FAIL;

    category = get_current_tuner_category();
    if (category == 0)
        return EPDK_FAIL;

    payload[0] = mode;

    if (category == CATEGORY_AM)
        dtype = AM_DATA_SCAN_MODE;
    else if (category == CATEGORY_WB)
        dtype = WB_DATA_SCAN_MODE;
    else
        dtype = FM_DATA_SCAN_MODE;

    ret = comms_send_single_data_block(category, OPERATION_SET, dtype, payload, sizeof(payload));

    if (ret == EPDK_OK)
    {
        if (category == CATEGORY_AM)
            reg_app_para->fm_para.g_am_para.scan_mode = mode;
        else if (category == CATEGORY_WB)
            reg_app_para->fm_para.g_wb_para.scan_mode = mode;
        else
            reg_app_para->fm_para.g_fm_para.scan_mode = mode;
    }
    return ret;
}

// Preset operation for current tuner source
// action: 0=recall, 1=save, 2=remove; index: preset number (1-based)
__s32 ui_fm_comms_set_preset(__u8 action, __u8 index)
{
    __s32 ret;
    __u8 category;
    __u8 payload[1];
    __u32 dtype;

    if (reg_app_para == NULL || index == 0)
        return EPDK_FAIL;

    category = get_current_tuner_category();
    if (category == 0)
        return EPDK_FAIL;

    payload[0] = index;

    if (action == 0)
    {
        // recall preset
        if (category == CATEGORY_AM)
            dtype = AM_DATA_PRESET_NUMBER;
        else if (category == CATEGORY_WB)
            dtype = WB_DATA_PRESET_NUMBER;
        else
            dtype = FM_DATA_PRESET_NUMBER;
    }
    else if (action == 1)
    {
        // save current frequency to preset slot
        if (category == CATEGORY_AM)
            dtype = AM_DATA_PRESET_SAVE;
        else if (category == CATEGORY_WB)
            dtype = WB_DATA_PRESET_SAVE;
        else
            dtype = FM_DATA_PRESET_SAVE;
    }
    else if (action == 2)
    {
        // remove preset slot
        if (category == CATEGORY_AM)
            dtype = AM_DATA_PRESET_REMOVE;
        else if (category == CATEGORY_WB)
            dtype = WB_DATA_PRESET_REMOVE;
        else
            dtype = FM_DATA_PRESET_REMOVE;
    }
    else
    {
        return EPDK_FAIL;
    }

    ret = comms_send_single_data_block(category, OPERATION_SET, dtype, payload, sizeof(payload));

    if (ret == EPDK_OK)
    {
        if (category == CATEGORY_AM)
        {
            reg_app_para->fm_para.g_am_para.preset_number = (action == 2) ? 0 : index;
            if (action == 1 && index >= 1 && index <= TUNER_AM_PRESET_MAX)
            {
                reg_app_para->fm_para.g_am_para.presets[index-1].frequency = reg_app_para->fm_para.g_am_para.frequency;
            }
            if (action == 2 && index >= 1 && index <= TUNER_AM_PRESET_MAX)
            {
                reg_app_para->fm_para.g_am_para.presets[index-1].frequency = 0;
            }
        }
        else if (category == CATEGORY_WB)
        {
            reg_app_para->fm_para.g_wb_para.preset_number = (action == 2) ? 0 : index;
            if (action == 1 && index >= 1 && index <= TUNER_WB_PRESET_MAX)
            {
                reg_app_para->fm_para.g_wb_para.presets[index-1].frequency = reg_app_para->fm_para.g_wb_para.frequency;
            }
            if (action == 2 && index >= 1 && index <= TUNER_WB_PRESET_MAX)
            {
                reg_app_para->fm_para.g_wb_para.presets[index-1].frequency = 0;
            }
        }
        else
        {
            reg_app_para->fm_para.g_fm_para.preset_number = (action == 2) ? 0 : index;
            if (action == 1 && index >= 1 && index <= TUNER_FM_PRESET_MAX)
            {
                reg_app_para->fm_para.g_fm_para.presets[index-1].frequency = reg_app_para->fm_para.g_fm_para.frequency;
            }
            if (action == 2 && index >= 1 && index <= TUNER_FM_PRESET_MAX)
            {
                reg_app_para->fm_para.g_fm_para.presets[index-1].frequency = 0;
            }
        }
    }
    return ret;
}

// Local seek sensitivity switch
__s32 ui_fm_comms_set_local_seek(__u8 on_off)
{
    __s32 ret;
    __u8 category;
    __u8 payload[1];
    __u32 dtype;

    if (reg_app_para == NULL)
        return EPDK_FAIL;

    category = get_current_tuner_category();
    if (category == 0)
        return EPDK_FAIL;

    payload[0] = on_off;

    if (category == CATEGORY_AM)
        dtype = AM_DATA_LOCAL_SEEK;
    else if (category == CATEGORY_WB)
        // WB does not support this item; return immediately
        return EPDK_FAIL;
    else
        dtype = FM_DATA_LOCAL_SEEK;

    ret = comms_send_single_data_block(category, OPERATION_SET, dtype, payload, sizeof(payload));

    if (ret == EPDK_OK)
    {
        if (category == CATEGORY_AM)
            reg_app_para->fm_para.g_am_para.local_seek = on_off;
        else
            reg_app_para->fm_para.g_fm_para.local_seek = on_off;
    }
    return ret;
}

// RDS main switch (FM only). Other bands will return EPDK_FAIL.
__s32 ui_fm_comms_set_rds_switch(__u8 on_off)
{
    __s32 ret;
    __u8 category;
    __u8 payload[1];

    if (reg_app_para == NULL)
        return EPDK_FAIL;

    category = get_current_tuner_category();
    if (category != CATEGORY_FM)
        return EPDK_FAIL;

    payload[0] = on_off;
    ret = comms_send_single_data_block(CATEGORY_FM, OPERATION_SET, FM_DATA_RDS_SWITCH, payload, sizeof(payload));
    if (ret == EPDK_OK)
    {
        reg_app_para->fm_para.g_fm_para.rds_switch = on_off;
    }
    return ret;
}

// Request preset list from MCU (FM/AM/WB).
__s32 ui_fm_comms_get_preset_list(void)
{
    __u8 category;
    __u32 dtype;

    if (reg_app_para == NULL)
        return EPDK_FAIL;

    category = get_current_tuner_category();
    if (category == 0)
        return EPDK_FAIL;

    if (category == CATEGORY_AM)
        dtype = AM_DATA_PRESET_LIST;
    else if (category == CATEGORY_WB)
        dtype = WB_DATA_PRESET_LIST;
    else
        dtype = FM_DATA_PRESET_LIST;

    return comms_send_single_data_block(category, OPERATION_GET, dtype, NULL, 0);
}

// Request RDS PS/RT (FM only)
__s32 ui_fm_comms_get_rds_text(void)
{
    __s32 ret1 = EPDK_OK, ret2 = EPDK_OK;
    if (reg_app_para == NULL)
        return EPDK_FAIL;
    if (get_current_tuner_category() != CATEGORY_FM)
        return EPDK_FAIL;

    ret1 = comms_send_single_data_block(CATEGORY_FM, OPERATION_GET, FM_DATA_RDS_PS, NULL, 0);
    ret2 = comms_send_single_data_block(CATEGORY_FM, OPERATION_GET, FM_DATA_RT_TEXT, NULL, 0);
    return (ret1 == EPDK_OK && ret2 == EPDK_OK) ? EPDK_OK : EPDK_FAIL;
}

// Full synchronization: copy local driver cache into UI context (no MCU transaction)
__s32 ui_fm_comms_sync_all(void *context)
{
    if (reg_app_para == NULL || context == NULL)
        return EPDK_FAIL;

    __s32 ret1 = ui_fm_comms_sync_base_info(context);
    __s32 ret2 = ui_fm_comms_sync_list(context);
    return (ret1 == EPDK_OK && ret2 == EPDK_OK) ? EPDK_OK : EPDK_FAIL;
}

// Incremental synchronization driven by change mask: copy to UI context
__s32 ui_fm_comms_sync_dirty(void *context, __u32 change_mask)
{
    if (reg_app_para == NULL || context == NULL)
        return EPDK_FAIL;

    __u8 category = get_current_tuner_category();
    if (category == 0)
        return EPDK_FAIL;

    // If preset list changed, sync list
    if ((category == CATEGORY_AM  && (change_mask & AM_DATA_PRESET_LIST)) ||
        (category == CATEGORY_WB  && (change_mask & WB_DATA_PRESET_LIST)) ||
        (category == CATEGORY_FM  && (change_mask & FM_DATA_PRESET_LIST)))
    {
        if (ui_fm_comms_sync_list(context) != EPDK_OK)
            return EPDK_FAIL;
    }

    // For any base info change (frequency/state/scan/seek/rds switch/stereo etc.), sync base info
    if ((category == CATEGORY_AM && (change_mask & (AM_DATA_FREQUENCY | AM_DATA_TUNER_STATE | AM_DATA_PRESET_NUMBER | AM_DATA_TUNING_STEP | AM_DATA_SCAN_MODE | AM_DATA_LOCAL_SEEK))) ||
        (category == CATEGORY_WB && (change_mask & (WB_DATA_FREQUENCY | WB_DATA_TUNER_STATE | WB_DATA_PRESET_NUMBER | WB_DATA_SCAN_MODE))) ||
        (category == CATEGORY_FM && (change_mask & (FM_DATA_FREQUENCY | FM_DATA_TUNER_STATE | FM_DATA_PRESET_NUMBER | FM_DATA_TUNING_STEP | FM_DATA_SCAN_MODE | FM_DATA_STEREO_STATUS | FM_DATA_LOCAL_SEEK | FM_DATA_RDS_SWITCH | FM_DATA_RDS_PS | FM_DATA_RT_TEXT))))
    {
        if (ui_fm_comms_sync_base_info(context) != EPDK_OK)
            return EPDK_FAIL;
    }

    return EPDK_OK;
}

__s32 ui_fm_comms_sync_base_info(void *context)
{
	__s32 ret;
	__u8 i=0,j=0;
	ui_fm_data_para_t *info = NULL;

	if(reg_app_para == NULL || context == NULL)
		return EPDK_FAIL;

	info = (ui_fm_data_para_t *)context;

	if(is_comms_current_work_source(SYS_SOURCE_AM))
	{
		am_tuner_status_t *am_mcu_para = (am_tuner_status_t *)&reg_app_para->fm_para.g_am_para;
		info->frequency = am_mcu_para->frequency;
		info->preset_number = am_mcu_para->preset_number;
		info->tuner_state = am_mcu_para->tuner_state;
		info->local_seek = am_mcu_para->local_seek;
		info->scan_mode = am_mcu_para->scan_mode;
	}
	else if(is_comms_current_work_source(SYS_SOURCE_WB))
	{
		wb_tuner_status_t *wb_mcu_para = (wb_tuner_status_t *)&reg_app_para->fm_para.g_wb_para;
		info->frequency = wb_mcu_para->frequency;
		info->preset_number = wb_mcu_para->preset_number;
		info->tuner_state = wb_mcu_para->tuner_state;
		info->scan_mode = wb_mcu_para->scan_mode;
	}
	else
	{
		fm_tuner_status_t *fm_mcu_para = (fm_tuner_status_t *)&reg_app_para->fm_para.g_fm_para;
		info->frequency = fm_mcu_para->frequency;
		info->preset_number = fm_mcu_para->preset_number;
		info->tuner_state = fm_mcu_para->tuner_state;
		info->local_seek = fm_mcu_para->local_seek;
		info->rds_switch = fm_mcu_para->rds_switch;
		info->scan_mode = fm_mcu_para->scan_mode;
	}

	return EPDK_OK;
	
}

__s32 ui_fm_comms_sync_list(void *context)
{
	__s32 ret;
	__u8 i=0,j=0;
	ui_fm_data_para_t *info = NULL;

	if(reg_app_para == NULL || context == NULL)
		return EPDK_FAIL;

	info = (ui_fm_data_para_t *)context;

	if(is_comms_current_work_source(SYS_SOURCE_AM))
	{
		am_tuner_status_t *am_mcu_para = (am_tuner_status_t *)&reg_app_para->fm_para.g_am_para;
		for(i=0;i<10;i++)
			eLIBs_memcpy(&info->am_presets[i],&am_mcu_para->presets[i],sizeof(list_am_wb_t));
	}
	else if(is_comms_current_work_source(SYS_SOURCE_WB))
	{
		wb_tuner_status_t *wb_mcu_para = (wb_tuner_status_t *)&reg_app_para->fm_para.g_wb_para;
		for(i=0;i<7;i++)
			eLIBs_memcpy(&info->wb_presets[i],&wb_mcu_para->presets[i],sizeof(list_am_wb_t));
	}
	else
	{
		fm_tuner_status_t *fm_mcu_para = (fm_tuner_status_t *)&reg_app_para->fm_para.g_fm_para;
		for(i=0;i<10;i++)
			eLIBs_memcpy(&info->fm_presets[i],&fm_mcu_para->presets[i],sizeof(list_fm_t));
	}

	return EPDK_OK;
}

 /***********************USB mode category**************************/

// Helper: decide USB category based on current source or runtime flag
static __u8 get_current_usb_category(void)
{
    if (reg_app_para == NULL)
        return 0;
    // Current design: use system_para.usb_device: 0=USB Disk, 1=iPod
    // Fallback to source if needed
    __u8 dev = reg_app_para->music_para.usb_device;
    if (dev == 1) return CATEGORY_USB_IPOD;
    return CATEGORY_USB_DISK;
}

// Sync connection/reading/ready/error status to UI
__s32 ui_usb_comms_sync_status(void *context)
{
    if (reg_app_para == NULL || context == NULL)
        return EPDK_FAIL;
    // context should be ui_music_para_t compatible subset; keep generic to UI_MUSIC
    // The UI side can query reg_app_para->music_para.g_usb_*_para directly; no extra copy here
    return EPDK_OK;
}

/**
 * @brief Sync USB track information to UI context
 * @param context UI application context
 * @return EPDK_OK on success, EPDK_FAIL on failure
 */
__s32 ui_usb_comms_sync_track_info(void *context)
{
    if (reg_app_para == NULL || context == NULL)
        return EPDK_FAIL;
    
    // Sync current track index and total tracks from reg_app_para to UI context
    // Implementation depends on specific UI context structure
    // Example: Copy track info from reg_app_para->music_para.g_usb_*_para to context
    
    return EPDK_OK;
}

/**
 * @brief Sync USB play time information to UI context
 * @param context UI application context
 * @return EPDK_OK on success, EPDK_FAIL on failure
 */
__s32 ui_usb_comms_sync_play_time(void *context)
{
    if (reg_app_para == NULL || context == NULL)
        return EPDK_FAIL;
    
    // Sync current play time and total time from reg_app_para to UI context
    // Implementation depends on specific UI context structure
    
    return EPDK_OK;
}

/**
 * @brief Sync USB play state to UI context
 * @param context UI application context
 * @return EPDK_OK on success, EPDK_FAIL on failure
 */
__s32 ui_usb_comms_sync_play_state(void *context)
{
    if (reg_app_para == NULL || context == NULL)
        return EPDK_FAIL;
    
    // Sync play/pause/stop state from reg_app_para to UI context
    // Implementation depends on specific UI context structure
    
    return EPDK_OK;
}

/**
 * @brief Sync USB play mode (repeat/shuffle) to UI context
 * @param context UI application context
 * @return EPDK_OK on success, EPDK_FAIL on failure
 */
__s32 ui_usb_comms_sync_play_mode(void *context)
{
    if (reg_app_para == NULL || context == NULL)
        return EPDK_FAIL;
    
    // Sync repeat mode and shuffle mode from reg_app_para to UI context
    // Implementation depends on specific UI context structure
    
    return EPDK_OK;
}

/**
 * @brief Sync USB browser list to UI context
 * @param context UI application context
 * @return EPDK_OK on success, EPDK_FAIL on failure
 */
__s32 ui_usb_comms_sync_browser_list(void *context)
{
    if (reg_app_para == NULL || context == NULL)
        return EPDK_FAIL;
    
    // Sync browser list data from reg_app_para to UI context
    // Implementation depends on specific UI context structure
    
    return EPDK_OK;
}

// Playback info (track index/total) GET
__s32 ui_usb_comms_get_track_info(void)
{
    __u8 category = get_current_usb_category();
    __u32 dtype = (category == CATEGORY_USB_IPOD) ? USB_IPOD_DATA_TRACK_INFO : USB_DISK_DATA_TRACK_INFO;
    if (category == 0) return EPDK_FAIL;
    return comms_send_single_data_block(category, OPERATION_GET, dtype, NULL, 0);
}
// Play time GET
__s32 ui_usb_comms_get_play_time(void)
{
    __u8 category = get_current_usb_category();
    __u32 dtype = (category == CATEGORY_USB_IPOD) ? USB_IPOD_DATA_PLAY_TIME : USB_DISK_DATA_PLAY_TIME;
    if (category == 0) return EPDK_FAIL;
    return comms_send_single_data_block(category, OPERATION_GET, dtype, NULL, 0);
}

// ID3 text GET (title/artist/album combined use metadata when available)
__s32 ui_usb_comms_get_id3_metadata(void)
{
    __u8 category = get_current_usb_category();
    __u32 dtype = (category == CATEGORY_USB_IPOD) ? USB_IPOD_DATA_ID3_METADATA : USB_DISK_DATA_ID3_METADATA;
    if (category == 0) return EPDK_FAIL;
    return comms_send_single_data_block(category, OPERATION_GET, dtype, NULL, 0);
}

// Album art GET
__s32 ui_usb_comms_get_album_art(void)
{
    __u8 category = get_current_usb_category();
    __u32 dtype = (category == CATEGORY_USB_IPOD) ? USB_IPOD_DATA_ALBUM_ART : USB_DISK_DATA_ALBUM_ART;
    if (category == 0) return EPDK_FAIL;
    return comms_send_single_data_block(category, OPERATION_GET, dtype, NULL, 0);
}

// Play/Pause toggle: state 0=stop,1=play,2=pause (follow protocol)
__s32 ui_usb_comms_set_play_state(__u8 state)
{
    __u8 category = get_current_usb_category();
    __u32 dtype = (category == CATEGORY_USB_IPOD) ? USB_IPOD_DATA_PLAY_STATE : USB_DISK_DATA_PLAY_STATE;
    if (category == 0) return EPDK_FAIL;
    return comms_send_single_data_block(category, OPERATION_SET, dtype, &state, 1);
}

// Next/Prev
__s32 ui_usb_comms_next_track(void)
{
    __u8 category = get_current_usb_category();
    __u32 dtype = (category == CATEGORY_USB_IPOD) ? USB_IPOD_DATA_TRACK_NEXT : USB_DISK_DATA_TRACK_NEXT;
    if (category == 0) return EPDK_FAIL;
    return comms_send_single_data_block(category, OPERATION_SET, dtype, NULL, 0);
}

__s32 ui_usb_comms_prev_track(void)
{
    __u8 category = get_current_usb_category();
    __u32 dtype = (category == CATEGORY_USB_IPOD) ? USB_IPOD_DATA_TRACK_PREV : USB_DISK_DATA_TRACK_PREV;
    if (category == 0) return EPDK_FAIL;
    return comms_send_single_data_block(category, OPERATION_SET, dtype, NULL, 0);
}

// Fast forward / backward press (value 1=start, 0=stop) or simple trigger
__s32 ui_usb_comms_fast_forward(__u8 action)
{
    __u8 category = get_current_usb_category();
    __u32 dtype = (category == CATEGORY_USB_IPOD) ? USB_IPOD_DATA_FAST_FORWARD : USB_DISK_DATA_FAST_FORWARD;
    if (category == 0) return EPDK_FAIL;
    return comms_send_single_data_block(category, OPERATION_SET, dtype, &action, 1);
}

__s32 ui_usb_comms_fast_backward(__u8 action)
{
    __u8 category = get_current_usb_category();
    __u32 dtype = (category == CATEGORY_USB_IPOD) ? USB_IPOD_DATA_FAST_BACKWARD : USB_DISK_DATA_FAST_BACKWARD;
    if (category == 0) return EPDK_FAIL;
    return comms_send_single_data_block(category, OPERATION_SET, dtype, &action, 1);
}

// Time jump (ms or seconds per protocol; assume seconds here, encoder side adapts)
__s32 ui_usb_comms_time_jump(__u32 seconds)
{
    __u8 category = get_current_usb_category();
    __u8 payload[4];
    payload[0] = (__u8)(seconds >> 24);
    payload[1] = (__u8)(seconds >> 16);
    payload[2] = (__u8)(seconds >> 8);
    payload[3] = (__u8)seconds;
    __u32 dtype = (category == CATEGORY_USB_IPOD) ? USB_IPOD_DATA_TIME_JUMP : USB_DISK_DATA_TIME_JUMP;
    if (category == 0) return EPDK_FAIL;
    return comms_send_single_data_block(category, OPERATION_SET, dtype, payload, 4);
}

// Play specified track index (1-based)
__s32 ui_usb_comms_play_track_index(__u32 index)
{
    __u8 category = get_current_usb_category();
    __u8 payload[4];
    payload[0] = (__u8)(index >> 24);
    payload[1] = (__u8)(index >> 16);
    payload[2] = (__u8)(index >> 8);
    payload[3] = (__u8)index;
    __u32 dtype = (category == CATEGORY_USB_IPOD) ? USB_IPOD_DATA_PLAY_TRACK_INDEX : USB_DISK_DATA_PLAY_TRACK_INDEX;
    if (category == 0) return EPDK_FAIL;
    return comms_send_single_data_block(category, OPERATION_SET, dtype, payload, 4);
}

// Repeat / Shuffle
__s32 ui_usb_comms_set_repeat_mode(__u8 mode)
{
    __u8 category = get_current_usb_category();
    __u32 dtype = (category == CATEGORY_USB_IPOD) ? USB_IPOD_DATA_REPEAT_MODE : USB_DISK_DATA_REPEAT_MODE;
    if (category == 0) return EPDK_FAIL;
    return comms_send_single_data_block(category, OPERATION_SET, dtype, &mode, 1);
}

__s32 ui_usb_comms_set_shuffle_mode(__u8 mode)
{
    __u8 category = get_current_usb_category();
    __u32 dtype = (category == CATEGORY_USB_IPOD) ? USB_IPOD_DATA_SHUFFLE_MODE : USB_DISK_DATA_SHUFFLE_MODE;
    if (category == 0) return EPDK_FAIL;
    return comms_send_single_data_block(category, OPERATION_SET, dtype, &mode, 1);
}

// Browser: open mode/get list/select/back
__s32 ui_usb_comms_browse_set_mode(__u8 mode)
{
    __u8 category = get_current_usb_category();
    __u32 dtype = (category == CATEGORY_USB_IPOD) ? USB_IPOD_DATA_BROWSER_MODE : USB_DISK_DATA_BROWSER_MODE;
    if (category == 0) return EPDK_FAIL;
    return comms_send_single_data_block(category, OPERATION_SET, dtype, &mode, 1);
}

__s32 ui_usb_comms_browse_get_list(void)
{
    __u8 category = get_current_usb_category();
    __u32 dtype = (category == CATEGORY_USB_IPOD) ? USB_IPOD_DATA_BROWSER_LIST : USB_DISK_DATA_BROWSER_LIST;
    if (category == 0) return EPDK_FAIL;
    return comms_send_single_data_block(category, OPERATION_GET, dtype, NULL, 0);
}


// Full synchronization: USB local cache to UI context (no MCU transaction)
__s32 ui_usb_comms_sync_all(void *context)
{
    if (reg_app_para == NULL)
        return EPDK_FAIL;
    // If UI needs a copy, extend ui_usb_comms_sync_status to perform memcpy using context
    return ui_usb_comms_sync_status(context);
}

/************************party mode category**************************/
__s32 comms_send_party_get_pkg(__u8 *data, __u16 data_len)
{
	return comms_send_category_get_pkg(CATEGORY_BT_PARTY,data,data_len);
}

__s32 comms_send_party_set_pkg(__u8 *data, __u16 data_len)
{
	return comms_send_category_set_pkg(CATEGORY_BT_PARTY,data,data_len);
}

//set command to mcu
//get init info by active
__s32 ui_party_comms_get_init_status(void)
{
	__s32 ret;
	__u8 data_pkg[32],i=0,j=0;
	__u16 data_len = 0,pkg_len = 0;
	__u32 data_type = 0;
	party_host_status_t* party_para = NULL;

	if(reg_app_para == NULL)
		return EPDK_FAIL;

	party_para = &reg_app_para->bluetooth_para.g_party_para;

	if(party_para->device_role == PARTY_D_ROLE_UNKNOWN) //unknown get own info
		data_type = PARTY_DATA_DEVICE_ROLE|PARTY_DATA_DEVICE_NAME|PARTY_DATA_DEVICE_ADDRESS;
	else if(party_para->device_role == PARTY_D_ROLE_BROADCAST) //broadcast to source
		data_type = PARTY_DATA_AUDIO_SOURCE;
	else if(party_para->device_role == PARTY_D_ROLE_RECEIVER) //receiver to known list
		data_type = PARTY_DATA_CONNECTION_STATUS|PARTY_DATA_CONNECTED_BROADCASTER|PARTY_DATA_KNOWN_DEVICE_LIST;
	else
		return EPDK_FAIL;
	data_len = 0; 
	//4byte data type
	data_pkg[i++] = (__u8)(data_type>>24);
	data_pkg[i++] = (__u8)(data_type>>16);
	data_pkg[i++] = (__u8)(data_type>>8);
	data_pkg[i++] = (__u8)data_type;
	//2 byte data len
	data_pkg[i++] = (__u8)(data_len>>8);
	data_pkg[i++] = (__u8)data_len;
	pkg_len = i;

	ret = comms_send_party_get_pkg(data_pkg,pkg_len);

	return ret;
}

//get own info
__s32 ui_party_comms_get_own_info(void)
{
	__s32 ret;
	__u8 data_pkg[32],i=0,j=0;
	__u16 data_len = 0,pkg_len = 0;
	__u32 data_type = 0;
	party_host_status_t* party_para = NULL;

	if(reg_app_para == NULL)
		return EPDK_FAIL;

	party_para = &reg_app_para->bluetooth_para.g_party_para;

	data_type = PARTY_DATA_DEVICE_ROLE|PARTY_DATA_DEVICE_NAME|PARTY_DATA_DEVICE_ADDRESS;
	data_len = 0; 
	//4byte data type
	data_pkg[i++] = (__u8)(data_type>>24);
	data_pkg[i++] = (__u8)(data_type>>16);
	data_pkg[i++] = (__u8)(data_type>>8);
	data_pkg[i++] = (__u8)data_type;
	//2 byte data len
	data_pkg[i++] = (__u8)(data_len>>8);
	data_pkg[i++] = (__u8)data_len;
	pkg_len = i;

	ret = comms_send_party_get_pkg(data_pkg,pkg_len);

	return ret;
}

//get broadcast info or receive info by role
__s32 ui_party_comms_get_role_device_info(void)
{
	__s32 ret;
	__u8 data_pkg[32],i=0,j=0;
	__u16 data_len = 0,pkg_len = 0;
	__u32 data_type = 0;
	party_host_status_t* party_para = NULL;

	if(reg_app_para == NULL)
		return EPDK_FAIL;

	party_para = &reg_app_para->bluetooth_para.g_party_para;

	if(party_para->device_role == PARTY_D_ROLE_RECEIVER)
		data_type = PARTY_DATA_CONNECTION_STATUS|PARTY_DATA_CONNECTED_BROADCASTER|PARTY_DATA_KNOWN_DEVICE_LIST;
	else
		data_type = PARTY_DATA_AUDIO_SOURCE;
	data_len = 0; 
	//4byte data type
	data_pkg[i++] = (__u8)(data_type>>24);
	data_pkg[i++] = (__u8)(data_type>>16);
	data_pkg[i++] = (__u8)(data_type>>8);
	data_pkg[i++] = (__u8)data_type;
	//2 byte data len
	data_pkg[i++] = (__u8)(data_len>>8);
	data_pkg[i++] = (__u8)data_len;
	pkg_len = i;

	ret = comms_send_party_get_pkg(data_pkg,pkg_len);

	return ret;
}

//get known list
__s32 ui_party_comms_get_rc_known_device_list(void)
{
	__s32 ret;
	__u8 data_pkg[32],i=0,j=0;
	__u16 data_len = 0,pkg_len = 0;
	__u32 data_type = 0;
	party_host_status_t* party_para = NULL;

	if(reg_app_para == NULL)
		return EPDK_FAIL;

	party_para = &reg_app_para->bluetooth_para.g_party_para;

	data_type = PARTY_DATA_KNOWN_DEVICE_LIST;
	data_len = 0; 
	//4byte data type
	data_pkg[i++] = (__u8)(data_type>>24);
	data_pkg[i++] = (__u8)(data_type>>16);
	data_pkg[i++] = (__u8)(data_type>>8);
	data_pkg[i++] = (__u8)data_type;
	//2 byte data len
	data_pkg[i++] = (__u8)(data_len>>8);
	data_pkg[i++] = (__u8)data_len;
	pkg_len = i;

	ret = comms_send_party_get_pkg(data_pkg,pkg_len);

	return ret;
}

//change role
__s32 ui_party_comms_set_device_role(void *context,__u8 target_role)
{
	__s32 ret;
	__u8 data_pkg[32],i=0,j=0;
	__u16 data_len = 0,pkg_len = 0;
	__u32 data_type = 0;
	party_info_t *info = NULL;
	party_host_status_t* party_para = NULL;

	if(reg_app_para == NULL || context == NULL)
		return EPDK_FAIL;

	party_para = &reg_app_para->bluetooth_para.g_party_para;
	info = (party_info_t *)context;

	data_type = PARTY_DATA_DEVICE_ROLE;
	data_len = 1; //source id
	//4byte data type
	data_pkg[i++] = (__u8)(data_type>>24);
	data_pkg[i++] = (__u8)(data_type>>16);
	data_pkg[i++] = (__u8)(data_type>>8);
	data_pkg[i++] = (__u8)data_type;
	//2 byte data len
	data_pkg[i++] = (__u8)(data_len>>8);
	data_pkg[i++] = (__u8)data_len;
	//1byte role target
	data_pkg[i++] = target_role;
	pkg_len = i;

	ret = comms_send_party_set_pkg(data_pkg,pkg_len);

	//if(ret == EPDK_OK)
	//{
	//	party_para->device_role = target_role;
	//	if(info->role != target_role)
	//		info->role = target_role;
	//}

	return ret;
}

//set broadcast source
__s32 ui_party_comms_set_bc_source(void *context,__u8 target_source)
{
	__s32 ret;
	__u8 data_pkg[32],i=0,j=0;
	__u16 data_len = 0,pkg_len = 0;
	__u32 data_type = 0;
	party_info_t *info = NULL;
	party_host_status_t* party_para = NULL;

	if(reg_app_para == NULL || context == NULL)
		return EPDK_FAIL;

	party_para = &reg_app_para->bluetooth_para.g_party_para;
	info = (party_info_t *)context;

	if(party_para->device_role == PARTY_D_ROLE_RECEIVER)
		return EPDK_FAIL;

	data_type = PARTY_DATA_AUDIO_SOURCE;
	data_len = 1; //source id
	//4byte data type
	data_pkg[i++] = (__u8)(data_type>>24);
	data_pkg[i++] = (__u8)(data_type>>16);
	data_pkg[i++] = (__u8)(data_type>>8);
	data_pkg[i++] = (__u8)data_type;
	//2 byte data len
	data_pkg[i++] = (__u8)(data_len>>8);
	data_pkg[i++] = (__u8)data_len;
	//1byte source id
	data_pkg[i++] = target_source;
	pkg_len = i;

	ret = comms_send_party_set_pkg(data_pkg,pkg_len);

	return ret;
}

//broadcasting start/stop control acton 0:stop 1:start
__s32 ui_party_comms_set_bc_casting_active(void *context,__u8 action)
{
	__s32 ret;
	__u8 data_pkg[32],i=0,j=0;
	__u16 data_len = 0,pkg_len = 0;
	__u32 data_type = 0;
	party_info_t *info = NULL;
	party_host_status_t* party_para = NULL;

	if(reg_app_para == NULL || context == NULL)
		return EPDK_FAIL;

	party_para = &reg_app_para->bluetooth_para.g_party_para;
	info = (party_info_t *)context;

	if(party_para->device_role == PARTY_D_ROLE_RECEIVER)
		return EPDK_FAIL;

	data_type = PARTY_DATA_BROADCAST_CONTROL;
	data_len = 1; //action
	//4byte data type
	data_pkg[i++] = (__u8)(data_type>>24);
	data_pkg[i++] = (__u8)(data_type>>16);
	data_pkg[i++] = (__u8)(data_type>>8);
	data_pkg[i++] = (__u8)data_type;
	//2 byte data len
	data_pkg[i++] = (__u8)(data_len>>8);
	data_pkg[i++] = (__u8)data_len;
	//1byte action
	data_pkg[i++] = action;
	pkg_len = i;

	ret = comms_send_party_set_pkg(data_pkg,pkg_len);

	return ret;
}

//set scan control  0:stop 1:start
__s32 ui_party_comms_set_rc_scan_control(void *context,__u8 action)
{
	__s32 ret;
	__u8 data_pkg[32],i=0,j=0;
	__u16 data_len = 0,pkg_len = 0;
	__u32 data_type = 0;
	party_info_t *info = NULL;
	party_host_status_t* party_para = NULL;

	if(reg_app_para == NULL || context == NULL)
		return EPDK_FAIL;

	party_para = &reg_app_para->bluetooth_para.g_party_para;
	info = (party_info_t *)context;

	if(party_para->device_role == PARTY_D_ROLE_BROADCAST)
		return EPDK_FAIL;

	data_type = PARTY_DATA_SCAN_CONTROL;
	data_len = 1; //action
	//4byte data type
	data_pkg[i++] = (__u8)(data_type>>24);
	data_pkg[i++] = (__u8)(data_type>>16);
	data_pkg[i++] = (__u8)(data_type>>8);
	data_pkg[i++] = (__u8)data_type;
	// 2 byte data len
	data_pkg[i++] = (__u8)(data_len>>8);
	data_pkg[i++] = (__u8)data_len;
	//1byte action
	data_pkg[i++] = action;
	pkg_len = i;

	ret = comms_send_party_set_pkg(data_pkg,pkg_len);

	if(ret == EPDK_OK)
	{
		//clear scan list
		if(action == PARTY_SCAN_START)
		{
			//clear list data
			for(j=0;j<info->rc_info.scan_count;j++)
			{
				//local ui
				memset(	info->rc_info.scan_list[j].name,0,32);
				memset(	info->rc_info.scan_list[j].address,0,6);
				//mcu para
				memset(party_para->receiver_status.scan_device_list[j].device_name,0,16+1);
				memset(party_para->receiver_status.scan_device_list[j].device_bda,0,6);
			}
			//clear count
			info->rc_info.scan_count = 0;
			party_para->receiver_status.scan_device_count = 0;
		}
	}
	return ret;
}



//set connect/disconnect device control  0:disconnect 1:connect
__s32 ui_party_comms_set_rc_connect_control(void *context,__u8 *address,__u8 action)
{
	__s32 ret;
	__u8 data_pkg[32],i=0,j=0;
	__u16 data_len = 0,pkg_len = 0;
	__u32 data_type = 0;
	party_info_t *info = NULL;
	party_host_status_t* party_para = NULL;

	if(reg_app_para == NULL || context == NULL)
		return EPDK_FAIL;

	party_para = &reg_app_para->bluetooth_para.g_party_para;
	info = (party_info_t *)context;

	if(party_para->device_role == PARTY_D_ROLE_BROADCAST)
		return EPDK_FAIL;

	data_type = PARTY_DATA_CONNECT_CONTROL;
	data_len = 1+6; //action+BDA
	//4byte data type
	data_pkg[i++] = (__u8)(data_type>>24);
	data_pkg[i++] = (__u8)(data_type>>16);
	data_pkg[i++] = (__u8)(data_type>>8);
	data_pkg[i++] = (__u8)data_type;
	//2 byte data len
	data_pkg[i++] = (__u8)(data_len>>8);
	data_pkg[i++] = (__u8)data_len;
	//1byte action
	data_pkg[i++] = action;
	//6byte BDA
	for(j=0;j<6;j++)
		data_pkg[i++] = *(address+j);
	pkg_len = i;

	ret = comms_send_party_set_pkg(data_pkg,pkg_len);

	return ret;
}

//set delete one device from known device list: item id(1~max)
__s32 ui_party_comms_set_rc_delete_known_device(void *context,__u8 *address,__u8 item_id)
{
	__s32 ret;
	__u8 data_pkg[32],i=0,j=0;
	__u16 data_len = 0,pkg_len = 0;
	__u32 data_type = 0;
	party_info_t *info = NULL;
	party_host_status_t* party_para = NULL;

	if(reg_app_para == NULL || context == NULL)
		return EPDK_FAIL;

	party_para = &reg_app_para->bluetooth_para.g_party_para;
	info = (party_info_t *)context;

	if(party_para->device_role == PARTY_D_ROLE_BROADCAST)
		return EPDK_FAIL;

	data_type = PARTY_DATA_KNOWN_DEVICE_DELETE;
	data_len = 1+6; //item ID+BDA
	//4byte data type
	data_pkg[i++] = (__u8)(data_type>>24);
	data_pkg[i++] = (__u8)(data_type>>16);
	data_pkg[i++] = (__u8)(data_type>>8);
	data_pkg[i++] = (__u8)data_type;
	//2 byte data len
	data_pkg[i++] = (__u8)(data_len>>8);
	data_pkg[i++] = (__u8)data_len;
	//1byte action
	data_pkg[i++] = item_id;
	//6byte BDA
	for(j=0;j<6;j++)
		data_pkg[i++] = *(address+j);
	pkg_len = i;

	ret = comms_send_party_set_pkg(data_pkg,pkg_len);

	if(ret == EPDK_OK)
	{//reorder
	    if(info->rc_info.known_count > 0)
	    {
    		info->rc_info.known_count--;
    		party_para->receiver_status.known_device_count = info->rc_info.known_count;
    		//id ot index
    		j = item_id-1;
    		for(i=j;i<info->rc_info.known_count;i++)
    		{
    			//local
    			eLIBs_memcpy(info->rc_info.known_list[i].name,info->rc_info.known_list[i+1].name,32);
    			eLIBs_memcpy(info->rc_info.known_list[i].address,info->rc_info.known_list[i+1].address,6);
    			//mcu
    			eLIBs_memcpy(party_para->receiver_status.known_device_list[i].device_name,party_para->receiver_status.known_device_list[i+1].device_name,16+1);
    			eLIBs_memcpy(party_para->receiver_status.known_device_list[i].device_bda,party_para->receiver_status.known_device_list[i+1].device_bda,6);
    		}
	    }
	}

	return ret;
}

//data sync to local ui
__s32 ui_party_comms_sync_party_own_info(void *context)
{
	__s32 ret;
	__u8 i=0,j=0;
	party_info_t *info = NULL;
	party_host_status_t* party_para = NULL;

	if(reg_app_para == NULL || context == NULL)
		return EPDK_FAIL;

	party_para = &reg_app_para->bluetooth_para.g_party_para;
	info = (party_info_t *)context;

	//role
	info->role = party_para->device_role;
	//name
	if(party_para->device_name[0])
		eLIBs_strcpy(info->own_info.name,party_para->device_name);
	//address
	eLIBs_memcpy(info->own_info.address,party_para->device_bda,6);

	if(party_para->device_role == PARTY_D_ROLE_BROADCAST)
	{
		info->bc_info.broadcasting_active = party_para->broadcaster_status.broadcasting_active;
		info->bc_info.source = party_para->broadcaster_status.current_source;
	}
	else if(party_para->device_role == PARTY_D_ROLE_RECEIVER)
	{
		//connect status
		info->rc_info.connect_status = party_para->receiver_status.connection_status;
		//connected device
		eLIBs_memcpy(info->rc_info.connected_device.name,party_para->receiver_status.connected_broadcaster_name,16+1);
		eLIBs_memcpy(info->rc_info.connected_device.address,party_para->receiver_status.connected_broadcaster_bda,6);
		//know list
		info->rc_info.known_count = party_para->receiver_status.known_device_count;
		if(party_para->receiver_status.known_device_count>0 && party_para->receiver_status.known_device_count<=PARTY_MAX_DISCOVERED_DEVICES)
		{
			for(i=0;i<party_para->receiver_status.known_device_count;i++)
			{
				eLIBs_memcpy(info->rc_info.known_list[i].name,party_para->receiver_status.known_device_list[i].device_name,16+1);
				eLIBs_memcpy(info->rc_info.known_list[i].address,party_para->receiver_status.known_device_list[i].device_bda,6);
                if(eLIBs_memcmp(info->rc_info.connected_device.address, info->rc_info.known_list[i].address, sizeof(info->rc_info.connected_device.address)) != EPDK_NO)
                {
                    eLIBs_printf("connected_device[%d]:%s\n",info->rc_info.connect_status,info->rc_info.connected_device.name);
                    info->rc_info.known_list[i].connect_status = 1;
                }
                else
                {
                    info->rc_info.known_list[i].connect_status = 0;
                }
			}
		}
	}

	return EPDK_OK;
}

__s32 ui_party_comms_sync_broadcast_info(void *context)
{
	__s32 ret;
	party_info_t *info = NULL;
	party_host_status_t* party_para = NULL;

	if(reg_app_para == NULL || context == NULL)
		return EPDK_FAIL;

	party_para = &reg_app_para->bluetooth_para.g_party_para;
	info = (party_info_t *)context;

	//broadcast active
	info->bc_info.broadcasting_active = party_para->broadcaster_status.broadcasting_active;
	//source
	info->bc_info.source = party_para->broadcaster_status.current_source;
	//playback info: TODO

	return EPDK_OK;
}

__s32 ui_party_comms_sync_broadcasting_active(void *context)
{
	party_info_t *info = NULL;
	party_host_status_t* party_para = NULL;

	if(reg_app_para == NULL || context == NULL)
		return EPDK_FAIL;

	party_para = &reg_app_para->bluetooth_para.g_party_para;
	info = (party_info_t *)context;

	info->bc_info.broadcasting_active = party_para->broadcaster_status.broadcasting_active;

	return EPDK_OK;
}

__s32 ui_party_comms_sync_receiver_connect_device(void *context)
{
	party_info_t *info = NULL;
	party_host_status_t* party_para = NULL;

	if(reg_app_para == NULL || context == NULL)
		return EPDK_FAIL;

	party_para = &reg_app_para->bluetooth_para.g_party_para;
	info = (party_info_t *)context;

	//connect status
	info->rc_info.connect_status = party_para->receiver_status.connection_status;
	//connect broadcast device
	eLIBs_memcpy(info->rc_info.connected_device.name,party_para->receiver_status.connected_broadcaster_name,16+1);
	eLIBs_memcpy(info->rc_info.connected_device.address,party_para->receiver_status.connected_broadcaster_bda,6);

	return EPDK_OK;
}

__s32 ui_party_comms_sync_receiver_known_list(void *context)
{
	__s32 ret;
	__u8 i=0;
	party_info_t *info = NULL;
	party_host_status_t* party_para = NULL;

	if(reg_app_para == NULL || context == NULL)
		return EPDK_FAIL;

	party_para = &reg_app_para->bluetooth_para.g_party_para;
	info = (party_info_t *)context;

	//known count
	info->rc_info.known_count = party_para->receiver_status.known_device_count;
	if(info->rc_info.known_count <= 0)
		return EPDK_FAIL;
	//know list
	for(i=0;i<info->rc_info.known_count;i++)
	{
		//name
		if(party_para->receiver_status.known_device_list[i].device_name[0])
			eLIBs_strcpy(info->rc_info.known_list[i].name,party_para->receiver_status.known_device_list[i].device_name);
		//address
		eLIBs_memcpy(info->rc_info.known_list[i].address,party_para->receiver_status.known_device_list[i].device_bda,6);
	}

	return EPDK_OK;
}

__s32 ui_party_comms_sync_receiver_scan_list(void *context)
{
	__s32 ret;
	__u8 i=0;
	party_info_t *info = NULL;
	party_host_status_t* party_para = NULL;

	if(reg_app_para == NULL || context == NULL)
		return EPDK_FAIL;

	party_para = &reg_app_para->bluetooth_para.g_party_para;
	info = (party_info_t *)context;

	//known count
	info->rc_info.scan_count = party_para->receiver_status.scan_device_count;
	if(info->rc_info.scan_count <= 0)
	{
		return EPDK_FAIL;
	}
	//know list
	for(i=0;i<info->rc_info.scan_count;i++)
	{
		//name
		if(party_para->receiver_status.scan_device_list[i].device_name[0])
			eLIBs_strcpy(info->rc_info.scan_list[i].name,party_para->receiver_status.scan_device_list[i].device_name);
		//address
		eLIBs_memcpy(info->rc_info.scan_list[i].address,party_para->receiver_status.scan_device_list[i].device_bda,6);
	}

	return EPDK_OK;
}

/***********************Settings menu category**************************/

// Beep switch GET/SET
__s32 ui_settings_comms_get_beep_switch(void)
{
    return comms_send_single_data_block(CATEGORY_SETTINGS, OPERATION_GET, SETTINGS_DATA_BEEP_SWITCH, NULL, 0);
}

__s32 ui_settings_comms_set_beep_switch(__u8 on_off)
{
    return comms_send_single_data_block(CATEGORY_SETTINGS, OPERATION_SET, SETTINGS_DATA_BEEP_SWITCH, &on_off, 1);
}

// AUX IN switch GET/SET
__s32 ui_settings_comms_get_aux_in_switch(void)
{
    return comms_send_single_data_block(CATEGORY_SETTINGS, OPERATION_GET, SETTINGS_DATA_AUX_IN_SWITCH, NULL, 0);
}

__s32 ui_settings_comms_set_aux_in_switch(__u8 on_off)
{
    return comms_send_single_data_block(CATEGORY_SETTINGS, OPERATION_SET, SETTINGS_DATA_AUX_IN_SWITCH, &on_off, 1);
}

// Device rename (first byte: target type, e.g., 0=head unit, 1=remote; rest bytes: name)
__s32 ui_settings_comms_set_device_rename(__u8 target_type, const char *name)
{
    __u8 buffer[48];
    __u16 len = 0;
    if (!name) return EPDK_FAIL;
    buffer[len++] = target_type;
    for (; len < sizeof(buffer) && name[len-1] != '\0'; ++len) {
        buffer[len] = (len-1 < 46) ? (unsigned char)name[len-1] : 0; // truncate safely
        if (buffer[len] == 0) break;
    }
    if (len < sizeof(buffer)) buffer[len++] = 0;
    return comms_send_single_data_block(CATEGORY_SETTINGS, OPERATION_SET, SETTINGS_DATA_DEVICE_RENAME, buffer, len);
}

// Source order SET（payload depends on protocol: sequence of source ids）
__s32 ui_settings_comms_set_source_order(const __u8 *order_list, __u16 count)
{
    if (!order_list || count == 0) return EPDK_FAIL;
    return comms_send_single_data_block(CATEGORY_SETTINGS, OPERATION_SET, SETTINGS_DATA_SOURCE_ORDER, (__u8 *)order_list, count);
}

// Bluetooth settings: switch/name/auto-connect
__s32 ui_settings_comms_get_bt_switch(void)
{
    return comms_send_single_data_block(CATEGORY_SETTINGS, OPERATION_GET, SETTINGS_DATA_BT_SWITCH, NULL, 0);
}

__s32 ui_settings_comms_set_bt_switch(__u8 on_off)
{
    return comms_send_single_data_block(CATEGORY_SETTINGS, OPERATION_SET, SETTINGS_DATA_BT_SWITCH, &on_off, 1);
}

__s32 ui_settings_comms_get_bt_name(void)
{
    return comms_send_single_data_block(CATEGORY_SETTINGS, OPERATION_GET, SETTINGS_DATA_BT_NAME, NULL, 0);
}

__s32 ui_settings_comms_set_bt_name(const char *name)
{
    if (!name) return EPDK_FAIL;
    __u16 len = (__u16)eLIBs_strlen(name);
    return comms_send_single_data_block(CATEGORY_SETTINGS, OPERATION_SET, SETTINGS_DATA_BT_NAME, (__u8 *)name, len);
}

__s32 ui_settings_comms_get_bt_device_list(void)
{
    return comms_send_single_data_block(CATEGORY_SETTINGS, OPERATION_GET, SETTINGS_DATA_BT_DEVICE_LIST, NULL, 0);
}

__s32 ui_settings_comms_set_bt_auto_connect(__u8 on_off)
{
    return comms_send_single_data_block(CATEGORY_SETTINGS, OPERATION_SET, SETTINGS_DATA_BT_AUTO_CONNECT, &on_off, 1);
}

// Own/Connected device info GET
__s32 ui_settings_comms_get_own_device(void)
{
    return comms_send_single_data_block(CATEGORY_SETTINGS, OPERATION_GET, SETTINGS_DATA_OWN_DEVICE, NULL, 0);
}

__s32 ui_settings_comms_get_connected_device(void)
{
    return comms_send_single_data_block(CATEGORY_SETTINGS, OPERATION_GET, SETTINGS_DATA_CONNECTED_DEVICE, NULL, 0);
}

// Firmware update triggers
__s32 ui_settings_comms_trigger_fw_update_usb(void)
{
    return comms_send_single_data_block(CATEGORY_SETTINGS, OPERATION_SET, SETTINGS_DATA_FW_UPDATE_USB, NULL, 0);
}

__s32 ui_settings_comms_trigger_fw_update_ota(void)
{
    return comms_send_single_data_block(CATEGORY_SETTINGS, OPERATION_SET, SETTINGS_DATA_FW_UPDATE_OTA, NULL, 0);
}

// Factory default
__s32 ui_settings_comms_factory_default(void)
{
    return comms_send_single_data_block(CATEGORY_SETTINGS, OPERATION_SET, SETTINGS_DATA_FACTORY_DEFAULT, NULL, 0);
}

/************************Source Switch Auto Sync*************************/

/**
 * @brief Handle automatic data synchronization when source is switched
 * @param new_source New source ID to switch to
 * @param ctx UI application context for data synchronization
 * @return EPDK_OK on success, EPDK_FAIL on error
 */
__s32 ui_source_comms_handle_source_switch(__u8 new_source, void *ctx)
{
    __s32 ret = EPDK_OK;
    
    if (ctx == NULL) {
        comms_debug("Invalid context for source switch sync\n");
        return EPDK_FAIL;
    }
    
    // Set new source to MCU first
    ret = ui_source_comms_set_work_source(new_source);
    if (ret != EPDK_OK) {
        comms_debug("Failed to set work source: %d\n", new_source);
        return ret;
    }
    
    // Trigger full data synchronization based on source type
    switch(new_source) {
        case SYS_SOURCE_FM:
        case SYS_SOURCE_AM:
        case SYS_SOURCE_WB:
            ret = ui_fm_comms_sync_all(ctx);
            break;
            
        case SYS_SOURCE_USB:
            ret = ui_usb_comms_sync_all(ctx);
            break;
            
        case SYS_SOURCE_BT_AUDIO:
            // Note: BT sync functions need to be implemented if required
            comms_debug("BT sync not yet implemented\n");
            ret = EPDK_OK; // Don't fail for unimplemented sources
            break;
            
        case SYS_SOURCE_PARTY:
            ret = ui_party_comms_sync_all(ctx);
            break;
            
        case SYS_SOURCE_SXM:
            // Note: SXM sync functions need to be implemented if required
            comms_debug("SXM sync not yet implemented\n");
            ret = EPDK_OK;
            break;
            
        case SYS_SOURCE_AUXIN:
        case SYS_SOURCE_AUXIN2:
        case SYS_SOURCE_CAMERA:
            // These sources typically don't need data synchronization
            ret = EPDK_OK;
            break;
            
        default:
            comms_debug("Unknown source for sync: %d\n", new_source);
            ret = EPDK_OK; // Don't fail for unknown sources
            break;
    }
    
    if (ret == EPDK_OK) {
        comms_debug("Source switch sync completed: %d\n", new_source);
    } else {
        comms_debug("Source switch sync failed: %d, ret=%d\n", new_source, ret);
    }
    
    return ret;
}

/**
 * @brief Handle GUI_MSG_UI_NEW_xxx_UPDATE message driven incremental sync
 * @param msg_id Message ID from driver
 * @param pending_mask Data type mask indicating which data has changed
 * @param msg_data Optional message data (not used currently)
 * @param ctx UI application context data structure
 * @return EPDK_OK on success, EPDK_FAIL on failure
 */
__s32 ui_source_comms_handle_update_message(__u32 msg_id, __u32 pending_mask, void *msg_data, void *ctx)
{
    __s32 ret = EPDK_OK;
    
    if (ctx == NULL) {
        comms_debug("Invalid context for update message sync\n");
        return EPDK_FAIL;
    }
    
    if (pending_mask == 0) {
        comms_debug("Empty pending mask, no sync needed\n");
        return EPDK_OK;
    }
    
    // Handle different update message types
    switch(msg_id) {
        case GUI_MSG_UI_NEW_TUNER_UPDATE:
        case GUI_MSG_UI_NEW_TUNER_FREQUENCY:
        case GUI_MSG_UI_NEW_TUNER_PRESET:
        case GUI_MSG_UI_NEW_TUNER_STATE:
            // Use existing FM incremental sync based on pending_mask
            ret = ui_fm_comms_sync_dirty(ctx, pending_mask);
            comms_debug("Tuner incremental sync: mask=0x%08X, ret=%d\n", pending_mask, ret);
            break;
            
        case GUI_MSG_UI_NEW_USB_DISK_UPDATE:
        case GUI_MSG_UI_NEW_USB_IPOD_UPDATE:
            // USB incremental sync (will be implemented next)
            ret = ui_usb_comms_sync_dirty(ctx, pending_mask);
            comms_debug("USB incremental sync: mask=0x%08X, ret=%d\n", pending_mask, ret);
            break;
            
        case GUI_MSG_UI_PARTY_UPDATE:
            // Party Mode incremental sync
            ret = ui_party_comms_sync_dirty(ctx, pending_mask);
            comms_debug("Party incremental sync: mask=0x%08X, ret=%d\n", pending_mask, ret);
            break;
            
        case GUI_MSG_UI_NEW_AUDIO_UPDATE:
        case GUI_MSG_UI_NEW_VOLUME_UPDATE:
            // Audio/Volume updates - these are typically handled globally
            comms_debug("Audio/Volume update: mask=0x%08X\n", pending_mask);
            ret = EPDK_OK;
            break;
            
        case GUI_MSG_UI_NEW_SETTINGS_UPDATE:
            // Settings updates
            comms_debug("Settings update: mask=0x%08X\n", pending_mask);
            ret = EPDK_OK;
            break;
            
        default:
            // Unknown message type - log but don't fail
            comms_debug("Unknown update message: id=%u, mask=0x%08X\n", msg_id, pending_mask);
            ret = EPDK_OK;
            break;
    }
    
    if (ret == EPDK_OK) {
        comms_debug("Update message sync completed: id=%u\n", msg_id);
    } else {
        comms_debug("Update message sync failed: id=%u, ret=%d\n", msg_id, ret);
    }
    
    return ret;
}

/**
 * @brief USB incremental synchronization - sync only changed data based on mask
 * @param ctx UI application context 
 * @param change_mask Data type mask indicating which USB data has changed
 * @return EPDK_OK on success, EPDK_FAIL on failure
 */
__s32 ui_usb_comms_sync_dirty(void *ctx, __u32 change_mask)
{
    __s32 ret = EPDK_OK;
    __u8 category;
    
    if (reg_app_para == NULL || ctx == NULL) {
        comms_debug("Invalid parameters for USB dirty sync\n");
        return EPDK_FAIL;
    }
    
    // Get current USB category (DISK or IPOD)
    category = get_current_usb_category();
    if (category == 0) {
        comms_debug("No valid USB category found\n");
        return EPDK_FAIL;
    }
    
    comms_debug("USB dirty sync: category=%d, mask=0x%08X\n", category, change_mask);
    
    // Handle USB DISK incremental sync
    if (category == CATEGORY_USB_DISK) {
        if (change_mask & USB_DISK_DATA_TRACK_INFO) {
            ret = ui_usb_comms_sync_track_info(ctx);
            if (ret != EPDK_OK) {
                comms_debug("Failed to sync USB DISK track info\n");
                return ret;
            }
        }
        
        if (change_mask & USB_DISK_DATA_PLAY_TIME) {
            ret = ui_usb_comms_sync_play_time(ctx);
            if (ret != EPDK_OK) {
                comms_debug("Failed to sync USB DISK play time\n");
                return ret;
            }
        }
        
        if (change_mask & USB_DISK_DATA_PLAY_STATE) {
            ret = ui_usb_comms_sync_play_state(ctx);
            if (ret != EPDK_OK) {
                comms_debug("Failed to sync USB DISK play state\n");
                return ret;
            }
        }
        
        if (change_mask & (USB_DISK_DATA_REPEAT_MODE | USB_DISK_DATA_SHUFFLE_MODE)) {
            ret = ui_usb_comms_sync_play_mode(ctx);
            if (ret != EPDK_OK) {
                comms_debug("Failed to sync USB DISK play modes\n");
                return ret;
            }
        }
        
        if (change_mask & USB_DISK_DATA_BROWSER_LIST) {
            ret = ui_usb_comms_sync_browser_list(ctx);
            if (ret != EPDK_OK) {
                comms_debug("Failed to sync USB DISK browser list\n");
                return ret;
            }
        }
    }
    // Handle USB IPOD incremental sync  
    else if (category == CATEGORY_USB_IPOD) {
        if (change_mask & USB_IPOD_DATA_TRACK_INFO) {
            ret = ui_usb_comms_sync_track_info(ctx);
            if (ret != EPDK_OK) {
                comms_debug("Failed to sync USB IPOD track info\n");
                return ret;
            }
        }
        
        if (change_mask & USB_IPOD_DATA_PLAY_TIME) {
            ret = ui_usb_comms_sync_play_time(ctx);
            if (ret != EPDK_OK) {
                comms_debug("Failed to sync USB IPOD play time\n");
                return ret;
            }
        }
        
        if (change_mask & USB_IPOD_DATA_PLAY_STATE) {
            ret = ui_usb_comms_sync_play_state(ctx);
            if (ret != EPDK_OK) {
                comms_debug("Failed to sync USB IPOD play state\n");
                return ret;
            }
        }
        
        if (change_mask & (USB_IPOD_DATA_REPEAT_MODE | USB_IPOD_DATA_SHUFFLE_MODE)) {
            ret = ui_usb_comms_sync_play_mode(ctx);
            if (ret != EPDK_OK) {
                comms_debug("Failed to sync USB IPOD play modes\n");
                return ret;
            }
        }
        
        if (change_mask & USB_IPOD_DATA_BROWSER_LIST) {
            ret = ui_usb_comms_sync_browser_list(ctx);
            if (ret != EPDK_OK) {
                comms_debug("Failed to sync USB IPOD browser list\n");
                return ret;
            }
        }
    }
    
    if (ret == EPDK_OK) {
        comms_debug("USB dirty sync completed successfully\n");
    } else {
        comms_debug("USB dirty sync failed: ret=%d\n", ret);
    }
    
    return ret;
}

/************************Party Mode Data Sync*************************/

/**
 * @brief Party Mode full synchronization - sync all party data to UI context
 * @param ctx UI application context (party_info_t *)
 * @return EPDK_OK on success, EPDK_FAIL on failure
 */
__s32 ui_party_comms_sync_all(void *ctx)
{
    __s32 ret = EPDK_OK;
    
    if (reg_app_para == NULL || ctx == NULL) {
        comms_debug("Invalid parameters for Party full sync\n");
        return EPDK_FAIL;
    }
    
    comms_debug("Starting Party Mode full sync\n");
    
    // Sync basic party information (role, name, address)
    ret = ui_party_comms_sync_party_own_info(ctx);
    if (ret != EPDK_OK) {
        comms_debug("Failed to sync party own info\n");
        return ret;
    }
    
    // Sync broadcast information if in broadcaster role
    ret = ui_party_comms_sync_broadcast_info(ctx);
    if (ret != EPDK_OK) {
        comms_debug("Failed to sync broadcast info\n");
        return ret;
    }
    
    // Sync receiver connection information
    ret = ui_party_comms_sync_receiver_connect_device(ctx);
    if (ret != EPDK_OK) {
        comms_debug("Failed to sync receiver connection info\n");
        return ret;
    }
    
    // Sync known device list
    ret = ui_party_comms_sync_receiver_known_list(ctx);
    if (ret != EPDK_OK) {
        comms_debug("Failed to sync known device list\n");
        return ret;
    }
    
    // Sync scan device list
    ret = ui_party_comms_sync_receiver_scan_list(ctx);
    if (ret != EPDK_OK) {
        comms_debug("Failed to sync scan device list\n");
        return ret;
    }
    
    comms_debug("Party Mode full sync completed successfully\n");
    return EPDK_OK;
}

/**
 * @brief Party Mode incremental synchronization - sync only changed data based on mask
 * @param ctx UI application context (party_info_t *)
 * @param change_mask Data type mask indicating which party data has changed
 * @return EPDK_OK on success, EPDK_FAIL on failure
 */
__s32 ui_party_comms_sync_dirty(void *ctx, __u32 change_mask)
{
    __s32 ret = EPDK_OK;
    
    if (reg_app_para == NULL || ctx == NULL) {
        comms_debug("Invalid parameters for Party dirty sync\n");
        return EPDK_FAIL;
    }
    
    if (change_mask == 0) {
        comms_debug("Empty change mask for Party dirty sync\n");
        return EPDK_OK;
    }
    
    comms_debug("Party dirty sync: mask=0x%08X\n", change_mask);
    
    // Sync device role and basic info changes
    if (change_mask & (PARTY_DATA_DEVICE_ROLE | PARTY_DATA_DEVICE_NAME | PARTY_DATA_DEVICE_ADDRESS)) {
        ret = ui_party_comms_sync_party_own_info(ctx);
        if (ret != EPDK_OK) {
            comms_debug("Failed to sync party own info changes\n");
            return ret;
        }
    }
    
    // Sync broadcast status changes
    if (change_mask & (PARTY_DATA_BROADCAST_CONTROL | PARTY_DATA_AUDIO_SOURCE)) {
        ret = ui_party_comms_sync_broadcast_info(ctx);
        if (ret != EPDK_OK) {
            comms_debug("Failed to sync broadcast info changes\n");
            return ret;
        }
    }
    
    // Sync connection status changes
    if (change_mask & (PARTY_DATA_CONNECTION_STATUS | PARTY_DATA_CONNECTED_BROADCASTER)) {
        ret = ui_party_comms_sync_receiver_connect_device(ctx);
        if (ret != EPDK_OK) {
            comms_debug("Failed to sync connection status changes\n");
            return ret;
        }
    }
    
    // Sync known device list changes
    if (change_mask & (PARTY_DATA_KNOWN_DEVICE_LIST | PARTY_DATA_KNOWN_DEVICE_DELETE)) {
        ret = ui_party_comms_sync_receiver_known_list(ctx);
        if (ret != EPDK_OK) {
            comms_debug("Failed to sync known device list changes\n");
            return ret;
        }
    }
    
    // Sync scan device list changes
    if (change_mask & PARTY_DATA_SCAN_CONTROL) {
        ret = ui_party_comms_sync_receiver_scan_list(ctx);
        if (ret != EPDK_OK) {
            comms_debug("Failed to sync scan device list changes\n");
            return ret;
        }
    }
    
    comms_debug("Party dirty sync completed successfully\n");
    return EPDK_OK;
}

/* ===================== MCU Interface Functions ===================== */

/**
 * @brief Tuner full data sync implementation
 */
__s32 ui_mcu_tuner_sync_all(void *ctx)
{
    return ui_fm_comms_sync_all(ctx);
}

/**
 * @brief Tuner incremental data sync implementation (handles dirty_mask_current)
 */
__s32 ui_mcu_tuner_sync_dirty(void *ctx, __u32 dirty_mask)
{
    __s32 ret = ui_fm_comms_sync_dirty(ctx, dirty_mask);
    return ret;
}

/**
 * @brief Tuner data retrieval implementation (with optional parameter and category support)
 * @param data_type Data type to retrieve
 * @param param Optional parameter (e.g., preset index)
 * @param category Protocol category (0=auto-detect from current source, non-zero=force specific category)
 */
__s32 ui_mcu_tuner_get_data(__u32 data_type, void *param, __u8 category)
{
    // Auto-detect category if not specified
    if (category == 0) {
        category = get_current_tuner_category();
        if (category == 0)
            return EPDK_FAIL;
    }
    
    // Handle data types that need parameters
    if (param) {
        comms_debug("Tuner get_data with param: category=%d, data_type=0x%08X\n", category, data_type);
    }
    
    comms_debug("Tuner get_data: category=%d, data_type=0x%08X\n", category, data_type);
    
    // Route to existing communication functions based on data type and category
    switch (data_type) {
        // Preset list - support all categories
        case FM_DATA_PRESET_LIST:
        case AM_DATA_PRESET_LIST:
        case WB_DATA_PRESET_LIST:
            return ui_fm_comms_get_preset_list();
            
        // RDS text - FM only
        case FM_DATA_RT_TEXT:
        case FM_DATA_RDS_PS:
            if (category == CATEGORY_FM)
                return ui_fm_comms_get_rds_text();
            else
                return EPDK_FAIL; // AM/WB don't support RDS
            
        default:
            // Use the correct category (auto-detected or specified)
            return comms_send_single_data_block(category, OPERATION_GET, data_type, NULL, 0);
    }
}

/**
 * @brief Tuner data setting/control implementation (with category support)
 * @param data_type Data type to set/control
 * @param data Data payload
 * @param data_len Data length
 * @param category Protocol category (0=auto-detect from current source, non-zero=force specific category)
 */
__s32 ui_mcu_tuner_set_control(__u32 data_type, void *data, __u16 data_len, __u8 category)
{
    // Auto-detect category if not specified
    if (category == 0) {
        category = get_current_tuner_category();
        if (category == 0)
            return EPDK_FAIL;
    }
    
    comms_debug("Tuner set_control: category=%d, data_type=0x%08X, data_len=%d\n", 
               category, data_type, data_len);
    
    // Use the correct category (auto-detected or specified)
    return comms_send_single_data_block(category, OPERATION_SET, data_type, data, data_len);
}

__u8 get_current_source_category(void)
{
	if (reg_app_para == NULL)
        	return 0;
    	__u8 src = reg_app_para->system_para.g_sys_para.current_source;
	switch (src)
	{
	    case SYS_SOURCE_AM: return CATEGORY_AM;
		break;
	    case SYS_SOURCE_WB: return CATEGORY_WB;
		break;
	    case SYS_SOURCE_FM: return CATEGORY_FM;
		break;
	    case SYS_SOURCE_USB:
    		if (reg_app_para->music_para.usb_device == 1) 
			return CATEGORY_USB_IPOD;
		else
    			return CATEGORY_USB_DISK;
		break;
	     case SYS_SOURCE_BT_AUDIO: return CATEGORY_BT_AUDIO;
		break;
	    case SYS_SOURCE_DAB: return CATEGORY_DAB;
		break;
	    case SYS_SOURCE_SXM: return CATEGORY_SXM;
		break;
	    case SYS_SOURCE_AUXIN: return CATEGORY_AUX_IN;
		break;	
	    case SYS_SOURCE_CAMERA: return CATEGORY_CAMERA;
		break;
            case SYS_SOURCE_PARTY: return CATEGORY_BT_PARTY;
		break;
	    case SYS_SOURCE_AUXIN2: return CATEGORY_AUX_IN;
		break;
			
	    default: return 0;
	}
}

__s32 ui_mcu_ops_v2_get_data(__u32 data_type, __u8 *param, __u16 param_len, __u8 category)
{
    // Auto-detect category if not specified
    if (category == 0) {
        category = get_current_source_category();
        if (category == 0)
            return EPDK_FAIL;
    }
    
    // Handle data types that need parameters
    if (param) {
        comms_debug("get_data with param: category=%d, data_type=0x%08X\n", category, data_type);
    }
    
    comms_debug("get_data: category=%d, data_type=0x%08X\n", category, data_type);
    
    return comms_send_single_data_block(category, OPERATION_GET, data_type, param, param_len);
}

__s32 ui_mcu_ops_v2_set_control(__u32 data_type, void *data, __u16 data_len, __u8 category)
{
    // Auto-detect category if not specified
    if (category == 0) {
        category = get_current_source_category();
        if (category == 0)
            return EPDK_FAIL;
    }
    
    comms_debug("set_control: category=%d, data_type=0x%08X, data_len=%d\n", 
               category, data_type, data_len);
    
    // Use the correct category (auto-detected or specified)
    return comms_send_single_data_block(category, OPERATION_SET, data_type, data, data_len);
}

/* ===================== Music MCU Interface Functions ===================== */

/**
 * @brief Music full data sync implementation (USB DISK/IPOD/BT AUDIO)
 * Routes to appropriate sync function based on current source type
 */
__s32 ui_mcu_music_sync_all(void *ctx)
{
    if (!ctx || !reg_app_para) return EPDK_FAIL;
    
    __u8 current_source = reg_app_para->system_para.g_sys_para.current_source;
    comms_debug("Music sync_all: current_source=%d\n", current_source);
    
    switch (current_source) {
        case SYS_SOURCE_USB:
            if (reg_app_para->music_para.usb_device == 1)
                return ui_usb_comms_sync_all(ctx);  // USB IPOD
            else
                return ui_usb_comms_sync_all(ctx);  // USB DISK (same implementation)
                
        case SYS_SOURCE_BT_AUDIO:
            return ui_bt_audio_comms_sync_all(ctx);
            
        default:
            comms_debug("Music sync_all: unsupported source %d\n", current_source);
            return EPDK_FAIL;
    }
}

/**
 * @brief Music incremental data sync implementation
 * Routes dirty mask to appropriate sync function based on current source
 */
__s32 ui_mcu_music_sync_dirty(void *ctx, __u32 dirty_mask)
{
    if (!ctx || !reg_app_para) return EPDK_FAIL;
    
    __u8 current_source = reg_app_para->system_para.g_sys_para.current_source;
    comms_debug("Music sync_dirty: current_source=%d, mask=0x%08X\n", current_source, dirty_mask);
    
    switch (current_source) {
        case SYS_SOURCE_USB:
            if (reg_app_para->music_para.usb_device == 1)
                return ui_usb_comms_sync_dirty(ctx, dirty_mask);  // USB IPOD
            else
                return ui_usb_comms_sync_dirty(ctx, dirty_mask);  // USB DISK
                
        case SYS_SOURCE_BT_AUDIO:
            return ui_bt_audio_comms_sync_dirty(ctx, dirty_mask);
            
        default:
            comms_debug("Music sync_dirty: unsupported source %d\n", current_source);
            return EPDK_FAIL;
    }
}

/* ===================== BT Audio Sync Functions ===================== */

/**
 * @brief BT Audio full synchronization implementation
 */
__s32 ui_bt_audio_comms_sync_all(void *context)
{
    if (!reg_app_para) return EPDK_FAIL;
    
    comms_debug("BT Audio sync_all started\n");
    
    // Sync basic BT audio info: connection status, device name, etc.
    // Add your BT audio data sync logic here
    
    comms_debug("BT Audio sync_all completed\n");
    return EPDK_OK;
}

/**
 * @brief BT Audio incremental synchronization by dirty mask
 */
__s32 ui_bt_audio_comms_sync_dirty(void *context, __u32 change_mask)
{
    if (!reg_app_para || change_mask == 0) return EPDK_OK;
    
    comms_debug("BT Audio sync_dirty: mask=0x%08X\n", change_mask);
    
    // Handle specific BT Audio data types based on change_mask
    if (change_mask & BT_AUDIO_DATA_TRACK_INFO) {
        // Sync track information
        comms_debug("Syncing BT Audio track info\n");
    }
    
    if (change_mask & BT_AUDIO_DATA_PLAY_STATE) {
        // Sync play state
        comms_debug("Syncing BT Audio play state\n");
    }
    
    if (change_mask & BT_AUDIO_DATA_CONNECTION) {
        // Sync connection status
        comms_debug("Syncing BT Audio connection\n");
    }
    
    comms_debug("BT Audio sync_dirty completed\n");
    return EPDK_OK;
}

/* ===================== SXM MCU Interface Functions ===================== */

/**
 * @brief SXM full data sync implementation
 */
__s32 ui_mcu_sxm_sync_all(void *ctx)
{
    if (!ctx || !reg_app_para) return EPDK_FAIL;
    
    comms_debug("SXM sync_all started\n");
    
    // Sync SXM channel info, preset list, etc.
    // Add your SXM data sync logic here
    
    comms_debug("SXM sync_all completed\n");
    return EPDK_OK;
}

/**
 * @brief SXM incremental data sync implementation
 */
__s32 ui_mcu_sxm_sync_dirty(void *ctx, __u32 dirty_mask)
{
    if (!ctx || !reg_app_para || dirty_mask == 0) return EPDK_OK;
    
    comms_debug("SXM sync_dirty: mask=0x%08X\n", dirty_mask);
    
    // Handle specific SXM data types based on dirty_mask
    if (dirty_mask & SXM_DATA_CHANNEL_INFO) {
        comms_debug("Syncing SXM channel info\n");
    }
    
    if (dirty_mask & SXM_DATA_PRESET_LIST) {
        comms_debug("Syncing SXM preset list\n");
    }
    
    comms_debug("SXM sync_dirty completed\n");
    return EPDK_OK;
}

/* ===================== Party Mode MCU Interface Functions ===================== */

/**
 * @brief Party Mode full data sync implementation
 */
__s32 ui_mcu_party_sync_all(void *ctx)
{
    if (!ctx || !reg_app_para) return EPDK_FAIL;
    
    comms_debug("Party sync_all started\n");
    
    // Use existing party sync functions
    __s32 ret = ui_party_comms_sync_all(ctx);
    
    comms_debug("Party sync_all completed: ret=%d\n", ret);
    return ret;
}

/**
 * @brief Party Mode incremental data sync implementation
 */
__s32 ui_mcu_party_sync_dirty(void *ctx, __u32 dirty_mask)
{
    if (!ctx || !reg_app_para || dirty_mask == 0) return EPDK_OK;
    
    comms_debug("Party sync_dirty: mask=0x%08X\n", dirty_mask);
    
    // Use existing party sync functions
    __s32 ret = ui_party_comms_sync_dirty(ctx, dirty_mask);
    
    comms_debug("Party sync_dirty completed: ret=%d\n", ret);
    return ret;
}

/* ===================== Settings MCU Interface Functions ===================== */

/**
 * @brief Settings full data sync implementation
 */
__s32 ui_mcu_settings_sync_all(void *ctx)
{
    if (!ctx || !reg_app_para) return EPDK_FAIL;
    
    comms_debug("Settings sync_all started\n");
    
    // Sync all settings data: beep, aux_in, bt settings, etc.
    // Most settings functions are already implemented, just organize them
    
    comms_debug("Settings sync_all completed\n");
    return EPDK_OK;
}

/**
 * @brief Settings incremental data sync implementation
 */
__s32 ui_mcu_settings_sync_dirty(void *ctx, __u32 dirty_mask)
{
    if (!ctx || !reg_app_para || dirty_mask == 0) return EPDK_OK;
    
    comms_debug("Settings sync_dirty: mask=0x%08X\n", dirty_mask);
    
    // Handle specific settings data types based on dirty_mask
    if (dirty_mask & SETTINGS_DATA_BEEP_SWITCH) {
        comms_debug("Syncing beep switch\n");
    }
    
    if (dirty_mask & SETTINGS_DATA_BT_SWITCH) {
        comms_debug("Syncing BT switch\n");
    }
    
    comms_debug("Settings sync_dirty completed\n");
    return EPDK_OK;
}

