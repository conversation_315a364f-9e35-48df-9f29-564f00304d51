
#ifndef _UI_SOURCE_COMMUNICATION_HUB_H
#define _UI_SOURCE_COMMUNICATION_HUB_H

#ifdef __cplusplus
extern "C" {
#endif

#include "leopard_init.h"
#include "../../framework/list.h"
#include "mod_mcu.h"
#include "ui_source_res.h"

__s32 ui_source_comms_hub_init(ui_source_para_t *para);
__s32 ui_source_comms_hub_deinit(ui_source_para_t *para);
__s32 ui_source_comms_hub_settings_para_init(ui_source_para_t *para);
__s32 ui_source_comms_hub_general_audio_para_init(ui_source_para_t *para);
__s32 ui_source_comms_hub_source_gain_para_update(ui_source_para_t *para);
__s32 ui_soruce_comms_hub_zone_audio_eq_para_update(ui_source_para_t *para);
__s32 ui_source_comms_hub_zone_audio_fade_balance_update(ui_source_para_t *para);
__s32 ui_soruce_comms_hub_zone_audio_crossover_update(ui_source_para_t *para);
__s32 ui_source_comms_hub_zone_audio_punch_eq_update(ui_source_para_t *para);
__s32 ui_source_comms_hub_zone_audio_para_init(ui_source_para_t *para);
__s32 ui_source_comms_hub_audio_volume_update(ui_source_para_t *para);
__s32 ui_source_comms_set_audio_zone_vs_subwoofer_volume(ui_source_para_t *para);
__s32 ui_source_comms_set_audio_zone_volume(ui_source_para_t *para);
__s32 ui_source_comms_set_audio_subwoofer_volume(ui_source_para_t *para);
__s32 ui_source_comms_set_audio_subwoofer_switch(ui_source_para_t *para);
__s32 ui_source_comms_set_audio_subwoofer_phase(ui_source_para_t *para);
__s32 ui_source_comms_set_audio_mute(ui_source_para_t *para);
__s32 ui_source_comms_set_audio_defeat(ui_source_para_t *para);
__s32 ui_source_comms_set_audio_source_gain(ui_source_para_t *para);
__s32 ui_source_comms_set_audio_volume_offset(ui_source_para_t *para);
__s32 ui_source_comms_set_audio_phone_volume_sync(ui_source_para_t *para);
__s32 ui_source_comms_set_audio_reset(ui_source_para_t *para);
__s32 ui_source_comms_set_audio_zone_enable(ui_source_para_t *para);
__s32 ui_source_comms_set_audio_favorite_zone_enable(ui_source_para_t *para);
__s32 ui_source_comms_set_zone_audio_eq(ui_source_para_t *para);
__s32 ui_source_comms_set_zone_audio_crossover(ui_source_para_t *para);
__s32 ui_source_comms_set_zone_audio_fade(ui_source_para_t *para);
__s32 ui_source_comms_set_zone_audio_balance(ui_source_para_t *para);
__s32 ui_source_comms_set_zone_audio_punch_eq(ui_source_para_t *para);

__s32 ui_source_comms_set_work_source(__u8 source_id);

/* ===================== Universal Data Synchronization ===================== */
// Automatic data synchronization when source is switched
__s32 ui_source_comms_handle_source_switch(__u8 new_source, void *ctx);
// Message-driven incremental synchronization based on GUI_MSG_UI_NEW_xxx_UPDATE
__s32 ui_source_comms_handle_update_message(__u32 msg_id, __u32 pending_mask, void *msg_data, void *ctx);
/* ===================== FM/AM/WB Data Synchronization ===================== */
// Full sync for current tuner category (FM/AM/WB).
__s32 ui_fm_comms_sync_all(void *context);
// Incremental sync for current tuner category by dirty mask (data type bitfield from driver).
__s32 ui_fm_comms_sync_dirty(void *context, __u32 change_mask);
/* ===================== Tuner/FM Synchronization & Control ===================== */
// Base info full sync: pull current tuner status into a UI-side struct
__s32 ui_fm_comms_sync_base_info(void *context);
// Preset list sync: pull preset arrays for current band
__s32 ui_fm_comms_sync_list(void *context);
// Set exact frequency in kHz for current band (FM/AM/WB)
__s32 ui_fm_comms_set_frequency(__u32 khz);
// Scan/seek mode control: 0=stop,1=manual_up,2=manual_down,3=auto_up,4=auto_down,5=preset_up,6=preset_down,7=auto_store
__s32 ui_fm_comms_set_scan_mode(__u8 mode);
// Preset operation: action 0=recall,1=save,2=remove; index is 1-based
__s32 ui_fm_comms_set_preset(__u8 action, __u8 index);
// Local seek sensitivity switch (FM/AM). WB not supported.
__s32 ui_fm_comms_set_local_seek(__u8 on_off);
// RDS switch (FM only)
__s32 ui_fm_comms_set_rds_switch(__u8 on_off);
// Request preset list from MCU for current band
__s32 ui_fm_comms_get_preset_list(void);
// Request RDS PS/RT text (FM only)
__s32 ui_fm_comms_get_rds_text(void);

/* ===================== USB Data Synchronization ===================== */
// Full sync for current USB category (Disk/iPod).
__s32 ui_usb_comms_sync_all(void *context);
// Incremental sync for current USB category by dirty mask (data type bitfield from driver).
__s32 ui_usb_comms_sync_dirty(void *context, __u32 change_mask);

// Individual USB data sync functions
__s32 ui_usb_comms_sync_track_info(void *context);
__s32 ui_usb_comms_sync_play_time(void *context);
__s32 ui_usb_comms_sync_play_state(void *context);
__s32 ui_usb_comms_sync_play_mode(void *context);
__s32 ui_usb_comms_sync_browser_list(void *context);

/* ===================== Party Mode Data Synchronization ===================== */
// Full sync for party mode - sync all party data to UI context
__s32 ui_party_comms_sync_all(void *context);
// Incremental sync for party mode by dirty mask (data type bitfield from driver)
__s32 ui_party_comms_sync_dirty(void *context, __u32 change_mask);

//party mode part
__s32 ui_party_comms_get_init_status(void);
__s32 ui_party_comms_get_own_info(void);
__s32 ui_party_comms_get_role_device_info(void);
__s32 ui_party_comms_get_rc_known_device_list(void);
__s32 ui_party_comms_set_device_role(void *context,__u8 target_role);
__s32 ui_party_comms_set_bc_source(void *context,__u8 target_source);
__s32 ui_party_comms_set_bc_casting_active(void *context,__u8 action);
__s32 ui_party_comms_set_rc_scan_control(void *context,__u8 action);
__s32 ui_party_comms_set_rc_connect_control(void *context,__u8 *address,__u8 action);
__s32 ui_party_comms_set_rc_delete_known_device(void *context,__u8 *address,__u8 item_id);
__s32 ui_party_comms_sync_party_own_info(void *context);
__s32 ui_party_comms_sync_broadcast_info(void *context);
__s32 ui_party_comms_sync_broadcasting_active(void *context);
__s32 ui_party_comms_sync_receiver_connect_device(void *context);
__s32 ui_party_comms_sync_receiver_known_list(void *context);
__s32 ui_party_comms_sync_receiver_scan_list(void *context);

#ifdef __cplusplus
} /*extern "C"*/
#endif

#endif

