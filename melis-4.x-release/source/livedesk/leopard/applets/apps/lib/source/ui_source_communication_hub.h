
#ifndef _UI_SOURCE_COMMUNICATION_HUB_H
#define _UI_SOURCE_COMMUNICATION_HUB_H

#ifdef __cplusplus
extern "C" {
#endif

#include "leopard_init.h"
#include "../../framework/list.h"
#include "mod_mcu.h"
#include "ui_source_res.h"

/* ===================== MCU Data Operations Interface ===================== */

typedef struct mcu_ops {
  // Data retrieval function
  __s32 (*get_data)(__u32 data_type, void *param, __u8 category);  // Get data (with optional param and category)
  // Data setting/control function
  __s32 (*set_control)(__u32 data_type, void *data, __u16 data_len, __u8 category); // Set/control data (with category)
}mcu_ops_t;

typedef struct mcu_data_ops {
    // Data synchronization functions
    __s32 (*sync_all)(void *ctx);                                 // Full data sync
    __s32 (*sync_dirty)(void *ctx, __u32 dirty_mask);           // Incremental sync
    // command operations
    mcu_ops_t *ops;
} mcu_data_ops_t;

__s32 ui_source_comms_hub_init(ui_source_para_t *para);
__s32 ui_source_comms_hub_deinit(ui_source_para_t *para);
__s32 ui_source_comms_hub_settings_para_init(ui_source_para_t *para);
__s32 ui_source_comms_hub_general_audio_para_init(ui_source_para_t *para);
__s32 ui_source_comms_hub_source_gain_para_update(ui_source_para_t *para);
__s32 ui_soruce_comms_hub_zone_audio_eq_para_update(ui_source_para_t *para);
__s32 ui_source_comms_hub_zone_audio_fade_balance_update(ui_source_para_t *para);
__s32 ui_soruce_comms_hub_zone_audio_crossover_update(ui_source_para_t *para);
__s32 ui_source_comms_hub_zone_audio_punch_eq_update(ui_source_para_t *para);
__s32 ui_source_comms_hub_zone_audio_para_init(ui_source_para_t *para);
__s32 ui_source_comms_hub_audio_volume_update(ui_source_para_t *para);
__s32 ui_source_comms_set_audio_zone_vs_subwoofer_volume(ui_source_para_t *para);
__s32 ui_source_comms_set_audio_zone_volume(ui_source_para_t *para);
__s32 ui_source_comms_set_audio_subwoofer_volume(ui_source_para_t *para);
__s32 ui_source_comms_set_audio_subwoofer_switch(ui_source_para_t *para);
__s32 ui_source_comms_set_audio_subwoofer_phase(ui_source_para_t *para);
__s32 ui_source_comms_set_audio_mute(ui_source_para_t *para);
__s32 ui_source_comms_set_audio_defeat(ui_source_para_t *para);
__s32 ui_source_comms_set_audio_source_gain(ui_source_para_t *para);
__s32 ui_source_comms_set_audio_volume_offset(ui_source_para_t *para);
__s32 ui_source_comms_set_audio_phone_volume_sync(ui_source_para_t *para);
__s32 ui_source_comms_set_audio_reset(ui_source_para_t *para);
__s32 ui_source_comms_set_audio_zone_enable(ui_source_para_t *para);
__s32 ui_source_comms_set_audio_favorite_zone_enable(ui_source_para_t *para);
__s32 ui_source_comms_set_zone_audio_eq(ui_source_para_t *para);
__s32 ui_source_comms_set_zone_audio_crossover(ui_source_para_t *para);
__s32 ui_source_comms_set_zone_audio_fade(ui_source_para_t *para);
__s32 ui_source_comms_set_zone_audio_balance(ui_source_para_t *para);
__s32 ui_source_comms_set_zone_audio_punch_eq(ui_source_para_t *para);

__s32 ui_source_comms_set_work_source(__u8 source_id);

/* ===================== Universal Data Synchronization ===================== */
// Automatic data synchronization when source is switched
__s32 ui_source_comms_handle_source_switch(__u8 new_source, void *ctx);
// Message-driven incremental synchronization based on GUI_MSG_UI_NEW_xxx_UPDATE
__s32 ui_source_comms_handle_update_message(__u32 msg_id, __u32 pending_mask, void *msg_data, void *ctx);
/* ===================== FM/AM/WB Data Synchronization ===================== */
// Full sync for current tuner category (FM/AM/WB).
__s32 ui_fm_comms_sync_all(void *context);
// Incremental sync for current tuner category by dirty mask (data type bitfield from driver).
__s32 ui_fm_comms_sync_dirty(void *context, __u32 change_mask);
/* ===================== Tuner/FM Synchronization & Control ===================== */
// Base info full sync: pull current tuner status into a UI-side struct
__s32 ui_fm_comms_sync_base_info(void *context);
// Preset list sync: pull preset arrays for current band
__s32 ui_fm_comms_sync_list(void *context);
// Set exact frequency in kHz for current band (FM/AM/WB)
__s32 ui_fm_comms_set_frequency(__u32 khz);
// Scan/seek mode control: 0=stop,1=manual_up,2=manual_down,3=auto_up,4=auto_down,5=preset_up,6=preset_down,7=auto_store
__s32 ui_fm_comms_set_scan_mode(__u8 mode);
// Preset operation: action 0=recall,1=save,2=remove; index is 1-based
__s32 ui_fm_comms_set_preset(__u8 action, __u8 index);
// Local seek sensitivity switch (FM/AM). WB not supported.
__s32 ui_fm_comms_set_local_seek(__u8 on_off);
// RDS switch (FM only)
__s32 ui_fm_comms_set_rds_switch(__u8 on_off);
// Request preset list from MCU for current band
__s32 ui_fm_comms_get_preset_list(void);
// Request RDS PS/RT text (FM only)
__s32 ui_fm_comms_get_rds_text(void);

/* ===================== USB Data Synchronization ===================== */
// Full sync for current USB category (Disk/iPod).
__s32 ui_usb_comms_sync_all(void *context);
// Incremental sync for current USB category by dirty mask (data type bitfield from driver).
__s32 ui_usb_comms_sync_dirty(void *context, __u32 change_mask);

// Individual USB data sync functions
__s32 ui_usb_comms_sync_track_info(void *context);
__s32 ui_usb_comms_sync_play_time(void *context);
__s32 ui_usb_comms_sync_play_state(void *context);
__s32 ui_usb_comms_sync_play_mode(void *context);
__s32 ui_usb_comms_sync_browser_list(void *context);

/* ===================== Party Mode Data Synchronization ===================== */
// Full sync for party mode - sync all party data to UI context
__s32 ui_party_comms_sync_all(void *context);
// Incremental sync for party mode by dirty mask (data type bitfield from driver)
__s32 ui_party_comms_sync_dirty(void *context, __u32 change_mask);

//party mode part
__s32 ui_party_comms_get_init_status(void);
__s32 ui_party_comms_get_own_info(void);
__s32 ui_party_comms_get_role_device_info(void);
__s32 ui_party_comms_get_rc_known_device_list(void);
__s32 ui_party_comms_set_device_role(void *context,__u8 target_role);
__s32 ui_party_comms_set_bc_source(void *context,__u8 target_source);
__s32 ui_party_comms_set_bc_casting_active(void *context,__u8 action);
__s32 ui_party_comms_set_rc_scan_control(void *context,__u8 action);
__s32 ui_party_comms_set_rc_connect_control(void *context,__u8 *address,__u8 action);
__s32 ui_party_comms_set_rc_delete_known_device(void *context,__u8 *address,__u8 item_id);
__s32 ui_party_comms_sync_party_own_info(void *context);
__s32 ui_party_comms_sync_broadcast_info(void *context);
__s32 ui_party_comms_sync_broadcasting_active(void *context);
__s32 ui_party_comms_sync_receiver_connect_device(void *context);
__s32 ui_party_comms_sync_receiver_known_list(void *context);
__s32 ui_party_comms_sync_receiver_scan_list(void *context);

/* ===================== MCU Interface Functions ===================== */
// Tuner MCU interface functions
__s32 ui_mcu_tuner_sync_all(void *ctx);
__s32 ui_mcu_tuner_sync_dirty(void *ctx, __u32 dirty_mask);
__s32 ui_mcu_tuner_get_data(__u32 data_type, void *param, __u8 category);
__s32 ui_mcu_tuner_set_control(__u32 data_type, void *data, __u16 data_len, __u8 category);

// Music MCU interface functions (USB DISK/IPOD/BT AUDIO)
__s32 ui_mcu_music_sync_all(void *ctx);
__s32 ui_mcu_music_sync_dirty(void *ctx, __u32 dirty_mask);

// SXM MCU interface functions  
__s32 ui_mcu_sxm_sync_all(void *ctx);
__s32 ui_mcu_sxm_sync_dirty(void *ctx, __u32 dirty_mask);

// Party Mode MCU interface functions
__s32 ui_mcu_party_sync_all(void *ctx);
__s32 ui_mcu_party_sync_dirty(void *ctx, __u32 dirty_mask);

// Settings MCU interface functions
__s32 ui_mcu_settings_sync_all(void *ctx);
__s32 ui_mcu_settings_sync_dirty(void *ctx, __u32 dirty_mask);

__s32 ui_mcu_ops_v2_get_data(__u32 data_type, __u8 *param, __u16 param_len, __u8 category);
__s32 ui_mcu_ops_v2_set_control(__u32 data_type, void *data, __u16 data_len, __u8 category);


/* ===================== Application Layer MCU Sync Macros ===================== */

/**
 * @brief Application layer MCU sync processing macro
 * Function: Read pending changes, clear pending and queue flags
 * Implementation: Direct copy of MCU_PROTOCOL_SYNC_PROCESS logic, avoiding driver header inclusion
 * Usage: __u32 changes = UI_MCU_SYNC_PROCESS(&reg_app_para->tuner_para.sync);
 */
#define UI_MCU_SYNC_PROCESS(sync_ptr) ({ \
    __u32 changes = (sync_ptr)->dirty_mask_pending; \
    (sync_ptr)->dirty_mask_pending = 0; \
    (sync_ptr)->msg_in_queue = 0; \
    changes; \
})

/**
 * @brief Application layer MCU update message unified processing macro
 * Function: Process MCU sync + call application's sync_dirty interface
 * Usage: UI_MCU_HANDLE_UPDATE(&reg_app_para->tuner_para.sync, app_para->mcu_ops, app_para);
 */
#define UI_MCU_HANDLE_UPDATE(sync_ptr, mcu_ops, ctx) do { \
    __u32 changes = UI_MCU_SYNC_PROCESS(sync_ptr); \
    if (changes != 0 && (mcu_ops).sync_dirty) { \
        (mcu_ops).sync_dirty((ctx), changes); \
    } \
} while(0)

/**
 * @brief Check application switch macro (optional usage)
 * Function: Detect sequence_number changes, determine if full sync needed
 * Usage: if (UI_MCU_CHECK_SWITCH(&reg_app_para->tuner_para.sync, &last_seq)) { full_sync(); }
 */
#define UI_MCU_CHECK_SWITCH(sync_ptr, last_seq_ptr) ({ \
    __bool need_sync = EPDK_FALSE; \
    if ((sync_ptr)->sequence_number != *(last_seq_ptr)) { \
        *(last_seq_ptr) = (sync_ptr)->sequence_number; \
        need_sync = EPDK_TRUE; \
    } \
    need_sync; \
})

#ifdef __cplusplus
} /*extern "C"*/
#endif

#endif

