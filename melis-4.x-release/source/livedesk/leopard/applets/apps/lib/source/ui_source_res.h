// This file was generated by SquareLine Studio
// SquareLine Studio version: SquareLine Studio 1.3.0
// LVGL version: 8.3.6
// Project name: SquareLine_Project

#ifndef _UI_SOURCE_RES_H
#define _UI_SOURCE_RES_H

#ifdef __cplusplus
extern "C" {
#endif

#include "lvgl.h"
#include "ui_explorer.h"

enum
{
    SOURCE_TEMP_MEDIA_SETUP_REPEAT_C_BMP,
    SOURCE_TEMP_COM_SOURCE_MEMU_N_BMP,   
    SOURCE_TEMP_COM_TOP_BAR_USB_BMP,     
    SOURCE_TEMP_COM_MEDIA_ARTIST_N_BMP,  
    SOURCE_TEMP_COM_MEDIA_PG_BAR_BG_BMP, 
    SOURCE_TEMP_MEDIA_SETUP_AUDIO_N_BMP, 
    SOURCE_TEMP_ZONE_SUB_S_BMP,          
    SOURCE_TEMP_ZONE_VOLUME_N_BMP,       
    SOURCE_TEMP_MEDIA_SETUP_SEL_BG_S_BMP,
    SOURCE_TEMP_COM_MEDIA_PLAY_BMP,      
    SOURCE_TEMP_COM_MENU_BG_N_BMP,       
    SOURCE_TEMP_COM_MENU_BG_S_BMP,       
    SOURCE_TEMP_COM_MENU_ENTER_BMP,      
    SOURCE_TEMP_MEDIA_SETUP_AUDIO_S_BMP, 
    SOURCE_TEMP_MEDIA_SETUP_N_BMP,       
    SOURCE_TEMP_COM_TOP_BAR_SXM_BMP,     
    SOURCE_TEMP_COM_MEDIA_PLAYLISTS_N_BMP,
    SOURCE_TEMP_COM_MEDIA_PLAY_BG_S_P_BMP,
    SOURCE_TEMP_MEDIA_SETUP_BACK_N_BMP,  
    SOURCE_TEMP_ZONE_OPEN_BG_N_BMP,      
    SOURCE_TEMP_LOAD_S_BMP,              
    SOURCE_TEMP_COM_MEDIA_PAUSE_BMP,     
    SOURCE_TEMP_COM_MEDIA_DOWN_N_BMP,    
    SOURCE_TEMP_COM_MEDIA_UP_DOWN_BG_P_BMP,  
    SOURCE_TEMP_MEDIA_SETUP_RANDOM_A_BMP,
    SOURCE_TEMP_COM_MEDIA_GENRES_N_BMP,  
    SOURCE_TEMP_COM_SIG_A_BMP,           
    SOURCE_TEMP_COM_MEDIA_COMPILATIONS_N_BMP,
    SOURCE_TEMP_COM_SIG_B_BMP,           
    SOURCE_TEMP_COM_MEDIA_PODCASTS_N_BMP,
    SOURCE_TEMP_COM_MEDIA_ALBUM_N_BMP,   
    SOURCE_TEMP_MEDIA_SETUP_RANDOM_N_BMP,
    SOURCE_TEMP_COM_TOP_BAR_P_BMP,       
    SOURCE_TEMP_MEDIA_SETUP_BAR_BG_BMP,  
    SOURCE_TEMP_COM_SOURCE_MEMU_S_BMP,   
    SOURCE_TEMP_MEDIA_SETUP_REPEAT_B_BMP,
    SOURCE_TEMP_COM_MEDIA_ALBUM_BG_BMP,  
    SOURCE_TEMP_LOAD_N_BMP,              
    SOURCE_TEMP_COM_MEDIA_PLAY_BG_BMP,   
    SOURCE_TEMP_COM_MEDIA_BOOK_N_BMP,    
    SOURCE_TEMP_COM_MEDIA_UP_N_BMP,      
    SOURCE_TEMP_COM_SIG_C_BMP,           
    SOURCE_TEMP_MEDIA_SETUP_REPEAT_N_BMP,
    SOURCE_TEMP_COM_MEDIA_PG_BAR_S_BMP,  
    SOURCE_TEMP_COM_MEDIA_SONG_N_BMP,    
    SOURCE_TEMP_COM_MEDIA_UP_S_BMP,      
    SOURCE_TEMP_COM_MEDIA_DOWN_S_BMP,    
    SOURCE_TEMP_COM_SOURCE_N_BMP,        
    SOURCE_TEMP_MEDIA_SETUP_REPEAT_A_BMP,
    SOURCE_TEMP_COM_SIG_D_BMP,           
    SOURCE_TEMP_MEDIA_SETUP_BACK_S_BMP,  
    SOURCE_TEMP_COM_SOURCE_S_BMP,        
    SOURCE_TEMP_ZONE_OPEN_S_BMP,         
    SOURCE_TEMP_MEDIA_SETUP_RANDOM_S_BMP,
    SOURCE_TEMP_MEDIA_SETUP_RANDOM_B_BMP,
    SOURCE_TEMP_MEDIA_SETUP_S_BMP,       
    SOURCE_TEMP_ZONE_OPEN_N_BMP,         
    SOURCE_TEMP_MEDIA_SETUP_REPEAT_S_BMP,
    SOURCE_TEMP_COM_MEDIA_SONG_S_BMP,    
    SOURCE_TEMP_ZONE_VOLUME_S_BMP,       
    SOURCE_TEMP_COM_MEDIA_COMPOSERS_N_BMP,
    SOURCE_TEMP_COM_MENU_BG_P_BMP,       
    SOURCE_TEMP_ZONE_SUB_N_BMP,          
    SOURCE_TEMP_ZONE_OPEN_BG_P_BMP,   
    SOURCE_TEMP_COM_BAND_SEL_N_BMP,          
    SOURCE_TEMP_COM_BAND_SEL_S_BMP,  

    SOURCE_TEMP_NIGHT_NIG_MEDIA_SETUP_REPEAT_B_BMP,  
    SOURCE_TEMP_NIGHT_NIG_FM_ICON_BMP,
    SOURCE_TEMP_NIGHT_NIG_AM_ICON_BMP,
    SOURCE_TEMP_NIGHT_NIG_WB_ICON_BMP,
    SOURCE_TEMP_NIGHT_NIG_COM_LIST_OFF_BMP,          
    SOURCE_TEMP_NIGHT_NIG_MEDIA_DUAL_PHONE_A_BMP,    
    SOURCE_TEMP_NIGHT_NIG_COM_RIG_BG_A_BMP,          
    SOURCE_TEMP_NIGHT_NIG_COM_RIG_ARROW_A_BMP,       
    SOURCE_TEMP_NIGHT_NIG_MEDIA_SETUP_REPEAT_A_BMP,  
    SOURCE_TEMP_NIGHT_NIG_COM_RIG_AUDIO_ICON_S_BMP,  
    SOURCE_TEMP_NIGHT_NIG_COM_RIG_AUDIO_ICON_N_BMP,  
    SOURCE_TEMP_NIGHT_NIG_MEDIA_DUAL_PHONE_B_BMP,    
    SOURCE_TEMP_NIGHT_NIG_COM_TOP_BACK_ICON_BMP,     
    SOURCE_TEMP_NIGHT_NIG_COM_MEDIA_ALBUM_BG_BMP,    
    SOURCE_TEMP_NIGHT_NIG_MEDIA_SETUP_RANDOM_A_BMP,  
    SOURCE_TEMP_NIGHT_NIG_COM_RIG_FUNC_ICON_S_BMP,   
    SOURCE_TEMP_NIGHT_NIG_COM_RIG_ARROW_B_BMP,       
    SOURCE_TEMP_NIGHT_NIG_COM_LOW_VOLTAGE_BMP,       
    SOURCE_TEMP_NIGHT_NIG_COM_RIG_BG_C_BMP,          
    SOURCE_TEMP_NIGHT_NIG_COM_TOP_ICON_BG_N_BMP,     
    SOURCE_TEMP_NIGHT_NIG_COM_LIST_ON_BMP,           
    SOURCE_TEMP_NIGHT_NIG_COM_LIST_BG_BMP,           
    SOURCE_TEMP_NIGHT_NIG_COM_RIG_BG_B_BMP,          
    SOURCE_TEMP_NIGHT_NIG_COM_RIG_ICON_BG_N_BMP,     
    SOURCE_TEMP_NIGHT_NIG_MEDIA_SETUP_RANDOM_B_BMP,  
    SOURCE_TEMP_NIGHT_NIG_COM_RIG_ZONE_ICON_BMP,     
    SOURCE_TEMP_NIGHT_NIG_COM_RIG_ICON_BG_P_BMP,     
    SOURCE_TEMP_NIGHT_NIG_COM_BG_BMP,                
    SOURCE_TEMP_NIGHT_NIG_COM_PD_CHARGE_BMP,         
    SOURCE_TEMP_NIGHT_NIG_MEDIA_SETUP_REPEAT_C_BMP,  
    SOURCE_TEMP_NIGHT_NIG_COM_PUNCH_EQ_BMP,          
    SOURCE_TEMP_NIGHT_NIG_COM_TOP_SOURCE_ICON_BMP,   
    SOURCE_TEMP_NIGHT_NIG_COM_RIG_ARROW_C_BMP,       
    SOURCE_TEMP_NIGHT_NIG_COM_TOP_BG_BMP,            
    SOURCE_TEMP_NIGHT_NIG_COM_RIG_ZONE_ICON_S_BMP,   
    SOURCE_TEMP_NIGHT_NIG_COM_RIG_ZONE_ICON_N_BMP,   
    SOURCE_TEMP_NIGHT_NIG_COM_RIG_AUDIO_ICON_BMP,    
    SOURCE_TEMP_NIGHT_NIG_COM_TOP_LIST_ICON_BMP,     
    SOURCE_TEMP_NIGHT_NIG_MEDIA_DUAL_PHONE_D_BMP,    
    SOURCE_TEMP_NIGHT_NIG_COM_RIG_FUNC_ICON_N_BMP,   
    SOURCE_TEMP_NIGHT_NIG_COM_TOP_ICON_BG_P_S_BMP,   
    SOURCE_TEMP_NIGHT_NIG_COM_RIG_ICON_N_BG_BMP,
    SOURCE_TEMP_NIGHT_NIG_COM_RIG_ICON_BG_S_BMP,
    SOURCE_TEMP_NIGHT_NIG_COM_RIG_FUNCTION_ICON_BMP,
    SOURCE_TEMP_NIGHT_NIG_COM_RIG_FUNCTION_ICON_N_BMP,
    SOURCE_TEMP_NIGHT_NIG_COM_RIG_LIST_ICON_BMP,
    SOURCE_TEMP_NIGHT_NIG_COM_RIG_LIST_ICON_N_BMP,
    SOURCE_TEMP_NIGHT_NIG_COM_TOP_NOWPLAY_ICON_BMP,
    SOURCE_TEMP_NIGHT_NIG_COM_TOP_SETTINGS_ICON_BMP,
    SOURCE_TEMP_NIGHT_NIG_COM_TOP_ZONE_ICON_BMP,
    SOURCE_TEMP_NIGHT_NIG_COM_RIG_INFO_ICON_BMP,
    SOURCE_TEMP_NIGHT_NIG_COM_RIG_INFO_ICON_N_BMP,
    SOURCE_TEMP_NIGHT_NIG_COM_RIG_SYSTEM_ICON_BMP,
    SOURCE_TEMP_NIGHT_NIG_COM_RIG_SYSTEM_ICON_N_BMP,

    SOURCE_COMMON_NIG_COM_BOT_BG_BMP,  
    SOURCE_COMMON_NIG_COM_BOT_ICON_BG_A_N_BMP,  
    SOURCE_COMMON_NIG_COM_BOT_ICON_BG_A_P_S_BMP, 
    SOURCE_COMMON_NIG_COM_BOT_ICON_BG_B_N_BMP,   
    SOURCE_COMMON_NIG_COM_BOT_ICON_BG_B_P_S_BMP, 
    SOURCE_COMMON_NIG_COM_BOT_MEDIA_PAUSE_BMP,   
    SOURCE_COMMON_NIG_COM_BOT_MEDIA_PAUSE_D_BMP,
    SOURCE_COMMON_NIG_COM_BOT_MEDIA_PLAY_BMP,    
    SOURCE_COMMON_NIG_COM_BOT_MEDIA_PLAY_D_BMP,
    SOURCE_COMMON_NIG_COM_BOT_MEDIA_DOWN_N_BMP,  
    SOURCE_COMMON_NIG_COM_BOT_MEDIA_DOWN_P_S_BMP,
    SOURCE_COMMON_NIG_COM_BOT_MEDIA_UP_N_BMP,    
    SOURCE_COMMON_NIG_COM_BOT_MEDIA_UP_P_S_BMP,
    SOURCE_COMMON_NIG_COM_BOT_BROWSE_N_BMP,
    SOURCE_COMMON_NIG_COM_BOT_BROWSE_D_BMP,
    SOURCE_COMMON_NIG_COM_BOT_BT_CONNECT_N_BMP,

    SOURCE_SETTING_NIG_SETTINGS_BT_N_BMP,
    SOURCE_SETTING_NIG_SETTINGS_BT_S_BMP,
    SOURCE_SETTING_NIG_SETTINGS_GEN_SET_N_BMP,
    SOURCE_SETTING_NIG_SETTINGS_GEN_SET_S_BMP,
    SOURCE_SETTING_NIG_SETTINGS_INFO_N_BMP,
    SOURCE_SETTING_NIG_SETTINGS_INFO_S_BMP,
    SOURCE_SETTING_NIG_SETTINGS_NETWORK_N_BMP,
    SOURCE_SETTING_NIG_SETTINGS_NETWORK_S_BMP,

    SOURCE_AUDIO_NIG_AUDIO_GEN_SET_N_BMP,
    SOURCE_AUDIO_NIG_AUDIO_GEN_SET_S_BMP,
    SOURCE_AUDIO_NIG_AUDIO_ZONE_AUDIO_N_BMP,
    SOURCE_AUDIO_NIG_AUDIO_ZONE_AUDIO_S_BMP,
    SOURCE_AUDIO_NIG_AUDIO_ZONE_SET_N_BMP,
    SOURCE_AUDIO_NIG_AUDIO_ZONE_SET_S_BMP,

    SOURCE_AUDIO_NIG_AUDIO_EQ_N_BMP,
    SOURCE_AUDIO_NIG_AUDIO_EQ_S_BMP,
    SOURCE_AUDIO_NIG_AUDIO_FB_N_BMP,
    SOURCE_AUDIO_NIG_AUDIO_FB_S_BMP,
    SOURCE_AUDIO_NIG_AUDIO_CO_N_BMP,
    SOURCE_AUDIO_NIG_AUDIO_CO_S_BMP,
    SOURCE_AUDIO_NIG_AUDIO_PEQ_A_N_BMP,
    SOURCE_AUDIO_NIG_AUDIO_PEQ_A_S_BMP,
    SOURCE_AUDIO_NIG_AUDIO_PEQ_B_N_BMP,
    SOURCE_AUDIO_NIG_AUDIO_PEQ_B_S_BMP,

    SOURCE_AUDIO_NIG_AUDIO_EQ_LEFT_N_BMP,
    SOURCE_AUDIO_NIG_AUDIO_EQ_LEFT_S_BMP,
    SOURCE_AUDIO_NIG_AUDIO_EQ_RIGHT_N_BMP,
    SOURCE_AUDIO_NIG_AUDIO_EQ_RIGHT_S_BMP,
    SOURCE_AUDIO_NIG_AUDIO_CO_UP_N_BMP,
    SOURCE_AUDIO_NIG_AUDIO_CO_DOWN_N_BMP,

    SOURCE_TEMP_NIGHT_NIG_COM_RIG_FUNC_ICON_BMP,     

    SOURCE_TEMP_DAY_DAY_MEDIA_SETUP_REPEAT_B_BMP,  
    SOURCE_TEMP_DAY_DAY_FM_ICON_BMP,
    SOURCE_TEMP_DAY_DAY_AM_ICON_BMP,
    SOURCE_TEMP_DAY_DAY_WB_ICON_BMP,
    SOURCE_TEMP_DAY_DAY_COM_LIST_OFF_BMP,          
    SOURCE_TEMP_DAY_DAY_MEDIA_DUAL_PHONE_A_BMP,    
    SOURCE_TEMP_DAY_DAY_COM_RIG_BG_A_BMP,          
    SOURCE_TEMP_DAY_DAY_COM_RIG_ARROW_A_BMP,       
    SOURCE_TEMP_DAY_DAY_MEDIA_SETUP_REPEAT_A_BMP,  
    SOURCE_TEMP_DAY_DAY_COM_RIG_AUDIO_ICON_S_BMP,  
    SOURCE_TEMP_DAY_DAY_COM_RIG_AUDIO_ICON_N_BMP,  
    SOURCE_TEMP_DAY_DAY_MEDIA_DUAL_PHONE_B_BMP,    
    SOURCE_TEMP_DAY_DAY_COM_TOP_BACK_ICON_BMP,     
    SOURCE_TEMP_DAY_DAY_COM_MEDIA_ALBUM_BG_BMP,    
    SOURCE_TEMP_DAY_DAY_MEDIA_SETUP_RANDOM_A_BMP,  
    SOURCE_TEMP_DAY_DAY_COM_RIG_FUNC_ICON_S_BMP,   
    SOURCE_TEMP_DAY_DAY_COM_RIG_ARROW_B_BMP,       
    SOURCE_TEMP_DAY_DAY_COM_LOW_VOLTAGE_BMP,       
    SOURCE_TEMP_DAY_DAY_COM_RIG_BG_C_BMP,          
    SOURCE_TEMP_DAY_DAY_COM_TOP_ICON_BG_N_BMP,     
    SOURCE_TEMP_DAY_DAY_COM_LIST_ON_BMP,           
    SOURCE_TEMP_DAY_DAY_COM_LIST_BG_BMP,           
    SOURCE_TEMP_DAY_DAY_COM_RIG_BG_B_BMP,          
    SOURCE_TEMP_DAY_DAY_COM_RIG_ICON_BG_N_BMP,     
    SOURCE_TEMP_DAY_DAY_MEDIA_SETUP_RANDOM_B_BMP,  
    SOURCE_TEMP_DAY_DAY_COM_RIG_ZONE_ICON_BMP,     
    SOURCE_TEMP_DAY_DAY_COM_RIG_ICON_BG_P_BMP,     
    SOURCE_TEMP_DAY_DAY_COM_BG_BMP,                
    SOURCE_TEMP_DAY_DAY_COM_PD_CHARGE_BMP,         
    SOURCE_TEMP_DAY_DAY_MEDIA_SETUP_REPEAT_C_BMP,  
    SOURCE_TEMP_DAY_DAY_COM_PUNCH_EQ_BMP,          
    SOURCE_TEMP_DAY_DAY_COM_TOP_SOURCE_ICON_BMP,   
    SOURCE_TEMP_DAY_DAY_COM_RIG_ARROW_C_BMP,       
    SOURCE_TEMP_DAY_DAY_COM_TOP_BG_BMP,            
    SOURCE_TEMP_DAY_DAY_COM_RIG_ZONE_ICON_S_BMP,   
    SOURCE_TEMP_DAY_DAY_COM_RIG_ZONE_ICON_N_BMP,   
    SOURCE_TEMP_DAY_DAY_COM_RIG_AUDIO_ICON_BMP,    
    SOURCE_TEMP_DAY_DAY_COM_TOP_LIST_ICON_BMP,     
    SOURCE_TEMP_DAY_DAY_MEDIA_DUAL_PHONE_D_BMP,    
    SOURCE_TEMP_DAY_DAY_COM_RIG_FUNC_ICON_N_BMP,   
    SOURCE_TEMP_DAY_DAY_COM_TOP_ICON_BG_P_S_BMP,   
    SOURCE_TEMP_DAY_DAY_COM_RIG_ICON_N_BG_BMP,
    SOURCE_TEMP_DAY_DAY_COM_RIG_ICON_BG_S_BMP,
    SOURCE_TEMP_DAY_DAY_COM_RIG_FUNCTION_ICON_BMP,
    SOURCE_TEMP_DAY_DAY_COM_RIG_FUNCTION_ICON_N_BMP,
    SOURCE_TEMP_DAY_DAY_COM_RIG_LIST_ICON_BMP,
    SOURCE_TEMP_DAY_DAY_COM_RIG_LIST_ICON_N_BMP,
    SOURCE_TEMP_DAY_DAY_COM_TOP_NOWPLAY_ICON_BMP,
    SOURCE_TEMP_DAY_DAY_COM_TOP_SETTINGS_ICON_BMP,
    SOURCE_TEMP_DAY_DAY_COM_TOP_ZONE_ICON_BMP,
    SOURCE_TEMP_DAY_DAY_COM_RIG_INFO_ICON_BMP,
    SOURCE_TEMP_DAY_DAY_COM_RIG_INFO_ICON_N_BMP,
    SOURCE_TEMP_DAY_DAY_COM_RIG_SYSTEM_ICON_BMP,
    SOURCE_TEMP_DAY_DAY_COM_RIG_SYSTEM_ICON_N_BMP,

    SOURCE_COMMON_DAY_COM_BOT_BG_BMP,  
    SOURCE_COMMON_DAY_COM_BOT_ICON_BG_A_N_BMP,  
    SOURCE_COMMON_DAY_COM_BOT_ICON_BG_A_P_S_BMP, 
    SOURCE_COMMON_DAY_COM_BOT_ICON_BG_B_N_BMP,   
    SOURCE_COMMON_DAY_COM_BOT_ICON_BG_B_P_S_BMP, 
    SOURCE_COMMON_DAY_COM_BOT_MEDIA_PAUSE_BMP,   
    SOURCE_COMMON_DAY_COM_BOT_MEDIA_PAUSE_D_BMP,
    SOURCE_COMMON_DAY_COM_BOT_MEDIA_PLAY_BMP,    
    SOURCE_COMMON_DAY_COM_BOT_MEDIA_PLAY_D_BMP,
    SOURCE_COMMON_DAY_COM_BOT_MEDIA_DOWN_N_BMP,  
    SOURCE_COMMON_DAY_COM_BOT_MEDIA_DOWN_P_S_BMP,
    SOURCE_COMMON_DAY_COM_BOT_MEDIA_UP_N_BMP,    
    SOURCE_COMMON_DAY_COM_BOT_MEDIA_UP_P_S_BMP,
    SOURCE_COMMON_DAY_COM_BOT_BROWSE_N_BMP,
    SOURCE_COMMON_DAY_COM_BOT_BROWSE_D_BMP,
    SOURCE_COMMON_DAY_COM_BOT_BT_CONNECT_N_BMP,

    SOURCE_SETTING_DAY_SETTINGS_BT_N_BMP,
    SOURCE_SETTING_DAY_SETTINGS_BT_S_BMP,
    SOURCE_SETTING_DAY_SETTINGS_GEN_SET_N_BMP,
    SOURCE_SETTING_DAY_SETTINGS_GEN_SET_S_BMP,
    SOURCE_SETTING_DAY_SETTINGS_INFO_N_BMP,
    SOURCE_SETTING_DAY_SETTINGS_INFO_S_BMP,
    SOURCE_SETTING_DAY_SETTINGS_NETWORK_N_BMP,
    SOURCE_SETTING_DAY_SETTINGS_NETWORK_S_BMP,

    SOURCE_AUDIO_DAY_AUDIO_GEN_SET_N_BMP,
    SOURCE_AUDIO_DAY_AUDIO_GEN_SET_S_BMP,
    SOURCE_AUDIO_DAY_AUDIO_ZONE_AUDIO_N_BMP,
    SOURCE_AUDIO_DAY_AUDIO_ZONE_AUDIO_S_BMP,
    SOURCE_AUDIO_DAY_AUDIO_ZONE_SET_N_BMP,
    SOURCE_AUDIO_DAY_AUDIO_ZONE_SET_S_BMP,

    SOURCE_AUDIO_DAY_AUDIO_EQ_N_BMP,
    SOURCE_AUDIO_DAY_AUDIO_EQ_S_BMP,
    SOURCE_AUDIO_DAY_AUDIO_FB_N_BMP,
    SOURCE_AUDIO_DAY_AUDIO_FB_S_BMP,
    SOURCE_AUDIO_DAY_AUDIO_CO_N_BMP,
    SOURCE_AUDIO_DAY_AUDIO_CO_S_BMP,
    SOURCE_AUDIO_DAY_AUDIO_PEQ_A_N_BMP,
    SOURCE_AUDIO_DAY_AUDIO_PEQ_A_S_BMP,
    SOURCE_AUDIO_DAY_AUDIO_PEQ_B_N_BMP,
    SOURCE_AUDIO_DAY_AUDIO_PEQ_B_S_BMP,

    SOURCE_AUDIO_DAY_AUDIO_EQ_LEFT_N_BMP,
    SOURCE_AUDIO_DAY_AUDIO_EQ_LEFT_S_BMP,
    SOURCE_AUDIO_DAY_AUDIO_EQ_RIGHT_N_BMP,
    SOURCE_AUDIO_DAY_AUDIO_EQ_RIGHT_S_BMP,
    SOURCE_AUDIO_DAY_AUDIO_CO_UP_N_BMP,
    SOURCE_AUDIO_DAY_AUDIO_CO_DOWN_N_BMP,

    SOURCE_TEMP_DAY_DAY_COM_RIG_FUNC_ICON_BMP,   
	
	MAX_SOURCE_BMP_ITEM
};

static const __s32 lv_source_res_bmp[] =
{
    ID_TEMP_MEDIA_SETUP_REPEAT_C_BMP,
    ID_TEMP_COM_SOURCE_MEMU_N_BMP,   
    ID_TEMP_COM_TOP_BAR_USB_BMP,     
    ID_TEMP_COM_MEDIA_ARTIST_N_BMP,  
    ID_TEMP_COM_MEDIA_PG_BAR_BG_BMP, 
    ID_TEMP_MEDIA_SETUP_AUDIO_N_BMP, 
    ID_TEMP_ZONE_SUB_S_BMP,          
    ID_TEMP_ZONE_VOLUME_N_BMP,       
    ID_TEMP_MEDIA_SETUP_SEL_BG_S_BMP,
    ID_TEMP_COM_MEDIA_PLAY_BMP,      
    ID_TEMP_COM_MENU_BG_N_BMP,       
    ID_TEMP_COM_MENU_BG_S_BMP,       
    ID_TEMP_COM_MENU_ENTER_BMP,      
    ID_TEMP_MEDIA_SETUP_AUDIO_S_BMP, 
    ID_TEMP_MEDIA_SETUP_N_BMP,       
    ID_TEMP_COM_TOP_BAR_SXM_BMP,     
    ID_TEMP_COM_MEDIA_PLAYLISTS_N_BMP,   
    ID_TEMP_COM_MEDIA_PLAY_BG_S_P_BMP,   
    ID_TEMP_MEDIA_SETUP_BACK_N_BMP,  
    ID_TEMP_ZONE_OPEN_BG_N_BMP,      
    ID_TEMP_LOAD_S_BMP,              
    ID_TEMP_COM_MEDIA_PAUSE_BMP,     
    ID_TEMP_COM_MEDIA_DOWN_N_BMP,    
    ID_TEMP_COM_MEDIA_UP_DOWN_BG_P_BMP,  
    ID_TEMP_MEDIA_SETUP_RANDOM_A_BMP,
    ID_TEMP_COM_MEDIA_GENRES_N_BMP,  
    ID_TEMP_COM_SIG_A_BMP,           
    ID_TEMP_COM_MEDIA_COMPILATIONS_N_BMP,
    ID_TEMP_COM_SIG_B_BMP,           
    ID_TEMP_COM_MEDIA_PODCASTS_N_BMP,
    ID_TEMP_COM_MEDIA_ALBUM_N_BMP,   
    ID_TEMP_MEDIA_SETUP_RANDOM_N_BMP,
    ID_TEMP_COM_TOP_BAR_P_BMP,       
    ID_TEMP_MEDIA_SETUP_BAR_BG_BMP,  
    ID_TEMP_COM_SOURCE_MEMU_S_BMP,   
    ID_TEMP_MEDIA_SETUP_REPEAT_B_BMP,
    ID_TEMP_COM_MEDIA_ALBUM_BG_BMP,  
    ID_TEMP_LOAD_N_BMP,              
    ID_TEMP_COM_MEDIA_PLAY_BG_BMP,   
    ID_TEMP_COM_MEDIA_BOOK_N_BMP,    
    ID_TEMP_COM_MEDIA_UP_N_BMP,      
    ID_TEMP_COM_SIG_C_BMP,           
    ID_TEMP_MEDIA_SETUP_REPEAT_N_BMP,
    ID_TEMP_COM_MEDIA_PG_BAR_S_BMP,  
    ID_TEMP_COM_MEDIA_SONG_N_BMP,    
    ID_TEMP_COM_MEDIA_UP_S_BMP,      
    ID_TEMP_COM_MEDIA_DOWN_S_BMP,    
    ID_TEMP_COM_SOURCE_N_BMP,        
    ID_TEMP_MEDIA_SETUP_REPEAT_A_BMP,
    ID_TEMP_COM_SIG_D_BMP,           
    ID_TEMP_MEDIA_SETUP_BACK_S_BMP,  
    ID_TEMP_COM_SOURCE_S_BMP,        
    ID_TEMP_ZONE_OPEN_S_BMP,         
    ID_TEMP_MEDIA_SETUP_RANDOM_S_BMP,
    ID_TEMP_MEDIA_SETUP_RANDOM_B_BMP,
    ID_TEMP_MEDIA_SETUP_S_BMP,       
    ID_TEMP_ZONE_OPEN_N_BMP,         
    ID_TEMP_MEDIA_SETUP_REPEAT_S_BMP,
    ID_TEMP_COM_MEDIA_SONG_S_BMP,    
    ID_TEMP_ZONE_VOLUME_S_BMP,       
    ID_TEMP_COM_MEDIA_COMPOSERS_N_BMP,   
    ID_TEMP_COM_MENU_BG_P_BMP,       
    ID_TEMP_ZONE_SUB_N_BMP,          
    ID_TEMP_ZONE_OPEN_BG_P_BMP,  
    ID_TEMP_COM_BAND_SEL_N_BMP,          
    ID_TEMP_COM_BAND_SEL_S_BMP,

    ID_TEMP_NIGHT_NIG_MEDIA_SETUP_REPEAT_B_BMP,  
    ID_TEMP_NIGHT_NIG_FM_ICON_BMP,
    ID_TEMP_NIGHT_NIG_AM_ICON_BMP,
    ID_TEMP_NIGHT_NIG_WB_ICON_BMP,
    ID_TEMP_NIGHT_NIG_COM_LIST_OFF_BMP,          
    ID_TEMP_NIGHT_NIG_MEDIA_DUAL_PHONE_A_BMP,    
    ID_TEMP_NIGHT_NIG_COM_RIG_BG_A_BMP,          
    ID_TEMP_NIGHT_NIG_COM_RIG_ARROW_A_BMP,       
    ID_TEMP_NIGHT_NIG_MEDIA_SETUP_REPEAT_A_BMP,  
    ID_TEMP_NIGHT_NIG_COM_RIG_AUDIO_ICON_S_BMP,  
    ID_TEMP_NIGHT_NIG_COM_RIG_AUDIO_ICON_N_BMP,  
    ID_TEMP_NIGHT_NIG_MEDIA_DUAL_PHONE_B_BMP,    
    ID_TEMP_NIGHT_NIG_COM_TOP_BACK_ICON_BMP,     
    ID_TEMP_NIGHT_NIG_COM_MEDIA_ALBUM_BG_BMP,    
    ID_TEMP_NIGHT_NIG_MEDIA_SETUP_RANDOM_A_BMP,  
    ID_TEMP_NIGHT_NIG_COM_RIG_FUNC_ICON_S_BMP,   
    ID_TEMP_NIGHT_NIG_COM_RIG_ARROW_B_BMP,       
    ID_TEMP_NIGHT_NIG_COM_LOW_VOLTAGE_BMP,       
    ID_TEMP_NIGHT_NIG_COM_RIG_BG_C_BMP,          
    ID_TEMP_NIGHT_NIG_COM_TOP_ICON_BG_N_BMP,     
    ID_TEMP_NIGHT_NIG_COM_LIST_ON_BMP,           
    ID_TEMP_NIGHT_NIG_COM_LIST_BG_BMP,           
    ID_TEMP_NIGHT_NIG_COM_RIG_BG_B_BMP,          
    ID_TEMP_NIGHT_NIG_COM_RIG_ICON_BG_N_BMP,     
    ID_TEMP_NIGHT_NIG_MEDIA_SETUP_RANDOM_B_BMP,  
    ID_TEMP_NIGHT_NIG_COM_RIG_ZONE_ICON_BMP,     
    ID_TEMP_NIGHT_NIG_COM_RIG_ICON_BG_P_BMP,     
    ID_TEMP_NIGHT_NIG_COM_BG_BMP,                
    ID_TEMP_NIGHT_NIG_COM_PD_CHARGE_BMP,         
    ID_TEMP_NIGHT_NIG_MEDIA_SETUP_REPEAT_C_BMP,  
    ID_TEMP_NIGHT_NIG_COM_PUNCH_EQ_BMP,          
    ID_TEMP_NIGHT_NIG_COM_TOP_SOURCE_ICON_BMP,   
    ID_TEMP_NIGHT_NIG_COM_RIG_ARROW_C_BMP,       
    ID_TEMP_NIGHT_NIG_COM_TOP_BG_BMP,            
    ID_TEMP_NIGHT_NIG_COM_RIG_ZONE_ICON_S_BMP,   
    ID_TEMP_NIGHT_NIG_COM_RIG_ZONE_ICON_N_BMP,   
    ID_TEMP_NIGHT_NIG_COM_RIG_AUDIO_ICON_BMP,    
    ID_TEMP_NIGHT_NIG_COM_TOP_LIST_ICON_BMP,     
    ID_TEMP_NIGHT_NIG_MEDIA_DUAL_PHONE_D_BMP,    
    ID_TEMP_NIGHT_NIG_COM_RIG_FUNC_ICON_N_BMP,   
    ID_TEMP_NIGHT_NIG_COM_TOP_ICON_BG_P_S_BMP,  
    ID_TEMP_NIGHT_NIG_COM_RIG_ICON_N_BG_BMP,
    ID_TEMP_NIGHT_NIG_COM_RIG_ICON_BG_S_BMP,
    ID_TEMP_NIGHT_NIG_COM_RIG_FUNCTION_ICON_BMP,
    ID_TEMP_NIGHT_NIG_COM_RIG_FUNCTION_ICON_N_BMP,
    ID_TEMP_NIGHT_NIG_COM_RIG_LIST_ICON_BMP,
    ID_TEMP_NIGHT_NIG_COM_RIG_LIST_ICON_N_BMP,
    ID_TEMP_NIGHT_NIG_COM_TOP_NOWPLAY_ICON_BMP,
    ID_TEMP_NIGHT_NIG_COM_TOP_SETTINGS_ICON_BMP,
    ID_TEMP_NIGHT_NIG_COM_TOP_ZONE_ICON_BMP,
    ID_TEMP_NIGHT_NIG_COM_RIG_INFO_ICON_BMP,
    ID_TEMP_NIGHT_NIG_COM_RIG_INFO_ICON_N_BMP,
    ID_TEMP_NIGHT_NIG_COM_RIG_SYSTEM_ICON_BMP,
    ID_TEMP_NIGHT_NIG_COM_RIG_SYSTEM_ICON_N_BMP,

    ID_COMMON_NIG_COM_BOT_BG_BMP,  
    ID_COMMON_NIG_COM_BOT_ICON_BG_A_N_BMP,  
    ID_COMMON_NIG_COM_BOT_ICON_BG_A_P_S_BMP, 
    ID_COMMON_NIG_COM_BOT_ICON_BG_B_N_BMP,   
    ID_COMMON_NIG_COM_BOT_ICON_BG_B_P_S_BMP, 
    ID_COMMON_NIG_COM_BOT_MEDIA_PAUSE_BMP,   
    ID_COMMON_NIG_COM_BOT_MEDIA_PAUSE_D_BMP,
    ID_COMMON_NIG_COM_BOT_MEDIA_PLAY_BMP,    
    ID_COMMON_NIG_COM_BOT_MEDIA_PLAY_D_BMP,
    ID_COMMON_NIG_COM_BOT_MEDIA_DOWN_N_BMP,  
    ID_COMMON_NIG_COM_BOT_MEDIA_DOWN_P_S_BMP,
    ID_COMMON_NIG_COM_BOT_MEDIA_UP_N_BMP,    
    ID_COMMON_NIG_COM_BOT_MEDIA_UP_P_S_BMP,
    ID_COMMON_NIG_COM_BOT_BROWSE_N_BMP,
    ID_COMMON_NIG_COM_BOT_BROWSE_D_BMP,
    ID_COMMON_NIG_COM_BOT_BT_CONNECT_N_BMP,

    ID_SETTING_NIG_SETTINGS_BT_N_BMP,
    ID_SETTING_NIG_SETTINGS_BT_S_BMP,
    ID_SETTING_NIG_SETTINGS_GEN_SET_N_BMP,
    ID_SETTING_NIG_SETTINGS_GEN_SET_S_BMP,
    ID_SETTING_NIG_SETTINGS_INFO_N_BMP,
    ID_SETTING_NIG_SETTINGS_INFO_S_BMP,
    ID_SETTING_NIG_SETTINGS_NETWORK_N_BMP,
    ID_SETTING_NIG_SETTINGS_NETWORK_S_BMP,

    ID_AUDIO_NIG_AUDIO_GEN_SET_N_BMP,
    ID_AUDIO_NIG_AUDIO_GEN_SET_S_BMP,
    ID_AUDIO_NIG_AUDIO_ZONE_AUDIO_N_BMP,
    ID_AUDIO_NIG_AUDIO_ZONE_AUDIO_S_BMP,
    ID_AUDIO_NIG_AUDIO_ZONE_SET_N_BMP,
    ID_AUDIO_NIG_AUDIO_ZONE_SET_S_BMP,

    ID_AUDIO_NIG_AUDIO_EQ_N_BMP,
    ID_AUDIO_NIG_AUDIO_EQ_S_BMP,
    ID_AUDIO_NIG_AUDIO_FB_N_BMP,
    ID_AUDIO_NIG_AUDIO_FB_S_BMP,
    ID_AUDIO_NIG_AUDIO_CO_N_BMP,
    ID_AUDIO_NIG_AUDIO_CO_S_BMP,
    ID_AUDIO_NIG_AUDIO_PEQ_A_N_BMP,
    ID_AUDIO_NIG_AUDIO_PEQ_A_S_BMP,
    ID_AUDIO_NIG_AUDIO_PEQ_B_N_BMP,
    ID_AUDIO_NIG_AUDIO_PEQ_B_S_BMP,

    ID_AUDIO_NIG_AUDIO_EQ_LEFT_N_BMP,
    ID_AUDIO_NIG_AUDIO_EQ_LEFT_S_BMP,
    ID_AUDIO_NIG_AUDIO_EQ_RIGHT_N_BMP,
    ID_AUDIO_NIG_AUDIO_EQ_RIGHT_S_BMP,
    ID_AUDIO_NIG_AUDIO_CO_UP_N_BMP,
    ID_AUDIO_NIG_AUDIO_CO_DOWN_N_BMP,

    ID_TEMP_NIGHT_NIG_COM_RIG_FUNC_ICON_BMP, 
    
    ID_TEMP_DAY_DAY_MEDIA_SETUP_REPEAT_B_BMP,  
    ID_TEMP_DAY_DAY_FM_ICON_BMP,
    ID_TEMP_DAY_DAY_AM_ICON_BMP,
    ID_TEMP_DAY_DAY_WB_ICON_BMP,
    ID_TEMP_DAY_DAY_COM_LIST_OFF_BMP,          
    ID_TEMP_DAY_DAY_MEDIA_DUAL_PHONE_A_BMP,    
    ID_TEMP_DAY_DAY_COM_RIG_BG_A_BMP,          
    ID_TEMP_DAY_DAY_COM_RIG_ARROW_A_BMP,       
    ID_TEMP_DAY_DAY_MEDIA_SETUP_REPEAT_A_BMP,  
    ID_TEMP_DAY_DAY_COM_RIG_AUDIO_ICON_S_BMP,  
    ID_TEMP_DAY_DAY_COM_RIG_AUDIO_ICON_N_BMP,  
    ID_TEMP_DAY_DAY_MEDIA_DUAL_PHONE_B_BMP,    
    ID_TEMP_DAY_DAY_COM_TOP_BACK_ICON_BMP,     
    ID_TEMP_DAY_DAY_COM_MEDIA_ALBUM_BG_BMP,    
    ID_TEMP_DAY_DAY_MEDIA_SETUP_RANDOM_A_BMP,  
    ID_TEMP_DAY_DAY_COM_RIG_FUNC_ICON_S_BMP,   
    ID_TEMP_DAY_DAY_COM_RIG_ARROW_B_BMP,       
    ID_TEMP_DAY_DAY_COM_LOW_VOLTAGE_BMP,       
    ID_TEMP_DAY_DAY_COM_RIG_BG_C_BMP,          
    ID_TEMP_DAY_DAY_COM_TOP_ICON_BG_N_BMP,     
    ID_TEMP_DAY_DAY_COM_LIST_ON_BMP,           
    ID_TEMP_DAY_DAY_COM_LIST_BG_BMP,           
    ID_TEMP_DAY_DAY_COM_RIG_BG_B_BMP,          
    ID_TEMP_DAY_DAY_COM_RIG_ICON_BG_N_BMP,     
    ID_TEMP_DAY_DAY_MEDIA_SETUP_RANDOM_B_BMP,  
    ID_TEMP_DAY_DAY_COM_RIG_ZONE_ICON_BMP,     
    ID_TEMP_DAY_DAY_COM_RIG_ICON_BG_P_BMP,     
    ID_TEMP_DAY_DAY_COM_BG_BMP,                
    ID_TEMP_DAY_DAY_COM_PD_CHARGE_BMP,         
    ID_TEMP_DAY_DAY_MEDIA_SETUP_REPEAT_C_BMP,  
    ID_TEMP_DAY_DAY_COM_PUNCH_EQ_BMP,          
    ID_TEMP_DAY_DAY_COM_TOP_SOURCE_ICON_BMP,   
    ID_TEMP_DAY_DAY_COM_RIG_ARROW_C_BMP,       
    ID_TEMP_DAY_DAY_COM_TOP_BG_BMP,            
    ID_TEMP_DAY_DAY_COM_RIG_ZONE_ICON_S_BMP,   
    ID_TEMP_DAY_DAY_COM_RIG_ZONE_ICON_N_BMP,   
    ID_TEMP_DAY_DAY_COM_RIG_AUDIO_ICON_BMP,    
    ID_TEMP_DAY_DAY_COM_TOP_LIST_ICON_BMP,     
    ID_TEMP_DAY_DAY_MEDIA_DUAL_PHONE_D_BMP,    
    ID_TEMP_DAY_DAY_COM_RIG_FUNC_ICON_N_BMP,   
    ID_TEMP_DAY_DAY_COM_TOP_ICON_BG_P_S_BMP,   
    ID_TEMP_DAY_DAY_COM_RIG_ICON_N_BG_BMP,
    ID_TEMP_NIGHT_NIG_COM_RIG_ICON_BG_S_BMP,
    ID_TEMP_DAY_DAY_COM_RIG_FUNCTION_ICON_BMP,
    ID_TEMP_DAY_DAY_COM_RIG_FUNCTION_ICON_N_BMP,
    ID_TEMP_DAY_DAY_COM_RIG_LIST_ICON_BMP,
    ID_TEMP_DAY_DAY_COM_RIG_LIST_ICON_N_BMP,
    ID_TEMP_DAY_DAY_COM_TOP_NOWPLAY_ICON_BMP,
    ID_TEMP_DAY_DAY_COM_TOP_SETTINGS_ICON_BMP,
    ID_TEMP_DAY_DAY_COM_TOP_ZONE_ICON_BMP,
    ID_TEMP_DAY_DAY_COM_RIG_INFO_ICON_BMP,
    ID_TEMP_DAY_DAY_COM_RIG_INFO_ICON_N_BMP,
    ID_TEMP_DAY_DAY_COM_RIG_SYSTEM_ICON_BMP,
    ID_TEMP_DAY_DAY_COM_RIG_SYSTEM_ICON_N_BMP,

    ID_COMMON_DAY_COM_BOT_BG_BMP,  
    ID_COMMON_DAY_COM_BOT_ICON_BG_A_N_BMP,  
    ID_COMMON_DAY_COM_BOT_ICON_BG_A_P_S_BMP, 
    ID_COMMON_DAY_COM_BOT_ICON_BG_B_N_BMP,   
    ID_COMMON_DAY_COM_BOT_ICON_BG_B_P_S_BMP, 
    ID_COMMON_DAY_COM_BOT_MEDIA_PAUSE_BMP,   
    ID_COMMON_DAY_COM_BOT_MEDIA_PAUSE_D_BMP,
    ID_COMMON_DAY_COM_BOT_MEDIA_PLAY_BMP,    
    ID_COMMON_DAY_COM_BOT_MEDIA_PLAY_D_BMP,
    ID_COMMON_DAY_COM_BOT_MEDIA_DOWN_N_BMP,  
    ID_COMMON_DAY_COM_BOT_MEDIA_DOWN_P_S_BMP,
    ID_COMMON_DAY_COM_BOT_MEDIA_UP_N_BMP,    
    ID_COMMON_DAY_COM_BOT_MEDIA_UP_P_S_BMP,
    ID_COMMON_DAY_COM_BOT_BROWSE_N_BMP,
    ID_COMMON_DAY_COM_BOT_BROWSE_D_BMP,
    ID_COMMON_DAY_COM_BOT_BT_CONNECT_N_BMP,

    ID_SETTING_DAY_SETTINGS_BT_N_BMP,
    ID_SETTING_DAY_SETTINGS_BT_S_BMP,
    ID_SETTING_DAY_SETTINGS_GEN_SET_N_BMP,
    ID_SETTING_DAY_SETTINGS_GEN_SET_S_BMP,
    ID_SETTING_DAY_SETTINGS_INFO_N_BMP,
    ID_SETTING_DAY_SETTINGS_INFO_S_BMP,
    ID_SETTING_DAY_SETTINGS_NETWORK_N_BMP,
    ID_SETTING_DAY_SETTINGS_NETWORK_S_BMP,

    ID_AUDIO_DAY_AUDIO_GEN_SET_N_BMP,
    ID_AUDIO_DAY_AUDIO_GEN_SET_S_BMP,
    ID_AUDIO_DAY_AUDIO_ZONE_AUDIO_N_BMP,
    ID_AUDIO_DAY_AUDIO_ZONE_AUDIO_S_BMP,
    ID_AUDIO_DAY_AUDIO_ZONE_SET_N_BMP,
    ID_AUDIO_DAY_AUDIO_ZONE_SET_S_BMP,

    ID_AUDIO_DAY_AUDIO_EQ_N_BMP,
    ID_AUDIO_DAY_AUDIO_EQ_S_BMP,
    ID_AUDIO_DAY_AUDIO_FB_N_BMP,
    ID_AUDIO_DAY_AUDIO_FB_S_BMP,
    ID_AUDIO_DAY_AUDIO_CO_N_BMP,
    ID_AUDIO_DAY_AUDIO_CO_S_BMP,
    ID_AUDIO_DAY_AUDIO_PEQ_A_N_BMP,
    ID_AUDIO_DAY_AUDIO_PEQ_A_S_BMP,
    ID_AUDIO_DAY_AUDIO_PEQ_B_N_BMP,
    ID_AUDIO_DAY_AUDIO_PEQ_B_S_BMP,

    ID_AUDIO_DAY_AUDIO_EQ_LEFT_N_BMP,
    ID_AUDIO_DAY_AUDIO_EQ_LEFT_S_BMP,
    ID_AUDIO_DAY_AUDIO_EQ_RIGHT_N_BMP,
    ID_AUDIO_DAY_AUDIO_EQ_RIGHT_S_BMP,
    ID_AUDIO_DAY_AUDIO_CO_UP_N_BMP,
    ID_AUDIO_DAY_AUDIO_CO_DOWN_N_BMP,

    ID_TEMP_DAY_DAY_COM_RIG_FUNC_ICON_BMP,   
};

typedef struct source_res_para
{
	HTHEME lv_source_bmp[MAX_SOURCE_BMP_ITEM];
	lv_img_dsc_t lv_source_icon[MAX_SOURCE_BMP_ITEM];

}ui_source_res_para_t;

enum
{
	SOURCE_MENU_WIN_NONE = 0,
	SOURCE_MENU_WIN_LIST,
	SOURCE_MENU_WIN_ZONE,
	SOURCE_MENU_WIN_GENERAL_SETTING,
	SOURCE_MENU_WIN_BRIGHTNESS_DIMMER,
	SOURCE_MENU_WIN_BT_SETTING,
	SOURCE_MENU_WIN_BT_NAME,
	SOURCE_MENU_WIN_DEVICE_LIST,
	SOURCE_MENU_WIN_NETWORK,
	SOURCE_MENU_WIN_MY_DEVICE,
	SOURCE_MENU_WIN_DEVICE_INFO,//10
	SOURCE_MENU_WIN_GENERAL_AUDIO,
	SOURCE_MENU_WIN_GENERAL_AUDIO_SOURCE_GAIN,
	SOURCE_MENU_WIN_ZONE_AUDIO_MENU,
	SOURCE_MENU_WIN_ZONE_SETTINGS,
	SOURCE_MENU_WIN_ZONE_AUDIO,
	SOURCE_MENU_WIN_INFO,
	SOURCE_MENU_WIN_INFO_FW_UPDATE,
	SOURCE_MENU_WIN_INFO_SOFTWARE,
	SOURCE_MENU_WIN_INFO_OTA,
	SOURCE_MENU_WIN_FUNC,//20
	SOURCE_MENU_WIN_MODE,

	SOURCE_MENU_MAX
};

enum
{
	SOURCE_MENU_GENERAL_VOLTAGE_GAUGE = 0,
	SOURCE_MENU_GENERAL_THEME,
	SOURCE_MENU_GENERAL_AUTO_BRIGHTNESS,
	SOURCE_MENU_GENERAL_DIMMER_BRIGHTNESS,
	SOURCE_MENU_GENERAL_RENAME,
	SOURCE_MENU_GENERAL_AUXIN,
	SOURCE_MENU_GENERAL_LANGUAGE,
	SOURCE_MENU_GENERAL_RADIO_REGION,
	SOURCE_MENU_GENERAL_BEEP,
	SOURCE_MENU_GENERAL_FACTORY_DEFAULT,
	
	SOURCE_MENU_GENERAL_MAX,
};

enum
{
	SOURCE_MENU_BT_ONOFF = 0,
	SOURCE_MENU_BT_NAME,
	SOURCE_MENU_BT_DEVICE_LIST,
	SOURCE_MENU_BT_ADD,

	SOURCE_MENU_BT_MAX
};

enum
{
	SOURCE_MENU_MY_DEVICE_ASSIGN = 0,
	SOURCE_MENU_MY_DEVICE_INFO,

	SOURCE_MENU_MY_DEVICE_MAX
};

enum
{
	SOURCE_MENU_DEVICE_INFO_BRAND = 0,
	SOURCE_MENU_DEVICE_INFO_MODEL,
	SOURCE_MENU_DEVICE_INFO_SERIAL,
	SOURCE_MENU_DEVICE_INFO_FW_REL,

	SOURCE_MENU_DEVICE_INFO_MAX
};

enum
{
	SOURCE_MENU_AUDIO_GENERAL_DEFEAT = 0,
	SOURCE_MENU_AUDIO_GENERAL_SOURCE_GAIN,
	SOURCE_MENU_AUDIO_GENERAL_VOLUME_OFFSET,
	SOURCE_MENU_AUDIO_GENERAL_PHONE_VOL_SYNC,
	SOURCE_MENU_AUDIO_GENERAL_SUBWOOFER,
	SOURCE_MENU_AUDIO_GENERAL_PHASE,
	SOURCE_MENU_AUDIO_GENERAL_RESET,

	SOURCE_MENU_AUDIO_GENERAL_MAX
};

enum
{
	SOURCE_MENU_PHONE_VOL_SYNC_ALL = 0,
	SOURCE_MENU_PHONE_VOL_SYNC_ZONE1,
	SOURCE_MENU_PHONE_VOL_SYNC_ZONE2,

	SOURCE_MENU_PHONE_VOL_SYNC_MAX
};

enum
{
	SOURCE_MENU_AUDIO_GENERAL = 0,
	SOURCE_MENU_AUDIO_ZONE_AUDIO,
	SOURCE_MENU_AUDIO_ZONE_SETTINGS,

	SOURCE_MENU_AUDIO_MAX
};

enum
{
	SOURCE_MENU_ZONE_AUDIO_7_BAND_EQ = 0,
	SOURCE_MENU_ZONE_AUDIO_FADER_BALANCE,
	SOURCE_MENU_ZONE_AUDIO_CROSSOVER,
	SOURCE_MENU_ZONE_AUDIO_PUNCH_EQ,

	SOURCE_MENU_ZONE_AUDIO_MAX
};

enum
{
	SOURCE_MENU_ZONE_SETTINGS_ZONE2 = 0,
	SOURCE_MENU_ZONE_SETTINGS_ASSIGN,

	SOURCE_MENU_ZONE_SETTINGS_MAX
};

enum
{
	SOURCE_MENU_AUDIO_ZONE_1 = 0,
	SOURCE_MENU_AUDIO_ZONE_2,

	SOURCE_MENU_AUDIO_ZONE_MAX
};

enum
{
	SOURCE_MENU_INFO_SOFTWARE = 0,
	SOURCE_MENU_INFO_FW_UPDATE,
	SOURCE_MENU_INFO_UPDATE_USB,
	SOURCE_MENU_INFO_OTA,

	SOURCE_MENU_INFO_MAX,
};

enum
{
	SOURCE_MENU_INFO_BT_SWITCH = 0,
	SOURCE_MENU_INFO_BT_NAME,
	SOURCE_MENU_INFO_BT_ADD,
	
	SOURCE_MENU_INFO_BT_MAX,
};

enum
{
    SOURCE_MENU_INFO_SOFTWARW_BRAND = 0,
	SOURCE_MENU_INFO_SOFTWARW_MDL,
	SOURCE_MENU_INFO_SOFTWARW_SN,
	SOURCE_MENU_INFO_SOFTWARW_FW_REL_DATE,
	SOURCE_MENU_INFO_SOFTWARW_MCU,
	SOURCE_MENU_INFO_SOFTWARW_SXM,
	SOURCE_MENU_INFO_SOFTWARW_DEC,
	SOURCE_MENU_INFO_SOFTWARW_LEA,
	SOURCE_MENU_INFO_SOFTWARW_GUI,

	SOURCE_MENU_INFO_SOFTWARW_MAX,
};

enum
{
    SOURCE_MENU_INFO_SOFTWARW_RMT_BRAND = 0,
	SOURCE_MENU_INFO_SOFTWARW_RMT_MDL,
	SOURCE_MENU_INFO_SOFTWARW_RMT_SN,
	SOURCE_MENU_INFO_SOFTWARW_RMT_FW_REL_DATE,
	SOURCE_MENU_INFO_SOFTWARW_RMT_MCU,
	SOURCE_MENU_INFO_SOFTWARW_RMT_GUI,

	SOURCE_MENU_INFO_SOFTWARW_RMT_MAX,
};

enum
{
	SOURCE_ITEM_TYPE_NONE = 0,
	SOURCE_ITEM_TYPE_ENTER,
	SOURCE_ITEM_TYPE_ARROW,
	SOURCE_ITEM_TYPE_TEXT,
	SOURCE_ITEM_TYPE_TEXT_ENTER,
	SOURCE_ITEM_TYPE_IOCN,
	SOURCE_ITEM_TYPE_ROLLER,
	SOURCE_ITEM_TYPE_BT_DEVICE,
	SOURCE_ITEM_TYPE_WIRELESS_DEVICE,
	SOURCE_ITEM_TYPE_WIFI_LIST,
	SOURCE_ITEM_TYPE_DEVICE_LIST,
	SOURCE_ITEM_TYPE_NEW_WIFI_LIST,
	SOURCE_ITEM_TYPE_NEW_WIFI_CONNECT,
	SOURCE_ITEM_TYPE_PRESET,
	SOURCE_ITEM_TYPE_MUSIC,
	SOURCE_ITEM_TYPE_MAX
};

enum
{
	SOURCE_ITEM_TEXT_TYPE_ARROW = 0,
	SOURCE_ITEM_TEXT_TYPE_TEXT,
	SOURCE_ITEM_TEXT_TYPE_CONTENT,
    SOURCE_ITEM_TEXT_TYPE_ROLLER,

	SOURCE_ITEM_TEXT_TYPE
};

enum
{
	SOURCE_NONE = 0,
	SOURCE_TOP,
	SOURCE_MID_TOP,
	SOURCE_MID_BOTTOM,
	SOURCE_BOTTOM
};

enum
{
	SOURCE_RIG_TYPE_MENU = 0,
	SOURCE_RIG_TYPE_SETTING
};

enum
{
	SOURCE_ZONE_NONE = 0,
	SOURCE_ZONE_ALL,
	SOURCE_ZONE_1,
	SOURCE_ZONE_2,
	SOURCE_ZONE_SUBWOOFER
};

enum
{
	SOURCE_BOTTOM_PLAY_NONE = 0,
	SOURCE_BOTTOM_PLAY,
	SOURCE_BOTTOM_PAUSE,
};

enum
{
	SOURCE_MENU_GROUP_NONE = 0,
	SOURCE_MENU_GROUP_ADD,
	SOURCE_MENU_GROUP_ADD_FLAG,
	SOURCE_MENU_GROUP_REMOVE,
	SOURCE_MENU_GROUP_REMOVE_FLAG,
	SOURCE_MENU_GROUP_ADD_SUB,
	SOURCE_MENU_GROUP_ADD_SUB_FLAG,
	SOURCE_MENU_GROUP_REMOVE_SUB,
	SOURCE_MENU_GROUP_REMOVE_ALL,
	SOURCE_MENU_GROUP_REMOVE_ALL_FLAG,
	SOURCE_MENU_GROUP_REMOVE_ALL_FLAG_ALL,
};

enum
{
	SOURCE_INDICANT_NONE = 0,
	SOURCE_INDICANT_SCAN,
	SOURCE_INDICANT_SEARCH,
	SOURCE_INDICANT_ADD,

	SOURCE_INDICANT_MAX
};

//source eq para
enum
{
	BAND_EQ_80HZ = 0,
	BAND_EQ_250HZ,
	BAND_EQ_500HZ,
	BAND_EQ_1KHZ,
	BAND_EQ_2KHZ,
	BAND_EQ_4KHZ,
	BAND_EQ_10KHZ,

	BAND_EQ_MAX
};

typedef struct source_eq_band_para_t
{  
    lv_obj_t * slider_big;
    lv_obj_t * slider;
    int32_t value;
    int32_t left_value;
    int32_t value_max;
    int32_t value_min;
}ui_source_eq_band_para_t;

typedef struct source_eq_para_t
{  
    ui_source_eq_band_para_t eq_band_para[BAND_EQ_MAX];
    lv_point_precise_t eq_band_line_points[BAND_EQ_MAX];
    __s32 eq_band_value[EQ_EQ_MAX][BAND_EQ_MAX];
    __s8 eq_mode;
    lv_draw_buf_t * eq_line_draw_buf;
    __u8 fouse_index;
}ui_source_eq_para_t;


//source fader&balance para
typedef struct source_fader_balance_para_t
{  
    int32_t dot_w;
    int32_t dot_h;
    int32_t dot_shadow_w;
    lv_area_t dot_coords;
    lv_point_t dot_p;
    lv_point_t zone2_dot_p;
    int32_t grid_w;
    int32_t grid_h;
    lv_area_t grid_coords;
    __u32 fader_vaule_max;
    __u32 balance_vaule_max;
    __s32 fader_vaule;
    __s32 balance_vaule;

    bool grid_para_flag;
    __u8 fouse_index;
}ui_source_fader_balance_para_t;


//source crossover para
enum
{
	CROSSOVER_MODE_FRONT = 0,
	CROSSOVER_MODE_REAR,
	CROSSOVER_MODE_SUBWOOFER,

	CROSSOVER_MODE_MAX
};

enum
{
	CROSSOVER_ADJUST_TYPE = 0,
	CROSSOVER_ADJUST_FREQ,
	CROSSOVER_ADJUST_SLOPE,

	CROSSOVER_ADJUST_MAX
};

enum
{
    CROSSOVER_ADJUST_TYPE_APF = 0,
	CROSSOVER_ADJUST_TYPE_HPF,
	CROSSOVER_ADJUST_TYPE_LPF,

	CROSSOVER_ADJUST_TYPE_MAX
};

enum
{
	CROSSOVER_ADJUST_FREQ_30HZ = 0,
	CROSSOVER_ADJUST_FREQ_40HZ,
	CROSSOVER_ADJUST_FREQ_50HZ,
	CROSSOVER_ADJUST_FREQ_60HZ,
	CROSSOVER_ADJUST_FREQ_70HZ,
	CROSSOVER_ADJUST_FREQ_80HZ,
	CROSSOVER_ADJUST_FREQ_90HZ,
	CROSSOVER_ADJUST_FREQ_100HZ,
	CROSSOVER_ADJUST_FREQ_120HZ,
	CROSSOVER_ADJUST_FREQ_150HZ,
	CROSSOVER_ADJUST_FREQ_180HZ,
	CROSSOVER_ADJUST_FREQ_220HZ,
	CROSSOVER_ADJUST_FREQ_250HZ,

	CROSSOVER_ADJUST_FREQ_MAX
};

enum
{
	CROSSOVER_ADJUST_SLOPE_12 = 0,
	CROSSOVER_ADJUST_SLOPE_24,

	CROSSOVER_ADJUST_SLOPE_MAX
};

typedef struct source_crossover_adjust_para_t
{  
    lv_obj_t * ui_adjust;
    lv_obj_t * ui_adjust_text;
    lv_obj_t * ui_adjust_sel;
    lv_obj_t * ui_adjust_sel_prev;
    lv_obj_t * ui_adjust_sel_text_panel;
    lv_obj_t * ui_adjust_sel_text;
    lv_obj_t * ui_adjust_sel_next;
}ui_source_crossover_adjust_para_t;

typedef struct source_crossover_mode_para_t
{  
    __s8 crossover_type;
    __s8 crossover_freq;
    __s8 crossover_freq_min;
    __s8 crossover_freq_max;
    __u16 crossover_freq_buf[CROSSOVER_ADJUST_FREQ_MAX];
    __s8 crossover_slope;
    __s8 crossover_slope_min;
    __s8 crossover_slope_max;
    __u16 crossover_slope_buf[CROSSOVER_ADJUST_SLOPE_MAX];
}ui_source_crossover_mode_para_t;

typedef struct source_crossover_para_t
{  
    ui_source_crossover_adjust_para_t crossover_adjust_para[CROSSOVER_ADJUST_MAX];
    ui_source_crossover_mode_para_t crossover_mode_para[CROSSOVER_MODE_MAX];
    __s8 crossover_mode;
    lv_chart_series_t* ser_front;
    lv_chart_series_t* ser_rear;
    lv_chart_series_t* ser_sub;

    lv_draw_buf_t* draw_buf;
    __u8 fouse_index;
}ui_source_crossover_para_t;



//source punch eq para
typedef struct source_punch_eq_para_t
{  
    int32_t value_max;
    int32_t value_min;
    int32_t value;
    __u8 fouse_index;
}ui_source_punch_eq_para_t;


//source audio zone para
enum
{
	SOURCE_ZONE_EQ = 0,
	SOURCE_ZONE_FADER_BALANCE,
	SOURCE_ZONE_CROSSOVER,
	SOURCE_ZONE_PUNCH_EQ,

    SOURCE_ZONE_MAX
};

typedef struct source_audio_zone_para_t
{  
	__s8 audio_zone_cur_tab;
    __s8 zone_index;
}ui_source_audio_zone_para_t;


//source gian para
#define SOURCE_GAIN_VALUE_MIN     (-3)
#define SOURCE_GAIN_VALUE_MAX     (3)

enum
{
	SOURCE_GAIN_FM = 0,
	SOURCE_GAIN_AM,
	SOURCE_GAIN_WB,
	SOURCE_GAIN_USB,
	SOURCE_GAIN_BT,
	SOURCE_GAIN_SXM,
	SOURCE_GAIN_AUX,
	//SOURCE_GAIN_CAMERA,
	SOURCE_GAIN_PARTY,
	SOURCE_GAIN_ALL,

    SOURCE_GAIN_MAX
};

typedef struct gain_para_t
{  
    lv_obj_t * source_panel;
    lv_obj_t * source_text;
    lv_obj_t * source_slider_big;
    lv_obj_t * source_slider;
    lv_obj_t * source_value;
    int32_t gain_value;
    lv_grad_dsc_t grad;
    int32_t value;
    int32_t left_value;
    int32_t value_max;
    int32_t value_min;
    char * str;
    lv_slider_mode_t mode;
}ui_gain_para_t;

typedef struct source_gain_para_t
{  
    ui_gain_para_t gain_para[SOURCE_GAIN_MAX];
}ui_source_gain_para_t;


//source vol para
enum
{
	VOL_ALL = 0,
	VOL_ZONE1,
	VOL_ZONE2,
	VOL_SUB,

    VOL_MAX
};

typedef struct vol_para_t
{  
    lv_obj_t * vol_panel;
    lv_obj_t * vol_text_panel;
    lv_obj_t * vol_text_mode;
    lv_obj_t * vol_text_val;
    lv_obj_t * vol_slider_panel;
    lv_obj_t * vol_slider_big;
    lv_obj_t * vol_slider;
    int32_t value;
    int32_t left_value;
    int32_t value_max;
    int32_t value_min;
    lv_slider_mode_t mode;
    char * str;
    int32_t vol_value;
}ui_vol_para_t;

typedef struct source_vol_para_t
{  
    ui_vol_para_t vol_para[VOL_MAX];
	__u8 mute;
}ui_source_vol_para_t;


//source setting para
enum
{
	LANG_ENGILSH = 0,
	LANG_SPANISH,
	LANG_FRENCH,
	LANG_KOREAN,
	LANG_JAPANESE,

    LANG_MAX
};
    
enum
{
	REG_USA = 0,
	REG_EUROPE,
	REG_JAPAN,
	REG_AUSTRALASIA,

    REG_MAX
};

enum
{
	BRIGHTNESS_MENU_LOW = 0,
	BRIGHTNESS_MENU_MID,
	BRIGHTNESS_MENU_HIGHT,

    BRIGHTNESS_MENU_MAX
};

enum
{
	THEME_MENU_DAY = 0,
	THEME_MENU_NIGHT,

    THEME_MENU_MAX
};

typedef struct source_setting_para_t
{  
    __u8 language;
    __u8 tuner_region;
    __u8 brightness;
	__u8 dimmer_on_brightness;
	__u8 dimmer_off_brightness;
    __u8 theme;
    __u8 aux_in_onoff;
    __u8 beep_onoff;
}ui_source_setting_para_t;


//source audio para
enum
{
	PHASE_0 = 0,
	PHASE_180,

    PHASE_MAX
};


typedef struct source_audio_para_t
{  
    __u8 zone1_on_off;
    char zone1_name[128];
    
    __u8 zone2_on_off;
    char zone2_name[128];

	__u8 favorite_zone;

    __u8 defeat_on_off;
    __u8 phone_vol_sync;
    __u8 vol_offset_on_off;
    __u8 subwoofer_off;
  	__u8 subwoofer_phase;

    char aux_input_name[128];
    __u8 aux_input_on_off;

    __u8 audio_cur_tab;
    __u8 zone_audio_index;
    __u8 audio_index;

    __u8 fouse_index;
}ui_source_audio_para_t;


//source bluetooth para
typedef struct source_bluetooth_para_t
{  
    __u8 on_off;
    char bt_name[128];
    char bt_add[128];
}ui_source_bluetooth_para_t;

//source bluetooth para
typedef struct source_network_para_t
{  
    __u8 favorite_zone;
}ui_source_network_para_t;

//source party mode para
typedef struct {
	char name[32];
	__u8 address[6];
	__u8 connect_status; //0: disconnected 1: connected 2: connecting
}device_info_t;

typedef struct {
	__u8 broadcasting_active; //reserved for broadcating or stop
	__u8 source;
    __u8 broadcast_source;
}broadcast_t;

typedef struct {
	//connected device info
	__u8 connect_status; 
	device_info_t connected_device;
	//known list
	__u8 known_count;
	device_info_t known_list[16];
	//scan list
	__u8 scan_count;
	device_info_t scan_list[16];
	//playback id3
	
}receiver_t;

typedef struct {
	__u8 switch_status; // on/off status
	__u8 role; // broadcast or receiver
	device_info_t own_info; //own name and bda
	broadcast_t bc_info; //broadcast info
	receiver_t rc_info; //receiver info
}party_info_t;

//source para
typedef struct source_para
{  
	lv_obj_t * ui_source;
	lv_obj_t * ui_source_panel;

	lv_obj_t * ui_top;
    lv_obj_t * ui_top_left;
    lv_obj_t * ui_top_zone_sel_panel;
    lv_obj_t * ui_top_zone_1_sel;
    lv_obj_t * ui_top_zone_1_sel_label;
    lv_obj_t * ui_top_zone_2_sel;
    lv_obj_t * ui_top_zone_2_sel_label;
    lv_obj_t * ui_top_logo;
    lv_obj_t * ui_top_logo_text;
    lv_obj_t * ui_top_dialog_text;
    lv_obj_t * ui_top_right;
    lv_obj_t * ui_top_indicant_panel;
    lv_obj_t * ui_top_indicant_scan;
    lv_obj_t * ui_top_indicant_search;
    lv_obj_t * ui_top_indicant_add;

    lv_obj_t * ui_play;
    
	lv_obj_t * ui_rig;
    lv_obj_t * ui_rig_bar;
    lv_obj_t * ui_rig_bar_bg;
    lv_obj_t * ui_rig_bar_bg_arrow;

	lv_obj_t * ui_rig_panel;
    
    lv_obj_t * ui_rig_panel_bar;
    //lv_obj_t * ui_rig_panel_bar_img;
    lv_obj_t * ui_rig_panel_button;
    lv_obj_t * ui_rig_panel_button_top;
    lv_obj_t * ui_rig_panel_button_top_img;
    lv_obj_t * ui_rig_panel_button_mid_top;
    lv_obj_t * ui_rig_panel_button_mid_top_img;
    lv_obj_t * ui_rig_panel_button_mid_bottom;
    lv_obj_t * ui_rig_panel_button_mid_bottom_img;
    lv_obj_t * ui_rig_panel_button_bottom;
    lv_obj_t * ui_rig_panel_button_bottom_img;

    lv_obj_t * ui_rig_panel_content;
    
    lv_obj_t * ui_rig_top_panel;
	lv_obj_t * ui_rig_list_panel;
	lv_obj_t * ui_rig_general_panel;
    lv_obj_t * ui_dimmer;
    lv_obj_t * ui_dimmer_on;
    lv_obj_t * ui_dimmer_on_button;
    lv_obj_t * ui_dimmer_on_button_text;
    lv_obj_t * ui_dimmer_on_ctrl_val;
    lv_obj_t * ui_dimmer_on_ctrl;
    lv_obj_t * ui_dimmer_on_ctrl_slider_big;
    lv_obj_t * ui_dimmer_on_ctrl_slider;
    
    lv_obj_t * ui_dimmer_off;
    lv_obj_t * ui_dimmer_off_button;
    lv_obj_t * ui_dimmer_off_button_text;
    lv_obj_t * ui_dimmer_off_ctrl_val;
    lv_obj_t * ui_dimmer_off_ctrl;
    lv_obj_t * ui_dimmer_off_ctrl_slider_big;
    lv_obj_t * ui_dimmer_off_ctrl_slider;

	lv_obj_t * ui_rig_mid_top_panel;
    lv_obj_t * ui_rig_mid_bottom_panel;

	lv_obj_t * ui_rig_bottom_panel;
	lv_obj_t * ui_rig_func_panel;
	lv_obj_t * ui_rig_info_panel;
    
	lv_obj_t * ui_rig_audio_setting_panel;
    lv_obj_t * ui_source_gain;

    lv_obj_t * ui_rig_zone_panel;
    lv_obj_t * ui_rig_zone_panel_1;
    lv_obj_t * ui_rig_zone_panel_2;

    lv_obj_t * ui_bottom;
    #if 0
    lv_obj_t * ui_bottom_up;
    lv_obj_t * ui_bottom_up_bg;
    lv_obj_t * ui_bottom_up_key;
    lv_obj_t * ui_bottom_play;
    lv_obj_t * ui_bottom_play_bg;
    lv_obj_t * ui_bottom_play_key;
    lv_obj_t * ui_bottom_down;
    lv_obj_t * ui_bottom_down_bg;
    lv_obj_t * ui_bottom_down_key;
    #endif
    
    lv_obj_t * ui_stripe;
    lv_obj_t * ui_stripe_bg1;
    lv_obj_t * ui_stripe_bg2;
    lv_obj_t * ui_stripe_panel;
    lv_obj_t * ui_stripe_panel1;
    lv_obj_t * ui_stripe_panel2;
    lv_obj_t * ui_stripe_panel3;
    lv_obj_t * ui_stripe_panel4;
    lv_obj_t * ui_stripe_panel5;

    lv_obj_t * ui_audio;
    lv_obj_t * ui_audio_tabview;
    lv_obj_t * ui_audio_tab_general;
    lv_obj_t * ui_audio_general;
    lv_obj_t * ui_audio_tab_audio;
    lv_obj_t * ui_audio_audio;
    lv_obj_t * ui_audio_tab_settings;
    lv_obj_t * ui_audio_settings;

    lv_obj_t * ui_zone_audio;
    lv_obj_t * ui_zone_audio_tabview;
    lv_obj_t * ui_zone_audio_tab_7band_eq;
    lv_obj_t * ui_zone_audio_tab_7band_eq_panel;
    lv_obj_t * ui_zone_audio_tab_7band_eq_db_panel;
    lv_obj_t * ui_zone_audio_tab_7band_eq_db_max;
    lv_obj_t * ui_zone_audio_tab_7band_eq_db_0;
    lv_obj_t * ui_zone_audio_tab_7band_eq_db_min;
    lv_obj_t * ui_zone_audio_tab_7band_eq_freq_panel;
    lv_obj_t * ui_zone_audio_tab_7band_eq_freq_text;
    lv_obj_t * ui_zone_audio_tab_7band_eq_bar_panel;
    lv_obj_t * ui_zone_audio_tab_7band_eq_bar_line_top;
    lv_obj_t * ui_zone_audio_tab_7band_eq_bar_line_mid;
    lv_obj_t * ui_zone_audio_tab_7band_eq_bar_line_bottom;
    lv_obj_t * ui_zone_audio_tab_7band_eq_bar_line;
    lv_obj_t * ui_zone_audio_tab_7band_eq_bar_line_canvas;
    lv_obj_t * ui_zone_audio_tab_7band_eq_switch_panel;
    lv_obj_t * ui_zone_audio_tab_7band_eq_switch_prev;
    lv_obj_t * ui_zone_audio_tab_7band_eq_switch_text;
    lv_obj_t * ui_zone_audio_tab_7band_eq_switch_next;
    lv_obj_t * ui_zone_audio_tab_fader_balance;
    lv_obj_t * ui_zone_audio_tab_fader_balance_panel;
    lv_obj_t * ui_zone_audio_tab_fader_text;
    lv_obj_t * ui_zone_audio_tab_balance_text;
    lv_obj_t * ui_zone_audio_tab_fader_balance_grid;
    lv_obj_t * ui_zone_audio_tab_fader_balance_grid_chart;
    lv_obj_t * ui_zone_audio_tab_fader_balance_grid_line_x;
    lv_obj_t * ui_zone_audio_tab_fader_balance_grid_line_y;
    lv_obj_t * ui_zone_audio_tab_fader_balance_grid_dot;
    lv_obj_t * ui_zone_audio_tab_fader_balance_grid_y_max;
    lv_obj_t * ui_zone_audio_tab_fader_balance_grid_y_min;
    lv_obj_t * ui_zone_audio_tab_fader_balance_grid_x_max;
    lv_obj_t * ui_zone_audio_tab_fader_balance_grid_x_min;
    lv_obj_t * ui_zone_audio_tab_crossover;
    lv_obj_t * ui_zone_audio_tab_crossover_panel;
    lv_obj_t * ui_zone_audio_tab_crossover_adjust;
    lv_obj_t * ui_zone_audio_tab_crossover_grid;
    lv_obj_t * ui_zone_audio_tab_crossover_grid_label;
    lv_obj_t * ui_zone_audio_tab_crossover_grid_label_max;
    lv_obj_t * ui_zone_audio_tab_crossover_grid_label_min;
    lv_obj_t * ui_zone_audio_tab_crossover_grid_panel;
    lv_obj_t * ui_zone_audio_tab_crossover_grid_chart;
    lv_obj_t * ui_zone_audio_tab_crossover_grid_canvas;
    lv_obj_t * ui_zone_audio_tab_crossover_switch;
    lv_obj_t * ui_zone_audio_tab_crossover_switch_prev;
    lv_obj_t * ui_zone_audio_tab_crossover_switch_text;
    lv_obj_t * ui_zone_audio_tab_crossover_switch_next;
    lv_obj_t * ui_zone_audio_tab_punch_eq;
    lv_obj_t * ui_zone_audio_tab_punch_eq_panel;
    lv_obj_t * ui_zone_audio_tab_punch_eq_slider;
    lv_obj_t * ui_zone_audio_tab_punch_eq_scale;
    lv_obj_t * ui_zone_audio_tab_punch_eq_scale_4;
    lv_obj_t * ui_zone_audio_tab_punch_eq_scale_3;
    lv_obj_t * ui_zone_audio_tab_punch_eq_scale_2;
    lv_obj_t * ui_zone_audio_tab_punch_eq_scale_1;
    lv_obj_t * ui_zone_audio_tab_punch_eq_scale_0;

	lv_obj_t * ui_menu_panel;
	lv_obj_t * ui_anim_panel;


    lv_obj_t * ui_keyboard_panel;
    lv_obj_t * ui_keyboar;
    lv_obj_t * ui_textarea;

	lv_obj_t * rig_menu_obj;
	lv_obj_t * rig_main_obj;

    lv_anim_t * menu_anim[6];
    lv_anim_t * rig_open_anim;
    lv_anim_t * rig_close_anim;
    lv_anim_t * keystripe_anim;

    lv_timer_t * ui_vol_timer;
    lv_timer_t * ui_source_timer;

    __u32 menu_win;
    __u32 old_menu_win;
    __u32 list_win;
    __u32 zone_win;
    __u32 sub_win;

	explr_list_para_t ui_list_para;
	ui_source_res_para_t ui_source_res_para;
    ui_source_gain_para_t source_gain_para;
    ui_source_vol_para_t source_vol_para;
    ui_source_eq_para_t eq_para;
    ui_source_fader_balance_para_t fader_balance_para;
    ui_source_crossover_para_t crossover_para;
    ui_source_punch_eq_para_t punch_eq_para;
    ui_source_audio_zone_para_t audio_zone_para;
    ui_source_setting_para_t setting_para;
    ui_source_audio_para_t audio_para;
    ui_source_bluetooth_para_t bluetooth_para;
    ui_source_network_para_t network_para;
	reg_app_para_t *mcu_app_para;
	mcu_ops_t mcu_v2_ops;
	party_info_t party_info;

    __s32 theme;//__THEME_TYPE
    __u32 theme_bmp_start;
    __u32 theme_bmp_end;

    __u32 rig_open_flag;
    __u32 rig_select_flag; // 0: SOURCE_NONE
    __u32 zone_select_flag; // 0: SOURCE_ZONE_NONE
    __u32 rig_setting_flag;
    __u32 save_rig_select_flag; // 0: SOURCE_NONE
    __u32 save_rig_setting_select_flag; // 0: SOURCE_NONE
    __u32 zone_no_anim_flag;
    __u32 rig_type_flag; // 0: SOURCE_RIG_TYPE_MENU
	__u32 menu_group_flag; // 0: SOURCE_MENU_GROUP_NONE
	__u32 menu_group_panel_flag; // 0: SOURCE_MENU_GROUP_NONE
	//__u32 menu_group_sel_flag;
    __u32 menu_tab_sub_flag;
    __u32 menu_sub_flag;
    __u32 source_focus_flag;

    __u32 only_top;
    __u32 nosupport_browser;
    __u32 nosupport_func;
    __u32 support_audio;

    __u32 top_indicant;

    __u8 source_str[128];

	H_WIN dialog_win;
	lv_indev_data_t * indev_data;
}ui_source_para_t;

extern const __s32 menu_setting_general_item_string_res_id[SOURCE_MENU_GENERAL_MAX];
extern const __s32 menu_setting_bt_item_string_res_id[SOURCE_MENU_BT_MAX];
extern const __s32 menu_setting_info_item_string_res_id[SOURCE_MENU_INFO_MAX];
extern const __s32 menu_setting_info_bt_item_string_res_id[SOURCE_MENU_INFO_BT_MAX];
extern const __s32 menu_audio_general_item_string_res_id[SOURCE_MENU_AUDIO_GENERAL_MAX];
extern const __s32 menu_vol_item_string_res_id[VOL_MAX];
extern const __s32 menu_audio_zone_item_string_res_id[SOURCE_MENU_AUDIO_GENERAL_MAX];
extern const __s32 eq_item_string_res_id[EQ_EQ_MAX];
extern const __s32 crossover_item_string_res_id[CROSSOVER_ADJUST_MAX];
extern const __s32 crossover_adjust_item_string_res_id[CROSSOVER_ADJUST_MAX];
extern const __s32 language_item_string_res_id[LANG_MAX];
extern const __s32 region_item_string_res_id[LANG_MAX];
extern const __s32 brightness_item_string_res_id[LANG_MAX];
extern const __s32 theme_item_string_res_id[LANG_MAX];;
extern const __s32 menu_zone_audio_item_string_res_id[SOURCE_MENU_ZONE_AUDIO_MAX];
extern const __s32 menu_zone_settings_item_string_res_id[SOURCE_MENU_ZONE_SETTINGS_MAX];

extern lv_img_dsc_t * ui_source_get_res(ui_source_para_t * source_para, __u32 icon);
extern void ui_source_init_res(ui_source_para_t * source_para);
extern void ui_source_uninit_res(ui_source_para_t * source_para);


#ifdef __cplusplus
} /*extern "C"*/
#endif

#endif
