// SquareLine LVGL GENERATED FILE
// EDITOR VERSION: SquareLine Studio 1.2.0
// LVGL VERSION: 8.3.4
// PROJECT: SquareLine_Project

#ifndef _UI_SXM_HELPERS_H
#define _UI_SXM_HELPERS_H

#ifdef __cplusplus
extern "C" {
#endif

#include "app_root_helpers.h"
#include "ui_sxm_res.h"
#include "ui_source_communication_hub.h"

#define FlashTimeWinTime		 10

enum
{
	SXM_WIN_NONE,
	SXM_WIN_LIST,
	SXM_WIN_FUNC,
	SXM_WIN_CATEGORY,
	SXM_WIN_DIRECT_TUNING,
	SXM_WIN_PARENTAL_CTRL,
	SXM_WIN_PARENTAL_CTRL_CODE,
	SXM_WIN_PARENTAL_CTRL_LOCK_CHAN,
	SXM_WIN_LOCK_CHAN,
	SXM_WIN_PARENTAL_CTRL_CHANGE_CODE,

	SXM_WIN_MAX,
};

enum
{
    //SXM_FUNC_JUMP_TO_LIVE,
    //SXM_FUNC_CATEGORY,
    //SXM_FUNC_DIRECT_TUNING,
    SXM_FUNC_PARENTAL_CONTROL,
    SXM_FUNC_RESET,

    SXM_FUNC_MAX,
};

enum
{
    SXM_PARENTAL_CTRL_CODE,
    SXM_PARENTAL_CTRL_LOCK_CHAN,
    SXM_PARENTAL_CTRL_CHANGE_CODE,

    SXM_PARENTAL_CTRL_MAX,
};

enum
{
	CODE_KEYBOARD_NONE,
	CODE_KEYBOARD_DIRECT_TUNING,
    CODE_KEYBOARD_PARENTAL_CTRL,
    CODE_KEYBOARD_PARENTAL_CTRL_LOCK_CHAN,
    CODE_KEYBOARD_PARENTAL_CTRL_CHANGE_CODE,

	CODE_KEYBOARD_MAX,
};

typedef struct
{
    char *buf;      //数据
    __u32 width;                        //宽度
    __u32  height;                       //高度
}albumart_t;

typedef struct sxm_res_para
{
	HTHEME lv_sxm_bmp[MAX_SXM_BMP_ITEM];
	lv_img_dsc_t lv_sxm_icon[MAX_SXM_BMP_ITEM];
	lv_img_dsc_t lv_album_logo;
}ui_sxm_res_para_t;

typedef struct sxm_para
{
	__u8  cur_zone;

	__u16 CurChanID;
	__u8 CurBand;
	__u8 CurPreset;
	__u8 SingalIntensity;

	__u8 connect_state;

	__s32  new_focus;
	__s32  old_focus;
	__s32  SXM_PageFirst_id;
	__s32  List_PageFirst_id;
	__s32  Cat_PageFirst_id;
	__s32  Cat_focus;

	__u32 touch_key_down;

	__s32 main_focus;
	__s32 main_PageFirst;
	__s32 sub_focus;
	__s32 sub_PageFirst;
	
	__s32  browser_cat_index;
	__u8    cat_max;
	__u16  cat_channel_max[SXM_CAT_MAX+1];
	__u16  channel_max;

	//local Meta data memory
	__u16 ChanID;
	__u8 CatID;
	char ChanName[16+1];			
	char CatName[16+1];			
	char Artist[36+1];			
	char Song[36+1];//[16+1];				
	char ContentInfo[36+1];			

	__u8 browser_wait_onshow;

	char browser_focus[36+1]; //mllee 160304 added for browser focus string

    __u8 tuning_channel[3+1];

	char parental_code[8+1];
	char parental_code_save[8+1];
    __u8 parental_onoff;
    __u8 change_index;
    __u8 cursor_index;
    __u8 cmp_error_flag;

	H_WIN dialog_win;

	RECT tags_miniature_rect;
	albumart_t AlbumArt;


    __u8 channel_num;
    __u8 preset_num;
    __u8 categoty;
    __u8 low_voltage_flag;
    __u8 charge_flag;
    __u8 eq_flag;
    __u8 live_replay_flag;//0:live    1:replay

    __u32 bottom_fouse_flag;
    __u32 bottom_fouse_index;

    __u32 theme;//__THEME_TYPE
    __u32 theme_bmp_start;
    __u32 theme_bmp_end;

    __u32 win_type;
    __u32 menu_type;
    __u32 code_kb_type;

    lv_anim_t * menu_anim[6];
	lv_obj_t * menu_obj;
	explr_list_para_t ui_list_para;
	ui_sxm_res_para_t ui_res_para;

    lv_obj_t * source_obj;
    ui_source_para_t * source_para;

	__u8 mode_store;
	lv_obj_t * sxm_main_obj;
	ui_sxm_res_para_t ui_sxm_res_para;
	__gui_msg_t sxm_msg;
	bool mode_uninit_flag;
	
	// MCU Protocol v2 communication interface
	mcu_data_ops_t mcu_ops;

	lv_indev_data_t * indev_data;
}ui_sxm_para_t;

typedef void (*ui_timer_cb_t)(ui_sxm_para_t *);

extern ui_sxm_para_t * ui_sxm_para;
extern const __s32 sxm_item_string_res_id[SXM_FUNC_MAX];
extern const __s32 sxm_parental_ctrl_item_string_res_id[SXM_PARENTAL_CTRL_MAX];

//ui_sxm_helpers.c
extern __s32 __sxm_cmd2parent(H_WIN hwin, __s32 id, __s32 data1, __s32 data2);
extern ui_sxm_para_t * ui_sxm_get_para(void);
extern __s32 ui_sxm_helpers_init(ui_sxm_para_t * sxm_para, void * para);
extern __s32 ui_sxm_helpers_uninit(ui_sxm_para_t * sxm_para);


//ui_sxm_res.c
extern lv_img_dsc_t * ui_sxm_get_res(ui_sxm_para_t * sxm_para, __u32 icon);
extern void ui_sxm_init_res(ui_sxm_para_t * sxm_para);
extern void ui_sxm_uninit_res(ui_sxm_para_t * sxm_para);


//ui_sxm.c
extern __s32 __sxm_kb_timer_create(ui_sxm_para_t *sxm_para, ui_timer_cb_t cb, uint32_t period);
extern __s32 __sxm_kb_timer_cancel(ui_sxm_para_t *sxm_para);
extern __s32 __sxm_tips_dialog_create(ui_sxm_para_t *sxm_para,__s32 title_id,__s32 content_id,__u32 time_out,__s32 id);
extern __s32 __sxm_tips_dialog_cancel(ui_sxm_para_t *sxm_para);
extern __s32 __sxm_tips_dialog_confirm(ui_sxm_para_t *sxm_para);
extern void __sxm_update_low_voltage(ui_sxm_para_t * sxm_para, __u8 update);
extern void __sxm_update_charge(ui_sxm_para_t * sxm_para, __u8 update);
extern void __sxm_update_eq(ui_sxm_para_t * sxm_para, __u8 update);
extern __s32 ui_sxm_refresh_handler(void * para, __gui_msg_t * pmsg);
extern void __ui_sxm_remove_hidden(ui_sxm_para_t * sxm_para);
extern void __ui_sxm_add_hidden(ui_sxm_para_t * sxm_para);
extern void __ui_sxm_layer_on(ui_sxm_para_t * sxm_para);
extern void __ui_sxm_layer_sleep(ui_sxm_para_t * sxm_para);
extern __s32 __sxm_time_proc(ui_sxm_para_t * sxm_para);


//ui_sxm_events.c
extern __s32 ui_sxm_opn_msg(__gui_msg_t * pmsg, __u32 sync);
extern __s32 ui_sxm_refresh_msg(__gui_msg_t * pmsg, __u32 sync);
extern __s32 ui_sxm_send_srv_msg(__s32 id, __u64 data1, __u64 data2, __u64 dwReserved, void *p_arg, __u32 flag, __u32 sync);
extern __s32 ui_sxm_send_opn_msg(__s32 id, __u64 data1, __u64 data2, __u64 dwReserved, void *p_arg, __u32 flag, __u32 sync);
extern __s32 sxm_cmd2parent(H_WIN hwin, __s32 id, __s32 data1, __s32 data2);
extern __s32 sxm_update_low_voltage(ui_sxm_para_t * sxm_para, __u8 update);
extern __s32 sxm_update_charge(ui_sxm_para_t * sxm_para, __u8 update);
extern __s32 sxm_update_eq(ui_sxm_para_t * sxm_para, __u8 update);
extern __s32 ui_sxm_remove_hidden(ui_sxm_para_t * sxm_para);
extern __s32 ui_sxm_add_hidden(ui_sxm_para_t * sxm_para);
extern __s32 ui_sxm_layer_on(ui_sxm_para_t * sxm_para);
extern __s32 ui_sxm_layer_sleep(ui_sxm_para_t * sxm_para);
extern __s32 sxm_time_proc(ui_sxm_para_t * sxm_para);
extern __s32 sxm_kb_timer_create(ui_sxm_para_t *sxm_para, ui_timer_cb_t cb, uint32_t period);
extern __s32 sxm_kb_timer_cancel(ui_sxm_para_t *sxm_para);
extern __s32 sxm_tips_dialog_create(ui_sxm_para_t *sxm_para,__s32 title_id,__s32 content_id,__u32 time_out,__s32 id);
extern __s32 sxm_tips_dialog_cancel(ui_sxm_para_t *sxm_para);
extern __s32 sxm_tips_dialog_confirm(ui_sxm_para_t *sxm_para);


#ifdef __cplusplus
} /*extern "C"*/
#endif

#endif
