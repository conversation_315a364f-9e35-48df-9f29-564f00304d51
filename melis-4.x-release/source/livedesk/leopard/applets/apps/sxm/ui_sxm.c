// SquareLine LVGL GENERATED FILE
// EDITOR VERSION: SquareLine Studio 1.2.0
// LVGL VERSION: 8.3.4
// PROJECT: SquareLine_Project

#include "ui_sxm.h"
#include "ui_helpers.h"
#include "ui_sxm_helpers.h"
#include "ui_sxm_res.h"

#if USE_LOG_PRINT
	#define __sxm_msg(...)			(eLIBs_printf("sxm MSG:L%d:", __LINE__),				 \
													  eLIBs_printf(__VA_ARGS__)) 
#else
	#define __sxm_msg(...)
#endif

#define ALBUMART_WIDTH	240
#define ALBUMART_HEIGHT	240

#define OUTPUT_BUF_SIZE     ((1*1024*1024 + 1023)/1024)     //1M


///////////////////// VARIABLES ////////////////////
ui_sxm_para_t * ui_sxm_para = NULL;

static void ui_event_sxm_timer_cb(lv_timer_t * t);
static void ui_sxm_top_right_event_handler(ui_sxm_para_t * sxm_para);
static void ui_sxm_bottom_menu_event_handler(ui_sxm_para_t * sxm_para);
static void ui_event_sxm_menu_item_cb(lv_event_t * e);
static void ui_event_sxm_menu_arrow_cb(lv_event_t * e);
static void ui_event_sxm_preset_screen(lv_event_t * e);
static void ui_sxm_switch_win(ui_sxm_para_t * sxm_para, __u32 win_type);
static __s32 ui_sxm_update_timer_proc(ui_sxm_para_t * sxm_para);
static void ui_sxm_item_group_update(ui_sxm_para_t * sxm_para, __u8 flag);

// SCREEN: ui_sxm
static void ui_sxm_switch_code_type(ui_sxm_para_t * sxm_para, __u32 code_kb_type);
static void ui_sxm_func_code_screen_init(ui_sxm_para_t * sxm_para);
static void ui_sxm_func_code_screen_uninit(ui_sxm_para_t * sxm_para);
static void ui_sxm_func_screen_init(ui_sxm_para_t * sxm_para);
static void ui_sxm_func_screen_uninit(ui_sxm_para_t * sxm_para);
static void ui_sxm_direct_tuning_screen_init(ui_sxm_para_t * sxm_para);
static void ui_sxm_direct_tuning_screen_uninit(ui_sxm_para_t * sxm_para);
static void ui_sxm_parental_ctrl_code_screen_init(ui_sxm_para_t * sxm_para);
static void ui_sxm_parental_ctrl_code_screen_uninit(ui_sxm_para_t * sxm_para);
static void ui_sxm_parental_ctrl_lock_channel_screen_init(ui_sxm_para_t * sxm_para);
static void ui_sxm_parental_ctrl_lock_channel_screen_uninit(ui_sxm_para_t * sxm_para);
static void ui_sxm_parental_ctrl_change_code_screen_init(ui_sxm_para_t * sxm_para);
static void ui_sxm_parental_ctrl_change_code_screen_uninit(ui_sxm_para_t * sxm_para);
static void ui_sxm_screen_init(ui_sxm_para_t * sxm_para);
static void ui_sxm_screen_uninit(ui_sxm_para_t * sxm_para);

lv_obj_t * ui_sxm = NULL;
lv_obj_t * ui_sxm_play = NULL;
lv_obj_t * ui_sxm_play_top = NULL;
lv_obj_t * ui_sxm_play_top_ch_num = NULL;
lv_obj_t * ui_sxm_play_top_ch = NULL;
lv_obj_t * ui_sxm_play_top_num = NULL;
lv_obj_t * ui_sxm_play_top_presets = NULL;
lv_obj_t * ui_sxm_play_top_category = NULL;
lv_obj_t * ui_sxm_play_top_low = NULL;
lv_obj_t * ui_sxm_play_top_charge = NULL;
lv_obj_t * ui_sxm_play_top_eq = NULL;
lv_obj_t * ui_sxm_play_mid = NULL;
lv_obj_t * ui_sxm_play_logo_panel = NULL;
lv_obj_t * ui_sxm_play_logo = NULL;
lv_obj_t * ui_sxm_play_logo_text = NULL;
lv_obj_t * ui_sxm_logo_panel = NULL;
lv_obj_t * ui_sxm_logo = NULL;
lv_obj_t * ui_sxm_play_disp = NULL;
lv_obj_t * ui_sxm_play_ind = NULL;
lv_obj_t * ui_sxm_play_ind_text = NULL;
lv_obj_t * ui_sxm_play_ind_text_panel = NULL;
lv_obj_t * ui_sxm_play_ind_text_panel_name = NULL;
lv_obj_t * ui_sxm_play_ind_text_panel_name_text = NULL;
lv_obj_t * ui_sxm_play_ind_text_panel_song = NULL;
lv_obj_t * ui_sxm_play_ind_text_panel_song_text = NULL;
lv_obj_t * ui_sxm_play_ind_text_panel_artist_info = NULL;
lv_obj_t * ui_sxm_play_ind_text_panel_artist_info_text = NULL;
lv_obj_t * ui_sxm_play_bottom = NULL;
lv_obj_t * ui_sxm_play_bottom_current = NULL;
lv_obj_t * ui_sxm_play_bottom_bar = NULL;
lv_obj_t * ui_sxm_play_bottom_ff = NULL;
lv_obj_t * ui_sxm_play_bottom_ff_img = NULL;
lv_obj_t * ui_sxm_bottom_list = NULL;
lv_obj_t * ui_sxm_bottom_list_img = NULL;
lv_obj_t * ui_sxm_bottom_cat = NULL;
lv_obj_t * ui_sxm_bottom_cat_label = NULL;
lv_obj_t * ui_sxm_bottom_replay = NULL;
lv_obj_t * ui_sxm_bottom_replay_label = NULL;
lv_obj_t * ui_sxm_bottom_direct = NULL;
lv_obj_t * ui_sxm_bottom_direct_img = NULL;

lv_obj_t * ui_sxm_func_code = NULL;
lv_obj_t * ui_sxm_func_code_blur_panel = NULL;
#if 0
lv_obj_t * ui_sxm_func_code_blur = NULL;
lv_obj_t * ui_sxm_func_code_panel = NULL;
lv_obj_t * ui_sxm_func_code_cursor_1 = NULL;
lv_obj_t * ui_sxm_func_code_cursor_2 = NULL;
lv_obj_t * ui_sxm_func_code_cursor_3 = NULL;
lv_obj_t * ui_sxm_func_code_cursor_4 = NULL;
lv_obj_t * ui_sxm_func_code_text = NULL;
#endif
lv_obj_t * ui_sxm_func_code_keyboard = NULL;
lv_obj_t * ui_sxm_func_code_textarea = NULL;

lv_obj_t * ui_sxm_direct_tuning = NULL;
lv_obj_t * ui_sxm_parental_ctrl_code = NULL;
lv_obj_t * ui_sxm_parental_ctrl_code_label_1 = NULL;
lv_obj_t * ui_sxm_parental_ctrl_code_label_2 = NULL;
lv_obj_t * ui_sxm_parental_ctrl_code_label_3 = NULL;
lv_obj_t * ui_sxm_parental_ctrl_lock_channel = NULL;
lv_obj_t * ui_sxm_parental_ctrl_change_code = NULL;

lv_obj_t * ui_sxm_func_menu = NULL;

lv_timer_t  * ui_sxm_timer = NULL;
lv_timer_t  * ui_sxm_kb_timer = NULL;

__u32 SXM_THMEM_TEMP_PM_BO_OFF_ICON_BMP = 0;
__u32 SXM_THMEM_TEMP_PM_BO_OFF_ICON_A_BMP = 0;
__u32 SXM_THMEM_TEMP_PM_RE_ICON_BMP = 0;
__u32 SXM_THMEM_TEMP_PM_RE_ICON_A_BMP = 0;
__u32 SXM_THMEM_TEMP_PM_RE_LIST_BMP = 0;
__u32 SXM_THMEM_TEMP_PM_RE_LIST_N_BMP = 0;
__u32 SXM_THMEM_TEMP_PM_RE_LIST_CONNECT_BMP = 0;
__u32 SXM_THMEM_TEMP_PM_RE_SEARCH_BMP = 0;
__u32 SXM_THMEM_TEMP_PM_RE_SEARCH_N_BMP = 0;
__u32 SXM_THMEM_TEMP_COM_LIST_ON_BMP = 0;
__u32 SXM_THMEM_TEMP_COM_LIST_OFF_BMP = 0;
//__u32 SXM_THMEM_TEMP_COM_MEDIA_ALBUM_BG_BMP = 0;

__u32 SXM_THEME_COM_BOT_BG_BMP = 0;  

__u32 SXM_THEME_COM_BOT_ICON_BG_A_L_N_BMP = 0;
__u32 SXM_THEME_COM_BOT_ICON_BG_A_L_S_BMP = 0;

__u32 SXM_THEME_COM_BOT_LIST_N_BMP = 0;
__u32 SXM_THEME_COM_BOT_LIST_D_BMP = 0;

__u32 SXM_THEME_COM_BOT_DIRECT_N_BMP = 0;
__u32 SXM_THEME_COM_BOT_DIRECT_D_BMP = 0;


__u32 SXM_THMEM_SXM_ICON_BMP = 0;
__u32 SXM_THMEM_SXM_SIGNAL_A_BMP = 0;
__u32 SXM_THMEM_SXM_SIGNAL_B_BMP = 0;
__u32 SXM_THMEM_SXM_SIGNAL_C_BMP = 0;
__u32 SXM_THMEM_SXM_SIGNAL_D_BMP = 0;
__u32 SXM_THMEM_SXM_LOCK_BMP = 0;
__u32 SXM_THMEM_SXM_TOP_BAR_ICON_BMP = 0;
__u32 SXM_THMEM_TEMP_COM_LOW_VOLTAGE_BMP = 0;
__u32 SXM_THMEM_TEMP_COM_PD_CHARGE_BMP = 0;
__u32 SXM_THMEM_TEMP_COM_PUNCH_EQ_BMP = 0;
__u32 SXM_THMEM_TEMP_COM_RIG_ICON_BG_N_BMP = 0;
__u32 SXM_THMEM_TEMP_COM_RIG_ICON_BG_P_BMP = 0;
__u32 SXM_THMEM_TEMP_COM_RIG_ICON_BG_S_BMP = 0;

///////////////////// TEST LVGL SETTINGS ////////////////////
#if LV_COLOR_DEPTH != 32
    #error "LV_COLOR_DEPTH should be 32bit to match SquareLine Studio's settings"
#endif
#if LV_COLOR_16_SWAP !=0
    #error "LV_COLOR_16_SWAP should be 0 to match SquareLine Studio's settings"
#endif

///////////////////// STATIC FUNCTION ////////////////////
__s32 __sxm_kb_timer_create(ui_sxm_para_t *sxm_para, ui_timer_cb_t cb, uint32_t period)
{
    if(!ui_sxm_kb_timer)
    {
        ui_sxm_kb_timer = lv_timer_create(cb, period, sxm_para);
        lv_timer_set_repeat_count(ui_sxm_kb_timer, 1);
    }
    
	return EPDK_OK;
}

__s32 __sxm_kb_timer_cancel(ui_sxm_para_t *sxm_para)
{
	if(ui_sxm_kb_timer)
	{
		//lv_timer_del(ui_sxm_kb_timer);
		ui_sxm_kb_timer = NULL;
	}
    
	return EPDK_OK;
}

__s32 __sxm_tips_dialog_create(ui_sxm_para_t *sxm_para,__s32 title_id,__s32 content_id,__u32 time_out,__s32 id)
{
	__s32 str[2] = {0};
	str[0] = title_id;
	str[1] = content_id;

	if(sxm_para->dialog_win)
	{
		ui_dialog_destroy(sxm_para->dialog_win);
		sxm_para->dialog_win = NULL;
	}
	
	sxm_para->dialog_win = ui_dialog_create(lv_layer_top(), APP_SXM_CODE_DIALOG_ID, id, str,time_out, 0);	
    //ui_source_sub_open(sxm_para->source_para);
    
	return EPDK_OK;
}

__s32 __sxm_tips_dialog_cancel(ui_sxm_para_t *sxm_para)
{
	if(sxm_para->dialog_win)
	{
		ui_dialog_destroy(sxm_para->dialog_win);
		sxm_para->dialog_win = NULL;
	}
    
    //ui_source_sub_close(sxm_para->source_para);
    
	return EPDK_OK;
}

__s32 __sxm_tips_dialog_confirm(ui_sxm_para_t *sxm_para)
{
    sxm_tips_dialog_cancel(sxm_para);
    
	return EPDK_OK;
}

void __sxm_update_low_voltage(ui_sxm_para_t * sxm_para, __u8 update)
{		
    if(sxm_para->low_voltage_flag == update)
    {
        return;
    }

    sxm_para->low_voltage_flag = update;
    
	if(sxm_para->low_voltage_flag)
	{
		if(lv_obj_has_flag(ui_sxm_play_top_low, LV_OBJ_FLAG_HIDDEN))
		{
			lv_obj_clear_flag(ui_sxm_play_top_low, LV_OBJ_FLAG_HIDDEN);
		}
	}
	else
	{
		if(!lv_obj_has_flag(ui_sxm_play_top_low, LV_OBJ_FLAG_HIDDEN))
			lv_obj_add_flag(ui_sxm_play_top_low, LV_OBJ_FLAG_HIDDEN);
	}
}

void __sxm_update_charge(ui_sxm_para_t * sxm_para, __u8 update)
{		
    if(sxm_para->charge_flag == update)
    {
        return;
    }

    sxm_para->charge_flag = update;
    
	if(sxm_para->charge_flag)
	{
		if(lv_obj_has_flag(ui_sxm_play_top_charge, LV_OBJ_FLAG_HIDDEN))
		{
			lv_obj_clear_flag(ui_sxm_play_top_charge, LV_OBJ_FLAG_HIDDEN);
		}
	}
	else
	{
		if(!lv_obj_has_flag(ui_sxm_play_top_charge, LV_OBJ_FLAG_HIDDEN))
			lv_obj_add_flag(ui_sxm_play_top_charge, LV_OBJ_FLAG_HIDDEN);
	}
}

void __sxm_update_eq(ui_sxm_para_t * sxm_para, __u8 update)
{		
    if(sxm_para->eq_flag == update)
    {
        return;
    }

    sxm_para->eq_flag = update;
    
	if(sxm_para->eq_flag)
	{
		if(lv_obj_has_flag(ui_sxm_play_top_eq, LV_OBJ_FLAG_HIDDEN))
		{
			lv_obj_clear_flag(ui_sxm_play_top_eq, LV_OBJ_FLAG_HIDDEN);
		}
	}
	else
	{
		if(!lv_obj_has_flag(ui_sxm_play_top_eq, LV_OBJ_FLAG_HIDDEN))
			lv_obj_add_flag(ui_sxm_play_top_eq, LV_OBJ_FLAG_HIDDEN);
	}
}

__s32 __sxm_draw_pic(ui_sxm_para_t *sxm_para, __u32 bmp)
{
	if(ui_sxm_logo == NULL)
	{
		return;
	}
	
	if(bmp < MAX_SXM_BMP_ITEM)
	{
		//eLIBs_printf("DrawMusicDrawPic00 time=%d\n",esKRNL_TimeGet());
		lv_img_set_src(ui_sxm_logo, ui_music_get_res(sxm_para, bmp));
#ifdef USE_GXPMX30_MODEL
        //lv_obj_add_flag(ui_music_bg, LV_OBJ_FLAG_HIDDEN);    /// Flags
		lv_obj_add_flag(ui_sxm_logo_panel, LV_OBJ_FLAG_HIDDEN);//ui_sxm_logo
		lv_obj_clear_flag(ui_sxm_play_logo, LV_OBJ_FLAG_HIDDEN); 	/// Flags
#if 0
		if(sxm_para->logo_anim)
		{
			//lv_anim_del(sxm_para->logo_anim, NULL);
			sxm_para->logo_anim = NULL;
		}
#ifdef USE_ALBUM_BG_FG
        if(sxm_para->snapshot)
        {
            lv_snapshot_free(sxm_para->snapshot);
            sxm_para->snapshot = NULL;
        }
#endif
#endif
#endif
	}
	else
	{
		//eLIBs_printf("DrawMusicDrawPic11 time=%d\n",esKRNL_TimeGet());
		sxm_para->ui_res_para.lv_album_logo.header.w = sxm_para->AlbumArt.width;
		sxm_para->ui_res_para.lv_album_logo.header.h = sxm_para->AlbumArt.height;
		sxm_para->ui_res_para.lv_album_logo.data_size = sxm_para->AlbumArt.width * sxm_para->AlbumArt.height * 4;
		sxm_para->ui_res_para.lv_album_logo.header.cf = LV_COLOR_FORMAT_ARGB8888;
		sxm_para->ui_res_para.lv_album_logo.data = sxm_para->AlbumArt.buf;
		lv_img_set_src(ui_sxm_logo, &sxm_para->ui_res_para.lv_album_logo);
#if 0//def USE_GXPMX30_MODEL
        if(sxm_para->AlbumArtBg.buf)
        {
    		lv_obj_clear_flag(ui_sxm_logo_panel, LV_OBJ_FLAG_HIDDEN);//ui_sxm_logo
    		lv_obj_add_flag(ui_sxm_play_logo, LV_OBJ_FLAG_HIDDEN);
    		if(sxm_para->logo_anim == NULL)
    		{
    			//sxm_para->logo_anim = img_rorate_Animation(ui_sxm_logo, LV_OBJ_FLAG_HIDDEN);
    		}

            lv_obj_clear_flag(ui_music_bg, LV_OBJ_FLAG_HIDDEN);    /// Flags
    		sxm_para->ui_music_res_para.lv_music_album_bg.header.w = sxm_para->AlbumArtBg.width;
    		sxm_para->ui_music_res_para.lv_music_album_bg.header.h = sxm_para->AlbumArtBg.height;
    		sxm_para->ui_music_res_para.lv_music_album_bg.data_size = sxm_para->AlbumArtBg.width * sxm_para->AlbumArtBg.height * 4;
    		sxm_para->ui_music_res_para.lv_music_album_bg.header.cf = LV_COLOR_FORMAT_XRGB8888;
    		sxm_para->ui_music_res_para.lv_music_album_bg.data = sxm_para->AlbumArtBg.buf;
            lv_image_set_src(ui_music_bg, &sxm_para->ui_music_res_para.lv_music_album_bg);
            
#ifdef USE_ALBUM_BG_FG
            lv_obj_clear_flag(ui_music_bg_fg, LV_OBJ_FLAG_HIDDEN);    /// Flags

            if(sxm_para->snapshot == NULL)
            {
                sxm_para->snapshot = lv_snapshot_take(ui_music_bg, LV_COLOR_FORMAT_XRGB8888);
                
                __sxm_msg("snapshot=0x%x\n", sxm_para->snapshot);
                if(sxm_para->snapshot)
                {
                    lv_obj_add_flag(ui_music_bg_fg, LV_OBJ_FLAG_HIDDEN);    /// Flags
                    lv_img_set_src(ui_music_bg, sxm_para->snapshot);
                }
            }
#endif
        }
#endif
		__sxm_msg("sxm_para->AlbumArt.width=%d,sxm_para->AlbumArt.height=%d.\n",sxm_para->AlbumArt.width,sxm_para->AlbumArt.height);
	}
}

static  __s32 sxm_albumart_data_converter(ui_sxm_para_t * sxm_para,__u8 *pic_out_buf)
{
	int ret;
	__u8 *pbuf = NULL;
	__u32 pic_size = 0;
	rat_miniature_para_t in_para;
	rat_pic_info_t out_para;
	reg_sxm_para_t* para=NULL;
	para = (reg_sxm_para_t*)dsk_reg_get_para_by_app(REG_APP_SXM);

	if(para->pix_onshow_flag == SXM_PIX_SHOW_RT1)
	{

		if(para->album_art[0].valid == 0 || para->chan_select_ind.SID != para->album_art[0].Sid
		 || ((para->chan_select_ind.ProgramID&SXM_PIX_PID_MASK) != (para->album_art[0].arg&SXM_PIX_PID_MASK)))
		{
			__msg("RT1 not match\n");
		 	return EPDK_FAIL;
		}

		pbuf = para->album_art[0].pPIC_buf;
		pic_size = para->album_art[0].pic_len;
	}
	else if(para->pix_onshow_flag == SXM_PIX_SHOW_RT2)
	{

		if(para->album_art[1].valid == 0 || para->chan_select_ind.SID != para->album_art[1].Sid
		 || ((para->chan_select_ind.ProgramID&SXM_PIX_PID_MASK) != (para->album_art[1].arg&SXM_PIX_PID_MASK)))
		{
			__msg("RT2 not match\n");
		 	return EPDK_FAIL;
		}

		pbuf = para->album_art[1].pPIC_buf;
		pic_size = para->album_art[1].pic_len;
	}
	else 
		return EPDK_FAIL;
	
	if(pic_out_buf == NULL)
		pic_out_buf = esMEMS_Palloc(OUTPUT_BUF_SIZE, 0);
	if(pic_out_buf == NULL)
		return EPDK_FAIL;

	eLIBs_memset(&in_para, 0, sizeof(rat_miniature_para_t));
	eLIBs_memset(&out_para, 0, sizeof(out_para));
	ret = rat_start_miniature_decode();
	if(ret != EPDK_OK)
	{
		return EPDK_FAIL;
	}

	//input
	in_para.input_type = 1; //buffer input 
	in_para.input_buf = pbuf;
	in_para.input_size = pic_size;
	in_para.format = PIXEL_COLOR_ARGB8888;
	in_para.width = ALBUMART_WIDTH;
	in_para.height = ALBUMART_HEIGHT;
	in_para.mode = WILLOW_SCALE_STRETCH;	// 1为拉伸模式
	//output
	out_para.miniature.buf = pic_out_buf;
	
	ret = rat_get_pic_info(&in_para, &out_para);

	__msg("pic buffer decode end\n");
	__msg("pic info, F:%d, W:%d, H:%d\n",out_para.format,out_para.width,out_para.height);
	__msg("miniature info, Len:%d, W:%d, H:%d\n",out_para.miniature.len,out_para.miniature.width,out_para.miniature.height);

	rat_stop_miniature_decode();

	return ret;
}

//show service default album art
static __s32 paint_update_albumArt(ui_sxm_para_t * sxm_para)
{
	int ret = 0;
 	void *pBuf= NULL;
	rat_miniature_para_t in_para;
	rat_pic_info_t out_para;

	RECT rect;
	reg_sxm_para_t* para=NULL;
	
	para = (reg_sxm_para_t*)dsk_reg_get_para_by_app(REG_APP_SXM);
	
	if(sxm_para == NULL)
		return EPDK_FAIL;
    
	if((sxm_para->tags_miniature_rect.width == 0) || (sxm_para->tags_miniature_rect.height == 0))
	{
		if(ui_sxm_logo)
		{
			sxm_para->tags_miniature_rect.x = lv_obj_get_x(ui_sxm_logo);
			sxm_para->tags_miniature_rect.y = lv_obj_get_y(ui_sxm_logo);
			sxm_para->tags_miniature_rect.width = lv_obj_get_width(ui_sxm_logo);
			sxm_para->tags_miniature_rect.height = lv_obj_get_height(ui_sxm_logo);
		}
	}
	rect.x = sxm_para->tags_miniature_rect.x;
	rect.y = sxm_para->tags_miniature_rect.y;
	rect.width = sxm_para->tags_miniature_rect.width;
	rect.height = sxm_para->tags_miniature_rect.height;

	eLIBs_memset(&in_para, 0, sizeof(rat_miniature_para_t));
	eLIBs_strcpy(in_para.file, SXM_SERVICE_DEFAULT_IMG_FILE_PATH);
	in_para.format = PIXEL_COLOR_ARGB8888;
	in_para.width = rect.width;
	in_para.height = rect.height;
	in_para.mode = WILLOW_SCALE_STRETCH;

    if(sxm_para->AlbumArt.buf && sxm_para->AlbumArt.width && sxm_para->AlbumArt.height)
    {
        __sxm_msg("width=%d, height=%d\n", sxm_para->AlbumArt.width, sxm_para->AlbumArt.height);
        esMEMS_Pfree(sxm_para->AlbumArt.buf, ((sxm_para->AlbumArt.width*sxm_para->AlbumArt.height*4)+1023)/1024);
        __sxm_msg("here11\n");
        sxm_para->AlbumArt.buf = NULL;
        sxm_para->AlbumArt.width = 0;
        sxm_para->AlbumArt.height = 0;
    }

	pBuf = esMEMS_Balloc(in_para.width*in_para.height*4);
	if(pBuf == NULL)
	{		
		return EPDK_FAIL; 
	}
	
	ret = rat_start_miniature_decode();
	
	eLIBs_memset(&out_para, 0, sizeof(out_para));
	eLIBs_memset(pBuf, 0, in_para.width*in_para.height*4);
	out_para.miniature.buf = pBuf;
	
	ret = rat_get_pic_info(&in_para, &out_para);	
	
	if (EPDK_OK == ret)
	{
        __sxm_draw_pic(sxm_para, NULL);
	}
    
	esMEMS_Bfree(pBuf, in_para.width*in_para.height*4);	 
	pBuf = NULL;
	rat_stop_miniature_decode();
	para->pix_onshow_flag = SXM_PIX_SHOW_SERVICE_DEFAULT;
	return EPDK_OK;

}

static __s32 update_rt_albumart(ui_sxm_para_t * sxm_para)
{
	int ret;
	__u8 *pbuf = NULL;
	RECT rect;
	reg_sxm_para_t* para=NULL;
	para = (reg_sxm_para_t*)dsk_reg_get_para_by_app(REG_APP_SXM);
	
	if(sxm_para == NULL)
		return EPDK_FAIL;


	if(pbuf == NULL)
		pbuf = esMEMS_Palloc(OUTPUT_BUF_SIZE, 0);
	if(pbuf == NULL)
		return EPDK_FAIL;

	if((sxm_para->tags_miniature_rect.width == 0) || (sxm_para->tags_miniature_rect.height == 0))
	{
		if(ui_sxm_logo)
		{
			sxm_para->tags_miniature_rect.x = lv_obj_get_x(ui_sxm_logo);
			sxm_para->tags_miniature_rect.y = lv_obj_get_y(ui_sxm_logo);
			sxm_para->tags_miniature_rect.width = lv_obj_get_width(ui_sxm_logo);
			sxm_para->tags_miniature_rect.height = lv_obj_get_height(ui_sxm_logo);
		}
	}
	rect.x = sxm_para->tags_miniature_rect.x;
	rect.y = sxm_para->tags_miniature_rect.y;
	rect.width = sxm_para->tags_miniature_rect.width;
	rect.height = sxm_para->tags_miniature_rect.height;

	ret = sxm_albumart_data_converter(sxm_para,pbuf);
    
	if(ret == EPDK_OK)
	{
        __sxm_draw_pic(sxm_para, NULL);
	}

	if(pbuf)
	{
		esMEMS_Pfree(pbuf, OUTPUT_BUF_SIZE);
		pbuf = NULL;
		__msg("free pbuf OK!\n");
	}

	if(ret != EPDK_OK) //pic decode err
	{
		paint_update_albumArt(sxm_para);
	}
	__msg("show album tag : %d\n",ret); 
	return ret;
}

__s32 show_albumart_by_type_match(ui_sxm_para_t * sxm_para)
{
	__s32 ret;
	__u8 last_onshow;
	reg_sxm_para_t* para=NULL;
	para = (reg_sxm_para_t*)dsk_reg_get_para_by_app(REG_APP_SXM);

	last_onshow = para->pix_onshow_flag;
	
	//if(sxm_para->win_type == SXM_LIVE_PLAY_WIN)
	{
		if(para->album_art[0].valid == 1 && para->chan_select_ind.SID == para->album_art[0].Sid
		 && ((para->chan_select_ind.ProgramID&SXM_PIX_PID_MASK) == (para->album_art[0].arg&SXM_PIX_PID_MASK)))
		{
			para->pix_onshow_flag = SXM_PIX_SHOW_RT1;
		}
		else if(para->album_art[1].valid == 1 && para->chan_select_ind.SID == para->album_art[1].Sid
		 && ((para->chan_select_ind.ProgramID&SXM_PIX_PID_MASK) == (para->album_art[1].arg&SXM_PIX_PID_MASK)))
		{
			para->pix_onshow_flag = SXM_PIX_SHOW_RT2;
		}
		else
			para->pix_onshow_flag = SXM_PIX_SHOW_SERVICE_DEFAULT;
	}
	if(last_onshow == SXM_PIX_SHOW_SERVICE_DEFAULT && para->pix_onshow_flag == SXM_PIX_SHOW_SERVICE_DEFAULT)
		return EPDK_OK;
	if(para->pix_onshow_flag == SXM_PIX_SHOW_SERVICE_DEFAULT)
	{
		paint_update_albumArt(sxm_para);
	}
	else
	{
		ret = update_rt_albumart(sxm_para);
	}
	return EPDK_OK;	
}

#ifdef SXM_BF_B_ENABLE
static  __s32 ir_albumart_data_converter(ui_sxm_para_t * sxm_para,__u8 *pic_out_buf)
{
	int ret;
	__u8 i;
	__u8 ir_show_id;
	__u8 *pbuf = NULL;
	__u32 pic_size = 0;
	rat_miniature_para_t in_para;
	rat_pic_info_t out_para;
	reg_sxm_para_t* para=NULL;
	para = (reg_sxm_para_t*)dsk_reg_get_para_by_app(REG_APP_SXM);

	for(i=0;i<25;i++)
	{
		if(para->ir_album_art[i].valid == 1 
		   && (para->chan_select_ind.SID == para->ir_album_art[i].Sid)
		   && ((para->replay_meta_ind.ProgramID&SXM_PIX_PID_MASK) == (para->ir_album_art[i].arg&SXM_PIX_PID_MASK)))
		{
			ir_show_id = i;
			break;
		}
	}

	if(i>=25)
		return EPDK_FAIL;

	pbuf = para->ir_album_art[ir_show_id].pPIC_buf;
	pic_size = para->ir_album_art[ir_show_id].pic_len;
	
	if(pic_out_buf == NULL)
		pic_out_buf = esMEMS_Palloc(OUTPUT_BUF_SIZE, 0);
	if(pic_out_buf == NULL)
		return EPDK_FAIL;

	eLIBs_memset(&in_para, 0, sizeof(rat_miniature_para_t));
	eLIBs_memset(&out_para, 0, sizeof(out_para));
	ret = rat_start_miniature_decode();
	if(ret != EPDK_OK)
	{
		return EPDK_FAIL;
	}

	//input
	in_para.input_type = 1; //buffer input 
	in_para.input_buf = pbuf;
	in_para.input_size = pic_size;
	in_para.format = PIXEL_COLOR_ARGB8888;
	in_para.width = ALBUMART_WIDTH;
	in_para.height = ALBUMART_HEIGHT;
	in_para.mode = WILLOW_SCALE_STRETCH;	// 1为拉伸模式
	//output
	out_para.miniature.buf = pic_out_buf;
	
	ret = rat_get_pic_info(&in_para, &out_para);

	__msg("pic buffer decode end\n");
	__msg("pic info, F:%d, W:%d, H:%d\n",out_para.format,out_para.width,out_para.height);
	__msg("miniature info, Len:%d, W:%d, H:%d\n",out_para.miniature.len,out_para.miniature.width,out_para.miniature.height);

	rat_stop_miniature_decode();

	return ret;
}

static __s32 update_ir_albumart(ui_sxm_para_t * sxm_para)
{
	int ret;
	__u8 *pbuf = NULL;
	RECT rect;
	reg_sxm_para_t* para=NULL;
	para = (reg_sxm_para_t*)dsk_reg_get_para_by_app(REG_APP_SXM);
	
	if(sxm_para == NULL)
		return EPDK_FAIL;

	if(pbuf == NULL)
		pbuf = esMEMS_Palloc(OUTPUT_BUF_SIZE, 0);
	if(pbuf == NULL)
		return EPDK_FAIL;
	
		
	ret = ir_albumart_data_converter(sxm_para,pbuf);
	if(ret == EPDK_OK)
	{

	}

	if(pbuf)
	{
		esMEMS_Pfree(pbuf, OUTPUT_BUF_SIZE);
		pbuf = NULL;
		__msg("free pbuf OK!\n");
	}

	__msg("ir show album tag : %d\n",ret); 
	return ret;
}

__s32 show_ir_albumart(ui_sxm_para_t * sxm_para)
{
	__s32 ret;
	__u8 last_onshow;
	reg_sxm_para_t* para=NULL;
	para = (reg_sxm_para_t*)dsk_reg_get_para_by_app(REG_APP_SXM);

	if(para->replay.state == SXM_REPLAY_LIVE)
		return EPDK_FAIL;

	ret = update_ir_albumart(sxm_para);

	if(ret == EPDK_OK)
		para->pix_onshow_flag = SXM_PIX_SHOW_IR;
	else
		paint_update_albumArt(sxm_para);

	return EPDK_OK;	
}
#endif

__s32 sxm_get_channel_array(ui_sxm_para_t * sxm_para)
{
	__u32 i;
	__u32 valid_channel =0;
	reg_sxm_para_t* para=NULL;
	
	para = (reg_sxm_para_t*)dsk_reg_get_para_by_app(REG_APP_SXM);
	//eLIBs_memset((void *)para->channel_array,0xFF,sizeof(para->channel_array));
	for(i=0;i<SXM_CHANNEL_MAX;i++) //init array to oxFFFF
		para->channel_array[i] = 0xFFFF;
	for(i=0;i<SXM_CHAN_MAX;i++)
	{
		//if(para->chan_meta_ind_ptr[i].SID != 0xFFFF)
		//skip unsubscribed and unavailable mllee 150422
		if(para->chan_meta_ind_ptr[i].SID != 0xFFFF
		  && ((para->chan_meta_ind_ptr[i].ChanAttributes & SXI_CHAN_ATTRIBUTE_NOT_SUBSCRIBED) == 0)
		  && ((para->chan_meta_ind_ptr[i].ChanAttributes & SXI_CHAN_ATTRIBUTE_NOT_AVAILABLE) == 0))
		{
			para->channel_array[para->chan_meta_ind_ptr[i].ChanID] = i;
			valid_channel++;
		}
	}
	__msg("channel array: %d\n",valid_channel);

	return EPDK_OK;
}

__s32 sxm_get_next_channel_index_in_array(ui_sxm_para_t * sxm_para)
{
	__s32 index = -1;
	__s32 i=0,cnt =0;
	
	reg_sxm_para_t* para=NULL;
	para = (reg_sxm_para_t*)dsk_reg_get_para_by_app(REG_APP_SXM);
	
	sxm_get_channel_array(sxm_para);

	i = sxm_para->CurChanID+1;
	//if(i>(SXM_CHANNEL_MAX-1))
	//	i=0;
	
	for( ; ;i++)
	{
		//__msg("i=%d\n",i);
		if(i>(SXM_CHANNEL_MAX-1))
			i=0;
		//if(para->channel_array[i] != 0xFFFF)
		if((para->channel_array[i] != 0xFFFF) && (para->channel_lock[para->chan_meta_ind_ptr[para->channel_array[i]].SID] == 0)) //mllee 150504 mod
		{
			index = para->channel_array[i];
			break;
		}
		cnt++;
		if(cnt > SXM_CHANNEL_MAX)
		{
			return -1;
		}
	}
	return index;
}

__s32 sxm_get_prev_channel_index_in_array(ui_sxm_para_t * sxm_para)
{
	__s32 index = -1;
	__s32 i=0,cnt =0;
	
	reg_sxm_para_t* para=NULL;
	para = (reg_sxm_para_t*)dsk_reg_get_para_by_app(REG_APP_SXM);
	
	sxm_get_channel_array(sxm_para);

	i = (__s32)(sxm_para->CurChanID-1);
	//if(i<0)
	//	i = SXM_CHANNEL_MAX-1;

	for(; ;i--)
	{
		//__msg("i=%d\n",i);
		if(i<0)
			i = SXM_CHANNEL_MAX-1;
		//if(para->channel_array[i] != 0xFFFF)
		if((para->channel_array[i] != 0xFFFF) && (para->channel_lock[para->chan_meta_ind_ptr[para->channel_array[i]].SID] == 0)) //mllee 150504 mod
		{
			index = para->channel_array[i];
			break;
		}
		cnt++;
		if(cnt > SXM_CHANNEL_MAX)
		{
			return -1;
		}
	}
	return index;
}

__s32 sxm_get_cat_index_by_catID(ui_sxm_para_t * sxm_para,__u8 catID)
{
	__u32 i;
	__s32 index = 0;
	reg_sxm_para_t* para=NULL;
	
	para = (reg_sxm_para_t*)dsk_reg_get_para_by_app(REG_APP_SXM);
	if(sxm_para == NULL)
		return EPDK_FAIL;

	for(i=0; i<SXM_CAT_MAX;i++)
	{
		if(para->cat_info_ind_ptr[i].CatID == catID)
			break;
	}

	if(i<SXM_CAT_MAX)
		return i;
	else 
		return -1;
}

static __s32 sxm_clear_connect_state(ui_sxm_para_t * sxm_para)
{
	if(sxm_para == NULL)
		return EPDK_FAIL;
	sxm_para->connect_state = SXM_CONNECT_STA_OK;
	
	return EPDK_OK;
}

static __s32 paint_update_head_source(ui_sxm_para_t * sxm_para)
{
	reg_system_para_t* para;
	para = (reg_system_para_t*)dsk_reg_get_para_by_app(REG_APP_SYSTEM);

	//common_view_update_bottom_tips(lyr,"Press MENU for Settings",HMR_GUI_GRAY);
}

static __s32 paint_update_signal(ui_sxm_para_t * sxm_para)
{
	reg_sxm_para_t* para=NULL;
	reg_system_para_t* para_sys;
	
    para_sys = (reg_system_para_t*)dsk_reg_get_para_by_app(REG_APP_SYSTEM);
	para = (reg_sxm_para_t*)dsk_reg_get_para_by_app(REG_APP_SXM);
	
	if(sxm_para == NULL)
		return EPDK_FAIL;



	return EPDK_OK;
}

static __s32 paint_update_preset(ui_sxm_para_t * sxm_para)
{
	char string[32] = {0};
	reg_sxm_para_t* para=NULL;
	reg_system_para_t* para_sys;
	
       para_sys = (reg_system_para_t*)dsk_reg_get_para_by_app(REG_APP_SYSTEM);
	para = (reg_sxm_para_t*)dsk_reg_get_para_by_app(REG_APP_SXM);
	
	if(sxm_para == NULL)
		return EPDK_FAIL;

	if(para->cur_preset>0)
	{
		sprintf(string,"P%d",para->cur_preset);
        lv_label_set_text(ui_sxm_play_top_presets, string);
        lv_obj_clear_flag(ui_sxm_play_top_presets, LV_OBJ_FLAG_HIDDEN);
	}
	else
	{
        lv_obj_add_flag(ui_sxm_play_top_presets, LV_OBJ_FLAG_HIDDEN);
	}	

	return EPDK_OK;
}

static __s32 paint_update_channelinfo(ui_sxm_para_t * sxm_para)
{
	char str[32]={0};
	char string[16] = {0};
	char artist_info[256] = {0};
	char channel_name[32] = {0};
	char song_title[32] = {0};
	__u32 str_w;
	void *pic_buf = NULL;

	reg_sxm_para_t* para=NULL;
	reg_system_para_t* para_sys;
	
    para_sys = (reg_system_para_t*)dsk_reg_get_para_by_app(REG_APP_SYSTEM);
	para = (reg_sxm_para_t*)dsk_reg_get_para_by_app(REG_APP_SXM);

	__msg("paint update channeinfo begin...\n");
	if(sxm_para == NULL)
		return EPDK_FAIL;

	if(para->replay.state != SXM_REPLAY_LIVE) //mllee 150506 added for replay win restore
	{
		sxm_para->ChanID = para->replay_meta_ind.ChanID;
		sxm_para->CatID = para->replay_meta_ind.CatID;
#ifdef SXM_NEW_PLAYINFO_LONG_SCROLL
		eLIBs_strcpy(sxm_para->ChanName,para->replay_meta_ind.ChanNameLong);
#else
		eLIBs_strcpy(sxm_para->ChanName,para->replay_meta_ind.ChanNameShort);
#endif
#ifdef SMX_SONG_TITLE_USE_EXTED
		eLIBs_strcpy(sxm_para->Song,para->replay_meta_ind.SongExtd);
#else
		eLIBs_strcpy(sxm_para->Song,para->replay_meta_ind.SongBasic);
#endif
#ifdef SXM_ARTIST_USE_EXTED
		eLIBs_strcpy(sxm_para->Artist,para->replay_meta_ind.ArtistExtd);
#else
		eLIBs_strcpy(sxm_para->Artist,para->replay_meta_ind.ArtistBasic);
#endif
		eLIBs_strcpy(sxm_para->ContentInfo,para->replay_meta_ind.ContentInfo);
		eLIBs_strcpy(sxm_para->CatName,para->replay_meta_ind.CatNameShort);

        eLIBs_strcpy(str,"CH ");
		sprintf(string,"%03d",para->replay_meta_ind.ChanID);
        lv_label_set_text(ui_sxm_play_top_num, string);
	}
	else
	{
		sxm_para->ChanID = para->chan_select_ind.ChanID;
		sxm_para->CatID = para->chan_select_ind.CatID;
#ifdef SXM_NEW_PLAYINFO_LONG_SCROLL
		eLIBs_strcpy(sxm_para->ChanName,para->chan_select_ind.ChanNameLong);
#else
		eLIBs_strcpy(sxm_para->ChanName,para->chan_select_ind.ChanNameShort);
#endif
#ifdef SMX_SONG_TITLE_USE_EXTED
		eLIBs_strcpy(sxm_para->Song,para->chan_select_ind.SongExtd);
#else
		eLIBs_strcpy(sxm_para->Song,para->chan_select_ind.SongBasic);
#endif
#ifdef SXM_ARTIST_USE_EXTED
		eLIBs_strcpy(sxm_para->Artist,para->chan_select_ind.ArtistExtd);
#else
		eLIBs_strcpy(sxm_para->Artist,para->chan_select_ind.ArtistBasic);
#endif
		eLIBs_strcpy(sxm_para->ContentInfo,para->chan_select_ind.ContentInfo);
		eLIBs_strcpy(sxm_para->CatName,para->chan_select_ind.CatNameShort);
#ifdef SXM_POWER_ON_NO_SELECT_CHANNEL_PATCH	
		if(para->chan_select_ind.SID == 0xFFFF)
			return EPDK_FAIL;
#endif

        eLIBs_strcpy(str,"CH ");
		sprintf(string,"%03d",para->chan_select_ind.ChanID);
		sxm_para->CurChanID = para->chan_select_ind.ChanID;
        lv_label_set_text(ui_sxm_play_top_num, string);
	}

	eLIBs_strcat(str,string);
	__msg("chan:%x\n",para->chan_select_ind.ChanID);

	//channel name
	//for name
	if(para->replay.state != SXM_REPLAY_LIVE)
	{
		eLIBs_strcpy(channel_name,para->replay_meta_ind.ChanNameLong);
	}
	else
	{
		eLIBs_strcpy(channel_name,para->chan_select_ind.ChanNameLong);
	}
    
	lv_label_set_text(ui_sxm_play_ind_text_panel_name_text, channel_name);

	//category name
	//for name
	if(para->replay.state != SXM_REPLAY_LIVE)
	{
		//if(para->replay_meta_ind.CatID != 0) //mllee 150504 added
		//	GUI_DispStringInRect(para->replay_meta_ind.CatNameLong,&rect,GUI_TA_LEFT|GUI_TA_VCENTER);//GUI_TA_BOTTOM);//	
        lv_label_set_text(ui_sxm_play_top_category, para->replay_meta_ind.CatNameLong);
	}
	else
	{
		//if(para->chan_select_ind.CatID != 0) //mllee 150504 added
		//	GUI_DispStringInRect(para->chan_select_ind.CatNameLong,&rect,GUI_TA_LEFT|GUI_TA_VCENTER);//GUI_TA_BOTTOM);//	
        lv_label_set_text(ui_sxm_play_top_category, para->chan_select_ind.CatNameLong);
	}

	//artist + info
	//for icon
	//for name
	if(para->replay.state != SXM_REPLAY_LIVE)
	{
#ifdef SXM_ARTIST_USE_EXTED
		if(para->replay_meta_ind.ArtistExtd[0])
			eLIBs_strcpy(artist_info, para->replay_meta_ind.ArtistExtd);
#else
		if(para->replay_meta_ind.ArtistBasic[0])
			eLIBs_strcpy(artist_info, para->replay_meta_ind.ArtistBasic);
#endif
		if(para->replay_meta_ind.ContentInfo[0])
		{
			if(artist_info[0])
				eLIBs_strcat(artist_info," / ");
			eLIBs_strcat(artist_info,para->replay_meta_ind.ContentInfo);
		}
	}
	else
	{
#ifdef SXM_ARTIST_USE_EXTED
		if(para->chan_select_ind.ArtistExtd[0])
			eLIBs_strcpy(artist_info, para->chan_select_ind.ArtistExtd);
#else
		if(para->chan_select_ind.ArtistBasic[0])
			eLIBs_strcpy(artist_info, para->chan_select_ind.ArtistBasic);
#endif
		if(para->chan_select_ind.ContentInfo[0])
		{
			if(artist_info[0])
				eLIBs_strcat(artist_info," / ");
			eLIBs_strcat(artist_info,para->chan_select_ind.ContentInfo);
		}
	}
    
	if(para->channel_lock[para->chan_select_ind.SID] != 0)
	{
		eLIBs_strcpy(artist_info,str);
	}
    
	lv_label_set_text(ui_sxm_play_ind_text_panel_artist_info_text, artist_info);

	//song title
	if(para->channel_lock[para->chan_select_ind.SID] != 0)
	{
		eLIBs_strcpy(song_title,str);
	}
	else
	{
		if(para->replay.state != SXM_REPLAY_LIVE)
		{
#ifdef SMX_SONG_TITLE_USE_EXTED
			eLIBs_strcpy(song_title,para->replay_meta_ind.SongExtd);
#else
			eLIBs_strcpy(song_title,para->replay_meta_ind.SongBasic);
#endif
		}
		else
		{
#ifdef SMX_SONG_TITLE_USE_EXTED
			eLIBs_strcpy(song_title,para->chan_select_ind.SongExtd);
#else
			eLIBs_strcpy(song_title,para->chan_select_ind.SongBasic);
#endif
		}
	}
    
	lv_label_set_text(ui_sxm_play_ind_text_panel_song_text, song_title);

	return EPDK_OK;

}

static __s32 pain_ProgressBar(ui_sxm_para_t * sxm_para)
{
	void *pic_buf = NULL;
	__s32 X = 0, Y = 0;	
	__u8 scroll_percent;
	reg_sxm_para_t* para=NULL;
	reg_system_para_t* para_sys;
	
    para_sys = (reg_system_para_t*)dsk_reg_get_para_by_app(REG_APP_SYSTEM);
	para = (reg_sxm_para_t*)dsk_reg_get_para_by_app(REG_APP_SXM);
	
	if(sxm_para == NULL)
		return EPDK_FAIL;

	scroll_percent = para->replay.playback_percent;

    __sxm_msg("scroll_percent=%d\n", scroll_percent);
	lv_slider_set_value(ui_sxm_play_bottom_bar, scroll_percent, LV_ANIM_OFF);

	return EPDK_OK;
}


static __s32 paint_update_replay_time(ui_sxm_para_t * sxm_para)
{
	char str[20]={0};
	char time_text[20];
	__u32 hours;
	__u32 str_w;
	__u32 time;
	reg_sxm_para_t* para=NULL;
	reg_system_para_t* para_sys;
	
    para_sys = (reg_system_para_t*)dsk_reg_get_para_by_app(REG_APP_SYSTEM);
	para = (reg_sxm_para_t*)dsk_reg_get_para_by_app(REG_APP_SXM);
	
	if(sxm_para == NULL)
		return EPDK_FAIL;


	hours = para->replay.time_remain/3600;
	time = para->replay.time_remain;
	time = time*1000;

	if(para->replay.state == SXM_REPLAY_LIVE)
	{
		return EPDK_OK;
	}
	eLIBs_strcpy(time_text,"-");
	time2str_by_format(time, str, TIME_AUTO_HMS);	
	eLIBs_strcat(time_text,str);
    lv_label_set_text(ui_sxm_play_bottom_current, time_text);
    
	pain_ProgressBar(sxm_para);
	
	return EPDK_OK;
}

static __s32 paint_fastseek_update_channelinfo(ui_sxm_para_t * sxm_para, __s32 index)
{
	GUI_RECT rect;
	char str[32]={0};
	char string[16] = {0};
	char artist_info[64] = {0};
	__s32 cat_index =0;
	__u32 str_w;
	void *pic_buf = NULL;
	reg_sxm_para_t* para=NULL;
	reg_system_para_t* para_sys;
	
       para_sys = (reg_system_para_t*)dsk_reg_get_para_by_app(REG_APP_SYSTEM);
	para = (reg_sxm_para_t*)dsk_reg_get_para_by_app(REG_APP_SXM);
	
	if(sxm_para == NULL)
		return EPDK_FAIL;

	if(para->pix_onshow_flag != SXM_PIX_SHOW_SERVICE_DEFAULT)
		paint_update_albumArt(sxm_para);
	
	sxm_para->CurChanID = para->chan_meta_ind_ptr[index].ChanID;
		
	//channel number
	eLIBs_strcpy(str,"CH ");

	sprintf(string,"%03d",para->chan_meta_ind_ptr[index].ChanID);

	eLIBs_strcat(str,string);
    lv_label_set_text(ui_sxm_play_top_num, string);

	//channel name
	//for name
	//GUI_DispStringInRect(para->chan_meta_ind_ptr[index].ChanNameLong,&rect,GUI_TA_LEFT|GUI_TA_VCENTER);//GUI_TA_BOTTOM);//	
	lv_label_set_text(ui_sxm_play_ind_text_panel_name_text, para->chan_meta_ind_ptr[index].ChanNameLong);

	//category name
	cat_index = sxm_get_cat_index_by_catID(sxm_para,para->chan_meta_ind_ptr[index].CatID);
	//for name
	if(cat_index != -1)
	{
		//GUI_DispStringInRect(para->cat_info_ind_ptr[cat_index].CatNameLong,&rect,GUI_TA_LEFT|GUI_TA_VCENTER);	//GUI_TA_BOTTOM);//
        lv_label_set_text(ui_sxm_play_top_category, para->cat_info_ind_ptr[cat_index].CatNameLong);
	}
	
	//artist + content info
	//for icon
	//for name
#ifdef SXM_ARTIST_USE_EXTED
	if(para->chan_meta_ind_ptr[index].ArtistExtd[0])
		eLIBs_strcpy(artist_info, para->chan_meta_ind_ptr[index].ArtistExtd);
#else
	if(para->chan_meta_ind_ptr[index].ArtistBasic[0])
		eLIBs_strcpy(artist_info, para->chan_meta_ind_ptr[index].ArtistBasic);
#endif
	if(para->chan_meta_ind_ptr[index].ContentInfo[0])
	{
		if(artist_info[0])
			eLIBs_strcat(artist_info," / ");
		eLIBs_strcat(artist_info,para->chan_meta_ind_ptr[index].ContentInfo);
	}
	//GUI_DispStringInRect(artist_info,&rect,GUI_TA_LEFT|GUI_TA_VCENTER);//GUI_TA_BOTTOM);//	
	lv_label_set_text(ui_sxm_play_ind_text_panel_artist_info_text, artist_info);

	//song title
	//for icon
	//for name
#ifdef SMX_SONG_TITLE_USE_EXTED
	//GUI_DispStringInRect(para->chan_meta_ind_ptr[index].SongExtd,&rect,GUI_TA_LEFT|GUI_TA_VCENTER);	//GUI_TA_BOTTOM);//
	lv_label_set_text(ui_sxm_play_ind_text_panel_song_text, para->chan_meta_ind_ptr[index].SongExtd);
#else
	//GUI_DispStringInRect(para->chan_meta_ind_ptr[index].SongBasic,&rect,GUI_TA_LEFT|GUI_TA_VCENTER);//GUI_TA_BOTTOM);//	
	lv_label_set_text(ui_sxm_play_ind_text_panel_song_text, para->chan_meta_ind_ptr[index].SongBasic);
#endif
	
	{//send to mcu ChanID and SID
		__u8 data[4];
		data[0] = (__u8)((para->chan_meta_ind_ptr[index].ChanID>>8)&0xFF);
		data[1] = (__u8)(para->chan_meta_ind_ptr[index].ChanID&0xFF);
		data[2] = (__u8)((para->chan_meta_ind_ptr[index].SID>>8)&0xFF);
		data[3] = (__u8)(para->chan_meta_ind_ptr[index].SID&0xFF);
		dsk_send_mcu_cmd(UART_SEND_SXM_FAST_SEEK,data);
	}

	return EPDK_OK;
}

static __s32 is_channel_meta_not_blank(ui_sxm_para_t * sxm_para)
{
	reg_sxm_para_t* para=NULL;
	
	para = (reg_sxm_para_t*)dsk_reg_get_para_by_app(REG_APP_SXM);

	if(sxm_para == NULL)
		return EPDK_FAIL;
	if(para->replay.state != SXM_REPLAY_LIVE) //replay mode
	{
#ifdef SXM_NEW_PLAYINFO_LONG_SCROLL
		if(eLIBs_strlen(para->replay_meta_ind.ChanNameLong) != 0)
			return 1;
#else
		if(eLIBs_strlen(para->replay_meta_ind.ChanNameShort) != 0)
			return 1;
#endif
#ifdef SMX_SONG_TITLE_USE_EXTED
		if(eLIBs_strlen(para->replay_meta_ind.SongExtd) != 0)
			return 1;
#else
		if(eLIBs_strlen(para->replay_meta_ind.SongBasic) != 0)
			return 1;
#endif
#ifdef SXM_ARTIST_USE_EXTED
		if(eLIBs_strlen(para->replay_meta_ind.ArtistExtd) != 0)
			return 1;
#else
		if(eLIBs_strlen(para->replay_meta_ind.ArtistBasic) != 0)
			return 1;
#endif
		if(eLIBs_strlen(para->replay_meta_ind.ContentInfo) != 0)
			return 1;
	}
	else// if(sxm_para->win_type == SXM_LIVE_PLAY_WIN) //live play mode
	{
#ifdef SXM_NEW_PLAYINFO_LONG_SCROLL
		if(eLIBs_strlen(para->chan_select_ind.ChanNameLong) != 0)
			return 1;
#else
		if(eLIBs_strlen(para->chan_select_ind.ChanNameShort) != 0)
			return 1;
#endif
#ifdef SMX_SONG_TITLE_USE_EXTED
		if(eLIBs_strlen(para->chan_select_ind.SongExtd) != 0)
			return 1;
#else
		if(eLIBs_strlen(para->chan_select_ind.SongBasic) != 0)
			return 1;
#endif
#ifdef SXM_ARTIST_USE_EXTED
		if(eLIBs_strlen(para->chan_select_ind.ArtistExtd) != 0)
			return 1;
#else
		if(eLIBs_strlen(para->chan_select_ind.ArtistBasic) != 0)
			return 1;
#endif
		if(eLIBs_strlen(para->chan_select_ind.ContentInfo) != 0)
			return 1;
	}

	return 0;
}

static __s32 sxm_open_paint(ui_sxm_para_t * sxm_para)
{
	__u8 notice_close_flag = 0;
	reg_sxm_para_t* para=NULL;
	
	para = (reg_sxm_para_t*)dsk_reg_get_para_by_app(REG_APP_SXM);

	if(sxm_para == NULL)
		return EPDK_FAIL;
	
	paint_update_head_source(sxm_para);
	paint_update_signal(sxm_para);
	paint_update_preset(sxm_para); //mllee 159605 for bug
	//paint_update_albumArt(sxm_para,lyr);
	__msg("open paint: pix_onshow = %d\n",para->pix_onshow_flag);
	if(para->pix_onshow_flag != SXM_PIX_SHOW_NONE || is_channel_meta_not_blank(sxm_para) == 1) //pix onshow or channel meta data not blank mllee 150614 mod
	{
		para->pix_onshow_flag = SXM_PIX_SHOW_NONE;
#ifdef SXM_BF_B_ENABLE
		if(para->replay.state != SXM_REPLAY_LIVE)
			show_ir_albumart(sxm_para);
		else
#endif
		show_albumart_by_type_match(sxm_para);
	}
#ifdef SXM_POPUP_ON_ALL_WIN_PATCH //mllee 160303

#else
	if(sxm_para->connect_state == SXM_CONNECT_STA_OK)//(para->connect_state == SXM_CONNECT_STA_OK)
#endif
	{
		paint_update_channelinfo(sxm_para);
	}

	return EPDK_OK;
}


//menu function
__s32 ui_sxm_draw_item_disable(lv_obj_t * item, int disable)
{
	__u32 i;
	__u32 child_cnt;
	lv_obj_t * item_panel;
    lv_obj_t * item_text_panel;
	lv_obj_t * item_text;
    lv_obj_t * item_content_panel;
	lv_obj_t * item_content;
	lv_obj_t * item_content_child;

	_ui_state_modify(item, LV_STATE_DISABLED, disable);

	item_panel = lv_obj_get_child(item, 0);
	_ui_state_modify(item_panel, LV_STATE_DISABLED, disable);

	item_text_panel = lv_obj_get_child(item_panel, 0);
	_ui_state_modify(item_text_panel, LV_STATE_DISABLED, disable);

	item_text = lv_obj_get_child(item_text_panel, 0);
	_ui_state_modify(item_text, LV_STATE_DISABLED, disable);

	if(disable == _UI_MODIFY_STATE_REMOVE)
		_ui_set_text_shadow(item_text, GUI_WHITE, GUI_BLACK, 3, 3);
	else
		_ui_remove_text_shadow(item_text, GUI_DARKGRAY);
		
	item_content_panel = lv_obj_get_child(item_panel, 1);
	_ui_state_modify(item_content_panel, LV_STATE_DISABLED, disable);
    
	item_content = lv_obj_get_child(item_content_panel, 0);
	_ui_state_modify(item_content, LV_STATE_DISABLED, disable);
	
	child_cnt = lv_obj_get_child_cnt(item_content);
	for(i = 0; i < child_cnt; i++)
	{
		item_content_child = lv_obj_get_child(item_content, i);
		_ui_state_modify(item_content_child, LV_STATE_DISABLED, disable);
	}
}

static __s32 ui_sxm_get_item_type(__lv_draw_para_t *draw_param)
{
	__s32 ret = SOURCE_ITEM_TYPE_ENTER;
	__s32 index = draw_param->index;
	ui_sxm_para_t * sxm_para = (ui_sxm_para_t *)draw_param->attr;

    switch(sxm_para->win_type)
    {    
        case SXM_WIN_LIST:
        {
            ret = SOURCE_ITEM_TYPE_PRESET;
        }
        break;
        
        case SXM_WIN_FUNC:
        {
            switch(index)
            {
                //case SXM_FUNC_JUMP_TO_LIVE:
                //case SXM_FUNC_DIRECT_TUNING:
                case SXM_FUNC_PARENTAL_CONTROL:
                case SXM_FUNC_RESET:
                {
                    ret = SOURCE_ITEM_TYPE_ENTER;
                }
                break;
            
                /*case SXM_FUNC_CATEGORY:
                {
                    ret = SOURCE_ITEM_TYPE_TEXT;
                }
                break;*/
            }
        }
        break;

        case SXM_WIN_PARENTAL_CTRL:
        {          
            switch(index)
            {
                case SXM_PARENTAL_CTRL_CODE:
                {
                    ret = SOURCE_ITEM_TYPE_ARROW;
                }
                break;

                case SXM_PARENTAL_CTRL_LOCK_CHAN:
                case SXM_PARENTAL_CTRL_CHANGE_CODE:
                {
                    ret = SOURCE_ITEM_TYPE_ENTER;
                }
                break;
            }
        }
        break;
        
        case SXM_WIN_LOCK_CHAN:
        {          
            ret = SOURCE_ITEM_TYPE_TEXT;
        }
        break;
    }

	return ret; 
}

static __s32 ui_sxm_draw_listview_item_text(__lv_draw_para_t *draw_param, lv_obj_t * text, __u8 item_type, __u8 index)
{
	int ret;
	__s32 item_index = draw_param->index;
    __u8 text_index;
	__u8 ucStringBuf[128 + 1]={0};
	ui_sxm_para_t * sxm_para = (ui_sxm_para_t *)draw_param->attr;
	reg_sxm_para_t* para = NULL;

	para = (reg_sxm_para_t*)dsk_reg_get_para_by_app(REG_APP_SXM);

	__sxm_msg("item_index = %d, item_type = %d, win_type = %d\n", item_index, item_type, sxm_para->win_type);

   switch(sxm_para->win_type)
   {    
        case SXM_WIN_LIST:
        {
            item_index = draw_param->index * 2 + index / 2;
            text_index = index % 2;
            
            if(text_index == 0)
            {
                sprintf(ucStringBuf, "%d", item_index + 1);
                //__sxm_msg("str = %s\n",ucStringBuf);
                        
                lv_label_set_text(text, ucStringBuf);
            }
            else if(text_index == 1)
            {
                __u32 frq_number;    
                __u8 frq_int_str[8]={0};
                __u8 frq_dec_str[8]={0};

                if(1)
                {
                    sprintf(ucStringBuf, "+");
                    lv_obj_set_style_text_font(text, lv_font_medium.font, LV_PART_MAIN | LV_STATE_DEFAULT);
                }
                else
                {
                    //frq_number = para->FM1_3_AM1_2_freq[fm_para->curFM1_3AM1_2_id][item_index]/(1000);
                    eLIBs_uint2str_dec(frq_number, frq_int_str);
                    //frq_number = (para->FM1_3_AM1_2_freq[fm_para->curFM1_3AM1_2_id][item_index]%(1000))/100;
                    eLIBs_uint2str_dec(frq_number, frq_dec_str);
                    eLIBs_strcat(frq_int_str,".");
                    eLIBs_strcat(frq_int_str,frq_dec_str);
                    //frq_number = (para->FM1_3_AM1_2_freq[fm_para->curFM1_3AM1_2_id][item_index]%(1000))%(100)/10;
                    eLIBs_uint2str_dec(frq_number, frq_dec_str);
                    eLIBs_strcat(frq_int_str,frq_dec_str);
                    //frq_number = (para->FM1_3_AM1_2_freq[fm_para->curFM1_3AM1_2_id][item_index]%(1000))%(100)%(10);
                    eLIBs_uint2str_dec(frq_number, frq_dec_str);
                    eLIBs_strcat(frq_int_str,frq_dec_str);   
                    eLIBs_strcpy(ucStringBuf, frq_int_str);
                    //sprintf(ucStringBuf, "%d", para->FM1_3_AM1_2_freq[fm_para->curFM1_3AM1_2_id][item_index]);
                    lv_obj_set_style_text_font(text, lv_font_large.font, LV_PART_MAIN | LV_STATE_DEFAULT);
                }
            }
            else  if(text_index == 2)
            {
                //if(para->FM1_3_AM1_2_freq[fm_para->curFM1_3AM1_2_id][item_index] == 0)
                    sprintf(ucStringBuf, " ");
                //else
                //    sprintf(ucStringBuf, "MHz");
            }
            
            __sxm_msg("ucStringBuf = %s\n",ucStringBuf);
            lv_label_set_text(text, ucStringBuf);
        }
        break;

        case SXM_WIN_FUNC:
        {
            if(item_type == SOURCE_ITEM_TEXT_TYPE_TEXT)
            {
                dsk_langres_get_menu_text(sxm_item_string_res_id[item_index], ucStringBuf, 128);
                
                lv_label_set_text(text, ucStringBuf);
                _ui_set_text_shadow_scroll(text, GUI_WHITE, GUI_BLACK, 3, 3);
            }
        }
        break;
        
        case SXM_WIN_CATEGORY:
        {
            if(item_type == SOURCE_ITEM_TEXT_TYPE_TEXT)
            {                
                lv_label_set_text(text, "XXX");
                _ui_set_text_shadow_scroll(text, GUI_WHITE, GUI_BLACK, 3, 3);
            }
        }
        break;
        
        case SXM_WIN_PARENTAL_CTRL:
        {            
            if(item_type == SOURCE_ITEM_TEXT_TYPE_TEXT)
            {
                dsk_langres_get_menu_text(sxm_parental_ctrl_item_string_res_id[item_index], ucStringBuf, 128);
                
                lv_label_set_text(text, ucStringBuf);

                if(draw_param->index > SXM_PARENTAL_CTRL_CODE)
                {
                    if(!sxm_para->parental_onoff)
                    {
                        lv_obj_t * item;
                        lv_obj_t * item_panel;
                        lv_obj_t * item_text_panel;
                        
                        item_text_panel = lv_obj_get_parent(text);
        				item_panel = lv_obj_get_parent(item_text_panel);
                        item = lv_obj_get_parent(item_panel);
                        
                        ui_sxm_draw_item_disable(item, _UI_MODIFY_STATE_ADD);
                    }
                    else
                    {
                        _ui_set_text_shadow_scroll(text, GUI_WHITE, GUI_BLACK, 3, 3);
                    }
                }
                else
                {
                    _ui_set_text_shadow_scroll(text, GUI_WHITE, GUI_BLACK, 3, 3);
                }
            }
        }
        break;

        case SXM_WIN_LOCK_CHAN:
        {          
            lv_label_set_text(text, "AAAAA");
            _ui_set_text_shadow_scroll(text, GUI_WHITE, GUI_BLACK, 3, 3);
        }
        break;
    }
    
				
	return EPDK_OK;
}

static __s32 ui_sxm_draw_listview_item_icon(__lv_draw_para_t *draw_param, lv_obj_t * obj, __u8 item_type)
{
	__s32 ret = EPDK_OK;
	ui_sxm_para_t * sxm_para = (ui_sxm_para_t *)draw_param->attr;

    //eLIBs_printf("draw_param->index=%d\n", draw_param->index);
    switch(draw_param->index)
    {    
        case SXM_PARENTAL_CTRL_CODE:
        {
            lv_obj_t *child = lv_obj_get_child(obj, 0);
            
            __sxm_msg("sxm_para->parental_onoff=%d\n", sxm_para->parental_onoff);
            lv_obj_remove_flag(obj, LV_OBJ_FLAG_CLICKABLE);
            lv_obj_remove_flag(child, LV_OBJ_FLAG_CLICKABLE);

            if(sxm_para->parental_onoff)
            {
                lv_obj_add_state(obj, LV_STATE_CHECKED);
                lv_obj_add_state(child, LV_STATE_CHECKED);
            }
            else
            {
                lv_obj_remove_state(obj, LV_STATE_CHECKED);
                lv_obj_remove_state(child, LV_STATE_CHECKED);
            }
        }
        break;
    }

	return ret;
}

static void * ui_sxm_draw_item(__lv_draw_para_t *draw_param)
{	
	__s32 ret;
    __s32 item_type;
	ui_sxm_para_t * sxm_para = (ui_sxm_para_t *)draw_param->attr;
	
	__sxm_msg("draw_param->index = %d, item_obj = 0x%x\n", draw_param->index, sxm_para->ui_list_para.item_obj);
	lv_obj_t * ui_list_item = lv_obj_create(sxm_para->ui_list_para.item_obj);
    lv_obj_remove_style_all(ui_list_item);
	lv_obj_set_width(ui_list_item, sxm_para->ui_list_para.item_width);
	lv_obj_set_height(ui_list_item, sxm_para->ui_list_para.item_height);
	lv_obj_clear_flag(ui_list_item, LV_OBJ_FLAG_SCROLLABLE);	  /// Flags
    lv_obj_add_flag(ui_list_item, LV_OBJ_FLAG_EVENT_BUBBLE | LV_OBJ_FLAG_GESTURE_BUBBLE);      /// Flags
	lv_obj_set_style_radius(ui_list_item, 0, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_bg_color(ui_list_item, lv_color_hex(0x000000), LV_PART_MAIN | LV_STATE_DEFAULT);
	lv_obj_set_style_bg_opa(ui_list_item, 255, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_border_width(ui_list_item, 2, LV_PART_MAIN | LV_STATE_FOCUSED);
    lv_obj_set_style_border_color(ui_list_item, lv_color_hex(0xff0000), LV_PART_MAIN | LV_STATE_FOCUSED);
    lv_obj_set_style_border_opa(ui_list_item, 255, LV_PART_MAIN | LV_STATE_FOCUSED);

    item_type = ui_sxm_get_item_type(draw_param);

    if(item_type == SOURCE_ITEM_TYPE_PRESET)
    {
        lv_obj_t * ui_list_item_panel = lv_obj_create(ui_list_item);
        lv_obj_remove_style_all(ui_list_item_panel);
        lv_obj_set_x(ui_list_item_panel, lv_pct(1));
        lv_obj_set_height(ui_list_item_panel, lv_pct(97));
        lv_obj_set_width(ui_list_item_panel, lv_pct(97));
        lv_obj_set_align(ui_list_item_panel, LV_ALIGN_LEFT_MID);
        lv_obj_set_flex_flow(ui_list_item_panel, LV_FLEX_FLOW_ROW);
		lv_obj_set_flex_align(ui_list_item_panel, LV_FLEX_ALIGN_SPACE_EVENLY, LV_FLEX_ALIGN_CENTER, LV_FLEX_ALIGN_CENTER);
        lv_obj_add_flag(ui_list_item_panel, LV_OBJ_FLAG_EVENT_BUBBLE | LV_OBJ_FLAG_GESTURE_BUBBLE);      /// Flags
        lv_obj_clear_flag(ui_list_item_panel, LV_OBJ_FLAG_CLICKABLE | LV_OBJ_FLAG_SCROLLABLE);      /// Flags
        lv_obj_set_style_bg_opa(ui_list_item_panel, 0, LV_PART_MAIN | LV_STATE_DEFAULT);
        
        for(__u8 i = 0; i < 2; i++)
        {
            lv_obj_t * ui_list_item_text_panel = lv_obj_create(ui_list_item_panel);
            //lv_obj_remove_style_all(ui_list_item_text_panel);
            lv_obj_set_height(ui_list_item_text_panel, lv_pct(100));
            lv_obj_set_width(ui_list_item_text_panel, lv_pct(49));
            lv_obj_set_align(ui_list_item_text_panel, LV_ALIGN_CENTER);
            lv_obj_clear_flag(ui_list_item_text_panel, LV_OBJ_FLAG_SCROLLABLE);      /// Flags
            lv_obj_add_flag(ui_list_item_text_panel, LV_OBJ_FLAG_EVENT_BUBBLE | LV_OBJ_FLAG_GESTURE_BUBBLE);      /// Flags
            lv_obj_set_style_bg_color(ui_list_item_text_panel, lv_color_hex(0x303030), LV_PART_MAIN | LV_STATE_DEFAULT);
            lv_obj_set_style_bg_opa(ui_list_item_text_panel, 255, LV_PART_MAIN | LV_STATE_DEFAULT);
            lv_obj_set_style_border_width(ui_list_item_text_panel, 0, LV_PART_MAIN | LV_STATE_DEFAULT);
            lv_obj_set_style_bg_color(ui_list_item_text_panel, lv_color_hex(0x303030), LV_PART_MAIN | LV_STATE_PRESSED);
            lv_obj_set_style_bg_opa(ui_list_item_text_panel, 255, LV_PART_MAIN | LV_STATE_PRESSED);
            lv_obj_set_style_bg_grad_color(ui_list_item_text_panel, lv_color_hex(0xD90011), LV_PART_MAIN | LV_STATE_PRESSED);
            lv_obj_set_style_bg_main_stop(ui_list_item_text_panel, 150, LV_PART_MAIN | LV_STATE_PRESSED);
            lv_obj_set_style_bg_grad_stop(ui_list_item_text_panel, 255, LV_PART_MAIN | LV_STATE_PRESSED);
            lv_obj_set_style_bg_grad_dir(ui_list_item_text_panel, LV_GRAD_DIR_VER, LV_PART_MAIN | LV_STATE_PRESSED);
            
            lv_obj_set_style_border_width(ui_list_item_text_panel, 2, LV_PART_MAIN | LV_STATE_FOCUSED);
            lv_obj_set_style_border_color(ui_list_item_text_panel, lv_color_hex(0xff0000), LV_PART_MAIN | LV_STATE_FOCUSED);
            lv_obj_set_style_border_opa(ui_list_item_text_panel, 255, LV_PART_MAIN | LV_STATE_FOCUSED);

            lv_obj_t * ui_list_item_text_index = lv_label_create(ui_list_item_text_panel);
            lv_obj_set_width(ui_list_item_text_index, LV_SIZE_CONTENT);
            lv_obj_set_height(ui_list_item_text_index, LV_SIZE_CONTENT);
            lv_obj_set_align(ui_list_item_text_index, LV_ALIGN_TOP_LEFT);
            lv_label_set_long_mode(ui_list_item_text_index, LV_LABEL_LONG_CLIP);
            lv_obj_set_style_text_color(ui_list_item_text_index, lv_color_hex(0xFFFFFF), LV_PART_MAIN | LV_STATE_DEFAULT);
            lv_obj_set_style_text_opa(ui_list_item_text_index, 255, LV_PART_MAIN | LV_STATE_DEFAULT);
            lv_obj_set_style_text_font(ui_list_item_text_index, lv_font_small.font, LV_PART_MAIN | LV_STATE_DEFAULT);
            ui_sxm_draw_listview_item_text(draw_param, ui_list_item_text_index, SOURCE_ITEM_TEXT_TYPE_TEXT, i * 2 + 0); 

            lv_obj_t * ui_list_item_text_content = lv_label_create(ui_list_item_text_panel);
            lv_obj_set_width(ui_list_item_text_content, LV_SIZE_CONTENT);
            lv_obj_set_height(ui_list_item_text_content, LV_SIZE_CONTENT);
            lv_obj_set_align(ui_list_item_text_content, LV_ALIGN_CENTER);
            lv_label_set_long_mode(ui_list_item_text_content, LV_LABEL_LONG_CLIP);
            lv_obj_set_style_text_color(ui_list_item_text_content, lv_color_hex(0xFFFFFF), LV_PART_MAIN | LV_STATE_DEFAULT);
            lv_obj_set_style_text_opa(ui_list_item_text_content, 255, LV_PART_MAIN | LV_STATE_DEFAULT);
            lv_obj_set_style_text_font(ui_list_item_text_content, lv_font_medium.font, LV_PART_MAIN | LV_STATE_DEFAULT);
            
            ui_sxm_draw_listview_item_text(draw_param, ui_list_item_text_content, SOURCE_ITEM_TEXT_TYPE_TEXT, i * 2 + 1); 
        }

        lv_obj_add_event_cb(ui_list_item, ui_event_sxm_preset_screen, LV_EVENT_ALL, sxm_para);
    }
    else
    {
        lv_obj_t * ui_list_item_panel = lv_obj_create(ui_list_item);
        lv_obj_remove_style_all(ui_list_item_panel);
        lv_obj_set_x(ui_list_item_panel, lv_pct(1));
        lv_obj_set_height(ui_list_item_panel, lv_pct(95));
        lv_obj_set_width(ui_list_item_panel, lv_pct(97));
    	lv_obj_set_align(ui_list_item_panel, LV_ALIGN_LEFT_MID);
        lv_obj_add_flag(ui_list_item_panel, LV_OBJ_FLAG_EVENT_BUBBLE | LV_OBJ_FLAG_GESTURE_BUBBLE);      /// Flags
        lv_obj_clear_flag(ui_list_item_panel, LV_OBJ_FLAG_CLICKABLE | LV_OBJ_FLAG_SCROLLABLE);      /// Flags
        lv_obj_set_style_bg_color(ui_list_item_panel, lv_color_hex(0x303030), LV_PART_MAIN | LV_STATE_DEFAULT);
        lv_obj_set_style_bg_opa(ui_list_item_panel, 255, LV_PART_MAIN | LV_STATE_DEFAULT);
        //lv_obj_set_style_bg_image_src(ui_list_item_panel, ui_sxm_get_res(sxm_para, RIG_THMEM_TEMP_COM_LIST_BG_BMP), LV_PART_MAIN | LV_STATE_DEFAULT);
        lv_obj_set_style_border_width(ui_list_item_panel, 0, LV_PART_MAIN | LV_STATE_DEFAULT);
        lv_obj_set_style_bg_color(ui_list_item_panel, lv_color_hex(0x303030), LV_PART_MAIN | LV_STATE_PRESSED);
        lv_obj_set_style_bg_opa(ui_list_item_panel, 255, LV_PART_MAIN | LV_STATE_PRESSED);
        lv_obj_set_style_bg_grad_color(ui_list_item_panel, lv_color_hex(0xD90011), LV_PART_MAIN | LV_STATE_PRESSED);
        lv_obj_set_style_bg_main_stop(ui_list_item_panel, 150, LV_PART_MAIN | LV_STATE_PRESSED);
        lv_obj_set_style_bg_grad_stop(ui_list_item_panel, 255, LV_PART_MAIN | LV_STATE_PRESSED);
        lv_obj_set_style_bg_grad_dir(ui_list_item_panel, LV_GRAD_DIR_VER, LV_PART_MAIN | LV_STATE_PRESSED);
        
        lv_obj_t * ui_list_item_text_panel = lv_obj_create(ui_list_item_panel);
        lv_obj_remove_style_all(ui_list_item_text_panel);
    	lv_obj_set_x(ui_list_item_text_panel, lv_pct(3));
        lv_obj_set_height(ui_list_item_text_panel, lv_pct(100));
        lv_obj_set_width(ui_list_item_text_panel, lv_pct(70));
    	lv_obj_set_align(ui_list_item_text_panel, LV_ALIGN_LEFT_MID);
        lv_obj_clear_flag(ui_list_item_text_panel, LV_OBJ_FLAG_CLICKABLE | LV_OBJ_FLAG_SCROLLABLE);      /// Flags
        lv_obj_set_style_bg_opa(ui_list_item_text_panel, 0, LV_PART_MAIN | LV_STATE_DEFAULT);
        
    	lv_obj_t * ui_list_item_text = lv_label_create(ui_list_item_text_panel);
    	lv_obj_set_width(ui_list_item_text, lv_pct(100));//lv_pct(89)
    	lv_obj_set_height(ui_list_item_text, LV_SIZE_CONTENT);	  /// 0
    	lv_obj_set_align(ui_list_item_text, LV_ALIGN_LEFT_MID);
    	lv_label_set_long_mode(ui_list_item_text, LV_LABEL_LONG_CLIP);
    	lv_obj_set_style_text_color(ui_list_item_text, lv_color_hex(0xFFFFFF), LV_PART_MAIN | LV_STATE_DEFAULT);
    	lv_obj_set_style_text_opa(ui_list_item_text, 255, LV_PART_MAIN | LV_STATE_DEFAULT);
    	lv_obj_set_style_text_font(ui_list_item_text, lv_font_medium.font, LV_PART_MAIN | LV_STATE_DEFAULT);
    	__sxm_msg("ui_list_item_text=%p\n", ui_list_item_text);

        lv_obj_t * ui_list_item_content_panel = lv_obj_create(ui_list_item_panel);
        lv_obj_remove_style_all(ui_list_item_content_panel);
        lv_obj_set_width(ui_list_item_content_panel, lv_pct(30));
        lv_obj_set_height(ui_list_item_content_panel, lv_pct(100));
        lv_obj_set_align(ui_list_item_content_panel, LV_ALIGN_RIGHT_MID);
        lv_obj_clear_flag(ui_list_item_content_panel, LV_OBJ_FLAG_CLICKABLE | LV_OBJ_FLAG_SCROLLABLE);      /// Flags
        lv_obj_add_flag(ui_list_item_content_panel, LV_OBJ_FLAG_EVENT_BUBBLE | LV_OBJ_FLAG_GESTURE_BUBBLE);    /// Flags
        lv_obj_set_style_bg_opa(ui_list_item_content_panel, 0, LV_PART_MAIN | LV_STATE_DEFAULT);
    	__sxm_msg("ui_list_item_content_panel=%p\n", ui_list_item_content_panel);

        switch(item_type)
        {
            case SOURCE_ITEM_TYPE_ENTER:
            {
                lv_obj_t * ui_list_item_enter = lv_image_create(ui_list_item_content_panel);
                lv_image_set_src(ui_list_item_enter, ui_sxm_get_res(sxm_para, SXM_TEMP_COM_MENU_ENTER_BMP));
                lv_obj_set_width(ui_list_item_enter, LV_SIZE_CONTENT);   /// 1
                lv_obj_set_height(ui_list_item_enter, LV_SIZE_CONTENT);    /// 1
                lv_obj_set_x(ui_list_item_enter, lv_pct(30));    /// 1
                lv_obj_set_align(ui_list_item_enter, LV_ALIGN_CENTER);
                lv_obj_remove_flag(ui_list_item_enter, LV_OBJ_FLAG_SCROLLABLE);      /// Flags
    		}
            break;

            case SOURCE_ITEM_TYPE_ARROW:
            {
    		    lv_obj_t * ui_list_item_arrow = lv_obj_create(ui_list_item_content_panel);
                lv_obj_set_width(ui_list_item_arrow, 118);
                lv_obj_set_height(ui_list_item_arrow, 69);
                lv_obj_set_align(ui_list_item_arrow, LV_ALIGN_CENTER);
                lv_obj_remove_flag(ui_list_item_arrow, LV_OBJ_FLAG_SCROLLABLE);      /// Flags
                lv_obj_add_flag(ui_list_item_arrow, LV_OBJ_FLAG_CHECKABLE);      /// Flags
                lv_obj_set_style_bg_color(ui_list_item_arrow, lv_color_hex(0xFFFFFF), LV_PART_MAIN | LV_STATE_DEFAULT);
                lv_obj_set_style_bg_opa(ui_list_item_arrow, 0, LV_PART_MAIN | LV_STATE_DEFAULT);
                lv_obj_set_style_bg_image_src(ui_list_item_arrow, ui_sxm_get_res(sxm_para, SXM_THMEM_TEMP_COM_LIST_OFF_BMP),
                                              LV_PART_MAIN | LV_STATE_DEFAULT);
                lv_obj_set_style_border_width(ui_list_item_arrow, 0, LV_PART_MAIN | LV_STATE_DEFAULT);
                lv_obj_set_style_bg_image_src(ui_list_item_arrow, ui_sxm_get_res(sxm_para, SXM_THMEM_TEMP_COM_LIST_ON_BMP),
                                      LV_PART_MAIN | LV_STATE_CHECKED);

                lv_obj_t * ui_list_item_arrow_switch = lv_switch_create(ui_list_item_arrow);
                lv_obj_set_width(ui_list_item_arrow_switch, 90);
                lv_obj_set_height(ui_list_item_arrow_switch, 45);
                lv_obj_set_align(ui_list_item_arrow_switch, LV_ALIGN_CENTER);
                lv_obj_remove_flag(ui_list_item_arrow_switch, LV_OBJ_FLAG_CLICKABLE);    /// Flags
                lv_obj_add_flag(ui_list_item_arrow_switch, LV_OBJ_FLAG_EVENT_BUBBLE);    /// Flags
                lv_obj_set_style_bg_color(ui_list_item_arrow_switch, lv_color_hex(0x272727), LV_PART_MAIN | LV_STATE_DEFAULT);
                lv_obj_set_style_bg_opa(ui_list_item_arrow_switch, 0, LV_PART_MAIN | LV_STATE_DEFAULT);
                lv_obj_set_style_bg_color(ui_list_item_arrow_switch, lv_color_hex(0x222121), LV_PART_MAIN | LV_STATE_CHECKED);
                lv_obj_set_style_bg_opa(ui_list_item_arrow_switch, 0, LV_PART_MAIN | LV_STATE_CHECKED);
                
                lv_obj_set_style_bg_color(ui_list_item_arrow_switch, lv_color_hex(0x272727), LV_PART_INDICATOR | LV_STATE_DEFAULT);
                lv_obj_set_style_bg_opa(ui_list_item_arrow_switch, 0, LV_PART_INDICATOR | LV_STATE_DEFAULT);
                lv_obj_set_style_bg_color(ui_list_item_arrow_switch, lv_color_hex(0x222121), LV_PART_INDICATOR | LV_STATE_CHECKED);
                lv_obj_set_style_bg_opa(ui_list_item_arrow_switch, 0, LV_PART_INDICATOR | LV_STATE_CHECKED);

                lv_obj_set_style_bg_color(ui_list_item_arrow_switch, lv_color_hex(0x575757), LV_PART_KNOB | LV_STATE_DEFAULT);
                lv_obj_set_style_bg_opa(ui_list_item_arrow_switch, 255, LV_PART_KNOB | LV_STATE_DEFAULT);
                lv_obj_set_style_bg_color(ui_list_item_arrow_switch, lv_color_hex(0xFFFFFF), LV_PART_KNOB | LV_STATE_CHECKED);
                lv_obj_set_style_bg_opa(ui_list_item_arrow_switch, 255, LV_PART_KNOB | LV_STATE_CHECKED);

                ui_sxm_draw_listview_item_icon(draw_param, ui_list_item_arrow, 0);

                //lv_obj_add_event_cb(ui_list_item_arrow_switch, ui_event_sxm_menu_arrow_cb, LV_EVENT_ALL, sxm_para);
    		}
            break;

            case SOURCE_ITEM_TYPE_TEXT:
            {
                lv_obj_t * ui_list_item_content_text = lv_label_create(ui_list_item_content_panel);
                lv_obj_set_width(ui_list_item_content_text, LV_SIZE_CONTENT);   /// 1
                lv_obj_set_height(ui_list_item_content_text, LV_SIZE_CONTENT);    /// 1
                lv_obj_set_y(ui_list_item_content_text, 0);
                lv_obj_set_align(ui_list_item_content_text, LV_ALIGN_CENTER);
                lv_obj_set_style_text_color(ui_list_item_content_text, lv_color_hex(0x909090), LV_PART_MAIN | LV_STATE_DEFAULT);
                lv_obj_set_style_text_opa(ui_list_item_content_text, 255, LV_PART_MAIN | LV_STATE_DEFAULT);
                lv_obj_set_style_text_font(ui_list_item_content_text, lv_font_small.font, LV_PART_MAIN | LV_STATE_DEFAULT);

                ui_sxm_draw_listview_item_text(draw_param, ui_list_item_content_text, SOURCE_ITEM_TEXT_TYPE_CONTENT, 0); 
    		}
            break;
        }
        
        ui_sxm_draw_listview_item_text(draw_param, ui_list_item_text, SOURCE_ITEM_TEXT_TYPE_TEXT, 0);  
        
        lv_obj_add_event_cb(ui_list_item, ui_event_sxm_menu_item_cb, LV_EVENT_ALL, sxm_para);
    }
    

	return (void *)ui_list_item;
}

static void ui_sxm_listbar_init(ui_sxm_para_t * sxm_para)
{		
	if(!sxm_para->ui_list_para.item_obj)
	{
        if(sxm_para->win_type == SXM_WIN_LIST)
            sxm_para->menu_obj = ui_menu_create(&sxm_para->ui_list_para, APP_SXM_PRESET_MENU_ID);
        else
		    sxm_para->menu_obj = ui_menu_create(&sxm_para->ui_list_para, APP_SXM_ID);

        ui_sxm_item_group_update(sxm_para, 1);
        ui_source_group_update(sxm_para->source_para, 0);

        if(sxm_para->theme > THEME_NIGHT)
            traverse_children_with_callback(sxm_para, sxm_para->menu_obj, sxm_para->theme_bmp_start, sxm_para->theme_bmp_end, sxm_para->theme_bmp_start, sxm_para->theme_bmp_end, sxm_para->ui_sxm_res_para.lv_sxm_icon, ui_sxm_get_res, NULL, 0);
#ifdef USE_MENU_ANIM
        ui_menu_on_anim(&sxm_para->ui_list_para, sxm_para->menu_anim);
#endif
	}
}

static void ui_sxm_listbar_uninit(ui_sxm_para_t * sxm_para)
{
	if(sxm_para->ui_list_para.item_obj != NULL)
	{
		__sxm_msg("rig_view_funtion_listbar_uninit:item_obj=0x%x\n", sxm_para->ui_list_para.item_obj);
#ifdef USE_MENU_ANIM
        ui_menu_off_anim(sxm_para->menu_anim);
#endif
        ui_source_menu_sel_group_update(sxm_para->source_para, &sxm_para->ui_list_para, ui_sxm_get_item_type, sxm_para, SOURCE_MENU_GROUP_REMOVE_ALL_FLAG_ALL, 0);

		//lv_obj_clean(sxm_para->ui_list_para.item_obj);//删除父对象下的子对象，不删除本身
		ui_menu_destroy(sxm_para->menu_obj);
		sxm_para->ui_list_para.item_obj = NULL;
	}
}

static void ui_sxm_listbar_para_init(ui_sxm_para_t * sxm_para, void *menu_obj, __u32 item_cnt)
{
	sxm_para->ui_list_para.menu_obj = menu_obj;
    sxm_para->ui_list_para.list_attr = (void *)sxm_para;
	sxm_para->ui_list_para.lbar_draw = (__lv_draw_item)ui_sxm_draw_item;
	sxm_para->ui_list_para.item_cnt = item_cnt;
#ifdef USE_MENU_ANIM
    sxm_para->ui_list_para.use_anim = 1;
#endif
    __sxm_msg("item_cnt=%d\n", item_cnt);
}

static void ui_sxm_list_folder_reinit(ui_sxm_para_t * sxm_para, void *menu_obj, __u32 item_cnt)
{
	ui_sxm_listbar_uninit(sxm_para);
    ui_sxm_listbar_para_init(sxm_para, menu_obj, item_cnt);
	ui_sxm_listbar_init(sxm_para);
}

static void ui_sxm_item_group_update(ui_sxm_para_t * sxm_para, __u8 flag)
{
    if(flag == 1)
    {
        if(sxm_para->win_type <= SXM_WIN_PARENTAL_CTRL)
        {
            ui_source_menu_sel_group_update(sxm_para->source_para, &sxm_para->ui_list_para, ui_sxm_get_item_type, sxm_para, SOURCE_MENU_GROUP_ADD, 0);
        }
        else
        {
            ui_source_menu_sel_group_update(sxm_para->source_para, &sxm_para->ui_list_para, ui_sxm_get_item_type, sxm_para, SOURCE_MENU_GROUP_ADD_SUB, 0);
        }
    }
    else
    {
        ui_source_menu_sel_group_update(sxm_para->source_para, &sxm_para->ui_list_para, ui_sxm_get_item_type, sxm_para, SOURCE_MENU_GROUP_REMOVE, 0);
    }
}

static void ui_sxm_bottom_group_update(ui_sxm_para_t * sxm_para, __u8 flag, __u8 index, __u8 force)
{
    if(sxm_para->bottom_fouse_flag == flag && !force)
    {
        __sxm_msg("ui_sxm_bottom_group_update cur is flag=%d, force=%d, not need change!!!!\n", flag, force);
        
        return;
    }
    
    sxm_para->bottom_fouse_flag = flag;
    __sxm_msg("sxm_para->bottom_fouse_flag=%d, index=%d\n", sxm_para->bottom_fouse_flag, index);

    if(flag == 1)
    {
        ui_source_sel_group_update(sxm_para->source_para->ui_bottom, sxm_para->bottom_fouse_index, 1);
        ui_source_group_update(sxm_para->source_para, 0);
        sxm_para->bottom_fouse_index = 0;
    }
    else if(flag == 2)
    {
        sxm_para->bottom_fouse_index = index;
        ui_source_group_update(sxm_para->source_para, 0);
        ui_source_sel_group_update(sxm_para->source_para->ui_bottom, 0, 0);
    }
    else
    {
        ui_source_group_update(sxm_para->source_para, 1);
        ui_source_sel_group_update(sxm_para->source_para->ui_bottom, 0, 0);
        sxm_para->bottom_fouse_index = 0;
    }
}

//win function
static void ui_sxm_normal_win_open(ui_sxm_para_t * sxm_para)
{
    sxm_para->win_type = SXM_WIN_NONE;

    ui_source_title(sxm_para->source_para, "SiriusXM", 1);
    if(sxm_para->bottom_fouse_flag == 0)
        ui_source_group_update(sxm_para->source_para, 1);
}

static void ui_sxm_normal_win_close(ui_sxm_para_t * sxm_para)
{

}

static void ui_sxm_list_win_open(ui_sxm_para_t * sxm_para)
{
    sxm_para->win_type = SXM_WIN_LIST;

    ui_source_title(sxm_para->source_para, "Favorite", 0);
    ui_source_sub_open(sxm_para->source_para);

    ui_sxm_func_screen_init(sxm_para);
    ui_sxm_listbar_para_init(sxm_para, ui_sxm_func_menu, 5);
    ui_sxm_listbar_init(sxm_para);
    ui_sxm_bottom_group_update(sxm_para, 2, 0, 0);
}

static void ui_sxm_list_win_close(ui_sxm_para_t * sxm_para)
{
    //ui_source_group_update(sxm_para->source_para, 1);
    ui_sxm_bottom_group_update(sxm_para, 1, 0, 0);
    ui_sxm_listbar_uninit(sxm_para);
    ui_sxm_func_screen_uninit(sxm_para);

    ui_source_sub_close(sxm_para->source_para);
}

static void ui_sxm_func_win_open(ui_sxm_para_t * sxm_para)
{
    sxm_para->win_type = SXM_WIN_FUNC;
    
    ui_source_title(sxm_para->source_para, "Menu", 0);
    ui_source_sub_open(sxm_para->source_para);

    ui_sxm_bottom_group_update(sxm_para, 0, 0, 0);
    //ui_sxm_func_code_screen_init(sxm_para);
    ui_sxm_func_screen_init(sxm_para);
    ui_sxm_listbar_para_init(sxm_para, ui_sxm_func_menu, SXM_FUNC_MAX);
    ui_sxm_listbar_init(sxm_para);
}

static void ui_sxm_func_win_close(ui_sxm_para_t * sxm_para)
{
    ui_sxm_listbar_uninit(sxm_para);
    ui_sxm_func_screen_uninit(sxm_para);
    //ui_sxm_func_code_screen_uninit(sxm_para);

    ui_source_sub_close(sxm_para->source_para);
}

static void ui_sxm_category_win_open(ui_sxm_para_t * sxm_para)
{
    sxm_para->win_type = SXM_WIN_CATEGORY;

    ui_source_title(sxm_para->source_para, "Category List", 0);
    ui_source_sub_open(sxm_para->source_para);

    ui_sxm_func_screen_init(sxm_para);
    ui_sxm_listbar_para_init(sxm_para, ui_sxm_func_menu, SXM_FUNC_MAX);
    ui_sxm_listbar_init(sxm_para);
    ui_sxm_bottom_group_update(sxm_para, 2, 1, 0);
}

static void ui_sxm_category_win_close(ui_sxm_para_t * sxm_para)
{
    //ui_source_group_update(sxm_para->source_para, 1);
    ui_sxm_bottom_group_update(sxm_para, 1, 0, 0);
    ui_sxm_listbar_uninit(sxm_para);
    ui_sxm_func_screen_uninit(sxm_para);
    
    ui_source_sub_close(sxm_para->source_para);
}

static void ui_sxm_direct_tuning_channel_handler_cb(lv_timer_t * t)
{
	ui_sxm_para_t * sxm_para = lv_timer_get_user_data(t);

    __sxm_msg("ui_sxm_direct_tuning_channel_handler_cb\n");
    sxm_kb_timer_cancel(sxm_para);

    ui_sxm_func_code_screen_uninit(sxm_para);
    //ui_sxm_switch_win(sxm_para, SXM_WIN_FUNC);
}

static void ui_sxm_direct_tuning_channel_handler(ui_sxm_para_t * sxm_para, lv_event_t* e)
{
    __u32 code, i;
    char * text;
    __u8 tuning_channel[3+1] = {0};
    static __u8 event_release_flag = 0;
    static __u8 event_draw_end_flag = 0;
    lv_obj_t* kb = lv_event_get_target(e);
    uint16_t btn_id = lv_btnmatrix_get_selected_btn(kb); // 修正获取按钮ID的方式
    const char* txt = lv_btnmatrix_get_btn_text(kb, btn_id);
    lv_event_code_t event_code = lv_event_get_code(e);

	reg_sxm_para_t* para=NULL;

	para = (reg_sxm_para_t*)dsk_reg_get_para_by_app(REG_APP_SXM);

#if 1
    __sxm_msg("event_code=%d\n", event_code);
	if(event_code == LV_EVENT_READY)
	{		
        text = lv_textarea_get_text(kb);

        if(text != NULL || text[0] == 0)
        {
            for (i = 0; text[i] != '\0' && i < sizeof(tuning_channel); i++) 
            {
                tuning_channel[i] = text[i] - '0';  
            }
            eLIBs_memcpy(sxm_para->tuning_channel, tuning_channel, sizeof(sxm_para->tuning_channel));
        }

        ui_sxm_func_code_screen_uninit(sxm_para);
        //ui_sxm_switch_win(sxm_para, SXM_WIN_FUNC);
	}
#else
	if(event_code == LV_EVENT_VALUE_CHANGED)
	{		
        if(sxm_para->cursor_index == 0)
        {
            sxm_para->cursor_index = 1;
            //eLIBs_strtol(txt, NULL, 10);
            code = eLIBs_atoi(txt);
            sxm_para->tuning_channel[0] = code;
            lv_label_set_text(ui_sxm_func_code_cursor_1, txt);
        }
        else if(sxm_para->cursor_index == 1)
        {
            sxm_para->cursor_index = 2;
            code = eLIBs_atoi(txt);
            sxm_para->tuning_channel[1] = code;
            lv_label_set_text(ui_sxm_func_code_cursor_2, txt);
        }
        else if(sxm_para->cursor_index == 2)
        {
            sxm_para->cursor_index = 3;
            code = eLIBs_atoi(txt);
            sxm_para->tuning_channel[2] = code;
            lv_label_set_text(ui_sxm_func_code_cursor_3, txt);   
        }
	}
    else if(event_code == LV_EVENT_DRAW_POST_END)
    {
        if(sxm_para->cursor_index > 2)
        {
            event_draw_end_flag = 1;
            __sxm_msg("event_release_flag=%d\n", event_release_flag);
            if(event_release_flag == 1)
            {
                event_release_flag = 0;
                event_draw_end_flag = 0;
                sxm_kb_timer_create(sxm_para, ui_sxm_direct_tuning_channel_handler_cb, 100);
            }
        }
    }
    else if(event_code == LV_EVENT_RELEASED)
    {
        if(sxm_para->cursor_index > 2)
        {
            event_release_flag = 1;
            __sxm_msg("event_draw_end_flag=%d\n", event_draw_end_flag);

            if(event_draw_end_flag == 1)
            {
                event_release_flag = 0;
                event_draw_end_flag = 0;
                sxm_kb_timer_create(sxm_para, ui_sxm_direct_tuning_channel_handler_cb, 100);
            }
        }
    }
#endif
}

static void ui_sxm_direct_tuning_win_open(ui_sxm_para_t * sxm_para)
{
    sxm_para->win_type = SXM_WIN_DIRECT_TUNING;

    ui_source_title(sxm_para->source_para, "Direct Tuning", 0);
    ui_source_sub_open(sxm_para->source_para);

    ui_sxm_direct_tuning_screen_init(sxm_para);
    ui_sxm_func_code_screen_init(sxm_para);
    ui_sxm_switch_code_type(sxm_para, CODE_KEYBOARD_DIRECT_TUNING);
    ui_sxm_bottom_group_update(sxm_para, 2, 3, 0);
}

static void ui_sxm_direct_tuning_win_close(ui_sxm_para_t * sxm_para)
{
    //ui_source_group_update(sxm_para->source_para, 1);
    ui_sxm_bottom_group_update(sxm_para, 1, 0, 0);
    ui_sxm_direct_tuning_screen_uninit(sxm_para);
    ui_sxm_func_code_screen_uninit(sxm_para);
    sxm_kb_timer_cancel(sxm_para);

    ui_source_sub_close(sxm_para->source_para);
}

static void ui_sxm_parental_ctrl_win_open(ui_sxm_para_t * sxm_para)
{
    sxm_para->win_type = SXM_WIN_PARENTAL_CTRL;

    ui_source_title(sxm_para->source_para, "Parental Control", 0);
    ui_source_sub_open(sxm_para->source_para);

    ui_sxm_func_screen_init(sxm_para);
    ui_sxm_listbar_para_init(sxm_para, ui_sxm_func_menu, SXM_PARENTAL_CTRL_MAX);
    ui_sxm_listbar_init(sxm_para);
}

static void ui_sxm_parental_ctrl_win_close(ui_sxm_para_t * sxm_para)
{
    ui_sxm_listbar_uninit(sxm_para);
    ui_sxm_func_screen_uninit(sxm_para);

    ui_source_sub_close(sxm_para->source_para);
}

static void ui_sxm_parental_ctrl_code_handler_cb(lv_timer_t * t)
{
	ui_sxm_para_t * sxm_para = lv_timer_get_user_data(t);
	reg_sxm_para_t* para=NULL;

	para = (reg_sxm_para_t*)dsk_reg_get_para_by_app(REG_APP_SXM);

    __sxm_msg("ui_sxm_parental_ctrl_code_handler_cb\n");
    sxm_kb_timer_cancel(sxm_para);

    __sxm_msg("sxm_para->cmp_error_flag=%d\n", sxm_para->cmp_error_flag);
    if(sxm_para->cmp_error_flag == 1)
    {
        sxm_para->cmp_error_flag = 0;
        sxm_para->parental_onoff = 0;
        sxm_tips_dialog_create(sxm_para, STRING_DIALOG_PARENTAL_CODE_ERROR, 0, 200, ADLG_NOTHING);
    }
    else
    {
        sxm_para->parental_onoff = 1;
        sxm_tips_dialog_create(sxm_para, STRING_DIALOG_PARENTAL_CODE, STRING_DIALOG_PARENTAL_CODE_1, 200, ADLG_NOTHING);
    }

    //ui_sxm_switch_win(sxm_para, SXM_WIN_PARENTAL_CTRL);
    ui_sxm_func_code_screen_uninit(sxm_para);
    ui_sxm_list_folder_reinit(sxm_para, ui_sxm_func_menu, SXM_PARENTAL_CTRL_MAX);
    ui_sxm_item_group_update(sxm_para, 1);
}

static void ui_sxm_parental_ctrl_code_handler(ui_sxm_para_t * sxm_para, lv_event_t* e)
{
    __u32 code, i;
    char * text;
	char parental_code[8+1] = {0};
    static __u8 event_release_flag = 0;
    static __u8 event_draw_end_flag = 0;
    lv_obj_t* kb = lv_event_get_target(e);
    lv_obj_t * textarea = lv_keyboard_get_textarea(kb);
    uint16_t btn_id = lv_btnmatrix_get_selected_btn(kb); // 修正获取按钮ID的方式
    const char* txt = lv_btnmatrix_get_btn_text(kb, btn_id);
    lv_event_code_t event_code = lv_event_get_code(e);
	reg_sxm_para_t* para=NULL;

	para = (reg_sxm_para_t*)dsk_reg_get_para_by_app(REG_APP_SXM);

#if 1
    if(event_code == LV_EVENT_READY)
    {       
        text = lv_textarea_get_text(textarea);

        __sxm_msg("text[0]=%d\n", text[0]);
        if(text == NULL || text[0] == 0)
        {
            sxm_para->parental_onoff = 0;
            sxm_tips_dialog_create(sxm_para, STRING_DIALOG_PARENTAL_CODE_ERROR, 0, 200, ADLG_NOTHING);
            return;
        }
                
        for (i = 0; text[i] != '\0' && i < sizeof(parental_code); i++) 
        {
            parental_code[i] = text[i] - '0';  
        }

        if(i != 4)
        {
            return;
        }
        
        if(eLIBs_memcmp(parental_code, sxm_para->parental_code, sizeof(parental_code)) == EPDK_NO)
        {
            sxm_para->parental_onoff = 0;
            sxm_tips_dialog_create(sxm_para, STRING_DIALOG_PARENTAL_CODE_ERROR, 0, 200, ADLG_NOTHING);
        }
        else
        {
            sxm_para->parental_onoff = 1;
            sxm_tips_dialog_create(sxm_para, STRING_DIALOG_PARENTAL_CODE, STRING_DIALOG_PARENTAL_CODE_1, 200, ADLG_NOTHING);
        }
        ui_sxm_func_code_screen_uninit(sxm_para);
        ui_sxm_list_folder_reinit(sxm_para, ui_sxm_func_menu, SXM_PARENTAL_CTRL_MAX);
        ui_sxm_item_group_update(sxm_para, 1);
    }
#else
	if(event_code == LV_EVENT_VALUE_CHANGED)
	{		
        if(sxm_para->cursor_index == 0)
        {
            sxm_para->cursor_index = 1;
            //eLIBs_strtol(txt, NULL, 10);
            code = eLIBs_atoi(txt);
            if(code != sxm_para->parental_code[0])
            {
                sxm_para->cmp_error_flag = 1;
            }
            __sxm_msg("ui_sxm_func_code_cursor_1 txt=%s, code=%d\n", txt, code);
            lv_label_set_text(ui_sxm_func_code_cursor_1, txt);
        }
        else if(sxm_para->cursor_index == 1)
        {
            sxm_para->cursor_index = 2;
            code = eLIBs_atoi(txt);
            if(code != sxm_para->parental_code[1])
            {
                sxm_para->cmp_error_flag = 1;
            }
            __sxm_msg("ui_sxm_func_code_cursor_2 txt=%s, code=%d\n", txt, code);
            lv_label_set_text(ui_sxm_func_code_cursor_2, txt);
        }
        else if(sxm_para->cursor_index == 2)
        {
            sxm_para->cursor_index = 3;
            code = eLIBs_atoi(txt);
            if(code != sxm_para->parental_code[2])
            {
                sxm_para->cmp_error_flag = 1;
            }
            __sxm_msg("ui_sxm_func_code_cursor_3 txt=%s, code=%d\n", txt, code);
            lv_label_set_text(ui_sxm_func_code_cursor_3, txt);        
        }
        else if(sxm_para->cursor_index == 3)
        {
            sxm_para->cursor_index = 4;
            code = eLIBs_atoi(txt);
            if(code != sxm_para->parental_code[3])
            {
                sxm_para->cmp_error_flag = 1;
            }
            __sxm_msg("ui_sxm_func_code_cursor_4 txt=%s, code=%d\n", txt, code);
            lv_label_set_text(ui_sxm_func_code_cursor_4, txt);
        }
	}
    else if(event_code == LV_EVENT_DRAW_POST_END)
    {
        if(sxm_para->cursor_index > 3)
        {
            event_draw_end_flag = 1;
            __sxm_msg("event_release_flag=%d\n", event_release_flag);
            if(event_release_flag == 1)
            {
                event_release_flag = 0;
                event_draw_end_flag = 0;
                sxm_kb_timer_create(sxm_para, ui_sxm_parental_ctrl_code_handler_cb, 100);
            }
        }
    }
    else if(event_code == LV_EVENT_RELEASED)
    {
        if(sxm_para->cursor_index > 3)
        {
            event_release_flag = 1;
            __sxm_msg("event_draw_end_flag=%d\n", event_draw_end_flag);

            if(event_draw_end_flag == 1)
            {
                event_release_flag = 0;
                event_draw_end_flag = 0;
                sxm_kb_timer_create(sxm_para, ui_sxm_parental_ctrl_code_handler_cb, 100);
            }
        }
    }
#endif
}

static void ui_sxm_parental_ctrl_code_win_open(ui_sxm_para_t * sxm_para)
{
    ui_sxm_parental_ctrl_code_screen_init(sxm_para);
    ui_sxm_func_code_screen_init(sxm_para);
    ui_sxm_switch_code_type(sxm_para, SXM_WIN_PARENTAL_CTRL_CODE);

    ui_source_sub_open(sxm_para->source_para);
}

static void ui_sxm_parental_ctrl_code_win_close(ui_sxm_para_t * sxm_para)
{
    ui_sxm_parental_ctrl_code_screen_uninit(sxm_para);
    ui_sxm_func_code_screen_uninit(sxm_para);
    sxm_kb_timer_cancel(sxm_para);

    ui_source_sub_close(sxm_para->source_para);
}

static void ui_sxm_parental_ctrl_lock_channel_handler_cb(lv_timer_t * t)
{
	ui_sxm_para_t * sxm_para = lv_timer_get_user_data(t);
    
    __sxm_msg("ui_sxm_parental_ctrl_lock_channel_handler_cb\n");
    sxm_kb_timer_cancel(sxm_para);

    if(sxm_para->cmp_error_flag == 1)
    {
        sxm_para->cmp_error_flag = 0;
    
        sxm_tips_dialog_create(sxm_para, STRING_DIALOG_PARENTAL_CODE_ERROR, 0, 200, ADLG_NOTHING);
    }
    
    //ui_sxm_switch_win(sxm_para, SXM_WIN_PARENTAL_CTRL);
    ui_sxm_func_code_screen_uninit(sxm_para);
}

static void ui_sxm_parental_ctrl_lock_channel_handler(ui_sxm_para_t * sxm_para, lv_event_t* e)
{
    __u32 code, i;
    char * text;
	char parental_code[8+1] = {0};
    static __u8 event_release_flag = 0;
    static __u8 event_draw_end_flag = 0;
    lv_obj_t* kb = lv_event_get_target(e);
    lv_obj_t * textarea = lv_keyboard_get_textarea(kb);
    uint16_t btn_id = lv_btnmatrix_get_selected_btn(kb); // 修正获取按钮ID的方式
    const char* txt = lv_btnmatrix_get_btn_text(kb, btn_id);
    lv_event_code_t event_code = lv_event_get_code(e);
	reg_sxm_para_t* para=NULL;

	para = (reg_sxm_para_t*)dsk_reg_get_para_by_app(REG_APP_SXM);

#if 1
    if(event_code == LV_EVENT_READY)
    {       
        text = lv_textarea_get_text(textarea);

        if(text == NULL || text[0] == 0)
        {
            sxm_para->parental_onoff = 0;
            sxm_tips_dialog_create(sxm_para, STRING_DIALOG_PARENTAL_CODE_ERROR, 0, 200, ADLG_NOTHING);
            return;
        }
                
        for (i = 0; text[i] != '\0' && i < sizeof(parental_code); i++) 
        {
            parental_code[i] = text[i] - '0';
        }

        if(i != 4)
        {
            return;
        }

        if(eLIBs_memcmp(parental_code, sxm_para->parental_code, sizeof(parental_code)) == EPDK_NO)
        {
            sxm_tips_dialog_create(sxm_para, STRING_DIALOG_PARENTAL_CODE_ERROR, 0, 200, ADLG_NOTHING);
        }
        else
        {
            ui_sxm_switch_win(sxm_para, SXM_WIN_LOCK_CHAN);
        }

        ui_sxm_func_code_screen_uninit(sxm_para);
    }
#else
	if(event_code == LV_EVENT_VALUE_CHANGED)
	{		
        if(sxm_para->cursor_index == 0)
        {
            sxm_para->cursor_index = 1;
            //eLIBs_strtol(txt, NULL, 10);
            code = eLIBs_atoi(txt);
            if(code != sxm_para->parental_code[0])
            {
                sxm_para->cmp_error_flag = 1;
            }
            __sxm_msg("ui_sxm_func_code_cursor_1 txt=%s, code=%d\n", txt, code);
            lv_label_set_text(ui_sxm_func_code_cursor_1, txt);
        }
        else if(sxm_para->cursor_index == 1)
        {
            sxm_para->cursor_index = 2;
            code = eLIBs_atoi(txt);
            if(code != sxm_para->parental_code[1])
            {
                sxm_para->cmp_error_flag = 1;
            }
            __sxm_msg("ui_sxm_func_code_cursor_2 txt=%s, code=%d\n", txt, code);
            lv_label_set_text(ui_sxm_func_code_cursor_2, txt);
        }
        else if(sxm_para->cursor_index == 2)
        {
            sxm_para->cursor_index = 3;
            code = eLIBs_atoi(txt);
            if(code != sxm_para->parental_code[2])
            {
                sxm_para->cmp_error_flag = 1;
            }
            __sxm_msg("ui_sxm_func_code_cursor_3 txt=%s, code=%d\n", txt, code);
            lv_label_set_text(ui_sxm_func_code_cursor_3, txt);        
        }
        else if(sxm_para->cursor_index == 3)
        {
            sxm_para->cursor_index = 4;
            code = eLIBs_atoi(txt);
            if(code != sxm_para->parental_code[3])
            {
                sxm_para->cmp_error_flag = 1;
            }
            __sxm_msg("ui_sxm_func_code_cursor_4 txt=%s, code=%d\n", txt, code);
            lv_label_set_text(ui_sxm_func_code_cursor_4, txt);

        }
	}
    else if(event_code == LV_EVENT_DRAW_POST_END)
    {
        if(sxm_para->cursor_index > 3)
        {
            event_draw_end_flag = 1;
            __sxm_msg("event_release_flag=%d\n", event_release_flag);
            if(event_release_flag == 1)
            {
                event_release_flag = 0;
                event_draw_end_flag = 0;
                sxm_kb_timer_create(sxm_para, ui_sxm_parental_ctrl_lock_channel_handler_cb, 100);
            }
        }
    }
    else if(event_code == LV_EVENT_RELEASED)
    {
        if(sxm_para->cursor_index > 3)
        {
            event_release_flag = 1;
            __sxm_msg("event_draw_end_flag=%d\n", event_draw_end_flag);

            if(event_draw_end_flag == 1)
            {
                event_release_flag = 0;
                event_draw_end_flag = 0;
                sxm_kb_timer_create(sxm_para, ui_sxm_parental_ctrl_lock_channel_handler_cb, 100);
            }
        }
    }
#endif
}

static void ui_sxm_parental_ctrl_lock_channel_win_open(ui_sxm_para_t * sxm_para)
{
    
    ui_sxm_parental_ctrl_lock_channel_screen_init(sxm_para);
    ui_sxm_func_code_screen_init(sxm_para);
    ui_sxm_switch_code_type(sxm_para, SXM_WIN_PARENTAL_CTRL_LOCK_CHAN);

    ui_source_sub_open(sxm_para->source_para);
}

static void ui_sxm_parental_ctrl_lock_channel_win_close(ui_sxm_para_t * sxm_para)
{
    ui_sxm_parental_ctrl_lock_channel_screen_uninit(sxm_para);
    ui_sxm_func_code_screen_uninit(sxm_para);
    sxm_kb_timer_cancel(sxm_para);

    ui_source_sub_close(sxm_para->source_para);
}

static void ui_sxm_lock_channel_win_open(ui_sxm_para_t * sxm_para)
{
    sxm_para->win_type = SXM_WIN_LOCK_CHAN;

    ui_source_title(sxm_para->source_para, "Rock", 0);
    ui_source_sub_open(sxm_para->source_para);

    ui_sxm_func_screen_init(sxm_para);
    ui_sxm_listbar_para_init(sxm_para, ui_sxm_func_menu, 10);
    ui_sxm_listbar_init(sxm_para);
}

static void ui_sxm_lock_channel_win_close(ui_sxm_para_t * sxm_para)
{
    ui_sxm_listbar_uninit(sxm_para);
    ui_sxm_func_screen_uninit(sxm_para);
    
    ui_source_sub_close(sxm_para->source_para);
}

#if 0
static void ui_sxm_parental_ctrl_change_code_handler_cb(lv_timer_t * t)
{
	ui_sxm_para_t * sxm_para = lv_timer_get_user_data(t);
	reg_sxm_para_t* para=NULL;

	para = (reg_sxm_para_t*)dsk_reg_get_para_by_app(REG_APP_SXM);

    __sxm_msg("ui_sxm_parental_ctrl_change_code_handler_cb\n");
    sxm_kb_timer_cancel(sxm_para);

    __sxm_msg("sxm_para->change_index=%d\n", sxm_para->change_index);
    __sxm_msg("sxm_para->cmp_error_flag=%d\n", sxm_para->cmp_error_flag);
    
    if(sxm_para->change_index == 0)
    {
        if(sxm_para->cmp_error_flag == 1)
        {
            sxm_para->cmp_error_flag = 0;

            sxm_tips_dialog_create(sxm_para, STRING_DIALOG_PARENTAL_CODE_ERROR, 0, 200, ADLG_NOTHING);

            //ui_sxm_switch_win(sxm_para, SXM_WIN_PARENTAL_CTRL);
            ui_sxm_func_code_screen_uninit(sxm_para);
        }
        else
        {
            sxm_para->change_index++;

            lv_label_set_text(ui_sxm_func_code_cursor_1, "-");
            lv_label_set_text(ui_sxm_func_code_cursor_2, "-");
            lv_label_set_text(ui_sxm_func_code_cursor_3, "-");
            lv_label_set_text(ui_sxm_func_code_cursor_4, "-");
            lv_label_set_text(ui_sxm_func_code_text, "Enter new code.");
        }
    }
    else if(sxm_para->change_index == 1)
    {
        sxm_para->change_index++;
        lv_label_set_text(ui_sxm_func_code_cursor_1, "-");
        lv_label_set_text(ui_sxm_func_code_cursor_2, "-");
        lv_label_set_text(ui_sxm_func_code_cursor_3, "-");
        lv_label_set_text(ui_sxm_func_code_cursor_4, "-");
        lv_label_set_text(ui_sxm_func_code_text, "Enter new code again.");
    }
    else if(sxm_para->change_index == 2)
    {
        if(sxm_para->cmp_error_flag == 1)
        {
            sxm_para->cmp_error_flag = 0;
            sxm_tips_dialog_create(sxm_para, STRING_DIALOG_PARENTAL_NOT_MATCH, 0, 200, ADLG_NOTHING);

            //ui_sxm_switch_win(sxm_para, SXM_WIN_PARENTAL_CTRL);
            ui_sxm_func_code_screen_uninit(sxm_para);
        }
        else
        {
            sxm_para->change_index++;
            eLIBs_memcpy(sxm_para->parental_code, sxm_para->parental_code_save, sizeof(sxm_para->parental_code_save));
            sxm_tips_dialog_create(sxm_para, STRING_DIALOG_PARENTAL_CODE, STRING_DIALOG_PARENTAL_CODE_1, 200, ADLG_NOTHING);

            //ui_sxm_switch_win(sxm_para, SXM_WIN_PARENTAL_CTRL);
            ui_sxm_func_code_screen_uninit(sxm_para);
        }
    }
}
#endif

static void ui_sxm_parental_ctrl_change_code_handler(ui_sxm_para_t * sxm_para, lv_event_t* e)
{
    __u32 code, i;
    char * text;
	char parental_code[8+1] = {0};
    static __u8 event_release_flag = 0;
    static __u8 event_draw_end_flag = 0;
    lv_obj_t* kb = lv_event_get_target(e);
    lv_obj_t * textarea = lv_keyboard_get_textarea(kb);
    uint16_t btn_id = lv_btnmatrix_get_selected_btn(kb); // 修正获取按钮ID的方式
    const char* txt = lv_btnmatrix_get_btn_text(kb, btn_id);
    lv_event_code_t event_code = lv_event_get_code(e);
	reg_sxm_para_t* para;

	para = (reg_sxm_para_t*)dsk_reg_get_para_by_app(REG_APP_SXM);

#if 1
    if(event_code == LV_EVENT_READY)
    {       
        text = lv_textarea_get_text(textarea);

        if(text == 0 || text[0] == 0)
        {
            sxm_tips_dialog_create(sxm_para, STRING_DIALOG_PARENTAL_CODE_ERROR, 0, 200, ADLG_NOTHING);
            
            //ui_sxm_switch_win(sxm_para, SXM_WIN_PARENTAL_CTRL);
            ui_sxm_func_code_screen_uninit(sxm_para);
            
            return;
        }
        
        for (i = 0; text[i] != '\0' && i < sizeof(parental_code); i++) 
        {
            parental_code[i] = text[i] - '0';
        }

        if(i != 4)
        {
            return;
        }

        if(sxm_para->change_index == 0)
        {
            if(eLIBs_memcmp(parental_code, sxm_para->parental_code, sizeof(parental_code)) == EPDK_NO)
            {
                sxm_tips_dialog_create(sxm_para, STRING_DIALOG_PARENTAL_CODE_ERROR, 0, 200, ADLG_NOTHING);
        
                //ui_sxm_switch_win(sxm_para, SXM_WIN_PARENTAL_CTRL);
                ui_sxm_func_code_screen_uninit(sxm_para);
            }
            else
            {
                sxm_para->change_index++;
                lv_textarea_set_text(ui_sxm_func_code_textarea, "");
                lv_textarea_set_placeholder_text(ui_sxm_func_code_textarea, "Enter new code.");
            }
        }
        else if(sxm_para->change_index == 1)
        {
            sxm_para->change_index++;
            eLIBs_memcpy(sxm_para->parental_code_save, parental_code, sizeof(sxm_para->parental_code_save));
            lv_textarea_set_text(ui_sxm_func_code_textarea, "");
            lv_textarea_set_placeholder_text(ui_sxm_func_code_textarea, "Enter new code again.");
        }
        else if(sxm_para->change_index == 2)
        {
            if(eLIBs_memcmp(parental_code, sxm_para->parental_code_save, sizeof(parental_code)) == EPDK_NO)
            {
                sxm_tips_dialog_create(sxm_para, STRING_DIALOG_PARENTAL_NOT_MATCH, 0, 200, ADLG_NOTHING);
        
                //ui_sxm_switch_win(sxm_para, SXM_WIN_PARENTAL_CTRL);
                ui_sxm_func_code_screen_uninit(sxm_para);
            }
            else
            {
                sxm_para->change_index++;
                eLIBs_memcpy(sxm_para->parental_code, parental_code, sizeof(sxm_para->parental_code));
                eLIBs_memcpy(para->parental_code, parental_code, sizeof(para->parental_code));
                sxm_tips_dialog_create(sxm_para, STRING_DIALOG_PARENTAL_CODE, STRING_DIALOG_PARENTAL_CODE_1, 200, ADLG_NOTHING);
        
                //ui_sxm_switch_win(sxm_para, SXM_WIN_PARENTAL_CTRL);
                ui_sxm_func_code_screen_uninit(sxm_para);
            }
        }
    }
#else
	if(event_code == LV_EVENT_VALUE_CHANGED)
	{		
        if(sxm_para->cursor_index == 0)
        {
            sxm_para->cursor_index = 1;
            //eLIBs_strtol(txt, NULL, 10);
            code = eLIBs_atoi(txt);
            if(sxm_para->change_index == 0 && code != sxm_para->parental_code[0])
            {
                sxm_para->cmp_error_flag = 1;
            }
            else if(sxm_para->change_index == 1)
            {
                sxm_para->parental_code_save[0] = code;
            }
            else if(sxm_para->change_index == 2 && code != sxm_para->parental_code_save[0])
            {
                sxm_para->cmp_error_flag = 1;
            }
            
            __sxm_msg("ui_sxm_func_code_cursor_1 txt=%s, code=%d\n", txt, code);
            lv_label_set_text(ui_sxm_func_code_cursor_1, txt);
        }
        else if(sxm_para->cursor_index == 1)
        {
            sxm_para->cursor_index = 2;
            code = eLIBs_atoi(txt);
            if(sxm_para->change_index == 0 && code != sxm_para->parental_code[1])
            {
                sxm_para->cmp_error_flag = 1;
            }
            else if(sxm_para->change_index == 1)
            {
                sxm_para->parental_code_save[1] = code;
            }
            else if(sxm_para->change_index == 2 && code != sxm_para->parental_code_save[1])
            {
                sxm_para->cmp_error_flag = 1;
            }
            
            __sxm_msg("ui_sxm_func_code_cursor_2 txt=%s, code=%d\n", txt, code);
            lv_label_set_text(ui_sxm_func_code_cursor_2, txt);
        }
        else if(sxm_para->cursor_index == 2)
        {
            sxm_para->cursor_index = 3;
            code = eLIBs_atoi(txt);
            if(sxm_para->change_index == 0 && code != sxm_para->parental_code[2])
            {
                sxm_para->cmp_error_flag = 1;
            }
            else if(sxm_para->change_index == 1)
            {
                sxm_para->parental_code_save[2] = code;
            }
            else if(sxm_para->change_index == 2 && code != sxm_para->parental_code_save[2])
            {
                sxm_para->cmp_error_flag = 1;
            }

            __sxm_msg("ui_sxm_func_code_cursor_3 txt=%s, code=%d\n", txt, code);
            lv_label_set_text(ui_sxm_func_code_cursor_3, txt);        
        }
        else if(sxm_para->cursor_index == 3)
        {
            sxm_para->cursor_index = 4;
            code = eLIBs_atoi(txt);
            if(sxm_para->change_index == 0 && code != sxm_para->parental_code[3])
            {
                sxm_para->cmp_error_flag = 1;
            }
            else if(sxm_para->change_index == 1)
            {
                sxm_para->parental_code_save[3] = code;
            }
            else if(sxm_para->change_index == 2 && code != sxm_para->parental_code_save[3])
            {
                sxm_para->cmp_error_flag = 1;
            }

            __sxm_msg("ui_sxm_func_code_cursor_4 txt=%s, code=%d\n", txt, code);
            lv_label_set_text(ui_sxm_func_code_cursor_4, txt);
        }
	}
    else if(event_code == LV_EVENT_DRAW_POST_END)
    {
        if(sxm_para->cursor_index > 3)
        {
            event_draw_end_flag = 1;
            __sxm_msg("event_release_flag=%d\n", event_release_flag);
            if(event_release_flag == 1)
            {
                event_release_flag = 0;
                event_draw_end_flag = 0;
                sxm_para->cursor_index = 0;
                sxm_kb_timer_create(sxm_para, ui_sxm_parental_ctrl_change_code_handler_cb, 100);
            }
        }
    }
    else if(event_code == LV_EVENT_RELEASED)
    {
        if(sxm_para->cursor_index > 3)
        {
            event_release_flag = 1;
            __sxm_msg("event_draw_end_flag=%d\n", event_draw_end_flag);

            if(event_draw_end_flag == 1)
            {
                event_release_flag = 0;
                event_draw_end_flag = 0;
                sxm_para->cursor_index = 0;
                sxm_kb_timer_create(sxm_para, ui_sxm_parental_ctrl_change_code_handler_cb, 100);
            }
        }
    }
#endif
}

static void ui_sxm_parental_ctrl_change_code_win_open(ui_sxm_para_t * sxm_para)
{
    ui_sxm_parental_ctrl_change_code_screen_init(sxm_para);
    ui_sxm_func_code_screen_init(sxm_para);
    ui_sxm_switch_code_type(sxm_para, SXM_WIN_PARENTAL_CTRL_CHANGE_CODE);

    ui_source_sub_open(sxm_para->source_para);
}

static void ui_sxm_parental_ctrl_change_code_win_close(ui_sxm_para_t * sxm_para)
{
    ui_sxm_parental_ctrl_change_code_screen_uninit(sxm_para);
    ui_sxm_func_code_screen_uninit(sxm_para);
    sxm_kb_timer_cancel(sxm_para);

    ui_source_sub_close(sxm_para->source_para);
}

static void ui_sxm_switch_win(ui_sxm_para_t * sxm_para, __u32 win_type)
{		
    __u32 win_type_old = sxm_para->win_type;
    
	if(sxm_para->win_type == win_type)
		return;

    __sxm_msg("sxm_para->win_type=%d, win_type=%d\n", sxm_para->win_type, win_type);
	//close other win
	switch(sxm_para->win_type)
	{
        case SXM_WIN_NONE:	
		{
            ui_sxm_normal_win_close(sxm_para);
		}		
		break;
        
        case SXM_WIN_LIST:	
		{
            ui_sxm_list_win_close(sxm_para);
		}		
		break;
        
        case SXM_WIN_FUNC:	
		{
            ui_sxm_func_win_close(sxm_para);
		}		
		break;
            
		case SXM_WIN_CATEGORY:	
		{
            ui_sxm_category_win_close(sxm_para);
		}		
		break;

		case SXM_WIN_DIRECT_TUNING:	
		{
            ui_sxm_direct_tuning_win_close(sxm_para);
		}		
		break;

		case SXM_WIN_PARENTAL_CTRL:	
		{
            ui_sxm_parental_ctrl_win_close(sxm_para);
		}		
		break;

        case SXM_WIN_PARENTAL_CTRL_CODE:	
		{
            ui_sxm_parental_ctrl_code_win_close(sxm_para);
		}		
		break;

        case SXM_WIN_PARENTAL_CTRL_LOCK_CHAN:	
		{
            ui_sxm_parental_ctrl_lock_channel_win_close(sxm_para);
		}		
		break;
        
        case SXM_WIN_LOCK_CHAN:	
		{
            ui_sxm_lock_channel_win_close(sxm_para);
		}		
		break;

        case SXM_WIN_PARENTAL_CTRL_CHANGE_CODE:	
		{
            ui_sxm_parental_ctrl_change_code_win_close(sxm_para);
		}		
		break;
	}

	//open win by type
	switch(win_type)
	{
        case SXM_WIN_NONE:	
		{
            ui_sxm_normal_win_open(sxm_para);
		}		
		break;
        
        case SXM_WIN_LIST:	
		{
            ui_sxm_list_win_open(sxm_para);
		}		
		break;
        
        case SXM_WIN_FUNC:	
		{
            ui_sxm_func_win_open(sxm_para);
		}		
		break;
        
		case SXM_WIN_CATEGORY:	
		{
            ui_sxm_category_win_open(sxm_para);
		}		
		break;

		case SXM_WIN_DIRECT_TUNING:	
		{
            ui_sxm_direct_tuning_win_open(sxm_para);
		}		
		break;

		case SXM_WIN_PARENTAL_CTRL:	
		{
            ui_sxm_parental_ctrl_win_open(sxm_para);
		}		
		break;

        case SXM_WIN_PARENTAL_CTRL_CODE:	
		{
            ui_sxm_parental_ctrl_code_win_open(sxm_para);
		}		
		break;

        case SXM_WIN_PARENTAL_CTRL_LOCK_CHAN:	
		{
            ui_sxm_parental_ctrl_lock_channel_win_open(sxm_para);
		}		
		break;

        case SXM_WIN_LOCK_CHAN:	
		{
            ui_sxm_lock_channel_win_open(sxm_para);
		}		
		break;

        case SXM_WIN_PARENTAL_CTRL_CHANGE_CODE:	
		{
            ui_sxm_parental_ctrl_change_code_win_open(sxm_para);
		}		
		break;
	}

#if 0
    if(win_type_old == SXM_WIN_NONE)
    {
        ui_sxm_item_group_update(sxm_para, 0);
    }
    else
    {
        ui_sxm_item_group_update(sxm_para, 1);
    }
#endif
}

static void ui_sxm_night_theme(ui_sxm_para_t * sxm_para, __u32 theme)
{
	__sxm_msg("ui_sxm_night_theme\n");

    if(sxm_para->theme == theme)
    {
        __sxm_msg("cur is theme %d, not need change!!!!\n", theme);
        return;
    }
    
    if(theme == THEME_NIGHT)
    {
        SXM_THMEM_TEMP_PM_BO_OFF_ICON_BMP           = SXM_TEMP_NIGHT_NIG_PM_BO_OFF_ICON_BMP;    
        SXM_THMEM_TEMP_PM_BO_OFF_ICON_A_BMP         = SXM_TEMP_NIGHT_NIG_PM_BO_OFF_ICON_A_BMP;  
        SXM_THMEM_TEMP_PM_RE_ICON_BMP               = SXM_TEMP_NIGHT_NIG_PM_RE_ICON_BMP;    
        SXM_THMEM_TEMP_PM_RE_ICON_A_BMP             = SXM_TEMP_NIGHT_NIG_PM_RE_ICON_A_BMP;    
        SXM_THMEM_TEMP_PM_RE_LIST_BMP               = SXM_TEMP_NIGHT_NIG_PM_RE_LIST_BMP;
        SXM_THMEM_TEMP_PM_RE_LIST_N_BMP             = SXM_TEMP_NIGHT_NIG_PM_RE_LIST_N_BMP;
        SXM_THMEM_TEMP_PM_RE_LIST_CONNECT_BMP       = SXM_TEMP_NIGHT_NIG_PM_RE_LIST_CONNECT_BMP;
        SXM_THMEM_TEMP_PM_RE_SEARCH_BMP             = SXM_TEMP_NIGHT_NIG_PM_RE_SEARCH_BMP;
        SXM_THMEM_TEMP_PM_RE_SEARCH_N_BMP           = SXM_TEMP_NIGHT_NIG_PM_RE_SEARCH_N_BMP;
        SXM_THMEM_TEMP_COM_LIST_ON_BMP              = SXM_TEMP_NIGHT_NIG_COM_LIST_ON_BMP;
        SXM_THMEM_TEMP_COM_LIST_OFF_BMP             = SXM_TEMP_NIGHT_NIG_COM_LIST_OFF_BMP;
        //SXM_THMEM_TEMP_COM_MEDIA_ALBUM_BG_BMP       = SXM_TEMP_NIGHT_NIG_COM_MEDIA_ALBUM_BG_BMP;
        SXM_THEME_COM_BOT_BG_BMP                       = SXM_COMMON_NIG_COM_BOT_BG_BMP;  

        SXM_THEME_COM_BOT_ICON_BG_A_L_N_BMP            = SXM_COMMON_NIG_COM_BOT_ICON_BG_A_L_N_BMP;
        SXM_THEME_COM_BOT_ICON_BG_A_L_S_BMP            = SXM_COMMON_NIG_COM_BOT_ICON_BG_A_L_S_BMP;
        
        SXM_THEME_COM_BOT_LIST_N_BMP                   = SXM_COMMON_NIG_COM_BOT_LIST_N_BMP;
        SXM_THEME_COM_BOT_LIST_D_BMP                   = SXM_COMMON_NIG_COM_BOT_LIST_D_BMP;
                
        SXM_THEME_COM_BOT_DIRECT_N_BMP                 = SXM_COMMON_NIG_COM_BOT_DIRECT_N_BMP;
        SXM_THEME_COM_BOT_DIRECT_D_BMP                 = SXM_COMMON_NIG_COM_BOT_DIRECT_D_BMP;

        SXM_THMEM_SXM_ICON_BMP                      = SXM_NIG_SXM_ICON_BMP;
        SXM_THMEM_SXM_SIGNAL_A_BMP                  = SXM_NIG_SXM_SIGNAL_A_BMP;
        SXM_THMEM_SXM_SIGNAL_B_BMP                  = SXM_NIG_SXM_SIGNAL_B_BMP;
        SXM_THMEM_SXM_SIGNAL_C_BMP                  = SXM_NIG_SXM_SIGNAL_C_BMP;
        SXM_THMEM_SXM_SIGNAL_D_BMP                  = SXM_NIG_SXM_SIGNAL_D_BMP;
        SXM_THMEM_SXM_LOCK_BMP                      = SXM_NIG_SXM_LOCK_BMP;
        SXM_THMEM_SXM_TOP_BAR_ICON_BMP              = SXM_NIG_SXM_TOP_BAR_ICON_BMP;
        SXM_THMEM_TEMP_COM_LOW_VOLTAGE_BMP          = SXM_TEMP_NIGHT_NIG_COM_LOW_VOLTAGE_BMP;
        SXM_THMEM_TEMP_COM_PD_CHARGE_BMP            = SXM_TEMP_NIGHT_NIG_COM_PD_CHARGE_BMP;
        SXM_THMEM_TEMP_COM_PUNCH_EQ_BMP             = SXM_TEMP_NIGHT_NIG_COM_PUNCH_EQ_BMP;
        SXM_THMEM_TEMP_COM_RIG_ICON_BG_N_BMP        = SXM_TEMP_NIGHT_NIG_COM_RIG_ICON_BG_N_BMP;
        SXM_THMEM_TEMP_COM_RIG_ICON_BG_P_BMP        = SXM_TEMP_NIGHT_NIG_COM_RIG_ICON_BG_P_BMP;
        SXM_THMEM_TEMP_COM_RIG_ICON_BG_S_BMP        = SXM_TEMP_NIGHT_NIG_COM_RIG_ICON_BG_S_BMP;
    }
    else  if(theme == THEME_DAY)
    {
        SXM_THMEM_TEMP_PM_BO_OFF_ICON_BMP           = SXM_TEMP_DAY_DAY_PM_BO_OFF_ICON_BMP;    
        SXM_THMEM_TEMP_PM_BO_OFF_ICON_A_BMP         = SXM_TEMP_DAY_DAY_PM_BO_OFF_ICON_A_BMP;  
        SXM_THMEM_TEMP_PM_RE_ICON_BMP               = SXM_TEMP_DAY_DAY_PM_RE_ICON_BMP;    
        SXM_THMEM_TEMP_PM_RE_ICON_A_BMP             = SXM_TEMP_DAY_DAY_PM_RE_ICON_A_BMP;    
        SXM_THMEM_TEMP_PM_RE_LIST_BMP               = SXM_TEMP_DAY_DAY_PM_RE_LIST_BMP;
        SXM_THMEM_TEMP_PM_RE_LIST_N_BMP             = SXM_TEMP_DAY_DAY_PM_RE_LIST_N_BMP;
        SXM_THMEM_TEMP_PM_RE_LIST_CONNECT_BMP       = SXM_TEMP_DAY_DAY_PM_RE_LIST_CONNECT_BMP;
        SXM_THMEM_TEMP_PM_RE_SEARCH_BMP             = SXM_TEMP_DAY_DAY_PM_RE_SEARCH_BMP;
        SXM_THMEM_TEMP_PM_RE_SEARCH_N_BMP           = SXM_TEMP_DAY_DAY_PM_RE_SEARCH_N_BMP;
        SXM_THMEM_TEMP_COM_LIST_ON_BMP              = SXM_TEMP_DAY_DAY_COM_LIST_ON_BMP;
        SXM_THMEM_TEMP_COM_LIST_OFF_BMP             = SXM_TEMP_DAY_DAY_COM_LIST_OFF_BMP;
        //SXM_THMEM_TEMP_COM_MEDIA_ALBUM_BG_BMP       = SXM_TEMP_DAY_DAY_COM_MEDIA_ALBUM_BG_BMP;

        SXM_THEME_COM_BOT_BG_BMP                       = SXM_COMMON_DAY_COM_BOT_BG_BMP;  

        SXM_THEME_COM_BOT_ICON_BG_A_L_N_BMP            = SXM_COMMON_DAY_COM_BOT_ICON_BG_A_L_N_BMP;
        SXM_THEME_COM_BOT_ICON_BG_A_L_S_BMP            = SXM_COMMON_DAY_COM_BOT_ICON_BG_A_L_S_BMP;
        
        SXM_THEME_COM_BOT_LIST_N_BMP                   = SXM_COMMON_DAY_COM_BOT_LIST_N_BMP;
        SXM_THEME_COM_BOT_LIST_D_BMP                   = SXM_COMMON_DAY_COM_BOT_LIST_D_BMP;
                
        SXM_THEME_COM_BOT_DIRECT_N_BMP                 = SXM_COMMON_DAY_COM_BOT_DIRECT_N_BMP;
        SXM_THEME_COM_BOT_DIRECT_D_BMP                 = SXM_COMMON_DAY_COM_BOT_DIRECT_D_BMP;

        SXM_THMEM_SXM_ICON_BMP                      = SXM_DAY_SXM_ICON_BMP;
        SXM_THMEM_SXM_SIGNAL_A_BMP                  = SXM_DAY_SXM_SIGNAL_A_BMP;
        SXM_THMEM_SXM_SIGNAL_B_BMP                  = SXM_DAY_SXM_SIGNAL_B_BMP;
        SXM_THMEM_SXM_SIGNAL_C_BMP                  = SXM_DAY_SXM_SIGNAL_C_BMP;
        SXM_THMEM_SXM_SIGNAL_D_BMP                  = SXM_DAY_SXM_SIGNAL_D_BMP;
        SXM_THMEM_SXM_LOCK_BMP                      = SXM_DAY_SXM_LOCK_BMP;
        SXM_THMEM_SXM_TOP_BAR_ICON_BMP              = SXM_DAY_SXM_TOP_BAR_ICON_BMP;
        SXM_THMEM_TEMP_COM_LOW_VOLTAGE_BMP          = SXM_TEMP_DAY_DAY_COM_LOW_VOLTAGE_BMP;
        SXM_THMEM_TEMP_COM_PD_CHARGE_BMP            = SXM_TEMP_DAY_DAY_COM_PD_CHARGE_BMP;
        SXM_THMEM_TEMP_COM_PUNCH_EQ_BMP             = SXM_TEMP_DAY_DAY_COM_PUNCH_EQ_BMP;
        SXM_THMEM_TEMP_COM_RIG_ICON_BG_N_BMP        = SXM_TEMP_DAY_DAY_COM_RIG_ICON_BG_N_BMP;
        SXM_THMEM_TEMP_COM_RIG_ICON_BG_P_BMP        = SXM_TEMP_DAY_DAY_COM_RIG_ICON_BG_P_BMP;
        SXM_THMEM_TEMP_COM_RIG_ICON_BG_S_BMP        = SXM_TEMP_DAY_DAY_COM_RIG_ICON_BG_S_BMP;
    }
    
    if(sxm_para->theme > THEME_NONE)
    {
        traverse_children_with_callback(sxm_para, ui_sxm, sxm_para->theme_bmp_start, sxm_para->theme_bmp_end, SXM_THMEM_TEMP_PM_BO_OFF_ICON_BMP, SXM_THMEM_TEMP_COM_RIG_ICON_BG_S_BMP, sxm_para->ui_sxm_res_para.lv_sxm_icon, ui_sxm_get_res, NULL, 0);
        ui_sxm_night_theme(sxm_para->source_obj, theme);
    }

    sxm_para->theme = theme;
    sxm_para->theme_bmp_start = SXM_THMEM_TEMP_PM_BO_OFF_ICON_BMP;
    sxm_para->theme_bmp_end = SXM_THMEM_TEMP_PM_RE_SEARCH_N_BMP;
}

__s32 __sxm_time_proc(ui_sxm_para_t * sxm_para) 
{
	if(!sxm_para)
	{
		return EPDK_FAIL;
	}

    ui_sxm_update_timer_proc(sxm_para);
}

void __ui_sxm_remove_hidden(ui_sxm_para_t * sxm_para)
{
	__sxm_msg("ui_ipod_remove_hidden\n");
	if(!ui_sxm_timer)
	{
		ui_sxm_timer = lv_timer_create(ui_event_sxm_timer_cb, FlashTimeWinTime*10, sxm_para);
	}
	_ui_flag_modify(ui_sxm, LV_OBJ_FLAG_HIDDEN, _UI_MODIFY_FLAG_REMOVE);
#ifdef LV_ADD_ANIM_SCREEN
	Opacity_Increase_Animation(ui_sxm, 0, NULL);
#endif
}

void __ui_sxm_add_hidden(ui_sxm_para_t * sxm_para)
{
	__sxm_msg("ui_ipod_add_hidden\n");
	_ui_flag_modify(ui_sxm, LV_OBJ_FLAG_HIDDEN, _UI_MODIFY_FLAG_ADD);
	if(ui_sxm_timer)
	{
		lv_timer_del(ui_sxm_timer);
		ui_sxm_timer = NULL;
	}
#ifdef LV_ADD_ANIM_SCREEN
	lv_obj_set_style_opa(ui_sxm, 0, 0);
#endif
}

void __ui_sxm_layer_on(ui_sxm_para_t * sxm_para)
{
	if(sxm_para == NULL)
		return EPDK_FAIL;

	if(sxm_para->mode_store == 0)
	{
		return EPDK_FAIL;
	}

	sxm_para->mode_store = 0;
	TopBar_view_set_status(1);
	gscene_bgd_set_img_src(BG_INDEX_AUTO);
	
	ui_sxm_remove_hidden(sxm_para);
}

void __ui_sxm_layer_sleep(ui_sxm_para_t * sxm_para)
{
	if(sxm_para == NULL)
		return EPDK_FAIL;

	if(sxm_para->mode_store == 1)
	{
		return EPDK_FAIL;
	}

	sxm_para->mode_store = 1;
	ui_sxm_add_hidden(sxm_para);
}

static __s32 ui_sxm_internal_para_update(ui_sxm_para_t * sxm_para)
{
    reg_sxm_para_t* reg_sxm_para=NULL;
    
    reg_sxm_para = (reg_sxm_para_t*)dsk_reg_get_para_by_app(REG_APP_SXM);
    
    sxm_para->theme_bmp_start = SXM_TEMP_NIGHT_NIG_PM_BO_OFF_ICON_BMP;
    sxm_para->theme_bmp_end = SXM_TEMP_NIGHT_NIG_PM_RE_SEARCH_N_BMP;
    sxm_para->source_obj = ui_source_content;//(lv_obj_t *)ui_source_create(ui_source);
    sxm_para->source_para = (ui_sxm_para_t *)lv_obj_get_user_data(sxm_para->source_obj);
    eLIBs_memcpy(sxm_para->parental_code, reg_sxm_para->parental_code, sizeof(sxm_para->parental_code));
}

__s32 ui_sxm_refresh_handler(void * para, __gui_msg_t * pmsg)
{
	__s32 ret = EPDK_OK;
	__gui_msg_t msg;
	ui_sxm_para_t * sxm_para = ui_sxm_para;//(ui_sxm_para_t *)para;
	root_app_para_t * root_para = app_root_get_para();//pmsg->p_arg;

	if(!sxm_para)
		return EPDK_FAIL;
		
    memset(&msg, 0x00, sizeof(msg));

	__sxm_msg("LOWORD(pmsg->id)=0x%x\n", LOWORD(pmsg->id));
    if(LOWORD(pmsg->id) == GUI_MSG_PAINT)
    {
		__sxm_msg("HIWORD(pmsg->id)=0x%x, GUI_MSG_SXM_REFRESH_INIT=0x%x\n", HIWORD(pmsg->id), GUI_MSG_SXM_REFRESH_INIT);
		switch(HIWORD(pmsg->id))
		{
			case GUI_MSG_SXM_REFRESH_INIT:
			{
                ui_sxm_night_theme(ui_sxm_para, THEME_NIGHT);

                ui_source_switch_clean(ui_sxm_para->source_para);
                ui_source_nosupport_browser(ui_sxm_para->source_para, 1);
                //ui_source_nosupport_func(ui_sxm_para->source_para, 1);
                //lv_obj_remove_flag(ui_sxm_para->source_para->ui_top_right, LV_OBJ_FLAG_CLICKABLE);      /// Flags
                ui_source_title(ui_sxm_para->source_para, "SiriusXM", 1);
                ui_source_rig_bar_set_flag(ui_sxm_para->source_para, LV_OBJ_FLAG_HIDDEN, 0);

				TopBar_view_set_status(1);
				Draw_topbar_view_with_app_id(APP_SXM_ID, 0);
				gscene_bgd_set_img_src(BG_INDEX_AUTO);
				ui_sxm_screen_init(ui_sxm_para);
				ui_sxm_para->sxm_main_obj = ui_sxm;
				root_para->app_interface_sxm->info.src_win = ui_sxm;
			}
			break;

			case GUI_MSG_SXM_REFRESH_UNINIT:
			{
				gscene_bgd_set_hid_flag(BGD_STATUS_SHOW);
				if(ui_sxm_timer)
				{
					lv_timer_del(ui_sxm_timer);
					ui_sxm_timer = NULL;
				}
				ui_sxm_screen_uninit(ui_sxm_para);
                //ui_source_destroy(ui_sxm_para->source_obj);
				ui_sxm_uninit_res(ui_sxm_para);
				esMEMS_Bfree(ui_sxm_para, sizeof(ui_sxm_para_t));
				ui_sxm_para = NULL;
				ui_sxm = NULL;
                ui_sxm_func_menu = NULL;
                ui_sxm_func_code = NULL;
                ui_sxm_direct_tuning = NULL;
                ui_sxm_parental_ctrl_code = NULL;
                ui_sxm_parental_ctrl_lock_channel = NULL;
                ui_sxm_parental_ctrl_change_code = NULL;
				eLIBs_printf("ui_sxm_uninit\n");
			}
			break;
			
			case GUI_MSG_SXM_REFRESH_HIDDEN:
			{
				__u32 flag = (__u32)pmsg->dwAddData1;

				if(flag == 0)
				{
					__ui_sxm_remove_hidden(sxm_para);
				}
				else
				{
					__ui_sxm_add_hidden(sxm_para);
				}
			}
			break;
			
			case GUI_MSG_SXM_REFRESH_PART:
			{
				__sxm_msg("pmsg->p_arg = 0x%x\n", pmsg->p_arg);
                
				if(pmsg->p_arg == (void *)__sxm_tips_dialog_create)
				{
                    __s32 title_id = (__s32)pmsg->dwAddData1;
                    __s32 content_id = (__s32)pmsg->dwAddData2;
                    __u32 time_out = LOWORD(pmsg->dwReserved);
                    __s32 id = HIWORD(pmsg->dwReserved);
					
					__sxm_tips_dialog_create(sxm_para, title_id, content_id, time_out, id);
				}
				else if(pmsg->p_arg == (void *)__sxm_tips_dialog_cancel)
				{					
					__sxm_tips_dialog_cancel(sxm_para);
				}
                else if(pmsg->p_arg == (void *)__sxm_kb_timer_create)
				{				
                    ui_timer_cb_t cb = (ui_timer_cb_t)pmsg->dwAddData1;
                    uint32_t period = (uint32_t)pmsg->dwAddData2;
                        
					__sxm_kb_timer_create(sxm_para, cb, period);
				}
                else if(pmsg->p_arg == (void *)__sxm_kb_timer_cancel)
				{					
					__sxm_kb_timer_cancel(sxm_para);
				}
                else if(pmsg->p_arg == (void *)__sxm_tips_dialog_confirm)
				{					
					__sxm_tips_dialog_confirm(sxm_para);
				}
                else if(pmsg->p_arg == (void *)__sxm_update_low_voltage)
				{
					__u8 update = (__u8)pmsg->dwAddData1;
					
					__sxm_update_low_voltage(sxm_para, update);
				}
				else if(pmsg->p_arg == (void *)__sxm_update_charge)
				{
					bool update = (bool)pmsg->dwAddData1;
					
					__sxm_update_charge(sxm_para, update);
				}
				else if(pmsg->p_arg == (void *)__sxm_update_eq)
				{
					bool update = (bool)pmsg->dwAddData1;
					
					__sxm_update_eq(sxm_para, update);
				}
			}
			break;
			
			case GUI_MSG_SXM_REFRESH_MSG:
			{
				__s32 id = (__s32)pmsg->dwAddData1;
				__s32 data1 = (__s32)pmsg->dwAddData2;
				__s32 data2 = (__s32)pmsg->dwReserved;

				__sxm_cmd2parent(NULL, id, data1, data2);
			}
			break;
			
			default:
			{
				ret = EPDK_FAIL;
			}
			break;
		}
	
}
	else
	{
		ret = EPDK_FAIL;
	}
	
	return ret;
}

static void ui_sxm_source_event_handler(ui_sxm_para_t * sxm_para)
{    
    ui_sxm_bottom_group_update(sxm_para, 0, 0, 0);
    ui_source_title(sxm_para->source_para, "Source", 0);
    ui_source_sub_open(sxm_para->source_para);
    sxm_cmd2parent(NULL,SWITCH_TO_MMENU,0,0);
}

static void ui_sxm_top_left_event_handler(ui_sxm_para_t * sxm_para)
{   
    __sxm_msg("sxm_para->dialog_win = %p\n", sxm_para->dialog_win);
    if(sxm_para->dialog_win)
    {
        sxm_tips_dialog_cancel(sxm_para);
        return;
    }
    
    __sxm_msg("sxm_para->code_kb_type = %d\n", sxm_para->code_kb_type);
    if(sxm_para->code_kb_type > CODE_KEYBOARD_NONE)
    {
        ui_sxm_func_code_screen_uninit(sxm_para);
        if(sxm_para->win_type == SXM_WIN_NONE)
        {
            ui_source_title(sxm_para->source_para, "SiriusXM", 0);
        }
        
        return;
    }
    
    __sxm_msg("sxm_para->win_type = %d\n", sxm_para->win_type);
	switch(sxm_para->win_type)
	{
        case SXM_WIN_NONE:
        {
            if(sxm_para->bottom_fouse_flag == 1)
            {
                ui_sxm_bottom_group_update(sxm_para, 0, 0, 0);
            }
            ui_home_restore_switch_source();
        }
        break;

        case SXM_WIN_LIST:	
		{
            //if(sxm_para->source_para->rig_open_flag == 0 || ((sxm_para->source_para->rig_open_flag == 1) && sxm_para->source_para->menu_win != SOURCE_MENU_WIN_FUNC))
                ui_sxm_switch_win(sxm_para, SXM_WIN_NONE);
            //else
            //    ui_sxm_switch_win(sxm_para, SXM_WIN_FUNC);
		}		
		break;
        
        case SXM_WIN_FUNC:	
		{
            ui_sxm_switch_win(sxm_para, SXM_WIN_NONE);
		}		
		break;
            
		case SXM_WIN_CATEGORY:	
		{
            ui_sxm_switch_win(sxm_para, SXM_WIN_NONE);
		}		
		break;

		case SXM_WIN_DIRECT_TUNING:	
		{
            ui_sxm_switch_win(sxm_para, SXM_WIN_NONE);
		}		
		break;

		case SXM_WIN_PARENTAL_CTRL:	
		{
            ui_sxm_switch_win(sxm_para, SXM_WIN_FUNC);
		}		
		break;

        case SXM_WIN_PARENTAL_CTRL_CODE:	
		{
            ui_sxm_switch_win(sxm_para, SXM_WIN_PARENTAL_CTRL);
		}		
		break;

        case SXM_WIN_PARENTAL_CTRL_LOCK_CHAN:	
		{
            ui_sxm_switch_win(sxm_para, SXM_WIN_PARENTAL_CTRL);
		}		
		break;
        
        case SXM_WIN_LOCK_CHAN:	
		{
            ui_sxm_switch_win(sxm_para, SXM_WIN_PARENTAL_CTRL);
		}		
		break;

        case SXM_WIN_PARENTAL_CTRL_CHANGE_CODE:	
		{
            ui_sxm_switch_win(sxm_para, SXM_WIN_PARENTAL_CTRL);
		}		
		break;

        default:
        {
        }
        break;
	}
#if 1
    //sxm_cmd2parent(NULL,SWITCH_TO_SMENU,0,0);
#else
    root_app_para_t * root_app_para = app_root_get_para();

    __sxm_msg("root_app_para->home_store=0x%x\n", root_app_para->home_store);
    if(root_app_para->home_store == 0)
    {
        lv_imgbtn_set_state(sxm_para->source_para->ui_top_left, LV_IMAGEBUTTON_STATE_RELEASED);
        ui_home_para_t * home_para = ui_home_get_para();
        
        ui_home_switch_source(home_para->cur_focus_item);
        //app_home_cmd2root(NULL, RESTORE_APP, NULL, NULL);
    }
    else
    {
        lv_imgbtn_set_state(sxm_para->source_para->ui_top_left, LV_IMAGEBUTTON_STATE_PRESSED);
        sxm_cmd2parent(NULL,SWITCH_TO_MMENU,0,0);
    }
#endif
}

static void ui_sxm_top_right_event_handler(ui_sxm_para_t * sxm_para)
{    
    __sxm_msg("sxm_para->dialog_win = %p\n", sxm_para->dialog_win);
    if(sxm_para->dialog_win)
    {
        sxm_tips_dialog_cancel(sxm_para);
        //return;
    }
    
    __sxm_msg("sxm_para->code_kb_type = %d\n", sxm_para->code_kb_type);
    if(sxm_para->code_kb_type > CODE_KEYBOARD_NONE)
    {
        ui_sxm_func_code_screen_uninit(sxm_para);
    }
    
    __sxm_msg("sxm_para->win_type = %d\n", sxm_para->win_type);
    if(sxm_para->win_type == SXM_WIN_NONE)
    {
        if(sxm_para->bottom_fouse_flag == 1)
        {
            ui_sxm_bottom_group_update(sxm_para, 0, 0, 0);
        }
        ui_home_restore_switch_source();
    }
    else
    {
        ui_sxm_switch_win(sxm_para, SXM_WIN_NONE);
    }
}


static void ui_sxm_open_bar_menu_event_handler(ui_sxm_para_t * sxm_para)
{
    //ui_sxm_switch_win(sxm_para, SXM_WIN_NONE);
    
    //if(sxm_para->source_para->rig_select_flag == SOURCE_BOTTOM)
    //{
        ui_sxm_bottom_menu_event_handler(sxm_para);
    //}
    //else
    ///{
        //ui_sxm_top_menu_event_handler(sxm_para);
    //}
}

static void ui_sxm_bottom_menu_event_handler(ui_sxm_para_t * sxm_para)
{
    ui_sxm_switch_win(sxm_para, SXM_WIN_FUNC);
}

static void ui_sxm_broadcast_source_event_handler(ui_sxm_para_t * sxm_para, uint32_t selected)
{
	switch(selected)
	{
	}
}

static void ui_sxm_item_event_handler(ui_sxm_para_t * sxm_para, lv_obj_t * target)
{
	__s32 ret;
	rat_media_type_t media_type;
    uint32_t index = lv_obj_get_index(target);
    index += sxm_para->ui_list_para.page_start_id;

    __sxm_msg("sxm_para->win_type=%d, index=%d\n", sxm_para->win_type, index);
    switch(sxm_para->win_type)
    {    
        case SXM_WIN_FUNC:
        {
            switch(index)
            {
#if 0
                case SXM_FUNC_JUMP_TO_LIVE: 
                {

                }       
                break;

                case SXM_FUNC_CATEGORY: 
                {
                    ui_sxm_switch_win(sxm_para, SXM_WIN_CATEGORY);
                }       
                break;

                case SXM_FUNC_DIRECT_TUNING:    
                {
                    //ui_sxm_switch_win(sxm_para, SXM_WIN_DIRECT_TUNING);
                    ui_source_title(sxm_para->source_para, "Direct Tuning", 0);
                    ui_sxm_func_code_screen_init(sxm_para);
                    ui_sxm_switch_code_type(sxm_para, CODE_KEYBOARD_DIRECT_TUNING);
                }       
                break;
#endif
                case SXM_FUNC_PARENTAL_CONTROL: 
                {
                    ui_sxm_switch_win(sxm_para, SXM_WIN_PARENTAL_CTRL);
                }       
                break;

                case SXM_FUNC_RESET:    
                {
                    sxm_tips_dialog_create(sxm_para, STRING_DIALOG_SXM_RESET, 0, 0, ADLG_YESNO);
                }       
                break;
            }
        }
        break;

        case SXM_WIN_PARENTAL_CTRL:
        {
            switch(index)
            {
                case SXM_PARENTAL_CTRL_CODE: 
                {
                    ui_sxm_func_code_screen_init(sxm_para);
                    ui_sxm_switch_code_type(sxm_para, CODE_KEYBOARD_PARENTAL_CTRL);
                    //ui_sxm_switch_win(sxm_para, SXM_WIN_PARENTAL_CTRL_CODE);
                }       
                break;
            
                case SXM_PARENTAL_CTRL_LOCK_CHAN: 
                {
                    ui_sxm_func_code_screen_init(sxm_para);
                    ui_sxm_switch_code_type(sxm_para, CODE_KEYBOARD_PARENTAL_CTRL_LOCK_CHAN);
                    //ui_sxm_switch_win(sxm_para, SXM_WIN_PARENTAL_CTRL_LOCK_CHAN);
                }       
                break;
            
                case SXM_PARENTAL_CTRL_CHANGE_CODE:    
                {
                    ui_sxm_func_code_screen_init(sxm_para);
                    ui_sxm_switch_code_type(sxm_para, CODE_KEYBOARD_PARENTAL_CTRL_CHANGE_CODE);
                    //ui_sxm_switch_win(sxm_para, SXM_WIN_PARENTAL_CTRL_CHANGE_CODE);
                }       
                break;
            }
        }
        break;
    }
}

static void ui_sxm_item_arrow_event_handler(ui_sxm_para_t * sxm_para, lv_obj_t * target)
{
    int32_t item_index = lv_obj_get_index(target);
    lv_obj_t * next_item = lv_obj_get_child(sxm_para->ui_list_para.item_obj, item_index + 1);
    
    switch(sxm_para->win_type)
    {    
        case SXM_WIN_PARENTAL_CTRL:
        {
            switch(item_index)
            {
                case SXM_PARENTAL_CTRL_CODE: 
                {
                    ui_sxm_func_code_screen_init(sxm_para);
                    ui_sxm_switch_code_type(sxm_para, CODE_KEYBOARD_PARENTAL_CTRL);
                    //ui_sxm_switch_win(sxm_para, SXM_WIN_PARENTAL_CTRL_CODE);
                }       
                break;
            }
        }
        break;
    }
}

static void ui_sxm_func_kb_handler(ui_sxm_para_t * sxm_para, lv_event_t* e)
{
#if 1
    switch(sxm_para->code_kb_type)
    {    
        case CODE_KEYBOARD_DIRECT_TUNING:
        {
            ui_sxm_direct_tuning_channel_handler(sxm_para, e);
        }
        break;

        case CODE_KEYBOARD_PARENTAL_CTRL:
        {
            ui_sxm_parental_ctrl_code_handler(sxm_para, e);
        }
        break;

        case CODE_KEYBOARD_PARENTAL_CTRL_LOCK_CHAN:
        {
            ui_sxm_parental_ctrl_lock_channel_handler(sxm_para, e);
        }
        break;

        case CODE_KEYBOARD_PARENTAL_CTRL_CHANGE_CODE:
        {
            ui_sxm_parental_ctrl_change_code_handler(sxm_para, e);
        }
        break;
    }
#else
    switch(sxm_para->win_type)
    {    
        case SXM_WIN_DIRECT_TUNING:
        {
            ui_sxm_direct_tuning_channel_handler(sxm_para, e);
        }
        break;

        case SXM_WIN_PARENTAL_CTRL_CODE:
        {
            ui_sxm_parental_ctrl_code_handler(sxm_para, e);
        }
        break;

        case SXM_WIN_PARENTAL_CTRL_LOCK_CHAN:
        {
            ui_sxm_parental_ctrl_lock_channel_handler(sxm_para, e);
        }
        break;

        case SXM_WIN_PARENTAL_CTRL_CHANGE_CODE:
        {
            ui_sxm_parental_ctrl_change_code_handler(sxm_para, e);
        }
        break;
    }
#endif
}

static void ui_sxm_func_textarea_handler(ui_sxm_para_t * sxm_para, char* txt)
{
    switch(sxm_para->win_type)
    {    
        case SXM_WIN_DIRECT_TUNING:
        {
            ui_sxm_direct_tuning_channel_handler(sxm_para, txt);
        }
        break;

        case SXM_WIN_PARENTAL_CTRL_CODE:
        {
            ui_sxm_parental_ctrl_code_handler(sxm_para, txt);
        }
        break;

        case SXM_WIN_PARENTAL_CTRL_LOCK_CHAN:
        {
            ui_sxm_direct_tuning_channel_handler(sxm_para, txt);
        }
        break;

        case SXM_WIN_PARENTAL_CTRL_CHANGE_CODE:
        {
            ui_sxm_direct_tuning_channel_handler(sxm_para, txt);
        }
        break;
    }
}

__s32 is_live_play_meta_data_update(ui_sxm_para_t * sxm_para)
{
	reg_sxm_para_t* para=NULL;
	para = (reg_sxm_para_t*)dsk_reg_get_para_by_app(REG_APP_SXM);

	if(para->replay.state != SXM_REPLAY_LIVE) //mllee 150627 added
		return EPDK_FALSE;
	//Channel number
	if(sxm_para->ChanID != para->chan_select_ind.ChanID)
		return EPDK_TRUE;
#ifdef SXM_NEW_PLAYINFO_LONG_SCROLL
	if(eLIBs_strcmp(sxm_para->ChanName,para->chan_select_ind.ChanNameLong) != 0)
		return EPDK_TRUE;
	if(eLIBs_strcmp(sxm_para->Song,para->chan_select_ind.SongExtd) != 0)
		return EPDK_TRUE;
	if(eLIBs_strcmp(sxm_para->Artist,para->chan_select_ind.ArtistExtd) != 0)
		return EPDK_TRUE;
	if(eLIBs_strcmp(sxm_para->ContentInfo,para->chan_select_ind.ContentInfo) != 0)
		return EPDK_TRUE;
#else
	if(eLIBs_strcmp(sxm_para->ChanName,para->chan_select_ind.ChanNameShort) != 0)
		return EPDK_TRUE;
	if(eLIBs_strcmp(sxm_para->Song,para->chan_select_ind.SongBasic) != 0)
		return EPDK_TRUE;
	if(eLIBs_strcmp(sxm_para->Artist,para->chan_select_ind.ArtistExtd) != 0)
		return EPDK_TRUE;
	if(eLIBs_strcmp(sxm_para->ContentInfo,para->chan_select_ind.ContentInfo) != 0)
		return EPDK_TRUE;
#endif
	if(eLIBs_strcmp(sxm_para->CatName,para->chan_select_ind.CatNameShort) != 0)
		return EPDK_TRUE;
	if(sxm_para->CatID != para->chan_select_ind.CatID)
		return EPDK_TRUE;

	return EPDK_FALSE;
	
}

static __s32 ui_sxm_update_timer_proc(ui_sxm_para_t * sxm_para)
{
	__s32 ret = EPDK_OK;    

    if(0)
    {
        sxm_update_low_voltage(sxm_para, 0);
    }

    if(0)
    {
        sxm_update_charge(sxm_para, 0);
    }

    if(0)
    {
        sxm_update_eq(sxm_para, 1);
    }
    
    return ret;
}

static __s32 ui_sxm_ui_update_proc(ui_sxm_para_t * sxm_para, __gui_msg_t *msg)
{
	__s32 ret = EPDK_FAIL;
	reg_root_para_t* last_root_para;
	reg_aux_para_t* aux_para;
	reg_system_para_t* sys_para;
	reg_sxm_para_t* para;

	para = (reg_sxm_para_t*)dsk_reg_get_para_by_app(REG_APP_SXM);
	sys_para = (reg_system_para_t*)dsk_reg_get_para_by_app(REG_APP_SYSTEM);
	last_root_para = (reg_root_para_t*)dsk_reg_get_para_by_app(REG_APP_ROOT);
	aux_para = (reg_aux_para_t*)dsk_reg_get_para_by_app(REG_APP_AUX);	
	

	if(sxm_para == NULL)
		return EPDK_FAIL;

    //eLIBs_printf("ui_aux_in_ui_update_proc: msg->dwAddData1=%d, GUI_MSG_UI_VOLUME_ADJUST=%d\n",msg->dwAddData1, GUI_MSG_UI_VOLUME_ADJUST);
	
	switch(msg->dwAddData1)
    {
		case GUI_MSG_UI_SXM_CHANNEL_UPDATE:
			paint_update_replay_time(sxm_para);
			paint_update_channelinfo(sxm_para);
			show_albumart_by_type_match(sxm_para); //mllee 150530 added
		break;
		case GUI_MSG_UI_SXM_CHANNEL_META_DATA_UPDATE:
			if(para->replay.state != SXM_REPLAY_LIVE)
				break;
			else if(is_live_play_meta_data_update(sxm_para) == EPDK_TRUE)
			{
				paint_update_channelinfo(sxm_para);
				show_albumart_by_type_match(sxm_para); //mllee 150530 added
			}
			break;
		case GUI_MSG_UI_SXM_REPLAY_CHANNEL_UPDATE:
			if(para->replay.state == SXM_REPLAY_LIVE) //mllee 150627 added
				break;

			paint_update_channelinfo(sxm_para);
#ifdef SXM_BF_B_ENABLE
			show_ir_albumart(sxm_para);
#endif
			break;

		case GUI_MSG_UI_SXM_FAST_SEEK:
		{
			__s32 chan_index = -1;
			//mllee 230118 added for fix bug85
			if(para->fast_seek_dir == SXM_SEEK_BROWSER_NEXT)
				chan_index = sxm_get_next_channel_index_in_array(sxm_para);
			else if(para->fast_seek_dir == SXM_SEEK_BROWSER_PREV)
				chan_index = sxm_get_prev_channel_index_in_array(sxm_para);
			if(chan_index != -1)
			{
				__u8 mcu_data[4];
				mcu_data[0] = (__u8)((para->chan_meta_ind_ptr[chan_index].ChanID>>8)&0xFF);
				mcu_data[1] = (__u8)(para->chan_meta_ind_ptr[chan_index].ChanID&0xFF);
				mcu_data[2] = (__u8)((para->chan_meta_ind_ptr[chan_index].SID>>8)&0xFF);
				mcu_data[3] = (__u8)(para->chan_meta_ind_ptr[chan_index].SID&0xFF);
				dsk_send_mcu_cmd(UART_SEND_SXM_BROWSER_CHANNEL,mcu_data);
			}
            
			//__msg("CurChanID = %d\n",sxm_para->CurChanID);
			if(para->fast_seek_dir == SXM_SEEK_BROWSER_NEXT)
				chan_index = sxm_get_next_channel_index_in_array(sxm_para);
			else if(para->fast_seek_dir == SXM_SEEK_BROWSER_PREV)
				chan_index = sxm_get_prev_channel_index_in_array(sxm_para);
			//__msg("chan index = %d\n",chan_index);
			if(chan_index != -1)
			{
				sxm_clear_connect_state(sxm_para);
				sxm_open_paint(sxm_para);
				//if(GUI_IsTimerInstalled(msg->h_deswin , SxmTimerID)) 
				//	GUI_ResetTimer( msg->h_deswin , SxmTimerID , SXM_TIMER_INTERVAL , NULL ); //mllee 150505 mod

				paint_fastseek_update_channelinfo(sxm_para, chan_index);
			}
		}
			break;

		case GUI_MSG_UI_SXM_DELETE_ALBUMART_DATA:
			if(para->pix_onshow_flag != SXM_PIX_SHOW_SERVICE_DEFAULT)
			{
				paint_update_albumArt(sxm_para);
			}
			break;

		case GUI_MSG_UI_SXM_ALBUMART_1_UPDATE:
		{
			__s32 ret;
			__msg("album art 1 update\n");

			para->pix_onshow_flag = SXM_PIX_SHOW_RT1;
			ret = update_rt_albumart(sxm_para);
				
		}	
			break;
			
		case GUI_MSG_UI_SXM_ALBUMART_2_UPDATE:
		{
			__s32 ret;
			__msg("album art 2 update\n");

			para->pix_onshow_flag = SXM_PIX_SHOW_RT2;
			ret = update_rt_albumart(sxm_para);
		}	
			break;

    	case GUI_MSG_UI_VOLUME_ADJUST:
        case GUI_MSG_UI_SUB_VOL:
    	{
            ui_source_send_event(sxm_para->source_para, LV_EVENT_MSG_RECEIVED, (void *)msg);
    	}
    	break;

        // MCU Protocol v2 Data Update Message
        case GUI_MSG_UI_NEW_SXM_UPDATE:
        {
            __sxm_msg("MCU SXM data update received\n");
            if (sxm_para->source_para && reg_app_para) {
                __u8 current_source = reg_app_para->system_para.g_sys_para.current_source;
                if (current_source == SYS_SOURCE_SXM) {
                    UI_MCU_HANDLE_UPDATE(&reg_app_para->sxm_para.sync, sxm_para->mcu_ops, sxm_para);
                }
            }
        }
        break;
        

    	default:
    		break;
	}
}

static __s32 ui_sxm_key_proc(ui_sxm_para_t * sxm_para)
{
	__u16 index = 0;
	__u8 manual_cancel_auto_pty_seek = 0;
	static int last_key = 0;
	
	__sxm_msg("sxm_para->indev_data->event_code=%d..\n",sxm_para->indev_data->event_code);
	__sxm_msg("sxm_para->indev_data->key=%d..\n",sxm_para->indev_data->key);

	if( KEY_DOWN_ACTION == sxm_para->indev_data->event_code )
	{
		last_key = sxm_para->indev_data->key;
		
		switch(sxm_para->indev_data->key)
		{
			case GUI_MSG_KEY_LEFT:
			{
				
			}
			break;
			case GUI_MSG_KEY_RIGHT:
			{
				
			}
			break;
			default:
				break;
		}
	}
	else if( KEY_REPEAT_ACTION == sxm_para->indev_data->event_code )
	{
		switch(sxm_para->indev_data->key)
		{
			case GUI_MSG_KEY_LONGRIGHT:
			{

			}
            break;
            
			case GUI_MSG_KEY_LONGLEFT:
			{

			}
			break;	
            
            case GUI_MSG_KEY_LONGMENU:
            {
                if(GUI_MSG_KEY_MENU == last_key)
                {
                    if(sxm_para->win_type == SXM_WIN_NONE)
                    {
                        ui_sxm_open_bar_menu_event_handler(sxm_para);
                    }
                    else
                    {
                        ui_sxm_top_right_event_handler(sxm_para);
                    }
                }
            }
            break;
		}
		
		last_key = sxm_para->indev_data->key;
	}
	else if( KEY_UP_ACTION == sxm_para->indev_data->event_code )
	{
		switch(sxm_para->indev_data->key)
		{
            case GUI_MSG_KEY_ENTER:
            {
                if(sxm_para->win_type == SXM_WIN_NONE && sxm_para->source_para->menu_win == SOURCE_MENU_WIN_NONE && sxm_para->bottom_fouse_flag == 0)
                {
                    ui_sxm_bottom_group_update(sxm_para, 1, 0, 0);
                }
            }
            break;

			case GUI_MSG_KEY_RIGHT:
			{
				if(GUI_MSG_KEY_RIGHT == last_key)
				{

				}
			}
			break;

			case GUI_MSG_KEY_LONGRIGHT:
			{
				if(GUI_MSG_KEY_LONGRIGHT == last_key)
				{

				}
			}
			break;
			
			case GUI_MSG_KEY_LEFT:
			{
				if(GUI_MSG_KEY_LEFT == last_key)
				{

				}
			}
			break;

            case GUI_MSG_KEY_LONGLEFT:
			{
				if(GUI_MSG_KEY_LONGLEFT == last_key)
                {

                }
			}
			break;

            case GUI_MSG_KEY_MENU:
            {
                if(GUI_MSG_KEY_MENU == last_key)
                {
                    root_app_para_t * root_app_para = app_root_get_para();
                    
                    eLIBs_printf("win_type=%d, source_para->menu_win = %d, root_app_para->home_store=%d\n", sxm_para->win_type, sxm_para->source_para->menu_win, root_app_para->home_store);
                    if(sxm_para->win_type == SXM_WIN_NONE 
                    && sxm_para->source_para->menu_win == SOURCE_MENU_WIN_NONE 
                    && (root_app_para->home_store == 1))
                    {
                        if(sxm_para->bottom_fouse_flag == 1)
                        {
                            ui_sxm_bottom_group_update(sxm_para, 0, 0, 0);
                        }
                        else
                        {
                            ui_sxm_source_event_handler(sxm_para);
                        }
                    }
                    else
                    {
                        if(sxm_para->win_type > SXM_WIN_NONE 
                        && sxm_para->win_type <=SXM_WIN_FUNC 
                        && sxm_para->source_para->menu_group_flag == SOURCE_MENU_GROUP_ADD 
                        && sxm_para->source_para->menu_win != SOURCE_MENU_WIN_MODE)
                        {
                            ui_source_menu_sel_group_update(sxm_para->source_para, &sxm_para->ui_list_para, ui_sxm_get_item_type, sxm_para, SOURCE_MENU_GROUP_REMOVE_SUB, 0);
                        }
                        else
                        {
                            ui_sxm_top_left_event_handler(sxm_para);
                        }
                    }
                }
            }
            break;

            case GUI_MSG_KEY_PLAY_PAUSE:
            {
                if(GUI_MSG_KEY_PLAY_PAUSE == last_key)
                {

                }
            }
            break;
						
			default:
				break;
		}
		
		last_key = 0;
	}
	
	return EPDK_OK;
}

///////////////////// FUNCTIONS ////////////////////
static void ui_event_sxm_screen_cb(lv_event_t * e)
{
	__s32 ret = 0;
    ui_sxm_para_t * sxm_para = lv_event_get_user_data(e);
    lv_event_code_t event_code = lv_event_get_code(e);
    lv_obj_t * target = lv_event_get_target(e);
    lv_obj_t * current_target = lv_event_get_current_target(e);

    if(event_code == LV_EVENT_MSG_RECEIVED)
    {
		__gui_msg_t * msg = (__gui_msg_t *)lv_event_get_param(e);
		__sxm_msg("ui_event_sxm_screen:msg->id=%d\n", msg->id);
		if((lv_obj_has_flag(current_target, LV_OBJ_FLAG_HIDDEN)) 
			&& (sxm_para->mode_store == 1)
			&& (LOWORD(msg->id) != GUI_MSG_SET_APP_LAYER_SUSPEND)
			&& (LOWORD(msg->id) != GUI_MSG_SET_APP_LAYER_ON)
			&& (LOWORD(msg->id) != GUI_MSG_SET_APP_LAYER_SLEEP)
			&& (LOWORD(msg->id) != GUI_MSG_SET_APP_LAYER_OFF)
			&& (LOWORD(msg->id) != GUI_MSG_SET_APP_STORE_LAYER_SUSPEND)
			&& (LOWORD(msg->id) != GUI_MSG_SET_APP_STORE_LAYER_ON)
			&& (LOWORD(msg->id) != GUI_MSG_SET_APP_STORE_LAYER_SLEEP)
			&& (LOWORD(msg->id) != GUI_MSG_SET_APP_STORE_LAYER_OFF)
			&& (HIWORD(msg->id) != GUI_MSG_SXM_REFRESH_UNINIT)
			)
		{
			return;
		}
            
		if((sxm_para->mode_uninit_flag == 1) && (HIWORD(msg->id) != GUI_MSG_SXM_REFRESH_UNINIT))
		{
			eLIBs_printf("sxm_para->mode_uninit_flag == 1 HIWORD(msg->id)=%d!!!!!!!!!\n", HIWORD(msg->id));
			return;
		}
        
    	switch(LOWORD(msg->id)) 
    	{
			case GUI_MSG_SET_APP_LAYER_ON:
			case GUI_MSG_SET_APP_STORE_LAYER_ON:
			{
				__sxm_msg("GUI_MSG_SET_APP_STORE_LAYER_ON\n");
				ui_sxm_layer_on(sxm_para);
			}
			break;
			
			case GUI_MSG_SET_APP_LAYER_SUSPEND:
			case GUI_MSG_SET_APP_STORE_LAYER_SLEEP:
			{
				__sxm_msg("GUI_MSG_SET_APP_STORE_LAYER_SLEEP\n");
				ui_sxm_layer_sleep(sxm_para);
			}
			break;
			
			case GUI_MSG_PAINT:
			{
				ui_sxm_refresh_handler(sxm_para, msg);
			}
			break;

			case DSK_MSG_HFP_STATUS:
			{
			}
			break;
			case DSK_MSG_DVR_CONNECT_STATUS:
			{
			}
			break;
						
			case GUI_MSG_MUTE_STATUS_CHANGE:
			{
			}
			break;
			
#ifdef AOA_WALLPAPER_ENTER
			case DSK_MSG_AOA_APP_CHANGE_BG:
			{
				reg_system_para_t*sys_para;
				sys_para = (reg_system_para_t*)dsk_reg_get_para_by_app(REG_APP_SYSTEM);
		
				if(lv_obj_has_flag(current_target, LV_OBJ_FLAG_HIDDEN))
				{
					break;
				}
				gscene_bgd_set_img_src(BG_INDEX_AUTO);
			}
			break;
#endif
            
            case GUI_MSG_SXM_DIALOG_CONFIRM:
            {
                __sxm_msg("GUI_MSG_SXM_DIALOG_CONFIRM\n");
                sxm_tips_dialog_confirm(sxm_para);
            }
            break;
            
            case GUI_MSG_SXM_DIALOG_CANCEL:
            {
                __sxm_msg("GUI_MSG_SXM_DIALOG_CANCEL\n");
                sxm_tips_dialog_cancel(sxm_para);
            }
            break;

            case DSK_MSG_HOME_ANIM_STOP:
            {
                ui_source_title(sxm_para->source_para, "SiriusXM", 0);
                ui_source_sub_close(sxm_para->source_para);
            }
            break;

            case GUI_MSG_UI_UPDATE:
            {
                ui_sxm_ui_update_proc(sxm_para, msg);
            }
            break;

		    default:
				__sxm_msg("msg->id: %d\n", msg->id);
		    break;
	    }
    }
    else if(event_code == LV_EVENT_KEY)
    {
    	sxm_para->indev_data = lv_event_get_param(e);
		ui_sxm_key_proc(sxm_para);
        if(!sxm_para->bottom_fouse_flag || (sxm_para->indev_data->key != GUI_MSG_KEY_UP && sxm_para->indev_data->key != GUI_MSG_KEY_DOWN))
        {
            ui_source_send_event(sxm_para->source_para, LV_EVENT_KEY, sxm_para->indev_data);
        }
    }
}

static void ui_event_sxm_play_logo_panel_cb(lv_event_t * e)
{
	ui_sxm_para_t * sxm_para = lv_event_get_user_data(e);
    lv_event_code_t event_code = lv_event_get_code(e);
    lv_obj_t * target = lv_event_get_target(e);
    lv_obj_t * current_target = lv_event_get_current_target(e);

	if(event_code == LV_EVENT_PRESSED)
	{
		app_root_msg_send_opn(GUI_MSG_BEEP_SETTING,SEND_MCU_BEEP_SETTING_TIME,0,0,0,0,0);
	}
	else if(event_code == LV_EVENT_SHORT_CLICKED)
	{
        ui_sxm_source_event_handler(sxm_para);
	}
}

static void ui_event_sxm_bottom_list_cb(lv_event_t * e)
{
	ui_sxm_para_t * sxm_para = lv_event_get_user_data(e);
    lv_event_code_t event_code = lv_event_get_code(e);
    lv_obj_t * target = lv_event_get_target(e);
    lv_obj_t * current_target = lv_event_get_current_target(e);

	if(event_code == LV_EVENT_PRESSED)
	{
		app_root_msg_send_opn(GUI_MSG_BEEP_SETTING,SEND_MCU_BEEP_SETTING_TIME,0,0,0,0,0);
	}
	else if(event_code == LV_EVENT_SHORT_CLICKED)
	{
        ui_sxm_switch_win(sxm_para, SXM_WIN_LIST);
	}
}

static void ui_event_sxm_bottom_cat_cb(lv_event_t * e)
{
	ui_sxm_para_t * sxm_para = lv_event_get_user_data(e);
    lv_event_code_t event_code = lv_event_get_code(e);
    lv_obj_t * target = lv_event_get_target(e);
    lv_obj_t * current_target = lv_event_get_current_target(e);

	if(event_code == LV_EVENT_PRESSED)
	{
		app_root_msg_send_opn(GUI_MSG_BEEP_SETTING,SEND_MCU_BEEP_SETTING_TIME,0,0,0,0,0);
	}
	else if(event_code == LV_EVENT_SHORT_CLICKED)
	{
        ui_sxm_switch_win(sxm_para, SXM_WIN_CATEGORY);
	}
}

static void ui_event_sxm_bottom_replay_cb(lv_event_t * e)
{
	ui_sxm_para_t * sxm_para = lv_event_get_user_data(e);
    lv_event_code_t event_code = lv_event_get_code(e);
    lv_obj_t * target = lv_event_get_target(e);
    lv_obj_t * current_target = lv_event_get_current_target(e);

	if(event_code == LV_EVENT_PRESSED)
	{
		app_root_msg_send_opn(GUI_MSG_BEEP_SETTING,SEND_MCU_BEEP_SETTING_TIME,0,0,0,0,0);
	}
	else if(event_code == LV_EVENT_SHORT_CLICKED)
	{
        if(sxm_para->live_replay_flag == 0)
        {
            sxm_para->live_replay_flag = 1;
            lv_label_set_text(ui_sxm_bottom_replay_label, "Replay");
        }
        else
        {
            sxm_para->live_replay_flag = 0;
            lv_label_set_text(ui_sxm_bottom_replay_label, "Live");
        }
	}
}

static void ui_event_sxm_bottom_direct_cb(lv_event_t * e)
{
    static __u8 pressed_flag = 0;
	ui_sxm_para_t * sxm_para = lv_event_get_user_data(e);
    lv_event_code_t event_code = lv_event_get_code(e);
    lv_obj_t * target = lv_event_get_target(e);
    lv_obj_t * current_target = lv_event_get_current_target(e);

	if(event_code == LV_EVENT_PRESSED)
	{
        pressed_flag = 1;
		app_root_msg_send_opn(GUI_MSG_BEEP_SETTING,SEND_MCU_BEEP_SETTING_TIME,0,0,0,0,0);
	}
	else if(event_code == LV_EVENT_SHORT_CLICKED)
	{
        if(pressed_flag)
        {
            ui_sxm_func_code_screen_init(sxm_para);
            ui_sxm_switch_code_type(sxm_para, CODE_KEYBOARD_DIRECT_TUNING);
        }
	}
    else if(event_code == LV_EVENT_CLICKED)
    {
        pressed_flag = 0;
    }
}

static void ui_event_sxm_left_cb(lv_event_t * e)
{
	ui_sxm_para_t * sxm_para = lv_event_get_user_data(e);
    lv_event_code_t event_code = lv_event_get_code(e);
    lv_obj_t * target = lv_event_get_target(e);
    lv_obj_t * current_target = lv_event_get_current_target(e);

	if(event_code == LV_EVENT_PRESSED)
	{
		app_root_msg_send_opn(GUI_MSG_BEEP_SETTING,SEND_MCU_BEEP_SETTING_TIME,0,0,0,0,0);
	}
	else if(event_code == LV_EVENT_SHORT_CLICKED)
	{
        ui_sxm_top_left_event_handler(sxm_para);
	}
}

static void ui_event_sxm_right_cb(lv_event_t * e)
{
	ui_sxm_para_t * sxm_para = lv_event_get_user_data(e);
    lv_event_code_t event_code = lv_event_get_code(e);
    lv_obj_t * target = lv_event_get_target(e);
    lv_obj_t * current_target = lv_event_get_current_target(e);

	if(event_code == LV_EVENT_PRESSED)
	{
		app_root_msg_send_opn(GUI_MSG_BEEP_SETTING,SEND_MCU_BEEP_SETTING_TIME,0,0,0,0,0);
	}
	else if(event_code == LV_EVENT_SHORT_CLICKED)
	{
        ui_sxm_top_right_event_handler(sxm_para);
	}
}

void ui_event_sxm_open_bar_cb(lv_event_t * e)
{
    lv_event_code_t event_code = lv_event_get_code(e);
	ui_sxm_para_t * sxm_para = lv_event_get_user_data(e);

    if(event_code == LV_EVENT_PRESSED)
	{
		//ui_fm_keystripe_anim(fm_para);
		app_root_msg_send_opn(GUI_MSG_BEEP_SETTING,SEND_MCU_BEEP_SETTING_TIME,0,0,0,0,0);
	}
	else if(event_code == LV_EVENT_SHORT_CLICKED) {
        ui_sxm_open_bar_menu_event_handler(sxm_para);
	}
}

void ui_event_sxm_bottom_menu_cb(lv_event_t * e)
{
    lv_event_code_t event_code = lv_event_get_code(e);
	ui_sxm_para_t * sxm_para = lv_event_get_user_data(e);

    if(event_code == LV_EVENT_PRESSED)
	{
		app_root_msg_send_opn(GUI_MSG_BEEP_SETTING,SEND_MCU_BEEP_SETTING_TIME,0,0,0,0,0);
	}
	else if(event_code == LV_EVENT_SHORT_CLICKED) {
        if(sxm_para->win_type > SXM_WIN_NONE 
        && sxm_para->win_type <=SXM_WIN_FUNC 
        && sxm_para->source_para->menu_group_flag == SOURCE_MENU_GROUP_ADD 
        && sxm_para->source_para->menu_win != SOURCE_MENU_WIN_MODE)
        {
            ui_source_menu_sel_group_update(sxm_para->source_para, &sxm_para->ui_list_para, ui_sxm_get_item_type, sxm_para, SOURCE_MENU_GROUP_REMOVE_SUB, 0);
        }
        ui_sxm_bottom_menu_event_handler(sxm_para);
        ui_source_menu_sel_group_update(sxm_para->source_para, &sxm_para->ui_list_para, ui_sxm_get_item_type, sxm_para, SOURCE_MENU_GROUP_ADD, 0);
	}
}

static void ui_event_sxm_menu_item_cb(lv_event_t * e)
{
	ui_sxm_para_t * sxm_para = lv_event_get_user_data(e);
    lv_event_code_t event_code = lv_event_get_code(e);
    lv_obj_t * target = lv_event_get_target(e);
    lv_obj_t * current_target = lv_event_get_current_target(e);
    lv_obj_t * list_item_panel = lv_obj_get_child(current_target, 0);
    lv_obj_t * item_text_panel = lv_obj_get_child(list_item_panel, 0);
    lv_obj_t * item_shadow_text = lv_obj_get_child(item_text_panel, 0);
    lv_obj_t * item_main_text = lv_obj_get_child(item_text_panel, 1);
	__u32 index = lv_obj_get_index(current_target);
    static __u8 move_flag = 0;

	if(event_code == LV_EVENT_PRESSED)
	{		
        ui_source_menu_sel_group_update(sxm_para->source_para, &sxm_para->ui_list_para, ui_sxm_get_item_type, sxm_para, SOURCE_MENU_GROUP_ADD, 0);
        move_flag = 0;
		app_root_msg_send_opn(GUI_MSG_BEEP_SETTING,SEND_MCU_BEEP_SETTING_TIME,0,0,0,0,0);
	}
	else if(event_code == LV_EVENT_SHORT_CLICKED)
	{
        if(move_flag)
        {
            return;
        }
		ui_sxm_item_event_handler(sxm_para, current_target);
	}
	else if(event_code == LV_EVENT_LONG_PRESSED)
	{
		lv_label_set_long_mode(item_shadow_text, LV_LABEL_LONG_SCROLL_ONCE);
		lv_label_set_long_mode(item_main_text, LV_LABEL_LONG_SCROLL_ONCE);
	}
	else if(event_code == LV_EVENT_RELEASED)
	{		
		lv_label_set_long_mode(item_shadow_text, LV_LABEL_LONG_CLIP);
		lv_label_set_long_mode(item_main_text, LV_LABEL_LONG_CLIP);
	}
    else if(event_code == LV_EVENT_PRESS_MOVEING)
	{		
        move_flag = 1;
	}
	else if(event_code == LV_EVENT_CLICKED)
	{		
        move_flag = 0;
	}
}

static void ui_event_sxm_menu_arrow_cb(lv_event_t * e)
{
	__u32 index, item_index;
	ui_sxm_para_t * sxm_para = lv_event_get_user_data(e);
    lv_event_code_t event_code = lv_event_get_code(e);
    lv_obj_t * target = lv_event_get_target(e);
    lv_obj_t * current_target = lv_event_get_current_target(e);
    lv_obj_t * ui_list_item_arrow = lv_obj_get_parent(target);
    lv_obj_t * ui_list_item_content_panel = lv_obj_get_parent(ui_list_item_arrow);
    lv_obj_t * ui_list_item_panel = lv_obj_get_parent(ui_list_item_content_panel);
    lv_obj_t * ui_list_item = lv_obj_get_parent(ui_list_item_panel);
	
	if(event_code == LV_EVENT_PRESSED)
	{
        ui_source_menu_sel_group_update(sxm_para->source_para, &sxm_para->ui_list_para, ui_sxm_get_item_type, sxm_para, SOURCE_MENU_GROUP_ADD, 0);
	}
	else if(event_code == LV_EVENT_VALUE_CHANGED)
	{
        ui_sxm_item_arrow_event_handler(sxm_para, ui_list_item);
	}
}

static void ui_event_sxm_preset_screen(lv_event_t * e)
{
	__u32 i;
	ui_sxm_para_t * sxm_para = lv_event_get_user_data(e);
	lv_event_code_t event_code = lv_event_get_code(e);
	lv_obj_t * target = lv_event_get_target(e);
	lv_obj_t * current_target = lv_event_get_current_target(e);
	static __u32 preset_index;
	static __s8 last_key_type = 0;	
    static __u8 move_flag = 0;
    reg_sxm_para_t* para = (reg_sxm_para_t*)dsk_reg_get_para_by_app(REG_APP_FM);

	if((current_target == target) || lv_obj_has_flag(current_target, LV_OBJ_FLAG_HIDDEN))
	{
		return;
	}

	if(event_code == LV_EVENT_PRESSED)
	{
        ui_source_menu_sel_group_update(sxm_para->source_para, &sxm_para->ui_list_para, ui_sxm_get_item_type, sxm_para, SOURCE_MENU_GROUP_ADD, 0);
        move_flag = 0;
		app_root_msg_send_opn(GUI_MSG_BEEP_SETTING,SEND_MCU_BEEP_SETTING_TIME,0,0,0,0,0);		
	}
	else if(event_code == LV_EVENT_SHORT_CLICKED)
	{
        if(move_flag)
        {
            return;
        }
        preset_index = lv_obj_get_index(current_target) * 2 + lv_obj_get_index(target);

        //if(para->FM1_3_AM1_2_freq[sxm_para->curFM1_3AM1_2_id][preset_index] == 0)
        //{
         //   para->FM1_3_AM1_2_freq[sxm_para->curFM1_3AM1_2_id][preset_index] = sxm_para->sxmchannelfreq;
        //    sxm_view_update_preset(sxm_para, 1);
        //}
        //else
        {
            ui_sxm_switch_win(sxm_para, SXM_WIN_NONE);
            ui_source_panel_close(sxm_para->source_para, LV_ANIM_ON);
            //lv_imgbtn_set_state(sxm_para->source_para->ui_top_right, LV_IMGBTN_STATE_RELEASED);
            //lv_obj_add_flag(sxm_para->source_para->ui_rig_list_panel, LV_OBJ_FLAG_HIDDEN);
            //lv_obj_remove_flag(sxm_para->source_para->ui_bottom, LV_OBJ_FLAG_HIDDEN);      /// Flags
        }
	}
	else if(event_code == LV_EVENT_LONG_PRESSED)
	{
#if 1
        preset_index = lv_obj_get_index(current_target) * 2 + lv_obj_get_index(target);
        //eLIBs_printf("preset_index=%d\n", preset_index);
        
        //para->FM1_3_AM1_2_freq[sxm_para->curFM1_3AM1_2_id][preset_index] = sxm_para->sxmchannelfreq;
#else
		preset_index = lv_obj_get_index(current_target);
		__sxm_msg("target=0x%x, preset_index=%d\n", target, preset_index);

        para->FM1_3_AM1_2_freq[sxm_para->curFM1_3AM1_2_id][preset_index] = sxm_para->sxmchannelfreq;
#endif
        //sxm_view_update_preset(sxm_para, 1);
	}
    else if(event_code == LV_EVENT_PRESS_MOVEING)
	{		
        move_flag = 1;
	}
	else if(event_code == LV_EVENT_CLICKED)
	{		
        move_flag = 0;
	}
    else if(event_code == LV_EVENT_FOCUSED)
	{		
        __sxm_msg("ui_event_sxm_preset_screen LV_EVENT_FOCUSED target=%p", target);
	}
}

static void ui_event_sxm_func_kb_cb(lv_event_t* e) 
{
	ui_sxm_para_t * sxm_para = lv_event_get_user_data(e);

    ui_sxm_func_kb_handler(sxm_para, e);
}

static void ui_event_sxm_func_textarea_cb(lv_event_t* e) 
{
	ui_sxm_para_t * sxm_para = lv_event_get_user_data(e);
    lv_obj_t* kb = lv_event_get_target(e);
    uint16_t btn_id = lv_btnmatrix_get_selected_btn(kb); // 修正获取按钮ID的方式
    const char* txt = lv_btnmatrix_get_btn_text(kb, btn_id);

    ui_sxm_func_textarea_handler(sxm_para, txt);
}

static void ui_event_sxm_timer_cb(lv_timer_t * t)
{	
	ui_sxm_para_t * sxm_para = lv_timer_get_user_data(t);

    if(t == ui_sxm_kb_timer)
    {
        ui_sxm_kb_timer = NULL;
#if 0
        lv_label_set_text(ui_sxm_func_code_cursor_1, "-");
        lv_label_set_text(ui_sxm_func_code_cursor_2, "-");
        lv_label_set_text(ui_sxm_func_code_cursor_3, "-");
        lv_label_set_text(ui_sxm_func_code_cursor_4, "-");
#endif
        if(sxm_para->win_type > SXM_WIN_PARENTAL_CTRL)
            ui_sxm_switch_win(sxm_para, SXM_WIN_PARENTAL_CTRL);
        else
            ui_sxm_switch_win(sxm_para, SXM_WIN_FUNC);
    }
    else
    {
	    sxm_time_proc(sxm_para);
    }
}

///////////////////// SCREENS ////////////////////
static void ui_sxm_switch_code_type(ui_sxm_para_t * sxm_para, __u32 code_kb_type)
{  
    lv_textarea_set_text(ui_sxm_func_code_textarea, "");

    __sxm_msg("code_kb_type=%d\n", code_kb_type);
    
    sxm_para->code_kb_type = code_kb_type;

    lv_obj_clear_flag(ui_sxm_func_code_keyboard, LV_OBJ_FLAG_HIDDEN);      /// Flags
    lv_group_focus_obj(ui_sxm_func_code_keyboard);
    lv_group_set_editing(lv_group_get_default(), true);
    ui_source_menu_group_update(sxm_para->source_para, &sxm_para->ui_list_para, ui_sxm_get_item_type, sxm_para, SOURCE_MENU_GROUP_REMOVE_SUB, 0);

    if(code_kb_type == CODE_KEYBOARD_NONE)
    {
        lv_textarea_set_placeholder_text(ui_sxm_func_code_textarea, " ");
        //lv_label_set_text(ui_sxm_func_code_text, " ");
        lv_obj_add_flag(ui_sxm_func_code, LV_OBJ_FLAG_HIDDEN);     /// Flags
    }
    else
    {
        ui_source_sub_open(sxm_para->source_para);
        if(code_kb_type == CODE_KEYBOARD_DIRECT_TUNING)
        {
            //lv_obj_add_flag(ui_sxm_func_code_cursor_4, LV_OBJ_FLAG_HIDDEN);
            //lv_label_set_text(ui_sxm_func_code_text, "Enter Channel.");
            lv_textarea_set_placeholder_text(ui_sxm_func_code_textarea, "Enter Channel.");
            ui_sxm_bottom_group_update(sxm_para, 2, 3, 0);
        }
        else 
        {
            if(code_kb_type == CODE_KEYBOARD_PARENTAL_CTRL)
            {
                if(sxm_para->parental_onoff)
                {
                    //lv_label_set_text(ui_sxm_func_code_text, "Enter code to turn on.");
                    lv_textarea_set_placeholder_text(ui_sxm_func_code_textarea, "Enter code to turn on.");
                }
                else
                {
                    //lv_label_set_text(ui_sxm_func_code_text, "Enter code to turn off.");
                    lv_textarea_set_placeholder_text(ui_sxm_func_code_textarea, "Enter code to turn off.");
                }
            }
            else if(sxm_para->code_kb_type == CODE_KEYBOARD_PARENTAL_CTRL_LOCK_CHAN)
            {
                //lv_label_set_text(ui_sxm_func_code_text, "Enter code.");
                lv_textarea_set_placeholder_text(ui_sxm_func_code_textarea, "Enter code.");
            }
            else if(sxm_para->code_kb_type == CODE_KEYBOARD_PARENTAL_CTRL_CHANGE_CODE)
            {
                //lv_label_set_text(ui_sxm_func_code_text, "Enter old code.");
                lv_textarea_set_placeholder_text(ui_sxm_func_code_textarea, "Enter old code.");
            }
            
            //lv_obj_clear_flag(ui_sxm_func_code_cursor_4, LV_OBJ_FLAG_HIDDEN);
        }
    
        lv_obj_remove_flag(ui_sxm_func_code, LV_OBJ_FLAG_HIDDEN);     /// Flags
        
    #if 0
        if(ui_sxm_func_code_blur == NULL)
            ui_sxm_func_code_blur = ui_canvas_blur_init(ui_sxm_func_code_blur_panel, sxm_para->source_para->ui_menu_panel, 20);
    #endif
    }
}

static void ui_sxm_func_code_screen_init(ui_sxm_para_t * sxm_para)
{    

    if(ui_sxm_func_code)
    {
#if 1
#if 0
        lv_obj_clear_flag(ui_sxm_func_code_keyboard, LV_OBJ_FLAG_HIDDEN);      /// Flags
        lv_group_focus_obj(ui_sxm_func_code_keyboard);
        lv_group_set_editing(lv_group_get_default(), true);
        ui_source_menu_group_update(sxm_para->source_para, &sxm_para->ui_list_para, ui_sxm_get_item_type, sxm_para, SOURCE_MENU_GROUP_REMOVE_SUB, 0);

        if(code_kb_type == CODE_KEYBOARD_NONE)
        {
            lv_label_set_text(ui_sxm_func_code_text, " ");
        }
        else
        {
            ui_source_sub_open(sxm_para->source_para);
            if(code_kb_type == CODE_KEYBOARD_DIRECT_TUNING)
            {
                lv_obj_add_flag(ui_sxm_func_code_cursor_4, LV_OBJ_FLAG_HIDDEN);
                lv_label_set_text(ui_sxm_func_code_text, "Enter Channel.");
            }
            else 
            {
                if(code_kb_type == CODE_KEYBOARD_PARENTAL_CTRL)
                {
                    if(sxm_para->parental_onoff)
                    {
                        lv_label_set_text(ui_sxm_func_code_text, "Enter code to turn on.");
                    }
                    else
                    {
                        lv_label_set_text(ui_sxm_func_code_text, "Enter code to turn off.");
                    }
                }
                else if(sxm_para->code_kb_type == CODE_KEYBOARD_PARENTAL_CTRL_LOCK_CHAN)
                {
                    lv_label_set_text(ui_sxm_func_code_text, "Enter code.");
                }
                else if(sxm_para->code_kb_type == CODE_KEYBOARD_PARENTAL_CTRL_CHANGE_CODE)
                {
                    lv_label_set_text(ui_sxm_func_code_text, "Enter old code.");
                }
                
                lv_obj_clear_flag(ui_sxm_func_code_cursor_4, LV_OBJ_FLAG_HIDDEN);
            }
        
            lv_obj_remove_flag(ui_sxm_func_code, LV_OBJ_FLAG_HIDDEN);     /// Flags
            
            if(ui_sxm_func_code_blur == NULL)
                ui_sxm_func_code_blur = ui_canvas_blur_init(ui_sxm_func_code_blur_panel, sxm_para->source_para->ui_menu_panel, 20);
        }
#endif
#else
        if(sxm_para->win_type == SXM_WIN_DIRECT_TUNING)
        {
            lv_obj_add_flag(ui_sxm_func_code_cursor_4, LV_OBJ_FLAG_HIDDEN);
            lv_label_set_text(ui_sxm_func_code_text, "Enter Channel.");
            lv_obj_remove_flag(ui_sxm_func_code, LV_OBJ_FLAG_HIDDEN);     /// Flags
        }
        else if(sxm_para->win_type == SXM_WIN_PARENTAL_CTRL_CODE)
        {
            lv_obj_clear_flag(ui_sxm_func_code_cursor_4, LV_OBJ_FLAG_HIDDEN);
            if(1)
                lv_label_set_text(ui_sxm_func_code_text, "Enter code to turn on.");
            else
                lv_label_set_text(ui_sxm_func_code_text, "Enter code to turn off.");
            lv_obj_remove_flag(ui_sxm_func_code, LV_OBJ_FLAG_HIDDEN);     /// Flags
        }
        else if(sxm_para->win_type == SXM_WIN_PARENTAL_CTRL_LOCK_CHAN)
        {
            lv_obj_clear_flag(ui_sxm_func_code_cursor_4, LV_OBJ_FLAG_HIDDEN);
            lv_label_set_text(ui_sxm_func_code_text, "Enter code.");
            lv_obj_remove_flag(ui_sxm_func_code, LV_OBJ_FLAG_HIDDEN);     /// Flags
        }
        else if(sxm_para->win_type == SXM_WIN_PARENTAL_CTRL_CHANGE_COD)
        {
            lv_obj_clear_flag(ui_sxm_func_code_cursor_4, LV_OBJ_FLAG_HIDDEN);
            lv_label_set_text(ui_sxm_func_code_text, "Enter old code.");
            lv_obj_remove_flag(ui_sxm_func_code, LV_OBJ_FLAG_HIDDEN);     /// Flags
        }
        else
        {
            lv_label_set_text(ui_sxm_func_code_text, " ");
        }
#endif

        return;
    }
    
    ui_sxm_func_code = lv_obj_create(ui_source_top_dialog);//sxm_para->source_para->ui_menu_panel
    lv_obj_set_width(ui_sxm_func_code, lv_pct(100));
    lv_obj_set_height(ui_sxm_func_code, lv_pct(86));
    lv_obj_set_x(ui_sxm_func_code, lv_pct(0));
    lv_obj_set_y(ui_sxm_func_code, lv_pct(14));
    //lv_obj_set_align(ui_sxm_func_code, LV_ALIGN_TOP_MID);
    lv_obj_add_flag(ui_sxm_func_code, LV_OBJ_FLAG_HIDDEN);      /// Flags
    lv_obj_remove_flag(ui_sxm_func_code, LV_OBJ_FLAG_SCROLLABLE);      /// Flags
    lv_obj_set_style_bg_color(ui_sxm_func_code, lv_color_hex(0x000000), LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_bg_opa(ui_sxm_func_code, 255, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_border_width(ui_sxm_func_code, 0, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_pad_left(ui_sxm_func_code, 0, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_pad_right(ui_sxm_func_code, 0, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_pad_top(ui_sxm_func_code, 0, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_pad_bottom(ui_sxm_func_code, 0, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_pad_row(ui_sxm_func_code, 0, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_pad_column(ui_sxm_func_code, 0, LV_PART_MAIN | LV_STATE_DEFAULT);

#if 0
    ui_sxm_func_code_blur_panel = lv_obj_create(ui_sxm_func_code);
    lv_obj_set_width(ui_sxm_func_code_blur_panel, lv_pct(100));
    lv_obj_set_height(ui_sxm_func_code_blur_panel, lv_pct(100));
    //lv_obj_set_align(ui_sxm_func_code_blur_panel, LV_ALIGN_BOTTOM_MID);
    lv_obj_remove_flag(ui_sxm_func_code_blur_panel, LV_OBJ_FLAG_SCROLLABLE);      /// Flags
    lv_obj_set_style_bg_color(ui_sxm_func_code_blur_panel, lv_color_hex(0x000000), LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_bg_opa(ui_sxm_func_code_blur_panel, 0, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_border_width(ui_sxm_func_code_blur_panel, 0, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_pad_left(ui_sxm_func_code_blur_panel, 0, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_pad_right(ui_sxm_func_code_blur_panel, 0, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_pad_top(ui_sxm_func_code_blur_panel, 0, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_pad_bottom(ui_sxm_func_code_blur_panel, 0, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_pad_row(ui_sxm_func_code_blur_panel, 0, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_pad_column(ui_sxm_func_code_blur_panel, 0, LV_PART_MAIN | LV_STATE_DEFAULT);

    ui_sxm_func_code_panel = lv_obj_create(ui_sxm_func_code);
    lv_obj_set_width(ui_sxm_func_code_panel, lv_pct(100));
    lv_obj_set_height(ui_sxm_func_code_panel, lv_pct(50));
    lv_obj_set_align(ui_sxm_func_code_panel, LV_ALIGN_TOP_MID);
    lv_obj_set_flex_flow(ui_sxm_func_code_panel, LV_FLEX_FLOW_ROW);
    lv_obj_set_flex_align(ui_sxm_func_code_panel, LV_FLEX_ALIGN_SPACE_EVENLY, LV_FLEX_ALIGN_CENTER, LV_FLEX_ALIGN_CENTER);
    lv_obj_remove_flag(ui_sxm_func_code_panel, LV_OBJ_FLAG_SCROLLABLE);      /// Flags
    lv_obj_set_style_bg_color(ui_sxm_func_code_panel, lv_color_hex(0x000000), LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_bg_opa(ui_sxm_func_code_panel, 0, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_border_width(ui_sxm_func_code_panel, 0, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_pad_left(ui_sxm_func_code_panel, 0, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_pad_right(ui_sxm_func_code_panel, 0, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_pad_top(ui_sxm_func_code_panel, 0, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_pad_bottom(ui_sxm_func_code_panel, 0, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_pad_row(ui_sxm_func_code_panel, 0, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_pad_column(ui_sxm_func_code_panel, 0, LV_PART_MAIN | LV_STATE_DEFAULT);

    ui_sxm_func_code_cursor_1 = lv_label_create(ui_sxm_func_code_panel);
    lv_obj_set_width(ui_sxm_func_code_cursor_1, lv_pct(10));   /// 1
    lv_obj_set_height(ui_sxm_func_code_cursor_1, LV_SIZE_CONTENT);    /// 1
    lv_obj_set_y(ui_sxm_func_code_cursor_1, 0);
    lv_obj_set_x(ui_sxm_func_code_cursor_1, lv_pct(-30));
    lv_obj_set_align(ui_sxm_func_code_cursor_1, LV_ALIGN_CENTER);
    lv_label_set_text(ui_sxm_func_code_cursor_1, "-");
    lv_obj_set_style_text_color(ui_sxm_func_code_cursor_1, lv_color_hex(0xFFFFFF), LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_text_opa(ui_sxm_func_code_cursor_1, 255, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_text_align(ui_sxm_func_code_cursor_1, LV_TEXT_ALIGN_CENTER, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_text_font(ui_sxm_func_code_cursor_1, lv_font_xxxlarge.font, LV_PART_MAIN | LV_STATE_DEFAULT);

    ui_sxm_func_code_cursor_2 = lv_label_create(ui_sxm_func_code_panel);
    lv_obj_set_width(ui_sxm_func_code_cursor_2, lv_pct(10));   /// 1
    lv_obj_set_height(ui_sxm_func_code_cursor_2, LV_SIZE_CONTENT);    /// 1
    lv_obj_set_align(ui_sxm_func_code_cursor_2, LV_ALIGN_CENTER);
    lv_obj_set_y(ui_sxm_func_code_cursor_2, 0);
    lv_obj_set_x(ui_sxm_func_code_cursor_2, lv_pct(-10));
    lv_label_set_text(ui_sxm_func_code_cursor_2, "-");
    lv_obj_set_style_text_color(ui_sxm_func_code_cursor_2, lv_color_hex(0xFFFFFF), LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_text_opa(ui_sxm_func_code_cursor_2, 255, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_text_align(ui_sxm_func_code_cursor_2, LV_TEXT_ALIGN_CENTER, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_text_font(ui_sxm_func_code_cursor_2, lv_font_xxxlarge.font, LV_PART_MAIN | LV_STATE_DEFAULT);

    ui_sxm_func_code_cursor_3 = lv_label_create(ui_sxm_func_code_panel);
    lv_obj_set_width(ui_sxm_func_code_cursor_3, lv_pct(10));   /// 1
    lv_obj_set_height(ui_sxm_func_code_cursor_3, LV_SIZE_CONTENT);    /// 1
    lv_obj_set_y(ui_sxm_func_code_cursor_3, 0);
    lv_obj_set_x(ui_sxm_func_code_cursor_3, lv_pct(10));
    lv_obj_set_align(ui_sxm_func_code_cursor_3, LV_ALIGN_CENTER);
    lv_label_set_text(ui_sxm_func_code_cursor_3, "-");
    lv_obj_set_style_text_color(ui_sxm_func_code_cursor_3, lv_color_hex(0xFFFFFF), LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_text_opa(ui_sxm_func_code_cursor_3, 255, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_text_align(ui_sxm_func_code_cursor_3, LV_TEXT_ALIGN_CENTER, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_text_font(ui_sxm_func_code_cursor_3, lv_font_xxxlarge.font, LV_PART_MAIN | LV_STATE_DEFAULT);

    ui_sxm_func_code_cursor_4 = lv_label_create(ui_sxm_func_code_panel);
    lv_obj_set_width(ui_sxm_func_code_cursor_4, lv_pct(10));   /// 1
    lv_obj_set_height(ui_sxm_func_code_cursor_4, LV_SIZE_CONTENT);    /// 1
    lv_obj_set_y(ui_sxm_func_code_cursor_4, 0);
    lv_obj_set_x(ui_sxm_func_code_cursor_4, lv_pct(30));
    lv_obj_set_align(ui_sxm_func_code_cursor_4, LV_ALIGN_CENTER);
    lv_label_set_text(ui_sxm_func_code_cursor_4, "-");
    lv_obj_set_style_text_color(ui_sxm_func_code_cursor_4, lv_color_hex(0xFFFFFF), LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_text_opa(ui_sxm_func_code_cursor_4, 255, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_text_align(ui_sxm_func_code_cursor_4, LV_TEXT_ALIGN_CENTER, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_text_font(ui_sxm_func_code_cursor_4, lv_font_xxxlarge.font, LV_PART_MAIN | LV_STATE_DEFAULT);

    ui_sxm_func_code_text = lv_label_create(ui_sxm_func_code_panel);
    lv_obj_set_width(ui_sxm_func_code_text, LV_SIZE_CONTENT);   /// 1
    lv_obj_set_height(ui_sxm_func_code_text, LV_SIZE_CONTENT);    /// 1
    lv_obj_set_x(ui_sxm_func_code_text, 0);
    lv_obj_set_y(ui_sxm_func_code_text, lv_pct(-15));
    lv_obj_set_align(ui_sxm_func_code_text, LV_ALIGN_BOTTOM_MID);
    lv_label_set_text(ui_sxm_func_code_text, " ");
    lv_obj_add_flag(ui_sxm_func_code_text, LV_OBJ_FLAG_IGNORE_LAYOUT);     /// Flags
    lv_obj_set_style_text_color(ui_sxm_func_code_text, lv_color_hex(0x808080), LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_text_opa(ui_sxm_func_code_text, 255, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_text_font(ui_sxm_func_code_text, lv_font_xsmall.font, LV_PART_MAIN | LV_STATE_DEFAULT);
#endif

    ui_sxm_func_code_keyboard = ui_keyboard_code_init(ui_sxm_func_code);
    lv_obj_add_flag(ui_sxm_func_code_keyboard, LV_OBJ_FLAG_HIDDEN);      /// Flags
    ui_sxm_func_code_textarea = lv_keyboard_get_textarea(ui_sxm_func_code_keyboard);

    /*ui_sxm_func_code_textarea = lv_textarea_create(ui_sxm_func_code);
    lv_obj_set_size(ui_sxm_func_code_textarea, lv_pct(100), LV_SIZE_CONTENT);
    lv_obj_add_state(ui_sxm_func_code_textarea, LV_STATE_FOCUSED);
    lv_obj_set_align(ui_sxm_func_code_textarea, LV_ALIGN_LEFT_MID);
    lv_obj_add_flag(ui_sxm_func_code_textarea, LV_OBJ_FLAG_HIDDEN);
    lv_textarea_set_one_line(ui_sxm_func_code_textarea, true);
    lv_obj_set_style_text_color(ui_sxm_func_code_textarea, lv_color_hex(0xFFFFFF), LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_text_opa(ui_sxm_func_code_textarea, 255, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_text_align(ui_sxm_func_code_textarea, LV_TEXT_ALIGN_AUTO, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_text_font(ui_sxm_func_code_textarea, lv_font_small.font, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_bg_color(ui_sxm_func_code_textarea, lv_color_hex(0xFFFFFF), LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_bg_opa(ui_sxm_func_code_textarea, 0, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_border_width(ui_sxm_func_code_textarea, 0, LV_PART_MAIN | LV_STATE_DEFAULT);
    
    lv_keyboard_set_textarea(ui_sxm_func_code_keyboard, ui_sxm_func_code_textarea);*/

    lv_obj_add_event_cb(ui_sxm_func_code_keyboard, ui_event_sxm_func_kb_cb, LV_EVENT_ALL, sxm_para);//LV_EVENT_VALUE_CHANGED
    //lv_obj_add_event_cb(ui_sxm_func_code_textarea, ui_event_sxm_func_textarea_cb, LV_EVENT_VALUE_CHANGED, sxm_para);

    ui_source_sub_open(sxm_para->source_para);
}

static void ui_sxm_func_code_screen_uninit(ui_sxm_para_t * sxm_para)
{	
    ui_sxm_item_group_update(sxm_para, 1);

    lv_obj_add_flag(ui_sxm_func_code_keyboard, LV_OBJ_FLAG_HIDDEN);      /// Flags
    if(sxm_para->win_type < SXM_WIN_PARENTAL_CTRL)
        ui_source_sub_close(sxm_para->source_para);
#if 0
    lv_label_set_text(ui_sxm_func_code_cursor_1, "-");
    lv_label_set_text(ui_sxm_func_code_cursor_2, "-");
    lv_label_set_text(ui_sxm_func_code_cursor_3, "-");
    lv_label_set_text(ui_sxm_func_code_cursor_4, "-");
    lv_label_set_text(ui_sxm_func_code_text, " ");
#endif
	lv_obj_add_flag(ui_sxm_func_code, LV_OBJ_FLAG_HIDDEN);
    eLIBs_memset(sxm_para->parental_code_save, 0, sizeof(sxm_para->parental_code_save));
    if(sxm_para->code_kb_type == CODE_KEYBOARD_DIRECT_TUNING)
    {
        ui_sxm_bottom_group_update(sxm_para, 1, 0, 0);
    }

    sxm_para->change_index = 0;
    sxm_para->cursor_index = 0;
    sxm_para->cmp_error_flag = 0;
#if 0
    ui_canvas_blur_uninit(ui_sxm_func_code_blur);
    ui_sxm_func_code_blur = NULL;
#endif
    sxm_para->code_kb_type = CODE_KEYBOARD_NONE;
}

static void ui_sxm_func_screen_init(ui_sxm_para_t * sxm_para)
{    
    if(ui_sxm_func_menu)
    {
        lv_obj_remove_flag(ui_sxm_func_menu, LV_OBJ_FLAG_HIDDEN);     /// Flags
        return;
    }

    ui_sxm_func_menu = lv_obj_create(sxm_para->source_para->ui_menu_panel);//sxm_para->source_para->ui_rig_func_panel
    lv_obj_remove_style_all(ui_sxm_func_menu);
    lv_obj_set_width(ui_sxm_func_menu, lv_pct(100));
    lv_obj_set_height(ui_sxm_func_menu, lv_pct(100));
    //lv_obj_add_flag(ui_sxm_func_menu, LV_OBJ_FLAG_HIDDEN);     /// Flags
    lv_obj_clear_flag(ui_sxm_func_menu, LV_OBJ_FLAG_SCROLLABLE);      /// Flags
    lv_obj_set_style_bg_color(ui_sxm_func_menu, lv_color_hex(0x000000), LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_bg_opa(ui_sxm_func_menu, 255, LV_PART_MAIN | LV_STATE_DEFAULT);
}

static void ui_sxm_func_screen_uninit(ui_sxm_para_t * sxm_para)
{		
	lv_obj_add_flag(ui_sxm_func_menu, LV_OBJ_FLAG_HIDDEN);
}

static void ui_sxm_direct_tuning_screen_init(ui_sxm_para_t * sxm_para)
{    
    /*if(ui_sxm_direct_tuning)
    {
        lv_obj_remove_flag(ui_sxm_direct_tuning, LV_OBJ_FLAG_HIDDEN);     /// Flags
        return;
    }
    
    ui_sxm_direct_tuning = lv_obj_create(sxm_para->source_para->ui_menu_panel);
    lv_obj_set_width(ui_sxm_direct_tuning, lv_pct(100));
    lv_obj_set_height(ui_sxm_direct_tuning, lv_pct(100));
    lv_obj_set_align(ui_sxm_direct_tuning, LV_ALIGN_BOTTOM_MID);
    lv_obj_set_flex_flow(ui_sxm_direct_tuning, LV_FLEX_FLOW_COLUMN);
    lv_obj_set_flex_align(ui_sxm_direct_tuning, LV_FLEX_ALIGN_SPACE_EVENLY, LV_FLEX_ALIGN_START, LV_FLEX_ALIGN_START);
    lv_obj_remove_flag(ui_sxm_direct_tuning, LV_OBJ_FLAG_SCROLLABLE);      /// Flags
    lv_obj_set_style_bg_color(ui_sxm_direct_tuning, lv_color_hex(0x000000), LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_bg_opa(ui_sxm_direct_tuning, 255, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_border_width(ui_sxm_direct_tuning, 0, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_pad_left(ui_sxm_direct_tuning, 0, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_pad_right(ui_sxm_direct_tuning, 0, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_pad_top(ui_sxm_direct_tuning, 0, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_pad_bottom(ui_sxm_direct_tuning, 20, LV_PART_MAIN | LV_STATE_DEFAULT);*/

}

static void ui_sxm_direct_tuning_screen_uninit(ui_sxm_para_t * sxm_para)
{		
	//lv_obj_add_flag(ui_sxm_direct_tuning, LV_OBJ_FLAG_HIDDEN);
}

static void ui_sxm_parental_ctrl_code_screen_init(ui_sxm_para_t * sxm_para)
{    
    if(ui_sxm_parental_ctrl_code)
    {
        //lv_obj_remove_flag(ui_sxm_parental_ctrl_code, LV_OBJ_FLAG_HIDDEN);     /// Flags
        return;
    }
    
    ui_sxm_parental_ctrl_code = lv_obj_create(sxm_para->source_para->ui_menu_panel);
    lv_obj_set_width(ui_sxm_parental_ctrl_code, lv_pct(100));
    lv_obj_set_height(ui_sxm_parental_ctrl_code, lv_pct(100));
    lv_obj_set_align(ui_sxm_parental_ctrl_code, LV_ALIGN_BOTTOM_MID);
	lv_obj_add_flag(ui_sxm_parental_ctrl_code, LV_OBJ_FLAG_HIDDEN);
    lv_obj_remove_flag(ui_sxm_parental_ctrl_code, LV_OBJ_FLAG_SCROLLABLE);      /// Flags
    lv_obj_set_style_bg_color(ui_sxm_parental_ctrl_code, lv_color_hex(0x000000), LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_bg_opa(ui_sxm_parental_ctrl_code, 220, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_border_width(ui_sxm_parental_ctrl_code, 0, LV_PART_MAIN | LV_STATE_DEFAULT);

    ui_sxm_parental_ctrl_code_label_1 = lv_label_create(ui_sxm_parental_ctrl_code);
    lv_obj_set_width(ui_sxm_parental_ctrl_code_label_1, LV_SIZE_CONTENT);   /// 1
    lv_obj_set_height(ui_sxm_parental_ctrl_code_label_1, LV_SIZE_CONTENT);    /// 1
    lv_obj_set_align(ui_sxm_parental_ctrl_code_label_1, LV_ALIGN_CENTER);
    lv_label_set_text(ui_sxm_parental_ctrl_code_label_1, "Your Parental code is:");
    lv_obj_set_style_text_color(ui_sxm_parental_ctrl_code_label_1, lv_color_hex(0x808080), LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_text_opa(ui_sxm_parental_ctrl_code_label_1, 255, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_text_font(ui_sxm_parental_ctrl_code_label_1, lv_font_xsmall.font,
                               LV_PART_MAIN | LV_STATE_DEFAULT);

    ui_sxm_parental_ctrl_code_label_2 = lv_label_create(ui_sxm_parental_ctrl_code);
    lv_obj_set_width(ui_sxm_parental_ctrl_code_label_2, LV_SIZE_CONTENT);   /// 1
    lv_obj_set_height(ui_sxm_parental_ctrl_code_label_2, LV_SIZE_CONTENT);    /// 1
    lv_obj_set_align(ui_sxm_parental_ctrl_code_label_2, LV_ALIGN_CENTER);
    lv_label_set_text(ui_sxm_parental_ctrl_code_label_2, "1000");
    lv_obj_set_style_text_color(ui_sxm_parental_ctrl_code_label_2, lv_color_hex(0xFFFFFF), LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_text_opa(ui_sxm_parental_ctrl_code_label_2, 255, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_text_font(ui_sxm_parental_ctrl_code_label_2, lv_font_xxxlarge_b.font, LV_PART_MAIN | LV_STATE_DEFAULT);

    ui_sxm_parental_ctrl_code_label_3 = lv_label_create(ui_sxm_parental_ctrl_code);
    lv_obj_set_width(ui_sxm_parental_ctrl_code_label_3, lv_pct(70));
    lv_obj_set_height(ui_sxm_parental_ctrl_code_label_3, LV_SIZE_CONTENT);    /// 1
    lv_obj_set_align(ui_sxm_parental_ctrl_code_label_3, LV_ALIGN_CENTER);
    lv_label_set_text(ui_sxm_parental_ctrl_code_label_3,
                      "You will need this code to change or turn off Parental Controls.");
    lv_obj_set_style_text_color(ui_sxm_parental_ctrl_code_label_3, lv_color_hex(0x808080), LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_text_opa(ui_sxm_parental_ctrl_code_label_3, 255, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_text_letter_space(ui_sxm_parental_ctrl_code_label_3, 0, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_text_line_space(ui_sxm_parental_ctrl_code_label_3, 15, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_text_align(ui_sxm_parental_ctrl_code_label_3, LV_TEXT_ALIGN_CENTER, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_text_font(ui_sxm_parental_ctrl_code_label_3, lv_font_xsmall.font,
                               LV_PART_MAIN | LV_STATE_DEFAULT);
}

static void ui_sxm_parental_ctrl_code_screen_uninit(ui_sxm_para_t * sxm_para)
{		
	lv_obj_add_flag(ui_sxm_parental_ctrl_code, LV_OBJ_FLAG_HIDDEN);
}

static void ui_sxm_parental_ctrl_lock_channel_screen_init(ui_sxm_para_t * sxm_para)
{
    if(ui_sxm_parental_ctrl_lock_channel)
    {
        //lv_obj_remove_flag(ui_sxm_parental_ctrl_lock_channel, LV_OBJ_FLAG_HIDDEN);     /// Flags
        return;
    }

    ui_sxm_parental_ctrl_lock_channel = lv_obj_create(sxm_para->source_para->ui_menu_panel);
    lv_obj_set_width(ui_sxm_parental_ctrl_lock_channel, lv_pct(100));
    lv_obj_set_height(ui_sxm_parental_ctrl_lock_channel, lv_pct(100));
    lv_obj_set_align(ui_sxm_parental_ctrl_lock_channel, LV_ALIGN_BOTTOM_MID);
	lv_obj_add_flag(ui_sxm_parental_ctrl_lock_channel, LV_OBJ_FLAG_HIDDEN);
    lv_obj_remove_flag(ui_sxm_parental_ctrl_lock_channel, LV_OBJ_FLAG_SCROLLABLE);      /// Flags
    lv_obj_set_style_bg_color(ui_sxm_parental_ctrl_lock_channel, lv_color_hex(0x000000), LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_bg_opa(ui_sxm_parental_ctrl_lock_channel, 220, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_border_width(ui_sxm_parental_ctrl_lock_channel, 0, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_pad_left(ui_sxm_parental_ctrl_lock_channel, 0, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_pad_right(ui_sxm_parental_ctrl_lock_channel, 0, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_pad_top(ui_sxm_parental_ctrl_lock_channel, 0, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_pad_bottom(ui_sxm_parental_ctrl_lock_channel, 0, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_pad_row(ui_sxm_parental_ctrl_lock_channel, 0, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_pad_column(ui_sxm_parental_ctrl_lock_channel, 0, LV_PART_MAIN | LV_STATE_DEFAULT);

}

static void ui_sxm_parental_ctrl_lock_channel_screen_uninit(ui_sxm_para_t * sxm_para)
{		
	lv_obj_add_flag(ui_sxm_parental_ctrl_lock_channel, LV_OBJ_FLAG_HIDDEN);
}

static void ui_sxm_parental_ctrl_change_code_screen_init(ui_sxm_para_t * sxm_para)
{    
    if(ui_sxm_parental_ctrl_change_code)
    {
        //lv_obj_remove_flag(ui_sxm_parental_ctrl_change_code, LV_OBJ_FLAG_HIDDEN);     /// Flags
        return;
    }

    ui_sxm_parental_ctrl_change_code = lv_obj_create(sxm_para->source_para->ui_menu_panel);
    lv_obj_set_width(ui_sxm_parental_ctrl_change_code, lv_pct(100));
    lv_obj_set_height(ui_sxm_parental_ctrl_change_code, lv_pct(100));
    lv_obj_set_align(ui_sxm_parental_ctrl_change_code, LV_ALIGN_BOTTOM_MID);
	lv_obj_add_flag(ui_sxm_parental_ctrl_change_code, LV_OBJ_FLAG_HIDDEN);
    lv_obj_remove_flag(ui_sxm_parental_ctrl_change_code, LV_OBJ_FLAG_SCROLLABLE);      /// Flags
    lv_obj_set_style_bg_color(ui_sxm_parental_ctrl_change_code, lv_color_hex(0x000000), LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_bg_opa(ui_sxm_parental_ctrl_change_code, 220, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_border_width(ui_sxm_parental_ctrl_change_code, 0, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_pad_left(ui_sxm_parental_ctrl_change_code, 0, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_pad_right(ui_sxm_parental_ctrl_change_code, 0, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_pad_top(ui_sxm_parental_ctrl_change_code, 0, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_pad_bottom(ui_sxm_parental_ctrl_change_code, 0, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_pad_row(ui_sxm_parental_ctrl_change_code, 0, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_pad_column(ui_sxm_parental_ctrl_change_code, 0, LV_PART_MAIN | LV_STATE_DEFAULT);

}

static void ui_sxm_parental_ctrl_change_code_screen_uninit(ui_sxm_para_t * sxm_para)
{		
	lv_obj_add_flag(ui_sxm_parental_ctrl_change_code, LV_OBJ_FLAG_HIDDEN);
}

static void ui_sxm_screen_init(ui_sxm_para_t * sxm_para)
{
    ui_sxm = lv_obj_create(sxm_para->source_para->ui_play);
	lv_obj_remove_style_all(ui_sxm);
    lv_obj_set_width(ui_sxm, lv_pct(100));
    lv_obj_set_height(ui_sxm, lv_pct(100));
    lv_obj_set_align(ui_sxm, LV_ALIGN_CENTER);
    lv_obj_clear_flag(ui_sxm, LV_OBJ_FLAG_CLICKABLE | LV_OBJ_FLAG_SCROLLABLE);      /// Flags
    lv_obj_set_style_bg_opa(ui_sxm, 0, LV_PART_MAIN | LV_STATE_DEFAULT);
    eLIBs_printf("ui_sxm=%p\n", ui_sxm);

    ui_sxm_play = lv_obj_create(ui_sxm);
	lv_obj_remove_style_all(ui_sxm_play);
    lv_obj_set_width(ui_sxm_play, lv_pct(100));
    lv_obj_set_height(ui_sxm_play, lv_pct(100));
    lv_obj_remove_flag(ui_sxm_play, LV_OBJ_FLAG_SCROLLABLE);      /// Flags
    lv_obj_set_flex_flow(ui_sxm_play, LV_FLEX_FLOW_COLUMN);
    lv_obj_set_flex_align(ui_sxm_play, LV_FLEX_ALIGN_CENTER, LV_FLEX_ALIGN_START, LV_FLEX_ALIGN_START);
    lv_obj_set_style_bg_color(ui_sxm_play, lv_color_hex(0x000000), LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_bg_opa(ui_sxm_play, 0, LV_PART_MAIN | LV_STATE_DEFAULT);

    ui_sxm_play_top = lv_obj_create(ui_sxm_play);
    lv_obj_set_width(ui_sxm_play_top, lv_pct(88));
    lv_obj_set_height(ui_sxm_play_top, lv_pct(20));
    lv_obj_set_flex_flow(ui_sxm_play_top, LV_FLEX_FLOW_ROW);
    lv_obj_set_flex_align(ui_sxm_play_top, LV_FLEX_ALIGN_END, LV_FLEX_ALIGN_CENTER, LV_FLEX_ALIGN_CENTER);
    lv_obj_remove_flag(ui_sxm_play_top, LV_OBJ_FLAG_SCROLLABLE);      /// Flags
    lv_obj_set_style_radius(ui_sxm_play_top, 0, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_bg_color(ui_sxm_play_top, lv_color_hex(0xFFFFFF), LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_bg_opa(ui_sxm_play_top, 0, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_border_width(ui_sxm_play_top, 0, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_pad_left(ui_sxm_play_top, 0, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_pad_right(ui_sxm_play_top, 0, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_pad_top(ui_sxm_play_top, 0, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_pad_bottom(ui_sxm_play_top, 0, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_pad_row(ui_sxm_play_top, 0, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_pad_column(ui_sxm_play_top, 12, LV_PART_MAIN | LV_STATE_DEFAULT);

    ui_sxm_play_top_ch_num = lv_obj_create(ui_sxm_play_top);
    lv_obj_set_width(ui_sxm_play_top_ch_num, LV_SIZE_CONTENT);   /// 100
    lv_obj_set_height(ui_sxm_play_top_ch_num, LV_SIZE_CONTENT);    /// 50
    lv_obj_set_align(ui_sxm_play_top_ch_num, LV_ALIGN_CENTER);
    lv_obj_add_flag(ui_sxm_play_top_ch_num, LV_OBJ_FLAG_HIDDEN);
    lv_obj_set_flex_flow(ui_sxm_play_top_ch_num, LV_FLEX_FLOW_ROW);
    lv_obj_set_flex_align(ui_sxm_play_top_ch_num, LV_FLEX_ALIGN_SPACE_AROUND, LV_FLEX_ALIGN_CENTER, LV_FLEX_ALIGN_START);
    lv_obj_remove_flag(ui_sxm_play_top_ch_num, LV_OBJ_FLAG_SCROLLABLE);      /// Flags
    lv_obj_set_style_radius(ui_sxm_play_top_ch_num, 0, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_bg_color(ui_sxm_play_top_ch_num, lv_color_hex(0xFFFFFF), LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_bg_opa(ui_sxm_play_top_ch_num, 0, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_border_width(ui_sxm_play_top_ch_num, 0, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_pad_left(ui_sxm_play_top_ch_num, 0, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_pad_right(ui_sxm_play_top_ch_num, 0, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_pad_top(ui_sxm_play_top_ch_num, 0, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_pad_bottom(ui_sxm_play_top_ch_num, 0, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_pad_row(ui_sxm_play_top_ch_num, 0, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_pad_column(ui_sxm_play_top_ch_num, 0, LV_PART_MAIN | LV_STATE_DEFAULT);

    ui_sxm_play_top_ch = lv_label_create(ui_sxm_play_top_ch_num);
    lv_obj_set_width(ui_sxm_play_top_ch, LV_SIZE_CONTENT);   /// 1
    lv_obj_set_height(ui_sxm_play_top_ch, LV_SIZE_CONTENT);    /// 1
    lv_obj_set_align(ui_sxm_play_top_ch, LV_ALIGN_CENTER);
    lv_label_set_text(ui_sxm_play_top_ch, "CH");
    lv_obj_set_style_text_color(ui_sxm_play_top_ch, lv_color_hex(0x808080), LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_text_opa(ui_sxm_play_top_ch, 255, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_text_font(ui_sxm_play_top_ch, lv_font_xxsmall.font, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_radius(ui_sxm_play_top_ch, 6, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_bg_color(ui_sxm_play_top_ch, lv_color_hex(0x808080), LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_bg_opa(ui_sxm_play_top_ch, 0, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_border_color(ui_sxm_play_top_ch, lv_color_hex(0x808080), LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_border_opa(ui_sxm_play_top_ch, 255, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_border_width(ui_sxm_play_top_ch, 2, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_pad_left(ui_sxm_play_top_ch, 3, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_pad_right(ui_sxm_play_top_ch, 3, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_pad_top(ui_sxm_play_top_ch, 3, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_pad_bottom(ui_sxm_play_top_ch, 0, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_pad_row(ui_sxm_play_top_ch, 0, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_pad_column(ui_sxm_play_top_ch, 0, LV_PART_MAIN | LV_STATE_DEFAULT);

    ui_sxm_play_top_num = lv_label_create(ui_sxm_play_top_ch_num);
    lv_obj_set_width(ui_sxm_play_top_num, LV_SIZE_CONTENT);   /// 1
    lv_obj_set_height(ui_sxm_play_top_num, LV_SIZE_CONTENT);    /// 1
    lv_obj_set_align(ui_sxm_play_top_num, LV_ALIGN_CENTER);
    lv_label_set_text(ui_sxm_play_top_num, " ");
    lv_obj_set_style_text_color(ui_sxm_play_top_num, lv_color_hex(0x808080), LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_text_opa(ui_sxm_play_top_num, 255, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_text_font(ui_sxm_play_top_num, lv_font_xxsmall.font, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_pad_left(ui_sxm_play_top_num, 3, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_pad_right(ui_sxm_play_top_num, 3, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_pad_top(ui_sxm_play_top_num, 3, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_pad_bottom(ui_sxm_play_top_num, 0, LV_PART_MAIN | LV_STATE_DEFAULT);

    ui_sxm_play_top_presets = lv_label_create(ui_sxm_play_top);
    lv_obj_set_width(ui_sxm_play_top_presets, LV_SIZE_CONTENT);   /// 1
    lv_obj_set_height(ui_sxm_play_top_presets, LV_SIZE_CONTENT);    /// 1
    lv_obj_set_align(ui_sxm_play_top_presets, LV_ALIGN_CENTER);
    lv_obj_add_flag(ui_sxm_play_top_presets, LV_OBJ_FLAG_HIDDEN);
    lv_label_set_text(ui_sxm_play_top_presets, " ");
    lv_obj_set_style_text_color(ui_sxm_play_top_presets, lv_color_hex(0x000000), LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_text_opa(ui_sxm_play_top_presets, 255, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_text_font(ui_sxm_play_top_presets, lv_font_xxsmall.font, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_radius(ui_sxm_play_top_presets, 6, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_bg_color(ui_sxm_play_top_presets, lv_color_hex(0x808080), LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_bg_opa(ui_sxm_play_top_presets, 255, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_pad_left(ui_sxm_play_top_presets, 3, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_pad_right(ui_sxm_play_top_presets, 3, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_pad_top(ui_sxm_play_top_presets, 3, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_pad_bottom(ui_sxm_play_top_presets, 0, LV_PART_MAIN | LV_STATE_DEFAULT);

    ui_sxm_play_top_category = lv_label_create(ui_sxm_play_top);
    lv_obj_set_width(ui_sxm_play_top_category, LV_SIZE_CONTENT);   /// 1
    lv_obj_set_height(ui_sxm_play_top_category, LV_SIZE_CONTENT);    /// 1
    lv_obj_set_align(ui_sxm_play_top_category, LV_ALIGN_CENTER);
    lv_label_set_text(ui_sxm_play_top_category, " ");
    lv_obj_set_style_text_color(ui_sxm_play_top_category, lv_color_hex(0xA7A7A7), LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_text_opa(ui_sxm_play_top_category, 255, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_text_font(ui_sxm_play_top_category, lv_font_xxsmall.font, LV_PART_MAIN | LV_STATE_DEFAULT);

    ui_sxm_play_top_low = lv_image_create(ui_sxm_play_top);
    lv_image_set_src(ui_sxm_play_top_low, ui_sxm_get_res(sxm_para, SXM_THMEM_TEMP_COM_LOW_VOLTAGE_BMP));
    lv_obj_set_width(ui_sxm_play_top_low, LV_SIZE_CONTENT);   /// 1
    lv_obj_set_height(ui_sxm_play_top_low, LV_SIZE_CONTENT);    /// 1
    lv_obj_set_x(ui_sxm_play_top_low, -93);
    lv_obj_set_y(ui_sxm_play_top_low, 1);
    lv_obj_set_align(ui_sxm_play_top_low, LV_ALIGN_RIGHT_MID);
    lv_obj_add_flag(ui_sxm_play_top_low, LV_OBJ_FLAG_HIDDEN | LV_OBJ_FLAG_CLICKABLE);     /// Flags
    lv_obj_remove_flag(ui_sxm_play_top_low, LV_OBJ_FLAG_SCROLLABLE);      /// Flags

    ui_sxm_play_top_charge = lv_image_create(ui_sxm_play_top);
    lv_image_set_src(ui_sxm_play_top_charge, ui_sxm_get_res(sxm_para, SXM_THMEM_TEMP_COM_PD_CHARGE_BMP));
    lv_obj_set_width(ui_sxm_play_top_charge, LV_SIZE_CONTENT);   /// 1
    lv_obj_set_height(ui_sxm_play_top_charge, LV_SIZE_CONTENT);    /// 1
    lv_obj_set_align(ui_sxm_play_top_charge, LV_ALIGN_RIGHT_MID);
    lv_obj_add_flag(ui_sxm_play_top_charge, LV_OBJ_FLAG_HIDDEN | LV_OBJ_FLAG_CLICKABLE);     /// Flags
    lv_obj_remove_flag(ui_sxm_play_top_charge, LV_OBJ_FLAG_SCROLLABLE);      /// Flags

    ui_sxm_play_top_eq = lv_image_create(ui_sxm_play_top);
    lv_image_set_src(ui_sxm_play_top_eq, ui_sxm_get_res(sxm_para, SXM_THMEM_TEMP_COM_PUNCH_EQ_BMP));
    lv_obj_set_width(ui_sxm_play_top_eq, LV_SIZE_CONTENT);   /// 1
    lv_obj_set_height(ui_sxm_play_top_eq, LV_SIZE_CONTENT);    /// 1
    lv_obj_set_align(ui_sxm_play_top_eq, LV_ALIGN_RIGHT_MID);
    lv_obj_add_flag(ui_sxm_play_top_eq, LV_OBJ_FLAG_CLICKABLE);     /// Flags
    lv_obj_remove_flag(ui_sxm_play_top_eq, LV_OBJ_FLAG_SCROLLABLE);      /// Flags

    ui_sxm_play_mid = lv_obj_create(ui_sxm_play);
    lv_obj_set_width(ui_sxm_play_mid, lv_pct(92));
    lv_obj_set_height(ui_sxm_play_mid, lv_pct(60));
    lv_obj_set_x(ui_sxm_play_mid, lv_pct(-86));
    lv_obj_set_y(ui_sxm_play_mid, lv_pct(0));
    lv_obj_set_flex_flow(ui_sxm_play_mid, LV_FLEX_FLOW_ROW);
    lv_obj_set_flex_align(ui_sxm_play_mid, LV_FLEX_ALIGN_START, LV_FLEX_ALIGN_CENTER, LV_FLEX_ALIGN_CENTER);
    lv_obj_remove_flag(ui_sxm_play_mid, LV_OBJ_FLAG_CLICKABLE | LV_OBJ_FLAG_SCROLLABLE);      /// Flags
    lv_obj_set_style_radius(ui_sxm_play_mid, 0, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_bg_color(ui_sxm_play_mid, lv_color_hex(0xB54444), LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_bg_opa(ui_sxm_play_mid, 0, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_border_width(ui_sxm_play_mid, 0, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_pad_left(ui_sxm_play_mid, 0, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_pad_right(ui_sxm_play_mid, 0, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_pad_top(ui_sxm_play_mid, 0, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_pad_bottom(ui_sxm_play_mid, 0, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_pad_row(ui_sxm_play_mid, 0, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_pad_column(ui_sxm_play_mid, 0, LV_PART_MAIN | LV_STATE_DEFAULT);

    ui_sxm_play_logo_panel = lv_obj_create(ui_sxm_play_mid);
    lv_obj_set_width(ui_sxm_play_logo_panel, lv_pct(40));
    lv_obj_set_height(ui_sxm_play_logo_panel, lv_pct(100));
    lv_obj_set_align(ui_sxm_play_logo_panel, LV_ALIGN_CENTER);
    lv_obj_remove_flag(ui_sxm_play_logo_panel, LV_OBJ_FLAG_SCROLLABLE);      /// Flags
    lv_obj_set_style_radius(ui_sxm_play_logo_panel, 0, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_bg_color(ui_sxm_play_logo_panel, lv_color_hex(0xFFFFFF), LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_bg_opa(ui_sxm_play_logo_panel, 0, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_border_width(ui_sxm_play_logo_panel, 0, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_pad_left(ui_sxm_play_logo_panel, 0, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_pad_right(ui_sxm_play_logo_panel, 0, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_pad_top(ui_sxm_play_logo_panel, 0, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_pad_bottom(ui_sxm_play_logo_panel, 0, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_pad_row(ui_sxm_play_logo_panel, 0, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_pad_column(ui_sxm_play_logo_panel, 0, LV_PART_MAIN | LV_STATE_DEFAULT);

    ui_sxm_play_logo = lv_image_create(ui_sxm_play_logo_panel);
    lv_img_set_src(ui_sxm_play_logo, ui_sxm_get_res(sxm_para, SXM_THMEM_SXM_ICON_BMP));
    lv_obj_set_width(ui_sxm_play_logo, LV_SIZE_CONTENT);   /// 214
    lv_obj_set_height(ui_sxm_play_logo, LV_SIZE_CONTENT);    /// 214
    lv_obj_set_x(ui_sxm_play_logo, lv_pct(2));
    lv_obj_set_y(ui_sxm_play_logo, lv_pct(0));
    lv_obj_set_align(ui_sxm_play_logo, LV_ALIGN_CENTER);
    lv_obj_add_flag(ui_sxm_play_logo, LV_OBJ_FLAG_ADV_HITTEST);     /// Flags
    lv_obj_clear_flag(ui_sxm_play_logo, LV_OBJ_FLAG_SCROLLABLE);      /// Flags

    ui_sxm_play_logo_text = lv_label_create(ui_sxm_play_logo);
    lv_obj_set_width(ui_sxm_play_logo_text, LV_SIZE_CONTENT);   /// 1
    lv_obj_set_height(ui_sxm_play_logo_text, LV_SIZE_CONTENT);    /// 1
    lv_obj_set_y(ui_sxm_play_logo_text, lv_pct(-10));
    lv_obj_set_align(ui_sxm_play_logo_text, LV_ALIGN_BOTTOM_MID);
    //lv_obj_add_flag(ui_sxm_play_logo_text, LV_OBJ_FLAG_HIDDEN);     /// Flags
    lv_label_set_text(ui_sxm_play_logo_text, "SiriusXM");
    lv_obj_set_style_text_color(ui_sxm_play_logo_text, lv_color_hex(0xFFFFFF), LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_text_opa(ui_sxm_play_logo_text, 255, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_text_font(ui_sxm_play_logo_text, lv_font_small.font, LV_PART_MAIN | LV_STATE_DEFAULT);//&ui_font_Archivo_56

    ui_sxm_logo_panel = lv_obj_create(ui_sxm_play_logo_panel);
    lv_obj_remove_style_all(ui_sxm_logo_panel);
	lv_obj_set_width(ui_sxm_logo_panel, 186);   /// 214 //220
	lv_obj_set_height(ui_sxm_logo_panel, 186);	 /// 214 //220
	lv_obj_set_x(ui_sxm_logo_panel, lv_pct(1));
	lv_obj_set_y(ui_sxm_logo_panel, lv_pct(0));
	lv_obj_set_align(ui_sxm_logo_panel, LV_ALIGN_LEFT_MID);
    lv_obj_remove_flag(ui_sxm_logo_panel, LV_OBJ_FLAG_CLICKABLE | LV_OBJ_FLAG_SCROLLABLE);      /// Flags
	lv_obj_set_style_radius(ui_sxm_logo_panel, 20, LV_PART_MAIN | LV_STATE_DEFAULT);//LV_RADIUS_CIRCLE
	lv_obj_set_style_clip_corner(ui_sxm_logo_panel, true, 0);
    lv_obj_set_style_bg_color(ui_sxm_logo_panel, lv_color_hex(0xFFFFFF), LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_bg_opa(ui_sxm_logo_panel, 0, LV_PART_MAIN | LV_STATE_DEFAULT);

	ui_sxm_logo = lv_img_create(ui_sxm_logo_panel);
	lv_obj_set_width(ui_sxm_logo, lv_pct(100));   /// 214 //220
	lv_obj_set_height(ui_sxm_logo, lv_pct(100));	 /// 214 //220
	//lv_obj_set_x(ui_sxm_logo, lv_pct(1));
	//lv_obj_set_y(ui_sxm_logo, lv_pct(0));
	//lv_obj_set_align(ui_sxm_logo, LV_ALIGN_LEFT_MID);
	lv_obj_set_align(ui_sxm_logo, LV_ALIGN_CENTER);
	//lv_obj_set_style_radius(ui_sxm_logo, 20, LV_PART_MAIN | LV_STATE_DEFAULT);//LV_RADIUS_CIRCLE
	//lv_obj_set_style_clip_corner(ui_sxm_logo, true, 0);
	//lv_obj_add_flag(ui_sxm_logo, LV_OBJ_FLAG_HIDDEN); 	/// Flags
	lv_obj_clear_flag(ui_sxm_logo, LV_OBJ_FLAG_SCROLLABLE);	  /// Flags

    ui_sxm_play_disp = lv_obj_create(ui_sxm_play_mid);
    lv_obj_set_width(ui_sxm_play_disp, lv_pct(60));
    lv_obj_set_height(ui_sxm_play_disp, lv_pct(90));
    lv_obj_set_align(ui_sxm_play_disp, LV_ALIGN_RIGHT_MID);
    lv_obj_remove_flag(ui_sxm_play_disp, LV_OBJ_FLAG_SCROLLABLE);      /// Flags
    lv_obj_set_style_radius(ui_sxm_play_disp, 0, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_bg_color(ui_sxm_play_disp, lv_color_hex(0x000000), LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_bg_opa(ui_sxm_play_disp, 0, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_border_width(ui_sxm_play_disp, 0, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_pad_left(ui_sxm_play_disp, 0, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_pad_right(ui_sxm_play_disp, 0, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_pad_top(ui_sxm_play_disp, 0, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_pad_bottom(ui_sxm_play_disp, 0, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_pad_row(ui_sxm_play_disp, 0, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_pad_column(ui_sxm_play_disp, 0, LV_PART_MAIN | LV_STATE_DEFAULT);

    ui_sxm_play_ind = lv_obj_create(ui_sxm_play_disp);
    lv_obj_set_width(ui_sxm_play_ind, lv_pct(100));
    lv_obj_set_height(ui_sxm_play_ind, lv_pct(100));
    lv_obj_set_align(ui_sxm_play_ind, LV_ALIGN_RIGHT_MID);
    lv_obj_remove_flag(ui_sxm_play_ind, LV_OBJ_FLAG_SCROLLABLE);      /// Flags
    lv_obj_set_style_radius(ui_sxm_play_ind, 0, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_bg_color(ui_sxm_play_ind, lv_color_hex(0x000000), LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_bg_opa(ui_sxm_play_ind, 0, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_border_width(ui_sxm_play_ind, 0, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_pad_left(ui_sxm_play_ind, 0, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_pad_right(ui_sxm_play_ind, 0, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_pad_top(ui_sxm_play_ind, 0, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_pad_bottom(ui_sxm_play_ind, 0, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_pad_row(ui_sxm_play_ind, 0, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_pad_column(ui_sxm_play_ind, 0, LV_PART_MAIN | LV_STATE_DEFAULT);

	ui_sxm_play_ind_text = lv_obj_create(ui_sxm_play_ind);
	lv_obj_set_width(ui_sxm_play_ind_text, lv_pct(100));
	lv_obj_set_height(ui_sxm_play_ind_text, lv_pct(90));
	lv_obj_clear_flag(ui_sxm_play_ind_text, LV_OBJ_FLAG_SCROLLABLE); 	 /// Flags
	lv_obj_set_style_radius(ui_sxm_play_ind_text, 0, LV_PART_MAIN | LV_STATE_DEFAULT);
	lv_obj_set_style_bg_color(ui_sxm_play_ind_text, lv_color_hex(0x000000), LV_PART_MAIN | LV_STATE_DEFAULT);
	lv_obj_set_style_bg_opa(ui_sxm_play_ind_text, 0, LV_PART_MAIN | LV_STATE_DEFAULT);
	lv_obj_set_style_border_width(ui_sxm_play_ind_text, 0, LV_PART_MAIN | LV_STATE_DEFAULT);
	lv_obj_set_style_pad_left(ui_sxm_play_ind_text, 0, LV_PART_MAIN | LV_STATE_DEFAULT);
	lv_obj_set_style_pad_right(ui_sxm_play_ind_text, 0, LV_PART_MAIN | LV_STATE_DEFAULT);
	lv_obj_set_style_pad_top(ui_sxm_play_ind_text, 0, LV_PART_MAIN | LV_STATE_DEFAULT);
	lv_obj_set_style_pad_bottom(ui_sxm_play_ind_text, 0, LV_PART_MAIN | LV_STATE_DEFAULT);
	lv_obj_set_style_pad_row(ui_sxm_play_ind_text, 0, LV_PART_MAIN | LV_STATE_DEFAULT);
	lv_obj_set_style_pad_column(ui_sxm_play_ind_text, 0, LV_PART_MAIN | LV_STATE_DEFAULT);

	ui_sxm_play_ind_text_panel = lv_obj_create(ui_sxm_play_ind_text);
	lv_obj_set_width(ui_sxm_play_ind_text_panel, lv_pct(100));
	lv_obj_set_height(ui_sxm_play_ind_text_panel, lv_pct(100));
	lv_obj_set_flex_flow(ui_sxm_play_ind_text_panel, LV_FLEX_FLOW_COLUMN);
	lv_obj_set_flex_align(ui_sxm_play_ind_text_panel, LV_FLEX_ALIGN_SPACE_BETWEEN, LV_FLEX_ALIGN_CENTER, LV_FLEX_ALIGN_CENTER);
	lv_obj_clear_flag(ui_sxm_play_ind_text_panel, LV_OBJ_FLAG_SCROLLABLE);	   /// Flags
	lv_obj_set_style_radius(ui_sxm_play_ind_text_panel, 0, LV_PART_MAIN | LV_STATE_DEFAULT);
	lv_obj_set_style_bg_color(ui_sxm_play_ind_text_panel, lv_color_hex(0x000000), LV_PART_MAIN | LV_STATE_DEFAULT);
	lv_obj_set_style_bg_opa(ui_sxm_play_ind_text_panel, 0, LV_PART_MAIN | LV_STATE_DEFAULT);
	lv_obj_set_style_border_width(ui_sxm_play_ind_text_panel, 0, LV_PART_MAIN | LV_STATE_DEFAULT);
	lv_obj_set_style_pad_left(ui_sxm_play_ind_text_panel, 0, LV_PART_MAIN | LV_STATE_DEFAULT);
	lv_obj_set_style_pad_right(ui_sxm_play_ind_text_panel, 0, LV_PART_MAIN | LV_STATE_DEFAULT);
	lv_obj_set_style_pad_top(ui_sxm_play_ind_text_panel, 0, LV_PART_MAIN | LV_STATE_DEFAULT);
	lv_obj_set_style_pad_bottom(ui_sxm_play_ind_text_panel, 0, LV_PART_MAIN | LV_STATE_DEFAULT);
	lv_obj_set_style_pad_row(ui_sxm_play_ind_text_panel, 0, LV_PART_MAIN | LV_STATE_DEFAULT);
	lv_obj_set_style_pad_column(ui_sxm_play_ind_text_panel, 0, LV_PART_MAIN | LV_STATE_DEFAULT);

	ui_sxm_play_ind_text_panel_name = lv_obj_create(ui_sxm_play_ind_text_panel);
	lv_obj_set_width(ui_sxm_play_ind_text_panel_name, lv_pct(100));
	lv_obj_set_height(ui_sxm_play_ind_text_panel_name, LV_SIZE_CONTENT);	 /// 30
	lv_obj_clear_flag(ui_sxm_play_ind_text_panel_name, LV_OBJ_FLAG_SCROLLABLE);	  /// Flags
	lv_obj_set_style_radius(ui_sxm_play_ind_text_panel_name, 0, LV_PART_MAIN | LV_STATE_DEFAULT);
	lv_obj_set_style_bg_color(ui_sxm_play_ind_text_panel_name, lv_color_hex(0x000000), LV_PART_MAIN | LV_STATE_DEFAULT);
	lv_obj_set_style_bg_opa(ui_sxm_play_ind_text_panel_name, 0, LV_PART_MAIN | LV_STATE_DEFAULT);
	lv_obj_set_style_border_width(ui_sxm_play_ind_text_panel_name, 0, LV_PART_MAIN | LV_STATE_DEFAULT);
	lv_obj_set_style_pad_left(ui_sxm_play_ind_text_panel_name, 0, LV_PART_MAIN | LV_STATE_DEFAULT);
	lv_obj_set_style_pad_right(ui_sxm_play_ind_text_panel_name, 0, LV_PART_MAIN | LV_STATE_DEFAULT);
	lv_obj_set_style_pad_top(ui_sxm_play_ind_text_panel_name, 0, LV_PART_MAIN | LV_STATE_DEFAULT);
	lv_obj_set_style_pad_bottom(ui_sxm_play_ind_text_panel_name, 0, LV_PART_MAIN | LV_STATE_DEFAULT);
	lv_obj_set_style_pad_row(ui_sxm_play_ind_text_panel_name, 0, LV_PART_MAIN | LV_STATE_DEFAULT);
	lv_obj_set_style_pad_column(ui_sxm_play_ind_text_panel_name, 0, LV_PART_MAIN | LV_STATE_DEFAULT);

	ui_sxm_play_ind_text_panel_name_text = lv_label_create(ui_sxm_play_ind_text_panel_name);
	lv_obj_set_width(ui_sxm_play_ind_text_panel_name_text, lv_pct(100));	/// 1
	lv_obj_set_height(ui_sxm_play_ind_text_panel_name_text, LV_SIZE_CONTENT);	  /// 1
    lv_obj_add_flag(ui_sxm_play_ind_text_panel_name_text, LV_OBJ_FLAG_CLICKABLE);
    lv_label_set_long_mode(ui_sxm_play_ind_text_panel_name_text, LV_LABEL_LONG_SCROLL_CIRCULAR);
	lv_label_set_text(ui_sxm_play_ind_text_panel_name_text, " ");
	lv_obj_set_style_text_color(ui_sxm_play_ind_text_panel_name_text, lv_color_hex(0xB3B3B3), LV_PART_MAIN | LV_STATE_DEFAULT);
	lv_obj_set_style_text_opa(ui_sxm_play_ind_text_panel_name_text, 255, LV_PART_MAIN | LV_STATE_DEFAULT);
	lv_obj_set_style_text_font(ui_sxm_play_ind_text_panel_name_text, lv_font_xsmall.font, LV_PART_MAIN | LV_STATE_DEFAULT);

	ui_sxm_play_ind_text_panel_song = lv_obj_create(ui_sxm_play_ind_text_panel);
	lv_obj_set_width(ui_sxm_play_ind_text_panel_song, lv_pct(100));
	lv_obj_set_height(ui_sxm_play_ind_text_panel_song, LV_SIZE_CONTENT);    /// 30
	lv_obj_clear_flag(ui_sxm_play_ind_text_panel_song, LV_OBJ_FLAG_SCROLLABLE);		/// Flags
	lv_obj_set_style_radius(ui_sxm_play_ind_text_panel_song, 0, LV_PART_MAIN | LV_STATE_DEFAULT);
	lv_obj_set_style_bg_color(ui_sxm_play_ind_text_panel_song, lv_color_hex(0x000000), LV_PART_MAIN | LV_STATE_DEFAULT);
	lv_obj_set_style_bg_opa(ui_sxm_play_ind_text_panel_song, 0, LV_PART_MAIN | LV_STATE_DEFAULT);
	lv_obj_set_style_border_width(ui_sxm_play_ind_text_panel_song, 0, LV_PART_MAIN | LV_STATE_DEFAULT);
	lv_obj_set_style_pad_left(ui_sxm_play_ind_text_panel_song, 0, LV_PART_MAIN | LV_STATE_DEFAULT);
	lv_obj_set_style_pad_right(ui_sxm_play_ind_text_panel_song, 0, LV_PART_MAIN | LV_STATE_DEFAULT);
	lv_obj_set_style_pad_top(ui_sxm_play_ind_text_panel_song, 0, LV_PART_MAIN | LV_STATE_DEFAULT);
	lv_obj_set_style_pad_bottom(ui_sxm_play_ind_text_panel_song, 0, LV_PART_MAIN | LV_STATE_DEFAULT);
	lv_obj_set_style_pad_row(ui_sxm_play_ind_text_panel_song, 0, LV_PART_MAIN | LV_STATE_DEFAULT);
	lv_obj_set_style_pad_column(ui_sxm_play_ind_text_panel_song, 0, LV_PART_MAIN | LV_STATE_DEFAULT);

	ui_sxm_play_ind_text_panel_song_text = lv_label_create(ui_sxm_play_ind_text_panel_song);
	lv_obj_set_width(ui_sxm_play_ind_text_panel_song_text, lv_pct(100));   /// 1
	lv_obj_set_height(ui_sxm_play_ind_text_panel_song_text, LV_SIZE_CONTENT);	/// 1
    lv_obj_add_flag(ui_sxm_play_ind_text_panel_song_text, LV_OBJ_FLAG_CLICKABLE);
    lv_label_set_long_mode(ui_sxm_play_ind_text_panel_song_text, LV_LABEL_LONG_SCROLL_CIRCULAR);
	lv_label_set_text(ui_sxm_play_ind_text_panel_song_text, " ");
	lv_obj_set_style_text_color(ui_sxm_play_ind_text_panel_song_text, lv_color_hex(0xFFFFFF), LV_PART_MAIN | LV_STATE_DEFAULT);
	lv_obj_set_style_text_opa(ui_sxm_play_ind_text_panel_song_text, 255, LV_PART_MAIN | LV_STATE_DEFAULT);
	lv_obj_set_style_text_font(ui_sxm_play_ind_text_panel_song_text, lv_font_large.font, LV_PART_MAIN | LV_STATE_DEFAULT);

	ui_sxm_play_ind_text_panel_artist_info = lv_obj_create(ui_sxm_play_ind_text_panel);
	lv_obj_set_width(ui_sxm_play_ind_text_panel_artist_info, lv_pct(100));
	lv_obj_set_height(ui_sxm_play_ind_text_panel_artist_info, LV_SIZE_CONTENT);	/// 30
	lv_obj_clear_flag(ui_sxm_play_ind_text_panel_artist_info, LV_OBJ_FLAG_SCROLLABLE); 	 /// Flags
	lv_obj_set_style_radius(ui_sxm_play_ind_text_panel_artist_info, 0, LV_PART_MAIN | LV_STATE_DEFAULT);
	lv_obj_set_style_bg_color(ui_sxm_play_ind_text_panel_artist_info, lv_color_hex(0x000000), LV_PART_MAIN | LV_STATE_DEFAULT);
	lv_obj_set_style_bg_opa(ui_sxm_play_ind_text_panel_artist_info, 0, LV_PART_MAIN | LV_STATE_DEFAULT);
	lv_obj_set_style_border_width(ui_sxm_play_ind_text_panel_artist_info, 0, LV_PART_MAIN | LV_STATE_DEFAULT);
	lv_obj_set_style_pad_left(ui_sxm_play_ind_text_panel_artist_info, 0, LV_PART_MAIN | LV_STATE_DEFAULT);
	lv_obj_set_style_pad_right(ui_sxm_play_ind_text_panel_artist_info, 0, LV_PART_MAIN | LV_STATE_DEFAULT);
	lv_obj_set_style_pad_top(ui_sxm_play_ind_text_panel_artist_info, 0, LV_PART_MAIN | LV_STATE_DEFAULT);
	lv_obj_set_style_pad_bottom(ui_sxm_play_ind_text_panel_artist_info, 0, LV_PART_MAIN | LV_STATE_DEFAULT);
	lv_obj_set_style_pad_row(ui_sxm_play_ind_text_panel_artist_info, 0, LV_PART_MAIN | LV_STATE_DEFAULT);
	lv_obj_set_style_pad_column(ui_sxm_play_ind_text_panel_artist_info, 0, LV_PART_MAIN | LV_STATE_DEFAULT);

	ui_sxm_play_ind_text_panel_artist_info_text = lv_label_create(ui_sxm_play_ind_text_panel_artist_info);
	lv_obj_set_width(ui_sxm_play_ind_text_panel_artist_info_text, lv_pct(100));   /// 1
	lv_obj_set_height(ui_sxm_play_ind_text_panel_artist_info_text, LV_SIZE_CONTENT);	 /// 1
    lv_obj_add_flag(ui_sxm_play_ind_text_panel_artist_info_text, LV_OBJ_FLAG_CLICKABLE);
    lv_label_set_long_mode(ui_sxm_play_ind_text_panel_artist_info_text, LV_LABEL_LONG_SCROLL_CIRCULAR);
	lv_label_set_text(ui_sxm_play_ind_text_panel_artist_info_text, " ");
	lv_obj_set_style_text_color(ui_sxm_play_ind_text_panel_artist_info_text, lv_color_hex(0xCCCCCC), LV_PART_MAIN | LV_STATE_DEFAULT);
	lv_obj_set_style_text_opa(ui_sxm_play_ind_text_panel_artist_info_text, 255, LV_PART_MAIN | LV_STATE_DEFAULT);
	lv_obj_set_style_text_font(ui_sxm_play_ind_text_panel_artist_info_text, lv_font_medium.font, LV_PART_MAIN | LV_STATE_DEFAULT);

    ui_sxm_play_bottom = lv_obj_create(ui_sxm_play);
    lv_obj_set_width(ui_sxm_play_bottom, lv_pct(93));
    lv_obj_set_height(ui_sxm_play_bottom, lv_pct(20));
    lv_obj_set_align(ui_sxm_play_bottom, LV_ALIGN_BOTTOM_LEFT);
    lv_obj_remove_flag(ui_sxm_play_bottom, LV_OBJ_FLAG_SCROLLABLE);      /// Flags
    lv_obj_set_style_radius(ui_sxm_play_bottom, 0, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_bg_color(ui_sxm_play_bottom, lv_color_hex(0xFFFFFF), LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_bg_opa(ui_sxm_play_bottom, 0, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_border_width(ui_sxm_play_bottom, 0, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_pad_left(ui_sxm_play_bottom, 0, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_pad_right(ui_sxm_play_bottom, 0, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_pad_top(ui_sxm_play_bottom, 0, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_pad_bottom(ui_sxm_play_bottom, 0, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_pad_row(ui_sxm_play_bottom, 0, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_pad_column(ui_sxm_play_bottom, 0, LV_PART_MAIN | LV_STATE_DEFAULT);

    ui_sxm_play_bottom_current = lv_label_create(ui_sxm_play_bottom);
    lv_obj_set_width(ui_sxm_play_bottom_current, LV_SIZE_CONTENT);   /// 1
    lv_obj_set_height(ui_sxm_play_bottom_current, LV_SIZE_CONTENT);    /// 1
    lv_obj_set_x(ui_sxm_play_bottom_current, lv_pct(-3));
    lv_obj_set_y(ui_sxm_play_bottom_current, lv_pct(0));
    lv_obj_set_align(ui_sxm_play_bottom_current, LV_ALIGN_RIGHT_MID);
    lv_label_set_text(ui_sxm_play_bottom_current, "00:00");
    lv_obj_set_style_text_color(ui_sxm_play_bottom_current, lv_color_hex(0x808080), LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_text_opa(ui_sxm_play_bottom_current, 255, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_text_font(ui_sxm_play_bottom_current, lv_font_xsmall.font, LV_PART_MAIN | LV_STATE_DEFAULT);

    ui_sxm_play_bottom_bar = lv_slider_create(ui_sxm_play_bottom);
    lv_slider_set_value(ui_sxm_play_bottom_bar, 0, LV_ANIM_OFF);
    if(lv_slider_get_mode(ui_sxm_play_bottom_bar) == LV_SLIDER_MODE_RANGE) lv_slider_set_left_value(ui_sxm_play_bottom_bar,
                                                                                                        0, LV_ANIM_OFF);
    lv_obj_set_height(ui_sxm_play_bottom_bar, 6);
    lv_obj_set_width(ui_sxm_play_bottom_bar, lv_pct(100));
    lv_obj_set_align(ui_sxm_play_bottom_bar, LV_ALIGN_BOTTOM_LEFT);
    lv_obj_set_style_radius(ui_sxm_play_bottom_bar, 0, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_bg_color(ui_sxm_play_bottom_bar, lv_color_hex(0x262626), LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_bg_opa(ui_sxm_play_bottom_bar, 255, LV_PART_MAIN | LV_STATE_DEFAULT);

    lv_obj_set_style_radius(ui_sxm_play_bottom_bar, 0, LV_PART_INDICATOR | LV_STATE_DEFAULT);
    lv_obj_set_style_bg_color(ui_sxm_play_bottom_bar, lv_color_hex(0xFFFFFF), LV_PART_INDICATOR | LV_STATE_DEFAULT);
    lv_obj_set_style_bg_opa(ui_sxm_play_bottom_bar, 255, LV_PART_INDICATOR | LV_STATE_DEFAULT);

    lv_obj_set_style_bg_color(ui_sxm_play_bottom_bar, lv_color_hex(0xFFFFFF), LV_PART_KNOB | LV_STATE_DEFAULT);
    lv_obj_set_style_bg_opa(ui_sxm_play_bottom_bar, 0, LV_PART_KNOB | LV_STATE_DEFAULT);

    //Compensating for LVGL9.1 draw crash with bar/slider max value when top-padding is nonzero and right-padding is 0
    if(lv_obj_get_style_pad_top(ui_sxm_play_bottom_bar,
                                LV_PART_MAIN) > 0) lv_obj_set_style_pad_right(ui_sxm_play_bottom_bar, lv_obj_get_style_pad_right(ui_sxm_play_bottom_bar,
                                                                                  LV_PART_MAIN) + 1, LV_PART_MAIN);
    
    ui_sxm_play_bottom_ff = lv_obj_create(ui_sxm_play_bottom);
    lv_obj_set_width(ui_sxm_play_bottom_ff, lv_pct(13));
    lv_obj_set_height(ui_sxm_play_bottom_ff, LV_SIZE_CONTENT);    /// 74
    lv_obj_set_x(ui_sxm_play_bottom_ff, lv_pct(-18));
    lv_obj_set_y(ui_sxm_play_bottom_ff, lv_pct(0));
    lv_obj_set_align(ui_sxm_play_bottom_ff, LV_ALIGN_RIGHT_MID);
    lv_obj_remove_flag(ui_sxm_play_bottom_ff, LV_OBJ_FLAG_CLICKABLE | LV_OBJ_FLAG_SCROLLABLE);      /// Flags
    lv_obj_set_style_radius(ui_sxm_play_bottom_ff, 0, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_bg_color(ui_sxm_play_bottom_ff, lv_color_hex(0xFFFFFF), LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_bg_opa(ui_sxm_play_bottom_ff, 0, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_border_width(ui_sxm_play_bottom_ff, 0, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_pad_left(ui_sxm_play_bottom_ff, 0, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_pad_right(ui_sxm_play_bottom_ff, 0, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_pad_top(ui_sxm_play_bottom_ff, 0, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_pad_bottom(ui_sxm_play_bottom_ff, 0, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_pad_row(ui_sxm_play_bottom_ff, 0, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_pad_column(ui_sxm_play_bottom_ff, 0, LV_PART_MAIN | LV_STATE_DEFAULT);

    ui_sxm_play_bottom_ff_img = lv_image_create(ui_sxm_play_bottom_ff);
    lv_image_set_src(ui_sxm_play_bottom_ff_img, ui_sxm_get_res(sxm_para, SXM_THMEM_TEMP_COM_PD_CHARGE_BMP));
    lv_obj_set_width(ui_sxm_play_bottom_ff_img, LV_SIZE_CONTENT);   /// 36
    lv_obj_set_height(ui_sxm_play_bottom_ff_img, LV_SIZE_CONTENT);    /// 31
    lv_obj_set_align(ui_sxm_play_bottom_ff_img, LV_ALIGN_TOP_MID);
    lv_obj_add_flag(ui_sxm_play_bottom_ff_img, LV_OBJ_FLAG_ADV_HITTEST);     /// Flags
    lv_obj_remove_flag(ui_sxm_play_bottom_ff_img, LV_OBJ_FLAG_SCROLLABLE);      /// Flags

    ui_sxm_bottom_list = lv_obj_create(sxm_para->source_para->ui_bottom);
    lv_obj_remove_style_all(ui_sxm_bottom_list);
    lv_obj_set_width(ui_sxm_bottom_list, 161);
    lv_obj_set_height(ui_sxm_bottom_list, 74);
    lv_obj_set_align(ui_sxm_bottom_list, LV_ALIGN_BOTTOM_LEFT);
    //lv_obj_add_flag(ui_sxm_bottom_list, LV_OBJ_FLAG_HIDDEN);      /// Flags
    lv_obj_clear_flag(ui_sxm_bottom_list, LV_OBJ_FLAG_SCROLLABLE);      /// Flags
    lv_obj_set_style_bg_color(ui_sxm_bottom_list, lv_color_hex(0x000000), LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_bg_opa(ui_sxm_bottom_list, 0, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_bg_img_src(ui_sxm_bottom_list, ui_sxm_get_res(sxm_para, SXM_THEME_COM_BOT_ICON_BG_A_L_N_BMP), LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_bg_img_src(ui_sxm_bottom_list, ui_sxm_get_res(sxm_para, SXM_THEME_COM_BOT_ICON_BG_A_L_S_BMP), LV_PART_MAIN | LV_STATE_PRESSED);
    lv_obj_set_style_bg_img_src(ui_sxm_bottom_list, ui_sxm_get_res(sxm_para, SXM_THEME_COM_BOT_ICON_BG_A_L_S_BMP), LV_PART_MAIN | LV_STATE_FOCUSED);

    ui_sxm_bottom_list_img = lv_img_create(ui_sxm_bottom_list);
    lv_img_set_src(ui_sxm_bottom_list_img, ui_sxm_get_res(sxm_para, SXM_THEME_COM_BOT_LIST_N_BMP));
    lv_obj_set_width(ui_sxm_bottom_list_img, LV_SIZE_CONTENT);   /// 42
    lv_obj_set_height(ui_sxm_bottom_list_img, LV_SIZE_CONTENT);    /// 34
    lv_obj_set_align(ui_sxm_bottom_list_img, LV_ALIGN_CENTER);
    lv_obj_add_flag(ui_sxm_bottom_list_img, LV_OBJ_FLAG_ADV_HITTEST);
    lv_obj_clear_flag(ui_sxm_bottom_list_img, LV_OBJ_FLAG_SCROLLABLE);

    ui_sxm_bottom_cat = lv_obj_create(sxm_para->source_para->ui_bottom);
    lv_obj_remove_style_all(ui_sxm_bottom_cat);
    lv_obj_set_width(ui_sxm_bottom_cat, 161);
    lv_obj_set_height(ui_sxm_bottom_cat, 74);
    lv_obj_set_align(ui_sxm_bottom_cat, LV_ALIGN_BOTTOM_LEFT);
    //lv_obj_add_flag(ui_sxm_bottom_cat, LV_OBJ_FLAG_HIDDEN);      /// Flags
    lv_obj_clear_flag(ui_sxm_bottom_cat, LV_OBJ_FLAG_SCROLLABLE);      /// Flags
    lv_obj_set_style_bg_color(ui_sxm_bottom_cat, lv_color_hex(0x000000), LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_bg_opa(ui_sxm_bottom_cat, 0, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_bg_img_src(ui_sxm_bottom_cat, ui_sxm_get_res(sxm_para, SXM_THEME_COM_BOT_ICON_BG_A_L_N_BMP), LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_bg_img_src(ui_sxm_bottom_cat, ui_sxm_get_res(sxm_para, SXM_THEME_COM_BOT_ICON_BG_A_L_S_BMP), LV_PART_MAIN | LV_STATE_PRESSED);
    lv_obj_set_style_bg_img_src(ui_sxm_bottom_cat, ui_sxm_get_res(sxm_para, SXM_THEME_COM_BOT_ICON_BG_A_L_S_BMP), LV_PART_MAIN | LV_STATE_FOCUSED);

	ui_sxm_bottom_cat_label = lv_label_create(ui_sxm_bottom_cat);
	lv_obj_set_width(ui_sxm_bottom_cat_label, lv_pct(100));//lv_pct(89)
	lv_obj_set_height(ui_sxm_bottom_cat_label, LV_SIZE_CONTENT);	  /// 0
	lv_obj_set_align(ui_sxm_bottom_cat_label, LV_ALIGN_CENTER);
    lv_label_set_text(ui_sxm_bottom_cat_label, "CAT");
	lv_label_set_long_mode(ui_sxm_bottom_cat_label, LV_LABEL_LONG_CLIP);
	lv_obj_set_style_text_color(ui_sxm_bottom_cat_label, lv_color_hex(0xFFFFFF), LV_PART_MAIN | LV_STATE_DEFAULT);
	lv_obj_set_style_text_opa(ui_sxm_bottom_cat_label, 255, LV_PART_MAIN | LV_STATE_DEFAULT);
	lv_obj_set_style_text_font(ui_sxm_bottom_cat_label, lv_font_medium.font, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_text_align(ui_sxm_bottom_cat_label, LV_TEXT_ALIGN_CENTER, LV_PART_MAIN | LV_STATE_DEFAULT);

	lv_obj_set_style_text_color(ui_sxm_bottom_cat_label, lv_color_hex(0x808080), LV_PART_MAIN | LV_STATE_PRESSED);

    ui_sxm_bottom_replay = lv_obj_create(sxm_para->source_para->ui_bottom);
    lv_obj_remove_style_all(ui_sxm_bottom_replay);
    lv_obj_set_width(ui_sxm_bottom_replay, 161);
    lv_obj_set_height(ui_sxm_bottom_replay, 74);
    lv_obj_set_align(ui_sxm_bottom_replay, LV_ALIGN_BOTTOM_LEFT);
    //lv_obj_add_flag(ui_sxm_bottom_replay, LV_OBJ_FLAG_HIDDEN);      /// Flags
    lv_obj_clear_flag(ui_sxm_bottom_replay, LV_OBJ_FLAG_SCROLLABLE);      /// Flags
    lv_obj_set_style_bg_color(ui_sxm_bottom_replay, lv_color_hex(0x000000), LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_bg_opa(ui_sxm_bottom_replay, 0, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_bg_img_src(ui_sxm_bottom_replay, ui_sxm_get_res(sxm_para, SXM_THEME_COM_BOT_ICON_BG_A_L_N_BMP), LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_bg_img_src(ui_sxm_bottom_replay, ui_sxm_get_res(sxm_para, SXM_THEME_COM_BOT_ICON_BG_A_L_S_BMP), LV_PART_MAIN | LV_STATE_PRESSED);
    lv_obj_set_style_bg_img_src(ui_sxm_bottom_replay, ui_sxm_get_res(sxm_para, SXM_THEME_COM_BOT_ICON_BG_A_L_S_BMP), LV_PART_MAIN | LV_STATE_FOCUSED);

	ui_sxm_bottom_replay_label = lv_label_create(ui_sxm_bottom_replay);
	lv_obj_set_width(ui_sxm_bottom_replay_label, lv_pct(100));//lv_pct(89)
	lv_obj_set_height(ui_sxm_bottom_replay_label, LV_SIZE_CONTENT);	  /// 0
	lv_obj_set_align(ui_sxm_bottom_replay_label, LV_ALIGN_CENTER);
    if(sxm_para->live_replay_flag)
    {
        lv_label_set_text(ui_sxm_bottom_replay_label, "Replay");
    }
    else
    {
        lv_label_set_text(ui_sxm_bottom_replay_label, "Live");
    }
	lv_label_set_long_mode(ui_sxm_bottom_replay_label, LV_LABEL_LONG_CLIP);
	lv_obj_set_style_text_color(ui_sxm_bottom_replay_label, lv_color_hex(0xFFFFFF), LV_PART_MAIN | LV_STATE_DEFAULT);
	lv_obj_set_style_text_opa(ui_sxm_bottom_replay_label, 255, LV_PART_MAIN | LV_STATE_DEFAULT);
	lv_obj_set_style_text_font(ui_sxm_bottom_replay_label, lv_font_medium.font, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_text_align(ui_sxm_bottom_replay_label, LV_TEXT_ALIGN_CENTER, LV_PART_MAIN | LV_STATE_DEFAULT);

	lv_obj_set_style_text_color(ui_sxm_bottom_replay_label, lv_color_hex(0x808080), LV_PART_MAIN | LV_STATE_PRESSED);

    ui_sxm_bottom_direct = lv_obj_create(sxm_para->source_para->ui_bottom);
    lv_obj_remove_style_all(ui_sxm_bottom_direct);
    lv_obj_set_width(ui_sxm_bottom_direct, 161);
    lv_obj_set_height(ui_sxm_bottom_direct, 74);
    lv_obj_set_align(ui_sxm_bottom_direct, LV_ALIGN_BOTTOM_LEFT);
    //lv_obj_add_flag(ui_sxm_bottom_direct, LV_OBJ_FLAG_HIDDEN);      /// Flags
    lv_obj_clear_flag(ui_sxm_bottom_direct, LV_OBJ_FLAG_SCROLLABLE);      /// Flags
    lv_obj_set_style_bg_color(ui_sxm_bottom_direct, lv_color_hex(0x000000), LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_bg_opa(ui_sxm_bottom_direct, 0, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_bg_img_src(ui_sxm_bottom_direct, ui_sxm_get_res(sxm_para, SXM_THEME_COM_BOT_ICON_BG_A_L_N_BMP), LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_bg_img_src(ui_sxm_bottom_direct, ui_sxm_get_res(sxm_para, SXM_THEME_COM_BOT_ICON_BG_A_L_S_BMP), LV_PART_MAIN | LV_STATE_PRESSED);
    lv_obj_set_style_bg_img_src(ui_sxm_bottom_direct, ui_sxm_get_res(sxm_para, SXM_THEME_COM_BOT_ICON_BG_A_L_S_BMP), LV_PART_MAIN | LV_STATE_FOCUSED);

    ui_sxm_bottom_direct_img = lv_img_create(ui_sxm_bottom_direct);
    lv_img_set_src(ui_sxm_bottom_direct_img, ui_sxm_get_res(sxm_para, SXM_THEME_COM_BOT_DIRECT_N_BMP));
    lv_obj_set_width(ui_sxm_bottom_direct_img, LV_SIZE_CONTENT);   /// 42
    lv_obj_set_height(ui_sxm_bottom_direct_img, LV_SIZE_CONTENT);    /// 34
    lv_obj_set_align(ui_sxm_bottom_direct_img, LV_ALIGN_CENTER);
    lv_obj_add_flag(ui_sxm_bottom_direct_img, LV_OBJ_FLAG_ADV_HITTEST);
    lv_obj_clear_flag(ui_sxm_bottom_direct_img, LV_OBJ_FLAG_SCROLLABLE);


    ui_sxm_timer = lv_timer_create(ui_event_sxm_timer_cb, FlashTimeWinTime*10, sxm_para);

	lv_obj_add_event_cb(ui_sxm, ui_event_sxm_screen_cb, LV_EVENT_ALL, sxm_para);
    lv_obj_add_event_cb(ui_sxm_play_logo_panel, ui_event_sxm_play_logo_panel_cb, LV_EVENT_ALL, sxm_para);
    lv_obj_add_event_cb(ui_sxm_bottom_list, ui_event_sxm_bottom_list_cb, LV_EVENT_ALL, sxm_para);
    lv_obj_add_event_cb(ui_sxm_bottom_cat, ui_event_sxm_bottom_cat_cb, LV_EVENT_ALL, sxm_para);
    lv_obj_add_event_cb(ui_sxm_bottom_replay, ui_event_sxm_bottom_replay_cb, LV_EVENT_ALL, sxm_para);
    lv_obj_add_event_cb(ui_sxm_bottom_direct, ui_event_sxm_bottom_direct_cb, LV_EVENT_ALL, sxm_para);
	lv_obj_add_event_cb(sxm_para->source_para->ui_top_left, ui_event_sxm_left_cb, LV_EVENT_ALL, sxm_para);
	lv_obj_add_event_cb(sxm_para->source_para->ui_top_right, ui_event_sxm_right_cb, LV_EVENT_ALL, sxm_para);
    lv_obj_add_event_cb(sxm_para->source_para->ui_rig_bar_bg, ui_event_sxm_open_bar_cb, LV_EVENT_ALL, sxm_para);
    //lv_obj_add_event_cb(sxm_para->source_para->ui_rig_panel_button_bottom, ui_event_sxm_bottom_menu_cb, LV_EVENT_ALL, sxm_para);

    lv_group_remove_obj(ui_sxm_play_bottom_bar);
}

static void ui_sxm_screen_uninit(ui_sxm_para_t * sxm_para)
{
	lv_obj_remove_event_cb(sxm_para->source_para->ui_top_left, ui_event_sxm_left_cb);
	lv_obj_remove_event_cb(sxm_para->source_para->ui_top_right, ui_event_sxm_right_cb);
    lv_obj_remove_event_cb(sxm_para->source_para->ui_rig_bar_bg, ui_event_sxm_open_bar_cb);
    lv_obj_remove_event_cb(sxm_para->source_para->ui_rig_panel_button_bottom, ui_event_sxm_bottom_menu_cb);
    if(ui_sxm_func_code)
      lv_obj_del(ui_sxm_func_code);
    if(ui_sxm_func_menu)
        lv_obj_del(ui_sxm_func_menu);
    if(ui_sxm_direct_tuning)
        lv_obj_del(ui_sxm_direct_tuning);
    if(ui_sxm_parental_ctrl_code)
        lv_obj_del(ui_sxm_parental_ctrl_code);
    if(ui_sxm_parental_ctrl_lock_channel)
        lv_obj_del(ui_sxm_parental_ctrl_lock_channel);
    if(ui_sxm_parental_ctrl_change_code)
        lv_obj_del(ui_sxm_parental_ctrl_change_code);
    if(ui_sxm)
	    lv_obj_del(ui_sxm);
}

void ui_sxm_init(void * para)
{
	if(!ui_sxm_para)
	{		
		ui_sxm_para = (ui_sxm_para_t *)esMEMS_Balloc(sizeof(ui_sxm_para_t));
		if(ui_sxm_para == NULL)
		{
			eLIBs_printf("esMEMS_Balloc() fail!\n");
			return;
		}
		eLIBs_memset((void*)ui_sxm_para, 0, sizeof(ui_sxm_para_t));

        ui_sxm_internal_para_update(ui_sxm_para);

        // Initialize MCU Protocol v2 communication interface
        if (ui_sxm_para->source_para) {
            ui_sxm_para->mcu_ops.sync_all = ui_mcu_sxm_sync_all;
            ui_sxm_para->mcu_ops.sync_dirty = ui_mcu_sxm_sync_dirty;
            ui_sxm_para->mcu_ops.ops = &ui_sxm_para->source_para->mcu_v2_ops;
            
            // Execute initial full data sync
            if (ui_sxm_para->mcu_ops.sync_all) {
                ui_sxm_para->mcu_ops.sync_all(ui_sxm_para);
            }
        }

	    ui_sxm_init_res(ui_sxm_para);
		ui_sxm_send_srv_msg(GUI_MSG_SXM_REFRESH_INIT,0,0,0,0,0,0);
	    ui_sxm_helpers_init(ui_sxm_para, para);
    }
    else
    {
    	
    }
}

void ui_sxm_uninit(void * para)
{
	#if 1
	if(ui_sxm_para)
	{
		ui_sxm_para->mode_uninit_flag = 1;
		__sxm_msg("ui_sxm_uninit cur_task_is_opn_handler:%d.\n",cur_task_is_opn_handler());
		__sxm_msg("ui_sxm_uninit cur_task_is_lvgl_handler:%d.\n",cur_task_is_lvgl_handler());
		
		ui_sxm_helpers_uninit(ui_sxm_para);
		ui_sxm_send_srv_msg(GUI_MSG_SXM_REFRESH_UNINIT,0,0,0,0,1,1);
	}
	#endif
}

