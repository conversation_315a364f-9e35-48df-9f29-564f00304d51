// SquareLine LVGL GENERATED FILE
// EDITOR VERSION: SquareLine Studio 1.2.0
// LVGL VERSION: 8.3.4
// PROJECT: SquareLine_Project

#ifndef _UI_PARTY_MODE_HELPERS_H
#define _UI_PARTY_MODE_HELPERS_H

#ifdef __cplusplus
extern "C" {
#endif

#include "app_root_helpers.h"
#include "ui_party_mode_res.h"
#include "ui_source_communication_hub.h"

#define FlashTimeWinTime		 23 // 50	// 0.5s time

enum
{
	PARTY_WIN_NONE,
	PARTY_WIN_SELECT_MODE,
	PARTY_WIN_BROADCAST,
	PARTY_WIN_RECEIVER,
	PARTY_WIN_DEVICE_LIST,
	PARTY_WIN_KNOWN_DEVICE,
	PARTY_WIN_MENU,

	PARTY_WIN_MAX,
};

enum
{
    PARTY_MENU_MODE,
    PARTY_MENU_SOURCE,
    PARTY_MENU_DEVICE_NAME,

    PARTY_MENU_MAX,
};

enum
{
	PARTY_SOURCE_USB,
    PARTY_SOURCE_BT,
    PARTY_SOURCE_AUX,

    PARTY_SOURCE_MAX,
};

enum
{
	PARTY_ROLE_BROADCAST,
	PARTY_ROLE_RECEIVER,
};

typedef struct party_mode_res_para
{
	HTHEME lv_party_mode_bmp[MAX_PARTY_MODE_BMP_ITEM];
	lv_img_dsc_t lv_party_mode_icon[MAX_PARTY_MODE_BMP_ITEM];
}ui_party_mode_res_para_t;

typedef struct party_mode_para
{
	H_WIN dialog_win;
	H_WIN del_device_dialog_win;

    __u32 theme;//__THEME_TYPE
    __u32 theme_bmp_start;
    __u32 theme_bmp_end;

    __u32 win_type;
    __u32 menu_type;

    lv_anim_t * menu_anim[6];
	lv_obj_t * menu_obj;
	explr_list_para_t ui_list_para;

    lv_obj_t * source_obj;
    ui_source_para_t * source_para;

	__u8 mode_store;
	lv_obj_t * party_mode_main_obj;
	ui_party_mode_res_para_t ui_party_mode_res_para;
	__gui_msg_t party_mode_msg;
	bool mode_uninit_flag;
	
	// MCU Protocol v2 communication interface
	mcu_data_ops_t mcu_ops;

	lv_indev_data_t * indev_data;
	
    __u32 bottom_fouse_flag;
    __u32 bottom_fouse_index;

    __u32 enter_home_flag;

    __u32 connect_index;
    __u32 delete_index;
}ui_party_mode_para_t;

extern ui_party_mode_para_t * ui_party_mode_para;
extern const __s32 party_mode_item_string_res_id[PARTY_MENU_MAX];


//ui_party_mode_helpers.c
extern __s32 __party_mode_cmd2parent(H_WIN hwin, __s32 id, __s32 data1, __s32 data2);
extern ui_party_mode_para_t * ui_party_mode_get_para(void);
extern __s32 ui_party_mode_helpers_init(ui_party_mode_para_t * party_mode_para, void * para);
extern __s32 ui_party_mode_helpers_uninit(ui_party_mode_para_t * party_mode_para);


//ui_party_mode_res.c
extern lv_img_dsc_t * ui_party_mode_get_res(ui_party_mode_para_t * party_mode_para, __u32 icon);
extern void ui_party_mode_init_res(ui_party_mode_para_t * party_mode_para);
extern void ui_party_mode_uninit_res(ui_party_mode_para_t * party_mode_para);


//ui_party_mode.c
extern __s32 ui_party_mode_refresh_handler(void * para, __gui_msg_t * pmsg);
extern void __ui_party_mode_remove_hidden(ui_party_mode_para_t * party_mode_para);
extern void __ui_party_mode_add_hidden(ui_party_mode_para_t * party_mode_para);
extern void __ui_party_mode_layer_on(ui_party_mode_para_t * party_mode_para);
extern void __ui_party_mode_layer_sleep(ui_party_mode_para_t * party_mode_para);
extern __s32 __party_mode_time_proc(ui_party_mode_para_t * party_mode_para);


//ui_party_mode_events.c
extern __s32 ui_party_mode_opn_msg(__gui_msg_t * pmsg, __u32 sync);
extern __s32 ui_party_mode_refresh_msg(__gui_msg_t * pmsg, __u32 sync);
extern __s32 ui_party_mode_send_srv_msg(__s32 id, __u64 data1, __u64 data2, __u64 dwReserved, void *p_arg, __u32 flag, __u32 sync);
extern __s32 ui_party_mode_send_opn_msg(__s32 id, __u64 data1, __u64 data2, __u64 dwReserved, void *p_arg, __u32 flag, __u32 sync);
extern __s32 party_mode_cmd2parent(H_WIN hwin, __s32 id, __s32 data1, __s32 data2);
extern __s32 ui_party_mode_remove_hidden(ui_party_mode_para_t * party_mode_para);
extern __s32 ui_party_mode_add_hidden(ui_party_mode_para_t * party_mode_para);
extern __s32 ui_party_mode_layer_on(ui_party_mode_para_t * party_mode_para);
extern __s32 ui_party_mode_layer_sleep(ui_party_mode_para_t * party_mode_para);
extern __s32 party_mode_time_proc(ui_party_mode_para_t * party_mode_para);
extern __s32 ui_party_mode_send_cmd(ui_party_mode_para_t * party_mode_para, __u32 cmd, __u32 value);

#ifdef __cplusplus
} /*extern "C"*/
#endif

#endif
