// SquareLine LVGL GENERATED FILE
// EDITOR VERSION: SquareLine Studio 1.2.0
// LVGL VERSION: 8.3.4
// PROJECT: SquareLine_Project

#include "ui_party_mode.h"
#include "ui_helpers.h"
#include "ui_party_mode_helpers.h"
#include "ui_party_mode_res.h"

#if 1//USE_LOG_PRINT
	#define __party_mode_msg(...)			(eLIBs_printf("party_mode MSG:L%d:", __LINE__),				 \
													  eLIBs_printf(__VA_ARGS__)) 
#else
	#define __party_mode_msg(...)
#endif

///////////////////// VARIABLES ////////////////////
ui_party_mode_para_t * ui_party_mode_para = NULL;

static void ui_party_mode_list_folder_reinit(ui_party_mode_para_t * party_mode_para, void *menu_obj, __u32 item_cnt);
static void ui_party_mode_bottom_group_update(ui_party_mode_para_t * party_mode_para, __u8 flag, __u8 index, __u8 force);
static void ui_event_party_mode_timer_cb(lv_timer_t * t);
static void ui_party_mode_top_right_event_handler(ui_party_mode_para_t * party_mode_para);
static void ui_party_mode_bottom_menu_event_handler(ui_party_mode_para_t * party_mode_para);
static void ui_party_mode_broadcast_source_event_handler(ui_party_mode_para_t * party_mode_para, uint32_t selected);
static void ui_event_party_mode_menu_item_cb(lv_event_t * e);
static void ui_event_party_mode_device_cb(lv_event_t * e);

// SCREEN: ui_party_mode
static void ui_party_mode_select_mode_screen_init(ui_party_mode_para_t * party_mode_para);
static void ui_party_mode_select_mode_screen_uninit(ui_party_mode_para_t * party_mode_para);
static void ui_party_mode_broadcast_screen_init(ui_party_mode_para_t * party_mode_para);
static void ui_party_mode_broadcast_screen_uninit(ui_party_mode_para_t * party_mode_para);
static void ui_party_mode_receiver_screen_init(ui_party_mode_para_t * party_mode_para);
static void ui_party_mode_receiver_screen_uninit(ui_party_mode_para_t * party_mode_para);
static void ui_party_mode_menu_screen_init(ui_party_mode_para_t * party_mode_para);
static void ui_party_mode_menu_screen_uninit(ui_party_mode_para_t * party_mode_para);
static void ui_party_mode_screen_init(ui_party_mode_para_t * party_mode_para);
static void ui_party_mode_screen_uninit(ui_party_mode_para_t * party_mode_para);

lv_obj_t * ui_party_mode = NULL;
lv_obj_t * ui_party_mode_play = NULL;
lv_obj_t * ui_party_mode_play_broadcast_bg = NULL;
lv_obj_t * ui_party_mode_play_top = NULL;
lv_obj_t * ui_party_mode_play_top_low = NULL;
lv_obj_t * ui_party_mode_play_top_charge = NULL;
lv_obj_t * ui_party_mode_play_top_eq = NULL;
lv_obj_t * ui_party_mode_play_mid = NULL;
lv_obj_t * ui_party_mode_play_logo_panel = NULL;
lv_obj_t * ui_party_mode_play_logo = NULL;
lv_obj_t * ui_party_mode_play_logo_text = NULL;
lv_obj_t * ui_party_mode_play_disp = NULL;
lv_obj_t * ui_party_mode_id3;
lv_obj_t * ui_party_mode_id3_text;
lv_obj_t * ui_party_mode_id3_text_panel;
lv_obj_t * ui_party_mode_id3_text_panel_artist;
lv_obj_t * ui_party_mode_id3_text_panel_artist_text;
lv_obj_t * ui_party_mode_id3_text_panel_song;
lv_obj_t * ui_party_mode_id3_text_panel_song_text;
lv_obj_t * ui_party_mode_id3_text_panel_album;
lv_obj_t * ui_party_mode_id3_text_panel_album_text;
lv_obj_t * ui_party_mode_connect_status;
lv_obj_t * ui_party_mode_connect_status_text;
lv_obj_t * ui_party_mode_play_bottom;
lv_obj_t * ui_party_mode_play_bottom_text = NULL;
lv_obj_t * ui_party_mode_content = NULL;
lv_obj_t * ui_party_mode_menu = NULL;
lv_obj_t * ui_party_mode_bottom_list = NULL;
lv_obj_t * ui_party_mode_bottom_list_img = NULL;
lv_obj_t * ui_party_mode_bottom_browse = NULL;
lv_obj_t * ui_party_mode_bottom_browse_img = NULL;
lv_obj_t * ui_party_mode_bottom_receiver = NULL;
lv_obj_t * ui_party_mode_bottom_receiver_label = NULL;
#if 0
lv_obj_t * ui_party_mode_menu_panel = NULL;
lv_obj_t * ui_party_mode_menu_panel_bar = NULL;
lv_obj_t * ui_party_mode_menu_panel_bar_img = NULL;
lv_obj_t * ui_party_mode_menu_panel_button = NULL;
lv_obj_t * ui_party_mode_menu_panel_button_device = NULL;
lv_obj_t * ui_party_mode_menu_panel_button_device_img = NULL;
lv_obj_t * ui_party_mode_menu_panel_button_search = NULL;
lv_obj_t * ui_party_mode_menu_panel_button_search_img = NULL;
lv_obj_t * ui_party_mode_menu_panel_content = NULL;
#endif

lv_obj_t * ui_party_mode_select_mode = NULL;
lv_obj_t * ui_party_mode_select_mode_bg = NULL;
lv_obj_t * ui_party_mode_select_mode_title = NULL;
lv_obj_t * ui_party_mode_select_mode_title_text = NULL;
lv_obj_t * ui_party_mode_select_mode_button = NULL;
lv_obj_t * ui_party_mode_select_mode_button_broadcast = NULL;
lv_obj_t * ui_party_mode_select_mode_button_broadcast_text = NULL;
lv_obj_t * ui_party_mode_select_mode_button_receiver = NULL;
lv_obj_t * ui_party_mode_select_mode_button_receiver_text = NULL;
lv_obj_t * ui_party_mode_broadcast = NULL;
lv_obj_t * ui_party_mode_broadcast_source = NULL;
//lv_obj_t * ui_party_mode_broadcast_select_source = NULL;
lv_obj_t * ui_party_mode_receiver = NULL;
lv_obj_t * ui_party_mode_receiver_menu = NULL;

lv_timer_t  * ui_party_mode_timer = NULL;


__u32 PARTY_THEME_TEMP_PM_BO_OFF_ICON_BMP = 0;
__u32 PARTY_THEME_TEMP_PM_BO_OFF_ICON_A_BMP = 0;
__u32 PARTY_THEME_TEMP_PM_RE_ICON_BMP = 0;
__u32 PARTY_THEME_TEMP_PM_RE_ICON_A_BMP = 0;
__u32 PARTY_THEME_TEMP_PM_RE_LIST_BMP = 0;
__u32 PARTY_THEME_TEMP_PM_RE_LIST_N_BMP = 0;
__u32 PARTY_THEME_TEMP_PM_RE_LIST_CONNECT_BMP = 0;
__u32 PARTY_THEME_TEMP_PM_RE_SEARCH_BMP = 0;
__u32 PARTY_THEME_TEMP_PM_RE_SEARCH_N_BMP = 0;
__u32 PARTY_THEME_PM_BC_ICON_BMP = 0;
__u32 PARTY_THEME_PM_RE_ICON_BMP = 0;
__u32 PARTY_THEME_PM_RE_A_ICON_BMP = 0;

__u32 PARTY_THEME_COM_BOT_BG_BMP = 0;  
__u32 PARTY_THEME_COM_BOT_ICON_BG_A_N_BMP = 0;  
__u32 PARTY_THEME_COM_BOT_ICON_BG_A_P_S_BMP = 0; 
__u32 PARTY_THEME_COM_BOT_ICON_BG_B_N_BMP = 0;   
__u32 PARTY_THEME_COM_BOT_ICON_BG_B_P_S_BMP = 0; 
__u32 PARTY_THEME_COM_BOT_MEDIA_PAUSE_BMP = 0;   
__u32 PARTY_THEME_COM_BOT_MEDIA_PAUSE_D_BMP = 0;
__u32 PARTY_THEME_COM_BOT_MEDIA_PLAY_BMP = 0;    
__u32 PARTY_THEME_COM_BOT_MEDIA_PLAY_D_BMP = 0;
__u32 PARTY_THEME_COM_BOT_MEDIA_DOWN_N_BMP = 0;  
__u32 PARTY_THEME_COM_BOT_MEDIA_DOWN_P_S_BMP = 0;
__u32 PARTY_THEME_COM_BOT_MEDIA_UP_N_BMP = 0;    
__u32 PARTY_THEME_COM_BOT_MEDIA_UP_P_S_BMP = 0;

__u32 PARTY_THEME_COM_BOT_ICON_BG_A_L_N_BMP = 0;
__u32 PARTY_THEME_COM_BOT_ICON_BG_A_L_S_BMP = 0;

__u32 PARTY_THEME_COM_BOT_LIST_N_BMP = 0;
__u32 PARTY_THEME_COM_BOT_LIST_D_BMP = 0;

__u32 PARTY_THEME_COM_BOT_BROWSE_N_BMP = 0;
__u32 PARTY_THEME_COM_BOT_BROWSE_D_BMP = 0;

__u32 PARTY_THEME_COM_BOT_DIRECT_N_BMP = 0;
__u32 PARTY_THEME_COM_BOT_DIRECT_D_BMP = 0;

__u32 PARTY_THEME_COM_BOT_PHONE_A_N_BMP = 0;
__u32 PARTY_THEME_COM_BOT_PHONE_B_N_BMP = 0;

__u32 PARTY_THEME_COM_BOT_BT_CONNECT_N_BMP = 0;

__u32 PARTY_THEME_COM_BOT_RANDOM_A_N_BMP = 0;
__u32 PARTY_THEME_COM_BOT_RANDOM_A_D_BMP = 0;
__u32 PARTY_THEME_COM_BOT_RANDOM_B_N_BMP = 0;
__u32 PARTY_THEME_COM_BOT_RANDOM_B_D_BMP = 0;

__u32 PARTY_THEME_COM_BOT_REPEAT_A_N_BMP = 0;
__u32 PARTY_THEME_COM_BOT_REPEAT_A_D_BMP = 0;
__u32 PARTY_THEME_COM_BOT_REPEAT_B_N_BMP = 0;
__u32 PARTY_THEME_COM_BOT_REPEAT_B_D_BMP = 0;
__u32 PARTY_THEME_COM_BOT_REPEAT_C_N_BMP = 0;
__u32 PARTY_THEME_COM_BOT_REPEAT_C_D_BMP = 0;

__u32 PARTY_THEME_COM_DEVICE_CON_N_BMP = 0;
__u32 PARTY_THEME_COM_DEVICE_CON_D_BMP = 0;
__u32 PARTY_THEME_COM_DEVICE_CON_S_BMP = 0;
__u32 PARTY_THEME_COM_DEVICE_DEL_N_BMP = 0;

__u32 PARTY_THEME_TEMP_COM_LIST_ON_BMP = 0;
__u32 PARTY_THEME_TEMP_COM_LIST_OFF_BMP = 0;
__u32 PARTY_THEME_TEMP_COM_RIG_ICON_BG_N_BMP = 0;
__u32 PARTY_THEME_TEMP_COM_RIG_ICON_BG_P_BMP = 0;
__u32 PARTY_THEME_TEMP_COM_RIG_ICON_BG_S_BMP = 0;

///////////////////// TEST LVGL SETTINGS ////////////////////
#if LV_COLOR_DEPTH != 32
    #error "LV_COLOR_DEPTH should be 32bit to match SquareLine Studio's settings"
#endif
#if LV_COLOR_16_SWAP !=0
    #error "LV_COLOR_16_SWAP should be 0 to match SquareLine Studio's settings"
#endif

///////////////////// STATIC FUNCTION ////////////////////
static __s32 ui_party_mode_tips_dialog_create(ui_party_mode_para_t *party_mode_para, __s32 title_id, __s32 content_id, __u32 time_out, __s32 id)
{
	__s32 str[2] = {0};
	str[0] = title_id;
	str[1] = content_id;

	if(party_mode_para->dialog_win)
	{
		ui_dialog_destroy(party_mode_para->dialog_win);
		party_mode_para->dialog_win = NULL;
	}
        
	party_mode_para->dialog_win = ui_dialog_create(lv_layer_top(), APP_PARTY_MODE_DIALOG_ID, id, str,time_out, 0);	
    
	return EPDK_OK;
}

static __s32 ui_party_mode_tips_dialog_cancel(ui_party_mode_para_t *party_mode_para)
{    
	if(party_mode_para->dialog_win)
	{
		ui_dialog_destroy(party_mode_para->dialog_win);
		party_mode_para->dialog_win = NULL;
	}

	return EPDK_OK;
}

static __s32 ui_party_mode_tips_dialog_confirm(ui_party_mode_para_t *party_mode_para)
{
    //send mcu to broadcast role
    ui_party_comms_set_device_role(&party_mode_para->source_para->party_info,PARTY_D_ROLE_BROADCAST);
    
    ui_party_mode_tips_dialog_cancel(party_mode_para);
    
	return EPDK_OK;
}

static __s32 ui_party_mode_del_deivce_tips_dialog_create(ui_party_mode_para_t *party_mode_para, __s32 title_id, __s32 content_id, __u32 time_out, __s32 id)
{
	__s32 str[2] = {0};
	str[0] = title_id;
	str[1] = content_id;

	if(party_mode_para->del_device_dialog_win)
	{
		ui_dialog_destroy(party_mode_para->del_device_dialog_win);
		party_mode_para->del_device_dialog_win = NULL;
	}
        
	party_mode_para->del_device_dialog_win = ui_dialog_create(lv_layer_top(), APP_PARTY_MODE_DEL_DEIVCE_DIALOG_ID, id, str,time_out, 0);	
    
	return EPDK_OK;
}

static __s32 ui_party_mode_del_deivce_tips_dialog_cancel(ui_party_mode_para_t *party_mode_para)
{    
	if(party_mode_para->del_device_dialog_win)
	{
		ui_dialog_destroy(party_mode_para->del_device_dialog_win);
		party_mode_para->del_device_dialog_win = NULL;
	}

	return EPDK_OK;
}

static __s32 ui_party_mode_del_deivce_tips_dialog_confirm(ui_party_mode_para_t *party_mode_para)
{
    ui_party_comms_set_rc_delete_known_device(&party_mode_para->source_para->party_info, party_mode_para->source_para->party_info.rc_info.known_list[party_mode_para->delete_index].address, party_mode_para->delete_index + 1);
    party_mode_para->delete_index = 0;
    
    ui_party_mode_del_deivce_tips_dialog_cancel(party_mode_para);
    if(party_mode_para->win_type == PARTY_WIN_KNOWN_DEVICE)
    {
        ui_party_mode_list_folder_reinit(party_mode_para, ui_party_mode_menu, party_mode_para->source_para->party_info.rc_info.known_count);
    }

	return EPDK_OK;
}

//menu function
static __s32 ui_party_mode_get_item_type(__lv_draw_para_t *draw_param)
{
	__s32 ret = SOURCE_ITEM_TYPE_ENTER;
	__s32 index = draw_param->index;
	ui_party_mode_para_t * party_mode_para = (ui_party_mode_para_t *)draw_param->attr;

    switch(party_mode_para->win_type)
    {    
        case PARTY_WIN_DEVICE_LIST:
        {
            ret = SOURCE_ITEM_TYPE_DEVICE_LIST;
        }
        break;

        case PARTY_WIN_KNOWN_DEVICE:
        {            
            ret = SOURCE_ITEM_TYPE_BT_DEVICE;
        }
        break;

        case PARTY_WIN_MENU:
        {            
            ret = SOURCE_ITEM_TYPE_TEXT_ENTER;
        }
        break;
    }

	return ret; 
}

static __s32 ui_party_mode_draw_listview_item_text(__lv_draw_para_t *draw_param, lv_obj_t * text, __u8 item_type)
{
	int ret;
	__s32 item_index = draw_param->index;
	__u8 ucStringBuf[128 + 1]={0};
	ui_party_mode_para_t * party_mode_para = (ui_party_mode_para_t *)draw_param->attr;

	__party_mode_msg("win_type = %d, item_index = %d\n", party_mode_para->win_type, item_index);

    switch(party_mode_para->win_type)
   {    
        case PARTY_WIN_DEVICE_LIST:
        {
            if(item_type == SOURCE_ITEM_TEXT_TYPE_TEXT)
            {                
                lv_label_set_text(text, party_mode_para->source_para->party_info.rc_info.scan_list[item_index].name);
            }
        }
        break;

        case PARTY_WIN_KNOWN_DEVICE:
        {            
            if(item_type == SOURCE_ITEM_TEXT_TYPE_TEXT)
            {
                lv_label_set_text(text, party_mode_para->source_para->party_info.rc_info.known_list[item_index].name);
            }
        }
        break;

        case PARTY_WIN_MENU:
        {            
            if(item_type == SOURCE_ITEM_TEXT_TYPE_TEXT)
            {
                dsk_langres_get_menu_text(party_mode_item_string_res_id[item_index], ucStringBuf, 128);
                
                lv_label_set_text(text, ucStringBuf);
            }
            else if(item_type == SOURCE_ITEM_TEXT_TYPE_CONTENT)
            {
                switch(item_index)
                {
                    case PARTY_MENU_MODE:
                    {
                        if(party_mode_para->source_para->party_info.role == PARTY_ROLE_BROADCAST)
                            dsk_langres_get_menu_text(STRING_ROOT_BROADCAST, ucStringBuf, 128);
                        else
                            dsk_langres_get_menu_text(STRING_ROOT_RECEIVER, ucStringBuf, 128);
                        
                        lv_label_set_text(text, ucStringBuf);
                    }
                    break;
                
                    case PARTY_MENU_SOURCE:
                    {
                        if(party_mode_para->source_para->party_info.bc_info.source == PARTY_D_SOURCE_USB)
                            lv_label_set_text(text, "USB");
                        else if(party_mode_para->source_para->party_info.bc_info.source == PARTY_D_SOURCE_BT_AUDIO)
                            lv_label_set_text(text, "Bluetooth");
                        else if(party_mode_para->source_para->party_info.bc_info.source == PARTY_D_SOURCE_AUXIN)
                            lv_label_set_text(text, "AUX-IN");
                    }
                    break;
                
                    case PARTY_MENU_DEVICE_NAME:
                    {
                        lv_label_set_text(text, party_mode_para->source_para->party_info.own_info.name);
                    }
                    break;
                }
            }
        }
        break;
    }
    
	_ui_set_text_shadow_scroll(text, GUI_WHITE, GUI_BLACK, 3, 3);
				
	return EPDK_OK;
}

static __s32 ui_party_mode_draw_listview_item_icon(ui_party_mode_para_t * party_mode_para, lv_obj_t * icon, __u32 index, bool focus)
{
	__s32 ret;
    
	return ret;
}

static void * ui_party_mode_draw_item(__lv_draw_para_t *draw_param)
{	
	__s32 ret;
    __s32 item_type;
	ui_party_mode_para_t * party_mode_para = (ui_party_mode_para_t *)draw_param->attr;
	
	__party_mode_msg("draw_param->index = %d, item_obj = 0x%x\n", draw_param->index, party_mode_para->ui_list_para.item_obj);
	lv_obj_t * ui_list_item = lv_obj_create(party_mode_para->ui_list_para.item_obj);
    lv_obj_remove_style_all(ui_list_item);
	lv_obj_set_width(ui_list_item, party_mode_para->ui_list_para.item_width);
	lv_obj_set_height(ui_list_item, party_mode_para->ui_list_para.item_height);
	lv_obj_clear_flag(ui_list_item, LV_OBJ_FLAG_SCROLLABLE | LV_OBJ_FLAG_CLICK_FOCUSABLE);	  /// Flags
    lv_obj_add_flag(ui_list_item, LV_OBJ_FLAG_EVENT_BUBBLE | LV_OBJ_FLAG_GESTURE_BUBBLE);      /// Flags
	lv_obj_set_style_radius(ui_list_item, 0, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_bg_color(ui_list_item, lv_color_hex(0x000000), LV_PART_MAIN | LV_STATE_DEFAULT);
	lv_obj_set_style_bg_opa(ui_list_item, 255, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_border_width(ui_list_item, 2, LV_PART_MAIN | LV_STATE_FOCUSED);
    lv_obj_set_style_border_color(ui_list_item, lv_color_hex(0xff0000), LV_PART_MAIN | LV_STATE_FOCUSED);
    lv_obj_set_style_border_opa(ui_list_item, 255, LV_PART_MAIN | LV_STATE_FOCUSED);

    lv_obj_t * ui_list_item_panel = lv_obj_create(ui_list_item);
    lv_obj_remove_style_all(ui_list_item_panel);
    lv_obj_set_x(ui_list_item_panel, lv_pct(1));
    lv_obj_set_height(ui_list_item_panel, lv_pct(95));
    lv_obj_set_width(ui_list_item_panel, lv_pct(97));
	lv_obj_set_align(ui_list_item_panel, LV_ALIGN_LEFT_MID);
    lv_obj_add_flag(ui_list_item_panel, LV_OBJ_FLAG_CLICKABLE | LV_OBJ_FLAG_EVENT_BUBBLE | LV_OBJ_FLAG_GESTURE_BUBBLE);      /// Flags
    lv_obj_clear_flag(ui_list_item_panel, LV_OBJ_FLAG_SCROLLABLE | LV_OBJ_FLAG_CLICK_FOCUSABLE);      /// Flags
    lv_obj_set_style_bg_color(ui_list_item_panel, lv_color_hex(0x303030), LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_bg_opa(ui_list_item_panel, 255, LV_PART_MAIN | LV_STATE_DEFAULT);
    //lv_obj_set_style_bg_image_src(ui_list_item_panel, ui_party_mode_get_res(party_mode_para, RIG_THMEM_TEMP_COM_LIST_BG_BMP), LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_border_width(ui_list_item_panel, 0, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_bg_color(ui_list_item_panel, lv_color_hex(0x303030), LV_PART_MAIN | LV_STATE_PRESSED);
    lv_obj_set_style_bg_opa(ui_list_item_panel, 255, LV_PART_MAIN | LV_STATE_PRESSED);
    lv_obj_set_style_bg_grad_color(ui_list_item_panel, lv_color_hex(0xD90011), LV_PART_MAIN | LV_STATE_PRESSED);
    lv_obj_set_style_bg_main_stop(ui_list_item_panel, 150, LV_PART_MAIN | LV_STATE_PRESSED);
    lv_obj_set_style_bg_grad_stop(ui_list_item_panel, 255, LV_PART_MAIN | LV_STATE_PRESSED);
    lv_obj_set_style_bg_grad_dir(ui_list_item_panel, LV_GRAD_DIR_VER, LV_PART_MAIN | LV_STATE_PRESSED);
    
    lv_obj_t * ui_list_item_text_panel = lv_obj_create(ui_list_item_panel);
    lv_obj_remove_style_all(ui_list_item_text_panel);
	lv_obj_set_x(ui_list_item_text_panel, lv_pct(3));
    lv_obj_set_height(ui_list_item_text_panel, lv_pct(100));
    lv_obj_set_width(ui_list_item_text_panel, lv_pct(80));
	lv_obj_set_align(ui_list_item_text_panel, LV_ALIGN_LEFT_MID);
    lv_obj_clear_flag(ui_list_item_text_panel, LV_OBJ_FLAG_CLICKABLE | LV_OBJ_FLAG_SCROLLABLE);      /// Flags
    lv_obj_set_style_bg_opa(ui_list_item_text_panel, 0, LV_PART_MAIN | LV_STATE_DEFAULT);
    
	lv_obj_t * ui_list_item_text = lv_label_create(ui_list_item_text_panel);
	lv_obj_set_width(ui_list_item_text, lv_pct(100));//lv_pct(89)
	lv_obj_set_height(ui_list_item_text, LV_SIZE_CONTENT);	  /// 0
	lv_obj_set_align(ui_list_item_text, LV_ALIGN_LEFT_MID);
	lv_label_set_long_mode(ui_list_item_text, LV_LABEL_LONG_CLIP);
	lv_obj_set_style_text_color(ui_list_item_text, lv_color_hex(0xFFFFFF), LV_PART_MAIN | LV_STATE_DEFAULT);
	lv_obj_set_style_text_opa(ui_list_item_text, 255, LV_PART_MAIN | LV_STATE_DEFAULT);
	lv_obj_set_style_text_font(ui_list_item_text, lv_font_medium.font, LV_PART_MAIN | LV_STATE_DEFAULT);
	ui_party_mode_draw_listview_item_text(draw_param, ui_list_item_text, SOURCE_ITEM_TEXT_TYPE_TEXT);	

    item_type = ui_party_mode_get_item_type(draw_param);

    lv_obj_t * ui_list_item_content_panel = lv_obj_create(ui_list_item_panel);
    lv_obj_remove_style_all(ui_list_item_content_panel);
    lv_obj_set_width(ui_list_item_content_panel, lv_pct(20));
    lv_obj_set_height(ui_list_item_content_panel, lv_pct(100));
    lv_obj_set_align(ui_list_item_content_panel, LV_ALIGN_RIGHT_MID);
    lv_obj_clear_flag(ui_list_item_content_panel, LV_OBJ_FLAG_SCROLLABLE | LV_OBJ_FLAG_CLICK_FOCUSABLE);        /// Flags
    lv_obj_add_flag(ui_list_item_content_panel, LV_OBJ_FLAG_EVENT_BUBBLE | LV_OBJ_FLAG_GESTURE_BUBBLE);    /// Flags
    lv_obj_set_style_bg_opa(ui_list_item_content_panel, 0, LV_PART_MAIN | LV_STATE_DEFAULT);

    switch(item_type)
    {
        case SOURCE_ITEM_TYPE_ENTER:
        {
            lv_obj_t * ui_list_item_enter = lv_image_create(ui_list_item_content_panel);
            lv_image_set_src(ui_list_item_enter, ui_party_mode_get_res(party_mode_para, PARTY_TEMP_COM_MENU_ENTER_BMP));
            lv_obj_set_width(ui_list_item_enter, LV_SIZE_CONTENT);   /// 1
            lv_obj_set_height(ui_list_item_enter, LV_SIZE_CONTENT);    /// 1
            lv_obj_set_x(ui_list_item_enter, lv_pct(30));    /// 1
            lv_obj_set_align(ui_list_item_enter, LV_ALIGN_CENTER);
            lv_obj_remove_flag(ui_list_item_enter, LV_OBJ_FLAG_SCROLLABLE);      /// Flags
		}
        break;

        case SOURCE_ITEM_TYPE_ARROW:
        {
		    lv_obj_t * ui_list_item_arrow = lv_obj_create(ui_list_item_content_panel);
            lv_obj_set_width(ui_list_item_arrow, 118);
            lv_obj_set_height(ui_list_item_arrow, 69);
            lv_obj_set_align(ui_list_item_arrow, LV_ALIGN_CENTER);
            lv_obj_remove_flag(ui_list_item_arrow, LV_OBJ_FLAG_SCROLLABLE | LV_OBJ_FLAG_CLICK_FOCUSABLE);      /// Flags
            lv_obj_add_flag(ui_list_item_arrow, LV_OBJ_FLAG_CHECKABLE);      /// Flags
            lv_obj_set_style_bg_color(ui_list_item_arrow, lv_color_hex(0xFFFFFF), LV_PART_MAIN | LV_STATE_DEFAULT);
            lv_obj_set_style_bg_opa(ui_list_item_arrow, 0, LV_PART_MAIN | LV_STATE_DEFAULT);
            lv_obj_set_style_bg_image_src(ui_list_item_arrow, ui_party_mode_get_res(party_mode_para, PARTY_THEME_TEMP_COM_LIST_OFF_BMP),
                                          LV_PART_MAIN | LV_STATE_DEFAULT);
            lv_obj_set_style_border_width(ui_list_item_arrow, 0, LV_PART_MAIN | LV_STATE_DEFAULT);
            lv_obj_set_style_bg_image_src(ui_list_item_arrow, ui_party_mode_get_res(party_mode_para, PARTY_THEME_TEMP_COM_LIST_ON_BMP),
                                  LV_PART_MAIN | LV_STATE_CHECKED);

            lv_obj_t * ui_list_item_arrow_switch = lv_switch_create(ui_list_item_arrow);
            lv_obj_set_width(ui_list_item_arrow_switch, 90);
            lv_obj_set_height(ui_list_item_arrow_switch, 45);
            lv_obj_set_align(ui_list_item_arrow_switch, LV_ALIGN_CENTER);
            lv_obj_remove_flag(ui_list_item_arrow_switch, LV_OBJ_FLAG_CLICK_FOCUSABLE);      /// Flags
            lv_obj_add_flag(ui_list_item_arrow_switch, LV_OBJ_FLAG_EVENT_BUBBLE);    /// Flags
            lv_obj_set_style_bg_color(ui_list_item_arrow_switch, lv_color_hex(0x272727), LV_PART_MAIN | LV_STATE_DEFAULT);
            lv_obj_set_style_bg_opa(ui_list_item_arrow_switch, 0, LV_PART_MAIN | LV_STATE_DEFAULT);
            lv_obj_set_style_bg_color(ui_list_item_arrow_switch, lv_color_hex(0x222121), LV_PART_MAIN | LV_STATE_CHECKED);
            lv_obj_set_style_bg_opa(ui_list_item_arrow_switch, 0, LV_PART_MAIN | LV_STATE_CHECKED);
            
            lv_obj_set_style_bg_color(ui_list_item_arrow_switch, lv_color_hex(0x272727), LV_PART_INDICATOR | LV_STATE_DEFAULT);
            lv_obj_set_style_bg_opa(ui_list_item_arrow_switch, 0, LV_PART_INDICATOR | LV_STATE_DEFAULT);
            lv_obj_set_style_bg_color(ui_list_item_arrow_switch, lv_color_hex(0x222121), LV_PART_INDICATOR | LV_STATE_CHECKED);
            lv_obj_set_style_bg_opa(ui_list_item_arrow_switch, 0, LV_PART_INDICATOR | LV_STATE_CHECKED);

            lv_obj_set_style_bg_color(ui_list_item_arrow_switch, lv_color_hex(0x575757), LV_PART_KNOB | LV_STATE_DEFAULT);
            lv_obj_set_style_bg_opa(ui_list_item_arrow_switch, 255, LV_PART_KNOB | LV_STATE_DEFAULT);
            lv_obj_set_style_bg_color(ui_list_item_arrow_switch, lv_color_hex(0xFFFFFF), LV_PART_KNOB | LV_STATE_CHECKED);
            lv_obj_set_style_bg_opa(ui_list_item_arrow_switch, 255, LV_PART_KNOB | LV_STATE_CHECKED);
		}
        break;

        case SOURCE_ITEM_TYPE_TEXT:
        {
            lv_obj_t * ui_list_item_content_text = lv_label_create(ui_list_item_content_panel);
            lv_obj_set_width(ui_list_item_content_text, LV_SIZE_CONTENT);   /// 1
            lv_obj_set_height(ui_list_item_content_text, LV_SIZE_CONTENT);    /// 1
            lv_obj_set_y(ui_list_item_content_text, 0);
            lv_obj_set_align(ui_list_item_content_text, LV_ALIGN_CENTER);
            lv_obj_set_style_text_color(ui_list_item_content_text, lv_color_hex(0x909090), LV_PART_MAIN | LV_STATE_DEFAULT);
            lv_obj_set_style_text_opa(ui_list_item_content_text, 255, LV_PART_MAIN | LV_STATE_DEFAULT);
            lv_obj_set_style_text_font(ui_list_item_content_text, lv_font_small.font, LV_PART_MAIN | LV_STATE_DEFAULT);

            ui_party_mode_draw_listview_item_text(draw_param, ui_list_item_content_text, SOURCE_ITEM_TEXT_TYPE_CONTENT); 
		}
        break;
        
        case SOURCE_ITEM_TYPE_TEXT_ENTER:
        {                
            lv_obj_set_width(ui_list_item_content_panel, lv_pct(30));

            lv_obj_t * ui_list_item_content_text = lv_label_create(ui_list_item_content_panel);
            lv_obj_set_width(ui_list_item_content_text, LV_SIZE_CONTENT);   /// 1
            lv_obj_set_height(ui_list_item_content_text, LV_SIZE_CONTENT);    /// 1
            lv_obj_set_x(ui_list_item_content_text, -15);
            lv_obj_set_align(ui_list_item_content_text, LV_ALIGN_RIGHT_MID);
            lv_label_set_long_mode(ui_list_item_content_text, LV_LABEL_LONG_DOT);//LV_LABEL_LONG_SCROLL_CIRCULAR
            lv_obj_set_style_text_color(ui_list_item_content_text, lv_color_hex(0x909090), LV_PART_MAIN | LV_STATE_DEFAULT);
            lv_obj_set_style_text_opa(ui_list_item_content_text, 255, LV_PART_MAIN | LV_STATE_DEFAULT);
            lv_obj_set_style_text_font(ui_list_item_content_text, lv_font_small.font, LV_PART_MAIN | LV_STATE_DEFAULT);
            lv_obj_set_style_text_align(ui_list_item_content_text, LV_TEXT_ALIGN_RIGHT, LV_PART_MAIN | LV_STATE_DEFAULT);

            if(lv_label_get_long_mode(ui_list_item_content_text) == LV_LABEL_LONG_DOT)
                lv_obj_update_layout(ui_list_item_content_text);

            ui_party_mode_draw_listview_item_text(draw_param, ui_list_item_content_text, SOURCE_ITEM_TEXT_TYPE_CONTENT);  
        
            lv_obj_t * ui_list_item_enter = lv_image_create(ui_list_item_content_panel);
            lv_image_set_src(ui_list_item_enter, ui_party_mode_get_res(party_mode_para, PARTY_TEMP_COM_MENU_ENTER_BMP));
            lv_obj_set_width(ui_list_item_enter, LV_SIZE_CONTENT);   /// 1
            lv_obj_set_height(ui_list_item_enter, LV_SIZE_CONTENT);    /// 1
            lv_obj_set_align(ui_list_item_enter, LV_ALIGN_RIGHT_MID);
            lv_obj_remove_flag(ui_list_item_enter, LV_OBJ_FLAG_SCROLLABLE);      /// Flags
        }
        break;

        case SOURCE_ITEM_TYPE_BT_DEVICE:
        {
            lv_obj_set_width(ui_list_item_content_panel, lv_pct(30));

            lv_obj_t * ui_list_item_device_panel = lv_obj_create(ui_list_item_content_panel);
            lv_obj_remove_style_all(ui_list_item_device_panel);
            lv_obj_set_width(ui_list_item_device_panel, lv_pct(100));
            lv_obj_set_height(ui_list_item_device_panel, lv_pct(100));
            lv_obj_set_align(ui_list_item_device_panel, LV_ALIGN_RIGHT_MID);
            lv_obj_set_flex_flow(ui_list_item_device_panel, LV_FLEX_FLOW_ROW);
            lv_obj_set_flex_align(ui_list_item_device_panel, LV_FLEX_ALIGN_SPACE_EVENLY, LV_FLEX_ALIGN_CENTER, LV_FLEX_ALIGN_CENTER);//LV_FLEX_ALIGN_SPACE_BETWEEN
            lv_obj_clear_flag(ui_list_item_device_panel, LV_OBJ_FLAG_SCROLLABLE);       /// Flags
            lv_obj_add_flag(ui_list_item_device_panel, LV_OBJ_FLAG_ADV_HITTEST);    /// Flags
            lv_obj_set_style_bg_opa(ui_list_item_device_panel, 0, LV_PART_MAIN | LV_STATE_DEFAULT);
            //lv_obj_set_style_pad_column(ui_list_item_device_panel, 10, LV_PART_MAIN | LV_STATE_DEFAULT);

            lv_obj_t * ui_list_item_device_connect = lv_obj_create(ui_list_item_device_panel);
            lv_obj_remove_style_all(ui_list_item_device_connect);
            lv_obj_set_width(ui_list_item_device_connect, lv_pct(40));
            lv_obj_set_height(ui_list_item_device_connect, lv_pct(80));
            lv_obj_set_align(ui_list_item_device_connect, LV_ALIGN_CENTER);
            lv_obj_clear_flag(ui_list_item_device_connect, LV_OBJ_FLAG_SCROLLABLE);       /// Flags
            lv_obj_add_flag(ui_list_item_device_connect, LV_OBJ_FLAG_EVENT_BUBBLE);    /// Flags
            lv_obj_set_style_bg_color(ui_list_item_device_connect, lv_color_hex(0x000000), LV_PART_MAIN | LV_STATE_DEFAULT);
            lv_obj_set_style_bg_opa(ui_list_item_device_connect, 255, LV_PART_MAIN | LV_STATE_DEFAULT);
            lv_obj_set_style_radius(ui_list_item_device_connect, 10, LV_PART_MAIN | LV_STATE_DEFAULT);
            if(party_mode_para->win_type == PARTY_WIN_KNOWN_DEVICE)
            {
                __party_mode_msg("connect_status[%d]=%d\n",draw_param->index, party_mode_para->source_para->party_info.rc_info.known_list[draw_param->index].connect_status);
                if(party_mode_para->source_para->party_info.rc_info.known_list[draw_param->index].connect_status == 1)
                    lv_obj_set_style_bg_image_src(ui_list_item_device_connect, ui_party_mode_get_res(party_mode_para, PARTY_THEME_COM_DEVICE_CON_N_BMP), LV_PART_MAIN | LV_STATE_DEFAULT);
                else
                    lv_obj_set_style_bg_image_src(ui_list_item_device_connect, ui_party_mode_get_res(party_mode_para, PARTY_THEME_COM_DEVICE_CON_D_BMP), LV_PART_MAIN | LV_STATE_DEFAULT);
            }
            
            lv_obj_set_style_bg_color(ui_list_item_device_connect, lv_color_hex(0x000000), LV_PART_MAIN | LV_STATE_PRESSED);
            lv_obj_set_style_bg_opa(ui_list_item_device_connect, 255, LV_PART_MAIN | LV_STATE_PRESSED);
            lv_obj_set_style_bg_grad_color(ui_list_item_device_connect, lv_color_hex(0xD90011), LV_PART_MAIN | LV_STATE_PRESSED);
            lv_obj_set_style_bg_main_stop(ui_list_item_device_connect, 0, LV_PART_MAIN | LV_STATE_PRESSED);
            lv_obj_set_style_bg_grad_stop(ui_list_item_device_connect, 255, LV_PART_MAIN | LV_STATE_PRESSED);
            lv_obj_set_style_bg_grad_dir(ui_list_item_device_connect, LV_GRAD_DIR_VER, LV_PART_MAIN | LV_STATE_PRESSED);

            lv_obj_t * ui_list_item_device_delect = lv_obj_create(ui_list_item_device_panel);
            lv_obj_remove_style_all(ui_list_item_device_delect);
            lv_obj_set_width(ui_list_item_device_delect, lv_pct(40));
            lv_obj_set_height(ui_list_item_device_delect, lv_pct(80));
            lv_obj_set_align(ui_list_item_device_delect, LV_ALIGN_CENTER);
            lv_obj_clear_flag(ui_list_item_device_delect, LV_OBJ_FLAG_SCROLLABLE);       /// Flags
            lv_obj_add_flag(ui_list_item_device_delect, LV_OBJ_FLAG_EVENT_BUBBLE);    /// Flags
            lv_obj_set_style_bg_color(ui_list_item_device_delect, lv_color_hex(0x000000), LV_PART_MAIN | LV_STATE_DEFAULT);
            lv_obj_set_style_bg_opa(ui_list_item_device_delect, 255, LV_PART_MAIN | LV_STATE_DEFAULT);
            lv_obj_set_style_radius(ui_list_item_device_delect, 10, LV_PART_MAIN | LV_STATE_DEFAULT);
            lv_obj_set_style_bg_image_src(ui_list_item_device_delect, ui_party_mode_get_res(party_mode_para, PARTY_THEME_COM_DEVICE_DEL_N_BMP), LV_PART_MAIN | LV_STATE_DEFAULT);

            lv_obj_set_style_bg_color(ui_list_item_device_delect, lv_color_hex(0x000000), LV_PART_MAIN | LV_STATE_PRESSED);
            lv_obj_set_style_bg_opa(ui_list_item_device_delect, 255, LV_PART_MAIN | LV_STATE_PRESSED);
            lv_obj_set_style_bg_grad_color(ui_list_item_device_delect, lv_color_hex(0xD90011), LV_PART_MAIN | LV_STATE_PRESSED);
            lv_obj_set_style_bg_main_stop(ui_list_item_device_delect, 0, LV_PART_MAIN | LV_STATE_PRESSED);
            lv_obj_set_style_bg_grad_stop(ui_list_item_device_delect, 255, LV_PART_MAIN | LV_STATE_PRESSED);
            lv_obj_set_style_bg_grad_dir(ui_list_item_device_delect, LV_GRAD_DIR_VER, LV_PART_MAIN | LV_STATE_PRESSED);

            lv_obj_add_event_cb(ui_list_item_device_panel, ui_event_party_mode_device_cb, LV_EVENT_ALL, party_mode_para);
        }
        break;

        case SOURCE_ITEM_TYPE_DEVICE_LIST:
        {
            lv_obj_t * ui_list_item_device_panel = lv_obj_create(ui_list_item_content_panel);
            lv_obj_remove_style_all(ui_list_item_device_panel);
            lv_obj_set_width(ui_list_item_device_panel, lv_pct(100));
            lv_obj_set_height(ui_list_item_device_panel, lv_pct(100));
            lv_obj_set_align(ui_list_item_device_panel, LV_ALIGN_RIGHT_MID);
            lv_obj_clear_flag(ui_list_item_device_panel, LV_OBJ_FLAG_SCROLLABLE);       /// Flags
            lv_obj_add_flag(ui_list_item_device_panel, LV_OBJ_FLAG_ADV_HITTEST);    /// Flags
            lv_obj_set_style_bg_opa(ui_list_item_device_panel, 0, LV_PART_MAIN | LV_STATE_DEFAULT);
            //lv_obj_set_style_pad_column(ui_list_item_device_panel, 10, LV_PART_MAIN | LV_STATE_DEFAULT);

            lv_obj_t * ui_list_item_device_scan = lv_spinner_create(ui_list_item_device_panel);
            //lv_spinner_set_anim_params(ui_list_item_device_scan, 1000, 90);
            lv_obj_set_width(ui_list_item_device_scan, 64);//lv_pct(100)
            lv_obj_set_height(ui_list_item_device_scan, lv_pct(80));
            lv_obj_set_align(ui_list_item_device_scan, LV_ALIGN_CENTER);
            lv_obj_add_flag(ui_list_item_device_scan, LV_OBJ_FLAG_HIDDEN);      /// Flags
            lv_obj_remove_flag(ui_list_item_device_scan, LV_OBJ_FLAG_CLICKABLE);      /// Flags
            lv_obj_set_style_arc_color(ui_list_item_device_scan, lv_color_hex(0x4040FF), LV_PART_MAIN | LV_STATE_DEFAULT);
            lv_obj_set_style_arc_opa(ui_list_item_device_scan, 0, LV_PART_MAIN | LV_STATE_DEFAULT);
            
            lv_obj_set_style_arc_color(ui_list_item_device_scan, lv_color_hex(0xFFFFFF), LV_PART_INDICATOR | LV_STATE_DEFAULT);
            lv_obj_set_style_arc_opa(ui_list_item_device_scan, 255, LV_PART_INDICATOR | LV_STATE_DEFAULT);
            lv_obj_set_style_arc_width(ui_list_item_device_scan, 5, LV_PART_INDICATOR | LV_STATE_DEFAULT);

            lv_obj_t * ui_list_item_device_connected = lv_imgbtn_create(ui_list_item_device_panel);
            lv_imgbtn_set_src(ui_list_item_device_connected, LV_IMGBTN_STATE_RELEASED, NULL, ui_party_mode_get_res(party_mode_para, PARTY_THEME_COM_DEVICE_CON_S_BMP), NULL);
            lv_imgbtn_set_src(ui_list_item_device_connected, LV_IMGBTN_STATE_PRESSED, NULL, ui_party_mode_get_res(party_mode_para, PARTY_THEME_COM_DEVICE_CON_S_BMP), NULL);
            lv_obj_set_height(ui_list_item_device_connected, 49);
            lv_obj_set_width(ui_list_item_device_connected, LV_SIZE_CONTENT);   /// 1
            lv_obj_set_align(ui_list_item_device_connected, LV_ALIGN_CENTER);
            lv_obj_add_flag(ui_list_item_device_connected, LV_OBJ_FLAG_HIDDEN);  /// Flags
        }
        break;
    }
    
	lv_obj_add_event_cb(ui_list_item, ui_event_party_mode_menu_item_cb, LV_EVENT_ALL, party_mode_para);

	return (void *)ui_list_item;
}


static void ui_party_mode_listbar_init(ui_party_mode_para_t * party_mode_para)
{		
	if(!party_mode_para->ui_list_para.item_obj)
	{
		party_mode_para->menu_obj = ui_menu_create(&party_mode_para->ui_list_para, APP_PARTY_MODE_ID);

        if(party_mode_para->ui_list_para.item_cnt > 0)
        {
            ui_source_menu_sel_group_update(party_mode_para->source_para, &party_mode_para->ui_list_para, ui_party_mode_get_item_type, party_mode_para, SOURCE_MENU_GROUP_ADD, 0);
            ui_source_group_update(party_mode_para->source_para, 0);
        }
        else
        {
            ui_source_group_update(party_mode_para->source_para, 1);
        }

        if(party_mode_para->theme > THEME_NIGHT)
            traverse_children_with_callback(party_mode_para, party_mode_para->menu_obj, party_mode_para->theme_bmp_start, party_mode_para->theme_bmp_end, party_mode_para->theme_bmp_start, party_mode_para->theme_bmp_end, party_mode_para->ui_party_mode_res_para.lv_party_mode_icon, ui_party_mode_get_res, NULL, 0);
#ifdef USE_MENU_ANIM
        ui_menu_on_anim(&party_mode_para->ui_list_para, party_mode_para->menu_anim);
#endif
	}
}

static void ui_party_mode_listbar_uninit(ui_party_mode_para_t * party_mode_para)
{
	if(party_mode_para->ui_list_para.item_obj != NULL)
	{
		__party_mode_msg("rig_view_funtion_listbar_uninit:item_obj=0x%x\n", party_mode_para->ui_list_para.item_obj);
#ifdef USE_MENU_ANIM
        ui_menu_off_anim(party_mode_para->menu_anim);
#endif
        if(party_mode_para->ui_list_para.item_cnt > 0)
        {
            ui_source_menu_sel_group_update(party_mode_para->source_para, &party_mode_para->ui_list_para, ui_party_mode_get_item_type, party_mode_para, SOURCE_MENU_GROUP_REMOVE_ALL_FLAG_ALL, 0);
        }
        
		//lv_obj_clean(party_mode_para->ui_list_para.item_obj);//删除父对象下的子对象，不删除本身
		ui_menu_destroy(party_mode_para->menu_obj);
		party_mode_para->ui_list_para.item_obj = NULL;
	}
}

static void ui_party_mode_listbar_para_init(ui_party_mode_para_t * party_mode_para, void *menu_obj, __u32 item_cnt)
{
	party_mode_para->ui_list_para.menu_obj = menu_obj;
    party_mode_para->ui_list_para.list_attr = (void *)party_mode_para;
	party_mode_para->ui_list_para.lbar_draw = (__lv_draw_item)ui_party_mode_draw_item;
	party_mode_para->ui_list_para.item_cnt = item_cnt;
#ifdef USE_MENU_ANIM
    if(party_mode_para->win_type == PARTY_WIN_DEVICE_LIST || party_mode_para->win_type == PARTY_WIN_KNOWN_DEVICE)
        party_mode_para->ui_list_para.use_anim = 0;
    else
        party_mode_para->ui_list_para.use_anim = 1;
#endif
    __party_mode_msg("item_cnt=%d\n", item_cnt);
}

static void ui_party_mode_list_folder_reinit(ui_party_mode_para_t * party_mode_para, void *menu_obj, __u32 item_cnt)
{
	ui_party_mode_listbar_uninit(party_mode_para);
    if(item_cnt > 0)
    {
        ui_party_mode_listbar_para_init(party_mode_para, menu_obj, item_cnt);
    	ui_party_mode_listbar_init(party_mode_para);
    }
}

static void ui_party_mode_bottom_group_update(ui_party_mode_para_t * party_mode_para, __u8 flag, __u8 index, __u8 force)
{
    if(party_mode_para->bottom_fouse_flag == flag && !force)
    {
        __party_mode_msg("ui_party_mode_bottom_group_update cur is flag=%d, force=%d, not need change!!!!\n", flag, force);
        
        return;
    }
    
    party_mode_para->bottom_fouse_flag = flag;
    __party_mode_msg("party_mode_para->bottom_fouse_flag=%d, index=%d\n", party_mode_para->bottom_fouse_flag, index);

    if(flag == 1)
    {
        ui_source_sel_group_update(party_mode_para->source_para->ui_bottom, party_mode_para->bottom_fouse_index, 1);
        ui_source_group_update(party_mode_para->source_para, 0);
        party_mode_para->bottom_fouse_index = index;
    }
    else if(flag == 2)
    {
        party_mode_para->bottom_fouse_index = index;
        ui_source_group_update(party_mode_para->source_para, 0);
        ui_source_sel_group_update(party_mode_para->source_para->ui_bottom, 0, 0);
    }
    else
    {
        ui_source_group_update(party_mode_para->source_para, 1);
        ui_source_sel_group_update(party_mode_para->source_para->ui_bottom, 0, 0);
        party_mode_para->bottom_fouse_index = index;
    }
}

//win function
static void ui_party_mode_normal_win_open(ui_party_mode_para_t * party_mode_para)
{
    party_mode_para->win_type = PARTY_WIN_NONE;
}

static void ui_party_mode_normal_win_close(ui_party_mode_para_t * party_mode_para)
{

}

static void ui_party_mode_device_list_win_open(ui_party_mode_para_t * party_mode_para)
{
    party_mode_para->win_type = PARTY_WIN_DEVICE_LIST;
    //send scan start to mcu
    ui_party_comms_set_rc_scan_control(&party_mode_para->source_para->party_info,PARTY_SCAN_START);

    ui_source_title(party_mode_para->source_para, "Device List", 0);
    ui_source_sub_open(party_mode_para->source_para);
    ui_source_top_indicant(party_mode_para->source_para, SOURCE_INDICANT_SCAN, 0);

    ui_party_mode_menu_screen_init(party_mode_para);
    if(party_mode_para->source_para->party_info.rc_info.scan_count > 0)
    {
        ui_party_mode_listbar_para_init(party_mode_para, ui_party_mode_menu, party_mode_para->source_para->party_info.rc_info.scan_count);
        ui_party_mode_listbar_init(party_mode_para);
        ui_party_mode_bottom_group_update(party_mode_para, 2, 1, 0);
    }
    else
    {
        ui_party_mode_bottom_group_update(party_mode_para, 0, 1, 0);
    }
}

static void ui_party_mode_device_list_win_close(ui_party_mode_para_t * party_mode_para)
{
	//send scan stop to mcu
	ui_party_comms_set_rc_scan_control(&party_mode_para->source_para->party_info,PARTY_SCAN_STOP);
    
    ui_party_mode_bottom_group_update(party_mode_para, 1, 0, 0);
    ui_party_mode_listbar_uninit(party_mode_para);
    ui_party_mode_menu_screen_uninit(party_mode_para);

    ui_source_sub_close(party_mode_para->source_para);
    ui_source_top_indicant(party_mode_para->source_para, SOURCE_INDICANT_NONE, 0);
}

static void ui_party_mode_known_device_win_open(ui_party_mode_para_t * party_mode_para)
{
    party_mode_para->win_type = PARTY_WIN_KNOWN_DEVICE;

    ui_source_title(party_mode_para->source_para, "Known Device", 0);
    ui_source_sub_open(party_mode_para->source_para);

    ui_party_mode_menu_screen_init(party_mode_para);
    if(party_mode_para->source_para->party_info.rc_info.known_count > 0)
    {
        ui_party_mode_listbar_para_init(party_mode_para, ui_party_mode_menu, party_mode_para->source_para->party_info.rc_info.known_count);
        ui_party_mode_listbar_init(party_mode_para);
        ui_party_mode_bottom_group_update(party_mode_para, 2, 0, 0);
    }
    else
    {
        ui_party_mode_bottom_group_update(party_mode_para, 0, 0, 0);
    }
}

static void ui_party_mode_known_device_win_close(ui_party_mode_para_t * party_mode_para)
{
    ui_party_mode_bottom_group_update(party_mode_para, 1, 0, 0);
    ui_party_mode_listbar_uninit(party_mode_para);
    ui_party_mode_menu_screen_uninit(party_mode_para);

    ui_source_sub_close(party_mode_para->source_para);
}

static void ui_party_mode_menu_win_open(ui_party_mode_para_t * party_mode_para)
{
    party_mode_para->win_type = PARTY_WIN_MENU;

    ui_source_title(party_mode_para->source_para, "Menu", 0);
    ui_source_sub_open(party_mode_para->source_para);

    ui_party_mode_bottom_group_update(party_mode_para, 0, 0, 0);
    ui_party_mode_menu_screen_init(party_mode_para);
    ui_party_mode_listbar_para_init(party_mode_para, ui_party_mode_menu, PARTY_MENU_MAX);
    ui_party_mode_listbar_init(party_mode_para);
}

static void ui_party_mode_menu_win_close(ui_party_mode_para_t * party_mode_para)
{
    ui_party_mode_listbar_uninit(party_mode_para);
    ui_party_mode_menu_screen_uninit(party_mode_para);

    ui_source_sub_close(party_mode_para->source_para);
}

static void ui_party_mode_select_mode_win_open(ui_party_mode_para_t * party_mode_para)
{
    party_mode_para->win_type = PARTY_WIN_SELECT_MODE;

    ui_source_title(party_mode_para->source_para, "Party Mode", 0);
    ui_party_mode_select_mode_screen_init(party_mode_para);
    ui_source_sub_open(party_mode_para->source_para);
    ui_source_group_update(party_mode_para->source_para, 0);
}

static void ui_party_mode_select_mode_win_close(ui_party_mode_para_t * party_mode_para)
{
    ui_source_group_update(party_mode_para->source_para, 1);
    ui_party_mode_select_mode_screen_uninit(party_mode_para);
    ui_source_sub_close(party_mode_para->source_para);
}

static void ui_party_mode_broadcast_win_open(ui_party_mode_para_t * party_mode_para)
{
    reg_aux_para_t* last_aux_para;
    
    last_aux_para = (reg_aux_para_t*)dsk_reg_get_para_by_app(REG_APP_AUX);
    party_mode_para->win_type = PARTY_WIN_BROADCAST;

    ui_source_title(party_mode_para->source_para, "Source to Broadcast", 0);
    __party_mode_msg("party_mode_para->source_para->party_info.role=%d\n", party_mode_para->source_para->party_info.role);
    __party_mode_msg("party_mode_para->source_para->party_info.bc_info.source=%d\n", party_mode_para->source_para->party_info.bc_info.source);
    //if(party_mode_para->source_para->party_info.role != PARTY_ROLE_BROADCAST)
    //    app_root_send2mcu_party(UART_SEND_PARTY_MODE_ROLE, PARTY_ROLE_BROADCAST);
    ui_party_mode_broadcast_screen_init(party_mode_para);

    ui_source_sub_open(party_mode_para->source_para);
    ui_source_group_update(party_mode_para->source_para, 0);
}

static void ui_party_mode_broadcast_win_close(ui_party_mode_para_t * party_mode_para)
{
    ui_source_group_update(party_mode_para->source_para, 1);
    ui_party_mode_broadcast_screen_uninit(party_mode_para);

    ui_source_sub_close(party_mode_para->source_para);
}

static void ui_party_mode_receiver_win_open(ui_party_mode_para_t * party_mode_para)
{
    reg_aux_para_t* last_aux_para;
    
    last_aux_para = (reg_aux_para_t*)dsk_reg_get_para_by_app(REG_APP_AUX);
    party_mode_para->win_type = PARTY_WIN_RECEIVER;
    ui_party_mode_receiver_screen_init(party_mode_para);

    if(party_mode_para->bottom_fouse_flag == 0)
        ui_source_group_update(party_mode_para->source_para, 1);

    ui_source_title(party_mode_para->source_para, "Receiver", 1);
    __party_mode_msg("party_mode_para->source_para->party_info.role=%d\n", party_mode_para->source_para->party_info.role);
    __party_mode_msg("party_mode_para->source_para->party_info.bc_info.source=%d\n", party_mode_para->source_para->party_info.bc_info.source);
    //if(party_mode_para->source_para->party_info.role != PARTY_ROLE_RECEIVER)
    //    app_root_send2mcu_party(UART_SEND_PARTY_MODE_ROLE, PARTY_ROLE_RECEIVER);
}

static void ui_party_mode_receiver_win_close(ui_party_mode_para_t * party_mode_para)
{
    //ui_party_mode_listbar_uninit(party_mode_para);
    ui_party_mode_receiver_screen_uninit(party_mode_para);
    //if(!lv_obj_has_flag(party_mode_para->source_para->ui_rig_list_panel, LV_OBJ_FLAG_HIDDEN))
    //{
		//lv_imgbtn_set_state(party_mode_para->source_para->ui_top_right, LV_IMGBTN_STATE_RELEASED);
        //lv_obj_add_flag(party_mode_para->source_para->ui_rig_list_panel, LV_OBJ_FLAG_HIDDEN);
        //lv_obj_remove_flag(party_mode_para->source_para->ui_bottom, LV_OBJ_FLAG_HIDDEN);      /// Flags
    //}
}

static void ui_party_mode_switch_win(ui_party_mode_para_t * party_mode_para, __u32 win_type)
{		
	if(party_mode_para->win_type == win_type)
		return;

    __party_mode_msg("party_mode_para->win_type=%d, win_type=%d\n", party_mode_para->win_type, win_type);
	//close other win
	switch(party_mode_para->win_type)
	{
		case PARTY_WIN_NONE:	
		{
            ui_party_mode_normal_win_close(party_mode_para);
		}		
		break;
        
		case PARTY_WIN_SELECT_MODE:	
		{
            ui_party_mode_select_mode_win_close(party_mode_para);
		}		
		break;

		case PARTY_WIN_BROADCAST:	
		{
            ui_party_mode_broadcast_win_close(party_mode_para);
		}		
		break;

		case PARTY_WIN_RECEIVER:	
		{
            ui_party_mode_receiver_win_close(party_mode_para);
		}		
		break;

		case PARTY_WIN_DEVICE_LIST:	
		{
            ui_party_mode_device_list_win_close(party_mode_para);
		}		
		break;

		case PARTY_WIN_KNOWN_DEVICE:	
		{
            ui_party_mode_known_device_win_close(party_mode_para);
		}		
		break;

        case PARTY_WIN_MENU:	
		{
            ui_party_mode_menu_win_close(party_mode_para);
		}		
		break;
	}

	//open win by type
	switch(win_type)
	{
		case PARTY_WIN_NONE:	
		{
            ui_party_mode_normal_win_open(party_mode_para);
		}		
		break;
        
		case PARTY_WIN_SELECT_MODE:
		{
            ui_party_mode_select_mode_win_open(party_mode_para);
		}
        break;

		case PARTY_WIN_BROADCAST:	
		{
            ui_party_mode_broadcast_win_open(party_mode_para);
		}		
		break;

		case PARTY_WIN_RECEIVER:	
		{
            ui_party_mode_receiver_win_open(party_mode_para);
		}		
		break;

		case PARTY_WIN_DEVICE_LIST:	
		{
            ui_party_mode_device_list_win_open(party_mode_para);
		}		
		break;

		case PARTY_WIN_KNOWN_DEVICE:	
		{
            ui_party_mode_known_device_win_open(party_mode_para);
		}		
		break;

        case PARTY_WIN_MENU:	
		{
            ui_party_mode_menu_win_open(party_mode_para);
		}		
		break;
	}
}

static __s32 ui_party_mode_role_update(ui_party_mode_para_t * party_mode_para, __u8 role, __u8 bc_focus)
{
	root_app_para_t * root_para = app_root_get_para();//pmsg->p_arg;

    if(role == PARTY_ROLE_BROADCAST)
    {
        if(party_mode_para->win_type != PARTY_WIN_BROADCAST && bc_focus == 0)
        {
            ui_party_mode_switch_win(party_mode_para, PARTY_WIN_BROADCAST);
        }
        else
        {      
            __party_mode_msg("ui_party_mode_role_update source = 0x%x\n", party_mode_para->source_para->party_info.bc_info.source);
            if(party_mode_para->source_para->party_info.bc_info.source == PARTY_D_SOURCE_USB)
            {
                party_mode_para->source_para->party_info.bc_info.broadcast_source = 1;
                app_root_source_create(root_para,ID_WORK_SOURCE_USB);
            }
            else if(party_mode_para->source_para->party_info.bc_info.source == PARTY_D_SOURCE_BT_AUDIO)
            {
                party_mode_para->source_para->party_info.bc_info.broadcast_source = 1;
                app_root_source_create(root_para,ID_WORK_SOURCE_BT);
            }
            else if(party_mode_para->source_para->party_info.bc_info.source == PARTY_D_SOURCE_AUXIN)
            {
                party_mode_para->source_para->party_info.bc_info.broadcast_source = 1;
                app_root_source_create(root_para,ID_WORK_SOURCE_AUXIN);
            } 
            else
            {
                ui_party_mode_switch_win(party_mode_para, PARTY_WIN_BROADCAST);
            }
        }
    }
    else if(role == PARTY_ROLE_RECEIVER)
    {
        if(party_mode_para->source_para->party_info.rc_info.connect_status != PARTY_STATUS_DISCONNECTED)
        { //connected
            ui_party_mode_switch_win(party_mode_para, PARTY_WIN_RECEIVER);
        }
        else
        {//disconnected
            if(party_mode_para->source_para->party_info.rc_info.known_count>0)
            {//have known list, to enter known list
                ui_party_mode_switch_win(party_mode_para, PARTY_WIN_KNOWN_DEVICE);
            }
            else
            {//no known list, to enter scan list
                ui_party_mode_switch_win(party_mode_para, PARTY_WIN_DEVICE_LIST);
            }
        }
    }
}

static void ui_party_mode_night_theme(ui_party_mode_para_t * party_mode_para, __u32 theme)
{
	__party_mode_msg("ui_party_mode_night_theme\n");

    if(party_mode_para->theme == theme)
    {
        __party_mode_msg("cur is theme %d, not need change!!!!\n", theme);
        return;
    }
    
    if(theme == THEME_NIGHT)
    {
        PARTY_THEME_TEMP_PM_BO_OFF_ICON_BMP           = PARTY_TEMP_NIGHT_NIG_PM_BO_OFF_ICON_BMP;    
        PARTY_THEME_TEMP_PM_BO_OFF_ICON_A_BMP         = PARTY_TEMP_NIGHT_NIG_PM_BO_OFF_ICON_A_BMP;  
        PARTY_THEME_TEMP_PM_RE_ICON_BMP               = PARTY_TEMP_NIGHT_NIG_PM_RE_ICON_BMP;    
        PARTY_THEME_TEMP_PM_RE_ICON_A_BMP             = PARTY_TEMP_NIGHT_NIG_PM_RE_ICON_A_BMP;    
        PARTY_THEME_TEMP_PM_RE_LIST_BMP               = PARTY_TEMP_NIGHT_NIG_PM_RE_LIST_BMP;
        PARTY_THEME_TEMP_PM_RE_LIST_N_BMP             = PARTY_TEMP_NIGHT_NIG_PM_RE_LIST_N_BMP;
        PARTY_THEME_TEMP_PM_RE_LIST_CONNECT_BMP       = PARTY_TEMP_NIGHT_NIG_PM_RE_LIST_CONNECT_BMP;
        PARTY_THEME_TEMP_PM_RE_SEARCH_BMP             = PARTY_TEMP_NIGHT_NIG_PM_RE_SEARCH_BMP;
        PARTY_THEME_TEMP_PM_RE_SEARCH_N_BMP           = PARTY_TEMP_NIGHT_NIG_PM_RE_SEARCH_N_BMP;
        PARTY_THEME_PM_BC_ICON_BMP                    = PARTY_MODE_NIG_PM_BC_ICON_BMP;
        PARTY_THEME_PM_RE_ICON_BMP                    = PARTY_MODE_NIG_PM_RE_ICON_BMP;
        PARTY_THEME_PM_RE_A_ICON_BMP                  = PARTY_MODE_NIG_PM_RE_A_ICON_BMP;

        PARTY_THEME_COM_BOT_BG_BMP                    = PARTY_COMMON_NIG_COM_BOT_BG_BMP;  
        PARTY_THEME_COM_BOT_ICON_BG_A_N_BMP           = PARTY_COMMON_NIG_COM_BOT_ICON_BG_A_N_BMP;  
        PARTY_THEME_COM_BOT_ICON_BG_A_P_S_BMP         = PARTY_COMMON_NIG_COM_BOT_ICON_BG_A_P_S_BMP; 
        PARTY_THEME_COM_BOT_ICON_BG_B_N_BMP           = PARTY_COMMON_NIG_COM_BOT_ICON_BG_B_N_BMP;   
        PARTY_THEME_COM_BOT_ICON_BG_B_P_S_BMP         = PARTY_COMMON_NIG_COM_BOT_ICON_BG_B_P_S_BMP; 
        PARTY_THEME_COM_BOT_MEDIA_PAUSE_BMP           = PARTY_COMMON_NIG_COM_BOT_MEDIA_PAUSE_BMP;   
        PARTY_THEME_COM_BOT_MEDIA_PAUSE_D_BMP         = PARTY_COMMON_NIG_COM_BOT_MEDIA_PAUSE_D_BMP;
        PARTY_THEME_COM_BOT_MEDIA_PLAY_BMP            = PARTY_COMMON_NIG_COM_BOT_MEDIA_PLAY_BMP;    
        PARTY_THEME_COM_BOT_MEDIA_PLAY_D_BMP          = PARTY_COMMON_NIG_COM_BOT_MEDIA_PLAY_D_BMP;
        PARTY_THEME_COM_BOT_MEDIA_DOWN_N_BMP          = PARTY_COMMON_NIG_COM_BOT_MEDIA_DOWN_N_BMP;  
        PARTY_THEME_COM_BOT_MEDIA_DOWN_P_S_BMP        = PARTY_COMMON_NIG_COM_BOT_MEDIA_DOWN_P_S_BMP;
        PARTY_THEME_COM_BOT_MEDIA_UP_N_BMP            = PARTY_COMMON_NIG_COM_BOT_MEDIA_UP_N_BMP;    
        PARTY_THEME_COM_BOT_MEDIA_UP_P_S_BMP          = PARTY_COMMON_NIG_COM_BOT_MEDIA_UP_P_S_BMP;

        PARTY_THEME_COM_BOT_ICON_BG_A_L_N_BMP         = PARTY_COMMON_NIG_COM_BOT_ICON_BG_A_L_N_BMP;
        PARTY_THEME_COM_BOT_ICON_BG_A_L_S_BMP         = PARTY_COMMON_NIG_COM_BOT_ICON_BG_A_L_S_BMP;
        
        PARTY_THEME_COM_BOT_LIST_N_BMP                = PARTY_COMMON_NIG_COM_BOT_LIST_N_BMP;
        PARTY_THEME_COM_BOT_LIST_D_BMP                = PARTY_COMMON_NIG_COM_BOT_LIST_D_BMP;
        
        PARTY_THEME_COM_BOT_BROWSE_N_BMP              = PARTY_COMMON_NIG_COM_BOT_BROWSE_N_BMP;
        PARTY_THEME_COM_BOT_BROWSE_D_BMP              = PARTY_COMMON_NIG_COM_BOT_BROWSE_D_BMP;
        
        PARTY_THEME_COM_BOT_DIRECT_N_BMP              = PARTY_COMMON_NIG_COM_BOT_DIRECT_N_BMP;
        PARTY_THEME_COM_BOT_DIRECT_D_BMP              = PARTY_COMMON_NIG_COM_BOT_DIRECT_D_BMP;
        
        PARTY_THEME_COM_BOT_PHONE_A_N_BMP             = PARTY_COMMON_NIG_COM_BOT_PHONE_A_N_BMP;
        PARTY_THEME_COM_BOT_PHONE_B_N_BMP             = PARTY_COMMON_NIG_COM_BOT_PHONE_B_N_BMP;
        
        PARTY_THEME_COM_BOT_BT_CONNECT_N_BMP          = PARTY_COMMON_NIG_COM_BOT_BT_CONNECT_N_BMP;
        
        PARTY_THEME_COM_BOT_RANDOM_A_N_BMP            = PARTY_COMMON_NIG_COM_BOT_RANDOM_A_N_BMP;
        PARTY_THEME_COM_BOT_RANDOM_A_D_BMP            = PARTY_COMMON_NIG_COM_BOT_RANDOM_A_D_BMP;
        PARTY_THEME_COM_BOT_RANDOM_B_N_BMP            = PARTY_COMMON_NIG_COM_BOT_RANDOM_B_N_BMP;
        PARTY_THEME_COM_BOT_RANDOM_B_D_BMP            = PARTY_COMMON_NIG_COM_BOT_RANDOM_B_D_BMP;
        
        PARTY_THEME_COM_BOT_REPEAT_A_N_BMP            = PARTY_COMMON_NIG_COM_BOT_REPEAT_A_N_BMP;
        PARTY_THEME_COM_BOT_REPEAT_A_D_BMP            = PARTY_COMMON_NIG_COM_BOT_REPEAT_A_D_BMP;
        PARTY_THEME_COM_BOT_REPEAT_B_N_BMP            = PARTY_COMMON_NIG_COM_BOT_REPEAT_B_N_BMP;
        PARTY_THEME_COM_BOT_REPEAT_B_D_BMP            = PARTY_COMMON_NIG_COM_BOT_REPEAT_B_D_BMP;
        PARTY_THEME_COM_BOT_REPEAT_C_N_BMP            = PARTY_COMMON_NIG_COM_BOT_REPEAT_C_N_BMP;
        PARTY_THEME_COM_BOT_REPEAT_C_D_BMP            = PARTY_COMMON_NIG_COM_BOT_REPEAT_C_D_BMP;

        PARTY_THEME_COM_DEVICE_CON_N_BMP              = PARTY_COMMON_NIG_COM_DEVICE_CON_N_BMP;
        PARTY_THEME_COM_DEVICE_CON_D_BMP              = PARTY_COMMON_NIG_COM_DEVICE_CON_D_BMP;
        PARTY_THEME_COM_DEVICE_CON_S_BMP              = PARTY_COMMON_NIG_COM_DEVICE_CON_S_BMP;
        PARTY_THEME_COM_DEVICE_DEL_N_BMP              = PARTY_COMMON_NIG_COM_DEVICE_DEL_N_BMP;

        PARTY_THEME_TEMP_COM_LIST_ON_BMP              = PARTY_TEMP_NIGHT_NIG_COM_LIST_ON_BMP;
        PARTY_THEME_TEMP_COM_LIST_OFF_BMP             = PARTY_TEMP_NIGHT_NIG_COM_LIST_OFF_BMP;
        PARTY_THEME_TEMP_COM_RIG_ICON_BG_N_BMP        = PARTY_TEMP_NIGHT_NIG_COM_RIG_ICON_BG_N_BMP;
        PARTY_THEME_TEMP_COM_RIG_ICON_BG_P_BMP        = PARTY_TEMP_NIGHT_NIG_COM_RIG_ICON_BG_P_BMP;
        PARTY_THEME_TEMP_COM_RIG_ICON_BG_S_BMP        = PARTY_TEMP_NIGHT_NIG_COM_RIG_ICON_BG_S_BMP;
    }
    else  if(theme == THEME_DAY)
    {
        PARTY_THEME_TEMP_PM_BO_OFF_ICON_BMP           = PARTY_TEMP_DAY_DAY_PM_BO_OFF_ICON_BMP;    
        PARTY_THEME_TEMP_PM_BO_OFF_ICON_A_BMP         = PARTY_TEMP_DAY_DAY_PM_BO_OFF_ICON_A_BMP;  
        PARTY_THEME_TEMP_PM_RE_ICON_BMP               = PARTY_TEMP_DAY_DAY_PM_RE_ICON_BMP;    
        PARTY_THEME_TEMP_PM_RE_ICON_A_BMP             = PARTY_TEMP_DAY_DAY_PM_RE_ICON_A_BMP;    
        PARTY_THEME_TEMP_PM_RE_LIST_BMP               = PARTY_TEMP_DAY_DAY_PM_RE_LIST_BMP;
        PARTY_THEME_TEMP_PM_RE_LIST_N_BMP             = PARTY_TEMP_DAY_DAY_PM_RE_LIST_N_BMP;
        PARTY_THEME_TEMP_PM_RE_LIST_CONNECT_BMP       = PARTY_TEMP_DAY_DAY_PM_RE_LIST_CONNECT_BMP;
        PARTY_THEME_TEMP_PM_RE_SEARCH_BMP             = PARTY_TEMP_DAY_DAY_PM_RE_SEARCH_BMP;
        PARTY_THEME_TEMP_PM_RE_SEARCH_N_BMP           = PARTY_TEMP_DAY_DAY_PM_RE_SEARCH_N_BMP;
        PARTY_THEME_PM_BC_ICON_BMP                    = PARTY_MODE_DAY_PM_BC_ICON_BMP;
        PARTY_THEME_PM_RE_ICON_BMP                    = PARTY_MODE_DAY_PM_RE_ICON_BMP;
        PARTY_THEME_PM_RE_A_ICON_BMP                  = PARTY_MODE_DAY_PM_RE_A_ICON_BMP;

        PARTY_THEME_COM_BOT_BG_BMP                    = PARTY_COMMON_DAY_COM_BOT_BG_BMP;  
        PARTY_THEME_COM_BOT_ICON_BG_A_N_BMP           = PARTY_COMMON_DAY_COM_BOT_ICON_BG_A_N_BMP;  
        PARTY_THEME_COM_BOT_ICON_BG_A_P_S_BMP         = PARTY_COMMON_DAY_COM_BOT_ICON_BG_A_P_S_BMP; 
        PARTY_THEME_COM_BOT_ICON_BG_B_N_BMP           = PARTY_COMMON_DAY_COM_BOT_ICON_BG_B_N_BMP;   
        PARTY_THEME_COM_BOT_ICON_BG_B_P_S_BMP         = PARTY_COMMON_DAY_COM_BOT_ICON_BG_B_P_S_BMP; 
        PARTY_THEME_COM_BOT_MEDIA_PAUSE_BMP           = PARTY_COMMON_DAY_COM_BOT_MEDIA_PAUSE_BMP;   
        PARTY_THEME_COM_BOT_MEDIA_PAUSE_D_BMP         = PARTY_COMMON_DAY_COM_BOT_MEDIA_PAUSE_D_BMP;
        PARTY_THEME_COM_BOT_MEDIA_PLAY_BMP            = PARTY_COMMON_DAY_COM_BOT_MEDIA_PLAY_BMP;    
        PARTY_THEME_COM_BOT_MEDIA_PLAY_D_BMP          = PARTY_COMMON_DAY_COM_BOT_MEDIA_PLAY_D_BMP;
        PARTY_THEME_COM_BOT_MEDIA_DOWN_N_BMP          = PARTY_COMMON_DAY_COM_BOT_MEDIA_DOWN_N_BMP;  
        PARTY_THEME_COM_BOT_MEDIA_DOWN_P_S_BMP        = PARTY_COMMON_DAY_COM_BOT_MEDIA_DOWN_P_S_BMP;
        PARTY_THEME_COM_BOT_MEDIA_UP_N_BMP            = PARTY_COMMON_DAY_COM_BOT_MEDIA_UP_N_BMP;    
        PARTY_THEME_COM_BOT_MEDIA_UP_P_S_BMP          = PARTY_COMMON_DAY_COM_BOT_MEDIA_UP_P_S_BMP;

        PARTY_THEME_COM_BOT_ICON_BG_A_L_N_BMP           = PARTY_COMMON_DAY_COM_BOT_ICON_BG_A_L_N_BMP;
        PARTY_THEME_COM_BOT_ICON_BG_A_L_S_BMP           = PARTY_COMMON_DAY_COM_BOT_ICON_BG_A_L_S_BMP;
        
        PARTY_THEME_COM_BOT_LIST_N_BMP                = PARTY_COMMON_DAY_COM_BOT_LIST_N_BMP;
        PARTY_THEME_COM_BOT_LIST_D_BMP                = PARTY_COMMON_DAY_COM_BOT_LIST_D_BMP;
        
        PARTY_THEME_COM_BOT_BROWSE_N_BMP              = PARTY_COMMON_DAY_COM_BOT_BROWSE_N_BMP;
        PARTY_THEME_COM_BOT_BROWSE_D_BMP              = PARTY_COMMON_DAY_COM_BOT_BROWSE_D_BMP;
        
        PARTY_THEME_COM_BOT_DIRECT_N_BMP              = PARTY_COMMON_DAY_COM_BOT_DIRECT_N_BMP;
        PARTY_THEME_COM_BOT_DIRECT_D_BMP              = PARTY_COMMON_DAY_COM_BOT_DIRECT_D_BMP;
        
        
        PARTY_THEME_COM_BOT_PHONE_A_N_BMP             = PARTY_COMMON_DAY_COM_BOT_PHONE_A_N_BMP;
        PARTY_THEME_COM_BOT_PHONE_B_N_BMP             = PARTY_COMMON_DAY_COM_BOT_PHONE_B_N_BMP;
        
        PARTY_THEME_COM_BOT_BT_CONNECT_N_BMP          = PARTY_COMMON_DAY_COM_BOT_BT_CONNECT_N_BMP;
        
        PARTY_THEME_COM_BOT_RANDOM_A_N_BMP            = PARTY_COMMON_DAY_COM_BOT_RANDOM_A_N_BMP;
        PARTY_THEME_COM_BOT_RANDOM_A_D_BMP            = PARTY_COMMON_DAY_COM_BOT_RANDOM_A_D_BMP;
        PARTY_THEME_COM_BOT_RANDOM_B_N_BMP            = PARTY_COMMON_DAY_COM_BOT_RANDOM_B_N_BMP;
        PARTY_THEME_COM_BOT_RANDOM_B_D_BMP            = PARTY_COMMON_DAY_COM_BOT_RANDOM_B_D_BMP;
        
        PARTY_THEME_COM_BOT_REPEAT_A_N_BMP            = PARTY_COMMON_DAY_COM_BOT_REPEAT_A_N_BMP;
        PARTY_THEME_COM_BOT_REPEAT_A_D_BMP            = PARTY_COMMON_DAY_COM_BOT_REPEAT_A_D_BMP;
        PARTY_THEME_COM_BOT_REPEAT_B_N_BMP            = PARTY_COMMON_DAY_COM_BOT_REPEAT_B_N_BMP;
        PARTY_THEME_COM_BOT_REPEAT_B_D_BMP            = PARTY_COMMON_DAY_COM_BOT_REPEAT_B_D_BMP;
        PARTY_THEME_COM_BOT_REPEAT_C_N_BMP            = PARTY_COMMON_DAY_COM_BOT_REPEAT_C_N_BMP;
        PARTY_THEME_COM_BOT_REPEAT_C_D_BMP            = PARTY_COMMON_DAY_COM_BOT_REPEAT_C_D_BMP;

        PARTY_THEME_COM_DEVICE_CON_N_BMP              = PARTY_COMMON_DAY_COM_DEVICE_CON_N_BMP;
        PARTY_THEME_COM_DEVICE_CON_D_BMP              = PARTY_COMMON_DAY_COM_DEVICE_CON_D_BMP;
        PARTY_THEME_COM_DEVICE_CON_S_BMP              = PARTY_COMMON_DAY_COM_DEVICE_CON_S_BMP;
        PARTY_THEME_COM_DEVICE_DEL_N_BMP              = PARTY_COMMON_DAY_COM_DEVICE_DEL_N_BMP;

        PARTY_THEME_TEMP_COM_LIST_ON_BMP              = PARTY_TEMP_DAY_DAY_COM_LIST_ON_BMP;
        PARTY_THEME_TEMP_COM_LIST_OFF_BMP             = PARTY_TEMP_DAY_DAY_COM_LIST_OFF_BMP;
        PARTY_THEME_TEMP_COM_RIG_ICON_BG_N_BMP        = PARTY_TEMP_DAY_DAY_COM_RIG_ICON_BG_N_BMP;
        PARTY_THEME_TEMP_COM_RIG_ICON_BG_P_BMP        = PARTY_TEMP_DAY_DAY_COM_RIG_ICON_BG_P_BMP;
        PARTY_THEME_TEMP_COM_RIG_ICON_BG_S_BMP        = PARTY_TEMP_DAY_DAY_COM_RIG_ICON_BG_S_BMP;
    }
    
    if(party_mode_para->theme > THEME_NONE)
    {
        traverse_children_with_callback(party_mode_para, ui_party_mode, party_mode_para->theme_bmp_start, party_mode_para->theme_bmp_end, PARTY_THEME_TEMP_PM_BO_OFF_ICON_BMP, PARTY_THEME_TEMP_COM_RIG_ICON_BG_S_BMP, party_mode_para->ui_party_mode_res_para.lv_party_mode_icon, ui_party_mode_get_res, NULL, 0);
        ui_party_mode_night_theme(party_mode_para->source_obj, theme);
    }

    party_mode_para->theme = theme;
    party_mode_para->theme_bmp_start = PARTY_THEME_TEMP_PM_BO_OFF_ICON_BMP;
    party_mode_para->theme_bmp_end = PARTY_THEME_TEMP_PM_RE_SEARCH_N_BMP;
}

__s32 ui_party_mode_home_open_close(ui_party_mode_para_t * party_mode_para) 
{
    root_app_para_t * root_app_para = app_root_get_para();
    
    if(root_app_para->home_store == 0)
    {
        //party_mode_para->enter_home_flag = 0;
        ui_home_restore_switch_source();
    }
    else
    {
        party_mode_para->enter_home_flag = 1;
        ui_source_title(party_mode_para->source_para, "Source", 0);
        party_mode_cmd2parent(NULL,SWITCH_TO_MMENU,0,0);
    }
}

__s32 __party_mode_time_proc(ui_party_mode_para_t * party_mode_para) 
{
	if(!party_mode_para)
	{
		return EPDK_FAIL;
	}
}

void __ui_party_mode_remove_hidden(ui_party_mode_para_t * party_mode_para)
{
	__party_mode_msg("ui_ipod_remove_hidden\n");
	if(!ui_party_mode_timer)
	{
		ui_party_mode_timer = lv_timer_create(ui_event_party_mode_timer_cb, FlashTimeWinTime*10, party_mode_para);
	}
	_ui_flag_modify(ui_party_mode, LV_OBJ_FLAG_HIDDEN, _UI_MODIFY_FLAG_REMOVE);
#ifdef LV_ADD_ANIM_SCREEN
	Opacity_Increase_Animation(ui_party_mode, 0, NULL);
#endif
}

void __ui_party_mode_add_hidden(ui_party_mode_para_t * party_mode_para)
{
	__party_mode_msg("ui_ipod_add_hidden\n");
	_ui_flag_modify(ui_party_mode, LV_OBJ_FLAG_HIDDEN, _UI_MODIFY_FLAG_ADD);
	if(ui_party_mode_timer)
	{
		lv_timer_del(ui_party_mode_timer);
		ui_party_mode_timer = NULL;
	}
#ifdef LV_ADD_ANIM_SCREEN
	lv_obj_set_style_opa(ui_party_mode, 0, 0);
#endif
}

void __ui_party_mode_layer_on(ui_party_mode_para_t * party_mode_para)
{
	if(party_mode_para == NULL)
		return EPDK_FAIL;

	if(party_mode_para->mode_store == 0)
	{
		return EPDK_FAIL;
	}

	party_mode_para->mode_store = 0;
	TopBar_view_set_status(1);
	gscene_bgd_set_img_src(BG_INDEX_AUTO);
	
	ui_party_mode_remove_hidden(party_mode_para);
}

void __ui_party_mode_layer_sleep(ui_party_mode_para_t * party_mode_para)
{
	if(party_mode_para == NULL)
		return EPDK_FAIL;

	if(party_mode_para->mode_store == 1)
	{
		return EPDK_FAIL;
	}

	party_mode_para->mode_store = 1;
	ui_party_mode_add_hidden(party_mode_para);
}

static __s32 ui_party_mode_internal_para_update(ui_party_mode_para_t * party_mode_para)
{
    party_mode_para->theme_bmp_start = PARTY_TEMP_NIGHT_NIG_PM_BO_OFF_ICON_BMP;
    party_mode_para->theme_bmp_end = PARTY_TEMP_NIGHT_NIG_PM_RE_SEARCH_N_BMP;
    party_mode_para->source_obj = ui_source_content;//(lv_obj_t *)ui_source_create(ui_source);
    party_mode_para->source_para = (ui_party_mode_para_t *)lv_obj_get_user_data(party_mode_para->source_obj);
    ui_party_comms_sync_party_own_info(&party_mode_para->source_para->party_info);
}

__s32 ui_party_mode_refresh_handler(void * para, __gui_msg_t * pmsg)
{
	__s32 ret = EPDK_OK;
	__gui_msg_t msg;
	ui_party_mode_para_t * party_mode_para = ui_party_mode_para;//(ui_party_mode_para_t *)para;
	root_app_para_t * root_para = app_root_get_para();//pmsg->p_arg;

	if(!party_mode_para)
		return EPDK_FAIL;
		
    memset(&msg, 0x00, sizeof(msg));

	__party_mode_msg("LOWORD(pmsg->id)=0x%x\n", LOWORD(pmsg->id));
    if(LOWORD(pmsg->id) == GUI_MSG_PAINT)
    {
		__party_mode_msg("HIWORD(pmsg->id)=0x%x, GUI_MSG_PARTY_MODE_REFRESH_INIT=0x%x\n", HIWORD(pmsg->id), GUI_MSG_PARTY_MODE_REFRESH_INIT);
		switch(HIWORD(pmsg->id))
		{
			case GUI_MSG_PARTY_MODE_REFRESH_INIT:
			{
                ui_party_mode_night_theme(ui_party_mode_para, THEME_NIGHT);
                
                ui_source_switch_clean(ui_party_mode_para->source_para);
                ui_source_nosupport_browser(ui_party_mode_para->source_para, 1);
                ui_source_nosupport_func(ui_party_mode_para->source_para, 1);
                //lv_obj_remove_flag(ui_party_mode_para->source_para->ui_top_right, LV_OBJ_FLAG_CLICKABLE);      /// Flags
                ui_source_title(ui_party_mode_para->source_para, "Party Mode", 1);
                ui_source_rig_bar_set_flag(ui_party_mode_para->source_para, LV_OBJ_FLAG_HIDDEN, 1);

				TopBar_view_set_status(1);
				Draw_topbar_view_with_app_id(APP_PARTY_MODE_ID, 0);
				gscene_bgd_set_img_src(BG_INDEX_AUTO);
				ui_party_mode_screen_init(ui_party_mode_para);
				ui_party_mode_para->party_mode_main_obj = ui_party_mode;
				root_para->app_interface_party_mode->info.src_win = ui_party_mode;

                __party_mode_msg("ui_party_mode_para->source_para->party_info.role=0x%x\n", ui_party_mode_para->source_para->party_info.role);
                if(ui_party_mode_para->source_para->party_info.role == PARTY_ROLE_BROADCAST)
                {
                    ui_party_mode_role_update(ui_party_mode_para, PARTY_ROLE_BROADCAST, 1);
                }
                else if(ui_party_mode_para->source_para->party_info.role == PARTY_ROLE_RECEIVER)
                {
                    ui_party_mode_role_update(ui_party_mode_para, PARTY_ROLE_RECEIVER, 0);
                }
                else
                {
                    ui_party_mode_switch_win(ui_party_mode_para, PARTY_WIN_SELECT_MODE);
                }
			}
			break;

			case GUI_MSG_PARTY_MODE_REFRESH_UNINIT:
			{
				gscene_bgd_set_hid_flag(BGD_STATUS_SHOW);
				if(ui_party_mode_timer)
				{
					lv_timer_del(ui_party_mode_timer);
					ui_party_mode_timer = NULL;
				}
				ui_party_mode_screen_uninit(ui_party_mode_para);
                //ui_source_destroy(ui_party_mode_para->source_obj);
				ui_party_mode_uninit_res(ui_party_mode_para);
				esMEMS_Bfree(ui_party_mode_para, sizeof(ui_party_mode_para_t));
				ui_party_mode_para = NULL;
				eLIBs_printf("ui_party_mode_uninit\n");
			}
			break;
			
			case GUI_MSG_PARTY_MODE_REFRESH_HIDDEN:
			{
				__u32 flag = (__u32)pmsg->dwAddData1;

				if(flag == 0)
				{
					__ui_party_mode_remove_hidden(party_mode_para);
				}
				else
				{
					__ui_party_mode_add_hidden(party_mode_para);
				}
			}
			break;
			
			case GUI_MSG_PARTY_MODE_REFRESH_PART:
			{
			}
			break;
			
			case GUI_MSG_PARTY_MODE_REFRESH_MSG:
			{
				__s32 id = (__s32)pmsg->dwAddData1;
				__s32 data1 = (__s32)pmsg->dwAddData2;
				__s32 data2 = (__s32)pmsg->dwReserved;

				__party_mode_cmd2parent(NULL, id, data1, data2);
			}
			break;
			
			default:
			{
				ret = EPDK_FAIL;
			}
			break;
		}
	
}
	else
	{
		ret = EPDK_FAIL;
	}
	
	return ret;
}

static void ui_party_mode_source_event_handler(ui_party_mode_para_t * party_mode_para)
{  
    ui_party_mode_bottom_group_update(party_mode_para, 0, 0, 0);
    if(party_mode_para->win_type == PARTY_WIN_SELECT_MODE)
    {
        ui_source_group_update(party_mode_para->source_para, 1);
        lv_group_remove_obj(ui_party_mode_select_mode_button_broadcast);
        lv_group_remove_obj(ui_party_mode_select_mode_button_receiver);
    }
    ui_source_sub_open(party_mode_para->source_para);
    ui_party_mode_home_open_close(party_mode_para);
}

static void ui_party_mode_top_left_event_handler(ui_party_mode_para_t * party_mode_para)
{  
#if 1
    switch(party_mode_para->win_type)
    {
        case PARTY_WIN_SELECT_MODE: 
        {
            ui_party_mode_home_open_close(party_mode_para);
#ifndef USE_PARTY_MODE_SOURCE
            party_mode_cmd2parent(NULL, SWITCH_TO_SOURCE, 0, 0);
#endif
        }       
        break;

        case PARTY_WIN_BROADCAST:   
        {
            //ui_party_mode_broadcast_source_event_handler(party_mode_para, PARTY_SOURCE_BT);
            ui_party_mode_switch_win(party_mode_para, PARTY_WIN_SELECT_MODE);
        }       
        break;

        case PARTY_WIN_RECEIVER:    
        {
            if(party_mode_para->bottom_fouse_flag == 1)
            {
                ui_party_mode_bottom_group_update(party_mode_para, 0, 0, 0);
            }
            ui_home_restore_switch_source();
            //ui_party_mode_switch_win(party_mode_para, PARTY_WIN_FUNC_LIST);
        }       
        break;

        case PARTY_WIN_DEVICE_LIST:
        case PARTY_WIN_KNOWN_DEVICE:
        case PARTY_WIN_MENU:
        {
            ui_party_mode_switch_win(party_mode_para, PARTY_WIN_RECEIVER);
        }       
        break;
    }
#else
    root_app_para_t * root_app_para = app_root_get_para();

    __party_mode_msg("root_app_para->home_store=0x%x\n", root_app_para->home_store);
    if(root_app_para->home_store == 0)
    {
        lv_imgbtn_set_state(party_mode_para->source_para->ui_top_left, LV_IMAGEBUTTON_STATE_RELEASED);
        ui_home_para_t * home_para = ui_home_get_para();
        
        ui_home_switch_source(home_para->cur_focus_item);
        //app_home_cmd2root(NULL, RESTORE_APP, NULL, NULL);
    }
    else
    {
        lv_imgbtn_set_state(party_mode_para->source_para->ui_top_left, LV_IMAGEBUTTON_STATE_PRESSED);
        party_mode_cmd2parent(NULL,SWITCH_TO_MMENU,0,0);
    }
#endif
}

static void ui_party_mode_top_right_event_handler(ui_party_mode_para_t * party_mode_para)
{   
    switch(party_mode_para->win_type)
    {
        case PARTY_WIN_SELECT_MODE:
        case PARTY_WIN_BROADCAST:   
        {
            ui_party_mode_home_open_close(party_mode_para);
        }       
        break;

        case PARTY_WIN_RECEIVER:    
        {
            if(party_mode_para->bottom_fouse_flag == 1)
            {
                ui_party_mode_bottom_group_update(party_mode_para, 0, 0, 0);
            }
            ui_home_restore_switch_source();
        }       
        break;

        case PARTY_WIN_DEVICE_LIST:
        case PARTY_WIN_KNOWN_DEVICE:
        case PARTY_WIN_MENU:
        {
            ui_party_mode_switch_win(party_mode_para, PARTY_WIN_RECEIVER);
        }       
        break;
    }
}

#if 0
static void ui_party_mode_device_event_handler(ui_party_mode_para_t * party_mode_para, __u32 menu_type)
{
	if(party_mode_para->menu_type == menu_type)
		return;

    __party_mode_msg("party_mode_para->menu_type=%d, menu_type=%d\n", party_mode_para->menu_type, menu_type);
    party_mode_para->menu_type = menu_type;
    
	switch(party_mode_para->menu_type)
	{
		case PARTY_MENU_MY_DEVICE:	
		{
            lv_obj_add_state(ui_party_mode_menu_panel_button_device, LV_STATE_CHECKED);
            lv_obj_remove_state(ui_party_mode_menu_panel_button_search, LV_STATE_CHECKED);
            
            ui_party_mode_listbar_uninit(party_mode_para);
            ui_party_mode_listbar_para_init(party_mode_para, ui_party_mode_menu_panel_content, 5);
            ui_party_mode_listbar_init(party_mode_para);
            
            lv_obj_remove_flag(ui_party_mode_menu_panel_bar, LV_OBJ_FLAG_HIDDEN);     /// Flags
		}		
		break;

		case PARTY_MENU_NEW_DEVICE:	
		{
            lv_obj_remove_state(ui_party_mode_menu_panel_button_device, LV_STATE_CHECKED);
            lv_obj_add_state(ui_party_mode_menu_panel_button_search, LV_STATE_CHECKED);
            
            ui_party_mode_listbar_uninit(party_mode_para);
            ui_party_mode_listbar_para_init(party_mode_para, ui_party_mode_menu_panel_content, 5);
            ui_party_mode_listbar_init(party_mode_para);
            
            lv_obj_remove_flag(ui_party_mode_menu_panel_bar, LV_OBJ_FLAG_HIDDEN);     /// Flags
		}		
		break;
	}
}

static void ui_party_mode_open_bar_menu_event_handler(ui_party_mode_para_t * party_mode_para)
{
    ui_party_mode_switch_win(party_mode_para, PARTY_WIN_NONE);
    
    if(party_mode_para->source_para->rig_select_flag == SOURCE_BOTTOM)
    {
        ui_party_mode_bottom_menu_event_handler(party_mode_para);
    }
    else
    {
        //ui_party_mode_top_menu_event_handler(party_mode_para);
    }
}

static void ui_party_mode_bottom_menu_event_handler(ui_party_mode_para_t * party_mode_para)
{
    ui_party_mode_switch_win(party_mode_para, PARTY_WIN_FUNC_MENU);
}
#endif

static void ui_party_mode_broadcast_source_event_handler(ui_party_mode_para_t * party_mode_para, uint32_t selected)
{
	root_app_para_t * root_para = app_root_get_para();

    __party_mode_msg("ui_party_mode_broadcast_source_event_handler selected=%d\n", selected);
	switch(selected)
	{
		case PARTY_SOURCE_USB:
		{
			ui_party_comms_set_bc_source((void *)&party_mode_para->source_para->party_info,PARTY_D_SOURCE_USB);
            //app_root_send2mcu_party(UART_SEND_PARTY_MODE_WORK_SOURCE, ID_WORK_SOURCE_USB);
            //app_root_source_create(root_para,ID_WORK_SOURCE_USB);
		}		
		break;

		case PARTY_SOURCE_BT:
		{
			ui_party_comms_set_bc_source((void *)&party_mode_para->source_para->party_info,PARTY_D_SOURCE_BT_AUDIO);
            //app_root_send2mcu_party(UART_SEND_PARTY_MODE_WORK_SOURCE, ID_WORK_SOURCE_BT);
            //app_root_source_create(root_para,ID_WORK_SOURCE_BT);
		}		
		break;

        case PARTY_SOURCE_AUX:
		{
			ui_party_comms_set_bc_source((void *)&party_mode_para->source_para->party_info,PARTY_D_SOURCE_AUXIN);
            //app_root_send2mcu_party(UART_SEND_PARTY_MODE_WORK_SOURCE, ID_WORK_SOURCE_AUXIN);
            //app_root_source_create(root_para,ID_WORK_SOURCE_AUXIN);
		}		
		break;
	}
}

static void ui_party_mode_item_event_handler(ui_party_mode_para_t * party_mode_para, lv_obj_t * target)
{
	__s32 ret;
	rat_media_type_t media_type;
    uint32_t index = lv_obj_get_index(target);
    index += party_mode_para->ui_list_para.page_start_id;

    __party_mode_msg("ui_party_mode_item_event_handler index=%d\n", index);

    if(party_mode_para->win_type == PARTY_WIN_DEVICE_LIST)
    {
        lv_obj_t * list_item_panel = lv_obj_get_child(target, 0);
        lv_obj_t * list_item_content_panel = lv_obj_get_child(list_item_panel, 1);
        lv_obj_t * list_item_device_panel = lv_obj_get_child(list_item_content_panel, 0);
        lv_obj_t * list_item_device_scan = lv_obj_get_child(list_item_device_panel, 0);
        lv_obj_t * list_item_device_connected = lv_obj_get_child(list_item_device_panel, 1);

        party_mode_para->connect_index = index;
        ui_source_top_indicant(party_mode_para->source_para, SOURCE_INDICANT_SEARCH, 0);
        lv_obj_remove_flag(list_item_device_scan, LV_OBJ_FLAG_HIDDEN);      /// Flags
        ui_party_comms_set_rc_connect_control(&party_mode_para->source_para->party_info, party_mode_para->source_para->party_info.rc_info.scan_list[index].address, 1);
    }
    else if(party_mode_para->win_type == PARTY_WIN_KNOWN_DEVICE)
    {
        //ui_party_comms_set_rc_connect_control(&party_mode_para->source_para->party_info, party_mode_para->source_para->party_info.rc_info.known_list[index].address, 1);
    }
}

static void ui_party_mode_device_event_handler(ui_party_mode_para_t * party_mode_para, lv_obj_t * target, lv_obj_t * current_target)
{
	__s32 ret;
	rat_media_type_t media_type;
    uint32_t index = lv_obj_get_index(target);
    lv_obj_t * list_item_content_panel = lv_obj_get_parent(current_target);
    lv_obj_t * list_item_panel = lv_obj_get_parent(list_item_content_panel);
    lv_obj_t * list_item = lv_obj_get_parent(list_item_panel);
    uint32_t item_index = lv_obj_get_index(list_item);
    item_index += party_mode_para->ui_list_para.page_start_id;

    __party_mode_msg("ui_party_mode_device_event_handler index=%d, item_index=%d\n", index, item_index);

    if(party_mode_para->win_type == PARTY_WIN_DEVICE_LIST)
    {
    }
    else if(party_mode_para->win_type == PARTY_WIN_KNOWN_DEVICE)
    {
        if(index == 0)
        {
            party_mode_para->connect_index = item_index;
            if(party_mode_para->source_para->party_info.rc_info.connect_status == 1 
            && eLIBs_memcmp(party_mode_para->source_para->party_info.rc_info.connected_device.address, party_mode_para->source_para->party_info.rc_info.known_list[item_index].address, sizeof(party_mode_para->source_para->party_info.rc_info.connected_device.address)) != EPDK_NO)
            {
                ui_party_comms_set_rc_connect_control(&party_mode_para->source_para->party_info, party_mode_para->source_para->party_info.rc_info.known_list[item_index].address, 0);
            }
            else
            {
                ui_party_comms_set_rc_connect_control(&party_mode_para->source_para->party_info, party_mode_para->source_para->party_info.rc_info.known_list[item_index].address, 1);
            }
        }
        else if(index == 1)
        {
            party_mode_para->delete_index = item_index;
            ui_party_mode_del_deivce_tips_dialog_create(party_mode_para, STRING_ROOT_DELETE_DEVICE, 0, 0, ADLG_YESNO);
            //ui_party_comms_set_rc_delete_known_device(&party_mode_para->source_para->party_info, party_mode_para->source_para->party_info.rc_info.known_list[item_index].address, item_index + 1);
        }
    }
}

static __s32 ui_party_mode_ui_update_proc(ui_party_mode_para_t * party_mode_para, __gui_msg_t *msg)
{
	__s32 ret = EPDK_FAIL;
	root_app_para_t * root_para = app_root_get_para();//pmsg->p_arg;
	reg_root_para_t* last_root_para;
	reg_aux_para_t* para;
	reg_system_para_t* sys_para;

	sys_para = (reg_system_para_t*)dsk_reg_get_para_by_app(REG_APP_SYSTEM);
	last_root_para = (reg_root_para_t*)dsk_reg_get_para_by_app(REG_APP_ROOT);
	para = (reg_aux_para_t*)dsk_reg_get_para_by_app(REG_APP_AUX);	
	

	if(party_mode_para == NULL)
		return EPDK_FAIL;

    __party_mode_msg("ui_aux_in_ui_update_proc: msg->dwAddData1=%d, GUI_MSG_UI_VOLUME_ADJUST=%d\n",msg->dwAddData1, GUI_MSG_UI_VOLUME_ADJUST);
	
	switch(msg->dwAddData1)
    {
    	case GUI_MSG_UI_VOLUME_ADJUST:
        case GUI_MSG_UI_SUB_VOL:
    	{
            ui_source_send_event(party_mode_para->source_para, LV_EVENT_MSG_RECEIVED, (void *)msg);
    	}
    	break;

		//role 
		case GUI_MSG_UI_PARTY_ROLE_UPDATE:
			ui_party_comms_sync_party_own_info(&party_mode_para->source_para->party_info);
			__party_mode_msg("Role update[%d]\n",party_mode_para->source_para->party_info.role);
            ui_party_mode_role_update(party_mode_para, party_mode_para->source_para->party_info.role, 0);
		break;

		//receiver connect state
		case GUI_MSG_UI_PARTY_RECEIVER_CONNECT_STATE:
			ui_party_comms_sync_receiver_connect_device(&party_mode_para->source_para->party_info);
			__party_mode_msg("Receiver connect state[%d]:%s\n",party_mode_para->source_para->party_info.rc_info.connect_status,party_mode_para->source_para->party_info.rc_info.connected_device.name);
            __party_mode_msg("party_mode_para->connect_index=%d\n", party_mode_para->connect_index);

#if 1
            if(party_mode_para->win_type == PARTY_WIN_DEVICE_LIST)
            {
                if(party_mode_para->ui_list_para.item_obj)
                {
                    lv_obj_t * list_item = lv_obj_get_child(party_mode_para->ui_list_para.item_obj, party_mode_para->connect_index);
                    
                    if(list_item != NULL)
                    {
                        lv_obj_t * list_item_panel = lv_obj_get_child(list_item, 0);
                        lv_obj_t * list_item_content_panel = lv_obj_get_child(list_item_panel, 1);
                        lv_obj_t * list_item_device_panel = lv_obj_get_child(list_item_content_panel, 0);
                        lv_obj_t * list_item_device_scan = lv_obj_get_child(list_item_device_panel, 0);
                        lv_obj_t * list_item_device_connected = lv_obj_get_child(list_item_device_panel, 1);
            
                        __party_mode_msg("win_type=%d\n", party_mode_para->win_type);
                        if(party_mode_para->source_para->party_info.rc_info.connect_status == 1)
                        {
                            lv_obj_add_flag(list_item_device_scan, LV_OBJ_FLAG_HIDDEN);      /// Flags
                            lv_obj_remove_flag(list_item_device_connected, LV_OBJ_FLAG_HIDDEN);      /// Flags
                            party_mode_para->source_para->party_info.rc_info.scan_list[party_mode_para->connect_index].connect_status = 1;
                            
                            lv_obj_remove_flag(ui_party_mode_id3, LV_OBJ_FLAG_HIDDEN);      /// Flags
                            lv_obj_add_flag(ui_party_mode_connect_status, LV_OBJ_FLAG_HIDDEN);      /// Flags
                        }
                        else if(party_mode_para->source_para->party_info.rc_info.connect_status == 0)
                        {
                            lv_obj_add_flag(list_item_device_scan, LV_OBJ_FLAG_HIDDEN);      /// Flags
                            party_mode_para->source_para->party_info.rc_info.scan_list[party_mode_para->connect_index].connect_status = 0;

                            lv_obj_add_flag(ui_party_mode_id3, LV_OBJ_FLAG_HIDDEN);      /// Flags
                            lv_obj_remove_flag(ui_party_mode_connect_status, LV_OBJ_FLAG_HIDDEN);      /// Flags
                        }
                    }
                }
                party_mode_para->connect_index = 0;
            }
            else if(party_mode_para->win_type == PARTY_WIN_KNOWN_DEVICE)
            {
                if(party_mode_para->ui_list_para.item_obj)
                {
                    lv_obj_t * list_item = lv_obj_get_child(party_mode_para->ui_list_para.item_obj, party_mode_para->connect_index);
                    
                    if(list_item != NULL)
                    {
                        lv_obj_t * list_item_panel = lv_obj_get_child(list_item, 0);
                        lv_obj_t * list_item_content_panel = lv_obj_get_child(list_item_panel, 1);
                        lv_obj_t * list_item_device_panel = lv_obj_get_child(list_item_content_panel, 0);
                        lv_obj_t * list_item_device_connect = lv_obj_get_child(list_item_device_panel, 0);
            
                        __party_mode_msg("win_type=%d\n", party_mode_para->win_type);
                        if(party_mode_para->source_para->party_info.rc_info.connect_status == 1)
                        {
                            party_mode_para->source_para->party_info.rc_info.known_list[party_mode_para->connect_index].connect_status = 1;
                            __party_mode_msg("connect_status=%d\n", party_mode_para->source_para->party_info.rc_info.known_list[party_mode_para->connect_index].connect_status);
                            lv_obj_set_style_bg_image_src(list_item_device_connect, ui_party_mode_get_res(party_mode_para, PARTY_THEME_COM_DEVICE_CON_N_BMP), LV_PART_MAIN | LV_STATE_DEFAULT);

                            lv_obj_remove_flag(ui_party_mode_id3, LV_OBJ_FLAG_HIDDEN);      /// Flags
                            lv_obj_add_flag(ui_party_mode_connect_status, LV_OBJ_FLAG_HIDDEN);      /// Flags
                        }
                        else if(party_mode_para->source_para->party_info.rc_info.connect_status == 0)
                        {
                            party_mode_para->source_para->party_info.rc_info.known_list[party_mode_para->connect_index].connect_status = 0;
                            __party_mode_msg("connect_status=%d\n", party_mode_para->source_para->party_info.rc_info.known_list[party_mode_para->connect_index].connect_status);
                            lv_obj_set_style_bg_image_src(list_item_device_connect, ui_party_mode_get_res(party_mode_para, PARTY_THEME_COM_DEVICE_CON_D_BMP), LV_PART_MAIN | LV_STATE_DEFAULT);

                            lv_obj_add_flag(ui_party_mode_id3, LV_OBJ_FLAG_HIDDEN);      /// Flags
                            lv_obj_remove_flag(ui_party_mode_connect_status, LV_OBJ_FLAG_HIDDEN);      /// Flags
                        }
                    }
                }
                party_mode_para->connect_index = 0;
            }
#else
            for(__u8 i = 0; i < 16; i++)
            {
                if(party_mode_para->win_type == PARTY_WIN_DEVICE_LIST)
                {
                    __party_mode_msg("connected_device.address[0]=0x%x\n",party_mode_para->source_para->party_info.rc_info.connected_device.address[0]);
                    __party_mode_msg("connected_device.address[1]=0x%x\n",party_mode_para->source_para->party_info.rc_info.connected_device.address[1]);
                    __party_mode_msg("connected_device.address[2]=0x%x\n",party_mode_para->source_para->party_info.rc_info.connected_device.address[2]);
                    __party_mode_msg("connected_device.address[3]=0x%x\n",party_mode_para->source_para->party_info.rc_info.connected_device.address[3]);
                    __party_mode_msg("connected_device.address[4]=0x%x\n",party_mode_para->source_para->party_info.rc_info.connected_device.address[4]);
                    __party_mode_msg("connected_device.address[5]=0x%x\n",party_mode_para->source_para->party_info.rc_info.connected_device.address[5]);

                    __party_mode_msg("scan_list[%d].address[0]=0x%x\n",i,party_mode_para->source_para->party_info.rc_info.scan_list[i].address[0]);
                    __party_mode_msg("scan_list[%d].address[1]=0x%x\n",i,party_mode_para->source_para->party_info.rc_info.scan_list[i].address[0]);
                    __party_mode_msg("scan_list[%d].address[2]=0x%x\n",i,party_mode_para->source_para->party_info.rc_info.scan_list[i].address[0]);
                    __party_mode_msg("scan_list[%d].address[3]=0x%x\n",i,party_mode_para->source_para->party_info.rc_info.scan_list[i].address[0]);
                    __party_mode_msg("scan_list[%d].address[4]=0x%x\n",i,party_mode_para->source_para->party_info.rc_info.scan_list[i].address[0]);
                    __party_mode_msg("scan_list[%d].address[5]=0x%x\n",i,party_mode_para->source_para->party_info.rc_info.scan_list[i].address[0]);
                    if(eLIBs_memcmp(party_mode_para->source_para->party_info.rc_info.connected_device.address, party_mode_para->source_para->party_info.rc_info.scan_list[i].address, sizeof(party_mode_para->source_para->party_info.rc_info.connected_device.address)) != EPDK_NO)
                    {
                        lv_obj_t * list_item = lv_obj_get_child(party_mode_para->ui_list_para.item_obj, i);
                        if(list_item != NULL)
                        {
                            lv_obj_t * list_item_panel = lv_obj_get_child(list_item, 0);
                            lv_obj_t * list_item_content_panel = lv_obj_get_child(list_item_panel, 1);
                            lv_obj_t * list_item_device_panel = lv_obj_get_child(list_item_content_panel, 0);
                            lv_obj_t * list_item_device_scan = lv_obj_get_child(list_item_device_panel, 0);
                            lv_obj_t * list_item_device_connected = lv_obj_get_child(list_item_device_panel, 1);

                            __party_mode_msg("win_type=%d\n", party_mode_para->win_type);
                            if(party_mode_para->source_para->party_info.rc_info.connect_status == 1)
                            {
                                lv_obj_add_flag(list_item_device_scan, LV_OBJ_FLAG_HIDDEN);      /// Flags
                                lv_obj_remove_flag(list_item_device_connected, LV_OBJ_FLAG_HIDDEN);      /// Flags
                                party_mode_para->source_para->party_info.rc_info.scan_list[i].connect_status = 1;
                                
                                lv_obj_add_flag(ui_party_mode_id3, LV_OBJ_FLAG_HIDDEN);      /// Flags
                                lv_obj_remove_flag(ui_party_mode_connect_status, LV_OBJ_FLAG_HIDDEN);      /// Flags
                            }
                            else if(party_mode_para->source_para->party_info.rc_info.connect_status == 0)
                            {
                                lv_obj_add_flag(list_item_device_scan, LV_OBJ_FLAG_HIDDEN);      /// Flags
                                party_mode_para->source_para->party_info.rc_info.scan_list[i].connect_status = 0;

                                lv_obj_remove_flag(ui_party_mode_id3, LV_OBJ_FLAG_HIDDEN);      /// Flags
                                lv_obj_add_flag(ui_party_mode_connect_status, LV_OBJ_FLAG_HIDDEN);      /// Flags
                            }
                        }
                        
                        break;
                    }
                }
                else if(party_mode_para->win_type == PARTY_WIN_KNOWN_DEVICE)
                {
                    __party_mode_msg("connected_device.address[0]=0x%x\n",party_mode_para->source_para->party_info.rc_info.connected_device.address[0]);
                    __party_mode_msg("connected_device.address[0]=0x%x\n",party_mode_para->source_para->party_info.rc_info.connected_device.address[1]);
                    __party_mode_msg("connected_device.address[0]=0x%x\n",party_mode_para->source_para->party_info.rc_info.connected_device.address[2]);
                    __party_mode_msg("connected_device.address[0]=0x%x\n",party_mode_para->source_para->party_info.rc_info.connected_device.address[3]);
                    __party_mode_msg("connected_device.address[0]=0x%x\n",party_mode_para->source_para->party_info.rc_info.connected_device.address[4]);
                    __party_mode_msg("connected_device.address[0]=0x%x\n",party_mode_para->source_para->party_info.rc_info.connected_device.address[5]);

                    __party_mode_msg("known_list[%d].address[0]=0x%x\n",i,party_mode_para->source_para->party_info.rc_info.known_list[i].address[0]);
                    __party_mode_msg("known_list[%d].address[1]=0x%x\n",i,party_mode_para->source_para->party_info.rc_info.known_list[i].address[0]);
                    __party_mode_msg("known_list[%d].address[2]=0x%x\n",i,party_mode_para->source_para->party_info.rc_info.known_list[i].address[0]);
                    __party_mode_msg("known_list[%d].address[3]=0x%x\n",i,party_mode_para->source_para->party_info.rc_info.known_list[i].address[0]);
                    __party_mode_msg("known_list[%d].address[4]=0x%x\n",i,party_mode_para->source_para->party_info.rc_info.known_list[i].address[0]);
                    __party_mode_msg("known_list[%d].address[5]=0x%x\n",i,party_mode_para->source_para->party_info.rc_info.known_list[i].address[0]);
                    if(eLIBs_memcmp(party_mode_para->source_para->party_info.rc_info.connected_device.address, party_mode_para->source_para->party_info.rc_info.known_list[i].address, sizeof(party_mode_para->source_para->party_info.rc_info.connected_device.address)) != EPDK_NO)
                    {
                        lv_obj_t * list_item = lv_obj_get_child(party_mode_para->ui_list_para.item_obj, i);
                        if(list_item != NULL)
                        {
                            lv_obj_t * list_item_panel = lv_obj_get_child(list_item, 0);
                            lv_obj_t * list_item_content_panel = lv_obj_get_child(list_item_panel, 1);
                            lv_obj_t * list_item_device_panel = lv_obj_get_child(list_item_content_panel, 0);
                            lv_obj_t * list_item_device_connect = lv_obj_get_child(list_item_device_panel, 0);

                            __party_mode_msg("win_type=%d\n", party_mode_para->win_type);
                            if(party_mode_para->source_para->party_info.rc_info.connect_status == 1)
                            {
                                party_mode_para->source_para->party_info.rc_info.known_list[i].connect_status = 1;
                                __party_mode_msg("connect_status=%d\n", party_mode_para->source_para->party_info.rc_info.known_list[i].connect_status);
                                lv_obj_set_style_bg_image_src(list_item_device_connect, ui_party_mode_get_res(party_mode_para, PARTY_THEME_COM_DEVICE_CON_D_BMP), LV_PART_MAIN | LV_STATE_DEFAULT);
                                
                                lv_obj_add_flag(ui_party_mode_id3, LV_OBJ_FLAG_HIDDEN);      /// Flags
                                lv_obj_remove_flag(ui_party_mode_connect_status, LV_OBJ_FLAG_HIDDEN);      /// Flags
                            }
                            else if(party_mode_para->source_para->party_info.rc_info.connect_status == 0)
                            {
                                party_mode_para->source_para->party_info.rc_info.known_list[i].connect_status = 0;
                                __party_mode_msg("connect_status=%d\n", party_mode_para->source_para->party_info.rc_info.known_list[i].connect_status);
                                lv_obj_set_style_bg_image_src(list_item_device_connect, ui_party_mode_get_res(party_mode_para, PARTY_THEME_COM_DEVICE_CON_N_BMP), LV_PART_MAIN | LV_STATE_DEFAULT);

                                lv_obj_remove_flag(ui_party_mode_id3, LV_OBJ_FLAG_HIDDEN);      /// Flags
                                lv_obj_add_flag(ui_party_mode_connect_status, LV_OBJ_FLAG_HIDDEN);      /// Flags
                            }
                        }
                        
                        break;
                    }
                }
            }
#endif
        break;

		//known device list update
		case GUI_MSG_UI_PARTY_KNOWN_LIST_UPDATE:
			ui_party_comms_sync_receiver_known_list(&party_mode_para->source_para->party_info);
			__party_mode_msg("Known list update[%d]\n",party_mode_para->source_para->party_info.rc_info.known_count);

            if(party_mode_para->win_type == PARTY_WIN_KNOWN_DEVICE)
            {
                ui_party_mode_list_folder_reinit(party_mode_para, ui_party_mode_menu, party_mode_para->source_para->party_info.rc_info.known_count);
            }
		break;

		//scan device list update
		case GUI_MSG_UI_PARTY_SCAN_LIST_UPDATE:
			ui_party_comms_sync_receiver_scan_list(&party_mode_para->source_para->party_info);
			__party_mode_msg("Scan list update[%d]\n",party_mode_para->source_para->party_info.rc_info.scan_count);

            if(party_mode_para->win_type == PARTY_WIN_DEVICE_LIST)
            {
                ui_party_mode_list_folder_reinit(party_mode_para, ui_party_mode_menu, party_mode_para->source_para->party_info.rc_info.scan_count);
            }
		break;
            
        //broadcast source selected
        case GUI_MSG_UI_PARTY_BROADCAST_SOURCE:
            ui_party_comms_sync_broadcast_info(&party_mode_para->source_para->party_info);
            __party_mode_msg("Broadcast source[%d]\n",party_mode_para->source_para->party_info.bc_info.source);
            if(party_mode_para->source_para->party_info.role == PARTY_ROLE_BROADCAST)
                ui_party_mode_role_update(party_mode_para, PARTY_ROLE_BROADCAST, 1);
        break;

        // MCU Protocol v2 Data Update Message
        case GUI_MSG_UI_NEW_PARTY_UPDATE:
        {
            __party_mode_msg("MCU Party data update received\n");
            if (party_mode_para->source_para && reg_app_para) {
                __u8 current_source = reg_app_para->system_para.g_sys_para.current_source;
                if (current_source == SYS_SOURCE_PARTY) {
                    UI_MCU_HANDLE_UPDATE(&reg_app_para->party_para.sync, party_mode_para->mcu_ops, party_mode_para);
                }
            }
        }
        break;

    	default:
    		break;
    }
}

static __s32 ui_party_mode_key_proc(ui_party_mode_para_t * party_mode_para)
{
	__u16 index = 0;
	__u8 manual_cancel_auto_pty_seek = 0;
	static int down_flag = 0;
	static int last_key = 0;
	
	__party_mode_msg("party_mode_para->indev_data->event_code=%d..\n",party_mode_para->indev_data->event_code);
	__party_mode_msg("party_mode_para->indev_data->key=%d..\n",party_mode_para->indev_data->key);

	if( KEY_DOWN_ACTION == party_mode_para->indev_data->event_code )
	{
		last_key = party_mode_para->indev_data->key;
		
		switch(party_mode_para->indev_data->key)
		{
            case GUI_MSG_KEY_ENTER:
            {
                if(party_mode_para->win_type == PARTY_WIN_RECEIVER)
                    down_flag = 1;
            }
            break;
            
			case GUI_MSG_KEY_LEFT:
			{
				
			}
			break;
			case GUI_MSG_KEY_RIGHT:
			{
				
			}
			break;
			default:
				break;
		}
	}
	else if( KEY_REPEAT_ACTION == party_mode_para->indev_data->event_code )
	{
		switch(party_mode_para->indev_data->key)
		{
			case GUI_MSG_KEY_LONGRIGHT:
			{

			}
            break;
            
			case GUI_MSG_KEY_LONGLEFT:
			{

			}
			break;	
            
            case GUI_MSG_KEY_LONGMENU:
            {
                if(GUI_MSG_KEY_MENU == last_key)
                {
                    if(party_mode_para->win_type == PARTY_WIN_NONE)
                    {
                        //ui_party_mode_open_bar_menu_event_handler(party_mode_para);
                    }
                    else
                    {
                        ui_party_mode_top_right_event_handler(party_mode_para);
                    }
                }
            }
            break;
		}
		
		last_key = party_mode_para->indev_data->key;
	}
	else if( KEY_UP_ACTION == party_mode_para->indev_data->event_code )
	{
		switch(party_mode_para->indev_data->key)
		{
            case GUI_MSG_KEY_ENTER:
            {
                __party_mode_msg("down_flag=%d, win_type=%d, menu_win=%d, bottom_fouse_flag=%d\n", down_flag, party_mode_para->win_type, party_mode_para->source_para->menu_win, party_mode_para->bottom_fouse_flag);
                if(down_flag && party_mode_para->win_type == PARTY_WIN_RECEIVER && party_mode_para->source_para->menu_win == SOURCE_MENU_WIN_NONE && party_mode_para->bottom_fouse_flag == 0)
                {
                    ui_party_mode_bottom_group_update(party_mode_para, 1, 0, 0);
                }
            }
            break;

			case GUI_MSG_KEY_RIGHT:
			{
				if(GUI_MSG_KEY_RIGHT == last_key)
				{

				}
			}
			break;

			case GUI_MSG_KEY_LONGRIGHT:
			{
				if(GUI_MSG_KEY_LONGRIGHT == last_key)
				{

				}
			}
			break;
			
			case GUI_MSG_KEY_LEFT:
			{
				if(GUI_MSG_KEY_LEFT == last_key)
				{

				}
			}
			break;

            case GUI_MSG_KEY_LONGLEFT:
			{
				if(GUI_MSG_KEY_LONGLEFT == last_key)
                {

                }
			}
			break;

            case GUI_MSG_KEY_MENU:
            {
                if(GUI_MSG_KEY_MENU == last_key)
                {
                    root_app_para_t * root_app_para = app_root_get_para();
                    
                    eLIBs_printf("win_type=%d, source_para->menu_win = %d, root_app_para->home_store=%d\n", party_mode_para->win_type, party_mode_para->source_para->menu_win, root_app_para->home_store);
                    if(((party_mode_para->win_type == PARTY_WIN_SELECT_MODE && party_mode_para->source_para->menu_win == SOURCE_MENU_WIN_MODE) 
                    || (party_mode_para->win_type == PARTY_WIN_RECEIVER && party_mode_para->source_para->menu_win == SOURCE_MENU_WIN_NONE))
                    && (root_app_para->home_store == 1))
                    {
                        if(party_mode_para->bottom_fouse_flag == 1)
                        {
                            ui_party_mode_bottom_group_update(party_mode_para, 0, 0, 0);
                        }
                        else
                        {
                            ui_party_mode_source_event_handler(party_mode_para);
                        }
                    }
                    else if(party_mode_para->win_type == PARTY_WIN_BROADCAST)
                    {
                        ui_party_mode_switch_win(party_mode_para, PARTY_WIN_SELECT_MODE);
                    }
                    else if(party_mode_para->win_type == PARTY_WIN_DEVICE_LIST || party_mode_para->win_type == PARTY_WIN_KNOWN_DEVICE || party_mode_para->win_type == PARTY_WIN_MENU)
                    {
                        ui_party_mode_switch_win(party_mode_para, PARTY_WIN_RECEIVER);
                    }
                }
            }
            break;

            case GUI_MSG_KEY_PLAY_PAUSE:
            {
                if(GUI_MSG_KEY_PLAY_PAUSE == last_key)
                {

                }
            }
            break;
						
			default:
				break;
		}
		
		last_key = 0;
        down_flag = 0;
	}
	
	return EPDK_OK;
}

///////////////////// FUNCTIONS ////////////////////
static void ui_event_party_mode_screen_cb(lv_event_t * e)
{
	__s32 ret = 0;
    ui_party_mode_para_t * party_mode_para = lv_event_get_user_data(e);
    lv_event_code_t event_code = lv_event_get_code(e);
    lv_obj_t * target = lv_event_get_target(e);
    lv_obj_t * current_target = lv_event_get_current_target(e);

    if(event_code == LV_EVENT_MSG_RECEIVED)
    {
		__gui_msg_t * msg = (__gui_msg_t *)lv_event_get_param(e);
		__party_mode_msg("ui_event_party_mode_screen:msg->id=%d\n", msg->id);
		if((lv_obj_has_flag(current_target, LV_OBJ_FLAG_HIDDEN)) 
			&& (party_mode_para->mode_store == 1)
			&& (LOWORD(msg->id) != GUI_MSG_SET_APP_LAYER_SUSPEND)
			&& (LOWORD(msg->id) != GUI_MSG_SET_APP_LAYER_ON)
			&& (LOWORD(msg->id) != GUI_MSG_SET_APP_LAYER_SLEEP)
			&& (LOWORD(msg->id) != GUI_MSG_SET_APP_LAYER_OFF)
			&& (LOWORD(msg->id) != GUI_MSG_SET_APP_STORE_LAYER_SUSPEND)
			&& (LOWORD(msg->id) != GUI_MSG_SET_APP_STORE_LAYER_ON)
			&& (LOWORD(msg->id) != GUI_MSG_SET_APP_STORE_LAYER_SLEEP)
			&& (LOWORD(msg->id) != GUI_MSG_SET_APP_STORE_LAYER_OFF)
			&& (HIWORD(msg->id) != GUI_MSG_PARTY_MODE_REFRESH_UNINIT)
			)
		{
			eLIBs_printf("mode_store = %d, HIWORD(msg->id)=%d, lv_obj_has_flag(current_target, LV_OBJ_FLAG_HIDDEN)=%d!!!!!!!!!\n", party_mode_para->mode_store, HIWORD(msg->id), lv_obj_has_flag(current_target, LV_OBJ_FLAG_HIDDEN));
			return;
		}
            
		if((party_mode_para->mode_uninit_flag == 1) && (HIWORD(msg->id) != GUI_MSG_PARTY_MODE_REFRESH_UNINIT))
		{
			eLIBs_printf("party_mode_para->mode_uninit_flag == 1 HIWORD(msg->id)=%d!!!!!!!!!\n", HIWORD(msg->id));
			return;
		}
        
    	switch(LOWORD(msg->id)) 
    	{
			case GUI_MSG_SET_APP_LAYER_ON:
			case GUI_MSG_SET_APP_STORE_LAYER_ON:
			{
				__party_mode_msg("GUI_MSG_SET_APP_STORE_LAYER_ON\n");
				ui_party_mode_layer_on(party_mode_para);
			}
			break;
			
			case GUI_MSG_SET_APP_LAYER_SUSPEND:
			case GUI_MSG_SET_APP_STORE_LAYER_SLEEP:
			{
				__party_mode_msg("GUI_MSG_SET_APP_STORE_LAYER_SLEEP\n");
				ui_party_mode_layer_sleep(party_mode_para);
			}
			break;
			
			case GUI_MSG_PAINT:
			{
				ui_party_mode_refresh_handler(party_mode_para, msg);
			}
			break;

			case DSK_MSG_HFP_STATUS:
			{
			}
			break;
			case DSK_MSG_DVR_CONNECT_STATUS:
			{
			}
			break;
						
			case GUI_MSG_MUTE_STATUS_CHANGE:
			{
			}
			break;
			
#ifdef AOA_WALLPAPER_ENTER
			case DSK_MSG_AOA_APP_CHANGE_BG:
			{
				reg_system_para_t*sys_para;
				sys_para = (reg_system_para_t*)dsk_reg_get_para_by_app(REG_APP_SYSTEM);
		
				if(lv_obj_has_flag(current_target, LV_OBJ_FLAG_HIDDEN))
				{
					break;
				}
				gscene_bgd_set_img_src(BG_INDEX_AUTO);
			}
			break;
#endif

            case DSK_MSG_HOME_ANIM_STOP:
            {
				__party_mode_msg("party_mode_para->enter_home_flag=%d\n", party_mode_para->enter_home_flag);
                if(party_mode_para->enter_home_flag)
                {
                    party_mode_para->enter_home_flag = 0;
                    if(party_mode_para->source_para->party_info.role == PARTY_ROLE_BROADCAST && (party_mode_para->source_para->party_info.bc_info.source > 0))
                    {
                        ui_party_mode_switch_win(party_mode_para, PARTY_WIN_BROADCAST);
                    }
                    else if(party_mode_para->source_para->party_info.role == PARTY_ROLE_RECEIVER)
                    {
                        ui_party_mode_switch_win(party_mode_para, PARTY_WIN_RECEIVER);
                    }
                    else
                    {
                        ui_source_title(party_mode_para->source_para, "Party Mode", 0);

                        if(party_mode_para->win_type != PARTY_WIN_SELECT_MODE)
                        {
                            ui_source_sub_close(party_mode_para->source_para);
                            ui_party_mode_switch_win(party_mode_para, PARTY_WIN_NONE);
                            ui_party_mode_switch_win(party_mode_para, PARTY_WIN_SELECT_MODE);
                        }
                        else
                        {
                            lv_group_add_obj(lv_group_get_default(), ui_party_mode_select_mode_button_broadcast);
                            lv_group_add_obj(lv_group_get_default(), ui_party_mode_select_mode_button_receiver);
                            lv_group_focus_obj(ui_party_mode_select_mode_button_broadcast);
                            ui_source_group_update(party_mode_para->source_para, 0);
                        }
                    }
                }
            }
            break;

            case GUI_MSG_PARTY_MODE_DIALOG_CONFIRM:
            {
                __party_mode_msg("GUI_MSG_PARTY_MODE_DIALOG_CONFIRM\n");
                ui_party_mode_tips_dialog_confirm(party_mode_para);
                //ui_party_mode_switch_win(party_mode_para, PARTY_WIN_BROADCAST);
            }
            break;
            
            case GUI_MSG_PARTY_MODE_DIALOG_CANCEL:
            {
                __party_mode_msg("GUI_MSG_PARTY_MODE_DIALOG_CANCEL\n");
                ui_party_mode_tips_dialog_cancel(party_mode_para);
            }
            break;
            
            case GUI_MSG_PARTY_MODE_DEL_DEVICE_DIALOG_CONFIRM:
            {
                __party_mode_msg("GUI_MSG_PARTY_MODE_DEL_DEVICE_DIALOG_CONFIRM\n");
                ui_party_mode_del_deivce_tips_dialog_confirm(party_mode_para);
            }
            break;
            
            case GUI_MSG_PARTY_MODE_DEL_DEVICE_DIALOG_CANCEL:
            {
                __party_mode_msg("GUI_MSG_PARTY_MODE_DEL_DEVICE_DIALOG_CANCEL\n");
                ui_party_mode_del_deivce_tips_dialog_cancel(party_mode_para);
            }
            break;


            case GUI_MSG_UI_UPDATE:
            {
                ui_party_mode_ui_update_proc(party_mode_para, msg);
            }
            break;

		    default:
				__party_mode_msg("msg->id: %d\n", msg->id);
		    break;
	    }
    }
    else if(event_code == LV_EVENT_KEY)
    {
    	party_mode_para->indev_data = lv_event_get_param(e);
		ui_party_mode_key_proc(party_mode_para);
        if(!party_mode_para->bottom_fouse_flag || (party_mode_para->indev_data->key != GUI_MSG_KEY_UP && party_mode_para->indev_data->key != GUI_MSG_KEY_DOWN))
        {
            ui_source_send_event(party_mode_para->source_para, LV_EVENT_KEY, party_mode_para->indev_data);
        }
    }
}

static void ui_event_party_mode_play_logo_panel(lv_event_t * e)
{
	ui_party_mode_para_t * party_mode_para = lv_event_get_user_data(e);
    lv_event_code_t event_code = lv_event_get_code(e);
    lv_obj_t * target = lv_event_get_target(e);
    lv_obj_t * current_target = lv_event_get_current_target(e);

	if(event_code == LV_EVENT_PRESSED)
	{
		app_root_msg_send_opn(GUI_MSG_BEEP_SETTING,SEND_MCU_BEEP_SETTING_TIME,0,0,0,0,0);
	}
	else if(event_code == LV_EVENT_SHORT_CLICKED)
	{
        ui_party_mode_source_event_handler(party_mode_para);
	}
}

static void ui_event_party_mode_left_cb(lv_event_t * e)
{
	ui_party_mode_para_t * party_mode_para = lv_event_get_user_data(e);
    lv_event_code_t event_code = lv_event_get_code(e);
    lv_obj_t * target = lv_event_get_target(e);
    lv_obj_t * current_target = lv_event_get_current_target(e);

	if(event_code == LV_EVENT_PRESSED)
	{
		app_root_msg_send_opn(GUI_MSG_BEEP_SETTING,SEND_MCU_BEEP_SETTING_TIME,0,0,0,0,0);
	}
	else if(event_code == LV_EVENT_SHORT_CLICKED)
	{
        ui_party_mode_top_left_event_handler(party_mode_para);
	}
}

static void ui_event_party_mode_right_cb(lv_event_t * e)
{
	ui_party_mode_para_t * party_mode_para = lv_event_get_user_data(e);
    lv_event_code_t event_code = lv_event_get_code(e);
    lv_obj_t * target = lv_event_get_target(e);
    lv_obj_t * current_target = lv_event_get_current_target(e);

	if(event_code == LV_EVENT_PRESSED)
	{
		app_root_msg_send_opn(GUI_MSG_BEEP_SETTING,SEND_MCU_BEEP_SETTING_TIME,0,0,0,0,0);
	}
	else if(event_code == LV_EVENT_SHORT_CLICKED)
	{
        ui_party_mode_top_right_event_handler(party_mode_para);
	}
}

static void ui_event_party_mode_bottom_list_cb(lv_event_t * e)
{
    lv_event_code_t event_code = lv_event_get_code(e);
	ui_party_mode_para_t * party_mode_para = lv_event_get_user_data(e);

    if(event_code == LV_EVENT_PRESSED) 
    {
		ui_source_keystripe_anim(party_mode_para->source_para);
        keypressanim_Animation(ui_party_mode_bottom_list_img, 0);
		_ui_image_set_property(ui_party_mode_bottom_list_img, _UI_IMAGE_PROPERTY_IMAGE, ui_party_mode_get_res(party_mode_para, PARTY_THEME_COM_BOT_LIST_D_BMP));
    }
    else if(event_code == LV_EVENT_RELEASED) 
    {
        keyreleaseanim_Animation(ui_party_mode_bottom_list_img, 0);
    	_ui_image_set_property(ui_party_mode_bottom_list_img, _UI_IMAGE_PROPERTY_IMAGE, ui_party_mode_get_res(party_mode_para, PARTY_THEME_COM_BOT_LIST_N_BMP));
    }
	else if(event_code == LV_EVENT_SHORT_CLICKED)
	{
        ui_party_mode_switch_win(party_mode_para, PARTY_WIN_KNOWN_DEVICE);
	}
}

static void ui_event_party_mode_bottom_browse_cb(lv_event_t * e)
{
    lv_event_code_t event_code = lv_event_get_code(e);
	ui_party_mode_para_t * party_mode_para = lv_event_get_user_data(e);

    if(event_code == LV_EVENT_PRESSED) 
    {
		ui_source_keystripe_anim(party_mode_para->source_para);
        keypressanim_Animation(ui_party_mode_bottom_browse_img, 0);
		_ui_image_set_property(ui_party_mode_bottom_browse_img, _UI_IMAGE_PROPERTY_IMAGE, ui_party_mode_get_res(party_mode_para, PARTY_THEME_COM_BOT_BROWSE_D_BMP));
    }
    else if(event_code == LV_EVENT_RELEASED) 
    {
        keyreleaseanim_Animation(ui_party_mode_bottom_browse_img, 0);
    	_ui_image_set_property(ui_party_mode_bottom_browse_img, _UI_IMAGE_PROPERTY_IMAGE, ui_party_mode_get_res(party_mode_para, PARTY_THEME_COM_BOT_BROWSE_N_BMP));
    }
	else if(event_code == LV_EVENT_SHORT_CLICKED)
	{
        ui_party_mode_switch_win(party_mode_para, PARTY_WIN_DEVICE_LIST);
	}
}

static void ui_event_party_mode_bottom_receiver_cb(lv_event_t * e)
{
    lv_event_code_t event_code = lv_event_get_code(e);
	ui_party_mode_para_t * party_mode_para = lv_event_get_user_data(e);

    if(event_code == LV_EVENT_PRESSED) 
    {
		ui_source_keystripe_anim(party_mode_para->source_para);
    }
    else if(event_code == LV_EVENT_RELEASED) 
    {
        
    }
	else if(event_code == LV_EVENT_SHORT_CLICKED)
	{
        ui_party_mode_tips_dialog_create(party_mode_para, STRING_ROOT_CHANGE_BROADCAST_MODE, 0, 0, ADLG_YESNO);
	}
}

static void ui_event_party_mode_open_bar_cb(lv_event_t * e)
{
    lv_event_code_t event_code = lv_event_get_code(e);
	ui_party_mode_para_t * party_mode_para = lv_event_get_user_data(e);

    if(event_code == LV_EVENT_PRESSED)
	{
		app_root_msg_send_opn(GUI_MSG_BEEP_SETTING,SEND_MCU_BEEP_SETTING_TIME,0,0,0,0,0);
	}
	else if(event_code == LV_EVENT_SHORT_CLICKED) 
    {
        ui_party_mode_switch_win(party_mode_para, PARTY_WIN_MENU);
	}
}

#if 0
static void ui_event_party_mode_device_cb(lv_event_t * e)
{
	ui_party_mode_para_t * party_mode_para = lv_event_get_user_data(e);
    lv_event_code_t event_code = lv_event_get_code(e);
    lv_obj_t * target = lv_event_get_target(e);
    lv_obj_t * current_target = lv_event_get_current_target(e);

	if(event_code == LV_EVENT_PRESSED)
	{
		app_root_msg_send_opn(GUI_MSG_BEEP_SETTING,SEND_MCU_BEEP_SETTING_TIME,0,0,0,0,0);
	}
	else if(event_code == LV_EVENT_SHORT_CLICKED)
	{
        ui_party_mode_device_event_handler(party_mode_para, PARTY_MENU_MY_DEVICE);
	}
}

static void ui_event_party_mode_search_cb(lv_event_t * e)
{
	ui_party_mode_para_t * party_mode_para = lv_event_get_user_data(e);
    lv_event_code_t event_code = lv_event_get_code(e);
    lv_obj_t * target = lv_event_get_target(e);
    lv_obj_t * current_target = lv_event_get_current_target(e);

	if(event_code == LV_EVENT_PRESSED)
	{
		app_root_msg_send_opn(GUI_MSG_BEEP_SETTING,SEND_MCU_BEEP_SETTING_TIME,0,0,0,0,0);
	}
	else if(event_code == LV_EVENT_SHORT_CLICKED)
	{
        ui_party_mode_device_event_handler(party_mode_para, PARTY_MENU_NEW_DEVICE);
	}
}

void ui_event_party_mode_open_bar_cb(lv_event_t * e)
{
    lv_event_code_t event_code = lv_event_get_code(e);
	ui_party_mode_para_t * party_mode_para = lv_event_get_user_data(e);

    if(event_code == LV_EVENT_PRESSED)
	{
		//ui_fm_keystripe_anim(fm_para);
		app_root_msg_send_opn(GUI_MSG_BEEP_SETTING,SEND_MCU_BEEP_SETTING_TIME,0,0,0,0,0);
	}
	else if(event_code == LV_EVENT_SHORT_CLICKED) {
        ui_party_mode_open_bar_menu_event_handler(party_mode_para);
	}
}

void ui_event_party_mode_bottom_menu_cb(lv_event_t * e)
{
    lv_event_code_t event_code = lv_event_get_code(e);
	ui_party_mode_para_t * party_mode_para = lv_event_get_user_data(e);

    if(event_code == LV_EVENT_PRESSED)
	{
		app_root_msg_send_opn(GUI_MSG_BEEP_SETTING,SEND_MCU_BEEP_SETTING_TIME,0,0,0,0,0);
	}
	else if(event_code == LV_EVENT_SHORT_CLICKED) {
        ui_party_mode_bottom_menu_event_handler(party_mode_para);
	}
}
#endif

static void ui_event_party_mode_broadcast_cb(lv_event_t * e)
{
	ui_party_mode_para_t * party_mode_para = lv_event_get_user_data(e);
    lv_event_code_t event_code = lv_event_get_code(e);
    lv_obj_t * target = lv_event_get_target(e);
    lv_obj_t * current_target = lv_event_get_current_target(e);

	if(event_code == LV_EVENT_PRESSED)
	{
		app_root_msg_send_opn(GUI_MSG_BEEP_SETTING,SEND_MCU_BEEP_SETTING_TIME,0,0,0,0,0);
	}
	else if(event_code == LV_EVENT_SHORT_CLICKED)
	{
        __party_mode_msg("party_mode_para->source_para->party_info.role=%d\n", party_mode_para->source_para->party_info.role);
		if(party_mode_para->source_para->party_info.role != PARTY_ROLE_BROADCAST)
		{ //send to mcu change role
			ui_party_comms_set_device_role((void *)&party_mode_para->source_para->party_info,PARTY_ROLE_BROADCAST);
		}
        else
        {
            ui_party_mode_switch_win(party_mode_para, PARTY_WIN_BROADCAST);
        }
	}
}

static void ui_event_party_mode_receiver_cb(lv_event_t * e)
{
	ui_party_mode_para_t * party_mode_para = lv_event_get_user_data(e);
    lv_event_code_t event_code = lv_event_get_code(e);
    lv_obj_t * target = lv_event_get_target(e);
    lv_obj_t * current_target = lv_event_get_current_target(e);

	if(event_code == LV_EVENT_PRESSED)
	{
		app_root_msg_send_opn(GUI_MSG_BEEP_SETTING,SEND_MCU_BEEP_SETTING_TIME,0,0,0,0,0);
	}
	else if(event_code == LV_EVENT_SHORT_CLICKED)
	{
        __party_mode_msg("party_mode_para->source_para->party_info.role=%d\n", party_mode_para->source_para->party_info.role);
		if(party_mode_para->source_para->party_info.role != PARTY_ROLE_RECEIVER)
		{ //send to mcu change role
			ui_party_comms_set_device_role((void *)&party_mode_para->source_para->party_info,PARTY_ROLE_RECEIVER);
		}
		else
		{
            ui_party_mode_role_update(party_mode_para, PARTY_ROLE_RECEIVER, 0);
		}
        //ui_party_mode_switch_win(party_mode_para, PARTY_WIN_RECEIVER);
	}
}

static void ui_event_party_mode_broadcast_source_cb(lv_event_t * e)
{
    lv_event_code_t event_code = lv_event_get_code(e);
	ui_party_mode_para_t * party_mode_para = lv_event_get_user_data(e);
    lv_obj_t * obj = lv_event_get_target(e);
    //static int32_t selected = -1;

    //eLIBs_printf("event_code=%d\n", event_code);
    if(event_code == LV_EVENT_SHORT_CLICKED) 
    {
        #if 1
        uint32_t cur_selected = lv_obj_get_index(obj);

        ui_party_mode_broadcast_source_event_handler(party_mode_para, cur_selected);
        #else
        uint32_t cur_selected = lv_roller_get_selected(obj);

        if(selected != cur_selected)
        {
            selected = cur_selected;
        }
        else
        {
            //eLIBs_printf("ui_event_music_repeat_roller selected=%d\n", selected);
    		ui_party_mode_broadcast_source_event_handler(party_mode_para, selected);
            selected = -1;
        }
        #endif
    }
}

static void ui_event_party_mode_menu_item_cb(lv_event_t * e)
{
	ui_party_mode_para_t * party_mode_para = lv_event_get_user_data(e);
    lv_event_code_t event_code = lv_event_get_code(e);
    lv_obj_t * target = lv_event_get_target(e);
    lv_obj_t * current_target = lv_event_get_current_target(e);
    lv_obj_t * list_item_panel = lv_obj_get_child(current_target, 0);
    lv_obj_t * item_text_panel = lv_obj_get_child(list_item_panel, 0);
    lv_obj_t * item_shadow_text = lv_obj_get_child(item_text_panel, 0);
    lv_obj_t * item_main_text = lv_obj_get_child(item_text_panel, 1);
	__u32 index = lv_obj_get_index(current_target);
    static __u8 move_flag = 0;

	if(event_code == LV_EVENT_PRESSED)
	{		
        move_flag = 0;
		app_root_msg_send_opn(GUI_MSG_BEEP_SETTING,SEND_MCU_BEEP_SETTING_TIME,0,0,0,0,0);
	}
	else if(event_code == LV_EVENT_SHORT_CLICKED)
	{
        if(move_flag)
        {
            return;
        }
		ui_party_mode_item_event_handler(party_mode_para, current_target);
	}
	else if(event_code == LV_EVENT_LONG_PRESSED)
	{
		lv_label_set_long_mode(item_shadow_text, LV_LABEL_LONG_SCROLL_ONCE);
		lv_label_set_long_mode(item_main_text, LV_LABEL_LONG_SCROLL_ONCE);
	}
	else if(event_code == LV_EVENT_RELEASED)
	{		
		lv_label_set_long_mode(item_shadow_text, LV_LABEL_LONG_CLIP);
		lv_label_set_long_mode(item_main_text, LV_LABEL_LONG_CLIP);
	}
    else if(event_code == LV_EVENT_PRESS_MOVEING)
	{		
        move_flag = 1;
	}
	else if(event_code == LV_EVENT_CLICKED)
	{		
        move_flag = 0;
	}
}

static void ui_event_party_mode_device_cb(lv_event_t * e)
{
	ui_party_mode_para_t * party_mode_para = lv_event_get_user_data(e);
    lv_event_code_t event_code = lv_event_get_code(e);
    lv_obj_t * target = lv_event_get_target(e);
    lv_obj_t * current_target = lv_event_get_current_target(e);
    //lv_obj_t * list_item_content_panel = lv_obj_get_child(current_target, 0);
    ///lv_obj_t * item_text_panel = lv_obj_get_child(list_item_content_panel, 0);
    //lv_obj_t * item_shadow_text = lv_obj_get_child(item_text_panel, 0);
    //lv_obj_t * item_main_text = lv_obj_get_child(item_text_panel, 1);
	//__u32 index = lv_obj_get_index(current_target);
    static __u8 move_flag = 0;

	if(event_code == LV_EVENT_PRESSED)
	{		
        move_flag = 0;
		app_root_msg_send_opn(GUI_MSG_BEEP_SETTING,SEND_MCU_BEEP_SETTING_TIME,0,0,0,0,0);
	}
	else if(event_code == LV_EVENT_SHORT_CLICKED)
	{
        if(move_flag)
        {
            return;
        }
        
		ui_party_mode_device_event_handler(party_mode_para, target, current_target);
	}
	else if(event_code == LV_EVENT_LONG_PRESSED)
	{
		//lv_label_set_long_mode(item_shadow_text, LV_LABEL_LONG_SCROLL_ONCE);
		//lv_label_set_long_mode(item_main_text, LV_LABEL_LONG_SCROLL_ONCE);
	}
	else if(event_code == LV_EVENT_RELEASED)
	{		
		//lv_label_set_long_mode(item_shadow_text, LV_LABEL_LONG_CLIP);
		//lv_label_set_long_mode(item_main_text, LV_LABEL_LONG_CLIP);
	}
    else if(event_code == LV_EVENT_PRESS_MOVEING)
	{		
        move_flag = 1;
	}
	else if(event_code == LV_EVENT_CLICKED)
	{		
        move_flag = 0;
	}
}

static void ui_event_party_mode_timer_cb(lv_timer_t * t)
{	
	ui_party_mode_para_t * party_mode_para = lv_timer_get_user_data(t);

	party_mode_time_proc(party_mode_para);
	Draw_topbar_view_with_app_id(APP_PARTY_MODE_ID, 0);
}

///////////////////// SCREENS ////////////////////
static void ui_party_mode_select_mode_screen_init(ui_party_mode_para_t * party_mode_para)
{
    if(ui_party_mode_select_mode)
    {
        lv_obj_remove_flag(ui_party_mode_select_mode, LV_OBJ_FLAG_HIDDEN);     /// Flags

        lv_group_add_obj(lv_group_get_default(), ui_party_mode_select_mode_button_broadcast);
        lv_group_add_obj(lv_group_get_default(), ui_party_mode_select_mode_button_receiver);
        lv_group_focus_obj(ui_party_mode_select_mode_button_broadcast);
        return;
    }
    
    ui_party_mode_select_mode = lv_obj_create(party_mode_para->source_para->ui_menu_panel);
    lv_obj_set_width(ui_party_mode_select_mode, lv_pct(100));
    lv_obj_set_height(ui_party_mode_select_mode, lv_pct(100));
    lv_obj_set_align(ui_party_mode_select_mode, LV_ALIGN_BOTTOM_MID);
    lv_obj_set_flex_flow(ui_party_mode_select_mode, LV_FLEX_FLOW_COLUMN);
    lv_obj_set_flex_align(ui_party_mode_select_mode, LV_FLEX_ALIGN_SPACE_EVENLY, LV_FLEX_ALIGN_START, LV_FLEX_ALIGN_START);
    lv_obj_remove_flag(ui_party_mode_select_mode, LV_OBJ_FLAG_SCROLLABLE);      /// Flags
    lv_obj_set_style_bg_color(ui_party_mode_select_mode, lv_color_hex(0x000000), LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_bg_opa(ui_party_mode_select_mode, 255, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_border_width(ui_party_mode_select_mode, 0, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_pad_left(ui_party_mode_select_mode, 0, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_pad_right(ui_party_mode_select_mode, 0, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_pad_top(ui_party_mode_select_mode, 0, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_pad_bottom(ui_party_mode_select_mode, 20, LV_PART_MAIN | LV_STATE_DEFAULT);

    /*ui_party_mode_select_mode_bg = lv_image_create(ui_party_mode_select_mode);
    lv_image_set_src(ui_party_mode_select_mode_bg, ui_party_mode_get_res(party_mode_para, PARTY_TEMP_NIGHT_NIG_COM_BG_BMP));
    lv_obj_set_width(ui_party_mode_select_mode_bg, LV_SIZE_CONTENT);   /// 1
    lv_obj_set_height(ui_party_mode_select_mode_bg, LV_SIZE_CONTENT);    /// 1
    lv_obj_set_x(ui_party_mode_select_mode_bg, 0);
    lv_obj_set_y(ui_party_mode_select_mode_bg, 12);
    lv_obj_set_align(ui_party_mode_select_mode_bg, LV_ALIGN_CENTER);
    lv_obj_add_flag(ui_party_mode_select_mode_bg, LV_OBJ_FLAG_CLICKABLE | LV_OBJ_FLAG_IGNORE_LAYOUT);     /// Flags
    lv_obj_remove_flag(ui_party_mode_select_mode_bg, LV_OBJ_FLAG_SCROLLABLE);      /// Flags
    lv_image_set_scale(ui_party_mode_select_mode_bg, 320);*/

    ui_party_mode_select_mode_title = lv_obj_create(ui_party_mode_select_mode);
    lv_obj_set_width(ui_party_mode_select_mode_title, lv_pct(100));
    lv_obj_set_height(ui_party_mode_select_mode_title, LV_SIZE_CONTENT);    /// 20
    lv_obj_set_align(ui_party_mode_select_mode_title, LV_ALIGN_TOP_MID);
    lv_obj_remove_flag(ui_party_mode_select_mode_title, LV_OBJ_FLAG_SCROLLABLE);      /// Flags
    lv_obj_set_style_bg_color(ui_party_mode_select_mode_title, lv_color_hex(0x000000), LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_bg_opa(ui_party_mode_select_mode_title, 0, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_border_width(ui_party_mode_select_mode_title, 0, LV_PART_MAIN | LV_STATE_DEFAULT);

    ui_party_mode_select_mode_title_text = lv_label_create(ui_party_mode_select_mode_title);
    lv_obj_set_width(ui_party_mode_select_mode_title_text, LV_SIZE_CONTENT);   /// 1
    lv_obj_set_height(ui_party_mode_select_mode_title_text, LV_SIZE_CONTENT);    /// 1
    lv_obj_set_align(ui_party_mode_select_mode_title_text, LV_ALIGN_TOP_MID);
    lv_label_set_text(ui_party_mode_select_mode_title_text, "Select Mode");
    lv_obj_set_style_text_color(ui_party_mode_select_mode_title_text, lv_color_hex(0xFFFFFF), LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_text_opa(ui_party_mode_select_mode_title_text, 255, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_text_font(ui_party_mode_select_mode_title_text, lv_font_medium_b.font, LV_PART_MAIN | LV_STATE_DEFAULT);

    ui_party_mode_select_mode_button = lv_obj_create(ui_party_mode_select_mode);
    lv_obj_set_width(ui_party_mode_select_mode_button, lv_pct(100));
    lv_obj_set_height(ui_party_mode_select_mode_button, LV_SIZE_CONTENT);    /// 80
    lv_obj_set_align(ui_party_mode_select_mode_button, LV_ALIGN_BOTTOM_MID);
    lv_obj_set_flex_flow(ui_party_mode_select_mode_button, LV_FLEX_FLOW_ROW);
    lv_obj_set_flex_align(ui_party_mode_select_mode_button, LV_FLEX_ALIGN_SPACE_AROUND, LV_FLEX_ALIGN_START,
                          LV_FLEX_ALIGN_START);
    lv_obj_remove_flag(ui_party_mode_select_mode_button, LV_OBJ_FLAG_SCROLLABLE);      /// Flags
    lv_obj_set_style_bg_color(ui_party_mode_select_mode_button, lv_color_hex(0x000000), LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_bg_opa(ui_party_mode_select_mode_button, 0, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_border_width(ui_party_mode_select_mode_button, 0, LV_PART_MAIN | LV_STATE_DEFAULT);

#if 1
    ui_party_mode_select_mode_button_broadcast = lv_obj_create(ui_party_mode_select_mode_button);
    lv_obj_remove_style_all(ui_party_mode_select_mode_button_broadcast);
    lv_obj_set_width(ui_party_mode_select_mode_button_broadcast, 237);
    lv_obj_set_height(ui_party_mode_select_mode_button_broadcast, 156);
    lv_obj_remove_flag(ui_party_mode_select_mode_button_broadcast, LV_OBJ_FLAG_SCROLLABLE);      /// Flags
    lv_obj_set_style_bg_color(ui_party_mode_select_mode_button_broadcast, lv_color_hex(0x000000), LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_bg_opa(ui_party_mode_select_mode_button_broadcast, 0, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_border_width(ui_party_mode_select_mode_button_broadcast, 2, LV_PART_MAIN | LV_STATE_FOCUSED);
    lv_obj_set_style_border_color(ui_party_mode_select_mode_button_broadcast, lv_color_hex(0xff0000), LV_PART_MAIN | LV_STATE_FOCUSED);
    lv_obj_set_style_border_opa(ui_party_mode_select_mode_button_broadcast, 255, LV_PART_MAIN | LV_STATE_FOCUSED);
    lv_obj_set_style_bg_image_src(ui_party_mode_select_mode_button_broadcast, ui_party_mode_get_res(party_mode_para, PARTY_THEME_PM_BC_ICON_BMP), LV_PART_MAIN | LV_STATE_DEFAULT);
    //lv_obj_set_style_bg_image_src(ui_party_mode_select_mode_button_broadcast, ui_party_mode_get_res(party_mode_para, PARTY_TEMP_SOURCE_SEL_BG_BMP), LV_PART_MAIN | LV_STATE_PRESSED);
#else
    ui_party_mode_select_mode_button_broadcast = lv_imagebutton_create(ui_party_mode_select_mode_button);
    lv_imagebutton_set_src(ui_party_mode_select_mode_button_broadcast, LV_IMAGEBUTTON_STATE_RELEASED, NULL, ui_party_mode_get_res(party_mode_para, PARTY_TEMP_SOURCE_SEL_BG_N_BMP), NULL);
    lv_imagebutton_set_src(ui_party_mode_select_mode_button_broadcast, LV_IMAGEBUTTON_STATE_PRESSED, NULL, ui_party_mode_get_res(party_mode_para, PARTY_TEMP_SOURCE_SEL_BG_BMP), NULL);
    lv_obj_set_height(ui_party_mode_select_mode_button_broadcast, 177);
    lv_obj_set_width(ui_party_mode_select_mode_button_broadcast, LV_SIZE_CONTENT);   /// 1
    lv_obj_set_align(ui_party_mode_select_mode_button_broadcast, LV_ALIGN_CENTER);
#endif

    ui_party_mode_select_mode_button_broadcast_text = lv_label_create(ui_party_mode_select_mode_button_broadcast);
    lv_obj_set_width(ui_party_mode_select_mode_button_broadcast_text, LV_SIZE_CONTENT);   /// 1
    lv_obj_set_height(ui_party_mode_select_mode_button_broadcast_text, LV_SIZE_CONTENT);    /// 1
    lv_obj_set_y(ui_party_mode_select_mode_button_broadcast_text, lv_pct(-10));
    lv_obj_set_align(ui_party_mode_select_mode_button_broadcast_text, LV_ALIGN_BOTTOM_MID);
    lv_label_set_text(ui_party_mode_select_mode_button_broadcast_text, "Broadcast");
    lv_obj_set_style_text_color(ui_party_mode_select_mode_button_broadcast_text, lv_color_hex(0xFFFFFF), LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_text_opa(ui_party_mode_select_mode_button_broadcast_text, 255, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_text_font(ui_party_mode_select_mode_button_broadcast_text, lv_font_medium_b.font, LV_PART_MAIN | LV_STATE_DEFAULT);

#if 1
    ui_party_mode_select_mode_button_receiver = lv_obj_create(ui_party_mode_select_mode_button);
    lv_obj_remove_style_all(ui_party_mode_select_mode_button_receiver);
    lv_obj_set_width(ui_party_mode_select_mode_button_receiver, 237);
    lv_obj_set_height(ui_party_mode_select_mode_button_receiver, 156);
    lv_obj_remove_flag(ui_party_mode_select_mode_button_receiver, LV_OBJ_FLAG_SCROLLABLE);      /// Flags
    lv_obj_set_style_bg_color(ui_party_mode_select_mode_button_receiver, lv_color_hex(0x000000), LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_bg_opa(ui_party_mode_select_mode_button_receiver, 0, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_border_width(ui_party_mode_select_mode_button_receiver, 2, LV_PART_MAIN | LV_STATE_FOCUSED);
    lv_obj_set_style_border_color(ui_party_mode_select_mode_button_receiver, lv_color_hex(0xff0000), LV_PART_MAIN | LV_STATE_FOCUSED);
    lv_obj_set_style_border_opa(ui_party_mode_select_mode_button_receiver, 255, LV_PART_MAIN | LV_STATE_FOCUSED);
    lv_obj_set_style_bg_image_src(ui_party_mode_select_mode_button_receiver, ui_party_mode_get_res(party_mode_para, PARTY_THEME_PM_RE_ICON_BMP), LV_PART_MAIN | LV_STATE_DEFAULT);
    //lv_obj_set_style_bg_image_src(ui_party_mode_select_mode_button_receiver, ui_party_mode_get_res(party_mode_para, PARTY_TEMP_SOURCE_SEL_BG_BMP), LV_PART_MAIN | LV_STATE_PRESSED);
#else
    ui_party_mode_select_mode_button_receiver = lv_imagebutton_create(ui_party_mode_select_mode_button);
    lv_imagebutton_set_src(ui_party_mode_select_mode_button_receiver, LV_IMAGEBUTTON_STATE_RELEASED, NULL, ui_party_mode_get_res(party_mode_para, PARTY_TEMP_SOURCE_SEL_BG_N_BMP), NULL);
    lv_imagebutton_set_src(ui_party_mode_select_mode_button_receiver, LV_IMAGEBUTTON_STATE_PRESSED, NULL, ui_party_mode_get_res(party_mode_para, PARTY_TEMP_SOURCE_SEL_BG_BMP), NULL);
    lv_obj_set_height(ui_party_mode_select_mode_button_receiver, 177);
    lv_obj_set_width(ui_party_mode_select_mode_button_receiver, LV_SIZE_CONTENT);   /// 1
    lv_obj_set_align(ui_party_mode_select_mode_button_receiver, LV_ALIGN_CENTER);
#endif

    ui_party_mode_select_mode_button_receiver_text = lv_label_create(ui_party_mode_select_mode_button_receiver);
    lv_obj_set_width(ui_party_mode_select_mode_button_receiver_text, LV_SIZE_CONTENT);   /// 1
    lv_obj_set_height(ui_party_mode_select_mode_button_receiver_text, LV_SIZE_CONTENT);    /// 1
    lv_obj_set_y(ui_party_mode_select_mode_button_receiver_text, lv_pct(-10));
    lv_obj_set_align(ui_party_mode_select_mode_button_receiver_text, LV_ALIGN_BOTTOM_MID);
    lv_label_set_text(ui_party_mode_select_mode_button_receiver_text, "Receiver");
    lv_obj_set_style_text_color(ui_party_mode_select_mode_button_receiver_text, lv_color_hex(0xFFFFFF), LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_text_opa(ui_party_mode_select_mode_button_receiver_text, 255, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_text_font(ui_party_mode_select_mode_button_receiver_text, lv_font_medium_b.font, LV_PART_MAIN | LV_STATE_DEFAULT);

	lv_obj_add_event_cb(ui_party_mode_select_mode_button_broadcast, ui_event_party_mode_broadcast_cb, LV_EVENT_ALL, party_mode_para);
	lv_obj_add_event_cb(ui_party_mode_select_mode_button_receiver, ui_event_party_mode_receiver_cb, LV_EVENT_ALL, party_mode_para);

    lv_group_add_obj(lv_group_get_default(), ui_party_mode_select_mode_button_broadcast);
    lv_group_add_obj(lv_group_get_default(), ui_party_mode_select_mode_button_receiver);
    lv_group_focus_obj(ui_party_mode_select_mode_button_broadcast);
}

static void ui_party_mode_select_mode_screen_uninit(ui_party_mode_para_t * party_mode_para)
{		
    lv_group_remove_obj(ui_party_mode_select_mode_button_broadcast);
    lv_group_remove_obj(ui_party_mode_select_mode_button_receiver);
    
	lv_obj_add_flag(ui_party_mode_select_mode, LV_OBJ_FLAG_HIDDEN);
}

static void ui_party_mode_broadcast_screen_init(ui_party_mode_para_t * party_mode_para)
{
    __u8 i;
    __u8 focus_flag = 0;
    lv_obj_t *ui_source;
    lv_obj_t *ui_source_img;
    lv_obj_t *ui_source_img_s;

    if(ui_party_mode_broadcast)
    {
        lv_obj_remove_flag(ui_party_mode_broadcast, LV_OBJ_FLAG_HIDDEN);     /// Flags
        
        if((party_mode_para->source_para->party_info.bc_info.source == PARTY_D_SOURCE_AUXIN))
            ui_source_sel_group_update(ui_party_mode_broadcast_source, 2, 1);
        else if((party_mode_para->source_para->party_info.bc_info.source == PARTY_D_SOURCE_BT_AUDIO))
            ui_source_sel_group_update(ui_party_mode_broadcast_source, 1, 1);
        else
            ui_source_sel_group_update(ui_party_mode_broadcast_source, 0, 1);
        return;
    }
    
    ui_party_mode_broadcast = lv_obj_create(party_mode_para->source_para->ui_menu_panel);
    lv_obj_set_width(ui_party_mode_broadcast, lv_pct(100));
    lv_obj_set_height(ui_party_mode_broadcast, lv_pct(100));
    lv_obj_set_align(ui_party_mode_broadcast, LV_ALIGN_BOTTOM_MID);
    lv_obj_remove_flag(ui_party_mode_broadcast, LV_OBJ_FLAG_SCROLLABLE);      /// Flags
    lv_obj_set_style_bg_color(ui_party_mode_broadcast, lv_color_hex(0x000000), LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_bg_opa(ui_party_mode_broadcast, 220, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_border_width(ui_party_mode_broadcast, 0, LV_PART_MAIN | LV_STATE_DEFAULT);

#if 1
    ui_party_mode_broadcast_source = lv_obj_create(ui_party_mode_broadcast);
    lv_obj_set_width(ui_party_mode_broadcast_source, lv_pct(100));
    lv_obj_set_height(ui_party_mode_broadcast_source, lv_pct(100));
    lv_obj_set_align(ui_party_mode_broadcast_source, LV_ALIGN_BOTTOM_MID);
    lv_obj_set_flex_flow(ui_party_mode_broadcast_source, LV_FLEX_FLOW_ROW_WRAP);
    lv_obj_set_flex_align(ui_party_mode_broadcast_source, LV_FLEX_ALIGN_SPACE_EVENLY, LV_FLEX_ALIGN_START, LV_FLEX_ALIGN_START);
    lv_obj_remove_flag(ui_party_mode_broadcast_source, LV_OBJ_FLAG_SCROLLABLE);      /// Flags
    lv_obj_set_style_bg_color(ui_party_mode_broadcast_source, lv_color_hex(0x000000), LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_bg_opa(ui_party_mode_broadcast_source, 0, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_border_width(ui_party_mode_broadcast_source, 0, LV_PART_MAIN | LV_STATE_DEFAULT);

    ui_source_sel_screen_init(party_mode_para, ui_party_mode_broadcast_source, ui_party_mode_get_res, PARTY_TEMP_SOU_USB_BMP, PARTY_TEMP_SOU_P_L_BMP, PARTY_TEMP_SOU_S_BMP, &source_sel_map[ID_SOURCE_USB], 0);
    ui_source_sel_screen_init(party_mode_para, ui_party_mode_broadcast_source, ui_party_mode_get_res, PARTY_TEMP_SOU_BT_BMP, PARTY_TEMP_SOU_P_L_BMP, PARTY_TEMP_SOU_S_BMP, &source_sel_map[ID_SOURCE_BT], 0);
    ui_source_sel_screen_init(party_mode_para, ui_party_mode_broadcast_source, ui_party_mode_get_res, PARTY_TEMP_SOU_AUXIN_BMP, PARTY_TEMP_SOU_P_L_BMP, PARTY_TEMP_SOU_S_BMP, &source_sel_map[ID_SOURCE_AUX], 0);

    lv_obj_add_event_cb(ui_party_mode_broadcast_source, ui_event_party_mode_broadcast_source_cb, LV_EVENT_ALL, party_mode_para);

	for (i = 0; i < lv_obj_get_child_cnt(ui_party_mode_broadcast_source); i++) 
	{
        ui_source = lv_obj_get_child(ui_party_mode_broadcast_source, i);
        ui_source_img = lv_obj_get_child(ui_source, 0);
        ui_source_img_s = lv_obj_get_child(ui_source_img, 0);

        __party_mode_msg("i=%d, broadcast_source=%d\n", i, party_mode_para->source_para->party_info.bc_info.source);
		if((i == 0) && (party_mode_para->source_para->party_info.bc_info.source == PARTY_D_SOURCE_USB))
		{
            focus_flag = 1;
            lv_obj_remove_flag(ui_source_img_s, LV_OBJ_FLAG_HIDDEN);
            //lv_image_set_src(ui_source_img_s, ui_party_mode_get_res(party_mode_para, PARTY_TEMP_SOU_S_BMP));
            //lv_imagebutton_set_state(ui_source_img, LV_IMAGEBUTTON_STATE_CHECKED_RELEASED);
            ui_source_sel_group_update(ui_party_mode_broadcast_source, 0, 1);
		}
        else if((i == 1) && (party_mode_para->source_para->party_info.bc_info.source == PARTY_D_SOURCE_BT_AUDIO))
		{

            focus_flag = 1;
            lv_obj_remove_flag(ui_source_img_s, LV_OBJ_FLAG_HIDDEN);
            //lv_image_set_src(ui_source_img_s, ui_party_mode_get_res(party_mode_para, PARTY_TEMP_SOU_S_BMP));
            //lv_imagebutton_set_state(ui_source_img, LV_IMAGEBUTTON_STATE_CHECKED_RELEASED);
            ui_source_sel_group_update(ui_party_mode_broadcast_source, 1, 1);
		}
        else if((i == 2) && (party_mode_para->source_para->party_info.bc_info.source == PARTY_D_SOURCE_AUXIN))
		{
            focus_flag = 1;
            lv_obj_remove_flag(ui_source_img_s, LV_OBJ_FLAG_HIDDEN);
            //lv_image_set_src(ui_source_img_s, ui_party_mode_get_res(party_mode_para, PARTY_TEMP_SOU_S_BMP));
            //lv_imagebutton_set_state(ui_source_img, LV_IMAGEBUTTON_STATE_CHECKED_RELEASED);
            ui_source_sel_group_update(ui_party_mode_broadcast_source, 2, 1);
		}
        else
        {
            lv_obj_add_flag(ui_source_img_s, LV_OBJ_FLAG_HIDDEN);
            //lv_image_set_src(ui_source_img, NULL);
            //lv_imagebutton_set_state(ui_source_img, LV_IMAGEBUTTON_STATE_RELEASED);
        }
	}

    if(focus_flag == 0)
    {
        ui_source_sel_group_update(ui_party_mode_broadcast_source, 0, 1);
    }
#else
    ui_party_mode_broadcast_select_source = lv_roller_create(ui_party_mode_broadcast);
    lv_roller_set_options(ui_party_mode_broadcast_select_source, "USB\nBluetooth\nAUX IN", LV_ROLLER_MODE_NORMAL);
    lv_obj_set_width(ui_party_mode_broadcast_select_source, lv_pct(70));
    lv_obj_set_height(ui_party_mode_broadcast_select_source, lv_pct(65));
    lv_obj_set_align(ui_party_mode_broadcast_select_source, LV_ALIGN_CENTER);
    lv_obj_set_style_text_color(ui_party_mode_broadcast_select_source, lv_color_hex(0x808080), LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_text_opa(ui_party_mode_broadcast_select_source, 255, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_text_font(ui_party_mode_broadcast_select_source, lv_font_xlarge.font, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_bg_color(ui_party_mode_broadcast_select_source, lv_color_hex(0x898989), LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_bg_opa(ui_party_mode_broadcast_select_source, 0, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_border_width(ui_party_mode_broadcast_select_source, 0, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_radius(ui_party_mode_broadcast_select_source, 10, LV_PART_SELECTED | LV_STATE_DEFAULT);
    lv_obj_set_style_bg_color(ui_party_mode_broadcast_select_source, lv_color_hex(0x252525), LV_PART_SELECTED | LV_STATE_DEFAULT);
    lv_obj_set_style_bg_opa(ui_party_mode_broadcast_select_source, 255, LV_PART_SELECTED | LV_STATE_DEFAULT);

    lv_obj_add_event_cb(ui_party_mode_broadcast_select_source, ui_event_party_mode_broadcast_source_cb, LV_EVENT_ALL, party_mode_para);
#endif
}

static void ui_party_mode_broadcast_screen_uninit(ui_party_mode_para_t * party_mode_para)
{		
	lv_obj_add_flag(ui_party_mode_broadcast, LV_OBJ_FLAG_HIDDEN);
    ui_source_sel_group_update(ui_party_mode_broadcast_source, 0, 0);
}

static void ui_party_mode_receiver_screen_init(ui_party_mode_para_t * party_mode_para)
{
    if(ui_party_mode_receiver)
    {
        lv_obj_remove_flag(ui_party_mode_receiver, LV_OBJ_FLAG_HIDDEN);     /// Flags
        return;
    }

    ui_party_mode_receiver = lv_obj_create(ui_party_mode);
	lv_obj_remove_style_all(ui_party_mode_receiver);
    lv_obj_set_width(ui_party_mode_receiver, lv_pct(100));
    lv_obj_set_height(ui_party_mode_receiver, lv_pct(100));
    lv_obj_set_align(ui_party_mode_receiver, LV_ALIGN_BOTTOM_MID);
    lv_obj_add_flag(ui_party_mode_receiver, LV_OBJ_FLAG_HIDDEN);     /// Flags
    lv_obj_remove_flag(ui_party_mode_receiver, LV_OBJ_FLAG_SCROLLABLE);      /// Flags
    lv_obj_set_style_bg_color(ui_party_mode_receiver, lv_color_hex(0x000000), LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_bg_opa(ui_party_mode_receiver, 0, LV_PART_MAIN | LV_STATE_DEFAULT);

    ui_party_mode_play = lv_obj_create(ui_party_mode_receiver);
    lv_obj_remove_style_all(ui_party_mode_play);
    lv_obj_set_width(ui_party_mode_play, lv_pct(100));
    lv_obj_set_height(ui_party_mode_play, lv_pct(100));
    lv_obj_remove_flag(ui_party_mode_play, LV_OBJ_FLAG_SCROLLABLE);      /// Flags
    //lv_obj_set_flex_flow(ui_party_mode_play, LV_FLEX_FLOW_COLUMN);
    //lv_obj_set_flex_align(ui_party_mode_play, LV_FLEX_ALIGN_CENTER, LV_FLEX_ALIGN_START, LV_FLEX_ALIGN_START);
    lv_obj_set_style_bg_color(ui_party_mode_play, lv_color_hex(0x000000), LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_bg_opa(ui_party_mode_play, 0, LV_PART_MAIN | LV_STATE_DEFAULT);

    ui_party_mode_play_broadcast_bg = lv_image_create(ui_party_mode_play);
    lv_img_set_src(ui_party_mode_play_broadcast_bg, ui_party_mode_get_res(party_mode_para, PARTY_DAY_NIG_PM_RE_BG_BMP));
    lv_obj_set_width(ui_party_mode_play_broadcast_bg, LV_SIZE_CONTENT);   /// 214
    lv_obj_set_height(ui_party_mode_play_broadcast_bg, LV_SIZE_CONTENT);    /// 214
    lv_obj_set_align(ui_party_mode_play_broadcast_bg, LV_ALIGN_LEFT_MID);
    lv_obj_clear_flag(ui_party_mode_play_broadcast_bg, LV_OBJ_FLAG_SCROLLABLE);      /// Flags

    /*ui_party_mode_play_top = lv_obj_create(ui_party_mode_play);
    lv_obj_set_width(ui_party_mode_play_top, lv_pct(88));
    lv_obj_set_height(ui_party_mode_play_top, lv_pct(20));
    lv_obj_set_flex_flow(ui_party_mode_play_top, LV_FLEX_FLOW_ROW);
    lv_obj_set_flex_align(ui_party_mode_play_top, LV_FLEX_ALIGN_END, LV_FLEX_ALIGN_CENTER, LV_FLEX_ALIGN_CENTER);
    lv_obj_remove_flag(ui_party_mode_play_top, LV_OBJ_FLAG_SCROLLABLE);      /// Flags
    lv_obj_set_style_radius(ui_party_mode_play_top, 0, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_bg_color(ui_party_mode_play_top, lv_color_hex(0xFFFFFF), LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_bg_opa(ui_party_mode_play_top, 0, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_border_width(ui_party_mode_play_top, 0, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_pad_left(ui_party_mode_play_top, 0, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_pad_right(ui_party_mode_play_top, 0, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_pad_top(ui_party_mode_play_top, 0, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_pad_bottom(ui_party_mode_play_top, 0, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_pad_row(ui_party_mode_play_top, 0, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_pad_column(ui_party_mode_play_top, 20, LV_PART_MAIN | LV_STATE_DEFAULT);

    ui_party_mode_play_top_low = lv_image_create(ui_party_mode_play_top);
    lv_image_set_src(ui_party_mode_play_top_low, ui_party_mode_get_res(party_mode_para, PARTY_THEME_TEMP_COM_LOW_VOLTAGE_BMP));
    lv_obj_set_width(ui_party_mode_play_top_low, LV_SIZE_CONTENT);   /// 1
    lv_obj_set_height(ui_party_mode_play_top_low, LV_SIZE_CONTENT);    /// 1
    lv_obj_set_x(ui_party_mode_play_top_low, -93);
    lv_obj_set_y(ui_party_mode_play_top_low, 1);
    lv_obj_set_align(ui_party_mode_play_top_low, LV_ALIGN_RIGHT_MID);
    lv_obj_add_flag(ui_party_mode_play_top_low, LV_OBJ_FLAG_HIDDEN | LV_OBJ_FLAG_CLICKABLE);     /// Flags
    lv_obj_remove_flag(ui_party_mode_play_top_low, LV_OBJ_FLAG_SCROLLABLE);      /// Flags

    ui_party_mode_play_top_charge = lv_image_create(ui_party_mode_play_top);
    lv_image_set_src(ui_party_mode_play_top_charge, ui_party_mode_get_res(party_mode_para, PARTY_THEME_TEMP_COM_PD_CHARGE_BMP));
    lv_obj_set_width(ui_party_mode_play_top_charge, LV_SIZE_CONTENT);   /// 1
    lv_obj_set_height(ui_party_mode_play_top_charge, LV_SIZE_CONTENT);    /// 1
    lv_obj_set_align(ui_party_mode_play_top_charge, LV_ALIGN_RIGHT_MID);
    lv_obj_add_flag(ui_party_mode_play_top_charge, LV_OBJ_FLAG_HIDDEN | LV_OBJ_FLAG_CLICKABLE);     /// Flags
    lv_obj_remove_flag(ui_party_mode_play_top_charge, LV_OBJ_FLAG_SCROLLABLE);      /// Flags

    ui_party_mode_play_top_eq = lv_image_create(ui_party_mode_play_top);
    lv_image_set_src(ui_party_mode_play_top_eq, ui_party_mode_get_res(party_mode_para, PARTY_THEME_TEMP_COM_PUNCH_EQ_BMP));
    lv_obj_set_width(ui_party_mode_play_top_eq, LV_SIZE_CONTENT);   /// 1
    lv_obj_set_height(ui_party_mode_play_top_eq, LV_SIZE_CONTENT);    /// 1
    lv_obj_set_align(ui_party_mode_play_top_eq, LV_ALIGN_RIGHT_MID);
    lv_obj_add_flag(ui_party_mode_play_top_eq, LV_OBJ_FLAG_CLICKABLE);     /// Flags
    lv_obj_remove_flag(ui_party_mode_play_top_eq, LV_OBJ_FLAG_SCROLLABLE);      /// Flags*/

    ui_party_mode_play_mid = lv_obj_create(ui_party_mode_play);
    lv_obj_set_width(ui_party_mode_play_mid, lv_pct(92));
    lv_obj_set_height(ui_party_mode_play_mid, lv_pct(60));
    lv_obj_set_align(ui_party_mode_play_mid, LV_ALIGN_LEFT_MID);
    //lv_obj_set_x(ui_party_mode_play_mid, lv_pct(-86));
    //lv_obj_set_y(ui_party_mode_play_mid, lv_pct(0));
    //lv_obj_set_flex_flow(ui_party_mode_play_mid, LV_FLEX_FLOW_ROW);
    //lv_obj_set_flex_align(ui_party_mode_play_mid, LV_FLEX_ALIGN_START, LV_FLEX_ALIGN_CENTER, LV_FLEX_ALIGN_CENTER);
    lv_obj_remove_flag(ui_party_mode_play_mid, LV_OBJ_FLAG_CLICKABLE | LV_OBJ_FLAG_SCROLLABLE);      /// Flags
    lv_obj_set_style_radius(ui_party_mode_play_mid, 0, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_bg_color(ui_party_mode_play_mid, lv_color_hex(0xB54444), LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_bg_opa(ui_party_mode_play_mid, 0, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_border_width(ui_party_mode_play_mid, 0, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_pad_left(ui_party_mode_play_mid, 0, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_pad_right(ui_party_mode_play_mid, 0, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_pad_top(ui_party_mode_play_mid, 0, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_pad_bottom(ui_party_mode_play_mid, 0, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_pad_row(ui_party_mode_play_mid, 0, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_pad_column(ui_party_mode_play_mid, 0, LV_PART_MAIN | LV_STATE_DEFAULT);

    ui_party_mode_play_logo_panel = lv_obj_create(ui_party_mode_play_mid);
    lv_obj_set_width(ui_party_mode_play_logo_panel, lv_pct(38));//lv_pct(40)
    lv_obj_set_height(ui_party_mode_play_logo_panel, lv_pct(100));
    lv_obj_set_y(ui_party_mode_play_logo_panel, -6);
    lv_obj_set_align(ui_party_mode_play_logo_panel, LV_ALIGN_LEFT_MID);//LV_ALIGN_CENTER
    lv_obj_remove_flag(ui_party_mode_play_logo_panel, LV_OBJ_FLAG_SCROLLABLE);      /// Flags
    lv_obj_set_style_radius(ui_party_mode_play_logo_panel, 0, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_bg_color(ui_party_mode_play_logo_panel, lv_color_hex(0xFFFFFF), LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_bg_opa(ui_party_mode_play_logo_panel, 0, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_border_width(ui_party_mode_play_logo_panel, 0, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_pad_left(ui_party_mode_play_logo_panel, 0, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_pad_right(ui_party_mode_play_logo_panel, 0, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_pad_top(ui_party_mode_play_logo_panel, 0, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_pad_bottom(ui_party_mode_play_logo_panel, 0, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_pad_row(ui_party_mode_play_logo_panel, 0, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_pad_column(ui_party_mode_play_logo_panel, 0, LV_PART_MAIN | LV_STATE_DEFAULT);

    ui_party_mode_play_logo = lv_image_create(ui_party_mode_play_logo_panel);
    lv_img_set_src(ui_party_mode_play_logo, ui_party_mode_get_res(party_mode_para, PARTY_THEME_PM_RE_A_ICON_BMP));
    lv_obj_set_width(ui_party_mode_play_logo, LV_SIZE_CONTENT);   /// 214
    lv_obj_set_height(ui_party_mode_play_logo, LV_SIZE_CONTENT);    /// 214
    lv_obj_set_x(ui_party_mode_play_logo, lv_pct(2));
    lv_obj_set_y(ui_party_mode_play_logo, lv_pct(0));
    lv_obj_set_align(ui_party_mode_play_logo, LV_ALIGN_CENTER);
    lv_obj_add_flag(ui_party_mode_play_logo, LV_OBJ_FLAG_ADV_HITTEST);     /// Flags
    lv_obj_clear_flag(ui_party_mode_play_logo, LV_OBJ_FLAG_SCROLLABLE);      /// Flags

    ui_party_mode_play_logo_text = lv_label_create(ui_party_mode_play_logo);
    lv_obj_set_width(ui_party_mode_play_logo_text, LV_SIZE_CONTENT);   /// 1
    lv_obj_set_height(ui_party_mode_play_logo_text, LV_SIZE_CONTENT);    /// 1
    lv_obj_set_y(ui_party_mode_play_logo_text, lv_pct(-10));
    lv_obj_set_align(ui_party_mode_play_logo_text, LV_ALIGN_BOTTOM_MID);
    //lv_obj_add_flag(ui_party_mode_play_logo_text, LV_OBJ_FLAG_HIDDEN);     /// Flags
    lv_label_set_text(ui_party_mode_play_logo_text, "Receiver");
    lv_obj_set_style_text_color(ui_party_mode_play_logo_text, lv_color_hex(0x808080), LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_text_opa(ui_party_mode_play_logo_text, 255, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_text_font(ui_party_mode_play_logo_text, lv_font_small.font, LV_PART_MAIN | LV_STATE_DEFAULT);//&ui_font_Archivo_56

    ui_party_mode_play_disp = lv_obj_create(ui_party_mode_play_mid);
    lv_obj_set_width(ui_party_mode_play_disp, lv_pct(60));
    lv_obj_set_height(ui_party_mode_play_disp, lv_pct(90));
    lv_obj_set_align(ui_party_mode_play_disp, LV_ALIGN_RIGHT_MID);
    lv_obj_remove_flag(ui_party_mode_play_disp, LV_OBJ_FLAG_SCROLLABLE);      /// Flags
    lv_obj_set_style_radius(ui_party_mode_play_disp, 0, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_bg_color(ui_party_mode_play_disp, lv_color_hex(0x000000), LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_bg_opa(ui_party_mode_play_disp, 0, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_border_width(ui_party_mode_play_disp, 0, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_pad_left(ui_party_mode_play_disp, 0, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_pad_right(ui_party_mode_play_disp, 0, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_pad_top(ui_party_mode_play_disp, 0, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_pad_bottom(ui_party_mode_play_disp, 0, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_pad_row(ui_party_mode_play_disp, 0, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_pad_column(ui_party_mode_play_disp, 0, LV_PART_MAIN | LV_STATE_DEFAULT);

    ui_party_mode_id3 = lv_obj_create(ui_party_mode_play_disp);
    lv_obj_set_width(ui_party_mode_id3, lv_pct(100));
    lv_obj_set_height(ui_party_mode_id3, lv_pct(100));
    lv_obj_set_align(ui_party_mode_id3, LV_ALIGN_RIGHT_MID);
    lv_obj_add_flag(ui_party_mode_id3, LV_OBJ_FLAG_HIDDEN);      /// Flags
    lv_obj_remove_flag(ui_party_mode_id3, LV_OBJ_FLAG_SCROLLABLE);      /// Flags
    lv_obj_set_style_radius(ui_party_mode_id3, 0, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_bg_color(ui_party_mode_id3, lv_color_hex(0x000000), LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_bg_opa(ui_party_mode_id3, 0, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_border_width(ui_party_mode_id3, 0, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_pad_left(ui_party_mode_id3, 0, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_pad_right(ui_party_mode_id3, 0, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_pad_top(ui_party_mode_id3, 0, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_pad_bottom(ui_party_mode_id3, 0, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_pad_row(ui_party_mode_id3, 0, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_pad_column(ui_party_mode_id3, 0, LV_PART_MAIN | LV_STATE_DEFAULT);

    ui_party_mode_id3_text = lv_obj_create(ui_party_mode_id3);
    lv_obj_set_width(ui_party_mode_id3_text, lv_pct(100));
    lv_obj_set_height(ui_party_mode_id3_text, lv_pct(90));
    lv_obj_clear_flag(ui_party_mode_id3_text, LV_OBJ_FLAG_SCROLLABLE);   /// Flags
    lv_obj_set_style_radius(ui_party_mode_id3_text, 0, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_bg_color(ui_party_mode_id3_text, lv_color_hex(0x000000), LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_bg_opa(ui_party_mode_id3_text, 0, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_border_width(ui_party_mode_id3_text, 0, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_pad_left(ui_party_mode_id3_text, 0, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_pad_right(ui_party_mode_id3_text, 0, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_pad_top(ui_party_mode_id3_text, 0, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_pad_bottom(ui_party_mode_id3_text, 0, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_pad_row(ui_party_mode_id3_text, 0, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_pad_column(ui_party_mode_id3_text, 0, LV_PART_MAIN | LV_STATE_DEFAULT);

    ui_party_mode_id3_text_panel = lv_obj_create(ui_party_mode_id3_text);
    lv_obj_set_width(ui_party_mode_id3_text_panel, lv_pct(100));
    lv_obj_set_height(ui_party_mode_id3_text_panel, lv_pct(100));
    lv_obj_set_flex_flow(ui_party_mode_id3_text_panel, LV_FLEX_FLOW_COLUMN);
    lv_obj_set_flex_align(ui_party_mode_id3_text_panel, LV_FLEX_ALIGN_SPACE_BETWEEN, LV_FLEX_ALIGN_CENTER, LV_FLEX_ALIGN_CENTER);
    lv_obj_clear_flag(ui_party_mode_id3_text_panel, LV_OBJ_FLAG_SCROLLABLE);       /// Flags
    lv_obj_set_style_radius(ui_party_mode_id3_text_panel, 0, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_bg_color(ui_party_mode_id3_text_panel, lv_color_hex(0x000000), LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_bg_opa(ui_party_mode_id3_text_panel, 0, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_border_width(ui_party_mode_id3_text_panel, 0, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_pad_left(ui_party_mode_id3_text_panel, 0, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_pad_right(ui_party_mode_id3_text_panel, 0, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_pad_top(ui_party_mode_id3_text_panel, 0, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_pad_bottom(ui_party_mode_id3_text_panel, 0, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_pad_row(ui_party_mode_id3_text_panel, 0, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_pad_column(ui_party_mode_id3_text_panel, 0, LV_PART_MAIN | LV_STATE_DEFAULT);

    ui_party_mode_id3_text_panel_album = lv_obj_create(ui_party_mode_id3_text_panel);
    lv_obj_set_width(ui_party_mode_id3_text_panel_album, lv_pct(100));
    lv_obj_set_height(ui_party_mode_id3_text_panel_album, LV_SIZE_CONTENT); /// 30
    lv_obj_clear_flag(ui_party_mode_id3_text_panel_album, LV_OBJ_FLAG_SCROLLABLE);   /// Flags
    lv_obj_set_style_radius(ui_party_mode_id3_text_panel_album, 0, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_bg_color(ui_party_mode_id3_text_panel_album, lv_color_hex(0x000000), LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_bg_opa(ui_party_mode_id3_text_panel_album, 0, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_border_width(ui_party_mode_id3_text_panel_album, 0, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_pad_left(ui_party_mode_id3_text_panel_album, 0, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_pad_right(ui_party_mode_id3_text_panel_album, 0, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_pad_top(ui_party_mode_id3_text_panel_album, 0, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_pad_bottom(ui_party_mode_id3_text_panel_album, 0, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_pad_row(ui_party_mode_id3_text_panel_album, 0, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_pad_column(ui_party_mode_id3_text_panel_album, 0, LV_PART_MAIN | LV_STATE_DEFAULT);

    ui_party_mode_id3_text_panel_album_text = lv_label_create(ui_party_mode_id3_text_panel_album);
    lv_obj_set_width(ui_party_mode_id3_text_panel_album_text, lv_pct(100));   /// 1
    lv_obj_set_height(ui_party_mode_id3_text_panel_album_text, LV_SIZE_CONTENT);     /// 1
    lv_obj_add_flag(ui_party_mode_id3_text_panel_album_text, LV_OBJ_FLAG_CLICKABLE);
    lv_label_set_long_mode(ui_party_mode_id3_text_panel_album_text, LV_LABEL_LONG_SCROLL_CIRCULAR);
    lv_label_set_text(ui_party_mode_id3_text_panel_album_text, "Unknown Album");
    lv_obj_set_style_text_color(ui_party_mode_id3_text_panel_album_text, lv_color_hex(0xCCCCCC), LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_text_opa(ui_party_mode_id3_text_panel_album_text, 255, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_text_font(ui_party_mode_id3_text_panel_album_text, lv_font_medium.font, LV_PART_MAIN | LV_STATE_DEFAULT);

    ui_party_mode_id3_text_panel_song = lv_obj_create(ui_party_mode_id3_text_panel);
    lv_obj_set_width(ui_party_mode_id3_text_panel_song, lv_pct(100));
    lv_obj_set_height(ui_party_mode_id3_text_panel_song, LV_SIZE_CONTENT);    /// 30
    lv_obj_clear_flag(ui_party_mode_id3_text_panel_song, LV_OBJ_FLAG_SCROLLABLE);       /// Flags
    lv_obj_set_style_radius(ui_party_mode_id3_text_panel_song, 0, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_bg_color(ui_party_mode_id3_text_panel_song, lv_color_hex(0x000000), LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_bg_opa(ui_party_mode_id3_text_panel_song, 0, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_border_width(ui_party_mode_id3_text_panel_song, 0, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_pad_left(ui_party_mode_id3_text_panel_song, 0, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_pad_right(ui_party_mode_id3_text_panel_song, 0, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_pad_top(ui_party_mode_id3_text_panel_song, 0, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_pad_bottom(ui_party_mode_id3_text_panel_song, 0, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_pad_row(ui_party_mode_id3_text_panel_song, 0, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_pad_column(ui_party_mode_id3_text_panel_song, 0, LV_PART_MAIN | LV_STATE_DEFAULT);

    ui_party_mode_id3_text_panel_song_text = lv_label_create(ui_party_mode_id3_text_panel_song);
    lv_obj_set_width(ui_party_mode_id3_text_panel_song_text, lv_pct(100));   /// 1
    lv_obj_set_height(ui_party_mode_id3_text_panel_song_text, LV_SIZE_CONTENT); /// 1
    lv_obj_add_flag(ui_party_mode_id3_text_panel_song_text, LV_OBJ_FLAG_CLICKABLE);
    lv_label_set_long_mode(ui_party_mode_id3_text_panel_song_text, LV_LABEL_LONG_SCROLL_CIRCULAR);
    lv_label_set_text(ui_party_mode_id3_text_panel_song_text, "Unknown Title");
    lv_obj_set_style_text_color(ui_party_mode_id3_text_panel_song_text, lv_color_hex(0xFFFFFF), LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_text_opa(ui_party_mode_id3_text_panel_song_text, 255, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_text_font(ui_party_mode_id3_text_panel_song_text, lv_font_large.font, LV_PART_MAIN | LV_STATE_DEFAULT);

    ui_party_mode_id3_text_panel_artist = lv_obj_create(ui_party_mode_id3_text_panel);
    lv_obj_set_width(ui_party_mode_id3_text_panel_artist, lv_pct(100));
    lv_obj_set_height(ui_party_mode_id3_text_panel_artist, LV_SIZE_CONTENT);     /// 30
    lv_obj_clear_flag(ui_party_mode_id3_text_panel_artist, LV_OBJ_FLAG_SCROLLABLE);   /// Flags
    lv_obj_set_style_radius(ui_party_mode_id3_text_panel_artist, 0, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_bg_color(ui_party_mode_id3_text_panel_artist, lv_color_hex(0x000000), LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_bg_opa(ui_party_mode_id3_text_panel_artist, 0, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_border_width(ui_party_mode_id3_text_panel_artist, 0, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_pad_left(ui_party_mode_id3_text_panel_artist, 0, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_pad_right(ui_party_mode_id3_text_panel_artist, 0, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_pad_top(ui_party_mode_id3_text_panel_artist, 0, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_pad_bottom(ui_party_mode_id3_text_panel_artist, 0, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_pad_row(ui_party_mode_id3_text_panel_artist, 0, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_pad_column(ui_party_mode_id3_text_panel_artist, 0, LV_PART_MAIN | LV_STATE_DEFAULT);

    ui_party_mode_id3_text_panel_artist_text = lv_label_create(ui_party_mode_id3_text_panel_artist);
    lv_obj_set_width(ui_party_mode_id3_text_panel_artist_text, lv_pct(100));    /// 1
    lv_obj_set_height(ui_party_mode_id3_text_panel_artist_text, LV_SIZE_CONTENT);     /// 1
    lv_obj_add_flag(ui_party_mode_id3_text_panel_artist_text, LV_OBJ_FLAG_CLICKABLE);
    lv_label_set_long_mode(ui_party_mode_id3_text_panel_artist_text, LV_LABEL_LONG_SCROLL_CIRCULAR);
    lv_label_set_text(ui_party_mode_id3_text_panel_artist_text, "Unknown Arist");
    lv_obj_set_style_text_color(ui_party_mode_id3_text_panel_artist_text, lv_color_hex(0xB3B3B3), LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_text_opa(ui_party_mode_id3_text_panel_artist_text, 255, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_text_font(ui_party_mode_id3_text_panel_artist_text, lv_font_xsmall.font, LV_PART_MAIN | LV_STATE_DEFAULT);

    ui_party_mode_connect_status = lv_obj_create(ui_party_mode_play_disp);
    lv_obj_remove_style_all(ui_party_mode_connect_status);
    lv_obj_set_width(ui_party_mode_connect_status, lv_pct(100));
    lv_obj_set_height(ui_party_mode_connect_status, lv_pct(100));
    lv_obj_set_align(ui_party_mode_connect_status, LV_ALIGN_RIGHT_MID);
    //lv_obj_add_flag(ui_party_mode_connect_status, LV_OBJ_FLAG_HIDDEN);      /// Flags
    lv_obj_remove_flag(ui_party_mode_connect_status, LV_OBJ_FLAG_SCROLLABLE);      /// Flags
    lv_obj_set_style_bg_color(ui_party_mode_connect_status, lv_color_hex(0x000000), LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_bg_opa(ui_party_mode_connect_status, 0, LV_PART_MAIN | LV_STATE_DEFAULT);

    ui_party_mode_connect_status_text = lv_label_create(ui_party_mode_connect_status);
    lv_obj_set_width(ui_party_mode_connect_status_text, lv_pct(100));
    lv_obj_set_height(ui_party_mode_connect_status_text, LV_SIZE_CONTENT);    /// 100
    lv_obj_set_align(ui_party_mode_connect_status_text, LV_ALIGN_CENTER);
    lv_label_set_text(ui_party_mode_connect_status_text, "disconnected!");
    lv_obj_set_style_text_color(ui_party_mode_connect_status_text, lv_color_hex(0xFFFFFF), LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_text_opa(ui_party_mode_connect_status_text, 255, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_text_letter_space(ui_party_mode_connect_status_text, 0, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_text_line_space(ui_party_mode_connect_status_text, 20, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_text_align(ui_party_mode_connect_status_text, LV_TEXT_ALIGN_CENTER, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_text_font(ui_party_mode_connect_status_text, lv_font_medium.font, LV_PART_MAIN | LV_STATE_DEFAULT);

    ui_party_mode_play_bottom = lv_obj_create(ui_party_mode_play);
    lv_obj_set_width(ui_party_mode_play_bottom, lv_pct(93));
    lv_obj_set_height(ui_party_mode_play_bottom, lv_pct(20));
    lv_obj_set_align(ui_party_mode_play_bottom, LV_ALIGN_BOTTOM_LEFT);
    lv_obj_clear_flag(ui_party_mode_play_bottom, LV_OBJ_FLAG_SCROLLABLE);       /// Flags
    lv_obj_set_style_radius(ui_party_mode_play_bottom, 0, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_bg_color(ui_party_mode_play_bottom, lv_color_hex(0xFFFFFF), LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_bg_opa(ui_party_mode_play_bottom, 0, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_border_width(ui_party_mode_play_bottom, 0, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_pad_left(ui_party_mode_play_bottom, 0, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_pad_right(ui_party_mode_play_bottom, 0, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_pad_top(ui_party_mode_play_bottom, 0, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_pad_bottom(ui_party_mode_play_bottom, 0, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_pad_row(ui_party_mode_play_bottom, 0, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_pad_column(ui_party_mode_play_bottom, 0, LV_PART_MAIN | LV_STATE_DEFAULT);

    ui_party_mode_play_bottom_text = lv_label_create(ui_party_mode_play_bottom);
    lv_obj_set_width(ui_party_mode_play_bottom_text, LV_SIZE_CONTENT);   /// 1
    lv_obj_set_height(ui_party_mode_play_bottom_text, LV_SIZE_CONTENT);    /// 1
    lv_obj_set_x(ui_party_mode_play_bottom_text, lv_pct(10));
    lv_obj_set_align(ui_party_mode_play_bottom_text, LV_ALIGN_LEFT_MID);
    lv_label_set_text(ui_party_mode_play_bottom_text, "PMX 30");
    lv_obj_set_style_text_color(ui_party_mode_play_bottom_text, lv_color_hex(0xFFFFFF), LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_text_opa(ui_party_mode_play_bottom_text, 255, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_text_font(ui_party_mode_play_bottom_text, lv_font_medium.font, LV_PART_MAIN | LV_STATE_DEFAULT);
    
#if 1
    ui_party_mode_bottom_list = lv_obj_create(party_mode_para->source_para->ui_bottom);
    lv_obj_remove_style_all(ui_party_mode_bottom_list);
    lv_obj_set_width(ui_party_mode_bottom_list, 222);
    lv_obj_set_height(ui_party_mode_bottom_list, 74);
    lv_obj_set_align(ui_party_mode_bottom_list, LV_ALIGN_BOTTOM_LEFT);
    //lv_obj_add_flag(ui_party_mode_bottom_list, LV_OBJ_FLAG_HIDDEN);      /// Flags
    lv_obj_clear_flag(ui_party_mode_bottom_list, LV_OBJ_FLAG_SCROLLABLE);      /// Flags
    lv_obj_set_style_bg_color(ui_party_mode_bottom_list, lv_color_hex(0x000000), LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_bg_opa(ui_party_mode_bottom_list, 0, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_bg_img_src(ui_party_mode_bottom_list, ui_party_mode_get_res(party_mode_para, PARTY_THEME_COM_BOT_ICON_BG_A_N_BMP), LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_bg_img_src(ui_party_mode_bottom_list, ui_party_mode_get_res(party_mode_para, PARTY_THEME_COM_BOT_ICON_BG_A_P_S_BMP), LV_PART_MAIN | LV_STATE_PRESSED);
    lv_obj_set_style_bg_img_src(ui_party_mode_bottom_list, ui_party_mode_get_res(party_mode_para, PARTY_THEME_COM_BOT_ICON_BG_A_P_S_BMP), LV_PART_MAIN | LV_STATE_FOCUSED);

    ui_party_mode_bottom_list_img = lv_img_create(ui_party_mode_bottom_list);
    lv_img_set_src(ui_party_mode_bottom_list_img, ui_party_mode_get_res(party_mode_para, PARTY_THEME_COM_BOT_LIST_N_BMP));
    lv_obj_set_width(ui_party_mode_bottom_list_img, LV_SIZE_CONTENT);   /// 42
    lv_obj_set_height(ui_party_mode_bottom_list_img, LV_SIZE_CONTENT);    /// 34
    lv_obj_set_align(ui_party_mode_bottom_list_img, LV_ALIGN_CENTER);
    lv_obj_add_flag(ui_party_mode_bottom_list_img, LV_OBJ_FLAG_ADV_HITTEST);
    lv_obj_clear_flag(ui_party_mode_bottom_list_img, LV_OBJ_FLAG_SCROLLABLE);

    ui_party_mode_bottom_browse = lv_obj_create(party_mode_para->source_para->ui_bottom);
    lv_obj_remove_style_all(ui_party_mode_bottom_browse);
    lv_obj_set_width(ui_party_mode_bottom_browse, 222);
    lv_obj_set_height(ui_party_mode_bottom_browse, 74);
    lv_obj_set_align(ui_party_mode_bottom_browse, LV_ALIGN_BOTTOM_LEFT);
    //lv_obj_add_flag(ui_party_mode_bottom_browse, LV_OBJ_FLAG_HIDDEN);      /// Flags
    lv_obj_clear_flag(ui_party_mode_bottom_browse, LV_OBJ_FLAG_SCROLLABLE);      /// Flags
    lv_obj_set_style_bg_color(ui_party_mode_bottom_browse, lv_color_hex(0x000000), LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_bg_opa(ui_party_mode_bottom_browse, 0, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_bg_img_src(ui_party_mode_bottom_browse, ui_party_mode_get_res(party_mode_para, PARTY_THEME_COM_BOT_ICON_BG_B_N_BMP), LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_bg_img_src(ui_party_mode_bottom_browse, ui_party_mode_get_res(party_mode_para, PARTY_THEME_COM_BOT_ICON_BG_B_P_S_BMP), LV_PART_MAIN | LV_STATE_PRESSED);
    lv_obj_set_style_bg_img_src(ui_party_mode_bottom_browse, ui_party_mode_get_res(party_mode_para, PARTY_THEME_COM_BOT_ICON_BG_B_P_S_BMP), LV_PART_MAIN | LV_STATE_FOCUSED);

    ui_party_mode_bottom_browse_img = lv_img_create(ui_party_mode_bottom_browse);
    lv_img_set_src(ui_party_mode_bottom_browse_img, ui_party_mode_get_res(party_mode_para, PARTY_THEME_COM_BOT_BROWSE_N_BMP));
    lv_obj_set_width(ui_party_mode_bottom_browse_img, LV_SIZE_CONTENT);   /// 42
    lv_obj_set_height(ui_party_mode_bottom_browse_img, LV_SIZE_CONTENT);    /// 34
    lv_obj_set_align(ui_party_mode_bottom_browse_img, LV_ALIGN_CENTER);
    lv_obj_add_flag(ui_party_mode_bottom_browse_img, LV_OBJ_FLAG_ADV_HITTEST);
    lv_obj_clear_flag(ui_party_mode_bottom_browse_img, LV_OBJ_FLAG_SCROLLABLE);

    ui_party_mode_bottom_receiver = lv_obj_create(party_mode_para->source_para->ui_bottom);
    lv_obj_remove_style_all(ui_party_mode_bottom_receiver);
    lv_obj_set_width(ui_party_mode_bottom_receiver, 222);
    lv_obj_set_height(ui_party_mode_bottom_receiver, 74);
    lv_obj_set_align(ui_party_mode_bottom_receiver, LV_ALIGN_BOTTOM_LEFT);
    //lv_obj_add_flag(ui_party_mode_bottom_receiver, LV_OBJ_FLAG_HIDDEN);      /// Flags
    lv_obj_clear_flag(ui_party_mode_bottom_receiver, LV_OBJ_FLAG_SCROLLABLE);      /// Flags
    lv_obj_set_style_bg_color(ui_party_mode_bottom_receiver, lv_color_hex(0x000000), LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_bg_opa(ui_party_mode_bottom_receiver, 0, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_bg_img_src(ui_party_mode_bottom_receiver, ui_party_mode_get_res(party_mode_para, PARTY_THEME_COM_BOT_ICON_BG_A_N_BMP), LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_bg_img_src(ui_party_mode_bottom_receiver, ui_party_mode_get_res(party_mode_para, PARTY_THEME_COM_BOT_ICON_BG_A_P_S_BMP), LV_PART_MAIN | LV_STATE_PRESSED);
    lv_obj_set_style_bg_img_src(ui_party_mode_bottom_receiver, ui_party_mode_get_res(party_mode_para, PARTY_THEME_COM_BOT_ICON_BG_A_P_S_BMP), LV_PART_MAIN | LV_STATE_FOCUSED);

    ui_party_mode_bottom_receiver_label = lv_label_create(ui_party_mode_bottom_receiver);
    lv_obj_set_width(ui_party_mode_bottom_receiver_label, lv_pct(100));//lv_pct(89)
    lv_obj_set_height(ui_party_mode_bottom_receiver_label, LV_SIZE_CONTENT);      /// 0
    lv_obj_set_align(ui_party_mode_bottom_receiver_label, LV_ALIGN_CENTER);
    lv_label_set_text(ui_party_mode_bottom_receiver_label, "Receiver");
    lv_label_set_long_mode(ui_party_mode_bottom_receiver_label, LV_LABEL_LONG_CLIP);
    lv_obj_set_style_text_color(ui_party_mode_bottom_receiver_label, lv_color_hex(0xFFFFFF), LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_text_opa(ui_party_mode_bottom_receiver_label, 255, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_text_font(ui_party_mode_bottom_receiver_label, lv_font_medium.font, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_text_align(ui_party_mode_bottom_receiver_label, LV_TEXT_ALIGN_CENTER, LV_PART_MAIN | LV_STATE_DEFAULT);

    lv_obj_set_style_text_color(ui_party_mode_bottom_receiver_label, lv_color_hex(0x808080), LV_PART_MAIN | LV_STATE_PRESSED);

    lv_obj_add_event_cb(ui_party_mode_bottom_list, ui_event_party_mode_bottom_list_cb, LV_EVENT_ALL, party_mode_para);
    lv_obj_add_event_cb(ui_party_mode_bottom_browse, ui_event_party_mode_bottom_browse_cb, LV_EVENT_ALL, party_mode_para);
    lv_obj_add_event_cb(ui_party_mode_bottom_receiver, ui_event_party_mode_bottom_receiver_cb, LV_EVENT_ALL, party_mode_para);
#else
    ui_party_mode_menu_panel = lv_obj_create(party_mode_para->source_para->ui_rig_list_panel);
    lv_obj_remove_style_all(ui_party_mode_menu_panel);
    lv_obj_set_width(ui_party_mode_menu_panel, lv_pct(100));
    lv_obj_set_height(ui_party_mode_menu_panel, lv_pct(100));
    lv_obj_set_y(ui_party_mode_menu_panel, 0);
    lv_obj_set_flex_flow(ui_party_mode_menu_panel, LV_FLEX_FLOW_ROW);
    lv_obj_set_flex_align(ui_party_mode_menu_panel, LV_FLEX_ALIGN_SPACE_BETWEEN, LV_FLEX_ALIGN_START, LV_FLEX_ALIGN_START);
    lv_obj_clear_flag(ui_party_mode_menu_panel, LV_OBJ_FLAG_SCROLLABLE | LV_OBJ_FLAG_CLICKABLE);      /// Flags
    lv_obj_set_style_bg_color(ui_party_mode_menu_panel, lv_color_hex(0x000000), LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_bg_opa(ui_party_mode_menu_panel, 255, LV_PART_MAIN | LV_STATE_DEFAULT);

    ui_party_mode_menu_panel_bar = lv_obj_create(ui_party_mode_menu_panel);
    lv_obj_set_width(ui_party_mode_menu_panel_bar, lv_pct(23));
    lv_obj_set_height(ui_party_mode_menu_panel_bar, lv_pct(100));
    lv_obj_add_flag(ui_party_mode_menu_panel_bar, LV_OBJ_FLAG_HIDDEN);     /// Flags
    lv_obj_remove_flag(ui_party_mode_menu_panel_bar, LV_OBJ_FLAG_CLICKABLE | LV_OBJ_FLAG_SCROLLABLE);      /// Flags
    lv_obj_set_style_radius(ui_party_mode_menu_panel_bar, 0, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_bg_color(ui_party_mode_menu_panel_bar, lv_color_hex(0x131313), LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_bg_opa(ui_party_mode_menu_panel_bar, 255, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_bg_grad_color(ui_party_mode_menu_panel_bar, lv_color_hex(0x272727), LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_bg_main_stop(ui_party_mode_menu_panel_bar, 0, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_bg_grad_stop(ui_party_mode_menu_panel_bar, 30, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_bg_grad_dir(ui_party_mode_menu_panel_bar, LV_GRAD_DIR_HOR, LV_PART_MAIN | LV_STATE_DEFAULT);
    //lv_obj_set_style_bg_image_src(ui_party_mode_menu_panel_bar, ui_party_mode_get_res(party_mode_para, PARTY_THEME_TEMP_COM_RIG_BG_C_BMP),
    //                              LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_border_width(ui_party_mode_menu_panel_bar, 0, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_pad_left(ui_party_mode_menu_panel_bar, 0, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_pad_right(ui_party_mode_menu_panel_bar, 0, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_pad_top(ui_party_mode_menu_panel_bar, 0, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_pad_bottom(ui_party_mode_menu_panel_bar, 0, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_pad_row(ui_party_mode_menu_panel_bar, 0, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_pad_column(ui_party_mode_menu_panel_bar, 0, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_bg_color(ui_party_mode_menu_panel_bar, lv_color_hex(0xFF0000), LV_PART_MAIN | LV_STATE_PRESSED);
    lv_obj_set_style_bg_opa(ui_party_mode_menu_panel_bar, 0, LV_PART_MAIN | LV_STATE_PRESSED);
    lv_obj_set_style_bg_grad_color(ui_party_mode_menu_panel_bar, lv_color_hex(0x000000), LV_PART_MAIN | LV_STATE_PRESSED);
    lv_obj_set_style_bg_main_stop(ui_party_mode_menu_panel_bar, 0, LV_PART_MAIN | LV_STATE_PRESSED);
    lv_obj_set_style_bg_grad_stop(ui_party_mode_menu_panel_bar, 120, LV_PART_MAIN | LV_STATE_PRESSED);
    lv_obj_set_style_bg_grad_dir(ui_party_mode_menu_panel_bar, LV_GRAD_DIR_HOR, LV_PART_MAIN | LV_STATE_PRESSED);

    ui_party_mode_menu_panel_button = lv_obj_create(ui_party_mode_menu_panel_bar);
    lv_obj_set_width(ui_party_mode_menu_panel_button, LV_SIZE_CONTENT);
    lv_obj_set_height(ui_party_mode_menu_panel_button, lv_pct(100));
    lv_obj_set_align(ui_party_mode_menu_panel_button, LV_ALIGN_CENTER);
    lv_obj_set_flex_flow(ui_party_mode_menu_panel_button, LV_FLEX_FLOW_COLUMN);
    lv_obj_set_flex_align(ui_party_mode_menu_panel_button, LV_FLEX_ALIGN_SPACE_AROUND, LV_FLEX_ALIGN_CENTER, LV_FLEX_ALIGN_CENTER);
    lv_obj_add_flag(ui_party_mode_menu_panel_button, LV_OBJ_FLAG_CHECKABLE);     /// Flags
    lv_obj_remove_flag(ui_party_mode_menu_panel_button, LV_OBJ_FLAG_SCROLLABLE);      /// Flags
    lv_obj_set_style_radius(ui_party_mode_menu_panel_button, 0, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_bg_color(ui_party_mode_menu_panel_button, lv_color_hex(0x404040), LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_bg_opa(ui_party_mode_menu_panel_button, 0, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_bg_grad_color(ui_party_mode_menu_panel_button, lv_color_hex(0x000000), LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_bg_main_stop(ui_party_mode_menu_panel_button, 0, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_bg_grad_stop(ui_party_mode_menu_panel_button, 120, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_bg_grad_dir(ui_party_mode_menu_panel_button, LV_GRAD_DIR_HOR, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_border_width(ui_party_mode_menu_panel_button, 0, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_pad_left(ui_party_mode_menu_panel_button, 0, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_pad_right(ui_party_mode_menu_panel_button, 0, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_pad_top(ui_party_mode_menu_panel_button, 0, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_pad_bottom(ui_party_mode_menu_panel_button, 0, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_pad_row(ui_party_mode_menu_panel_button, 0, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_pad_column(ui_party_mode_menu_panel_button, 0, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_bg_color(ui_party_mode_menu_panel_button, lv_color_hex(0xFF0000), LV_PART_MAIN | LV_STATE_PRESSED);
    lv_obj_set_style_bg_opa(ui_party_mode_menu_panel_button, 0, LV_PART_MAIN | LV_STATE_PRESSED);
    lv_obj_set_style_bg_grad_color(ui_party_mode_menu_panel_button, lv_color_hex(0x000000), LV_PART_MAIN | LV_STATE_PRESSED);
    lv_obj_set_style_bg_main_stop(ui_party_mode_menu_panel_button, 0, LV_PART_MAIN | LV_STATE_PRESSED);
    lv_obj_set_style_bg_grad_stop(ui_party_mode_menu_panel_button, 120, LV_PART_MAIN | LV_STATE_PRESSED);
    lv_obj_set_style_bg_grad_dir(ui_party_mode_menu_panel_button, LV_GRAD_DIR_HOR, LV_PART_MAIN | LV_STATE_PRESSED);

    ui_party_mode_menu_panel_button_device = lv_obj_create(ui_party_mode_menu_panel_button);
    lv_obj_remove_style_all(ui_party_mode_menu_panel_button_device);
    lv_obj_set_width(ui_party_mode_menu_panel_button_device, 101);
    lv_obj_set_height(ui_party_mode_menu_panel_button_device, 101);
    lv_obj_add_flag(ui_party_mode_menu_panel_button_device, LV_OBJ_FLAG_CHECKABLE);     /// Flags
    lv_obj_remove_flag(ui_party_mode_menu_panel_button_device, LV_OBJ_FLAG_SCROLLABLE);      /// Flags
    lv_obj_set_style_bg_color(ui_party_mode_menu_panel_button_device, lv_color_hex(0x000000), LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_bg_opa(ui_party_mode_menu_panel_button_device, 0, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_bg_image_src(ui_party_mode_menu_panel_button_device, ui_party_mode_get_res(party_mode_para, PARTY_THEME_TEMP_COM_RIG_ICON_BG_N_BMP), LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_bg_image_src(ui_party_mode_menu_panel_button_device, ui_party_mode_get_res(party_mode_para, PARTY_THEME_TEMP_COM_RIG_ICON_BG_P_BMP), LV_PART_MAIN | LV_STATE_PRESSED);
    lv_obj_set_style_bg_image_src(ui_party_mode_menu_panel_button_device, ui_party_mode_get_res(party_mode_para, PARTY_THEME_TEMP_COM_RIG_ICON_BG_S_BMP), LV_PART_MAIN | LV_STATE_CHECKED);
    lv_obj_add_state(ui_party_mode_menu_panel_button_device, LV_STATE_CHECKED);

    ui_party_mode_menu_panel_button_device_img = lv_image_create(ui_party_mode_menu_panel_button_device);
    lv_image_set_src(ui_party_mode_menu_panel_button_device_img, ui_party_mode_get_res(party_mode_para, PARTY_THEME_TEMP_PM_RE_LIST_N_BMP));
    lv_obj_set_width(ui_party_mode_menu_panel_button_device_img, LV_SIZE_CONTENT);   /// 65
    lv_obj_set_height(ui_party_mode_menu_panel_button_device_img, LV_SIZE_CONTENT);    /// 65
    lv_obj_set_align(ui_party_mode_menu_panel_button_device_img, LV_ALIGN_CENTER);
    lv_obj_remove_flag(ui_party_mode_menu_panel_button_device_img, LV_OBJ_FLAG_SCROLLABLE);      /// Flags

    ui_party_mode_menu_panel_button_search = lv_obj_create(ui_party_mode_menu_panel_button);
    lv_obj_remove_style_all(ui_party_mode_menu_panel_button_search);
    lv_obj_set_width(ui_party_mode_menu_panel_button_search, 101);
    lv_obj_set_height(ui_party_mode_menu_panel_button_search, 101);
    lv_obj_add_flag(ui_party_mode_menu_panel_button_search, LV_OBJ_FLAG_CHECKABLE);     /// Flags
    lv_obj_remove_flag(ui_party_mode_menu_panel_button_search, LV_OBJ_FLAG_SCROLLABLE);      /// Flags
    lv_obj_set_style_bg_color(ui_party_mode_menu_panel_button_search, lv_color_hex(0x000000), LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_bg_opa(ui_party_mode_menu_panel_button_search, 0, LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_bg_image_src(ui_party_mode_menu_panel_button_search, ui_party_mode_get_res(party_mode_para, PARTY_THEME_TEMP_COM_RIG_ICON_BG_N_BMP), LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_bg_image_src(ui_party_mode_menu_panel_button_search, ui_party_mode_get_res(party_mode_para, PARTY_THEME_TEMP_COM_RIG_ICON_BG_P_BMP), LV_PART_MAIN | LV_STATE_PRESSED);
    lv_obj_set_style_bg_image_src(ui_party_mode_menu_panel_button_search, ui_party_mode_get_res(party_mode_para, PARTY_THEME_TEMP_COM_RIG_ICON_BG_S_BMP), LV_PART_MAIN | LV_STATE_CHECKED);

    ui_party_mode_menu_panel_button_search_img = lv_image_create(ui_party_mode_menu_panel_button_search);
    lv_image_set_src(ui_party_mode_menu_panel_button_search_img, ui_party_mode_get_res(party_mode_para, PARTY_THEME_TEMP_PM_RE_SEARCH_N_BMP));
    lv_obj_set_width(ui_party_mode_menu_panel_button_search_img, LV_SIZE_CONTENT);   /// 65
    lv_obj_set_height(ui_party_mode_menu_panel_button_search_img, LV_SIZE_CONTENT);    /// 65
    lv_obj_set_align(ui_party_mode_menu_panel_button_search_img, LV_ALIGN_CENTER);
    lv_obj_remove_flag(ui_party_mode_menu_panel_button_search_img, LV_OBJ_FLAG_SCROLLABLE);      /// Flags

    ui_party_mode_menu_panel_content = lv_obj_create(ui_party_mode_menu_panel);//ui_fm
    lv_obj_remove_style_all(ui_party_mode_menu_panel_content);
    lv_obj_set_width(ui_party_mode_menu_panel_content, lv_pct(77));
    lv_obj_set_height(ui_party_mode_menu_panel_content, lv_pct(100));
    //lv_obj_add_flag(ui_party_mode_menu_panel_content, LV_OBJ_FLAG_HIDDEN);     /// Flags
    lv_obj_clear_flag(ui_party_mode_menu_panel_content, LV_OBJ_FLAG_SCROLLABLE);      /// Flags
    lv_obj_set_style_bg_color(ui_party_mode_menu_panel_content, lv_color_hex(0x000000), LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_bg_opa(ui_party_mode_menu_panel_content, 0, LV_PART_MAIN | LV_STATE_DEFAULT);

    lv_obj_add_event_cb(ui_party_mode_menu_panel_button_device, ui_event_party_mode_device_cb, LV_EVENT_ALL, party_mode_para);
    lv_obj_add_event_cb(ui_party_mode_menu_panel_button_search, ui_event_party_mode_search_cb, LV_EVENT_ALL, party_mode_para);
    lv_obj_add_event_cb(party_mode_para->source_para->ui_rig_panel_button_bottom, ui_event_party_mode_bottom_menu_cb, LV_EVENT_ALL, party_mode_para);
#endif

	lv_obj_add_event_cb(ui_party_mode_play_logo_panel, ui_event_party_mode_play_logo_panel, LV_EVENT_ALL, party_mode_para);

}

static void ui_party_mode_receiver_screen_uninit(ui_party_mode_para_t * party_mode_para)
{		
	lv_obj_add_flag(ui_party_mode_receiver, LV_OBJ_FLAG_HIDDEN);
}

static void ui_party_mode_menu_screen_init(ui_party_mode_para_t * party_mode_para)
{
    if(ui_party_mode_menu)
    {
        lv_obj_remove_flag(ui_party_mode_menu, LV_OBJ_FLAG_HIDDEN);     /// Flags
        return;
    }

    ui_party_mode_menu = lv_obj_create(party_mode_para->source_para->ui_menu_panel);
    lv_obj_remove_style_all(ui_party_mode_menu);
    lv_obj_set_width(ui_party_mode_menu, lv_pct(100));
    lv_obj_set_height(ui_party_mode_menu, lv_pct(100));
    //lv_obj_add_flag(ui_party_mode_menu, LV_OBJ_FLAG_HIDDEN);      /// Flags
    lv_obj_clear_flag(ui_party_mode_menu, LV_OBJ_FLAG_SCROLLABLE | LV_OBJ_FLAG_CLICKABLE);      /// Flags
    lv_obj_set_style_bg_color(ui_party_mode_menu, lv_color_hex(0x000000), LV_PART_MAIN | LV_STATE_DEFAULT);
    lv_obj_set_style_bg_opa(ui_party_mode_menu, 255, LV_PART_MAIN | LV_STATE_DEFAULT);
}

static void ui_party_mode_menu_screen_uninit(ui_party_mode_para_t * party_mode_para)
{		
	lv_obj_add_flag(ui_party_mode_menu, LV_OBJ_FLAG_HIDDEN);
}

static void ui_party_mode_screen_init(ui_party_mode_para_t * party_mode_para)
{
    ui_party_mode = lv_obj_create(party_mode_para->source_para->ui_play);
	lv_obj_remove_style_all(ui_party_mode);
    lv_obj_set_width(ui_party_mode, lv_pct(100));
    lv_obj_set_height(ui_party_mode, lv_pct(100));
    lv_obj_set_align(ui_party_mode, LV_ALIGN_CENTER);
    lv_obj_clear_flag(ui_party_mode, LV_OBJ_FLAG_CLICKABLE | LV_OBJ_FLAG_SCROLLABLE);      /// Flags
    lv_obj_set_style_bg_opa(ui_party_mode, 0, LV_PART_MAIN | LV_STATE_DEFAULT);

    ui_party_mode_receiver_screen_init(party_mode_para);

	lv_obj_add_event_cb(ui_party_mode, ui_event_party_mode_screen_cb, LV_EVENT_ALL, party_mode_para);
	lv_obj_add_event_cb(party_mode_para->source_para->ui_top_left, ui_event_party_mode_left_cb, LV_EVENT_ALL, party_mode_para);
	lv_obj_add_event_cb(party_mode_para->source_para->ui_top_right, ui_event_party_mode_right_cb, LV_EVENT_ALL, party_mode_para);
    lv_obj_add_event_cb(party_mode_para->source_para->ui_rig_bar_bg, ui_event_party_mode_open_bar_cb, LV_EVENT_ALL, party_mode_para);
}

static void ui_party_mode_screen_uninit(ui_party_mode_para_t * party_mode_para)
{
    ui_party_mode_switch_win(party_mode_para, PARTY_WIN_NONE);
	lv_obj_remove_event_cb(party_mode_para->source_para->ui_top_left, ui_event_party_mode_left_cb);
	lv_obj_remove_event_cb(party_mode_para->source_para->ui_top_right, ui_event_party_mode_right_cb);
    lv_obj_remove_event_cb(party_mode_para->source_para->ui_rig_bar_bg, ui_event_party_mode_open_bar_cb);
#if 0
    lv_obj_remove_event_cb(party_mode_para->source_para->ui_rig_panel_button_bottom, ui_event_party_mode_bottom_menu_cb);
#endif
    if(ui_party_mode_select_mode)
    {
        lv_obj_del(ui_party_mode_select_mode);
        ui_party_mode_select_mode = NULL;
    }
    
    if(ui_party_mode_broadcast)
    {
        lv_obj_del(ui_party_mode_broadcast);
        ui_party_mode_broadcast = NULL;
    }
    
    if(ui_party_mode_receiver)
    {
        lv_obj_del(ui_party_mode_receiver);
        ui_party_mode_receiver = NULL;
    }
    
    if(ui_party_mode_menu)
    {
        lv_obj_del(ui_party_mode_menu);
        ui_party_mode_menu = NULL;
    }
    
    if(ui_party_mode_bottom_list)
    {
        //lv_obj_del(ui_party_mode_bottom_list);
        ui_party_mode_bottom_list = NULL;
    }
    
    if(ui_party_mode_bottom_browse)
    {
        //lv_obj_del(ui_party_mode_bottom_browse);
        ui_party_mode_bottom_browse = NULL;
    }
    
    if(ui_party_mode_bottom_receiver)
    {
        //lv_obj_del(ui_party_mode_bottom_receiver);
        ui_party_mode_bottom_receiver = NULL;
    }

    //if(ui_party_mode_menu_panel)
    //{
    //    lv_obj_del(ui_party_mode_menu_panel);
    //    ui_party_mode_menu_panel = NULL;
    //}
    
    if(ui_party_mode)
    {
	    lv_obj_del(ui_party_mode);
        ui_party_mode = NULL;
    }
}



void ui_party_mode_init(void * para)
{
	if(!ui_party_mode_para)
	{		
		ui_party_mode_para = (ui_party_mode_para_t *)esMEMS_Balloc(sizeof(ui_party_mode_para_t));
		if(ui_party_mode_para == NULL)
		{
			eLIBs_printf("esMEMS_Balloc() fail!\n");
			return;
		}
		eLIBs_memset((void*)ui_party_mode_para, 0, sizeof(ui_party_mode_para_t));
        ui_party_mode_internal_para_update(ui_party_mode_para);

        // Initialize MCU Protocol v2 communication interface
        if (ui_party_mode_para->source_para) {
            ui_party_mode_para->mcu_ops.sync_all = ui_mcu_party_sync_all;
            ui_party_mode_para->mcu_ops.sync_dirty = ui_mcu_party_sync_dirty;
            ui_party_mode_para->mcu_ops.ops = &ui_party_mode_para->source_para->mcu_v2_ops;
            
            // Execute initial full data sync
            if (ui_party_mode_para->mcu_ops.sync_all) {
                ui_party_mode_para->mcu_ops.sync_all(ui_party_mode_para);
            }
        }

	    ui_party_mode_init_res(ui_party_mode_para);
		ui_party_mode_send_srv_msg(GUI_MSG_PARTY_MODE_REFRESH_INIT,0,0,0,0,0,0);
	    ui_party_mode_helpers_init(ui_party_mode_para, para);
    }
    else
    {
    	
    }
}

void ui_party_mode_uninit(void * para)
{
	#if 1
	if(ui_party_mode_para)
	{
		ui_party_mode_para->mode_uninit_flag = 1;
		
		ui_party_mode_helpers_uninit(ui_party_mode_para);
		ui_party_mode_send_srv_msg(GUI_MSG_PARTY_MODE_REFRESH_UNINIT,0,0,0,0,1,1);
	}
	#endif
}

