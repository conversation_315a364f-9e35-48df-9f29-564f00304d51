########### paramter (ref to tulip_cedarx.conf)############
[paramter]
start_play_cache_video_frame_num = 30
start_play_cache_size = 128            # KB
cache_buffer_size = 20480              # KB
cache_buffer_size_live = 20480         # KB
start_play_cache_time = 5              # Second
max_start_play_chache_size = 153600    # KB
max_cache_buffer_size = 204800         # KB

# see comment in player.cpp
av_sync_duration = 0              # millisecond

# picture num for modules
pic_4list_num = 3
pic_4di_num = 0
pic_4rotate_num = 0
pic_4smooth_num = 0
# picture format: mb32/nv21/nv12/nv
# deinterlace format
deinterlace_fmt = nv12

# video decoder output picture format
vd_output_fmt = nv21

# gpu align stride values: 16/32
# mali 400mp2 32bit
gpu_align_bitwidth = 32

# if value 1 : send 3-frames black-pic to GPU
# SP: switch Program
#black_pic_4_SP = 0

# compensate for av vsync
compensate_vsync = 4

# log will output if level >= log_level
#VERBOSE = 2,
#DEBUG = 3,
#INFO = 4,
#WARNING = 5,
#ERROR = 6,
log_level = 5

########### plugin ##############
# 1. audio decoder plugin

# 2. video decoder plugin

#[vdecoder-0]
#comment = h264_vdecoder
#id = vdecoder.h264
#lib = libawh264.so
#init = CedarPluginVDInit

#[vdecoder-1]
#comment = h265_vdecoder
#id = vdecoder.h265
#lib = libawh265.so
#init = CedarPluginVDInit

#[vdecoder-2]
#comment = mjpegplus_vdecoder
#id = vdecoder.mjpegplus
#lib = libawmjpegplus.so
#init = CedarPluginVDInit

# 3. external plugin

#[plugin-0]
#comment = rtp_plugin
#id = rtp
#lib = librtp.so
