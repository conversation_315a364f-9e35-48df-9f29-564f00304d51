;/**************************************************************************/
;2010-06-09
;Sam
;image
;/**************************************************************************/


[MAIN_TYPE]
ITEM_COMMON         = "COMMON  "
ITEM_INFO           = "INFO    "
ITEM_BOOTROM        = "BOOTROM "
ITEM_FES            = "FES     "
ITEM_FET            = "FET     "
ITEM_FED            = "FED     "
ITEM_FEX            = "FEX     "
ITEM_BOOT           = "BOOT    "
ITEM_ROOTFSFAT12    = "RFSFAT12"
ITEM_ROOTFSFAT16    = "RFSFAT16"
ITEM_ROOTFSFAT32    = "FFSFAT32"
ITEM_USERFSFAT12    = "UFSFAT12"
ITEM_USERFSFAT16    = "UFSFAT16"
ITEM_USERFSFAT32    = "UFSFAT32"
ITEM_PHOENIX_SCRIPT = "PXSCRIPT"
ITEM_PHOENIX_TOOLS  = "PXTOOLS "
ITEM_AUDIO_DSP      = "AUDIODSP"
ITEM_VIDEO_DSP      = "VIDEODSP"
ITEM_FONT           = "FONT    "
ITEM_FLASH_DRV      = "FLASHDRV"
ITEM_OS_CORE        = "OS_CORE "
ITEM_DRIVER         = "DRIVER  "
ITEM_PIC            = "PICTURE "
ITEM_AUDIO          = "AUDIO   "
ITEM_VIDEO          = "VIDEO   "
ITEM_APPLICATION    = "APP     "

[SUB_TYPE]
SUBTYPEdemo1 = "071228HWSXXXX100"
;-->071228 2007-12-28
;-->HWS hardware scan
;-->100 version 1.00


[DIR_DEF]
INPUT_DIR = "../"

[FILELIST]
    ;-->constant
    {filename = "sys_config_card_product.fex",          maintype = ITEM_COMMON,         subtype = "SYS_CONFIG100000",},
    {filename = "config_card_product.fex",              maintype = ITEM_COMMON,         subtype = "SYS_CONFIG_BIN00",},
    {filename = "split_xxxx.fex",               maintype = ITEM_COMMON,         subtype = "SPLIT_0000000000",},
    {filename = "sys_partition_card_product.fex",       maintype = ITEM_COMMON,         subtype = "SYS_CONFIG000000",},
    {filename = "sunxi.fex",                    maintype = ITEM_COMMON,         subtype = "DTB_CONFIG000000",},

    ;-->boot files
    {filename = "boot0_nor.fex",                maintype = "12345678",          subtype = "1234567890BNOR_0",},
    {filename = "boot0_card_product.fex",               maintype = "12345678",          subtype = "1234567890BOOT_0",},
    {filename = "boot_pkg_uboot_card_product.fex",      maintype = "12345678",          subtype = "BOOTPKG-00000000",},
    {filename = "boot_pkg_uboot_card_product.fex",      maintype = "12345678",          subtype = "BOOTPKG-NOR00000",},
    {filename = "u-boot_nor.fex",               maintype = "12345678",          subtype = "UBOOT_0000000000",},
    {filename = "fes1.fex",                     maintype = ITEM_FES,            subtype = "FES_1-0000000000",},
    ;-------------------------------usb download part-------------------------------------;
    ;-->usb tools
    {filename = "usbtool.fex",                  maintype = "PXTOOLSB",          subtype = "xxxxxxxxxxxxxxxx",},
    {filename = "usbtool_crash.fex",            maintype = "PXTOOLCH",          subtype = "xxxxxxxxxxxxxxxx",},
    {filename = "aultools.fex",                 maintype = "UPFLYTLS",          subtype = "xxxxxxxxxxxxxxxx",},
    {filename = "aultls32.fex",                 maintype = "UPFLTL32",          subtype = "xxxxxxxxxxxxxxxx",},

    ;-------------------------------card download part----------------------------------------;
    ;-->card tools
    {filename = "cardtool.fex",                 maintype = "12345678",          subtype = "1234567890cardtl",},
    {filename = "cardscript_product.fex",               maintype = "12345678",          subtype = "1234567890script",},

    ;-->other
;    {filename = "sunxi_gpt.fex",                maintype = "12345678",          subtype = "1234567890___GPT",},
    {filename = "sunxi_mbr_card_product.fex",           maintype = "12345678",          subtype = "1234567890___MBR",},
    {filename = "env.fex",           maintype = "12345678",          subtype = "ENV_FEX000000000",},
    {filename = "data_udisk.fex",           maintype = "12345678",          subtype = "DATA_UDISK_FEX00",},
    {filename = "dlinfo.fex",                   maintype = "12345678",          subtype = "1234567890DLINFO",},
    {filename = "arisc.fex",                    maintype = "12345678",          subtype = "1234567890ARISC" ,},


[IMAGE_CFG]
version = 0x100234
pid = 0x00001234
vid = 0x00008743
hardwareid = 0x100
firmwareid = 0x100
bootromconfig = "bootrom_071203_00001234.cfg"
rootfsconfig = "rootfs.cfg"
;;imagename = "ePDKv100_nand.img"
filelist = FILELIST
;imagename = ..\sun4i_test_evb.img
encrypt = 0

imagename = ePDKv100.img

