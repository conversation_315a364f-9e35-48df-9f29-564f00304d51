libcurl bindings
================

 Creative people have written bindings or interfaces for various environments
 and programming languages. Using one of these allows you to take advantage of
 curl powers from within your favourite language or system.

 This is a list of all known interfaces as of this writing.

 The bindings listed below are not part of the curl/libcurl distribution
 archives, but must be downloaded and installed separately.

[Ada95](https://web.archive.org/web/20070403105909/www.almroth.com/adacurl/index.html) Written by <PERSON>

[Basic](http://scriptbasic.com/) ScriptBasic bindings written by <PERSON>+: [curlpp](http://curlpp.org/) Written by <PERSON><PERSON><PERSON>,
[curlcpp](https://github.com/JosephP91/curlcpp) by <PERSON> and [C++
Requests](https://github.com/whoshuu/cpr) by <PERSON><PERSON>

[Ch](https://chcurl.sourceforge.io/) Written by <PERSON> and <PERSON>: [BBHTTP](https://github.com/bruno<PERSON><PERSON><PERSON><PERSON>/BBHTTP) written by <PERSON>
[curlhandle](https://github.com/karelia/curlhandle) Written by <PERSON>: [clj-curl](https://github.com/lsevero/clj-curl) by Lucas Severo

[D](https://dlang.org/library/std/net/curl.html) Written by Kenneth Bogert

[Delphi](https://github.com/Mercury13/curl4delphi) Written by Mikhail Merkuryev

[Dylan](https://dylanlibs.sourceforge.io/) Written by Chris Double

[Eiffel](https://room.eiffel.com/library/curl) Written by Eiffel Software

[Euphoria](https://web.archive.org/web/20050204080544/rays-web.com/eulibcurl.htm) Written by Ray Smith

[Falcon](http://www.falconpl.org/index.ftd?page_id=prjs&prj_id=curl)

[Ferite](https://web.archive.org/web/20150102192018/ferite.org/) Written by Paul Querna

[Gambas](https://gambas.sourceforge.io/)

[glib/GTK+](https://web.archive.org/web/20100526203452/atterer.net/glibcurl) Written by Richard Atterer

Go: [go-curl](https://github.com/andelf/go-curl) by ShuYu Wang

[Guile](http://www.lonelycactus.com/guile-curl.html) Written by Michael L. Gran

[Harbour](https://github.com/vszakats/harbour-core/tree/master/contrib/hbcurl) Written by Viktor Szakáts

[Haskell](https://hackage.haskell.org/cgi-bin/hackage-scripts/package/curl) Written by Galois, Inc

[Java](https://github.com/pjlegato/curl-java)

[Julia](https://github.com/forio/Curl.jl) Written by Paul Howe

[Kapito](https://github.com/puzza007/katipo) is an Erlang HTTP library around libcurl.

[Lisp](https://common-lisp.net/project/cl-curl/) Written by Liam Healy

Lua: [luacurl](http://luacurl.luaforge.net/) by Alexander Marinov, [Lua-cURL](https://github.com/Lua-cURL) by Jürgen Hötzel

[Mono](https://forge.novell.com/modules/xfmod/project/?libcurl-mono) Written by Jeffrey Phillips

[.NET](https://sourceforge.net/projects/libcurl-net/) libcurl-net by Jeffrey Phillips

[Nim](https://nimble.directory/pkg/libcurl) wrapper for libcurl

[node.js](https://github.com/JCMais/node-libcurl) node-libcurl by Jonathan Cardoso Machado

[Object-Pascal](https://web.archive.org/web/20020610214926/www.tekool.com/opcurl) Free Pascal, Delphi and Kylix binding written by Christophe Espern.

[OCaml](https://opam.ocaml.org/packages/ocurl/) Written by Lars Nilsson and ygrek

[Pascal](https://web.archive.org/web/20030804091414/houston.quik.com/jkp/curlpas/) Free Pascal, Delphi and Kylix binding written by Jeffrey Pohlmeyer.

Perl: [WWW::Curl](https://github.com/szbalint/WWW--Curl) Maintained by Cris
Bailiff and Bálint Szilakszi,
[perl6-net-curl](https://github.com/azawawi/perl6-net-curl) by Ahmad M. Zawawi
[NET::Curl](https://metacpan.org/pod/Net::Curl) by Przemyslaw Iskra

[PHP](https://php.net/curl) Originally written by Sterling Hughes

[PostgreSQL](https://github.com/pramsey/pgsql-http) - HTTP client for PostgreSQL

[PureBasic](https://www.purebasic.com/documentation/http/index.html) uses libcurl in its "native" HTTP subsystem

[Python](http://pycurl.io/) PycURL by Kjetil Jacobsen

[R](https://cran.r-project.org/package=curl)

[Rexx](https://rexxcurl.sourceforge.io/) Written Mark Hessling

[Ring](https://ring-lang.sourceforge.io/doc1.3/libcurl.html) RingLibCurl by Mahmoud Fayed

RPG, support for ILE/RPG on OS/400 is included in source distribution

Ruby: [curb](https://github.com/taf2/curb) written by Ross Bamford

[Rust](https://github.com/carllerche/curl-rust) curl-rust - by Carl Lerche

[Scheme](https://www.metapaper.net/lisovsky/web/curl/) Bigloo binding by Kirill Lisovsky

[Scilab](https://help.scilab.org/docs/current/fr_FR/getURL.html) binding by Sylvestre Ledru

[S-Lang](https://www.jedsoft.org/slang/modules/curl.html) by John E Davis

[Smalltalk](http://www.squeaksource.com/CurlPlugin/) Written by Danil Osipchuk

[SP-Forth](https://sourceforge.net/p/spf/spf/ci/master/tree/devel/~ac/lib/lin/curl/) Written by Andrey Cherezov

[SPL](http://www.clifford.at/spl/) Written by Clifford Wolf

[Tcl](https://web.archive.org/web/20160826011806/mirror.yellow5.com/tclcurl/) Tclcurl by Andrés García

[Visual Basic](https://sourceforge.net/projects/libcurl-vb/) libcurl-vb by Jeffrey Phillips

[Visual Foxpro](https://web.archive.org/web/20130730181523/www.ctl32.com.ar/libcurl.asp) by Carlos Alloatti

[Q](https://q-lang.sourceforge.io/) The libcurl module is part of the default install

[wxWidgets](https://wxcode.sourceforge.io/components/wxcurl/) Written by Casey O'Donnell

[XBLite](https://web.archive.org/web/20060426150418/perso.wanadoo.fr/xblite/libraries.html) Written by David Szafranski

[Xojo](https://github.com/charonn0/RB-libcURL) Written by Andrew Lambert
