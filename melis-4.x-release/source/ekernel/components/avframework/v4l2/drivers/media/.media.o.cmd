cmd_ekernel/components/avframework/v4l2/drivers/media/media.o := if [ -n "" ]; then mv ekernel/components/avframework/v4l2/drivers/media/media.o ekernel/components/avframework/v4l2/drivers/media/media.o.tmp && /home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/source/../toolchain/riscv64-elf-x86_64-20201104//bin/riscv64-unknown-elf-objcopy -S -O binary -R .note -R .comment -R COMMON -R .ARM.attributes -R .en_frame -R .init -R .fini --gap-fill=0xff  ekernel/components/avframework/v4l2/drivers/media/media.o.tmp ekernel/components/avframework/v4l2/drivers/media/media.o && rm -fr ekernel/components/avframework/v4l2/drivers/media/media.o.tmp; fi
