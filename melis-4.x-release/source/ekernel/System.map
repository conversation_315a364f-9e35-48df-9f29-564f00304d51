0000000000001000 A __STACKSIZE__
0000000040000000 T __DRAM_SBI_RUN_ADDRESS
0000000040000000 A __sbi_binary_start
0000000040000000 T _firmware_start
0000000040000000 T blob_input_sbi_start
000000004000be00 A __sbi_binary_end
000000004000be00 T blob_input_sbi_end
0000000040010000 T __DRAM_KRN_RUN_ADDRESS
0000000040010000 T melis_head
0000000040010600 T _start
000000004001075c t clear_bss
0000000040010768 t clear_bss_done
00000000400107ac t page_init
00000000400107f8 t running_address
0000000040010810 t to_c
0000000040010838 t _load_start
0000000040010840 t _link_start
0000000040010848 t _link_end
0000000040010858 T awos_arch_task_switch
00000000400108c6 T awos_arch_first_task_start
000000004001090c T awos_arch_chksched_start
000000004001091a T awos_arch_save_fpu_status
00000000400109a4 T awos_arch_restore_fpu_status
0000000040010a2e t .gpldr
0000000040010a2e T check_gp_balance
0000000040010a38 T delay_10insn
0000000040010a4c t do_prepare
0000000040010a84 t do_resample
0000000040010ac2 T Create_Resampler
0000000040010c18 T Destroy_Resampler
0000000040010c78 t STAURATE16
0000000040010c78 T clip
0000000040010c96 T Init_ResampleInfo
0000000040010cc4 T Destroy_ResampleInfo
0000000040010ccc T PreAMP
0000000040010d14 T AudioMixFuc
0000000040010f5a T blackman
0000000040011070 T gcd
000000004001107e T AudioResample
0000000040011642 T do_AuMIX
0000000040011700 T __assert_func
0000000040011750 T __assert
0000000040011770 T atoi
0000000040011790 T _atoi_r
00000000400117b0 T atoll
00000000400117c0 T _atoll_r
00000000400117e0 T calloc
0000000040011800 T ctime
0000000040011820 T exit
0000000040011850 T _fclose_r
0000000040011900 T fclose
0000000040011910 T ferror
0000000040011960 T __sflush_r
0000000040011b00 T _fflush_r
0000000040011b40 T fflush
0000000040011b80 T ffs
0000000040011ba0 T _fgets_r
0000000040011c70 T fgets
0000000040011c90 T fileno
0000000040011ce0 t __fp_lock
0000000040011cf0 T _cleanup_r
0000000040011d00 t __sinit.part.0
0000000040011e30 t __fp_unlock
0000000040011e40 T __sfmoreglue
0000000040011e90 T __sfp
0000000040011f60 T _cleanup
0000000040011f80 T __sinit
0000000040011f90 T __sfp_lock_acquire
0000000040011fa0 T __sfp_lock_release
0000000040011fb0 T __sinit_lock_acquire
0000000040011fc0 T __sinit_lock_release
0000000040011fd0 T __fp_lock_all
0000000040011ff0 T __fp_unlock_all
0000000040012020 T _fiprintf_r
0000000040012040 T fiprintf
0000000040012080 T fls
00000000400120b0 T _fopen_r
0000000040012170 T fopen
0000000040012190 T _fprintf_r
00000000400121b0 T fprintf
00000000400121f0 T _fputc_r
0000000040012230 T fputc
0000000040012280 T _fputs_r
00000000400122f0 T fputs
0000000040012310 T _fread_r
00000000400124c0 T fread
00000000400124e0 T _fseek_r
00000000400124f0 T fseek
0000000040012510 T _fseeko_r
0000000040012850 T fseeko
0000000040012870 T _ftell_r
0000000040012880 T ftell
00000000400128a0 T _ftello_r
0000000040012950 T ftello
0000000040012960 T __sfvwrite_r
0000000040012cc0 T _fwalk
0000000040012d50 T _fwalk_reent
0000000040012df0 T _fwrite_r
0000000040012e70 T fwrite
0000000040012e90 T _getchar_r
0000000040012ea0 T getchar
0000000040012ec0 T _getc_r
0000000040012f20 T getc
0000000040012f90 t permute
0000000040013010 t getopt_internal
00000000400136f0 T getopt
0000000040013750 T getopt_long
00000000400137b0 T getopt_long_only
0000000040013810 T __getopt_r
0000000040013820 T __getopt_long_r
0000000040013830 T __getopt_long_only_r
0000000040013840 T localtime
0000000040013860 T localtime_r
0000000040013ab0 T _setlocale_r
0000000040013b10 T __locale_mb_cur_max
0000000040013b30 T __locale_ctype_ptr_l
0000000040013b40 T __locale_ctype_ptr
0000000040013b60 T setlocale
0000000040013b80 T __swhatbuf_r
0000000040013c10 T __smakebuf_r
0000000040013ce0 T malloc
0000000040013cf0 T free
0000000040013d10 T _mbtowc_r
0000000040013d30 T __ascii_mbtowc
0000000040013d70 T memchr
0000000040013e30 T memcmp
0000000040013e98 T memcpy
0000000040013f68 T memmove
000000004001403c T memset
00000000400140f0 t validate_structure
00000000400142f0 T mktime
0000000040014630 T _putc_r
0000000040014690 T putc
0000000040014710 T _puts_r
00000000400147a0 T puts
00000000400147b0 T srand
00000000400147d0 T rand
0000000040014810 T srandom
0000000040014830 T random
0000000040014870 T realloc
0000000040014890 t lflush
00000000400148b0 T __srefill_r
0000000040014a20 T __srget_r
0000000040014a70 T __srget
0000000040014a90 T setbuf
0000000040014aa0 T setvbuf
0000000040014c50 T _snprintf_r
0000000040014cf0 T snprintf
0000000040014da0 T _sprintf_r
0000000040014df0 T sprintf
0000000040014e50 T sscanf
0000000040014eb0 T _sscanf_r
0000000040014f20 T __sread
0000000040014f60 T __seofread
0000000040014f70 T __swrite
0000000040014fd0 T __sseek
0000000040015020 T __sclose
0000000040015030 T strcat
0000000040015090 T strchr
00000000400151a4 T strcmp
0000000040015230 T strcpy
00000000400152e0 T strdup
0000000040015300 T _strdup_r
0000000040015350 T _strerror_r
0000000040015750 T strerror
0000000040015770 T strerror_l
0000000040015790 T strlcpy
00000000400157f0 T strlen
0000000040015890 T strncmp
0000000040015950 T strncpy
0000000040015a00 T strrchr
0000000040015a40 t critical_factorization
0000000040015b00 t two_way_long_needle
0000000040015d70 T strstr
0000000040015fb0 t sulp
0000000040016000 T _strtod_l
0000000040016e00 T _strtod_r
0000000040016e20 T strtod_l
0000000040016e40 T strtod
0000000040016e70 T strtof_l
0000000040016f00 T strtof
0000000040016fb0 t _strtoll_l.isra.0
0000000040017140 T _strtoll_r
0000000040017160 T strtoll_l
0000000040017180 T strtoll
00000000400171b0 t _strtol_l.isra.0
0000000040017340 T _strtol_r
0000000040017360 T strtol_l
0000000040017380 T strtol
00000000400173b0 t _strtoul_l.isra.0
0000000040017570 T _strtoul_r
0000000040017590 T strtoul_l
00000000400175b0 T strtoul
00000000400175e0 T _svfprintf_r
00000000400190a0 T __ssvfscanf_r
000000004001b250 T gettimeofday
000000004001b270 T time
000000004001b2b0 T __tzcalc_limits
000000004001b450 T __tz_lock
000000004001b460 T __tz_unlock
000000004001b470 T _tzset_unlocked
000000004001b480 T tzset
000000004001b4b0 T _tzset_unlocked_r
000000004001b880 T _tzset_r
000000004001b8a0 T _user_strerror
000000004001b8c0 t __sprint_r.part.0
000000004001b960 T __sprint_r
000000004001b970 T _vfiprintf_r
000000004001c650 T vfiprintf
000000004001c670 t __sbprintf
000000004001c710 T _vfprintf_r
000000004001e250 T vfprintf
000000004001e270 t __sbprintf
000000004001e320 T __swbuf_r
000000004001e420 T __swbuf
000000004001e440 T _wctomb_r
000000004001e460 T __ascii_wctomb
000000004001e4a0 T __swsetup_r
000000004001e5b0 T abort
000000004001e5d0 T asctime
000000004001e5f0 T asctime_r
000000004001e650 T __call_exitprocs
000000004001e720 T div
000000004001e770 T __sflags
000000004001e810 T __fputwc
000000004001e8e0 T _fputwc_r
000000004001e910 T fputwc
000000004001e980 t rshift
000000004001ea50 T __gethex
000000004001f040 T __match
000000004001f080 T __hexnan
000000004001f250 T _findenv
000000004001f270 T getenv
000000004001f2a0 T _findenv_r
000000004001f360 T _getenv_r
000000004001f380 T __gettzinfo
000000004001f3a0 T gmtime_r
000000004001f550 T iswspace
000000004001f580 t eshdn1
000000004001f5e0 t eshup1
000000004001f640 t m16m
000000004001f6c0 t eisnan.part.0
000000004001f6e0 t eneg
000000004001f710 t eisneg
000000004001f740 t emovi
000000004001f800 t ecmp
000000004001f900 t eisinf.part.1
000000004001f920 t eshift.part.3
000000004001fa80 t enormlz
000000004001fba0 t emdnorm
000000004001fef0 t eiremain
000000004001ffd0 t emovo.isra.6
0000000040020060 t emul
0000000040020380 t ediv
0000000040020760 t e113toe.isra.8
0000000040020860 T _ldtoa_r
00000000400216d0 T _ldcheck
0000000040021710 T __localeconv_l
0000000040021720 T _localeconv_r
0000000040021740 T localeconv
0000000040021770 T _mbrtowc_r
00000000400217f0 T mbrtowc
0000000040021880 T _Balloc
00000000400218f0 T _Bfree
0000000040021910 T __multadd
00000000400219c0 T __s2b
0000000040021a90 T __hi0bits
0000000040021ae0 T __lo0bits
0000000040021b60 T __i2b
0000000040021b80 T __multiply
0000000040021cf0 T __pow5mult
0000000040021dc0 T __lshift
0000000040021ec0 T __mcmp
0000000040021f00 T __mdiff
0000000040022050 T __ulp
00000000400220b0 T __b2d
00000000400221d0 T __d2b
00000000400222c0 T __ratio
0000000040022360 T _mprec_log10
00000000400223a0 T __copybits
0000000040022400 T __any_on
0000000040022460 T __sccl
0000000040022500 T nanf
0000000040022520 T frexp
00000000400225a0 t _init_signal_r.part.0
00000000400225e0 T _init_signal_r
00000000400225f0 T _signal_r
0000000040022640 T _raise_r
00000000400226c0 T __sigtramp_r
0000000040022730 T raise
0000000040022740 T signal
00000000400227a0 T _init_signal
00000000400227c0 T __sigtramp
00000000400227e0 T _siprintf_r
0000000040022830 T siprintf
0000000040022890 T siscanf
00000000400228f0 T _siscanf_r
0000000040022960 T _strtold_r
0000000040022990 T strtold_l
00000000400229c0 T strtold
0000000040022a90 T _strtorx_l
0000000040022af0 t _strtoull_l.isra.0
0000000040022cb0 T _strtoull_r
0000000040022cd0 T strtoull_l
0000000040022cf0 T strtoull
0000000040022d20 T __ssprint_r
0000000040022e90 T _svfiprintf_r
00000000400239a0 T _sungetc_r
0000000040023a50 T __ssrefill_r
0000000040023aa0 T _sfread_r
0000000040023b40 T __ssvfiscanf_r
0000000040024ea0 T __submore
0000000040024f40 T _ungetc_r
0000000040025060 T ungetc
0000000040025080 T _wcrtomb_r
00000000400250e0 T wcrtomb
0000000040025150 T __env_lock
0000000040025160 T __env_unlock
0000000040025170 t rshift
0000000040025240 T increment
00000000400252d0 t rvOK
0000000040025570 T decrement
0000000040025590 T set_ones
0000000040025630 T _strtodg_l
00000000400269fe T __clzdi2
0000000040026a2c T __eqtf2
0000000040026a2c T __netf2
0000000040026ac0 T __getf2
0000000040026ac0 T __gttf2
0000000040026b64 T __letf2
0000000040026b64 T __lttf2
0000000040026c08 T __multf3
00000000400271e6 T __subtf3
0000000040027a04 T __fixtfsi
0000000040027a94 T __floatsitf
0000000040027af4 T __extenddftf2
0000000040027bb4 T __trunctfdf2
0000000040027de0 T cos
0000000040027e70 T floor
0000000040027fb0 T rint
0000000040028120 T sin
00000000400281b0 T pow
0000000040028410 T __ieee754_pow
0000000040028b90 T __ieee754_rem_pio2
0000000040028e60 T __ieee754_sqrt
0000000040029070 T __kernel_cos
00000000400291c0 T __kernel_rem_pio2
00000000400298e0 T __kernel_sin
0000000040029990 T fabs
00000000400299c0 T finite
00000000400299f0 T matherr
0000000040029a10 T nan
0000000040029a30 T scalbn
0000000040029b70 T copysign
0000000040029ba2 T rt_hw_context_switch_to
0000000040029bbe T finish_task_switch
0000000040029be6 T rt_hw_context_switch
0000000040029c74 T schedule_preempt_inirq
0000000040029cce T rt_hw_stack_init
0000000040029cd2 T cpu_do_idle
0000000040029ce0 T ret_from_create_c
0000000040029d48 T awos_arch_lock_irq
0000000040029d4e T awos_arch_unlock_irq
0000000040029d56 T rt_hw_interrupt_disable
0000000040029d5c T rt_hw_interrupt_enable
0000000040029d64 T irqs_disabled
0000000040029d72 T local_irq_enable
0000000040029d78 T local_irq_disable
0000000040029d7e T awos_arch_stack_init
0000000040029dda t default_handle.isra.0
0000000040029f5a T fpu_save_inirq
0000000040029f9c T fpu_restore_inirq
0000000040029fde T riscv_cpu_handle_exception
000000004002a088 T trap_init
000000004002a0a0 t arch_counter_get_cntpct
000000004002a0a6 t arch_counter_read
000000004002a0b4 T ktime_get
000000004002a0de T do_gettimeofday
000000004002a10c T udelay
000000004002a126 T usleep
000000004002a160 T msleep
000000004002a178 T sleep
000000004002a184 T timekeeping_init
000000004002a1a4 T timestamp
000000004002a1cc T system_tick_init
000000004002a1f0 T riscv_timer_interrupt
000000004002a228 t print_backtrace
000000004002a2d4 t check_task_is_running
000000004002a318 T backtrace_check_address
000000004002a330 t find_lr_offset
000000004002a3be T riscv_ins32_get_push_lr_framesize
000000004002a41c T riscv_ins32_backtrace_stask_push
000000004002a454 t riscv_backtrace_from_stack.isra.6
000000004002a6e4 t _backtrace.isra.8
000000004002aa84 T arch_backtrace
000000004002aa9a T arch_backtrace_exception
000000004002aabc T awos_init_bootstrap
000000004002ab46 T awos_arch_virt_to_phys
000000004002ab48 t copy_config
000000004002ab58 t awos_bsp_init
000000004002abd0 t usb_switch_io.part.0.constprop.3
000000004002ac38 t usb_switch_io.constprop.1
000000004002acb0 t awos_init_thread
000000004002ae84 T start_kernel
000000004002af00 T handle_exception
000000004002afbc t .interrupt
000000004002aff8 t .exception
000000004002b010 t .restore_all
000000004002b02c t .gpldr
000000004002b0e6 T awos_arch_clean_dcache
000000004002b0ec T awos_arch_flush_icache
000000004002b0f2 T awos_arch_mems_clean_dcache_region
000000004002b110 T awos_arch_mems_clean_flush_dcache_region
000000004002b12a T awos_arch_mems_flush_dcache_region
000000004002b144 T mmu_build_page_dirs
000000004002b21c T mmu_init
000000004002b260 T awos_arch_vaddr_map2_phys
000000004002b2f8 T check_virtual_address
000000004002b30a T flush_icache_all
000000004002b31a T __pmd_alloc
000000004002b384 T __pte_alloc_kernel
000000004002b3e4 T map_vm_area
000000004002b566 T __vmalloc_node_range
000000004002b67a T pud_clear_bad
000000004002b6a8 T unmap_vmap_area
000000004002b7e8 T get_memory_prot
000000004002b806 T module_alloc
000000004002b86c T module_free
000000004002b8fe T awos_arch_vmem_create
000000004002b938 T awos_arch_vmem_delete
000000004002b972 t plic_irq_eoi
000000004002b988 t plic_irq_toggle
000000004002b9e8 t plic_irq_unmask
000000004002b9f0 t plic_irq_mask
000000004002b9f8 T irq_disable
000000004002ba0a T irq_enable
000000004002ba1c T plic_handle_irq
000000004002ba6c T plic_init
000000004002bae2 t handle_level_irq
000000004002bb12 T interrupt_init
000000004002bb48 T irq_to_desc
000000004002bb66 t __disable_irq_nosync
000000004002bba6 T generic_handle_irq
000000004002bbc2 T free_irq
000000004002bc12 T disable_irq
000000004002bc16 T __enable_irq
000000004002bc22 T enable_irq
000000004002bc4e T request_threaded_irq
000000004002bd04 T handle_arch_irq
000000004002bd14 T riscv_cpu_handle_interrupt
000000004002bd5a T printf
000000004002bd8a T dump_memory
000000004002bdee T dump_register_memory
000000004002be48 T panic_goto_cli
000000004002be74 T melis_banner_string
000000004002be7e T show_melis_version
000000004002c026 T common_init
000000004002c028 T do_one_initcall
000000004002c030 T do_initcalls
000000004002c076 T app_entry
000000004002c096 T awos_task_create
000000004002c0b8 T awos_task_delete
000000004002c0c8 T awos_task_self
000000004002c0cc T awos_arch_tick_increase
000000004002c0d0 T awos_arch_isin_irq
000000004002c0e4 T rt_tick_get
000000004002c0f0 T rt_tick_set
000000004002c10e T rt_tick_increase
000000004002c146 T rt_tick_from_millisecond
000000004002c172 T rt_thread_idle_excute
000000004002c26a t rt_thread_idle_entry
000000004002c298 T rt_thread_idle_init
000000004002c2da T rt_thread_idle_gethandler
000000004002c2e4 t rt_ipc_list_resume_all
000000004002c320 t rt_ipc_list_resume.isra.0
000000004002c334 t preempt_count
000000004002c350 t preempt_count
000000004002c36c t rt_ipc_list_suspend
000000004002c3ce T rt_ipc_get_highest_priority
000000004002c3ec T rt_sem_init
000000004002c462 T rt_sem_detach
000000004002c4e0 T rt_sem_create
000000004002c576 T rt_sem_delete
000000004002c636 T rt_sem_take
000000004002c7ba T rt_sem_trytake
000000004002c7c0 T rt_sem_release
000000004002c854 T rt_sem_control
000000004002c8ec T rt_mutex_init
000000004002c946 T rt_mutex_detach
000000004002c9c4 T rt_mutex_create
000000004002ca3e T rt_mutex_delete
000000004002cafe T rt_mutex_take
000000004002ccfa T rt_mutex_release
000000004002ceaa T rt_event_create
000000004002cf20 T rt_event_delete
000000004002cfe0 T rt_event_send
000000004002d0c6 T rt_event_recv
000000004002d2c4 T rt_event_control
000000004002d35a T rt_mb_create
000000004002d3fe T rt_mb_delete
000000004002d4cc T rt_mb_send_wait
000000004002d6b8 T rt_mb_send
000000004002d6be T rt_mb_recv
000000004002d90a T rt_mb_control
000000004002d9ae t preempt_count_add
000000004002d9d2 T rt_interrupt_enter
000000004002d9fe T rt_interrupt_leave
000000004002da3a W rt_interrupt_get_nest
000000004002da6a T rt_enter_critical
000000004002da90 T rt_exit_critical
000000004002db0c T rt_critical_level
000000004002db1e t print_number
000000004002dcb4 T rt_get_errno
000000004002dcd8 T rt_set_errno
000000004002dd00 T __errno
000000004002dd00 T _rt_errno
000000004002dd24 T rt_memset
000000004002dda4 T rt_memcpy
000000004002de4e T rt_strncpy
000000004002de7c T rt_strncmp
000000004002dea0 T rt_strcmp
000000004002deb8 T rt_strlen
000000004002deca T rt_strdup
000000004002def8 T rt_vsnprintf
000000004002e2b0 T rt_snprintf
000000004002e2cc T rt_console_set_device
000000004002e30c W rt_hw_console_output
000000004002e340 T rt_kprintf
000000004002e3e2 T rt_malloc_align
000000004002e41e T rt_free_align
000000004002e426 W __rt_ffs
000000004002e476 T rt_assert_handler
000000004002e538 T rt_object_get_information
000000004002e566 T rt_object_init
000000004002e634 T rt_object_detach
000000004002e692 T rt_object_allocate
000000004002e770 T rt_object_delete
000000004002e7ec T rt_object_is_systemobject
000000004002e81e T rt_object_get_type
000000004002e850 t thread_set_resched
000000004002e872 t thread_clear_resched
000000004002e892 T rt_scheduler_sethook
000000004002e89c T rt_system_scheduler_init
000000004002e8de T rt_system_scheduler_start
000000004002e8fa T rt_schedule
000000004002ea42 T rt_schedule_insert_thread
000000004002eab2 T rt_schedule_remove_thread
000000004002eb24 t _rt_thread_init.isra.0
000000004002ec30 T rt_thread_exit
000000004002ecb2 t rt_thread_timeout
000000004002ed52 T rt_thread_init
000000004002edda T rt_thread_self
000000004002ede6 T rt_thread_detach
000000004002eeba T rt_thread_create
000000004002ef16 T rt_thread_nameset
000000004002ef4c T rt_thread_delete
000000004002f006 T rt_thread_yield
000000004002f070 T rt_thread_suspend
000000004002f10c T rt_thread_sleep
000000004002f1a4 T rt_thread_delay
000000004002f1a8 T rt_thread_resume
000000004002f248 T rt_thread_startup
000000004002f2f0 T rt_thread_control
000000004002f3c6 T rt_thread_find
000000004002f454 t _rt_timer_remove.part.1
000000004002f468 T rt_timer_init
000000004002f4d8 T rt_timer_detach
000000004002f564 T rt_timer_create
000000004002f5a2 T rt_timer_delete
000000004002f62e T rt_timer_start
000000004002f7d2 T rt_timer_stop
000000004002f85c T rt_timer_control
000000004002f8f8 T rt_timer_check
000000004002f9a2 T rt_soft_timer_check
000000004002fa54 t rt_thread_timer_entry
000000004002faa0 T rt_system_timer_init
000000004002faae T rt_system_timer_thread_init
000000004002fafa t rt_poll_add
000000004002fb0a T rt_pipe_control
000000004002fb0e T rt_pipe_open
000000004002fb4a t pipe_fops_open
000000004002fbd6 T rt_pipe_close
000000004002fc18 t pipe_fops_poll
000000004002fca0 t pipe_fops_ioctl
000000004002fce6 t pipe_fops_write
000000004002fdb8 t pipe_fops_read
000000004002fe6c T rt_pipe_read
000000004002fed8 T rt_pipe_write
000000004002ff44 T rt_pipe_create
0000000040030028 T rt_pipe_delete
0000000040030078 t pipe_fops_close
0000000040030136 T pipe
00000000400301ca T rt_ringbuffer_init
000000004003022a T rt_ringbuffer_data_len
000000004003025e T rt_ringbuffer_put
000000004003034a T rt_ringbuffer_get
0000000040030442 T rt_ringbuffer_create
00000000400304a0 T rt_ringbuffer_destroy
00000000400304d4 T __wqueue_default_wake
00000000400304d8 T rt_wqueue_add
0000000040030504 T rt_wqueue_remove
000000004003052c T rt_wqueue_wakeup
00000000400305a2 T rt_wqueue_wait
000000004003069a t rt_list_remove
00000000400306aa t _workqueue_thread_entry
0000000040030740 t _workqueue_submit_work
00000000400307b4 T rt_workqueue_create
0000000040030824 T rt_workqueue_dowork
0000000040030874 T rt_workqueue_cancel_work_sync
00000000400308f2 T rt_workqueue_cancel_all_work
000000004003093a T rt_workqueue_destroy
0000000040030984 t __rt_page_alloc
00000000400309de t __rt_page_free
0000000040030b12 t zoneindex
0000000040030bf0 T rt_page_alloc
0000000040030c3e T rt_page_free
0000000040030cbe T rt_system_heap_init
0000000040030e90 T __internal_malloc
000000004003110e T k_realloc
000000004003110e T rt_realloc
00000000400311f4 T k_calloc
00000000400311f4 T rt_calloc
000000004003121c T __internal_free
0000000040031462 T rt_memory_info
000000004003149a T list_mem
00000000400314f0 T rt_device_unregister
0000000040031566 T rt_device_find
00000000400315ea T rt_device_register
0000000040031640 T rt_device_create
0000000040031676 T rt_device_destroy
00000000400316ee T rt_device_open
00000000400317e8 T rt_device_close
000000004003186e T rt_device_read
00000000400318fc T rt_device_write
000000004003198a T rt_device_control
0000000040031a00 T rt_memheap_init
0000000040031ad2 T rt_memheap_alloc
0000000040031c3a T rt_memheap_free
0000000040031dbc T epos_memleak_detect_enable
0000000040031dee T epos_memleak_detect_disable
0000000040031ea6 T epos_memleak_detect_show
0000000040031f46 T k_malloc
0000000040031f46 T rt_malloc
0000000040031fda T k_free
0000000040031fda T rt_free
000000004003204a T printk
00000000400320be T dump_system_information
0000000040032230 T show_thread_info
0000000040032244 T dma_coherent_heap_init
0000000040032284 T dma_coherent_heap_alloc
0000000040032292 T dma_coherent_heap_alloc_align
00000000400322ce T dma_coherent_heap_free_align
00000000400322da t rt_sched_hook
00000000400322f4 T rt_perf_init
0000000040032368 T monitor_start
00000000400323bc T monitor_end
00000000400325c6 t rt_perf_thread
00000000400325f8 T rt_perf_exit
0000000040032630 t spi_init
0000000040032634 t spi_open
000000004003265a t spi_write
0000000040032684 t spi_close
00000000400326a8 t spi_read
00000000400326d2 t spi_control
000000004003272a t init_spi_device
0000000040032780 T sunxi_driver_spi_init
000000004003281e t sunxi_spinor_init
000000004003283e t sunxi_spinor_read
0000000040032876 t sunxi_spinor_open
000000004003287a t sunxi_spinor_close
000000004003287e t sunxi_spinor_write
00000000400328b6 t sunxi_spinor_fops_open
00000000400328e0 t sunxi_spinor_fops_close
00000000400328e4 t sunxi_spinor_fops_read
000000004003290c t sunxi_spinor_fops_write
0000000040032934 t sunxi_spinor_fops_lseek
0000000040032952 T sunxi_driver_spinor_init
0000000040032aa4 t sunxi_spinor_control
0000000040032b78 t sunxi_spinor_fops_ioctl
0000000040032b8a T sunxi_driver_spinor_deinit
0000000040032bb6 t part_fops_open
0000000040032be0 t part_fops_close
0000000040032be4 t part_fops_ioctl
0000000040032bf6 t part_fops_read
0000000040032c1e t part_fops_write
0000000040032c46 t part_fops_lseek
0000000040032c64 t calc_crc32
0000000040032cd4 T calc_crc32
0000000040032d44 t spinor_acdev_open
0000000040032d56 t spinor_acdev_close
0000000040032d68 t spinor_acdev_read
0000000040032d6e t spinor_dev_read
0000000040032dc2 t spinor_dev_close
0000000040032dea t spinor_dev_open
0000000040032e1e t melis_rwpart_mark_dirty_data
0000000040032e72 t melis_rwpart_flush_dirty_data
0000000040032f7c t spinor_acdev_ioctrl
000000004003306a t spinor_dev_write
00000000400330ba t spinor_acdev_write
00000000400330c0 T spinor_compare_rootfs_after_update_fw
00000000400331c0 t spinor_dev_ioctrl
0000000040033214 T melis_spinor_part_init
000000004003337e t register_blk_device
000000004003389a T nor_blkpart_init
000000004003391a T drv_gpio_pinmux_set_function
0000000040033958 T drv_gpio_get_input
000000004003398c T drv_gpio_set_output
00000000400339b8 T drv_gpio_get_output
00000000400339bc T drv_gpio_set_direction
00000000400339e8 T drv_gpio_set_pull_status
0000000040033a14 T drv_gpio_get_pull_status
0000000040033a48 T drv_gpio_set_driving_level
0000000040033a74 T drv_gpio_get_driving_level
0000000040033aa8 T drv_gpio_to_irq
0000000040033adc T drv_gpio_irq_request
0000000040033aee T drv_gpio_irq_free
0000000040033b00 T drv_gpio_irq_enable
0000000040033b12 T drv_gpio_irq_disable
0000000040033b24 T drv_gpio_init
0000000040033b34 t cmd_drv_avs
0000000040033b84 t avs_open
0000000040033ba8 t avs_close
0000000040033bcc t avs_init
0000000040033bd0 t avs_write
0000000040033bf6 t avs_read
0000000040033c1c t avs_control
0000000040033c44 T sunxi_driver_avs_init
0000000040033d60 T drv_dma_init
0000000040033d70 T sunxi_wdt_init
0000000040033d80 T reset_cpu
0000000040033d84 t sunxi_serial_open
0000000040033d88 t sunxi_serial_fops_open
0000000040033d8c t sunxi_serial_fops_lseek
0000000040033d90 t sunxi_serial_fops_getdents
0000000040033d94 t init_uart_device
0000000040033dfc t sunxi_serial_fops_poll
0000000040033e9c t uart_wakeup_poll_waitqueue
0000000040033ede t sunxi_serial_control
0000000040033f34 t sunxi_serial_fops_ioctl
0000000040033f46 t sunxi_serial_write
0000000040033fa2 t sunxi_serial_fops_write
0000000040033fba t sunxi_serial_read
0000000040034042 t sunxi_serial_fops_read
000000004003405a t sunxi_serial_close
00000000400340b0 t sunxi_serial_fops_close
00000000400340c2 t sunxi_serial_init
0000000040034118 t sunxi_serial_fops_flush
000000004003411c T sunxi_driver_uart_init
0000000040034202 t DEV_UART_Write
0000000040034234 t DEV_UART_Read
0000000040034266 t DEV_UART_Close
000000004003429a t DEV_UART_Open
00000000400342d4 t DEV_UART_Ioctl
00000000400343ee T sunxi_driver_uart_dms_init
0000000040034498 T CircleBufCreate
00000000400344f0 T CircleBufDestroy
000000004003452c T CircleBufRead
000000004003463c T CircleBufWrite
0000000040034752 T CircleBufFlush
000000004003477e T CircleBufQuerySize
00000000400347a4 t init_audio_play_device
00000000400347ec T sunxi_driver_audio_init
0000000040034a18 T audio_playback_config_mgr_create
0000000040034a88 T audio_record_config_mgr_create
0000000040034af4 T audio_config_mgr_release
0000000040034b26 T alsa_set_pcm_params
0000000040034dd8 T alsa_open_pcm
0000000040034e26 T alsa_close_pcm
0000000040034e72 T alsa_prepare_pcm
0000000040034e7c T alsa_read_pcm
0000000040034f28 T writeSoundDevice
0000000040035030 T alsa_write_pcm
0000000040035176 T alsa_mixer_set_volume
0000000040035214 T alsa_mixer_get_volume
00000000400352d8 T alsa_mixer_set_softvol
00000000400352fc T alsa_mixer_get_softvol
0000000040035332 T alsa_mixer_set_cap_rx_sync_mode
000000004003542a T alsa_mixer_set_hub_mode
0000000040035522 T alsa_mixer_set_daudio_loopback_enable
0000000040035568 T audio_cmd_start
000000004003558a T audio_cmd_stop
00000000400355ae T audio_cmd_pause
00000000400355d8 T audio_cmd_drop
00000000400355fa T audio_cmd_drain
000000004003561c T audio_cmd_resume
000000004003564c T audio_get_playback_avail
0000000040035678 T audio_play_init
0000000040035732 T audio_play_exit
00000000400357c6 T audio_play_open
000000004003586c T audio_play_close
000000004003592c T audio_play_read
0000000040035930 T single_to_double_chn
0000000040035956 T audio_play_write
0000000040035a40 T audio_play_control
0000000040035e6a t RelRecDevRes
0000000040035efe T audio_rec_init
0000000040035fb6 T audio_rec_open
0000000040036046 T audio_rec_close
0000000040036380 T audio_rec_read
00000000400363dc T audio_rec_write
00000000400363e0 T audio_rec_control
0000000040036926 T audio_rec_task
0000000040036a5c t fake_hal_twi_control
0000000040036a9c t twi_open
0000000040036ac0 t twi_close
0000000040036ae4 t twi_init
0000000040036ae8 t twi_read
0000000040036b26 t twi_read
0000000040036bc4 t twi_control
0000000040036bea t init_twi_device
0000000040036c40 T sunxi_driver_twi_init
0000000040036d04 t twi_write
0000000040036db6 t pwm_open
0000000040036dd0 t pwm_init
0000000040036dd4 t pwm_close
0000000040036df6 t pwm_read
0000000040036dfa t pwm_control
0000000040036e1a T sunxi_driver_pwm_init
0000000040036e8e t pwm_write
0000000040036e92 t is_event_support
0000000040036eae T find_input_dev_by_name
0000000040036ef4 T sunxi_input_event
0000000040037044 t sunxi_g2d_init
0000000040037048 t g2d_probe_wrap
00000000400370b4 t sunxi_g2d_control_wrap
00000000400370c6 t sunxi_g2d_close_wrap
00000000400370d4 t sunxi_g2d_open_wrap
00000000400370e2 t sunxi_di_init
00000000400370e6 t di_probe_wrap
0000000040037152 t sunxi_di_control_wrap
000000004003715a t sunxi_di_close_wrap
0000000040037168 t sunxi_di_open_wrap
0000000040037176 t sdmmc_dev_open
0000000040037178 t sdmmc_dev_close
000000004003717c t sdmmc_dev_ioctrl
00000000400371a2 T sdmmc_dev_phy_read
000000004003726c T sdmmc_dev_read
0000000040037274 T sdmmc_dev_phy_write
000000004003733e T sdmmc_dev_write
0000000040037346 T melis_part_init
0000000040037438 T melis_part_deinit
000000004003746c T sunxi_get_logical_offset_param
0000000040037484 T gpt_convert_to_sunxi_mbr
000000004003760a T melis_part_init_bootcard
0000000040037a2a T melis_part_deinit_bootcard
0000000040037a72 T register_part
0000000040037af6 T mount_sdmmc_filesystem
0000000040037b9e T unmount_sdmmc_filesystem
0000000040037c10 t disp_init_wrap
0000000040037c14 t disp_ioctl_wrap
0000000040037c26 t disp_release_wrap
0000000040037c34 t disp_open_wrap
0000000040037c42 T disp_module_init
0000000040037c6e t tv_init_wrap
0000000040037c72 t tv_ioctl_wrap
0000000040037c84 t tv_release_wrap
0000000040037c92 t tv_open_wrap
0000000040037ca0 T tv_drv_module_init
0000000040037ccc T drv_clock_init
0000000040037cdc t power_init
0000000040037ce0 t power_write
0000000040037ce4 T power_info
0000000040037ce8 T init_adc_power
0000000040037d50 t power_check_dcin
0000000040037da4 t PowerTimer
0000000040037da8 t power_control
0000000040037e12 t power_close
0000000040037ea2 t power_open
0000000040037fa8 t power_read
0000000040037fac t drv_usb_open
0000000040037fd0 t drv_usb_close
0000000040037ff4 t drv_usb_init
0000000040037ff8 t drv_usb_write
000000004003801e t drv_usb_read
0000000040038044 t drv_usb_control
0000000040038068 T sunxi_driver_usb_init
00000000400380d6 t transfer_complete_callback
0000000040038104 t usb_virt_claim_interface
0000000040038198 t usb_virt_bus_dev_driver_detach
000000004003822a t transfer_timer_callback
0000000040038258 T dev_usb_init
000000004003825c T dev_usb_uninit
0000000040038260 T dev_usb_write
0000000040038264 T dev_usb_read
0000000040038268 T dev_usb_control
0000000040038ba2 t sunxi_thermal_init
0000000040038ba6 t sunxi_thermal_open
0000000040038baa t sunxi_thermal_write
0000000040038bae t sunxi_thermal_control
0000000040038bda T sunxi_driver_thermal_init
0000000040038c56 t sunxi_thermal_read
0000000040038c6a t sunxi_thermal_close
0000000040038c6e T hal_clock_init
0000000040038c72 T hal_clock_get
0000000040038c76 T hal_clock_put
0000000040038c7a T hal_clk_set_parent
0000000040038c7e T hal_clk_get_parent
0000000040038c82 T hal_clk_round_rate
0000000040038c96 T hal_clk_get_rate
0000000040038ca8 T hal_clk_set_rate
0000000040038cac T hal_clock_is_enabled
0000000040038cb0 T hal_clock_enable
0000000040038cb4 T hal_clock_disable
0000000040038cb8 T reset_control_register
0000000040038cd8 T hal_reset_control_get
0000000040038d2c T hal_reset_control_put
0000000040038d42 T hal_reset_control_assert
0000000040038d62 T hal_reset_control_deassert
0000000040038d82 T hal_reset_control_reset
0000000040038da2 T hal_reset_control_status
0000000040038dbe W sunxi_mcu_init
0000000040038dc2 T clk_init
0000000040038de6 T clk_get
0000000040038e02 T clk_put
0000000040038e14 T clk_is_enabled
0000000040038e34 T clk_prepare_enable
0000000040038e3e T clk_disable_unprepare
0000000040038e48 T clk_get_parent
0000000040038e8e T clk_set_parent
0000000040038e9e T clk_get_rate
0000000040038ec2 T clk_set_rate
0000000040038eea T clk_round_rate
0000000040038f0a t clk_core_get_rate_nolock
0000000040038f22 t clk_core_round_rate_nolock
0000000040038f92 T clk_hw_get_flags
0000000040038fa2 T clk_hw_get_core
0000000040038fa8 T clk_core_get_by_name
0000000040038fee t clk_core_get_parent_by_index
000000004003904e t __clk_init_parent
0000000040039080 T clk_hw_get_parent_by_index
0000000040039098 T clk_hw_get_num_parents
00000000400390a0 T clk_hw_get_parent
00000000400390aa T __clk_determine_rate
00000000400390b8 T clk_mux_determine_rate_flags
00000000400391c8 T __clk_mux_determine_rate
00000000400391ce T clk_hw_round_rate
00000000400391f8 T clk_hw_unregister_with_slab
000000004003924a T clk_core_get
000000004003927a T clk_core_is_enabled
000000004003929a T clk_core_disable
00000000400392de T clk_core_enable
0000000040039326 T clk_core_round_rate
0000000040039352 T clk_core_recalc_rate
000000004003938e T clk_core_get_rate
00000000400393ae t __clk_core_init
000000004003944c T clk_hw_register
0000000040039598 T clk_hw_register_with_slab
00000000400396c8 T clk_hw_get_rate
00000000400396ce T clk_core_set_rate
0000000040039750 T clk_core_get_parent
000000004003976c T clk_hw_set_rate
000000004003978e T clk_core_set_parent
0000000040039868 T ccu_slab_init
00000000400398f2 T ccu_malloc
0000000040039936 T ccu_slab_deinit
000000004003995e t ccu_mux_is_enabled
000000004003996a t ccu_mux_disable
0000000040039976 t ccu_mux_enable
0000000040039982 t ccu_mux_get_prediv
0000000040039a3e t ccu_mux_recalc_rate
0000000040039a60 T ccu_mux_helper_apply_prediv
0000000040039a76 T ccu_mux_helper_determine_rate
0000000040039bb6 T ccu_mux_helper_get_parent
0000000040039c26 t ccu_mux_get_parent
0000000040039c32 T ccu_mux_helper_set_parent
0000000040039cc8 t ccu_mux_set_parent
0000000040039cd6 t ccu_nm_find_best
0000000040039d26 t ccu_nm_round_rate
0000000040039e00 t ccu_nm_set_rate
000000004003a006 t ccu_nm_recalc_rate
000000004003a0e0 t ccu_nm_is_enabled
000000004003a0ec t ccu_nm_disable
000000004003a0f8 t ccu_nm_enable
000000004003a104 T ccu_helper_wait_for_lock
000000004003a138 T ccu_common_init
000000004003a2b6 T set_reg
000000004003a2ec T set_reg_key
000000004003a334 t ccu_reset_status
000000004003a35e t ccu_reset_deassert
000000004003a3ce t ccu_reset_assert
000000004003a446 t ccu_reset_reset
000000004003a46a t ccu_div_set_rate
000000004003a548 t ccu_div_get_parent
000000004003a554 t ccu_div_set_parent
000000004003a562 t ccu_div_determine_rate
000000004003a57c t ccu_div_round_rate
000000004003a5c0 t ccu_div_recalc_rate
000000004003a63a t ccu_div_is_enabled
000000004003a646 t ccu_div_disable
000000004003a652 t ccu_div_enable
000000004003a65e T ccu_frac_helper_is_enabled
000000004003a684 T ccu_frac_helper_enable
000000004003a6e8 T ccu_frac_helper_disable
000000004003a744 T ccu_frac_helper_has_rate
000000004003a760 T ccu_frac_helper_read_rate
000000004003a788 T ccu_frac_helper_set_rate
000000004003a81c t ccu_gate_recalc_rate
000000004003a830 t ccu_gate_set_rate
000000004003a834 t ccu_gate_round_rate
000000004003a890 T ccu_gate_helper_disable
000000004003a924 t ccu_gate_disable
000000004003a930 T ccu_gate_helper_enable
000000004003a9be t ccu_gate_enable
000000004003a9ca T ccu_gate_helper_is_enabled
000000004003a9ea t ccu_gate_is_enabled
000000004003a9f6 t ccu_mp_recalc_rate
000000004003aa7c t ccu_mp_get_parent
000000004003aa88 t ccu_mp_set_parent
000000004003aa96 t ccu_mp_determine_rate
000000004003aab0 t ccu_mp_is_enabled
000000004003aabc t ccu_mp_disable
000000004003aac8 t ccu_mp_enable
000000004003aad4 t ccu_mp_round_rate
000000004003ac90 t ccu_mp_set_rate
000000004003aea0 t ccu_mult_set_rate
000000004003afbc t ccu_mult_get_parent
000000004003afc8 t ccu_mult_set_parent
000000004003afd6 t ccu_mult_determine_rate
000000004003aff0 t ccu_mult_recalc_rate
000000004003b074 t ccu_mult_is_enabled
000000004003b080 t ccu_mult_disable
000000004003b08c t ccu_mult_enable
000000004003b098 t ccu_mult_round_rate
000000004003b0d0 t ccu_nkmp_find_best
000000004003b16e t ccu_nkmp_recalc_rate
000000004003b234 t ccu_nkmp_round_rate
000000004003b312 t ccu_nkmp_set_rate
000000004003b556 t ccu_nkmp_is_enabled
000000004003b562 t ccu_nkmp_disable
000000004003b56e t ccu_nkmp_enable
000000004003b57a T ccu_sdm_helper_is_enabled
000000004003b5c2 T ccu_sdm_helper_enable
000000004003b694 T ccu_sdm_helper_disable
000000004003b73c T ccu_sdm_helper_has_rate
000000004003b76e T ccu_sdm_helper_read_rate
000000004003b7c4 T ccu_sdm_helper_get_factors
000000004003b80c t clk_factor_recalc_rate
000000004003b81e t clk_factor_round_rate
000000004003b872 t clk_factor_set_rate
000000004003b8b2 t clk_fixed_rate_recalc_rate
000000004003b8b6 t clk_fixed_rate_recalc_accuracy
000000004003b8ba T clk_hw_register_fixed_rate_with_accuracy
000000004003b932 T sunxi_fixed_clk_init
000000004003bada t _round_up_table
000000004003bb0a t _get_maxdiv
000000004003bb48 t _get_div
000000004003bb8a T divider_recalc_rate
000000004003bbba T divider_round_rate_parent
000000004003be00 T divider_get_val
000000004003beac W sunxi_dsp_init
000000004003beb0 T sunxi_ccu_init
000000004003bf84 T sunxi_r_ccu_init
000000004003bf94 T sunxi_rtc_ccu_init
000000004003c002 t _uart_getc
000000004003c032 t uart_set_format
000000004003c116 t uart_enable_irq
000000004003c17e t uart_pinctrl_init_by_sys_config
000000004003c206 t uart_pinctrl_uninit_by_sys_config
000000004003c28c t uart_irq_handler
000000004003c40a t uart_set_baudrate
000000004003c50c t uart_pinctrl_init
000000004003c574 T hal_uart_get_version
000000004003c580 T hal_uart_get_capabilities
000000004003c584 T hal_uart_init
000000004003c7f4 T hal_uart_deinit
000000004003c8ee T hal_uart_power_control
000000004003c8f2 T hal_uart_put_char
000000004003c918 T hal_uart_send
000000004003c99a T hal_uart_receive_polling
000000004003ca2a T hal_uart_receive
000000004003cad2 T hal_uart_transfer
000000004003cad6 T hal_uart_get_tx_count
000000004003cada T hal_uart_get_rx_count
000000004003cade T hal_uart_control
000000004003cb14 T hal_uart_get_status
000000004003cb18 T hal_uart_set_modem_control
000000004003cb1c T hal_uart_get_modem_status
000000004003cb20 t is_leap_year
000000004003cb42 t is_leap_year
000000004003cb64 t hal_rtc_alarmirq
000000004003cba0 t hal_rtc_wait.constprop.1
000000004003cbd8 t hal_rtc_setaie.isra.0.constprop.3
000000004003cc30 T hal_rtc_set_fel_flag
000000004003cc6a T hal_rtc_register_callback
000000004003cc7c T hal_rtc_gettime
000000004003cd9e T hal_rtc_settime
000000004003cedc T hal_rtc_getalarm
000000004003cfbe T hal_rtc_setalarm
000000004003d09a T hal_rtc_alarm_irq_enable
000000004003d0aa T hal_rtc_init
000000004003d1ee T rtc_month_days
000000004003d21e T mktime64
000000004003d296 T rtc_tm_to_time64
000000004003d2ac T rtc_time64_to_tm
000000004003d3c4 t sunxi_spi_can_dma
000000004003d3ee t sunxi_spi_dma_cb_tx
000000004003d3f0 t sunxi_spi_pinctrl_init
000000004003d55c t sunxi_spi_clk_exit
000000004003d592 t sunxi_spi_soft_reset.isra.0
000000004003d5c6 t sunxi_spi_disable_tp.isra.2
000000004003d5e4 t sunxi_spi_set_master.isra.4
000000004003d602 t sunxi_spi_enable_bus.isra.6
000000004003d620 t sunxi_spi_disable_bus.isra.7
000000004003d63c t sunxi_spi_hw_exit
000000004003d678 t sunxi_spi_start_xfer.isra.8
000000004003d698 t sunxi_spi_set_sample_mode.isra.9
000000004003d6fe t sunxi_spi_set_discard_burst.isra.11
000000004003d726 t sunxi_spi_bus_set_cs
000000004003d7b2 t sunxi_spi_enable_irq.isra.18
000000004003d7ce t sunxi_spi_disable_irq.isra.19
000000004003d7ee t sunxi_spi_reset_txfifo.isra.23
000000004003d822 t sunxi_spi_reset_rxfifo.isra.24
000000004003d852 t sunxi_spi_enable_dma_irq.isra.25
000000004003d872 t sunxi_spi_set_fifo_trig_level_rx.isra.27
000000004003d89a t sunxi_spi_set_fifo_trig_level_tx.isra.28
000000004003d8c8 t sunxi_spi_get_txfifo_cnt.isra.29
000000004003d8da t sunxi_spi_cpu_tx
000000004003d952 t sunxi_spi_set_bc_tc_stc.isra.33
000000004003d9d6 t sunxi_spi_enable_dual.isra.34
000000004003d9fa t sunxi_spi_disable_dual.isra.35
000000004003da20 t sunxi_spi_enable_quad.isra.36
000000004003da44 t sunxi_spi_disable_quad.isra.37
000000004003da6a t sunxi_spi_dma_rx
000000004003db7c t sunxi_spi_release_dma
000000004003dbbc t sunxi_spi_cpu_rx
000000004003dc18 t sunxi_spi_dma_cb_rx
000000004003dc74 t sunxi_spi_dma_tx
000000004003dd6c t sunxi_spi_set_delay_chain
000000004003de40 T sunxi_spi_bit_start_xfer
000000004003de60 T sunxi_spi_bit_qry_irq_pending
000000004003de74 T sunxi_spi_bit_clr_irq_pending
000000004003de94 T sunxi_spi_bit_enable_irq
000000004003deb8 T sunxi_spi_bit_disable_irq
000000004003dede t sunxi_spi_handler
000000004003e098 T sunxi_spi_bit_set_bc
000000004003e0d8 T sunxi_spi_bit_ss_owner
000000004003e106 T sunxi_spi_bit_ss_polarity
000000004003e12e t sunxi_spi_bit_set_cs
000000004003e1b2 T sunxi_spi_xfer_slave
000000004003e32c T hal_spi_init
000000004003e7c2 T hal_spi_deinit
000000004003e842 T hal_spi_xfer
000000004003f0b2 T hal_spi_write
000000004003f0fc T hal_spi_read
000000004003f148 T hal_spi_slave_abort
000000004003f178 T hal_watchdog_disable
000000004003f1ae T hal_watchdog_restart
000000004003f1ea T hal_watchdog_init
000000004003f1fc t pin_to_gpio_desc
000000004003f216 t gpio_pconf_reg
000000004003f2da t irq_to_gpio_desc
000000004003f30c t virq_to_gpio_desc
000000004003f346 t bad_gpio_irq_handle
000000004003f34a t gpio_conf_get
000000004003f3c4 t gpio_conf_set
000000004003f466 t gpio_irq_ack
000000004003f4a6 t gpio_irq_handle
000000004003f576 T hal_gpio_check_valid
000000004003f58a T hal_gpio_get_data
000000004003f5b8 T hal_gpio_set_data
000000004003f5c4 T hal_gpio_set_direction
000000004003f5ca T hal_gpio_set_pull
000000004003f5d6 T hal_gpio_get_pull
000000004003f604 T hal_gpio_set_driving_level
000000004003f610 T hal_gpio_get_driving_level
000000004003f63e T hal_gpio_pinmux_set_function
000000004003f644 T hal_gpio_sel_vol_mode
000000004003f706 T hal_gpio_to_irq
000000004003f748 T hal_gpio_set_debounce
000000004003f7e8 T hal_gpio_irq_request
000000004003f93e T hal_gpio_irq_free
000000004003f9be T hal_gpio_irq_enable
000000004003fa72 T hal_gpio_irq_disable
000000004003fb18 T hal_gpio_init
000000004003fc82 T gpio_get_platform_desc
000000004003fc8c T hal_spinor_get_version
000000004003fc90 T hal_spinor_get_capabilities
000000004003fc94 T hal_spinor_init
000000004003fc98 T hal_spinor_deinit
000000004003fc9c T hal_spinor_power_control
000000004003fca0 T hal_spinor_read_data
000000004003fca4 T hal_spinor_program_data
000000004003fca8 T hal_spinor_erase_sector
000000004003fcac T hal_spinor_erase_chip
000000004003fcc0 T hal_spinor_get_status
000000004003fcc4 T hal_spinor_get_info
000000004003fcf4 T hal_spinor_signal_event
000000004003fcf6 T hal_spinor_control
000000004003fcfa T hal_spinor_read_uuid
000000004003fcfe t cmd_bit
000000004003fd6c t nor_lock
000000004003fd7a t nor_unlock
000000004003fd88 T nor_register_factory
000000004003fda6 T nor_transfer
000000004003fe7c T nor_read_status
000000004003feae t nor_is_busy
000000004003fecc T nor_wait_ready
000000004003ff10 T nor_send_cmd
000000004003ff38 t nor_set_4byte
000000004003ff66 T nor_write_enable
000000004003ff8c t nor_erase_do
000000004003fff0 t nor_write_do
00000000400400a4 T nor_write_status
000000004004010c T nor_read_uuid
000000004004013c T nor_init
00000000400404f8 T nor_deinit
0000000040040550 T nor_erase
0000000040040742 T nor_read
0000000040040820 T nor_write
00000000400408c2 T get_nor_flash
00000000400408cc t nor_gd_quad_mode
0000000040040964 T nor_register_factory_gd
0000000040040970 t nor_mxic_quad_mode
00000000400409e0 t nor_mxic_blk_islock
0000000040040a1a t nor_mxic_islock
0000000040040a20 t nor_mxic_unlock
0000000040040a6c t nor_mxic_lock
0000000040040aba t nor_mxic_init_lock
0000000040040b46 T nor_register_factory_mxic
0000000040040b52 t nor_winbond_blk_islock
0000000040040b96 t nor_winbond_islock
0000000040040b9c t nor_winbond_unlock
0000000040040bf4 t nor_winbond_lock
0000000040040c4c t nor_winbond_set_wps
0000000040040cb4 t nor_winbond_deinit_lock
0000000040040cba t nor_winbond_init_lock
0000000040040cc0 t nor_winbond_init
0000000040040d1c T nor_register_factory_winbond
0000000040040d28 T esmt_read_uuid
0000000040040d5e T nor_register_factory_esmt
0000000040040d6a t nor_xtx_read_status1
0000000040040d9e t nor_xtx_quad_mode
0000000040040df6 t nor_xtx_set_wps
0000000040040e5e t nor_xtx_deinit_lock
0000000040040e64 t nor_xtx_init_lock
0000000040040e6a t nor_xtx_blk_islock
0000000040040eae t nor_xtx_islock
0000000040040eb4 t nor_xtx_unlock
0000000040040f0c t nor_xtx_lock
0000000040040f64 T nor_register_factory_xtx
0000000040040f70 t nor_fm_read_status1
0000000040040fa4 t nor_fm_quad_mode
0000000040040ffc t nor_fm_set_wps
0000000040041064 t nor_fm_deinit_lock
000000004004106a t nor_fm_init_lock
0000000040041070 t nor_fm_blk_islock
00000000400410b4 t nor_fm_islock
00000000400410ba t nor_fm_unlock
0000000040041112 t nor_fm_lock
000000004004116a T nor_register_factory_fm
0000000040041176 t nor_xmc_init
000000004004117a t nor_xmc_deinit_lock
000000004004117c t nor_xmc_lock
0000000040041180 t nor_xmc_islock
0000000040041184 t nor_xmc_unlock
0000000040041188 t nor_xmc_init_lock
000000004004118c T nor_xmc_read_status2
00000000400411c0 T nor_xmc_write_status2
000000004004122a t nor_xmc_quad_mode
000000004004126a T nor_register_factory_xmc
0000000040041276 t nor_puya_quad_mode
0000000040041312 T nor_register_factory_puya
000000004004131e t nor_boya_quad_mode
00000000400413ba T nor_register_factory_boya
00000000400413c6 t nor_zetta_quad_mode
000000004004145e T nor_register_factory_zetta
000000004004146a T nor_wr_lock_init
0000000040041486 T nor_wr_lock_deinit
00000000400414a0 T nor_wr_unlock
000000004004152e T nor_wr_unlock_all
000000004004154a T nor_wr_lock_all
0000000040041566 T hal_timer_init
000000004004156a T hal_timer_uninit
000000004004156e T hal_timer_set_oneshot
0000000040041586 t sunxi_timer_irq_handle
00000000400415b4 T sunxi_timer_stop
00000000400415f6 T sunxi_timer_start
000000004004162c T sunxi_timer_set_oneshot
00000000400416d4 T sunxi_timer_init
00000000400417cc T sunxi_timer_uninit
0000000040041812 T hal_avs_continue
000000004004185c T hal_avs_pause
00000000400418a0 T hal_avs_disable
00000000400418e8 T hal_avs_enable
000000004004192a T hal_avs_get_counter
000000004004195c T hal_avs_set_counter
000000004004198c T hal_avs_set_cnt_div
00000000400419e2 T hal_avs_init
0000000040041a2a T hal_avs_uninit
0000000040041a68 T hal_avs_control
0000000040041aaa T print_hex_dump_words
0000000040041b1e T print_hex_dump_bytes
0000000040041b7c T OS_MutexCreate
0000000040041bde T OS_MutexDelete
0000000040041c3a T OS_MutexLock
0000000040041c9c T OS_MutexUnlock
0000000040041cd4 T OS_TimerCreate
0000000040041d58 T OS_TimerDelete
0000000040041db4 T OS_TimerChangePeriod
0000000040041e34 T OS_SemaphoreCreate
0000000040041e76 T OS_SemaphoreCreateBinary
0000000040041ea6 T OS_SemaphoreDelete
0000000040041f02 T OS_SemaphoreWait
0000000040041f64 T OS_SemaphoreRelease
0000000040041f9c T OS_SemaphoreReset
0000000040041fd8 t mmc_power_up
000000004004200a t mmc_power_off
0000000040042036 T mmc_wait_for_req
0000000040042040 T mmc_wait_for_cmd
000000004004205c T mmc_power_cycle
000000004004207a T mmc_select_voltage
00000000400420d4 T mmc_host_set_uhs_voltage
0000000040042110 T mmc_set_uhs_voltage
000000004004218c T mmc_go_idle
00000000400421c0 T mmc_send_status
0000000040042200 T mmc_sd_switch
0000000040042286 T mmc_app_cmd
0000000040042304 T mmc_wait_for_app_cmd
0000000040042384 T mmc_app_set_bus_width
00000000400423cc T __sdmmc_block_rw
0000000040042598 T mmc_add_card
000000004004284e T mmc_set_blocklen
000000004004288e T mmc_block_read
000000004004295c T mmc_block_write
0000000040042a2a T mmc_send_relative_addr
0000000040042a6c T mmc_send_if_cond
0000000040042ab4 T mmc_select_card
0000000040042afc T mmc_all_send_cid
0000000040042b5a T mmc_rescan
0000000040042c92 T mmc_card_deinit
0000000040042cf6 T mmc_card_create
0000000040042e0a T mmc_card_delete
0000000040042ecc T mmc_card_open
0000000040042f4c T mmc_card_close
0000000040042fd4 T sdmmc_pinctl_set_from_cfg
000000004004314a T sdmmc_pinctrl_init
00000000400433a4 T mmc_gpiod_request_cd_irq
00000000400434b4 T mmc_gpiod_init_pg_power_pin
00000000400435be T mmc_gpiod_set_pg_power
00000000400436ae t __mci_sel_access_mode
00000000400436cc t __mci_clk_prepare_enable
00000000400437f6 t __mci_enable_sdio_irq
0000000040043852 t __mci_irq_handler
0000000040043bfc T SDC0_IRQHandler
0000000040043c02 T SDC2_IRQHandler
0000000040043c08 T SDC1_IRQHandler
0000000040043c0e t __mci_force_dump_host_info
0000000040043d14 t __mci_program_clk
0000000040043e34 t __mci_clk_disable_unprepare
0000000040043e9e t __mci_clk_enable_MClock
0000000040043f1c t __mci_clk_disable_MClock
0000000040043f9a t __mci_update_clock
0000000040044390 t sunxi_mmc_set_dly_raw
0000000040044480 t __mci_debounce_onoff
00000000400444c8 t __mci_restore_io
00000000400444ea t __mci_hold_io
000000004004450c t __mci_cd_irq
0000000040044558 t __mci_voltage_stable_det
00000000400445a4 t __mci_dat3_det
00000000400445cc t __mci_cd_timer
0000000040044658 T __mci_wait_access_done
00000000400446c2 T HAL_SDC_Update_Clk
00000000400447ae T HAL_SDC_Clk_PWR_Opt
00000000400448d0 T HAL_SDC_set_timing
0000000040044a28 T HAL_SDC_Is_Busy
0000000040044af0 T HAL_SDC_signal_voltage_switch
0000000040044b24 T __mci_check_bit_clear
0000000040044c14 T HAL_SDC_Set_BusWidth
0000000040044c6e T do_rom_HAL_SDC_Request
000000004004614c T HAL_SDC_Request
0000000040046386 T HAL_SDC_Claim_Host
00000000400463a6 T HAL_SDC_Release_Host
00000000400463ae T HAL_SDC_PowerOn
0000000040046556 T HAL_SDC_PowerOff
000000004004665e T sdc_is_1bit_card
00000000400466ea T hal_sdc_init
0000000040046c0a T hal_sdc_deinit
0000000040046de0 T hal_sdc_create
0000000040046efc T hal_sdc_open
0000000040046f46 T hal_sdc_close
0000000040046f90 t mmc_send_app_op_cond
0000000040047050 T mmc_app_sd_status
000000004004711c T mmc_app_send_scr
00000000400471a8 T mmc_decode_cid
000000004004722a T mmc_sd_init_uhs_card
0000000040047420 T mmc_sd_switch_hs
00000000400474ca T mmc_send_csd
0000000040047538 T mmc_sd_get_csd
000000004004769a T mmc_sd_setup_card
0000000040047928 T mmc_sd_get_max_clock
0000000040047936 T mmc_attach_sd
0000000040047cb4 T mmc_deattach_sd
0000000040047cda t cistpl_manfid
0000000040047cfa t cistpl_funce_common
0000000040047d40 t cistpl_funce_func
0000000040047d62 t cis_tpl_parse
0000000040047dfa t cistpl_funce
0000000040047e1e t cistpl_vers_1
0000000040047eda t mmc_send_io_op_cond.isra.3
0000000040047f3c T sdio_free_common_cis
0000000040047f64 T sdio_free_func_cis
0000000040047f94 T mmc_io_rw_direct_host
000000004004815e T mmc_io_rw_direct
000000004004818c t sdio_read_cis
000000004004834e T sdio_read_common_cis
0000000040048354 T sdio_read_func_cis
0000000040048386 T mmc_sdio_get_max_clock
000000004004839c T sdio_alloc_func
00000000400483cc T sdio_remove_func
0000000040048408 T mmc_sdio_remove
00000000400484bc T mmc_sdio_init_card
00000000400488e4 T sdio_reset
0000000040048924 T mmc_attach_sdio
0000000040048b72 T mmc_deattach_sdio
0000000040048b94 T card_detect
0000000040048d1a T mmc_test_init
0000000040048df8 T mmc_test_exit
0000000040048eae T mmc_test
00000000400492bc T cmd_sd_test_exec
00000000400492e8 T cmd_sdmmc_sample
0000000040049334 t convert_burst
0000000040049386 t sunxi_dma_irq_handle
000000004004946a t sunxi_lli_list
00000000400494b0 t sunxi_cfg_lli
0000000040049528 T sunxi_dma_free_ill
0000000040049588 T hal_dma_chan_request
0000000040049604 T hal_dma_prep_memcpy
00000000400496c0 T hal_dma_prep_device
00000000400497ea T hal_dma_prep_cyclic
0000000040049a44 T hal_dma_callback_install
0000000040049ac6 T hal_dma_slave_config
0000000040049b38 T hal_dma_tx_status
0000000040049c4c T hal_dma_start
0000000040049d82 T hal_dma_stop
0000000040049e3a T hal_dma_chan_free
0000000040049f1a T hal_dma_chan_desc_free
0000000040049f2a T hal_dma_resource_get
000000004004a0aa T hal_dma_init
000000004004a0ae t __fls
000000004004a0d0 t __fls
000000004004a10e t soc_pcm_close
000000004004a166 t soc_pcm_pointer
000000004004a17c t soc_pcm_ioctl
000000004004a196 t snd_platform_unregister
000000004004a1ca t soc_pcm_trigger
000000004004a27c t soc_pcm_prepare
000000004004a352 t soc_pcm_hw_free
000000004004a3f6 t soc_pcm_open
000000004004a5e8 t snd_ctl_kcontrol_user_get
000000004004a64e t snd_ctl_enum_set
000000004004a702 t snd_ctl_kcontrol_set
000000004004a736 t snd_pcm_free_stream
000000004004a75c t soc_dai_set_clkdiv.constprop.7
000000004004a778 t soc_dai_set_sysclk.constprop.9
000000004004a796 t snd_ctl_kcontrol_user_set
000000004004a81e t soc_pcm_hw_params
000000004004aace T snd_mutex_init
000000004004aad2 T snd_mutex_lock
000000004004aae0 T snd_mutex_unlock
000000004004aae8 T snd_mutex_destroy
000000004004aaf0 T snd_schd_init
000000004004ab3a T snd_schd_timeout
000000004004ab70 T snd_schd_wakeup
000000004004ab7e T snd_schd_destroy
000000004004ab9c T snd_pcm_stream_lock_irq
000000004004abd6 T snd_pcm_stream_unlock_irq
000000004004abf8 T _snd_pcm_stream_lock_irqsave
000000004004ac30 T snd_pcm_stream_unlock_irqrestore
000000004004ac5a T snd_set_runtime_hwparams
000000004004ac84 T snd_soc_dai_set_fmt
000000004004ac9c T snd_pcm_new_stream
000000004004ad40 T snd_kcontrol_to_snd_ctl_info
000000004004ad94 t snd_ctl_enum_get
000000004004ae20 t snd_ctl_kcontrol_get
000000004004ae8a T snd_ctl_add_elem
000000004004afa4 t add_controls
000000004004b0aa T snd_card_find_pcm
000000004004b0ca T snd_card_find_by_name
000000004004b12e T snd_card_find_by_platform_type
000000004004b158 T snd_card_find_by_num
000000004004b17e T snd_card_get_number
000000004004b196 T snd_card_register
000000004004b624 T snd_card_list
000000004004b650 t dmaengine_pcm_dma_complete
000000004004b68e T snd_dmaengine_pcm_get_chan
000000004004b696 T snd_hwparams_to_dma_slave_config
000000004004b6fe T snd_dmaengine_pcm_open_request_chan
000000004004b760 T snd_dmaengine_pcm_close_release_chan
000000004004b7b8 T snd_dmaengine_pcm_pointer
000000004004b81a T snd_dmaengine_pcm_trigger
000000004004b972 t snd_pcm_capture_avail
000000004004b98a t snd_pcm_playback_avail
000000004004b9ae t snd_pcm_running
000000004004b9d2 t do_align_dcache_control
000000004004b9fe t snd_pcm_hw_constraint_minmax
000000004004ba20 t snd_pcm_release_substream
000000004004bad4 t snd_pcm_set_state
000000004004baf8 t wait_for_avail
000000004004bc16 t snd_mask_single.isra.9
000000004004bc3e t snd_range_setinteger
000000004004bc62 t snd_pcm_hw_param_value.isra.17
000000004004bcf4 t snd_pcm_stop
000000004004bd6a t snd_pcm_update_state
000000004004be18 t snd_pcm_hw_rule_add.constprop.31
000000004004bf16 t snd_pcm_playback_silence.isra.18
000000004004c064 t snd_pcm_do_start
000000004004c120 t snd_pcm_update_hw_ptr0.isra.20
000000004004c206 t snd_pcm_do_reset.isra.21
000000004004c25e t snd_pcm_paused
000000004004c2ee T snd_pcm_period_elapsed
000000004004c34e T ksnd_card_index
000000004004c364 T ksnd_card_name
000000004004c376 T ksnd_card_num
000000004004c37a T ksnd_card_info
000000004004c426 T ksnd_pcm_stream_info
000000004004c5cc T ksnd_pcm_open
000000004004cc26 T ksnd_pcm_release
000000004004cc9e T ksnd_pcm_hw_refine
000000004004ce66 T ksnd_pcm_hw_params
000000004004d0fe T ksnd_pcm_sw_params
000000004004d1f0 T ksnd_pcm_prepare
000000004004d272 T ksnd_pcm_reset
000000004004d312 T ksnd_pcm_hw_free
000000004004d37c T ksnd_pcm_writei
000000004004d5b8 T ksnd_pcm_readi
000000004004d826 T ksnd_pcm_start
000000004004d850 T ksnd_pcm_drop
000000004004d8c4 T ksnd_pcm_pause
000000004004d8f0 T ksnd_pcm_drain
000000004004da48 T ksnd_pcm_sync_ptr
000000004004db6e T ksnd_pcm_delay
000000004004dc0c T ksnd_pcm_channel_info
000000004004dccc T ksnd_pcm_hw_mmap_dcache_update
000000004004dcfa T ksnd_pcm_wait
000000004004dda8 T ksnd_pcm_rewind
000000004004de68 T ksnd_pcm_dsleep_init
000000004004dece T ksnd_pcm_dsleep_release
000000004004df20 T snd_pcm_format_physical_width
000000004004df42 T snd_pcm_rate_mask_intersect
000000004004df98 T snd_pcm_limit_hw_rates
000000004004dff0 T snd_pcm_format_set_silence
000000004004e0e6 T snd_codec_read
000000004004e0f8 T snd_codec_write
000000004004e10e T snd_codec_update_bits
000000004004e168 T snd_platform_read
000000004004e170 T snd_platform_write
000000004004e17a T snd_platform_update_bits
000000004004e194 T ksnd_ctl_num
000000004004e216 T ksnd_ctl_get
000000004004e2cc T ksnd_ctl_get_bynum
000000004004e362 T ksnd_ctl_set
000000004004e43c T ksnd_ctl_set_bynum
000000004004e4f6 T ksnd_ctl_add_elem
000000004004e568 t muldiv32
000000004004e59a t snd_range_checkempty
000000004004e5ba t snd_range_single
000000004004e5e8 t snd_range_single
000000004004e616 T snd_range_refine
000000004004e6be T snd_range_refine_first
000000004004e6f2 T snd_range_refine_last
000000004004e726 T snd_range_refine_min
000000004004e77e T snd_range_refine_max
000000004004e7d6 T snd_range_refine_set
000000004004e7f0 T snd_range_mul
000000004004e870 T snd_range_div
000000004004e8dc T snd_range_muldiv
000000004004e976 T snd_range_muldivk
000000004004e9fc T snd_range_mulkdiv
000000004004ea8e T snd_range_list
000000004004eb12 T snd_pcm_hw_rule_mul
000000004004eb52 T snd_pcm_hw_rule_div
000000004004eb92 T snd_pcm_hw_rule_muldivk
000000004004ebd2 T snd_pcm_hw_rule_mulkdiv
000000004004ec12 T snd_pcm_hw_rule_format
000000004004ec9e T snd_pcm_hw_rule_sample_bits
000000004004ed0e T snd_pcm_hw_rule_rate
000000004004ed2a T snd_pcm_hw_rule_buffer_bytes_max
000000004004ed54 t sunxi_pb_audio_route_set_data
000000004004ed6a t sunxi_codec_startup
000000004004ed7a t sunxi_codec_shutdown
000000004004ed7c t sunxi_pb_audio_route_get_data
000000004004ed94 t sunxi_set_data_invert
000000004004edd0 t sunxi_codec_trigger
000000004004ee30 t sunxi_get_data_invert
000000004004ee82 t sunxi_set_rx_sync_mode
000000004004ef3a t sunxi_set_rx_sync_mode
000000004004efee t sunxi_get_rx_sync_mode
000000004004f048 t sunxi_get_rx_sync_mode
000000004004f0a2 t sunxi_codec_set_sysclk
000000004004f100 t sunxi_jack_irq_handler
000000004004f124 t sunxi_codec_prepare
000000004004f17a t sunxi_codec_playback_lineout_route
000000004004f2b0 t sunxi_codec_playback_hp_route
000000004004f444 t sunxi_codec_dapm_control
000000004004f6e4 t sunxi_codec_hw_params
000000004004f9dc t sunxi_jack_detect
000000004004fa24 t sun8iw20_codec_probe
0000000040050612 t sun8iw20_codec_remove
000000004005071c t sunxi_cpudai_startup
0000000040050732 t sunxi_cpudai_shutdown
0000000040050734 t sunxi_cpudai_probe
0000000040050744 t sunxi_cpudai_platform_probe_adc
0000000040050768 t sunxi_cpudai_platform_probe_dac
000000004005078c t sunxi_cpudai_platform_remove
00000000400507a8 t sunxi_cpudai_platform_probe
000000004005085e T snd_platform_cpudai_register
000000004005092a t sunxi_pcm_hw_free
000000004005093c t sunxi_pcm_preallocate_dma_buffer
000000004005098e t sunxi_pcm_close
0000000040050992 t sunxi_pcm_open
0000000040050a1a t sunxi_pcm_trigger
0000000040050a48 t sunxi_pcm_hw_params
0000000040050b48 T sunxi_pcm_new
0000000040050bc6 T sunxi_pcm_free
0000000040050bfa t sunxi_daudio_dai_probe
0000000040050dca t sunxi_daudio_set_fmt
0000000040050f00 t sunxi_daudio_set_clkdiv
0000000040050ff6 t sunxi_daudio_get_asrc_function
0000000040051066 t sunxi_get_tx_hub_mode
00000000400510d4 t snd_sunxi_daudio_clk_exit
000000004005112a t sunxi_daudio_hw_params
00000000400513a0 t sunxi_daudio_set_asrc_function
0000000040051436 t sunxi_set_tx_hub_mode
00000000400514ce t sunxi_daudio_global_enable
0000000040051508 t sunxi_daudio_prepare
0000000040051590 t sunxi_daudio_gpio_init.isra.10
00000000400516ae t sunxi_daudio_platform_remove
00000000400516e2 t sunxi_daudio_set_sysclk
0000000040051764 t snd_sunxi_pa_pin_disable.isra.13
00000000400517bc t sunxi_daudio_platform_probe
0000000040051e8e t sunxi_daudio_shutdown
0000000040051edc t sunxi_daudio_startup
0000000040051f48 t sunxi_daudio_trigger
0000000040052030 T snd_platform_daudio_register
0000000040052154 t __snd_pcm_avail_update
0000000040052160 t __snd_pcm_state
000000004005216c t __snd_pcm_hwsync
0000000040052178 t snd_pcm_mmap_avail
00000000400521aa t snd_pcm_link_ptr
0000000040052236 t snd_pcm_check_error
0000000040052270 t snd_pcm_channel_area_addr
00000000400522ac t snd_pcm_channel_area_addr
00000000400522e8 t snd_pcm_channel_area_addr
0000000040052324 t snd_pcm_channel_area_addr
0000000040052360 t snd_pcm_channel_area_addr
000000004005239c T snd_pcm_hw_params_sizeof
00000000400523a2 T snd_pcm_sw_params_sizeof
00000000400523a8 T snd_pcm_link_hw_ptr
00000000400523f8 T snd_pcm_link_appl_ptr
0000000040052448 T snd_pcm_unlink_hw_ptr
00000000400524be T snd_pcm_unlink_appl_ptr
0000000040052534 T snd_pcm_set_hw_ptr
000000004005257a T snd_pcm_set_appl_ptr
00000000400525c0 T snd_pcm_open_config
000000004005260c T snd_pcm_open
000000004005267a T snd_pcm_hw_params_any
00000000400526d6 T snd_pcm_hw_params_set_format
00000000400526e2 T snd_pcm_hw_params_set_rate
00000000400526ee T snd_pcm_hw_params_set_period_size
00000000400526fc T snd_pcm_hw_params_set_period_size_near
0000000040052724 T snd_pcm_hw_params_set_period_time
0000000040052730 T snd_pcm_hw_params_set_buffer_size
000000004005273e T snd_pcm_hw_params_set_buffer_size_near
0000000040052766 T snd_pcm_hw_params_set_buffer_time
0000000040052772 T snd_pcm_hw_params_set_channels
000000004005277e T snd_pcm_hw_params_set_periods
000000004005278a T snd_pcm_hw_params_set_access
0000000040052796 T snd_pcm_hw_params_get_access
00000000400527ba T snd_pcm_hw_params_get_format
00000000400527de T snd_pcm_hw_params_get_channels
00000000400527e8 T snd_pcm_hw_params_get_rate
00000000400527f2 T snd_pcm_hw_params_get_period_time
00000000400527fc T snd_pcm_hw_params_get_period_size
0000000040052820 T snd_pcm_hw_params_get_buffer_size
0000000040052844 T snd_pcm_hw_free
00000000400528b4 T snd_pcm_sw_params_current
00000000400528f4 T snd_pcm_sw_params_set_start_threshold
0000000040052922 T snd_pcm_sw_params_set_stop_threshold
0000000040052950 T snd_pcm_sw_params_set_silence_size
000000004005297e T snd_pcm_sw_params_set_avail_min
00000000400529ac T snd_pcm_sw_params_get_boundary
00000000400529dc T snd_pcm_sw_params
0000000040052ab4 T snd_pcm_prepare
0000000040052b24 T snd_pcm_hw_params
0000000040052b6c T snd_pcm_reset
0000000040052bfc T snd_pcm_start
0000000040052c6c T snd_pcm_drop
0000000040052cdc T snd_pcm_drain
0000000040052d2c T snd_pcm_pause
0000000040052da0 T snd_pcm_state
0000000040052e10 T snd_pcm_hwsync
0000000040052e80 T snd_pcm_stream
0000000040052eac T snd_pcm_writei
0000000040052efc T snd_pcm_readi
0000000040052f4c T snd_pcm_resume
0000000040052f92 T snd_pcm_recover
000000004005309c T snd_pcm_delay
000000004005310c T snd_pcm_dump_hw_setup
0000000040053212 T snd_pcm_dump_sw_setup
00000000400532ac T snd_pcm_dump_setup
00000000400532c8 T snd_pcm_dump
0000000040053312 T snd_pcm_format_size
000000004005332e T snd_pcm_bytes_to_frames
0000000040053360 T snd_pcm_frames_to_bytes
0000000040053396 T snd_pcm_hw_params_can_pause
00000000400533c6 T snd_pcm_format_name
00000000400533de T snd_pcm_new
0000000040053446 T snd_pcm_free
00000000400534a0 T snd_pcm_close
0000000040053534 T snd_pcm_areas_from_buf
000000004005357e T snd_pcm_format_silence_64
00000000400535ba T snd_pcm_area_silence
00000000400536f4 T snd_pcm_areas_silence
00000000400537c2 T snd_pcm_area_copy
0000000040053a1a T snd_pcm_areas_copy
0000000040053bac T snd_pcm_avail_update
0000000040053bda T __snd_pcm_mmap_begin
0000000040053c7a T snd_pcm_mmap_begin
0000000040053cb4 T __snd_pcm_mmap_commit
0000000040053d5e T snd_pcm_mmap_commit
0000000040053d94 T snd_pcm_type
0000000040053dc0 T snd_pcm_wait_nocheck
0000000040053e34 T snd_pcm_write_areas
0000000040053fbe T __snd_pcm_wait_in_lock
0000000040054022 T snd_pcm_read_areas
0000000040054130 T snd_pcm_wait
0000000040054162 T snd_pcm_query_chmaps
0000000040054172 T snd_pcm_get_chmap
0000000040054182 T snd_pcm_set_chmap
00000000400541d2 T _snd_pcm_make_single_query_chmaps
0000000040054226 T snd_pcm_rewind
00000000400542a8 T snd_pcm_link
0000000040054300 T snd_pcm_unlink
0000000040054338 T snd_ctl_open
0000000040054398 T snd_ctl_num
00000000400543b0 T snd_ctl_get_bynum
00000000400543e4 T snd_ctl_get
0000000040054410 T snd_ctl_set_bynum
0000000040054414 T snd_ctl_set
0000000040054418 T snd_ctl_add
000000004005441c T snd_pcm_config_get_config
000000004005446e T snd_pcm_config_get_open_func
00000000400544b8 t snd_pcm_hw_mmap
00000000400544bc t snd_pcm_hw_may_wait_for_avail_min
00000000400544c0 t snd_pcm_hw_wait
00000000400544cc t snd_pcm_hw_cache_update
00000000400544d4 t snd_pcm_hw_dump
000000004005453e t snd_pcm_hw_close
0000000040054570 t snd_pcm_hw_resume
00000000400545b6 t snd_pcm_hw_readi
0000000040054602 t snd_pcm_hw_writei
000000004005464e t snd_pcm_hw_delay
0000000040054698 t snd_pcm_hw_pause
00000000400546c6 t snd_pcm_hw_drain
00000000400546f4 t snd_pcm_hw_drop
0000000040054722 t snd_pcm_hw_start
0000000040054750 t snd_pcm_hw_reset
000000004005477e t snd_pcm_hw_prepare
00000000400547ac t snd_pcm_hw_channel_info
0000000040054804 t snd_pcm_hw_sw_params
000000004005484a t snd_pcm_hw_hw_free
0000000040054888 t snd_pcm_hw_hw_params
00000000400548be t snd_pcm_hw_hw_refine
00000000400548ec t snd_pcm_sync_ptr.isra.15
00000000400548fc t snd_pcm_hw_mmap_commit
0000000040054930 t snd_pcm_hw_avail_update
000000004005499a t snd_pcm_hw_hwsync
00000000400549b8 t snd_pcm_hw_state
00000000400549de t snd_pcm_hw_rewind
0000000040054a38 t snd_pcm_hw_munmap
0000000040054a3c T snd_pcm_hw_wait_with_index
0000000040054a44 T snd_pcm_hw_poll_index_init
0000000040054a4c T snd_pcm_hw_poll_index_release
0000000040054a54 T _snd_pcm_hw_open
0000000040054b86 t snd_pcm_mmap_areas
0000000040054bb2 t snd_pcm_mmap_areas
0000000040054bde t snd_pcm_mmap_areas
0000000040054c0a t snd_pcm_mmap_areas
0000000040054c36 t snd_pcm_mmap_areas
0000000040054c62 t hw_param_range_refine_one
0000000040054ccc t snd_pcm_direct_semaphore_up.constprop.13
0000000040054d08 t snd_pcm_direct_semaphore_down.constprop.14
0000000040054d44 T snd_pcm_slave_conf_hw_params
0000000040054d9a t snd_pcm_direct_ipcs_lock_init
0000000040054dd2 t snd_pcm_direct_ipcs_lock_destroy
0000000040054dde T snd_pcm_direct_semaphore_shm_discard
0000000040054e26 T snd_pcm_direct_semaphore_shm_create_or_connect
0000000040054f9a T snd_pcm_direct_last_pcm_drop
0000000040054ffe T snd_pcm_direct_initialize_slave
00000000400554a4 T snd_pcm_direct_initialize_secondary_slave
000000004005556e T snd_pcm_direct_slave_recover
0000000040055684 T snd_pcm_direct_client_chk_xrun
00000000400556ba T snd_pcm_direct_check_interleave
000000004005577a T snd_pcm_direct_hw_refine
0000000040055a56 T snd_pcm_direct_prepare
0000000040055ac0 T snd_pcm_direct_hw_params
0000000040055ac8 T snd_pcm_direct_hw_free
0000000040055acc T snd_pcm_direct_sw_params
0000000040055ad0 T snd_pcm_direct_channel_info
0000000040055ad6 T snd_pcm_direct_mmap
0000000040055ada T snd_pcm_direct_munmap
0000000040055ade T snd_pcm_direct_wait
0000000040055aea T snd_pcm_direct_initialize_poll_index
0000000040055b24 T snd_pcm_direct_destroy_poll_index
0000000040055b34 t snd_pcm_dsnoop_reset
0000000040055b4a t snd_pcm_dsnoop_pause
0000000040055b4e t snd_pcm_dsnoop_writei
0000000040055b52 t snd_pcm_dsnoop_state
0000000040055b94 t snd_pcm_dsnoop_drop
0000000040055bac t snd_pcm_dsnoop_dump
0000000040055bf4 t snd_pcm_dsnoop_close
0000000040055c58 t snd_pcm_dsnoop_sync_ptr
0000000040055e46 t snd_pcm_dsnoop_avail_update
0000000040055e8c t snd_pcm_dsnoop_hwsync
0000000040055ec4 t snd_pcm_dsnoop_mmap_commit
0000000040055f24 t snd_pcm_dsnoop_drain
0000000040055f9c t snd_pcm_dsnoop_start
0000000040055fe8 t snd_pcm_dsnoop_delay
0000000040056058 T _snd_pcm_dsnoop_open
000000004005636c t snd_pcm_mmap_playback_avail
000000004005638e t snd_pcm_mmap_playback_avail
00000000400563b0 t snd_pcm_mmap_playback_avail
00000000400563d2 t snd_pcm_mmap_playback_avail
00000000400563f4 t generic_mix_areas_16_native
000000004005644c t generic_remix_areas_16_native
00000000400564ae t generic_mix_areas_32_native
000000004005650c t generic_remix_areas_32_native
0000000040056572 t generic_mix_areas_24
0000000040056604 t generic_remix_areas_24
00000000400566a0 t generic_mix_areas_u8
0000000040056700 t generic_remix_areas_u8
0000000040056766 t reset_slave_ptr
000000004005678e t snd_pcm_dmix_reset
00000000400567ae t snd_pcm_dmix_pause
00000000400567b2 t snd_pcm_dmix_readi
00000000400567b6 t snd_pcm_dmix_may_wait_for_avail_min
00000000400567ba t snd_pcm_dmix_state
000000004005681e t snd_pcm_dmix_sync_ptr
0000000040056920 t snd_pcm_dmix_avail_update
000000004005696a t snd_pcm_dmix_hwsync
00000000400569b0 t snd_pcm_dmix_drop
00000000400569c8 t snd_pcm_dmix_dump
0000000040056a10 t snd_pcm_direct_semaphore_up.constprop.1
0000000040056a4c t snd_pcm_direct_semaphore_down.constprop.2
0000000040056a88 t snd_pcm_dmix_sync_area
0000000040056d14 t snd_pcm_dmix_mmap_commit
0000000040056dca t snd_pcm_dmix_close
0000000040056eac t snd_pcm_dmix_delay
0000000040056f1e t snd_pcm_dmix_start
0000000040056f82 t snd_pcm_dmix_drain
000000004005706a t shm_sum_ipcs_lock_init
00000000400570a2 t shm_sum_ipcs_lock_destroy
00000000400570ae T _snd_pcm_dmix_open
0000000040057524 T _snd_pcm_asym_open
000000004005757c t snd_pcm_mmap_write_areas
0000000040057614 t snd_pcm_mmap_read_areas
00000000400576be T snd_pcm_mmap_appl_forward
00000000400576d0 T snd_pcm_mmap_hw_forward
00000000400576e2 T snd_pcm_mmap_writei
000000004005772e T snd_pcm_mmap_readi
000000004005777a T snd_pcm_channel_info_shm
00000000400577da T snd_pcm_mmap
0000000040057a42 T snd_pcm_munmap
0000000040057b60 T snd_pcm_generic_close
0000000040057b88 T snd_pcm_generic_hw_free
0000000040057b90 T snd_pcm_generic_sw_params
0000000040057b98 T snd_pcm_generic_hw_refine
0000000040057ba0 T snd_pcm_generic_hw_params
0000000040057ba8 T snd_pcm_generic_channel_info
0000000040057bc0 T snd_pcm_generic_mmap
0000000040057bde T snd_pcm_generic_munmap
0000000040057bf4 T snd_pcm_generic_query_chmaps
0000000040057bfc T snd_pcm_generic_get_chmap
0000000040057c04 T snd_pcm_generic_set_chmap
0000000040057c0c T snd_pcm_generic_state
0000000040057c14 T snd_pcm_generic_hwsync
0000000040057c1c T snd_pcm_generic_start
0000000040057c24 T snd_pcm_generic_drop
0000000040057c2c T snd_pcm_generic_drain
0000000040057c34 T snd_pcm_generic_pause
0000000040057c3c T snd_pcm_generic_resume
0000000040057c44 T snd_pcm_generic_may_wait_for_avail_min
0000000040057c90 t snd_pcm_mmap_capture_avail
0000000040057ca6 t snd_pcm_mmap_capture_avail
0000000040057cbc t snd_pcm_plugin_undo_read
0000000040057cc0 T snd_pcm_plugin_may_wait_for_avail_min
0000000040057d62 T snd_pcm_plugin_wait
0000000040057d6a t snd_pcm_plugin_read_areas
0000000040057e58 t snd_pcm_plugin_write_areas
0000000040057f48 t snd_pcm_plugin_mmap_commit
00000000400580ac t snd_pcm_plugin_avail_update
0000000040058208 t snd_pcm_plugin_readi
0000000040058254 t snd_pcm_plugin_writei
00000000400582a0 t snd_pcm_plugin_delay
00000000400582ee t snd_pcm_plugin_reset
0000000040058330 t snd_pcm_plugin_prepare
0000000040058372 t snd_pcm_plugin_undo_write
0000000040058376 T snd_pcm_plugin_undo_read_generic
000000004005837a T snd_pcm_plugin_undo_write_generic
000000004005837e T snd_pcm_plugin_init
00000000400583a6 t sx24
00000000400583be t sx24
00000000400583d6 t sx24s
00000000400583ea t sx24s
00000000400583fe t snd_pcm_route_get_chmap
00000000400584b8 t snd_pcm_route_query_chmaps
00000000400584e2 t snd_pcm_route_dump
0000000040058624 t snd_pcm_route_hw_params
0000000040058708 t snd_pcm_route_hw_refine_schange
0000000040058750 t snd_pcm_route_hw_refine_cchange
0000000040058794 t snd_pcm_route_hw_refine_sprepare
00000000400587ee t snd_pcm_route_hw_refine
000000004005881a t snd_pcm_route_close
000000004005886a t route_chmap_init
00000000400588e0 t snd_pcm_route_convert1_zero
00000000400588ec t snd_pcm_route_convert
0000000040058976 t snd_pcm_route_write_areas
00000000400589b8 t snd_pcm_route_read_areas
00000000400589fa t snd_pcm_channel_area_step.isra.1
0000000040058a2a t snd_pcm_route_convert1_one_getput
0000000040058e9a t snd_pcm_route_convert1_one
0000000040059318 t snd_pcm_route_convert1_many
0000000040059a4c t snd_pcm_route_hw_refine_cprepare
0000000040059aae t snd_pcm_route_load_ttable.part.5
0000000040059b78 T snd_pcm_route_determine_ttable
0000000040059c02 T snd_pcm_route_load_ttable
0000000040059c06 T snd_pcm_route_open
0000000040059e6c T _snd_pcm_route_open
0000000040059fd4 t snd_pcm_softvol_dump
000000004005a06e t check_access_mask
000000004005a0ac t snd_pcm_softvol_hw_refine_sprepare
000000004005a0f6 t snd_pcm_softvol_hw_refine
000000004005a122 t softvol_free
000000004005a154 t snd_pcm_softvol_close
000000004005a166 t MULTI_DIV_int.isra.3
000000004005a1ae t MULTI_DIV_short.isra.4
000000004005a1ea t snd_pcm_softvol_hw_params
000000004005a268 t snd_pcm_softvol_hw_refine_schange
000000004005a2b8 t snd_pcm_softvol_hw_refine_cchange
000000004005a308 t snd_pcm_softvol_hw_refine_cprepare
000000004005a380 t get_current_volume.part.9
000000004005a39c t get_current_volume
000000004005a3d4 t snd_pcm_channel_area_step.isra.11
000000004005a404 t softvol_convert_mono_vol
000000004005a644 t softvol_convert_stereo_vol
000000004005a938 t snd_pcm_softvol_write_areas
000000004005a9a8 t snd_pcm_softvol_read_areas
000000004005aa12 T _snd_pcm_softvol_open
000000004005ad76 T snd_pcm_format_signed
000000004005ada6 T snd_pcm_format_unsigned
000000004005adbc T snd_pcm_format_linear
000000004005add2 T snd_pcm_format_little_endian
000000004005ae00 T snd_pcm_format_big_endian
000000004005ae16 T snd_pcm_format_width
000000004005ae32 T snd_pcm_build_linear_format
000000004005ae86 t snd_pcm_linear_hw_refine
000000004005ae96 t snd_pcm_linear_dump
000000004005aee8 t snd_pcm_channel_area_step.isra.1.part.2
000000004005af0c T snd_pcm_linear_convert_index
000000004005af9e T snd_pcm_linear_get_index
000000004005b032 T snd_pcm_linear_put_index
000000004005b0c4 t snd_pcm_linear_hw_params
000000004005b1c8 T snd_pcm_linear_convert
000000004005b642 T snd_pcm_linear_getput
000000004005ba72 t snd_pcm_linear_write_areas
000000004005bacc t snd_pcm_linear_read_areas
000000004005bb26 T snd_pcm_linear_open
000000004005bc2a t snd_pcm_plug_change_access
000000004005bc4e t snd_pcm_plug_dump
000000004005bc6e t snd_pcm_plug_clear
000000004005bcb6 t snd_pcm_plug_hw_free
000000004005bcdc t snd_pcm_plug_hw_refine_sprepare
000000004005bd44 t snd_pcm_plug_hw_refine_slave
000000004005bd4c t snd_pcm_plug_change_channels
000000004005bfa8 t snd_pcm_plug_change_format
000000004005c07a t snd_pcm_plug_hw_refine
000000004005c0a6 t check_linear_format.part.7
000000004005c148 t snd_pcm_plug_close
000000004005c1c4 t snd_pcm_plug_change_rate
000000004005c26a t snd_pcm_plug_hw_refine_cprepare
000000004005c2e8 t snd_pcm_plug_slave_format
000000004005c3e4 t snd_pcm_plug_hw_refine_schange
000000004005c640 t snd_pcm_plug_hw_params
000000004005c804 t snd_pcm_plug_hw_refine_cchange
000000004005ca2c T snd_pcm_plug_open
000000004005cafa T _snd_pcm_plug_open
000000004005ccee t recalc
000000004005cd5c t snd_pcm_rate_sync_hwptr0
000000004005cdee t snd_pcm_rate_state
000000004005ce00 t snd_pcm_rate_start
000000004005ce64 t snd_pcm_rate_dump
000000004005ceea t snd_pcm_rate_sw_params
000000004005cff0 t snd_pcm_rate_hw_free
000000004005d03c t snd_pcm_rate_hw_params
000000004005d2ce t snd_pcm_rate_hw_refine_schange
000000004005d360 t snd_pcm_rate_hw_refine_cchange
000000004005d464 t snd_pcm_rate_hw_refine_sprepare
000000004005d4c0 t snd_pcm_rate_hw_refine
000000004005d4ec t snd_pcm_rate_hw_refine_cprepare
000000004005d570 t snd_pcm_rate_close
000000004005d58e t snd_pcm_rate_reset
000000004005d5d2 t snd_pcm_channel_area_addr.part.2
000000004005d5f6 t snd_pcm_channel_area_step.isra.3.part.4
000000004005d61a t convert_to_s16
000000004005d8ac t convert_from_s16
000000004005db74 t do_convert
000000004005dc48 t snd_pcm_rate_write_areas1
000000004005dc6a t snd_pcm_rate_commit_area
000000004005de0e t snd_pcm_rate_sync_playback_area
000000004005dea0 t snd_pcm_rate_mmap_commit
000000004005deda t snd_pcm_rate_drain
000000004005dfa0 t snd_pcm_rate_read_areas1
000000004005dfba t snd_pcm_rate_avail_update
000000004005e23c t snd_pcm_rate_hwsync
000000004005e268 t snd_pcm_rate_delay
000000004005e2e8 t snd_pcm_rate_prepare
000000004005e32c T snd_pcm_rate_open
000000004005e500 T _snd_pcm_rate_open
000000004005e5cc T _snd_pcm_file_open
000000004005e5d0 t snd_pcm_multi_channel_info
000000004005e61e t snd_pcm_multi_may_wait_for_avail_min
000000004005e678 t snd_pcm_multi_munmap
000000004005e69e t snd_pcm_multi_mmap
000000004005e774 t snd_pcm_multi_wait
000000004005e78a t snd_pcm_multi_mmap_commit
000000004005e7e6 t snd_pcm_multi_avail_update
000000004005e83a t snd_pcm_multi_delay
000000004005e850 t snd_pcm_multi_hwsync
000000004005e866 t snd_pcm_multi_state
000000004005e87c t snd_pcm_multi_pause
000000004005e8e0 t snd_pcm_multi_drain
000000004005e93a t snd_pcm_multi_drop
000000004005e994 t snd_pcm_multi_start
000000004005e9ee t snd_pcm_multi_reset
000000004005ea3a t snd_pcm_multi_prepare
000000004005ea86 t snd_pcm_multi_dump
000000004005eb34 t snd_pcm_multi_sw_params
000000004005eb7a t snd_pcm_multi_hw_free
000000004005ebf0 t snd_pcm_multi_close
000000004005ec4e t snd_pcm_multi_hw_refine_sprepare.isra.3
000000004005ec9a t snd_pcm_multi_hw_refine_schange.isra.5
000000004005ece2 t snd_pcm_multi_hw_refine_cchange.isra.6
000000004005ed3a t snd_pcm_multi_hw_params
000000004005eeac t snd_pcm_multi_hw_refine
000000004005f00c T _snd_pcm_multi_open
000000004005f442 t snd_mask_min.isra.4
000000004005f460 t snd_mask_max.isra.5
000000004005f478 t snd_mask_single.isra.12
000000004005f4a0 t snd_mask_refine_min
000000004005f4e2 t boundary_sub
000000004005f56c t boundary_lt
000000004005f5ea t snd_pcm_hw_refine_soft.part.29
000000004005f706 t snd_mask_refine_max
000000004005f74c T snd_pcm_hw_param_empty
000000004005f79a T snd_pcm_hw_param_always_eq
000000004005f840 T snd_pcm_hw_param_never_eq
000000004005f8d4 T _snd_pcm_hw_param_any
000000004005f94c T _snd_pcm_hw_params_any
000000004005f988 T snd_pcm_hw_param_get
000000004005fa40 T snd_pcm_hw_param_get_min
000000004005fadc T snd_pcm_hw_param_get_max
000000004005fb82 T _snd_pcm_hw_param_set_mask
000000004005fbec T _snd_pcm_hw_param_set_range
000000004005fc54 T _snd_pcm_hw_param_set_min
000000004005fce6 T _snd_pcm_hw_param_set_max
000000004005fd82 T _snd_pcm_hw_param_set_minmax
000000004005fe72 T _snd_pcm_hw_param_refine
000000004005fee4 T _snd_pcm_hw_params_refine
000000004005ff3c T snd_pcm_hw_refine
000000004005ff8e T snd_pcm_hw_param_set_mask
000000004006003e T snd_pcm_hw_param_set_first
000000004006011e T snd_pcm_hw_param_set_last
00000000400601fe T snd_pcm_hw_param_set_min
00000000400602e6 T snd_pcm_hw_param_set_max
00000000400603ce T snd_pcm_hw_param_set_minmax
00000000400604d8 T _snd_pcm_hw_param_set
00000000400605b0 T snd_pcm_hw_param_set
0000000040060670 T snd_pcm_hw_param_set_near
00000000400607fa t snd_pcm_hw_param_set_near_minmax
00000000400608c4 T snd_pcm_hw_param_refine_near
0000000040060926 T snd_pcm_hw_param_refine_multiple
00000000400609d4 T snd_pcm_hw_refine_soft
0000000040060a32 T snd_pcm_hw_refine_slave
0000000040060af0 T snd_pcm_hw_params_slave
0000000040060b48 T _snd_pcm_hw_params_internal
0000000040060d02 t linear_convert
0000000040060d08 t get_supported_rates
0000000040060d1e t get_supported_rates
0000000040060d2a t get_supported_rates
0000000040060d36 t linear_reset
0000000040060d4a t linear_free
0000000040060d64 t linear_close
0000000040060d68 t linear_init
0000000040060e20 t input_frames
0000000040060e66 t input_frames
0000000040060e9a t input_frames
0000000040060eae t linear_adjust_pitch
0000000040060f52 t snd_pcm_channel_area_step.isra.3
0000000040060f82 t linear_shrink_s16
00000000400610a2 t linear_expand_s16
00000000400611b6 t output_frames
0000000040061206 t output_frames
000000004006123a t output_frames
000000004006124e t linear_shrink
00000000400616fa t linear_expand
0000000040061b72 T _snd_pcm_rate_linear_open
0000000040061c08 t pcm_src_convert_s16
0000000040061c26 t pcm_src_convert_s16
0000000040061d04 t pcm_src_adjust_pitch
0000000040061d1e t pcm_src_reset
0000000040061d24 t pcm_src_reset
0000000040061d2a t pcm_src_free
0000000040061d4a t pcm_src_free
0000000040061d8c t pcm_src_init
0000000040061dd0 t pcm_src_init
0000000040061e88 t pcm_src_close
0000000040061e8c t pcm_src_close
0000000040061e90 T _snd_pcm_rate_speexrate_open
0000000040061ece t cubic_coef
0000000040061f4c t resampler_basic_direct_single
0000000040061ffe t resampler_basic_direct_double
0000000040062120 t resampler_basic_interpolate_single
000000004006227c t resampler_basic_interpolate_double
000000004006240c t resampler_basic_zero
000000004006246a t speex_resampler_process_native
0000000040062502 t speex_resampler_magic
00000000400625b8 t sinc
0000000040062704 t update_filter
0000000040062b34 T speex_resampler_destroy
0000000040062b66 T speex_resampler_process_int
0000000040062dd2 T speex_resampler_process_interleaved_int
0000000040062e62 T speex_resampler_set_rate_frac
0000000040062efa T speex_resampler_get_ratio
0000000040062f04 T speex_resampler_set_quality
0000000040062f22 T speex_resampler_init_frac
0000000040063072 T speex_resampler_reset_mem
00000000400630ae T _snd_pcm_rate_awrate_open
00000000400630ea T xrun
0000000040063124 T audio_mgr_create
0000000040063178 T audio_mgr_release
0000000040063192 T set_param
000000004006339e T pcm_write
0000000040063476 T pcm_read
000000004006355e T dump_wav_header
00000000400635f0 T check_wav_header
0000000040063690 T resize_wav
000000004006369c T create_wav
0000000040063742 T cmd_soundcard
00000000400638ee t usage
000000004006397a t usage
0000000040063a22 T cmd_aplay
0000000040064086 T aplay
00000000400641d4 T capture_fs_wav
0000000040064426 T cmd_arecord
0000000040064880 t amixer_usage
00000000400648dc t amixer_ctl_info_print
000000004006498a t amixer
0000000040064d0c T cmd_amixer
0000000040064d1c T sunxi_soundcard_init
0000000040064d94 t twi_set_clock
0000000040064e6c t twi_clear_irq_flag
0000000040064e9e t twi_get_byte
0000000040064eb0 t twi_put_byte
0000000040064ec2 t twi_enable_irq
0000000040064ee0 t twi_disable_irq
0000000040064efa t twi_set_start
0000000040064f16 t twi_disable_ack
0000000040064f2e t twi_enable_ack
0000000040064f4a t twi_query_irq_status
0000000040064f5a t twi_get_sda
0000000040064f6a t twi_set_scl
0000000040064f8a t twi_clear_pending
0000000040064fa8 t twi_start_xfer
0000000040064fc8 t twi_set_rx_trig_level
0000000040064ff2 t twi_set_packet_addr_byte
0000000040065020 t twi_set_packet_data_byte
0000000040065044 t twi_disable_read_tran_mode
0000000040065066 t twi_disable_tran_irq
0000000040065088 t twi_slave_addr
00000000400650c8 t twi_clear_rxfifo
00000000400650e8 t twi_soft_reset
0000000040065186 t twi_slave_reset
00000000400651b4 t twi_dma_xfer
00000000400652b2 t twi_dma_callback
00000000400652b8 t hal_twi_sys_pinctrl_init
000000004006536a t hal_twi_pinctrl_exit
00000000400653a0 t twi_restart.isra.2
00000000400653d8 t twi_disable_dma_irq.constprop.9
00000000400653fc t twi_enable_tran_irq.constprop.10
000000004006541e t twi_set_packet_cnt.constprop.11
0000000040065440 t hal_twi_clk_exit
000000004006547c t twi_disable_lcr.constprop.16
0000000040065494 t twi_send_msgs.isra.6
00000000400654ec t hal_twi_handler
0000000040065abc t hal_twi_clk_init
0000000040065bca t twi_dma_read
0000000040065c82 T hal_twi_xfer
0000000040066062 T hal_twi_init_with_freq
0000000040066322 T hal_twi_init
000000004006633a T hal_twi_uninit
00000000400663bc T hal_twi_write
0000000040066406 T hal_twi_read
0000000040066506 t hal_pwm_pinctrl_exit.part.0
0000000040066522 t hal_pwm_pinctrl_init.isra.1
000000004006659a T hal_pwm_enable_controller
00000000400665e6 T hal_pwm_disable_controller
0000000040066624 T hal_pwm_porality
0000000040066654 T hal_pwm_init
0000000040066702 T hal_pwm_deinit
00000000400667a8 T hal_pwm_control_single
0000000040066b36 T hal_pwm_control_dual
0000000040067042 T hal_pwm_control
0000000040067098 T hal_gpadc_callback
000000004006709c t gpadc_handler
00000000400671d8 t hal_gpadc_hw_exit.constprop.0
000000004006722a T gpadc_channel_enable_lowirq
0000000040067258 T gpadc_read_channel_data
000000004006728e T hal_gpadc_channel_init
0000000040067386 T hal_gpadc_init
00000000400674fc T list_head_ext_remov_node_from_list
0000000040067542 T list_node_exist
000000004006759e T list_del_node_by_data
0000000040067614 T list_destroy_whole_list
0000000040067674 t bit_offset
0000000040067690 t bit_len
00000000400676b0 t usb_phy_tp_write
00000000400677e6 t usb_phy_tp_resistance_soft_adjust
000000004006783e t usb_phy_tp_read
000000004006790c t bit_read_map
0000000040067940 t bit_cal
0000000040067972 T usb_phy_range_show
0000000040067a70 T usb_phy_range_set
0000000040067bb8 T usb_phy_init
0000000040067ca0 t list_head_malloc_and_add
0000000040067ccc t _usb_virt_bus_remov_dev_from_drv
0000000040067cdc T usb_virt_bus_drv_reg
0000000040067d7e T usb_virt_bus_drv_unreg
0000000040067dfe T usb_virt_bus_dev_add
0000000040067ee4 T usb_virt_bus_dev_del
0000000040067f50 T usb_virt_bus_init
0000000040067f98 T usb_virt_bus_exit
0000000040067fde T usb_virt_bus_reference
0000000040067fe8 t _urb_done_complete_callback
0000000040067ff2 t _urb_timeout_process
0000000040067ffa t usb_start_wait_urb.constprop.1
00000000400681d6 T _usb_internal_ctrll_msg
0000000040068232 T usb_get_string
00000000400682ac T _usb_string_sub
0000000040068382 t usb_enable_interface.isra.1
0000000040068406 T usb_control_msg
0000000040068524 T usb_get_descriptor
00000000400685ce T usb_string
00000000400686ba T usb_set_configuration
0000000040068934 T usb_get_device_descriptor
0000000040068990 T usb_get_status
0000000040068a0a T usb_clear_halt
0000000040068a7e T usb_disable_interface
0000000040068ac2 T usb_set_interface
0000000040068bb8 T hub_port_status
0000000040068c54 T usb_resume_device
0000000040068c58 T hub_suspend
0000000040068c5c T hub_resume
0000000040068c60 T remote_wakeup
0000000040068c64 T hub_quiesce
0000000040068c7a T hub_hub_status
0000000040068d0e T hub_thread_sleep
0000000040068d20 T hub_thread_wakeup
0000000040068d26 t recursively_mark_NOTATTACHED
0000000040068d62 t hub_ioctl
0000000040068d66 t usb_set_bit
0000000040068da0 t usb_set_bit
0000000040068dda t usb_clear_bit
0000000040068e1a t usb_clear_bit
0000000040068e5a t release_address
0000000040068e84 t usb_test_and_clear_bit
0000000040068ed0 t set_port_feature
0000000040068f06 t hub_power_on
0000000040068f50 t clear_port_feature
0000000040068f86 t clear_hub_feature
0000000040068fba t usb_get_intfdata
0000000040068ff2 t usb_get_intfdata
000000004006902a t hub_status_req_complete
0000000040069038 t list_head_unlink_and_del
000000004006905e t hub_tt_work
000000004006915a t hub_disconnect
00000000400692a0 t get_string.part.4
00000000400692fc t Set_usb_cheak_flag
0000000040069380 t Set_external_usb_hub_flag
0000000040069404 t Get_external_usb_hub_flag
0000000040069474 t __usb_gen_hub_thread_exit.constprop.15
00000000400694ae t find_next_zero_bit.constprop.22
0000000040069580 T kick_khubd
00000000400695f4 t HubStatusThread
00000000400696a8 T hub_activate
00000000400696d2 t hub_probe
0000000040069ac4 T usb_enable_root_hub_irq
0000000040069ae0 T usb_root_hub_notify
0000000040069afc T usb_set_device_state
0000000040069c54 t hub_port_reset.part.12
0000000040069d50 T usb_new_device
0000000040069e30 t hub_port_disable.isra.13.constprop.24
0000000040069e62 T usb_disable_endpoint
0000000040069e90 t ep0_reinit
0000000040069ebc t hub_port_init
000000004006a306 T usb_disable_device
000000004006a3d6 T usb_disconnect
000000004006a44e t hub_pre_reset
000000004006a490 T usb_reset_device
000000004006a6fe T hub_main_thread
000000004006ac4e T hub_GetHubNo
000000004006ac5a T hub_GetHubSeries
000000004006ac70 T usb_gen_hub_init
000000004006adcc T usb_gen_hub_exit
000000004006ae3c t rh_string.isra.0
000000004006ae6a T usb_hcd_poll_rh_status
000000004006af10 T rh_timer_func
000000004006af14 T rh_urb_enqueue
000000004006b1c0 T rh_urb_dequeue
000000004006b26e t unlink1
000000004006b282 T usb_hcd_start_port_resume
000000004006b29c T usb_hcd_end_port_resume
000000004006b2ba T usb_release_bandwidth
000000004006b2e4 t urb_unlink
000000004006b352 t __usb_hcd_giveback_urb
000000004006b3fa T usb_hcd_giveback_urb
000000004006b46a T usb_hc_died
000000004006b4e0 T usb_create_hc_gen_dev
000000004006b57e T usb_add_hc_gen_dev
000000004006b76c T usb_remove_hc_gen_dev
000000004006b82c T hcd_ops_submit_urb
000000004006b972 T hcd_ops_unlink_urb
000000004006ba38 T hcd_ops_buffer_alloc
000000004006ba64 T hcd_ops_buffer_free
000000004006ba7e T hcd_ops_endpoint_disable
000000004006bb4a T usb_hcd_link_urb_to_ep
000000004006bc1e T usb_hcd_unlink_urb_from_ep
000000004006bc56 T usb_hcd_check_unlink_urb
000000004006bc82 T usb_hub_clear_tt_buffer
000000004006bd5a T usb_calc_bus_time
000000004006be46 T usb_gen_hcd_init
000000004006be5e T usb_gen_hcd_exit
000000004006be7e T usb_host_func_drv_reg
000000004006beb6 T usb_host_func_drv_unreg
000000004006beba T usb_core_init
000000004006bee4 T usb_core_exit
000000004006bf0e t _find_next_desc.constprop.4
000000004006bf3e T usbm_sendcmd
000000004006bf76 T usb_get_all_config_desc_and_parser
000000004006c494 t interface_to_usbdev
000000004006c4c6 T usb_core_enable
000000004006c4d2 T usb_core_disable
000000004006c4e0 T usb_core_is_enabled
000000004006c4f2 T usb_host_alloc_virt_dev
000000004006c578 T usb_host_free_virt_dev
000000004006c730 T usb_match_id
000000004006c82e T _usb_core_func_drv_probe_interface
000000004006c88c T _usb_core_func_drv_disconncet_interface
000000004006c8ec T usb_altnum_to_altsetting
000000004006c912 T usb_lock_device
000000004006c91a T usb_unlock_device
000000004006c922 T usb_buffer_alloc
000000004006c92a T usb_buffer_free
000000004006c932 T usb_ifnum_to_if
000000004006c960 T usb_drivers_init
000000004006c970 T usb_drivers_exit
000000004006c980 T usb_init_urb
000000004006c98e T usb_alloc_urb
000000004006c9b2 T usb_free_urb
000000004006c9ba T usb_submit_urb
000000004006caec T usb_kill_urb
000000004006cb34 T usb_unlink_urb
000000004006cb58 t list_del
000000004006cb72 t list_del
000000004006cb8c t list_del
000000004006cbaa t list_del
000000004006cbc8 t list_del
000000004006cbe6 t list_move_tail
000000004006cbfa t list_splice_tail_init
000000004006cc12 t ehci_disable_PSE
000000004006cc3a t usb_test_bit
000000004006cc76 t ehci_urb_done
000000004006cce6 t find_tt
000000004006cdca t reserve_release_intr_bandwidth
000000004006ceea t ehci_enable_event
000000004006cf70 T ehci_hrtimer_func
000000004006d008 t usb_dma_malloc
000000004006d044 t usb_dma_malloc
000000004006d080 t qh_refresh.isra.11
000000004006d102 t periodic_next_shadow.isra.13
000000004006d124 t ehci_read_frame_index
000000004006d152 t ehci_clear_tt_buffer.isra.26
000000004006d194 t qh_completions
000000004006d6b8 t usb_dma_free.isra.28
000000004006d6c0 t qtd_list_free.isra.30
000000004006d6f4 t iso_sched_alloc.isra.34
000000004006d724 t qh_destroy.isra.35
000000004006d746 t ehci_mem_cleanup
000000004006d77c t iso_sched_free
000000004006d79c t iso_stream_schedule
000000004006d912 t turn_on_io_watchdog
000000004006d94c t start_free_itds
000000004006d974 t end_free_itds
000000004006da36 t ehci_poll_PSS
000000004006dab4 t disable_periodic
000000004006daca t enable_periodic
000000004006dafa t qh_link_periodic
000000004006dc1e t iso_stream_find
000000004006df16 t qtd_fill.isra.60
000000004006dfa6 t cancel_unlink_wait_intr.isra.16
000000004006dfc6 t ehci_qtd_alloc.isra.50
000000004006e01c t ehci_qh_alloc.isra.51
000000004006e088 t qh_urb_transaction.isra.61
000000004006e38e t ehci_disable_ASE
000000004006e3b8 t qh_append_tds
000000004006e7d0 t check_intr_schedule.isra.18
000000004006ea6e t qh_schedule
000000004006ec06 t ehci_handle_intr_unlinks
000000004006ecf6 t start_unlink_intr
000000004006ee96 t ehci_handle_start_intr_unlinks
000000004006ef0c t ehci_poll_ASS
000000004006ef8a t qh_link_async
000000004006f022 t end_unlink_async
000000004006f176 t start_iaa_cycle
000000004006f1ce t start_unlink_async
000000004006f244 t unlink_empty_async
000000004006f2c6 T ehci_work
000000004006f972 t end_iaa_cycle
000000004006f99a t ehci_iaa_watchdog
000000004006f9e8 t ehci_handle_controller_death
000000004006fa52 T ehci_handshake
000000004006fa9e T ehci_halt
000000004006fb06 T ehci_reset
000000004006fb8a T ehci_bus_suspend
000000004006fb8e T ehci_bus_resume
000000004006fb92 T ehci_hub_status_data
000000004006fcc6 T ehci_hub_control
00000000400702f6 T ehci_clear_tt_buffer_complete
000000004007035c T ehci_stop
00000000400704d2 T ehci_init
00000000400706e2 T ehci_run
00000000400707b6 T ehci_irq_handler
0000000040070a42 T ehci_urb_enqueue
0000000040071302 T ehci_urb_dequeue
00000000400713a4 T ehci_endpoint_disable
0000000040071590 T ehci_get_frame
00000000400715b4 T hci_clock_init
0000000040071618 T open_clock
00000000400716da T close_clock
0000000040071772 T usb_passby
00000000400717be T sunxi_set_vbus
0000000040071800 T sunxi_hci_get_config_param
00000000400718ce t sunxi_ehci_setup
00000000400718ee t sunxi_stop_ehci
0000000040071934 T sunxi_ehci_status_get
000000004007193e T sunxi_ehci_phy_addr_get
000000004007195a T sunxi_insmod_ehci
0000000040071a48 T sunxi_rmmod_ehci
0000000040071a80 T sunxi_ehci_hcd_init
0000000040071b74 T sunxi_ehci_hcd_deinit
0000000040071b92 T ehci_ed_test
0000000040071c86 t periodic_reinit
0000000040071cd6 t roothub_a
0000000040071d12 t roothub_portstatus
0000000040071d58 t ohci_usb_reset
0000000040071d82 t ed_schedule
0000000040071f90 t ed_deschedule
0000000040072108 t ohci_frame_no
0000000040072128 t start_ed_unlink
0000000040072196 t td_fill
0000000040072284 t td_alloc.isra.20
00000000400722be t td_free
000000004007230a t urb_free_priv
0000000040072370 t finish_urb
000000004007245a t td_done.isra.15
0000000040072552 t ohci_work
0000000040072902 T ohci_hub_status_data
0000000040072a48 T ohci_hub_control
0000000040072d14 T ohci_hcd_init
0000000040072d2e T ohci_urb_enqueue
00000000400732de T ohci_urb_dequeue
0000000040073348 T ohci_endpoint_disable
0000000040073406 T ohci_get_frame
000000004007341a T ohci_init
0000000040073506 T ohci_run
0000000040073792 T ohci_stop
00000000400737e4 T ohci_restart
00000000400738d4 t ohci_rh_resume
0000000040073b32 T ohci_irq_handler
0000000040073e44 t sunxi_ohci_start
0000000040073e84 t sunxi_stop_ohci
0000000040073eac T sunxi_ohci_hcd_init
000000004007406c T sunxi_ohci_hcd_deinit
00000000400740dc T hal_usb_core_init
00000000400740e0 T hal_usb_core_exit
00000000400740e4 T hal_usb_hcd_init
0000000040074106 T hal_usb_hcd_deinit
0000000040074128 T hal_usb_hci_init
000000004007413a T hal_usb_hcd_debug_set
0000000040074144 T hal_usb_hcd_debug_get
0000000040074150 T hal_hci_ed_test
000000004007428c T hal_hci_phy_range_show
00000000400742a0 T hal_hci_phy_range_set
00000000400742b8 t usb_manager_open
00000000400742bc t usb_manager_close
00000000400742c0 t usb_hardware_scan_thread
0000000040074312 t usb_host_scan_thread
000000004007436e t usb_device_scan_thread
00000000400743ca T SetUsbIapConnect
0000000040074400 t usb_manager_control
00000000400744ea T usb_manager_dev_init
000000004007457a T hal_usb_manager_init
0000000040074a06 T get_usb_role
0000000040074a10 T usb_msg_center
0000000040074b4e T usb_msg_center_init
0000000040074b66 T hw_insmod_usb_host
0000000040074b72 T hw_rmmod_usb_host
0000000040074b7e T hw_insmod_usb_device
0000000040074b8a T hw_rmmod_usb_device
0000000040074b96 t null_hw_scan
0000000040074b98 t PIODataIn_debounce
0000000040074bea t vbus_id_hw_scan
0000000040074dc8 T usb_hw_scan
0000000040074dde T usb_hw_scan_init
0000000040074fba T mscQIC157Command
0000000040074fc8 T mscATAPICommand
0000000040074fcc T mscUFICommand
0000000040074fd0 T mscScsiCommand
0000000040074fde t mscUrbCallBack
0000000040074fe8 t mscUsbTransport
00000000400750d2 t mscTimeOut
00000000400750e4 t mscClearHalt
00000000400751be t mscSendBulkReq
000000004007528a T mscGetMaxLun
000000004007535e T mscResetRecovery
000000004007539c T mscBoTransport
0000000040075584 T mscBoStopTransport
00000000400755d4 t mscTransportRequestSense.constprop.0
0000000040075656 T mscGetDataTransportReason
0000000040075764 t GetProtocol
00000000400757e8 t mscDevSuspend
0000000040075804 t MediaChangeThread
000000004007585e t mscDevScanThread
0000000040075908 t mscMainThread
0000000040075a0a t mscDevFree
0000000040075ad0 t mscDevProbe
0000000040075e8e t mscDevRemove
0000000040075f36 T mscDevQueueCmnd
0000000040075fbc T mscInit
0000000040076048 T mscExit
0000000040076076 t Usb_uint2str_dec
00000000400760d0 t sunxi_udisk_open
00000000400760d4 t sunxi_udisk_close
00000000400760d8 t sunxi_udisk_control
00000000400760e6 t DiskOpen
0000000040076102 t DiskClose
0000000040076124 t DiskIoctl
000000004007619e t __DiskWrite
000000004007621a t DiskWrite
00000000400762a6 t __DiskRead
000000004007631c t DiskRead
000000004007639a T UsbBlkDevAllocInit
000000004007649e T UsbBlkDevFree
00000000400764c8 T GetDiskInfo
000000004007674a T ShutDown
000000004007675e T UsbBlkDevReg
0000000040076992 T UsbBlkDevUnReg
00000000400769f2 t GetCDRomMediaEvent
0000000040076a76 t GetCDDriveCapabilites
0000000040076c04 t CDMediaChange
0000000040076d66 T CDIOCtrl
0000000040076e2a T CDProbe
0000000040076ee8 T CDRemove
0000000040076f3c t Set_usb_disk_MediaPresent_flag
0000000040076fa0 t DiskMediaChange
0000000040077078 T DiskProbe
0000000040077146 T DiskRemove
000000004007718e T mscLun_alloc
00000000400771ec T mscLun_free
0000000040077228 T mscLunAdd
00000000400772f2 T mscLunDel
0000000040077316 t ScsiCmndDone
0000000040077320 T ScsiRequestSense
00000000400773ee T ScsiStartStopUnit
0000000040077478 t ScsiDoCommand.constprop.0
00000000400775a8 T ScsiTestUnitReady
0000000040077610 T ScsiModeSense10
00000000400776dc T ScsiModeSense6
000000004007779c T ScsiReadCapacity
0000000040077846 T ScsiInquery
00000000400778dc T ScsiSynchronizeCache
0000000040077952 T ScsiRead
0000000040077a3e T ScsiWrite
0000000040077b2a T ScsiPreventAllowMedium
0000000040077bb4 T ScsiDvdGetEventStatus
0000000040077c66 t set_usbh_temp_buff_valid
0000000040077cd6 t read_usbh_temp_buff
0000000040077da8 t set_usbh_temp_buff_default
0000000040077de6 t select_invalid_usbh_temp_buff.part.1
0000000040077e8a t set_usbh_temp_buff_busy
0000000040077ecc t set_usbh_temp_buff_free
0000000040077f0e T set_usbh_temp_buff_invalid_by_dev
0000000040077f56 T set_all_usbh_temp_buff_invalid
0000000040077f96 T usbh_msc_special_read
000000004007811c T usbh_msc_special_write
0000000040078270 T init_usbh_buff_manager
0000000040078316 T exit_usbh_buff_manager
000000004007836c t usbh_disk_info_open
000000004007837c t usbh_disk_info_close
0000000040078396 t usbh_disk_info_read
000000004007839a t usbh_disk_info_write
000000004007839e t usbh_disk_info_ioctrl
000000004007843a T usbh_disk_SaveDeviceInfo
0000000040078476 T usbh_disk_FreeDeviceInfo
0000000040078486 T usbh_disk_info_reg
00000000400784e4 T usbh_disk_info_unreg
000000004007852c T usbh_disk_save_time
0000000040078552 T usbh_disk_del_time
000000004007857a T usbh_disk_time_init
000000004007858c T usbh_disk_time_exit
0000000040078590 T platform_get_ehci_table
000000004007859a T platform_get_ohci_table
00000000400785a4 T platform_get_otg_table
00000000400785ae t disp_draw_colorbar
000000004007869c t disp_sync_finish_process
0000000040078704 T DRV_disp_int_process
0000000040078782 t start_process
00000000400787a4 t start_work
00000000400788f8 T disp_enhance_mode_show
0000000040078920 T disp_enhance_mode_store
000000004007899a T disp_enhance_bright_show
0000000040078a1e T disp_enhance_bright_store
0000000040078aa2 T disp_enhance_saturation_show
0000000040078b26 T disp_enhance_saturation_store
0000000040078baa T disp_enhance_contrast_show
0000000040078c2e T disp_enhance_contrast_store
0000000040078cb2 T disp_enhance_detail_show
0000000040078d0e T disp_enhance_detail_store
0000000040078d4e T disp_enhance_denoise_show
0000000040078daa T disp_enhance_denoise_store
0000000040078dea T disp_color_temperature_show
0000000040078e46 T disp_color_temperature_store
0000000040078ea2 T sunxi_disp_get_source_ops
0000000040078fa0 T disp_register_sync_finish_proc
0000000040078fde T disp_probe
000000004007991c T disp_sys_show
0000000040079b98 T disp_colorbar_store
0000000040079c00 T disp_capture_dump
0000000040079e66 t disp_capture_dump
0000000040079ebe T disp_ioctl
000000004007a940 T disp_release
000000004007a944 T disp_open
000000004007a948 T disp_sys_clk_set_rate
000000004007a974 T disp_sys_clk_get_rate
000000004007a990 T disp_sys_clk_enable
000000004007a9f0 T disp_clock_is_enabled
000000004007aa10 T disp_sys_clk_disable
000000004007aa4c T disp_sys_clk_set_parent
000000004007aa92 T disp_sys_clk_get_parent
000000004007aaaa T disp_delay_ms
000000004007aaae T disp_delay_us
000000004007aab2 T disp_sys_malloc
000000004007aada T disp_sys_free
000000004007aade T disp_dma_malloc
000000004007ab12 T disp_dma_free
000000004007ab1c T disp_sys_mutex_lock
000000004007ab22 T disp_sys_mutex_unlock
000000004007ab28 T disp_sys_mutex_init
000000004007ab48 T disp_sys_register_irq
000000004007ab58 T disp_sys_unregister_irq
000000004007ab5c T disp_sys_enable_irq
000000004007ab60 T disp_sys_disable_irq
000000004007ab64 T disp_sys_script_get_item
000000004007ac86 T disp_sys_gpio_request
000000004007ace6 T disp_sys_gpio_release
000000004007ad02 T disp_sys_gpio_set_direction
000000004007ad14 T disp_sys_gpio_set_value
000000004007ad32 T disp_sys_pin_set_state
000000004007adee T disp_sys_power_enable
000000004007adf2 T disp_sys_power_disable
000000004007adf6 T disp_getprop_regbase
000000004007ae0c T disp_getprop_irq
000000004007ae22 T disp_getprop_clk
000000004007ae38 T disp_get_rst_by_name
000000004007ae50 T disp_sys_pwm_request
000000004007ae6c T disp_sys_pwm_free
000000004007ae82 T disp_sys_pwm_enable
000000004007aebc T disp_sys_pwm_disable
000000004007aefa T disp_sys_pwm_config
000000004007af26 T disp_sys_pwm_set_polarity
000000004007af34 t printf_from_to
000000004007af8e t cmd_disp_debug
000000004007b4a0 t disp_tasklet
000000004007b4c2 t disp_sync_all
000000004007b518 T bsp_disp_shadow_protect
000000004007b5ee T disp_device_attached
000000004007b670 T disp_device_attached_and_enable
000000004007b774 T bsp_disp_device_switch
000000004007b7ee T bsp_disp_device_set_config
000000004007b87a T disp_init_connections
000000004007b962 T bsp_disp_init
000000004007ba1a T bsp_disp_sync_with_hw
000000004007bb38 T bsp_disp_vsync_event_enable
000000004007bb54 T bsp_disp_get_fps
000000004007bbb2 T bsp_disp_get_health_info
000000004007bbe4 T sync_event_proc
000000004007bd1c T bsp_disp_get_output_type
000000004007bd48 T bsp_disp_hdmi_check_support_mode
000000004007bd94 T bsp_disp_tv_set_hpd
000000004007bdde T bsp_disp_tv_register
000000004007be48 T bsp_disp_lcd_set_panel_funs
000000004007beaa T LCD_OPEN_FUNC
000000004007bed0 T LCD_CLOSE_FUNC
000000004007bef6 T bsp_disp_get_lcd_registered
000000004007bf0e T bsp_disp_get_hdmi_registered
000000004007bf1c T bsp_disp_lcd_backlight_enable
000000004007bf3c T bsp_disp_lcd_backlight_disable
000000004007bf5c T bsp_disp_lcd_pwm_enable
000000004007bf7c T bsp_disp_lcd_pwm_disable
000000004007bf9c T bsp_disp_lcd_power_enable
000000004007bfc0 T bsp_disp_lcd_power_disable
000000004007bfe4 T bsp_disp_lcd_tcon_enable
000000004007c05c T bsp_disp_lcd_tcon_disable
000000004007c0ca T bsp_disp_lcd_pin_cfg
000000004007c0ee T bsp_disp_lcd_gpio_set_value
000000004007c116 T bsp_disp_lcd_gpio_set_direction
000000004007c13e T bsp_disp_get_panel_info
000000004007c162 T bsp_disp_lcd_dsi_mode_switch
000000004007c1e6 T bsp_disp_lcd_dsi_clk_enable
000000004007c25e T bsp_disp_lcd_dsi_dcs_wr
000000004007c2f4 T bsp_disp_lcd_dsi_gen_wr
000000004007c38a T bsp_disp_lcd_dsi_gen_short_read
000000004007c39c T bsp_disp_lcd_dsi_dcs_read
000000004007c3a8 T bsp_disp_lcd_set_max_ret_size
000000004007c3ac T bsp_disp_feat_get_num_screens
000000004007c3b0 T bsp_disp_feat_get_num_devices
000000004007c3b4 T bsp_disp_feat_get_num_channels
000000004007c3b8 T bsp_disp_feat_get_num_layers
000000004007c3bc T bsp_disp_feat_get_num_layers_by_chn
000000004007c3c0 T bsp_disp_feat_is_supported_output_types
000000004007c3c4 T bsp_disp_feat_is_support_capture
000000004007c3c8 T bsp_disp_feat_is_support_enhance
000000004007c3cc T disp_init_feat
000000004007c3d0 T disp_device_set_manager
000000004007c3e2 T disp_device_unset_manager
000000004007c3f8 T disp_device_get_resolution
000000004007c40a T disp_device_get_timings
000000004007c432 T disp_device_is_interlace
000000004007c43e T disp_device_get_status
000000004007c448 T disp_device_is_in_safe_period
000000004007c474 T disp_device_show_builtin_patten
000000004007c47e T disp_device_usec_before_vblank
000000004007c4d6 T disp_device_get
000000004007c500 T disp_device_find
000000004007c526 T disp_device_register
000000004007c53c t disp_lcd_is_used
000000004007c554 t disp_lcd_bright_curve_init
000000004007c61a T disp_lcd_get_bright
000000004007c632 T disp_lcd_is_enabled
000000004007c64a t disp_lcd_set_open_func
000000004007c676 t disp_lcd_set_close_func
000000004007c6a2 t disp_lcd_get_dimensions
000000004007c6c0 t disp_lcd_get_fps
000000004007c6d8 t disp_lcd_check_config_dirty
000000004007c704 t disp_lcd_get_static_config
000000004007c722 t disp_lcd_exit
000000004007c736 T disp_lcd_tcon_disable
000000004007c74c t disp_lcd_get_color_temperature
000000004007c796 T disp_lcd_check_if_enabled
000000004007c7c8 t disp_lcd_power_enable
000000004007c822 t disp_lcd_update_gamma_tbl_set
000000004007c93e t disp_lcd_set_color_temperature
000000004007c9ae t disp_lcd_get_gamma_tbl
000000004007ca1e t disp_lcd_set_gamma_tbl
000000004007ca9e t disp_lcd_get_panel_info
000000004007cac8 t disp_lcd_power_disable
000000004007cb22 t disp_lcd_backlight_disable
000000004007cbd2 T disp_lcd_gpio_set_direction
000000004007cc20 T disp_lcd_gpio_set_value
000000004007cc74 t disp_lcd_disable_gamma
000000004007cc98 t lcd_calc_judge_line
000000004007cd4e t disp_lcd_is_in_safe_period
000000004007cd90 t disp_lcd_tcon_enable
000000004007cda6 t disp_lcd_pin_cfg
000000004007ce96 t disp_lcd_pwm_disable
000000004007ced4 t disp_lcd_pwm_enable
000000004007cf3c t disp_lcd_set_panel_funs
000000004007cfda t disp_lcd_event_proc
000000004007d0fa t disp_lcd_init
000000004007dc56 t disp_lcd_set_static_config
000000004007dca2 t lcd_clk_enable
000000004007de76 t cal_real_frame_period
000000004007df2e t cal_real_frame_period
000000004007dfe4 t disp_lcd_sw_enable
000000004007e266 t disp_lcd_enable_gamma
000000004007e2d0 t disp_lcd_get_status
000000004007e2e2 T disp_lcd_set_bright
000000004007e440 t disp_lcd_set_bright_dimming
000000004007e488 t disp_lcd_backlight_enable
000000004007e55e T disp_get_lcd
000000004007e59e T disp_lcd_gpio_init
000000004007e652 t disp_lcd_fake_enable
000000004007e83a t disp_lcd_enable
000000004007ea3c T disp_lcd_gpio_exit
000000004007eaf2 t disp_lcd_disable
000000004007ecc8 T disp_init_lcd
000000004007f0ae t disp_lyr_check
000000004007f0c0 t disp_lyr_check2
000000004007f0c4 t disp_lyr_is_dirty
000000004007f0e0 t disp_lyr_dirty_clear
000000004007f0f6 t disp_lyr_apply
000000004007f108 t disp_mgr_get_priv
000000004007f122 t disp_mgr_shadow_protect
000000004007f152 t disp_mgr_get_clk_rate
000000004007f17e t disp_mgr_exit
000000004007f1a0 t disp_mgr_get_output_color_range
000000004007f1c0 t disp_mgr_tasklet
000000004007f214 t disp_mgr_is_enabled
000000004007f234 t disp_mgr_smooth_switch
000000004007f2c4 t disp_mgr_update_color_space
000000004007f368 t disp_mgr_set_output_color_range
000000004007f3da t disp_mgr_get_back_color
000000004007f432 t disp_mgr_set_back_color
000000004007f4a0 t disp_mgr_get_color_key
000000004007f506 t disp_mgr_set_color_key
000000004007f586 t disp_lyr_force_apply
000000004007f5d4 t disp_lyr_unset_manager
000000004007f632 t disp_lyr_set_manager
000000004007f690 t disp_mgr_sync
000000004007f792 t disp_mgr_update_regs
000000004007f7e6 t disp_mgr_init
000000004007f81e t disp_mgr_blank
000000004007f8a8 t disp_mgr_dump
000000004007f9d0 t disp_lyr_dump
000000004007fb5a t disp_mgr_apply
000000004007fcb0 t disp_mgr_force_apply
000000004007fd64 t disp_mgr_disable
000000004007fdf6 t disp_mgr_enable
0000000040080002 T disp_get_layer
0000000040080086 t disp_mgr_get_layer_config
00000000400800f0 t disp_mgr_set_layer_config
00000000400801a4 T disp_get_layer_1
0000000040080224 T __disp_config_transfer2inner
000000004008031c t disp_lyr_save_and_dirty_check
0000000040080404 T __disp_config2_transfer2inner
0000000040080522 t disp_lyr_save_and_dirty_check2
0000000040080610 T __disp_inner_transfer2config
00000000400806f0 t disp_lyr_get_config
000000004008074c T __disp_inner_transfer2config2
000000004008086a t disp_lyr_get_config2
00000000400808c6 T disp_get_layer_manager
00000000400808fa T disp_get_current_output_format_by_device_type
000000004008091e T disp_init_mgr
0000000040080d0e t disp_capture_get_priv
0000000040080d44 T disp_capture_query
0000000040080d62 T disp_capture_init
0000000040080d8e T disp_capture_exit
0000000040080db2 T disp_capture_set_manager
0000000040080df8 T disp_capture_unset_manager
0000000040080e34 T disp_capture_start
0000000040080e90 T disp_capture_stop
0000000040080ff8 T disp_capture_commit
0000000040081160 T disp_capture_sync
00000000400812fe T disp_get_capture
000000004008133e T disp_init_capture
00000000400814e6 T disp_tv_is_enabled
00000000400814f8 T disp_tv_suspend
000000004008152e T disp_tv_resume
0000000040081564 T disp_tv_set_mode
000000004008159a T disp_tv_get_mode
00000000400815d6 T disp_tv_get_input_csc
00000000400815ee T disp_tv_set_func
000000004008162e T disp_tv_check_support_mode
000000004008164e t disp_tv_get_fps
0000000040081662 T disp_set_enhance_mode
000000004008167a t disp_tv_check_config_dirty
000000004008169e t disp_tv_set_static_config
00000000400816a6 t disp_tv_get_static_config
00000000400816e8 t tv_calc_judge_line
000000004008179a t tv_clk_enable
00000000400817d0 T disp_tv_enable
00000000400818b0 T disp_tv_sw_enable
0000000040081970 t disp_tv_check_if_enabled
000000004008199a t disp_tv_init
00000000400819cc T disp_tv_event_proc
0000000040081a1a t disp_tv_set_gamma_tbl
0000000040081aa8 T disp_init_tv_para
0000000040081acc T disp_tv_set_hpd
0000000040081ae4 T disp_tv_disable
0000000040081b92 T disp_tv_exit
0000000040081bca T disp_init_tv
0000000040081f38 t disp_enhance_get_priv
0000000040081f56 t disp_enhance_is_enabled
0000000040081f7a t disp_enhance_get_mode
0000000040081f9c t disp_enhance_get_bright
0000000040081fbc t disp_enhance_get_saturation
0000000040081fdc t disp_enhance_get_contrast
0000000040081ffc t disp_enhance_get_edge
000000004008201c t disp_enhance_get_detail
000000004008203c t disp_enhance_get_denoise
000000004008205c t disp_enhance_shadow_protect
000000004008208e t disp_enhance_init
00000000400820ae t disp_enhance_exit
00000000400820b2 t disp_enhance_unset_manager
00000000400820f4 t disp_enhance_set_manager
0000000040082136 T disp_enhance_set_bright_ex
000000004008222e T disp_enhance_get_bright_ex
0000000040082296 T disp_enhance_set_contrast_ex
000000004008238e T disp_enhance_get_contrast_ex
00000000400823f6 T disp_enhance_set_saturation_ex
00000000400824ee T disp_enhance_get_saturation_ex
0000000040082556 T disp_enhance_set_hue_ex
000000004008264e T disp_enhance_get_hue_ex
00000000400826b6 t disp_enhance_apply
0000000040082770 T disp_enhance_set_para
0000000040082834 t disp_enhance_set_denoise
0000000040082892 t disp_enhance_set_detail
00000000400828f0 t disp_enhance_set_edge
000000004008294e t disp_enhance_set_contrast
00000000400829b0 t disp_enhance_set_saturation
0000000040082a0e t disp_enhance_set_bright
0000000040082a6c t disp_enhance_set_mode
0000000040082ad4 t disp_enhance_demo_disable
0000000040082b20 t disp_enhance_demo_enable
0000000040082b6c t disp_enhance_disable
0000000040082bc8 t disp_enhance_enable
0000000040082c6e t disp_enhance_dump
0000000040082ce6 t disp_enhance_tasklet
0000000040082d16 t disp_enhance_sync
0000000040082d4e t disp_enhance_update_regs
0000000040082d9a t disp_enhance_force_apply
0000000040082e0a T disp_get_enhance
0000000040082e3e T disp_init_enhance
00000000400830fa t lcd_set_panel_funs.part.0
0000000040083120 T lcd_set_panel_funs
0000000040083124 T lcd_init
0000000040083140 T sunxi_lcd_delay_ms
0000000040083156 T sunxi_lcd_tcon_enable
000000004008316a T sunxi_lcd_tcon_disable
000000004008317e T sunxi_lcd_backlight_disable
0000000040083192 T sunxi_lcd_power_enable
00000000400831a6 T sunxi_lcd_power_disable
00000000400831ba T sunxi_lcd_pwm_disable
00000000400831d0 T sunxi_lcd_dsi_clk_enable
00000000400831e8 T sunxi_lcd_set_panel_funs
00000000400831fe T sunxi_lcd_pin_cfg
0000000040083214 t LCD_panel_exit
0000000040083216 t LCD_user_defined_func
000000004008321a t lcd_reset
000000004008325e t LCD_close_flow
00000000400832b2 t LCD_power_off
00000000400832ce t LCD_open_flow
000000004008330e t LCD_power_on
0000000040083344 t LCD_cfg_panel_info
00000000400833e2 t LCD_panel_init
00000000400834c6 T LCD_bl_set_gpio
000000004008350a T lcd_pwm_stop
000000004008352e T LCD_bl_close
000000004008356e T de_fcc_init
00000000400835ec T de_fcc_update_regs
0000000040083650 T de_fcc_enable
0000000040083694 T de_fcc_set_size
0000000040083708 T de_fcc_set_window
00000000400837d2 T de_fcc_set_para
0000000040083840 T de_fcc_info2para
00000000400838de t regwrite
00000000400838fa t regwrite
0000000040083916 t regwrite
0000000040083932 T de_gsu_init
0000000040083a6e T de_gsu_update_regs
0000000040083b4a T de_gsu_set_para
0000000040083cb6 T de_gsu_calc_scaler_para
0000000040083d4a T de_calc_ovl_coord
0000000040083d66 T de_gsu_sel_ovl_scaler_para
0000000040083e30 t de_calc_overlay_scaler_para.constprop.1
000000004008445c T de_update_device_fps
000000004008446c T de_update_clk_rate
0000000040084484 T de_get_clk_rate
000000004008449c T de_al_lyr_apply_direct_show
000000004008453c T de_al_lyr_apply
0000000040084d74 T de_al_mgr_apply_color
0000000040084dba T de_al_mgr_apply
0000000040084e70 T de_al_mgr_sync
0000000040084e74 T de_al_mgr_update_regs
0000000040084ea2 T de_al_init
0000000040084eec t disp_al_lcd_get_clk_info.part.0
0000000040084fae T disp_al_get_direct_show_state
0000000040084fc2 T disp_al_layer_apply
0000000040085136 T disp_al_de_clk_enable
000000004008513c T disp_al_manager_init
0000000040085140 T disp_al_de_clk_disable
0000000040085146 T disp_al_manager_exit
000000004008514a T disp_al_manager_apply
00000000400851de T disp_al_manager_sync
00000000400851e2 T disp_al_manager_update_regs
00000000400851e6 T disp_al_enhance_apply
0000000040085222 T disp_al_enhance_apply_ex_bright
000000004008526a T disp_al_enhance_apply_ex_get_bright
000000004008527c T disp_al_enhance_apply_ex_contrast
00000000400852c4 T disp_al_enhance_apply_ex_get_contrast
00000000400852d6 T disp_al_enhance_apply_ex_saturation
000000004008531e T disp_al_enhance_apply_ex_get_saturation
0000000040085330 T disp_al_enhance_apply_ex_hue
0000000040085378 T disp_al_enhance_apply_ex_get_hue
000000004008538a T disp_al_enhance_update_regs
000000004008538e T disp_al_enhance_sync
0000000040085392 T disp_al_enhance_tasklet
0000000040085396 T disp_al_capture_init
00000000400853b0 T disp_al_capture_exit
00000000400853b6 T disp_al_capture_sync
00000000400853da T disp_al_capture_apply
00000000400853de T disp_al_capture_get_status
00000000400853e2 T disp_al_lcd_get_clk_info
00000000400853fc T disp_al_lcd_cfg
00000000400854e6 T disp_al_lcd_cfg_ext
00000000400854f6 T disp_al_lcd_enable
000000004008554e T disp_al_lcd_disable
00000000400855ae T disp_al_lcd_query_irq
00000000400855ec T disp_al_lcd_enable_irq
0000000040085620 T disp_al_lcd_disable_irq
0000000040085654 T disp_al_lcd_tri_busy
000000004008568a T disp_al_lcd_tri_start
00000000400856ac T disp_al_lcd_io_cfg
000000004008570e T disp_al_lcd_get_cur_line
0000000040085732 T disp_al_lcd_get_start_delay
00000000400857ac T disp_al_tv_enable
00000000400857ca T disp_al_tv_disable
00000000400857fa T disp_al_tv_cfg
0000000040085896 T disp_al_tv_irq_enable
00000000400858a8 T disp_al_tv_irq_disable
00000000400858ba T disp_al_device_get_cur_line
00000000400858d0 T disp_al_device_get_start_delay
00000000400858ea T disp_al_device_query_irq
0000000040085906 T disp_al_device_get_status
000000004008591c T disp_init_al
0000000040085a60 T disp_al_show_builtin_patten
0000000040085a64 T de_peak_update_regs
0000000040085b78 T de_peak_init
0000000040085c28 T de_peak_enable
0000000040085c70 T de_peak_set_size
0000000040085cd4 T de_peak_set_window
0000000040085d9e T de_peak_set_para
0000000040085e3e T de_peak_info2para
0000000040085e8c T de_rtmx_update_regs
00000000400861d4 T de_rtmx_set_overlay_reg_base
000000004008622a T de_rtmx_init
0000000040086652 T de_rtmx_set_display_size
00000000400866c2 T de_rtmx_set_dbuff_rdy
00000000400866f0 T de_rtmx_set_enable
000000004008683c T de_rtmx_set_lay_cfg
0000000040086e72 T de_rtmx_set_lay_haddr
0000000040086fd8 T de_rtmx_get_3d_in_single_size
0000000040087016 T de_rtmx_get_3d_in
0000000040087592 T de_rtmx_get_li_addr_offset
00000000400875b0 T de_rtmx_set_lay_laddr
00000000400878b2 T de_rtmx_get_3d_out
0000000040087900 T de_rtmx_set_lay_fcolor
0000000040087a3c T de_rtmx_set_overlay_size
0000000040087bac T de_rtmx_set_palette
0000000040087c8c T de_rtmx_set_coarse_fac
0000000040087f0c T de_rtmx_set_pf_en
0000000040087fba T de_rtmx_set_pipe_cfg
0000000040088080 T de_rtmx_set_route
00000000400880d0 T de_rtmx_set_premul
0000000040088116 T de_rtmx_set_background_color
0000000040088162 T de_rtmx_set_blend_size
00000000400881ce T de_rtmx_set_blend_mode
00000000400882b0 T de_rtmx_set_blend_color
00000000400882ec T de_rtmx_set_outitl
000000004008832a T de_rtmx_extend_rect
00000000400883ca T de_rtmx_calc_chnrect
0000000040088558 T de_rtmx_get_premul_ctl
000000004008859c T de_rtmx_sync_hw
0000000040088750 T de_rtmx_get_display_size
000000004008877e T direct_show_after_vep
00000000400887a6 t de_vsu_calc_fir_coef
00000000400887e2 T de_vsu_update_regs
0000000040088966 T de_vsu_init
0000000040088b92 T de_vsu_set_para
0000000040088e76 T de_vsu_calc_scaler_para
0000000040089054 T de_vsu_sel_ovl_scaler_para
0000000040089176 T de_vsu_recalc_scale_para
00000000400891c8 T de_recalc_ovl_bld_for_scale
000000004008933e T de_feat_get_num_screens
000000004008934c T de_feat_get_num_devices
000000004008935a T de_feat_get_num_chns
000000004008936c T de_feat_get_num_vi_chns
000000004008937e T de_feat_get_num_ui_chns
000000004008939a T de_feat_get_num_layers
00000000400893fe T de_feat_get_num_layers_by_chn
0000000040089464 T de_feat_is_support_vep
00000000400894c8 T de_feat_is_support_vep_by_chn
000000004008952e T de_feat_is_support_smbl
0000000040089540 T de_feat_is_supported_output_types
0000000040089556 T de_feat_is_support_wb
0000000040089568 T de_feat_is_support_lbc_by_chn
00000000400895ce T de_feat_is_support_scale
0000000040089632 T de_feat_get_scale_linebuf
0000000040089644 T de_feat_get_tcon_type
000000004008965c T de_feat_get_tcon_index
000000004008969e T de_feat_init
00000000400896b2 T de_lti_update_regs
0000000040089716 T de_lti_init
0000000040089796 T de_lti_enable
00000000400897da T de_lti_set_size
000000004008983e T de_lti_set_window
000000004008990e T de_lti_set_para
00000000400899de T de_lti_info2para
00000000400899fc T de_fce_update_regs
0000000040089ae0 T de_fce_init
0000000040089c6e T de_fce_enable
0000000040089cb2 T de_fce_set_size
0000000040089d16 T de_fce_set_window
0000000040089de0 T de_fce_set_para
0000000040089ea8 T de_fce_get_hist
0000000040089f06 T de_fce_set_ce
0000000040089f4e T de_fce_info2para
000000004008a024 T de_hist_apply
000000004008a0b6 T de_hist_tasklet
000000004008a142 T de_ce_apply
000000004008a220 T de_ce_tasklet
000000004008a9d4 T de_ase_update_regs
000000004008aa38 T de_ase_init
000000004008aab4 T de_ase_enable
000000004008aaf8 T de_ase_set_size
000000004008ab5c T de_ase_set_window
000000004008ac24 T de_ase_set_para
000000004008ac68 T de_ase_info2para
000000004008ac8e T de_enhance_info2data
000000004008ad74 T de_enhance_set_size
000000004008af38 T de_enhance_demo_enable
000000004008af4c T de_enhance_sync
000000004008af50 T de_enhance_tasklet
000000004008afbe T de_enhance_update_regs
000000004008b01e T de_enhance_init
000000004008b0f6 T de_enhance_set_mode
000000004008b168 T de_enhance_apply
000000004008b2ec T de_enhance_set_format
000000004008b352 T de_smbl_init
000000004008b356 T de_ccsc_update_regs
000000004008b406 T de_ccsc_init
000000004008b58c T IntRightShift64
000000004008b5b2 T de_csc_coeff_calc
000000004008bbaa T de_ccsc_apply
000000004008bce4 T de_dcsc_apply
000000004008be62 T de_dcsc_get_config
000000004008be8e T de_dcsc_apply_ex
000000004008bff4 T de_dcsc_update_regs
000000004008c0b8 T de_dcsc_init
000000004008c266 T wb_ebios_set_reg_base
000000004008c27c T wb_ebios_init
000000004008c290 T wb_ebios_writeback_enable
000000004008c2a6 T wb_ebios_apply
000000004008c2c2 T wb_ebios_set_para
000000004008c720 T wb_ebios_update_regs
000000004008c738 T wb_ebios_get_status
000000004008c76a T wb_ebios_enableint
000000004008c782 T wb_input_select
000000004008c7b0 T de_clk_enable
000000004008c99c T de_clk_disable
000000004008cacc T de_clk_set_reg_base
000000004008cad8 T dsi_set_reg_base
000000004008caf8 T dsi_get_cur_line
000000004008cb38 T dsi_irq_enable
000000004008cb5c T dsi_irq_disable
000000004008cb80 T dsi_irq_query
000000004008cbb6 T dsi_inst_busy
000000004008cbc8 T dsi_start
000000004008cc58 T dsi_open
000000004008cc82 T dsi_close
000000004008ccb6 T dsi_mode_switch
000000004008ccba T dsi_gen_short_rd
000000004008ccbe T dsi_tri_start
000000004008ccd0 T dsi_comb_dphy_pll_set
000000004008ce6c T dsi_dphy_open
000000004008d03e T dsi_dphy_close
000000004008d09e T lvds_combphy_close
000000004008d0c2 T lvds_combphy_open
000000004008d138 T dsi_io_open
000000004008d148 T dsi_io_close
000000004008d2bc T dsi_clk_enable
000000004008d2d4 T dsi_ecc_pro
000000004008d494 T dsi_set_max_ret_size
000000004008d4e8 T dsi_dcs_rd
000000004008d632 T dsi_crc_pro
000000004008d678 T dsi_dcs_wr
000000004008d7ac T dsi_gen_wr
000000004008d89c T dsi_crc_pro_pd_repeat
000000004008d8e0 T dsi_cfg
000000004008e2a0 t tcon0_cfg_mode_auto
000000004008e4cc t tcon0_cfg_mode_tri
000000004008e754 T tcon0_out_to_gpio
000000004008e780 T tcon1_tv_clk_enable
000000004008e80c T tcon0_dsi_clk_enable
000000004008e82e T tcon_de_attach
000000004008e8da T get_tcon_type_by_de_index
000000004008e90c T tcon_top_set_reg_base
000000004008e91c T lvds_open
000000004008ec50 T lvds_close
000000004008ed00 T tcon_set_reg_base
000000004008ed10 T tcon_init
000000004008ed70 T tcon_exit
000000004008ed9c T tcon_irq_enable
000000004008edc0 T tcon_irq_disable
000000004008ede4 T tcon_irq_query
000000004008ee1a T tcon_get_start_delay
000000004008ee4c T tcon0_get_cpu_tri2_start_delay
000000004008ee5e T tcon_get_cur_line
000000004008ee92 T tcon_get_status
000000004008eee6 T tcon0_src_select
000000004008ef16 T tcon0_open
000000004008efcc T tcon0_close
000000004008f046 T tcon0_tri_busy
000000004008f05a T tcon0_cpu_set_auto_mode
000000004008f07e T tcon0_tri_start
000000004008f096 T tcon0_frm
000000004008f156 T tcon0_cfg
000000004008f616 T tcon0_cpu_16b_to_24b
000000004008f646 T tcon0_cpu_busy
000000004008f668 T tcon0_cpu_wr_24b_index
000000004008f6de T tcon0_cpu_wr_24b_data
000000004008f752 T tcon0_cpu_wr_24b
000000004008f774 T tcon0_cpu_wr_16b
000000004008f7a8 T tcon0_cpu_wr_16b_index
000000004008f7ca T tcon0_cpu_wr_16b_data
000000004008f7ec T tcon0_set_dclk_div
000000004008f80a T tcon1_open
000000004008f836 T tcon1_close
000000004008f860 T tcon1_cfg
000000004008faae T tcon1_hdmi_color_remap
000000004008fb7c T tcon1_yuv_range
000000004008fc4a T tcon1_set_timming
000000004008fcde T tcon1_src_select
000000004008fd26 T tcon_gamma
000000004008fd90 T tcon_cmap
000000004008fe9e T tcon0_cfg_ext
000000004008fece T tcon_show_builtin_patten
000000004008fee6 T plat_get_reg_base
000000004008ff10 T plat_get_irq_no
000000004008ff3a T plat_get_clk
000000004009000a T plat_get_clk_parent
0000000040090058 T plat_get_clk_from_id
00000000400900b6 T plat_get_rst_by_name
000000004009011c T disp_get_all_pin_property
0000000040090120 T tv_get_mode
0000000040090130 T tv_get_input_csc
0000000040090158 T tv_set_enhance_mode
000000004009015c t tve_top_clk_enable
000000004009018a T tv_hot_plugging_detect
00000000400901a0 t tve_clk_enable
00000000400901f0 T tv_mode_support
000000004009021e t tv_resync_pixel_store
0000000040090222 t tv_resync_line_store
0000000040090226 t tv_faketv_show
000000004009022a t tv_faketv_store
000000004009022e T tv_report_hpd_work
0000000040090260 T tv_detect_thread
00000000400902da T tv_detect_enable
000000004009032c T tv_detect_disable
000000004009035c T tv_mutex_lock
0000000040090360 T tv_mutex_unlock
0000000040090364 T tv_set_mode
00000000400903a6 T tv_get_video_timing_info
000000004009040e T tv_enable
0000000040090588 T tv_disable
00000000400905ca T tv_suspend
000000004009064a T tv_resume
00000000400906d6 T tv_init
0000000040090860 T tv_open
0000000040090864 T tv_release
0000000040090868 T tv_ioctl
000000004009086c T tv_module_init
00000000400908f8 T tv_probe
0000000040090a10 T tve_low_set_reg_base
0000000040090a26 T tve_low_set_top_reg_base
0000000040090a32 T tve_low_init
0000000040090aca T tve_low_dac_enable
0000000040090aee T tve_low_open
0000000040090b14 T tve_low_close
0000000040090b38 T tve_low_set_tv_mode
00000000400913c8 T tve_low_get_dac_status
00000000400913f6 T tve_low_dac_autocheck_enable
000000004009144a T tve_low_dac_autocheck_disable
0000000040091472 T tve_low_get_sid
00000000400914b0 T tve_adjust_resync
00000000400914ee T tve_low_enhance
000000004009163e t frame_get_rcq_mem_size
000000004009167c t g2d_mixer_frame_destory
00000000400916b8 t frame_get_reg_block_num
00000000400916f6 t g2d_mixer_mem_setup
0000000040091960 t g2d_mixer_apply
0000000040091a2a t g2d_mixer_task_destory
0000000040091ac4 t frame_mem_setup
0000000040091b1e T g2d_mixer_frame_apply
000000004009209e T g2d_mixer_idr_init
00000000400920bc T create_mixer_task
00000000400922d8 T g2d_mixer_get_inst
00000000400922ee T mixer_task_process
0000000040092322 t ovl_v_get_reg_block_num
000000004009232c t ovl_v_get_reg_block_num
0000000040092336 t ovl_v_get_reg
000000004009233c t ovl_v_get_reg
000000004009235a t ovl_v_set_block_dirty
0000000040092370 t ovl_v_set_block_dirty
000000004009239c t ovl_v_get_rcq_mem_size
00000000400923a2 t ovl_v_get_rcq_mem_size
00000000400923aa t ovl_v_rcq_setup
000000004009240a t ovl_v_get_reg_block
000000004009242a t ovl_v_get_reg_block
000000004009244c t ovl_v_destory
0000000040092476 T g2d_ovl_v_calc_coarse
0000000040092654 T g2d_ovl_v_fc_set
0000000040092688 T g2d_vlayer_set
00000000400928fc T g2d_vlayer_overlay_set
000000004009295e T g2d_ovl_v_submodule_setup
0000000040092a22 t ovl_u_destory
0000000040092a4c t ovl_u_rcq_setup
0000000040092b38 T g2d_ovl_u_fc_set
0000000040092b72 T g2d_uilayer_set
0000000040092cce T g2d_uilayer_overlay_set
0000000040092d30 T g2d_ovl_u_submodule_setup
0000000040092df0 T g2d_top_mem_pool_alloc
0000000040092e1a T g2d_top_reg_memory_alloc
0000000040092e4c T g2d_top_mem_pool_free
0000000040092e5c t scal_get_reg_block_num
0000000040092e66 t scal_get_reg
0000000040092e6c t scal_set_block_dirty
0000000040092e82 t g2d_vsu_calc_fir_coef
0000000040092ebe t scal_get_rcq_mem_size
0000000040092ec4 t scal_destory
0000000040092eee t scal_rcq_setup
0000000040092f4a t scal_get_reg_block
0000000040092f6c T g2d_vsu_para_set
0000000040093340 T g2d_scaler_submodule_setup
00000000400933fe t wb_get_reg_block_num
0000000040093408 t wb_get_reg
000000004009340e t wb_set_block_dirty
0000000040093424 t wb_get_rcq_mem_size
000000004009342a t wb_destory
0000000040093454 t wb_rcq_setup
00000000400934b0 t wb_get_reg_block
00000000400934d2 T g2d_wb_set
00000000400936a6 T g2d_wb_submodule_setup
000000004009376a t bld_get_reg_block_num
0000000040093774 t bld_get_reg
000000004009377a t bld_set_block_dirty
0000000040093790 t bld_get_rcq_mem_size
0000000040093796 T bld_rcq_setup
00000000400937f0 t bld_destory
000000004009381a t bld_get_reg_block
000000004009383a T bld_in_set
000000004009391c T bld_fc_set
0000000040093a1c T bld_ck_para_set
0000000040093a8c T bld_bk_set
0000000040093ab8 T bld_out_setting
0000000040093b2c T bld_set_rop_ctrl
0000000040093b62 T bld_cs_set
0000000040093bac T bld_csc_reg_set
0000000040093c70 T bld_porter_duff
0000000040093d22 T bld_rop2_set
0000000040093e48 T bld_rop3_set
0000000040093f70 T g2d_bld_submodule_setup
0000000040094030 T g2d_handle_irq
0000000040094068 T g2d_malloc
00000000400940b2 T g2d_free
00000000400940ba T g2d_mutex_lock
00000000400940be T g2d_mutex_unlock
00000000400940c2 T g2d_byte_cal
000000004009416a T cal_align
00000000400941f6 T g2d_image_check
00000000400942ae T g2d_blit_h
00000000400942ba T g2d_wait_cmd_finish
00000000400943d4 T g2d_clk_init
000000004009443c T g2d_clock_enable
000000004009448a T drv_g2d_init
00000000400944e0 T g2d_probe
0000000040094562 T g2d_ioctl_mutex_lock
0000000040094584 T g2d_ioctl_mutex_unlock
00000000400945a2 T sunxi_g2d_open
00000000400945e2 T sunxi_g2d_close
000000004009465e T sunxi_g2d_control
0000000040094878 T g2d_top_set_base
000000004009488e T g2d_mixer_scan_order_fun
00000000400948aa T g2d_bsp_open
00000000400948e8 T g2d_bsp_close
000000004009491a T g2d_bsp_reset
0000000040094944 T g2d_top_mixer_reset
0000000040094960 T g2d_top_rcq_irq_en
000000004009497a T g2d_top_rcq_update_en
0000000040094990 T g2d_top_rcq_task_irq_query
00000000400949b2 T g2d_top_set_rcq_head
00000000400949d0 T g2d_rot_set_base
00000000400949da T g2d_rot_irq_query
00000000400949fe T g2d_rotate_set_para
0000000040094e84 T g2d_lbc_calc_ctrl
0000000040094f7c T g2d_lbc_rot_set_para
000000004009541a t find_empty_slot
000000004009548a T id_alloc
000000004009567a T id_free
0000000040095848 T id_creat
00000000400958c6 t di_get_format_type
0000000040095966 T di_dev_set_reg_base
0000000040095970 T di_dev_get_ip_version
000000004009597e T di_dev_query_state_with_clear
000000004009599a T di_dev_apply
0000000040095eb4 T di_malloc
0000000040095f4a T di_free
0000000040095f54 T di_get_free_mem_index
0000000040095f76 T di_mem_request
000000004009604a T di_mem_release
00000000400960ba T di_dma_buf_alloc_map
0000000040096162 T di_dma_buf_unmap_free
00000000400961a4 T di_format_to_string
000000004009627c t di_client_get_fb.isra.1
0000000040096312 T di_client_process_fb
0000000040096574 T di_client_get_version
000000004009657a T di_client_set_timeout
00000000400965c0 T di_client_create
00000000400966c4 T di_client_destroy
000000004009673a T find_client_by_num
000000004009676c T di_client_mem_request
0000000040096790 T di_client_mem_release
00000000400967a6 t di_client_get_number
00000000400967b6 T sunxi_di_control
0000000040096926 T sunxi_di_open
000000004009699c T sunxi_di_release
00000000400969b6 t di_check_enable_device_locked
0000000040096ae4 t __wait_yeild_cpu
0000000040096b42 T atomic_set
0000000040096b72 T atomic_read
0000000040096ba2 t di_drv_wait2start
0000000040096d38 t di_drv_wait4finish
0000000040096ea8 t di_irq_handler
0000000040096fac T di_drv_get_version
0000000040096fd2 T di_mutex_lock
0000000040096fd6 T di_mutex_unlock
0000000040096fda T di_drv_is_valid_client
0000000040097052 T di_drv_client_inc
00000000400970fa T di_drv_client_dec
00000000400971b6 T di_drv_process_fb
0000000040097246 T find_client_by_name
00000000400972be T di_probe
0000000040097454 t _efuse_reg_read_key
00000000400974f8 T efuse_sram_read_key
000000004009750e T efuse_acl_ck
00000000400975da T efuse_search_key_by_name
0000000040097620 T efuse_read_chip_ver
000000004009763a T hal_efuse_read
000000004009770e T hal_efuse_get_security_mode
0000000040097722 T hal_efuse_get_chipid
0000000040097734 T hal_efuse_get_thermal_cdata
0000000040097746 T hal_efuse_get_chip_ver
0000000040097778 T hal_ths_init
000000004009784c T hal_ths_get_temp
000000004009789e T hal_mbus_pmu_get_value
000000004009794a T hal_mbus_pmu_enable
000000004009795a T mbus_get_cpu_ddr
0000000040097964 T mbus_get_gpu_ddr
0000000040097968 T mbus_get_rv_sys_ddr
0000000040097972 T mbus_get_mahb_ddr
0000000040097976 T mbus_get_dma0_ddr
000000004009797a T mbus_get_dma1_ddr
000000004009797e T mbus_get_ce_ddr
0000000040097988 T mbus_get_tvd_ddr
000000004009798c T mbus_get_csi_ddr
0000000040097990 T mbus_get_dsp_sys_ddr
0000000040097994 T mbus_get_g2d_ddr
000000004009799e T mbus_get_di_ddr
00000000400979a8 T mbus_get_iommu_ddr
00000000400979b2 T mbus_get_ve_ddr
00000000400979b6 T mbus_get_de_ddr
00000000400979ba T mbus_get_oth_ddr
00000000400979c4 T mbus_get_total_ddr
00000000400979ce T mbus_pmu_enable
00000000400979de T hal_free_coherent
00000000400979ea T hal_malloc_coherent
0000000040097a22 T hal_malloc_coherent_init
0000000040097a2e T hal_malloc_coherent_prot
0000000040097b22 T hal_malloc_coherent_non_cacheable
0000000040097b28 t hex_dump.constprop.2
0000000040097c06 t transfer
0000000040097d46 t cmd_spidev_test
0000000040098338 t transfer_slave_package
0000000040098438 t hex_dump.constprop.1
0000000040098516 t cmd_test_spi_slave
000000004009884a t spi_slave_driver_thread
00000000400989fe T sunxi_spi_init_slave_data
0000000040098a14 t cmd_spi_slave_driver
0000000040098cb0 T cmd_test_gpadc
0000000040098d26 t callback
0000000040098d3e t cmd_test_rtc
0000000040098e04 T usb_test_show_help
0000000040098e12 t usb_test_command
0000000040098eec T usb_test_is_otg
0000000040098f06 T usb_test_get_port
0000000040098f36 T usb_test_cmd_ed_test
0000000040098f96 T usb_test_cmd_phy_range
0000000040099098 T usb_test_cmd_hci
0000000040099180 T usb_test_cmd_debug
00000000400991d0 t disp_mem_release
0000000040099242 T disp_mem_getadr
0000000040099262 T disp_mem
0000000040099454 T parse_cmdline_and_alloc
0000000040099608 T disp_mem_clear
0000000040099618 T disp_layer_clear
000000004009967e T disp_layer_clear_all
00000000400996f8 T disp_layer_get_resolution
000000004009974a T disp_layer_cfg
00000000400999d6 T parse_cmdline_and_set_config
0000000040099e2e t show_ui_layer
0000000040099e84 T disp_layer_alpha_test
000000004009a020 t show_vi_layer
000000004009a072 T disp_layer_scal_test
000000004009a292 t show_layer
000000004009a34c T disp_layer_format_test
000000004009a932 T cal
000000004009a95c T lbc_test
000000004009aa88 T cmd_ths_gt
000000004009aae8 T hal_dcache_clean
000000004009aaec T hal_dcache_clean_invalidate
000000004009aaf0 T hal_dcache_invalidate
000000004009aaf4 T hal_thread_create
000000004009ab32 T hal_thread_start
000000004009ab64 T hal_thread_sleep
000000004009ab74 T hal_thread_stop
000000004009ab9a T hal_thread_resume
000000004009abd2 T hal_thread_suspend
000000004009ac0a T hal_thread_self
000000004009ac0e T hal_thread_scheduler_is_running
000000004009ac22 T hal_thread_tick_increase
000000004009ac26 T hal_request_irq
000000004009ac32 T hal_free_irq
000000004009ac36 T hal_enable_irq
000000004009ac46 T hal_disable_irq
000000004009ac4a T hal_interrupt_get_nest
000000004009ac5a T hal_interrupt_enable
000000004009ac5e T hal_interrupt_disable
000000004009ac62 T hal_interrupt_disable_irqsave
000000004009ac66 T hal_interrupt_enable_irqrestore
000000004009ac6a T hal_interrupt_enter
000000004009ac6e T hal_interrupt_leave
000000004009ac72 T hal_spin_lock_init
000000004009ac76 T hal_spin_lock_deinit
000000004009ac7a T hal_spin_lock
000000004009ac7e T hal_spin_unlock
000000004009ac82 T hal_spin_lock_irqsave
000000004009ac98 T hal_spin_unlock_irqrestore
000000004009acae T hal_enter_critical
000000004009acb2 T hal_exit_critical
000000004009acb6 T osal_timer_create
000000004009acc2 T osal_timer_delete
000000004009acd4 T osal_timer_start
000000004009ace6 T osal_timer_stop
000000004009acf8 T osal_timer_control
000000004009ad0a T hal_sleep
000000004009ad1a T hal_msleep
000000004009ad1e T hal_usleep
000000004009ad22 T hal_udelay
000000004009ad26 T hal_gettime_ns
000000004009ad4a T hal_sem_init
000000004009ad96 T hal_sem_deinit
000000004009adb0 T hal_sem_create
000000004009add4 T hal_sem_delete
000000004009ae20 T hal_sem_getvalue
000000004009ae96 T hal_sem_post
000000004009aed8 T hal_sem_timedwait
000000004009af20 T hal_sem_trywait
000000004009af26 T hal_sem_wait
000000004009af2c T hal_sem_clear
000000004009af78 T hal_mailbox_create
000000004009af82 T hal_mailbox_delete
000000004009af98 T hal_mailbox_send
000000004009afba T hal_mailbox_recv
000000004009afdc T hal_workqueue_create
000000004009afe0 T hal_workqueue_destroy
000000004009aff0 T hal_workqueue_dowork
000000004009b000 T hal_workqueue_cancel_work_sync
000000004009b010 T hal_malloc
000000004009b018 T hal_calloc
000000004009b024 T hal_realloc
000000004009b02c T hal_free
000000004009b030 T hal_malloc_align
000000004009b038 T hal_free_align
000000004009b03c T hal_mutex_init
000000004009b064 T hal_mutex_detach
000000004009b080 T hal_mutex_create
000000004009b0a0 T hal_mutex_delete
000000004009b0cc T hal_mutex_lock
000000004009b0f4 T hal_mutex_unlock
000000004009b114 T hal_mutex_timedwait
000000004009b134 T hal_event_create_initvalue
000000004009b144 T hal_event_delete
000000004009b15a T hal_event_wait
000000004009b16c T hal_event_set_bits
000000004009b182 T hal_event_get
000000004009b198 t autoremove_wake_function
000000004009b1ae T init_wait_entry
000000004009b1cc T prepare_to_wait_event
000000004009b222 T finish_wait
000000004009b278 T __hal_wake_up
000000004009b2e8 T hal_cfg_init
000000004009b2ec T hal_cfg_get_keyvalue
000000004009b2f0 T hal_cfg_get_gpiosec_keycount
000000004009b2f4 T hal_cfg_get_gpiosec_data
000000004009b2f8 T do_write_without_erase
000000004009b312 t do_erase_write_blk
000000004009b35c T part_control
000000004009b44a T part_read
000000004009b5b2 T blkpart_add_list
000000004009b5ca T get_part_by_name
000000004009b658 T _part_write
000000004009b850 T part_erase_before_write
000000004009b856 T part_erase_without_write
000000004009b85c T gpt_part_cnt
000000004009b878 T first_gpt_part
000000004009b8f4 T next_gpt_part
000000004009b952 t console_device_init
000000004009b956 t console_device_open
000000004009b95a t console_device_control
000000004009b95e t console_device_fops_open
000000004009b962 t console_device_fops_lseek
000000004009b966 t console_device_fops_getdents
000000004009b96a t new_line_format_write.isra.0
000000004009b9c0 t cli_console_check_is_in_list.part.1
000000004009ba0e t console_device_fops_flush
000000004009ba12 t console_device_close
000000004009ba16 t console_device_fops_close
000000004009ba1a T get_default_console
000000004009ba64 T get_global_console
000000004009ba98 T set_global_console
000000004009bad6 T get_current_console
000000004009bb06 T get_clitask_console
000000004009bb16 T cli_console_add_task_list_node
000000004009bb84 T cli_console_remove_task_list_node
000000004009bbdc T cli_console_read
000000004009bc34 t console_device_read
000000004009bc50 t console_device_fops_read
000000004009bc68 T cli_console_write
000000004009bd62 t console_device_write
000000004009bd7e t console_device_fops_write
000000004009bd96 T cli_console_init
000000004009be0a T cli_console_deinit
000000004009bf06 T cli_console_task_destory
000000004009bf2a T cli_console_current_task_destory
000000004009bf3a T cli_console_task_check_exit_flag
000000004009bf54 T multiple_console_init
000000004009c014 T cli_current_task
000000004009c018 T cli_task_get_console
000000004009c020 T cli_task_set_console
000000004009c05e T cli_console_is_irq_context
000000004009c062 T cli_task_clear_console
000000004009c066 T cli_console_lock
000000004009c078 T cli_console_unlock
000000004009c07e T cli_console_lock_init
000000004009c090 T null_console_write
000000004009c096 t null_console_dummy_cb
000000004009c09a T null_console_read
000000004009c0ac t uart_console_deinit
000000004009c0b0 T uart_console_write
000000004009c10e T uart_console_read
000000004009c122 t uart_console_init
000000004009c17e t _test_str_length
000000004009c19e T script_parser_init
000000004009c1f0 T script_parser_exit
000000004009c21e T script_parser_fetch
000000004009c3a2 T script_parser_subkey_count
000000004009c41a T script_parser_mainkey_count
000000004009c42a T script_parser_mainkey_get_gpio_count
000000004009c4dc T script_parser_mainkey_get_gpio_cfg
000000004009c606 T esCFG_Init
000000004009c628 T esCFG_GetKeyValue
000000004009c646 T esCFG_GetSecKeyCount
000000004009c65c T esCFG_GetSecCount
000000004009c670 T esCFG_GetGPIOSecKeyCount
000000004009c686 T esCFG_GetGPIOSecData
000000004009c6a2 T esCFG_Init_Ex
000000004009c730 T esCFG_Exit_Ex
000000004009c746 T esCFG_GetKeyValue_Ex
000000004009c7b8 T esCFG_GetSecKeyCount_Ex
000000004009c7c2 T esCFG_GetSecCount_Ex
000000004009c7cc T esCFG_GetGPIOSecKeyCount_Ex
000000004009c7d6 T esCFG_GetGPIOSecData_Ex
000000004009c7e0 T _isatty_r
000000004009c7f4 T __libc_init_array
000000004009c7f6 T _isatty
000000004009c804 T _mkdir_r
000000004009c846 T _rename_r
000000004009c87e T _stat_r
000000004009c8e4 T _unlink_r
000000004009c908 T remove
000000004009c92a T _gettimeofday_r
000000004009c9dc T settimeofday
000000004009ca24 T _malloc_r
000000004009ca42 T _realloc_r
000000004009ca62 T _calloc_r
000000004009ca84 T _free_r
000000004009ca8a T _exit
000000004009caa0 T _close_r
000000004009caea T _fstat_r
000000004009cb54 T _lseek_r
000000004009cbb6 T _open_r
000000004009cc16 T _read_r
000000004009ccca T _write_r
000000004009cdba T fioctrl
000000004009ce18 T _system
000000004009ce1a T _getpid_r
000000004009ce2a T _kill_r
000000004009ce34 T system_time_init
000000004009ce56 T libc_stdio_get_console
000000004009ce6a t crc32_0
000000004009cea0 T sunxi_env_open
000000004009cea4 T sunxi_env_close
000000004009cea8 T sunxi_env_read
000000004009ceac T sunxi_env_init
000000004009cf1a t envmatch
000000004009cf4a T sunxi_env_write
000000004009cf4e t flash_env_read.constprop.9
000000004009cfd6 T fw_getenv
000000004009d03a T fw_env_write
000000004009d1ac T fw_env_flush
000000004009d316 T fw_env_open
000000004009d4be T fw_env_close
000000004009d502 t cmd_printenv
000000004009d5e4 T cmd_setenv
000000004009d63a T sunxi_getenv
000000004009d682 T sunxi_setenv
000000004009d6c0 T sunxi_env_control
000000004009d6f2 T backtrace_exception
000000004009d6f6 T backtrace
000000004009d6fa T list_fd
000000004009d836 T dfs_init
000000004009d8e8 T dfs_lock
000000004009d932 T dfs_unlock
000000004009d93e T fd_new
000000004009da2e T fd_get
000000004009da98 T fd_put
000000004009db14 T fd_uninstall
000000004009db96 T dupfd
000000004009dc66 T dupfd2
000000004009dd1a T dfs_subdir
000000004009dd58 T dfs_normalize_path
000000004009deba T fd_is_open
000000004009df6e t cmd_show_mount_table
000000004009e002 T dfs_register
000000004009e09a T dfs_filesystem_lookup
000000004009e166 T dfs_mount
000000004009e2d4 T dfs_unmount
000000004009e37a T dfs_mkfs
000000004009e484 T dfs_statfs
000000004009e4aa T df
000000004009e580 t dfs_filesystem_check_mount_flag
000000004009e5a8 T dfs_file_open
000000004009e76a T dfs_file_close
000000004009e7a0 T dfs_file_ioctl
000000004009e7e6 T dfs_file_read
000000004009e81c T dfs_file_getdents
000000004009e840 T dfs_file_unlink
000000004009e8f4 T dfs_file_write
000000004009e962 t copyfile
000000004009ea32 T dfs_file_flush
000000004009ea92 T dfs_file_lseek
000000004009eafe T dfs_file_stat
000000004009ebfc t copydir
000000004009ed36 T dfs_file_rename
000000004009ee00 T dfs_file_truncate
000000004009ee6a T cat
000000004009ef66 T copy
000000004009f074 T mount
000000004009f09e T umount
000000004009f0a2 T dup
000000004009f0d6 T dup2
000000004009f112 T ftruncate
000000004009f154 T open
000000004009f1bc T close
000000004009f228 T read
000000004009f26e T write
000000004009f2b4 T lseek
000000004009f324 T rename
000000004009f340 T stat
000000004009f35c T fstat
000000004009f3a6 T fsync
000000004009f3d8 T fcntl
000000004009f426 T ioctl
000000004009f444 T statfs
000000004009f460 T mkdir
000000004009f4cc T rmdir
000000004009f4e8 T unlink
000000004009f4ec T opendir
000000004009f570 T readdir
000000004009f5f8 T rewinddir
000000004009f636 T closedir
000000004009f682 T chdir
000000004009f70a T access
000000004009f71e T getcwd
000000004009f74a t _poll_add
000000004009f7b0 t __wqueue_pollwake
000000004009f7c8 T poll
000000004009f902 t fdszero
000000004009f934 T select
000000004009fb36 T dfs_device_fs_mount
000000004009fb3a t dfs_device_fs_mmap
000000004009fba8 t dfs_device_fs_poll
000000004009fc16 t dfs_device_fs_lseek
000000004009fc8e T dfs_device_fs_ioctl
000000004009fcec T dfs_device_fs_read
000000004009fd56 T dfs_device_fs_write
000000004009fdc0 T dfs_device_fs_open
000000004009feb8 T dfs_device_fs_stat
000000004009ff5c T dfs_device_fs_getdents
00000000400a000c T dfs_device_fs_close
00000000400a0094 T devfs_init
00000000400a00ac t melis_vfs_mount
00000000400a00b2 t melis_vfs_unmount
00000000400a00ba t melis_fstat_time_to_libc_time
00000000400a00f8 t dfs_path_to_melis_path
00000000400a01a4 T melis_vfs_init
00000000400a01ec t melis_vfs_rename
00000000400a0294 t melis_vfs_unlink
00000000400a0318 t melis_vfs_statfs
00000000400a03c8 t melis_vfs_lseek64
00000000400a042a t melis_vfs_ftruncate
00000000400a0442 t melis_vfs_getdents
00000000400a0556 t melis_vfs_lseek
00000000400a05b2 t melis_vfs_flush
00000000400a05c8 t melis_vfs_write
00000000400a0626 t melis_vfs_read
00000000400a0684 t melis_vfs_ioctl
00000000400a069e t melis_vfs_close
00000000400a06b4 t melis_vfs_open
00000000400a0766 t melis_vfs_mkfs.isra.0
00000000400a0792 t melis_vfs_ntfs_mkfs
00000000400a07a2 t melis_vfs_exfat_mkfs
00000000400a07b2 t melis_vfs_fat_mkfs
00000000400a07c2 T melis_fstat_to_libc_stat
00000000400a0828 t melis_vfs_stat
00000000400a08a6 T dfs_rootfs_mount
00000000400a08aa T dfs_rootfs_unmount
00000000400a08ae T dfs_rootfs_statfs
00000000400a08c0 T dfs_rootfs_open
00000000400a0902 T dfs_rootfs_stat
00000000400a094c T dfs_rootfs_getdents
00000000400a0a1a T dfs_rootfs_init
00000000400a0a32 t dfs_rootfs_close
00000000400a0a36 t rt_list_len
00000000400a0a48 t show_wait_queue
00000000400a0a94 t list_get_next
00000000400a0b24 t object_split.constprop.2
00000000400a0b4e T list_thread
00000000400a0d08 T list_memheap
00000000400a0dcc T list_device
00000000400a0eb8 T list_mutex
00000000400a0f8e T list_timer
00000000400a1088 T list_event
00000000400a1198 T list_sem
00000000400a12b6 T list_msgqueue
00000000400a13d4 T list_mailbox
00000000400a14fa T list_mempool
00000000400a1606 T msh_help
00000000400a1688 T cmd_ps
00000000400a1698 T cmd_free
00000000400a16a8 T msh_is_used
00000000400a16ac T msh_get_cmd
00000000400a1736 t _msh_exec_cmd
00000000400a1898 T msh_exec
00000000400a190e T msh_auto_complete_path
00000000400a1ac4 T msh_auto_complete
00000000400a1bda T cmd_pwd
00000000400a1bfa T cmd_mkfs
00000000400a1c5a T cmd_cp
00000000400a1c90 T cmd_mv
00000000400a1d74 T cmd_cat
00000000400a1e3a T cmd_cd
00000000400a1e7e T cmd_mkdir
00000000400a1eb4 T cmd_df
00000000400a1f0a T cmd_echo
00000000400a1f82 T msh_exec_script
00000000400a210c T finsh_get_prompt
00000000400a21ba t shell_handle_history
00000000400a21f4 T finsh_thread_entry
00000000400a26ac T finsh_set_prompt_mode
00000000400a26ec T finsh_system_init
00000000400a2798 t fork_thread_entry
00000000400a27c4 t cmd_fork
00000000400a28d0 t cmd_panic
00000000400a28d6 t cmd_mmlk
00000000400a28f6 t cmd_top
00000000400a293c t cmd_reboot
00000000400a2978 t msh_backtrace
00000000400a29b2 t cmd_modify_mem_value
00000000400a2a16 t cmd_print_mem_value
00000000400a2ab6 t getdayofmonth
00000000400a2b10 t show_help
00000000400a2b30 t show_help
00000000400a2b6c t show_help
00000000400a2ba4 t show_help
00000000400a2c18 t cmd_date
00000000400a2d70 t cmd_exit
00000000400a2d80 t cmd_uninsmod
00000000400a2e72 t cmd_insmod
00000000400a2fda t cmd_send_key
00000000400a30d6 T cmd_update
00000000400a326c t mtop_post
00000000400a329a t cmd_mtop
00000000400a356a t mtop_read
00000000400a35b4 t mtop_start
00000000400a382e T cmd_ths_gt_status
00000000400a3886 T tail_do
00000000400a3972 T tail_main
00000000400a3aa6 t touch_main
00000000400a3b38 T grep_main
00000000400a3be8 t hexdump_do
00000000400a3e74 t hexdump_main
00000000400a3fc6 t hd_main
00000000400a403a t relative_path
00000000400a4078 t ls_show
00000000400a41ee t dir_list_wildcard
00000000400a4336 t ls_do
00000000400a4506 t ls_main
00000000400a4600 t ll_main
00000000400a4628 t rrmdir
00000000400a4750 t do_remove
00000000400a47a8 t cmd_rm
00000000400a4908 t get_path
00000000400a494c t get_sum_by_file
00000000400a4a5c t append_sum
00000000400a4b0e t do_check
00000000400a4c68 t rwcheck_main
00000000400a4f6c t remove_file_test
00000000400a517e t begin.constprop.1
00000000400a5340 T rwcheck_test_thread
00000000400a54ea t rwcheck_thread
00000000400a58a0 t cmd_umount
00000000400a58da t cmd_format
00000000400a592c t cmd_mount
00000000400a5a3a T wildcard_check
00000000400a5a56 T wild_char_match
00000000400a5bdc T clock_getres
00000000400a5c08 T clock_gettime
00000000400a5c78 T clock_time_to_tick
00000000400a5cfe T clock_settime
00000000400a5d42 T posix_mq_system_init
00000000400a5d5a t pthread_entry_stub
00000000400a5d72 T _pthread_get_data
00000000400a5db2 T _pthread_data_get_pth
00000000400a5df4 T _pthread_data_create
00000000400a5e82 T _pthread_data_destroy
00000000400a5eee t _pthread_destroy
00000000400a5f0c t _pthread_cleanup
00000000400a5f58 T pthread_system_init
00000000400a5f70 T pthread_create
00000000400a60a2 T pthread_detach
00000000400a6108 T pthread_join
00000000400a6162 T pthread_self
00000000400a618c T pthread_exit
00000000400a6228 T pthread_once
00000000400a628c T pthread_atfork
00000000400a6292 T pthread_kill
00000000400a6298 T pthread_cleanup_pop
00000000400a62fc T pthread_cleanup_push
00000000400a6366 T pthread_setcancelstate
00000000400a63c8 T pthread_setcanceltype
00000000400a6428 T pthread_testcancel
00000000400a647e T pthread_cancel
00000000400a64ec T pthread_setname_np
00000000400a654a T pthread_attr_init
00000000400a658e T pthread_attr_destroy
00000000400a65ca T pthread_attr_setdetachstate
00000000400a6606 T pthread_attr_getdetachstate
00000000400a663c T pthread_attr_setschedpolicy
00000000400a6670 T pthread_attr_getschedpolicy
00000000400a66a6 T pthread_attr_setschedparam
00000000400a66f6 T pthread_attr_getschedparam
00000000400a6746 T pthread_attr_setstacksize
00000000400a677a T pthread_attr_getstacksize
00000000400a67b0 T pthread_attr_setstackaddr
00000000400a67de T pthread_attr_getstackaddr
00000000400a680c T pthread_attr_setstack
00000000400a684c T pthread_attr_getstack
00000000400a688e T pthread_attr_setguardsize
00000000400a6894 T pthread_attr_getguardsize
00000000400a689a T pthread_attr_setscope
00000000400a68ac T pthread_attr_getscope
00000000400a68b0 T pthread_barrierattr_destroy
00000000400a68ba T pthread_barrierattr_init
00000000400a68c8 T pthread_barrierattr_getpshared
00000000400a68d6 T pthread_barrierattr_setpshared
00000000400a68da T pthread_barrier_destroy
00000000400a68e6 T pthread_barrier_init
00000000400a691e T pthread_barrier_wait
00000000400a6976 T pthread_condattr_destroy
00000000400a6980 T pthread_condattr_init
00000000400a698e T pthread_condattr_getclock
00000000400a6992 T pthread_condattr_setclock
00000000400a6996 T pthread_cond_init
00000000400a6a0e T pthread_cond_destroy
00000000400a6a68 T pthread_cond_broadcast
00000000400a6aba T pthread_cond_signal
00000000400a6ae8 T _pthread_cond_timedwait
00000000400a6b46 T pthread_cond_wait
00000000400a6b5a T pthread_cond_timedwait
00000000400a6b8e T pthread_mutexattr_init
00000000400a6b9c T pthread_mutexattr_destroy
00000000400a6baa T pthread_mutexattr_gettype
00000000400a6bc2 T pthread_mutexattr_settype
00000000400a6bdc T pthread_mutexattr_setpshared
00000000400a6c00 T pthread_mutexattr_getpshared
00000000400a6c14 T pthread_mutex_init
00000000400a6c80 T pthread_mutex_destroy
00000000400a6cbc T pthread_mutex_lock
00000000400a6d1e T pthread_mutex_unlock
00000000400a6d70 T pthread_mutex_trylock
00000000400a6dd4 T pthread_rwlockattr_init
00000000400a6de2 T pthread_rwlockattr_destroy
00000000400a6dec T pthread_rwlockattr_getpshared
00000000400a6dfe T pthread_rwlockattr_setpshared
00000000400a6e08 T pthread_rwlock_init
00000000400a6e46 T pthread_rwlock_destroy
00000000400a6ee2 T pthread_rwlock_rdlock
00000000400a6f64 T pthread_rwlock_tryrdlock
00000000400a6fbe T pthread_rwlock_timedrdlock
00000000400a7048 T pthread_rwlock_timedwrlock
00000000400a70c6 T pthread_rwlock_trywrlock
00000000400a7116 T pthread_rwlock_unlock
00000000400a719a T pthread_rwlock_wrlock
00000000400a7210 T pthread_spin_init
00000000400a721e T pthread_spin_destroy
00000000400a7228 T pthread_spin_lock
00000000400a723a T pthread_spin_trylock
00000000400a724e T pthread_spin_unlock
00000000400a7262 T pthread_spin_lock_irqsave
00000000400a7282 T pthread_spin_unlock_irqrestore
00000000400a7296 T pthread_key_system_init
00000000400a72a8 T pthread_getspecific
00000000400a7308 T pthread_setspecific
00000000400a737c T pthread_key_create
00000000400a73d8 T pthread_key_delete
00000000400a7414 t posix_sem_find
00000000400a744a t posix_sem_delete
00000000400a749c T posix_sem_system_init
00000000400a74b4 T sem_close
00000000400a7508 T sem_destroy
00000000400a7592 T sem_unlink
00000000400a75ec T sem_getvalue
00000000400a760e T sem_init
00000000400a7694 T sem_open
00000000400a7766 T sem_post
00000000400a7784 T sem_timedwait
00000000400a77c6 T sem_trywait
00000000400a77f6 T sem_wait
00000000400a781c W _init
00000000400a781e W _fini
00000000400a7820 W cplusplus_system_init
00000000400a7850 T mmap
00000000400a78bc T munmap
00000000400a78c0 t __ffs
00000000400a78f4 t __ffs
00000000400a7928 t _find_next_bit.part.0
00000000400a797e T find_next_zero_bit
00000000400a7990 T find_first_zero_bit
00000000400a79ce t __media_device_unregister_entity
00000000400a7a82 T media_device_register_entity
00000000400a7b92 T media_device_unregister_entity
00000000400a7b9c t media_add_link
00000000400a7bd6 t bitmap_zero.part.1
00000000400a7be8 T __media_entity_enum_init
00000000400a7c2e T media_entity_enum_cleanup
00000000400a7c34 T media_gobj_create
00000000400a7ce0 T media_gobj_destroy
00000000400a7d02 t __media_entity_remove_link
00000000400a7dce T media_entity_graph_walk_init
00000000400a7dda T media_entity_graph_walk_cleanup
00000000400a7de2 T __media_entity_remove_links
00000000400a7e22 T media_devnode_create
00000000400a7e88 T media_create_intf_link
00000000400a7ece T __media_remove_intf_link
00000000400a7efe T __media_remove_intf_links
00000000400a7f2c T media_remove_intf_links
00000000400a7f36 T media_devnode_remove
00000000400a7f54 t v4l2_mmap
00000000400a7fa8 t v4l2_poll
00000000400a8000 t v4l2_write
00000000400a8074 t v4l2_read
00000000400a80e8 t v4l2_close
00000000400a815e t v4l2_open
00000000400a8266 t v4l2_ioctl
00000000400a82f6 t clear_bit
00000000400a832e t clear_bit
00000000400a8358 t clear_bit
00000000400a8382 t clear_bit
00000000400a83ac t set_bit
00000000400a83e0 t set_bit
00000000400a8406 t set_bit
00000000400a842c t set_bit
00000000400a8452 t set_bit
00000000400a8478 t videodev_init
00000000400a849a T video_device_alloc
00000000400a84c0 T video_device_release
00000000400a84c4 T video_device_release_empty
00000000400a84c6 T video_devdata
00000000400a84f8 T v4l2_prio_init
00000000400a8502 T v4l2_prio_change
00000000400a854c T v4l2_prio_max
00000000400a8568 T v4l2_prio_check
00000000400a8584 T __video_register_device
00000000400a8e46 T video_unregister_device
00000000400a8f1e t v4l_print_querycap
00000000400a8f20 t check_ext_ctrls
00000000400a8f84 t v4l_s_output
00000000400a8f92 t v4l_streamon
00000000400a8fa2 t v4l_streamoff
00000000400a8fb2 t v4l_overlay
00000000400a8fc0 t v4l_g_crop
00000000400a903e t v4l_s_crop
00000000400a90b8 t v4l_cropcap
00000000400a91a6 t v4l_subscribe_event
00000000400a91b0 t v4l_unsubscribe_event
00000000400a91ba t __v4l_vidioc_g_fbuf_fnc
00000000400a91c8 t __v4l_vidioc_s_fbuf_fnc
00000000400a91d6 t __v4l_vidioc_expbuf_fnc
00000000400a91e4 t __v4l_vidioc_g_std_fnc
00000000400a91f2 t __v4l_vidioc_g_input_fnc
00000000400a9200 t __v4l_vidioc_g_output_fnc
00000000400a920e t __v4l_vidioc_g_selection_fnc
00000000400a921c t __v4l_vidioc_s_selection_fnc
00000000400a922a t __v4l_vidioc_enum_framesizes_fnc
00000000400a9238 t __v4l_vidioc_enum_frameintervals_fnc
00000000400a9246 t __v4l_vidioc_g_enc_index_fnc
00000000400a9254 t __v4l_vidioc_encoder_cmd_fnc
00000000400a9262 t __v4l_vidioc_try_encoder_cmd_fnc
00000000400a9270 t __v4l_vidioc_decoder_cmd_fnc
00000000400a927e t __v4l_vidioc_try_decoder_cmd_fnc
00000000400a928c t check_fmt
00000000400a9354 t v4l_log_status
00000000400a937c t v4l_enumoutput
00000000400a93bc t v4l_enuminput
00000000400a93fc t v4l_querycap
00000000400a946a t v4l_g_fmt
00000000400a95fc t v4l_dqevent
00000000400a960a t v4l_querystd
00000000400a9654 t v4l_s_std
00000000400a96ac t v4l_s_priority
00000000400a96e4 t v4l_g_priority
00000000400a9704 t v4l_enum_fmt
00000000400aa5ac t v4l_prepare_buf
00000000400aa5ee t v4l_s_parm
00000000400aa630 t v4l_dqbuf
00000000400aa672 t v4l_qbuf
00000000400aa6b4 t v4l_querybuf
00000000400aa6f6 t v4l_query_ext_ctrl
00000000400aa75c t v4l_sanitize_format
00000000400aa780 t v4l_try_fmt
00000000400aa968 t v4l_s_fmt
00000000400aab78 t v4l_create_bufs
00000000400aabde t v4l_g_sliced_vbi_cap
00000000400aac30 t v4l_reqbufs
00000000400aac76 t v4l_s_hw_freq_seek
00000000400aacd6 t v4l_s_input
00000000400aad12 t v4l_try_ext_ctrls
00000000400aad98 t v4l_s_ext_ctrls
00000000400aae1c t v4l_g_ext_ctrls
00000000400aaea2 t v4l_querymenu
00000000400aaf08 t v4l_queryctrl
00000000400aaf6e t v4l_s_ctrl
00000000400aaff8 t v4l_g_ctrl
00000000400ab092 t v4l_print_default
00000000400ab094 t v4l_print_enuminput
00000000400ab096 t v4l_print_enumoutput
00000000400ab098 t v4l_print_fmtdesc
00000000400ab09a t v4l_print_format
00000000400ab09c t v4l_print_framebuffer
00000000400ab09e t v4l_print_buftype
00000000400ab0a0 t v4l_print_standard
00000000400ab0a2 t v4l_print_std
00000000400ab0a4 t v4l_print_hw_freq_seek
00000000400ab0a6 t v4l_print_requestbuffers
00000000400ab0a8 t v4l_print_buffer
00000000400ab0aa t v4l_print_exportbuffer
00000000400ab0ac t v4l_print_create_buffers
00000000400ab0ae t v4l_print_streamparm
00000000400ab0b0 t v4l_print_queryctrl
00000000400ab0b2 t v4l_print_query_ext_ctrl
00000000400ab0b4 t v4l_print_querymenu
00000000400ab0b6 t v4l_print_control
00000000400ab0b8 t v4l_print_ext_controls
00000000400ab0ba t v4l_print_cropcap
00000000400ab0bc t v4l_print_crop
00000000400ab0be t v4l_print_selection
00000000400ab0c0 t v4l_print_enc_idx
00000000400ab0c2 t v4l_print_encoder_cmd
00000000400ab0c4 t v4l_print_decoder_cmd
00000000400ab0c6 t v4l_print_frmsizeenum
00000000400ab0c8 t v4l_print_frmivalenum
00000000400ab0ca t v4l_print_event
00000000400ab0cc t v4l_print_event_subscription
00000000400ab0ce t v4l_print_sliced_vbi_cap
00000000400ab0d0 t v4l_print_u32
00000000400ab0d2 t v4l_print_newline
00000000400ab0d4 T v4l2_video_std_frame_period
00000000400ab0f4 t v4l_g_parm
00000000400ab166 T v4l2_video_std_construct
00000000400ab1b0 t v4l_enumstd
00000000400ab238 T v4l2_is_known_ioctl
00000000400ab260 t __video_do_ioctl
00000000400ab3ba T v4l2_ioctl_get_lock
00000000400ab408 T video_usercopy
00000000400ab424 T video_ioctl2
00000000400ab430 T v4l2_device_register
00000000400ab474 T v4l2_device_put
00000000400ab478 T v4l2_device_unregister_subdev
00000000400ab4d2 T v4l2_device_unregister
00000000400ab51c t v4l2_event_subscribed
00000000400ab53a t __v4l2_event_dequeue
00000000400ab5ca t __v4l2_event_queue_fh.isra.2
00000000400ab744 T v4l2_event_dequeue
00000000400ab7aa T v4l2_event_queue_fh
00000000400ab7e8 T v4l2_event_pending
00000000400ab7ec t is_cur_manual
00000000400ab808 t check_range
00000000400ab8cc t validate_new
00000000400ab902 t find_ref
00000000400ab97e t find_ref_lock
00000000400ab986 t prepare_ext_ctrls
00000000400abad0 t std_init
00000000400abb7e t std_validate
00000000400abcbe t ptr_to_user
00000000400abd38 t new_to_user
00000000400abd3e t cur_to_user
00000000400abd44 t def_to_user
00000000400abd7e t user_to_ptr
00000000400abe5c t std_equal
00000000400abf08 t std_log
00000000400abf0a t class_check
00000000400abf42 t cur_to_new
00000000400abf5a t update_from_auto_cluster
00000000400abfae t get_ctrl
00000000400ac038 t fill_event
00000000400ac0a4 t send_event
00000000400ac0f2 t try_or_set_cluster
00000000400ac2ce t try_set_ext_ctrls
00000000400ac4e0 t set_ctrl
00000000400ac57e T v4l2_ctrl_get_name
00000000400ad46a T v4l2_ctrl_fill
00000000400ada88 T v4l2_ctrl_handler_init_class
00000000400adae0 T v4l2_ctrl_handler_free
00000000400adbac T v4l2_ctrl_find
00000000400adbbe T v4l2_ctrl_new_std
00000000400adc5a t handler_new_ref
00000000400add62 t v4l2_ctrl_new.part.7
00000000400ae08a T v4l2_query_ext_ctrl
00000000400ae1ea T v4l2_queryctrl
00000000400ae262 T v4l2_querymenu
00000000400ae2de T v4l2_g_ext_ctrls
00000000400ae45e T v4l2_g_ctrl
00000000400ae490 T v4l2_try_ext_ctrls
00000000400ae49c T v4l2_s_ext_ctrls
00000000400ae4a2 T v4l2_s_ctrl
00000000400ae51c T v4l_enable_media_source
00000000400ae542 T v4l_vb2q_enable_media_source
00000000400ae552 t __vb2_buf_mem_free
00000000400ae58c t __enqueue_in_driver
00000000400ae5f4 t __qbuf_userptr
00000000400ae7c4 t __vb2_queue_alloc
00000000400ae9d0 t __buf_prepare
00000000400aea56 t __vb2_queue_free
00000000400aeb64 T vb2_buffer_in_use
00000000400aebbc T vb2_core_querybuf
00000000400aebda T vb2_verify_memory_type
00000000400aec5a T vb2_plane_vaddr
00000000400aec86 T vb2_plane_cookie
00000000400aecb2 T vb2_buffer_done
00000000400aede4 t __vb2_queue_cancel
00000000400aeeb2 T vb2_core_reqbufs
00000000400af046 t vb2_start_streaming
00000000400af0e8 T vb2_core_qbuf
00000000400af1b6 T vb2_core_dqbuf
00000000400af316 T vb2_core_streamon
00000000400af37c t __vb2_init_fileio
00000000400af4d6 T vb2_core_streamoff
00000000400af50e t __vb2_cleanup_fileio
00000000400af54a t __vb2_perform_fileio
00000000400af744 T vb2_mmap
00000000400af812 T vb2_core_queue_init
00000000400af888 T vb2_core_queue_release
00000000400af8c0 T vb2_core_poll
00000000400af9e4 T vb2_read
00000000400af9ea t __fill_v4l2_buffer
00000000400afbc8 t __verify_planes_array.isra.1
00000000400afbf2 t __verify_planes_array_core
00000000400afbf8 t __copy_timestamp
00000000400afc4c t vb2_queue_or_prepare_buf.isra.4
00000000400afc80 t vb2_queue_is_busy.isra.5
00000000400afc92 t __fill_vb2_buffer
00000000400aff38 T vb2_querybuf
00000000400aff8a T vb2_qbuf
00000000400affbc T vb2_dqbuf
00000000400afff2 T vb2_streamon
00000000400b0000 T vb2_streamoff
00000000400b000e T vb2_queue_init
00000000400b0092 T vb2_queue_release
00000000400b0096 T vb2_poll
00000000400b010e T vb2_ioctl_reqbufs
00000000400b0178 T vb2_ioctl_querybuf
00000000400b0192 T vb2_ioctl_qbuf
00000000400b01d0 T vb2_ioctl_dqbuf
00000000400b0218 T vb2_ioctl_streamon
00000000400b0256 t vb2_dc_cookie
00000000400b025c t vb2_dc_vaddr
00000000400b0260 t vb2_dc_num_users
00000000400b0264 t vb2_dc_mmap
00000000400b026c t vb2_dc_put_userptr
00000000400b028a t vb2_dc_prepare
00000000400b02a2 t vb2_dc_put
00000000400b02cc t vb2_dc_get_userptr
00000000400b0322 t vb2_dc_alloc
00000000400b03a6 t vb2_dc_finish
00000000400b03ba t queue_setup
00000000400b041a t vidioc_enum_input
00000000400b0428 t vidioc_enum_framesizes
00000000400b046a t buffer_queue
00000000400b04ae t tvd_lock
00000000400b04ba t tvd_unlock
00000000400b04c4 t video_drvdata
00000000400b04d6 t vidioc_s_parm
00000000400b04fe t vidioc_g_parm
00000000400b0526 t vidioc_g_input
00000000400b0540 t vidioc_s_input
00000000400b055e t __tvd_clk_init
00000000400b062a t vidioc_enum_fmt_vid_cap
00000000400b0672 t vidioc_querycap
00000000400b06cc t tvd_mmap
00000000400b06e4 t tvd_poll
00000000400b0716 t tvd_read
00000000400b0754 t tvd_s_ctrl
00000000400b090e t tvd_g_volatile_ctrl
00000000400b0992 t __get_status
00000000400b0a72 t vidioc_try_fmt_vid_cap
00000000400b0ac8 t vidioc_g_fmt_vid_cap
00000000400b0be0 t vidioc_s_fmt_vid_cap
00000000400b12a0 t tvd_module_init
00000000400b1604 t clear_bit.constprop.13
00000000400b1620 t vidioc_streamoff
00000000400b16f6 t set_bit.constprop.14
00000000400b1714 t tvd_open
00000000400b1902 t __tvd_set_addr
00000000400b19d4 t vidioc_streamon
00000000400b1ac4 t start_streaming
00000000400b1ada t stop_streaming
00000000400b1b4a t buffer_prepare
00000000400b1baa t tvd_close
00000000400b1d0a T tvd_valid_frame_setting
00000000400b1e54 t tvd_isr
00000000400b1f3e T tvd_top_set_reg_base
00000000400b1f4a T tvd_set_reg_base
00000000400b1f64 T tvd_3d_mode
00000000400b1fc2 T tvd_enable_chanel
00000000400b1fda T tvd_input_sel
00000000400b1ff4 T tvd_reset
00000000400b2024 T tvd_blue_display_mode
00000000400b2042 T tvd_get_status
00000000400b207e T tvd_config
00000000400b2ad6 T tvd_set_wb_width
00000000400b2af4 T tvd_set_wb_width_jump
00000000400b2b1a T tvd_set_wb_height
00000000400b2b40 T tvd_set_wb_addr
00000000400b2b5c T tvd_set_wb_fmt
00000000400b2ba8 T tvd_set_wb_uv_swap
00000000400b2bca T tvd_capture_on
00000000400b2be2 T tvd_capture_off
00000000400b2bf8 T tvd_irq_enable
00000000400b2c18 T tvd_irq_disable
00000000400b2c3e T tvd_irq_status_get
00000000400b2c5a T tvd_get_lock
00000000400b2c9a T tvd_irq_status_clear
00000000400b2cb4 T tvd_dma_irq_status_get
00000000400b2cca T tvd_dma_irq_status_clear_err_flag
00000000400b2cde T tvd_set_saturation
00000000400b2cf6 T tvd_set_sharpness
00000000400b2e10 T tvd_set_hue
00000000400b2e3a T tvd_set_luma
00000000400b2e52 T tvd_set_contrast
00000000400b2e6a T tvd_get_saturation
00000000400b2e7c T tvd_get_sharpness
00000000400b2e9a T tvd_get_hue
00000000400b2eb0 T tvd_get_luma
00000000400b2ec2 T tvd_get_contrast
00000000400b2ed4 T tvd_adc_config
00000000400b2fe2 T tvd_init
00000000400b3058 T tvd_deinit
00000000400b30a0 T tvd_agc_auto_config
00000000400b30ec T tvd_cagc_config
00000000400b310c T UTF8_uni2char
00000000400b3172 T UTF8_char2uni
00000000400b31d6 T UTF8_get_charset2upper_table
00000000400b3210 T UTF8_get_charset2lower_table
00000000400b324a T eschs_init
00000000400b3274 T esCHS_Uni2Char
00000000400b329e T esCHS_Char2Uni
00000000400b32c8 T esCHS_GetChUpperTbl
00000000400b32ea T esCHS_GetChLowerTbl
00000000400b330c T check_ccu_clk_type
00000000400b3322 T esCLK_SetSrcFreq
00000000400b336c T esCLK_GetSrcFreq
00000000400b33b4 T esCLK_OpenMclk
00000000400b33f6 T esCLK_CloseMclk
00000000400b3438 T esCLK_MclkRegCb
00000000400b343c T esCLK_MclkUnregCb
00000000400b3440 T esCLK_SetMclkSrc
00000000400b34c4 T esCLK_GetMclkSrc
00000000400b350e T esCLK_GetRound_Rate
00000000400b3560 T esCLK_SetMclkDiv
00000000400b3564 T esCLK_GetMclkDiv
00000000400b3568 T esCLK_MclkOnOff
00000000400b356c T esCLK_MclkAssert
00000000400b359c T esCLK_MclkDeassert
00000000400b35cc T esCLK_MclkReset
00000000400b35fc T esCLK_MclkGetRstStatus
00000000400b362c T esCLK_SysInfo
00000000400b362e T esCLK_ModInfo
00000000400b3630 T esDMA_Request
00000000400b3648 T esDMA_Release
00000000400b3656 T esDMA_Setting
00000000400b36d0 T esDMA_Start
00000000400b36de T esDMA_Stop
00000000400b36ec T esDMA_Restart
00000000400b36f0 T esDMA_QueryStat
00000000400b3702 T esDMA_QuerySrc
00000000400b3706 T esDMA_QueryDst
00000000400b370a T esDMA_EnableINT
00000000400b370e T esDMA_DisableINT
00000000400b3712 T esDMA_QueryRestCount
00000000400b3728 T esDMA_ChangeMode
00000000400b372c T esDMA_RegDmaHdler
00000000400b373c T esDMA_UnregDmaHdler
00000000400b3740 T esDMA_Information
00000000400b3742 T PIN_CheckPinHdle
00000000400b3780 T esPINS_PinGrpReq
00000000400b3834 T esPINS_PinGrpRel
00000000400b38de T esPINS_GetPinGrpStat
00000000400b38f8 T esPINS_GetPinStat
00000000400b39be T esPINS_SetPinStat
00000000400b3a44 T esPINS_SetPinIO
00000000400b3a8e T esPINS_SetPinPull
00000000400b3ad8 T esPINS_SetPinDrive
00000000400b3b22 T esPINS_ReadPinData
00000000400b3b68 T esPINS_WritePinData
00000000400b3bba T esPINS_SetIntMode
00000000400b3bec T esPINS_RegIntHdler
00000000400b3c28 T esPINS_UnregIntHdler
00000000400b3c5e T esPINS_EnbaleInt
00000000400b3cb6 T esPINS_DisbaleInt
00000000400b3d04 T esPINS_QueryInt
00000000400b3d1e T esPINS_ClearPending
00000000400b3d22 T input_init
00000000400b3d50 t INPUT_DeattachLdev
00000000400b3d76 t INPUT_Lock
00000000400b3d9e t INPUT_Unlock
00000000400b3dac T INPUT_CoreInit
00000000400b3de0 T INPUT_CoreExit
00000000400b3e20 T INPUT_GetLdevPos
00000000400b3e7e T esINPUT_RegLdev
00000000400b3ef2 T esINPUT_UnregLdev
00000000400b3f3a T esINPUT_LdevGrab
00000000400b3fa6 T esINPUT_LdevRelease
00000000400b3ff8 T esINPUT_GetLdevID
00000000400b4006 T INPUT_SetRepPeriod
00000000400b40b6 T esINPUT_LdevCtl
00000000400b40ce T esINPUT_RegDev
00000000400b4322 T esINPUT_UnregDev
00000000400b436e t __change_bit
00000000400b4384 t test_bit
00000000400b4394 T IsEventSupported
00000000400b43ae T esINPUT_SendEvent
00000000400b45ce T INPUT_RepeatKey
00000000400b465c T esINPUT_LdevFeedback
00000000400b46f0 t LKeyDevOpen
00000000400b46f4 t LKeyDevClose
00000000400b46f8 t LKeyDevRead
00000000400b46fc t LKeyDevIoctrl
00000000400b4700 t LKeyDevEvent
00000000400b481a t LKeyDevGraberThread
00000000400b4958 t LKeyDevWrite
00000000400b495c T INPUT_LKeyDevInit
00000000400b4a30 T INPUT_LKeyDevExit
00000000400b4a5a T console_LKeyDevEvent
00000000400b4a92 t LTSDevOpen
00000000400b4a96 t LTSDevClose
00000000400b4a9a t LTSDevRead
00000000400b4a9e t LTSDevIoctrl
00000000400b4aa2 t LTSDevEvent
00000000400b4bd4 t LTSDevGraberThread
00000000400b4d12 t LTSDevWrite
00000000400b4d16 T INPUT_LTSDevInit
00000000400b4dec T esINT_SetIRQPrio
00000000400b4df0 T esINT_EnableINT
00000000400b4e00 T esINT_DisableINT
00000000400b4e10 T esINT_InsISR
00000000400b4e56 T esINT_UniISR
00000000400b4e7e T esINT_InsFIR
00000000400b4e82 T esINT_UniFIR
00000000400b4e86 T sram_switch_mapping
00000000400b4f3c T esMEM_SramSwitchBlk
00000000400b4f8c T esMEM_SramReqBlk
00000000400b4ffc T esMEM_SramRelBlk
00000000400b503e T esMEM_RegDramAccess
00000000400b5042 T esMEM_UnRegDramAccess
00000000400b5046 T LDR_LoadELFFile
00000000400b51be T LDR_GetELFFileSecROMHdr
00000000400b520a T LDR_GetELFFileSecData
00000000400b526e T LDR_LoadFile
00000000400b534e T LDR_UnloadFile
00000000400b5372 T LDR_GetFileType
00000000400b537c T LDR_GetMagicIndex
00000000400b5386 T LDR_GetSecNumber
00000000400b5390 T LDR_GetSecData
00000000400b53d2 T LDR_GetSecROMHdr
00000000400b5414 T MODS_GetMagicData
00000000400b5444 T MODS_LoadSectionData
00000000400b5584 T MODS_SectionVMBitMapSet
00000000400b560a T MODS_DestoryModuleVM
00000000400b5656 T MODS_CreateModuleVM
00000000400b56de T MODS_GetModuleID
00000000400b571e T esMODS_MInstall
00000000400b588a T MODS_NotifyUnInstall
00000000400b5890 T esMODS_MUninstall
00000000400b58d6 T esMODS_MOpen
00000000400b58ec T esMODS_MClose
00000000400b590a T esMODS_MRead
00000000400b5920 T esMODS_MWrite
00000000400b5936 T esMODS_MIoctrl
00000000400b5952 t find_thread
00000000400b59ae t __thread_del_req
00000000400b5a3c t __task_del
00000000400b5aa4 T esKRNL_CallBack
00000000400b5ab4 T esKRNL_FlagAccept
00000000400b5aec T esKRNL_FlagCreate
00000000400b5b6a T esKRNL_FlagDel
00000000400b5baa T esKRNL_FlagNameGet
00000000400b5bb0 T esKRNL_FlagNameSet
00000000400b5bb4 T esKRNL_FlagPend
00000000400b5c34 T esKRNL_FlagPendGetFlagsRdy
00000000400b5c3a T esKRNL_FlagPost
00000000400b5c72 T esKRNL_FlagQuery
00000000400b5c8e T esKRNL_GetCallBack
00000000400b5c90 T esKRNL_GetTIDCur
00000000400b5cf4 T esKRNL_InterruptDisable
00000000400b5cf8 T esKRNL_InterruptEnable
00000000400b5cfc T esKRNL_MboxAccept
00000000400b5d16 T esKRNL_MboxCreate
00000000400b5d4e T esKRNL_MboxDel
00000000400b5d88 T esKRNL_MboxPend
00000000400b5e04 T esKRNL_MboxPost
00000000400b5e24 T esKRNL_MboxPostOpt
00000000400b5e2a T esKRNL_MboxQuery
00000000400b5e30 T esKRNL_MemLeakChk
00000000400b5e58 T esKRNL_MutexCreate
00000000400b5e88 T esKRNL_MutexDel
00000000400b5ec2 T esKRNL_MutexPend
00000000400b5efe T esKRNL_MutexPost
00000000400b5f28 T esKRNL_QAccept
00000000400b5f66 T esKRNL_QCreate
00000000400b5f8e T esKRNL_QDel
00000000400b5fca T esKRNL_QFlush
00000000400b5fe4 T esKRNL_QPend
00000000400b6056 T esKRNL_QPost
00000000400b607e T esKRNL_QPostFront
00000000400b6084 T esKRNL_QPostOpt
00000000400b608a T esKRNL_QQuery
00000000400b60b6 T esKRNL_SchedLock
00000000400b60ba T esKRNL_SchedUnlock
00000000400b60be T esKRNL_SemAccept
00000000400b60d6 T esKRNL_SemCreate
00000000400b60fc T esKRNL_SemDel
00000000400b6140 T esKRNL_SemPend
00000000400b61a2 T esKRNL_SemPost
00000000400b61c2 T esKRNL_SemQuery
00000000400b61f8 T esKRNL_SemSet
00000000400b621a T esKRNL_SktAccept
00000000400b6268 T esKRNL_SktCreate
00000000400b641e T esKRNL_SktDel
00000000400b6482 T esKRNL_SktPend
00000000400b64ca T esKRNL_SktPost
00000000400b64f4 T aw_check_melis_task_exit
00000000400b6566 T esKRNL_TaskPrefEn
00000000400b6570 T esKRNL_TaskQuery
00000000400b65d4 T esKRNL_TaskNameSet
00000000400b6632 T esKRNL_TaskResume
00000000400b66c6 T esKRNL_TaskSuspend
00000000400b677c T esKRNL_TCreate
00000000400b6968 T esKRNL_TDel
00000000400b696c T esKRNL_TDelReq
00000000400b6970 T esKRNL_Time
00000000400b6980 T esKRNL_TimeDly
00000000400b6984 T esKRNL_TimeDlyResume
00000000400b69fe T esKRNL_TimeGet
00000000400b6a0e T esKRNL_TimeSet
00000000400b6a16 T esKRNL_TmrCreate
00000000400b6a9c T esKRNL_TmrDel
00000000400b6ae0 T esKRNL_TmrError
00000000400b6ae8 T esKRNL_TmrRemainGet
00000000400b6aee T esKRNL_TmrStart
00000000400b6b32 T esKRNL_TmrStateGet
00000000400b6b6e T esKRNL_TmrStop
00000000400b6bb4 T esKRNL_Version
00000000400b6bba T esMEMS_Balloc
00000000400b6bc0 T esMEMS_Bfree
00000000400b6bc4 T esMEMS_Calloc
00000000400b6bcc T esMEMS_CleanDCache
00000000400b6bd0 T esMEMS_CleanDCacheRegion
00000000400b6bd8 T esMEMS_CleanFlushCache
00000000400b6bdc T esMEMS_CleanFlushCacheRegion
00000000400b6be0 T esMEMS_CleanFlushDCache
00000000400b6be4 T esMEMS_CleanFlushDCacheRegion
00000000400b6bf2 T esMEMS_CleanInvalidateCacheAll
00000000400b6bf6 T esMEMS_FlushCache
00000000400b6bfa T esMEMS_FlushCacheRegion
00000000400b6bfe T esMEMS_FlushDCache
00000000400b6c02 T esMEMS_FlushDCacheRegion
00000000400b6c0a T esMEMS_FlushICache
00000000400b6c0e T esMEMS_FlushICacheRegion
00000000400b6c12 T esMEMS_FreeMemSize
00000000400b6c2c T esMEMS_GetIoVAByPA
00000000400b6c2e T esMEMS_HeapCreate
00000000400b6c34 T esMEMS_HeapDel
00000000400b6c38 T esMEMS_Info
00000000400b6c4e T esMEMS_LockDCache
00000000400b6c54 T esMEMS_LockICache
00000000400b6c5a T esMEMS_Malloc
00000000400b6c60 T esMEMS_Mfree
00000000400b6c66 T esMEMS_Palloc
00000000400b6c9a T esMEMS_Pfree
00000000400b6ca2 T esMEMS_PhyAddrConti
00000000400b6cc2 T esMEMS_Realloc
00000000400b6cca T esMEMS_TotalMemSize
00000000400b6cd0 T esMEMS_UnlockDCache
00000000400b6cd6 T esMEMS_UnlockICache
00000000400b6cdc T esMEMS_VA2PA
00000000400b6ce0 T esMEMS_VMalloc
00000000400b6cf0 T esMEMS_VMCreate
00000000400b6cf6 T esMEMS_VMDelete
00000000400b6cfa T esMEMS_VMfree
00000000400b6cfe T esKRNL_Ioctrl
00000000400b6d0c T esKSRV_GetHighMsg
00000000400b6d38 T esKSRV_GetLowMsg
00000000400b6d64 T esKSRV_GetMsg
00000000400b6dac T esKSRV_GetVersion
00000000400b6db4 T esKSRV_Get_Display_Hld
00000000400b6ddc T esKSRV_Get_Mixture_Hld
00000000400b6dfc T esKSRV_Save_Display_Hld
00000000400b6e14 T esKSRV_Save_Mixture_Hld
00000000400b6e2a T esKSRV_Get_Raw_Decoder_Hld
00000000400b6e7c T esKSRV_Save_Raw_Decoder_Hld
00000000400b6eb6 T esKSRV_memcpy
00000000400b6ec6 T esKSRV_memset
00000000400b6ed6 T esKSRV_Random
00000000400b6f54 T esKSRV_Reset
00000000400b6f5e T esKSRV_SendMsg
00000000400b6fc4 t kservice_maintask
00000000400b7162 T esKSRV_SendMsgEx
00000000400b71c0 T esKSRV_SysInfo
00000000400b71dc T ksrv_init
00000000400b7268 T syscall_trap
00000000400b726c T esSYSCALL_function
00000000400b72b2 T esPWRMAN_ReqPwrmanMode
00000000400b743a T esPWRMAN_RelPwrmanMode
00000000400b7600 T esPWRMAN_EnterStandby
00000000400b7602 T esPWRMAN_UsrEventNotify
00000000400b7644 T esPWRMAN_LockCpuFreq
00000000400b7686 T esPWRMAN_UnlockCpuFreq
00000000400b76b8 T esPWRMAN_RegDevice
00000000400b7764 T esPWRMAN_UnregDevice
00000000400b77a4 T esPWRMAN_GetStandbyPara
00000000400b77c0 T esPWRMAN_SetStandbyMode
00000000400b77cc T esRESM_ROpen
00000000400b7876 T esRESM_RClose
00000000400b789e T esRESM_RRead
00000000400b78ae T esRESM_RSeek
00000000400b78c2 T esSVC_ResourceRel
00000000400b78fe T esSVC_ResourceReq
00000000400b7974 T SVC_ResourceManInit
00000000400b7998 T svc_init
00000000400b79ae T SIOS_putstrtofile
00000000400b7a48 T esSIOS_putstr
00000000400b7a80 T esTIME_RequestCntr
00000000400b7b54 T esTIME_ReleaseCntr
00000000400b7bbc T esTIME_StartCntr
00000000400b7be8 T esTIME_StopCntr
00000000400b7c14 T esTIME_PauseCntr
00000000400b7c40 T esTIME_ContiCntr
00000000400b7c6c T esTIME_SetCntrValue
00000000400b7c8a T esTIME_QuerryCntr
00000000400b7ca8 T esTIME_SetCntrPrescale
00000000400b7cc6 T esTIME_QuerryCntrStat
00000000400b7cd0 T esTIME_GetTime
00000000400b7d12 T esTIME_SetTime
00000000400b7d68 T esTIME_GetDate
00000000400b7db0 T esTIME_SetDate
00000000400b7e0c T esTIME_RequestAlarm
00000000400b7e10 T esTIME_ReleaseAlarm
00000000400b7e14 T esTIME_StartAlarm
00000000400b7e18 T esTIME_StopAlarm
00000000400b7e1c T esTIME_QuerryAlarm
00000000400b7e20 T fsys_init
00000000400b7e54 T esFSYS_fsdbg
00000000400b7e58 T dev_hardsect_size
00000000400b7e90 T blksize_bits
00000000400b7ea2 T set_blocksize
00000000400b7ee2 T sb_set_blocksize
00000000400b7f1c T sb_min_blocksize
00000000400b7f44 t __insert_into_lru_list
00000000400b7f90 t __remove_from_lru_list
00000000400b7ff4 t __remove_from_free_list
00000000400b8028 t put_last_free
00000000400b8068 t remove_inode_queue
00000000400b807a t __refile_buffer
00000000400b80c4 t wait_for_buffers
00000000400b8122 t test_and_set_bit
00000000400b8156 t test_and_clear_bit
00000000400b818e t test_and_clear_bit
00000000400b81c6 t __hash_unlink
00000000400b81dc t atomic_sub_return.constprop.10
00000000400b8202 t atomic_add_return.constprop.11
00000000400b8228 t get_bh
00000000400b8230 T unlock_buffer
00000000400b8250 T end_buffer_io_sync
00000000400b8284 T file_flush
00000000400b82a8 T get_hash_table
00000000400b8328 T inode_has_buffers
00000000400b8344 T invalidate_inode_buffers
00000000400b8380 T __mark_dirty
00000000400b83a6 T refile_buffer
00000000400b83aa T __brelse
00000000400b83b8 t brelse
00000000400b83c0 t brelse
00000000400b83c8 t brelse
00000000400b83d0 t brelse
00000000400b83d8 t brelse
00000000400b83e0 t brelse
00000000400b83e8 t brelse
00000000400b83f0 t brelse
00000000400b83f8 t brelse
00000000400b8400 t brelse
00000000400b8408 T __bforget
00000000400b844a T submit_bh
00000000400b84b0 t ll_rw_block.part.5
00000000400b854e T invalidate_buffers
00000000400b8678 T try_to_free_buffers
00000000400b8766 t write_some_buffers
00000000400b8868 t write_unlocked_buffers
00000000400b8880 T sync_buffers
00000000400b88b6 T file_fsync
00000000400b88e4 T fsync_dev
00000000400b8924 T balance_dirty
00000000400b8980 T __getblk
00000000400b8b96 T __bread
00000000400b8bc4 T mark_buffer_dirty
00000000400b8bf0 T ll_rw_block
00000000400b8bf8 T sync_dirty_buffer
00000000400b8c48 T alloc_large_system_hash
00000000400b8c88 T buffer_init
00000000400b8d24 t p_hash
00000000400b8d58 t d_hash
00000000400b8da0 t dentry_iput
00000000400b8dbe t d_kill
00000000400b8e20 t __d_drop
00000000400b8e44 t _d_rehash
00000000400b8e74 T dput
00000000400b8f3c T d_invalidate
00000000400b8f7a T shrink_dcache_for_umount
00000000400b9008 T shrink_dcache_memory
00000000400b909c T d_alloc
00000000400b91c6 T d_instantiate
00000000400b91ca T d_alloc_root
00000000400b91f6 T d_splice_alias
00000000400b920a T __d_lookup
00000000400b92c8 T d_delete
00000000400b92fc T d_rehash
00000000400b9300 T d_move
00000000400b93c0 T vfs_caches_init
00000000400b9466 t filldir
00000000400b94d8 T esFSYS_opendir
00000000400b95a2 T esFSYS_closedir
00000000400b960e T esFSYS_mkdir
00000000400b9666 T esFSYS_rmdir
00000000400b96bc T esFSYS_readdir
00000000400b97e0 T esFSYS_rewinddir
00000000400b982a T no_llseek
00000000400b982e T epdk_mode_to_flag_trans
00000000400b9888 T epdk_flag_to_mode_trans
00000000400b98b6 T esFSYS_fopen
00000000400b996c T esFSYS_open
00000000400b9a14 T esFSYS_fclose
00000000400b9a8c T esFSYS_file2fd
00000000400b9a96 T esFSYS_fd2file
00000000400b9abc T esFSYS_fsync
00000000400b9b12 T esFSYS_remove
00000000400b9b68 T esFSYS_fstat
00000000400b9bea T esFSYS_fread
00000000400b9c94 T esFSYS_fwrite
00000000400b9d34 T generic_file_llseek
00000000400b9d6c T esFSYS_fseekex
00000000400b9dd2 T esFSYS_fseek
00000000400b9e30 T esFSYS_ftellex
00000000400b9e86 T esFSYS_ftell
00000000400b9ec6 T generic_fillattr
00000000400b9f24 T esFSYS_fioctrl
00000000400b9f86 T esFSYS_ferror
00000000400b9f98 T esFSYS_ferrclr
00000000400b9f9a T esFSYS_rename
00000000400ba004 T esFSYS_ftruncate
00000000400ba06a T fsys_vfs_init
00000000400ba0da T esFSYS_vfslock
00000000400ba138 T esFSYS_vfsunlock
00000000400ba182 T esFSYS_fsreg
00000000400ba214 T esFSYS_fsunreg
00000000400ba282 T esFSYS_mntfs
00000000400ba3c2 T esFSYS_umntfs
00000000400ba4a4 T test_and_freeze_partfs
00000000400ba4ee T unfreezepart
00000000400ba522 T localtime_to_xtime
00000000400ba5cc T update_xtime
00000000400ba5fe T current_seconds
00000000400ba616 T current_timespec
00000000400ba630 T timespec_trunc
00000000400ba64e T current_fs_time
00000000400ba668 T fs_timespec_to_kstamp
00000000400ba7de T esFSYS_format
00000000400ba868 T esFSYS_statfs
00000000400ba8ba T esFSYS_partfslck
00000000400ba902 T esFSYS_partfsunlck
00000000400ba94e T esFSYS_setfs
00000000400ba994 T esFSYS_getfscharset
00000000400ba9c2 t list_del_init
00000000400ba9d0 t list_move
00000000400ba9e4 t hash
00000000400baa16 t hlist_del_init
00000000400baa2c t find_inode_fast.isra.3.part.4
00000000400baa4e t __writeback_single_inode.isra.6
00000000400baaf6 T destroy_inode
00000000400bab14 T inode_init_once
00000000400bab48 t init_once
00000000400bab4c t init_once
00000000400bab5c t init_once
00000000400bab62 t init_once
00000000400bab7c t alloc_inode
00000000400babf8 T is_bad_inode
00000000400bac0a T __iget
00000000400bac66 T clear_inode
00000000400bac88 t dispose_list
00000000400bad02 T invalidate_inodes
00000000400bada2 T shrink_icache_memory
00000000400bae4e T new_inode
00000000400baeb2 T new_inode_nocache
00000000400baeb6 T iunique
00000000400baf20 T igrab
00000000400baf42 T iget_locked
00000000400bb018 T __insert_inode_hash
00000000400bb050 T generic_delete_inode
00000000400bb0a8 T iput
00000000400bb108 T inode_needs_sync
00000000400bb146 T __mark_inode_dirty
00000000400bb1b2 T touch_atime
00000000400bb200 T sync_part_inodes
00000000400bb2c2 T write_inode_now
00000000400bb2c8 T generic_drop_inode
00000000400bb384 T inode_init
00000000400bb402 t may_delete
00000000400bb460 t may_create.isra.1
00000000400bb482 t do_revalidate
00000000400bb4be t lookup_hash
00000000400bb54a t do_lookup
00000000400bb5ca t dget
00000000400bb5ec T getname
00000000400bb63a T putname
00000000400bb640 T do_char2uni
00000000400bb650 t __link_path_walk
00000000400bb8c4 T vfs_create
00000000400bb90c T may_open
00000000400bb99e T vfs_mkdir
00000000400bb9de T sys_mkdirat
00000000400bba76 T dentry_unhash
00000000400bbab4 T vfs_rmdir
00000000400bbb16 T do_rmdir
00000000400bbbac T path_release
00000000400bbbb2 T vfs_rename_dir
00000000400bbc70 T vfs_rename_other
00000000400bbcea T vfs_rename
00000000400bbd02 T __do_rename
00000000400bbde6 T do_rename
00000000400bbe44 T vfs_unlink
00000000400bbe8c T do_unlink
00000000400bbf58 T get_empty_filp
00000000400bbf8c T fput
00000000400bbffc T filp_close
00000000400bc042 T put_filp
00000000400bc080 T release_open_intent
00000000400bc08e t __path_lookup_intent_open
00000000400bc0f4 T open_namei
00000000400bc1fc T nameidata_to_filp
00000000400bc29a T do_sys_open
00000000400bc304 t char2uni
00000000400bc328 t uni2char
00000000400bc356 T utf8_mbtowc
00000000400bc3b2 T utf8_mbstowcs
00000000400bc416 T utf8_wctomb
00000000400bc46e T utf8_wcstombs
00000000400bc4ce T load_nls
00000000400bc50a T unload_nls
00000000400bc50c T get_current_charset
00000000400bc518 t change_bit
00000000400bc54c t rmqueue
00000000400bc63e T mem_map_index
00000000400bc652 T nr_free_buffer_pages
00000000400bc65e T virt_to_page
00000000400bc680 T __free_pages
00000000400bc798 T free_pages
00000000400bc7c0 T activate_page_nolock
00000000400bc834 T mark_page_accessed
00000000400bc89a T lru_cache_add
00000000400bc8da T __lru_cache_del
00000000400bc938 T try_to_free_pages
00000000400bcb4c T alloc_pages
00000000400bcbba T alloc_page
00000000400bcbd0 T __get_free_pages
00000000400bcbe6 T page_pool_ini
00000000400bccf4 t kmem_cache_estimate
00000000400bcd4a t kmem_freepages.isra.0
00000000400bcdc0 T kmem_cache_init
00000000400bce06 T kmem_cache_alloc
00000000400bd000 T kmalloc
00000000400bd01e T kmem_cache_free
00000000400bd08e t kmem_slab_destroy
00000000400bd0fe t __kmem_cache_shrink
00000000400bd146 T kmem_cache_destroy
00000000400bd1ba T kfree
00000000400bd1d4 T kmem_find_general_cachep
00000000400bd1ec T kmem_cache_create
00000000400bd40a T kmem_cache_sizes_init
00000000400bd490 T kmem_cache_reap
00000000400bd57e T fsync_super
00000000400bd5c4 T generic_shutdown_super
00000000400bd694 T sync_supers
00000000400bd6ce T path_to_sb
00000000400bd770 T deactivate_super
00000000400bd7ba T get_sb_bdev
00000000400bd95c T unmount_check
00000000400bd95e T devfs_format
00000000400bd962 T devfs_identify
00000000400bd9a8 T devfs_mount
00000000400bd9d4 T devfs_unmount
00000000400bd9ea T fsys_devfs_init
00000000400bda0a t devfs_readdir
00000000400bdabe t devfs_file_fsync
00000000400bdac2 T devfs_generic_ioctl
00000000400bdad0 t devfs_file_close
00000000400bdad6 t devfs_file_open
00000000400bdafe t devfs_file_write
00000000400bdb0a t devfs_file_read
00000000400bdb16 t devfs_clear_inode
00000000400bdb44 t devfs_statfs
00000000400bdb8c t devfs_drop_inode
00000000400bdb90 t devfs_destroy_inode
00000000400bdba2 t devfs_alloc_inode
00000000400bdbc4 T devfs_init_inodecache
00000000400bdbfc T devfs_iget
00000000400bdc3a T devfs_build_inode
00000000400bdcf0 T devfs_fill_super
00000000400bdde0 t nls_strnicmp
00000000400bde14 t nls_strnicmp
00000000400bde48 t devfs_hashi
00000000400bde90 t devfs_cmpi
00000000400bdebc T devfs_d_delete
00000000400bdec0 t devfs_lookup
00000000400bdf74 t devfs_revalidate
00000000400bdf8e T minfs_identify
00000000400be002 T minfs_mount
00000000400be02e T minfs_unmount
00000000400be044 T fsys_minfs_init
00000000400be06a T minfs_uncompress
00000000400be088 T minfs_allocate_temp_buffer
00000000400be096 T minfs_free_temp_buffer
00000000400be0a0 T minfs_cache_init
00000000400be10a T minfs_cache_exit
00000000400be130 T minfs_cache_lock
00000000400be150 T minfs_cache_unlock
00000000400be164 T minfs_get_data_from_cache
00000000400be18c T minfs_use_cache_load_data
00000000400be1dc T minfs_pread
00000000400be356 t minfs_readdir
00000000400be3f8 t minfs_file_read
00000000400be448 t minfs_file_ioctl
00000000400be688 t minfs_subdirs
00000000400be6b6 t minfs_inode_init_once
00000000400be6bc T minfs_alloc_inode
00000000400be6dc T minfs_destroy_inode
00000000400be6ee T minfs_init_inodecache
00000000400be726 T minfs_destroy_inodecache
00000000400be734 T minfs_read_root_inode
00000000400be7ba T minfs_iget
00000000400be86e t minfs_dentry_hashi
00000000400be8b6 t minfs_dentry_cmpi
00000000400be904 T minfs_dentry_delete
00000000400be908 t minfs_lookup
00000000400be9ae t minfs_statfs
00000000400bea06 t minfs_drop_inode
00000000400bea0a T minfs_init_sb_info
00000000400beabc T minfs_exit_sb_info
00000000400beaec t minfs_put_super
00000000400beaf0 T minfs_fill_super
00000000400bebc4 T minfs_lock
00000000400bebe4 T minfs_unlock
00000000400bebf8 t LzmaDec_WriteRem
00000000400bec78 t LzmaDec_DecodeReal2
00000000400bf8cc t LzmaDec_TryDummy
00000000400bfdcc T LzmaDec_Init
00000000400bfde6 T LzmaDec_DecodeToDic
00000000400c0052 T LzmaDec_FreeProbs
00000000400c006e t LzmaDec_AllocateProbs2
00000000400c00c6 T LzmaProps_Decode
00000000400c0122 T LzmaDec_AllocateProbs
00000000400c0152 T LzmaDecode
00000000400c01fc t SzFree
00000000400c0202 t SzAlloc
00000000400c020a T LzmaUncompress
00000000400c022a T exfat_count_free_clusters
00000000400c02fc T exfat_setup_bitmap
00000000400c0354 T exfat_free_bitmap
00000000400c0374 T exfat_bit_set
00000000400c03a4 T exfat_set_bits
00000000400c03d6 T exfat_bit_get
00000000400c03f0 t exfat_cache_init_once
00000000400c03f6 T exfat_init_cache_cachep
00000000400c042e T exfat_destroy_cache_cachep
00000000400c043c T exfat_cache_lookup
00000000400c04a2 T exfat_cache_add
00000000400c0598 T exfat_cache_inval
00000000400c05f8 T exfat_cache_inode_init_once
00000000400c0608 t sb_bread
00000000400c060e t sb_bread
00000000400c0614 t sb_bread
00000000400c061a t exfat_ent_read.isra.8
00000000400c069a t exfat_ent_write.isra.10
00000000400c06c0 t exfat_ent_prepare.constprop.16
00000000400c0714 t exfat_write_one_cluster
00000000400c075a t exfat_write_conti_chain
00000000400c084c t exfat_free_bitmap_bits
00000000400c093a T exfat_get_cluster
00000000400c0a9a t exfat_get_last_clus
00000000400c0b2a T exfat_inode_clusters_state
00000000400c0bb6 T exfat_chain_add
00000000400c0d1e T exfat_free_clusters
00000000400c0e04 T exfat_alloc_clusters
00000000400c1208 T exfat_free
00000000400c1330 t exfat_d_revalidate
00000000400c1334 t exfat_d_hash
00000000400c1338 t exfat_d_delete
00000000400c133c t exfat_d_release
00000000400c133e t exfat_d_iput
00000000400c1344 t exfat_d_compare
00000000400c1360 T pd_init
00000000400c1366 T pd_brelse
00000000400c1392 t exfat_parse_dir.isra.1
00000000400c154c t exfat_readdir
00000000400c15ac T pd_dirty
00000000400c15da T pd_iter_first_de
00000000400c15f2 t subdirs_parse
00000000400c1622 T pd_iter_next_de
00000000400c1668 t lookup_parse
00000000400c17f0 t find_parse
00000000400c19b0 t readdir_parse
00000000400c1b5e T exfat_lookup
00000000400c1c60 T exfat_subdirs
00000000400c1c84 T exfat_find
00000000400c1d70 T rootdir_parse_finish
00000000400c1d80 t rootdir_parse
00000000400c1dd2 T exfat_read_rootdir
00000000400c1e44 T exfat_generic_ioctl
00000000400c1e48 t exfat_cont_expand
00000000400c1f30 t exfat_file_llseek
00000000400c1fa2 t exfat_getattr
00000000400c1fce t exfat_file_release
00000000400c1fee t exfat_file_read
00000000400c227c T exfat_truncate
00000000400c22da t exfat_setattr
00000000400c2376 t exfat_file_write
00000000400c2640 T exfat_format
00000000400c2644 T exfat_mount
00000000400c2670 T exfat_unmount
00000000400c2686 T exfat_boot_sector_is_exfat
00000000400c26bc T exfat_identify
00000000400c2726 T fsys_exfatfs_init
00000000400c275a t exfat_fill_inode
00000000400c2876 T exfat_add_clusters
00000000400c28de T exfat_get_block
00000000400c2ac6 T exfat_hash_init
00000000400c2ada T exfat_attach
00000000400c2b4c T exfat_detach
00000000400c2b74 T exfat_ilookup
00000000400c2bca T exfat_build_inode
00000000400c2c36 T exfat_free_internal_inode
00000000400c2c3e T exfat_new_internal_inode
00000000400c2d02 T exfat_alloc_root_inode
00000000400c2d4e T exfat_rootdir_iget
00000000400c2dd4 t exfat_slot_info_init
00000000400c2dea t exfat_slot_info_brelse
00000000400c2e04 t exfat_get_entry
00000000400c2ef8 t exfat_dir_empty
00000000400c2f38 T exfat_sync_bhs
00000000400c2f74 T exfat_zeroed_blocks
00000000400c2ff0 T exfat_alloc_chunks
00000000400c3230 T exfat_fill_chunks
00000000400c3304 T exfat_remove_chunks
00000000400c33ae t exfat_rmdir
00000000400c342a t exfat_unlink
00000000400c348e T exfat_add_chunks
00000000400c34d0 t exfat_add_entry
00000000400c3802 t exfat_rename
00000000400c3992 t exfat_create
00000000400c3a1e T exfat_alloc_new_dir
00000000400c3a86 t exfat_mkdir
00000000400c3b40 t exfat_inode_init_once
00000000400c3b62 t exfat_clear_inode
00000000400c3b7a t exfat_statfs
00000000400c3c16 t exfat_delete_inode
00000000400c3c3a t exfat_destroy_inode
00000000400c3c4c t exfat_alloc_inode
00000000400c3c6e t exfat_put_super
00000000400c3c98 t exfat_write_inode.part.2
00000000400c3dfe t exfat_write_inode
00000000400c3e14 t exfat_exsb_validate.isra.3
00000000400c3ec8 T exfat_init_inodecache
00000000400c3f00 T exfat_destroy_inodecache
00000000400c3f0e T exfat_sync_inode
00000000400c3f14 T exfat_flush_inodes
00000000400c3f4a T exfat_super_block_flush
00000000400c3fda t exfat_write_super
00000000400c3fea T exfat_fill_super
00000000400c425c T exfat_setup_upcase
00000000400c44fc T exfat_free_upcase
00000000400c4530 T exfat_towupper
00000000400c4574 T exfat_checksum32
00000000400c5afe T exfat_checksum16
00000000400c5dac T exfat_conv_c2u
00000000400c5e0a T exfat_time_exfat2unix
00000000400c5eea T exfat_conv_u2c
00000000400c5f4a T exfat_time_unix2exfat
00000000400c608e t write_fat_entry
00000000400c615a t exfat_create_root_dir
00000000400c6290 t exfat_write_sector.isra.1
00000000400c62b4 t exfat_write_extended_boot_sectors
00000000400c634c t write_fat_entries.isra.3
00000000400c63a4 t exfat_create_volume_boot_record
00000000400c65d4 T exfat_set_bit
00000000400c6612 T Exfat_AutoFormat
00000000400c69c8 T ntfs_format
00000000400c69cc T ntfs_mount
00000000400c69fc T ntfs_unmount
00000000400c6a12 T ntfs_identify
00000000400c6a56 T fsys_ntfs_init
00000000400c6a6c t ntfs_attr_init_search_ctx
00000000400c6a98 t ntfs_attr_find
00000000400c6c1e t ntfs_attr_fill_zero
00000000400c6ca2 t NAttrClearFlag
00000000400c6cc8 t ntfs_attrlist_mark_dirty
00000000400c6cde T NAttrCompressed
00000000400c6d0c T NAttrSetCompressed
00000000400c6d34 T NAttrClearCompressed
00000000400c6d3e T NAttrEncrypted
00000000400c6d68 T NAttrSetEncrypted
00000000400c6d8c T NAttrClearEncrypted
00000000400c6d92 T NAttrSparse
00000000400c6dbe T NAttrSetSparse
00000000400c6de2 T NAttrClearSparse
00000000400c6dea T ntfs_get_attribute_value_length
00000000400c6e10 T ntfs_get_attribute_value
00000000400c6f88 T ntfs_attr_init
00000000400c7060 T ntfs_attr_close
00000000400c70a0 T ntfs_attr_reinit_search_ctx
00000000400c70c0 T ntfs_attr_lookup
00000000400c7524 T ntfs_attr_get_search_ctx
00000000400c755e T ntfs_attr_open
00000000400c773e T ntfs_attr_map_runlist
00000000400c77bc T ntfs_attr_find_vcn
00000000400c7880 T ntfs_attr_pread
00000000400c7afa T ntfs_attr_mst_pread
00000000400c7b62 T ntfs_attr_put_search_ctx
00000000400c7b66 T ntfs_attr_readall
00000000400c7bc2 T ntfs_attr_map_whole_runlist
00000000400c7cb2 T ntfs_attr_position
00000000400c7cfa T ntfs_attr_find_in_attrdef
00000000400c7d38 T ntfs_attr_size_bounds_check
00000000400c7d9c T ntfs_attr_can_be_non_resident
00000000400c7dc4 T ntfs_attr_can_be_resident
00000000400c7de8 T ntfs_make_room_for_attr
00000000400c7e70 T ntfs_attr_record_resize
00000000400c7f16 T ntfs_resident_attr_record_add
00000000400c80de T ntfs_non_resident_attr_record_add
00000000400c82ca T ntfs_attr_record_rm
00000000400c840c T ntfs_resident_attr_value_resize
00000000400c845c T ntfs_attr_record_move_to
00000000400c857a T ntfs_attr_record_move_away
00000000400c8630 t ntfs_attr_update_mapping_pairs_i
00000000400c8a86 T ntfs_attr_update_mapping_pairs
00000000400c8a8a T ntfs_attr_pwrite
00000000400c9026 t ntfs_attr_make_non_resident
00000000400c91fa t ntfs_resident_attr_resize_i
00000000400c94b8 T ntfs_attr_truncate
00000000400c99c6 T ntfs_attr_mst_pwrite
00000000400c9a64 T ntfs_attr_name_free
00000000400c9a84 T ntfs_attr_name_get
00000000400c9aaa T ntfs_attr_rm
00000000400c9b40 T ntfs_attr_add
00000000400c9dba T ntfs_attr_exist
00000000400c9e04 T ntfs_attr_remove
00000000400c9e3e T ntfs_attrlist_need
00000000400c9e86 T ntfs_attrlist_entry_add
00000000400ca0a6 T ntfs_attrlist_entry_rm
00000000400ca1a8 t ntfs_bitmap_set_bits_in_run
00000000400ca376 T ntfs_bitmap_set_run
00000000400ca37c T ntfs_bitmap_clear_run
00000000400ca382 T ntfs_boot_sector_is_ntfs
00000000400ca490 T ntfs_boot_sector_parse
00000000400ca5aa t ntfs_collate_ntofs_ulong
00000000400ca5ca t ntfs_collate_binary
00000000400ca602 t ntfs_collate_file_name
00000000400ca64e T ntfs_is_collation_rule_supported
00000000400ca660 T ntfs_collate
00000000400ca6ba T ntfs_pread
00000000400ca83a T ntfs_mst_pread
00000000400ca8a4 T ntfs_device_block_size_set
00000000400ca8a8 T ntfs_pwrite
00000000400ca9a4 T ntfs_d_delete
00000000400ca9a8 t ntfs_filldir.isra.2
00000000400caa58 T ntfs_readdir
00000000400cae4e t ntfs_create_node.isra.3
00000000400cb1a8 t ntfs_mkdir
00000000400cb1e2 t ntfs_create
00000000400cb21c T ntfs_inode_lookup_by_name
00000000400cb496 t ntfs_lookup
00000000400cb52e T ntfs_check_empty_dir
00000000400cb586 T __ntfs_unlink
00000000400cb7d0 t ntfs_rmdir
00000000400cb804 t ntfs_unlink
00000000400cb838 T __ntfs_delete
00000000400cb922 T ntfs_truncate
00000000400cb92c T ntfs_file_llseek
00000000400cb998 t ntfs_file_open
00000000400cb9e0 t ntfs_file_write
00000000400cba64 t ntfs_file_read
00000000400cbb1c T ntfs_setattr
00000000400cbba4 t ntfs_ie_end
00000000400cbbba t ntfs_ie_get_last
00000000400cbbe4 t ntfs_ie_get_by_pos
00000000400cbbfa t ntfs_ih_numof_entries
00000000400cbc36 t ntfs_ib_alloc
00000000400cbc9e t ntfs_ib_write
00000000400cbcca t ntfs_index_ctx_free
00000000400cbd06 t ntfs_ir_lookup
00000000400cbd6a t ntfs_ir_lookup2
00000000400cbd8a t ntfs_ia_open
00000000400cbd9a t ntfs_ie_dup
00000000400cbdc4 t ntfs_ir_truncate
00000000400cbe1e t ntfs_ibm_modify
00000000400cbee6 t ntfs_ibm_get_free
00000000400cbf74 t ntfs_ie_insert
00000000400cbfb6 t ntfs_ie_delete
00000000400cbfd8 t ntfs_ie_add_vcn
00000000400cc012 t ntfs_ib_read
00000000400cc078 t ntfs_ir_make_space
00000000400cc248 T ntfs_index_entry_mark_dirty
00000000400cc25e T ntfs_index_ctx_get
00000000400cc2ba T ntfs_index_ctx_put
00000000400cc2d2 T ntfs_ie_get_vcn
00000000400cc2de t ntfs_ie_lookup
00000000400cc3d4 t ntfs_ih_insert
00000000400cc44e t ntfs_ib_split
00000000400cc68c T ntfs_index_lookup
00000000400cc856 T ntfs_index_ctx_reinit
00000000400cc88e t ntfs_ie_add
00000000400cc938 t ntfs_ih_takeout
00000000400cc9e0 t ntfs_index_rm_leaf
00000000400ccb2a T ntfs_index_add_filename
00000000400ccbd4 T ntfs_index_remove
00000000400ccea2 t ntfs_inode_release
00000000400ccee2 T ntfs_inode_allocate
00000000400ccf00 T ntfs_big_inode_alloc
00000000400ccf36 T ntfs_big_inode_destroy
00000000400ccf82 T ntfs_fill_vfsinode
00000000400cd02a T ntfs_inode_open
00000000400cd23e T ntfs_extent_inode_open
00000000400cd320 T ntfs_inode_mark_dirty
00000000400cd352 T ntfs_inode_attach_all_extents
00000000400cd3d6 T ntfs_inode_sync
00000000400cd80a T ntfs_inode_close
00000000400cd8ea T ntfs_drop_inode
00000000400cd924 T ntfs_inode_free_space
00000000400cda00 T ntfs_inode_add_attrlist
00000000400cdcc8 T ntfs_inode_update_times
00000000400cdd28 t ntfs_cluster_update_zone_pos
00000000400cdd70 t bitmap_writeback.isra.2
00000000400cddac T ntfs_cluster_free_from_rl
00000000400cddee T ntfs_cluster_alloc
00000000400ce240 T ntfs_cluster_free
00000000400ce362 T ntfs_check_logfile
00000000400ce742 T ntfs_is_logfile_clean
00000000400ce78c T ntfs_empty_logfile
00000000400ce868 t ntfs_mft_attr_extend
00000000400ce898 t ntfs_mft_bitmap_extend_allocation_i
00000000400ceb44 t ntfs_mft_bitmap_find_free_rec
00000000400cecb8 T ntfs_mft_records_read
00000000400ced2e T ntfs_mft_record_check
00000000400ced68 T ntfs_file_record_read
00000000400cee06 T ntfs_mft_record_layout
00000000400ceeea T ntfs_mft_records_write
00000000400cf018 T ntfs_mft_record_format
00000000400cf070 T ntfs_mft_record_alloc
00000000400cf92a T ntfs_mft_record_free
00000000400cfa04 T ntfs_mst_post_read_fixup
00000000400cfa9e T ntfs_mst_pre_write_fixup
00000000400cfb3a T ntfs_mst_post_write_fixup
00000000400cfb66 t ntfs_rl_realloc
00000000400cfb88 t ntfs_rl_are_mergeable
00000000400cfbd0 t ntfs_rl_mm
00000000400cfbf4 t ntfs_rl_mc
00000000400cfc10 t ntfs_runlists_merge_i
00000000400d01c0 T ntfs_runlists_merge
00000000400d01c4 T ntfs_mapping_pairs_decompress_i
00000000400d03e4 T ntfs_mapping_pairs_decompress
00000000400d03e8 T ntfs_rl_vcn_to_lcn
00000000400d042c T ntfs_rl_pread
00000000400d0544 T ntfs_rl_pwrite
00000000400d065a T ntfs_get_nr_significant_bytes
00000000400d0696 T ntfs_get_size_for_mapping_pairs
00000000400d0790 T ntfs_write_significant_bytes
00000000400d07f8 T ntfs_mapping_pairs_build
00000000400d09b0 T ntfs_rl_truncate
00000000400d09f6 T ntfs_rl_sparse
00000000400d0a20 T ntfs_rl_get_compressed_size
00000000400d0a52 T ntfs_sd_add_everyone
00000000400d0b10 T ntfs_names_collate
00000000400d0b64 T ntfs_ucsncmp
00000000400d0b90 T ntfs_ucsncasecmp
00000000400d0bd0 T ntfs_names_are_equal
00000000400d0c04 T ntfs_ucsnlen
00000000400d0c1c T ntfs_ucsndup
00000000400d0c60 T ntfs_file_values_compare
00000000400d0c7c T ntfs_ucstombs
00000000400d0d88 T ntfs_mbstoucs
00000000400d0e88 T ntfs_upcase_table_build
00000000400d0f30 t ntfs_error_set
00000000400d0f42 t ntfs_attr_free
00000000400d0f64 t ntfs_inode_free
00000000400d0f8a t __ntfs_volume_release
00000000400d104e t ntfs_put_super
00000000400d1058 T ntfs_statfs
00000000400d11b0 T ntfs_volume_alloc
00000000400d11ba T ntfs_volume_startup
00000000400d1590 T ntfs_part_identify
00000000400d15fc T ntfs_volume_check_hiberfile
00000000400d1708 T ntfs_logfile_reset
00000000400d179a T ntfs_fill_super
00000000400d1d54 t fat_cache_merge
00000000400d1d7a t fat_cache_add
00000000400d1e3e T fat_cache_init
00000000400d1e76 T fat_cache_destroy
00000000400d1e84 T fat_cache_inval_inode
00000000400d1ee4 T fat_get_cluster
00000000400d20ac T fat_bmap
00000000400d21b8 t fat_make_i_pos
00000000400d21cc t fat__get_entry
00000000400d2270 t fat_dir_ioctl
00000000400d2282 t uni16_to_x8.isra.2
00000000400d2330 t fat_short2uni.isra.3
00000000400d2356 t fat_short2lower_uni
00000000400d23c4 t fat_shortname2uni
00000000400d23e6 t fat_get_entry
00000000400d2418 t fat_get_short_entry
00000000400d246c t set_bit.constprop.11
00000000400d248a t __fat_remove_entries
00000000400d2538 t fat_zeroed_cluster.constprop.8
00000000400d264c t fat_add_new_entries
00000000400d27f6 T fat16_towchar
00000000400d281e t fat_parse_long.part.7
00000000400d2b7a t fat_readdir
00000000400d2fe8 T fat_search_long
00000000400d330e T fat_subdirs
00000000400d334e T fatwchar_to16
00000000400d3376 T fat_scan
00000000400d3400 T fat_dir_empty
00000000400d346a T fat_get_dotdot_entry
00000000400d34de T fat_remove_entries
00000000400d35ae T fat_alloc_new_dir
00000000400d3706 T fat_add_entries
00000000400d39f2 t fat12_ent_blocknr
00000000400d3a14 t fat_ent_blocknr
00000000400d3a36 t fat16_ent_set_ptr
00000000400d3a40 t fat32_ent_set_ptr
00000000400d3a4a t fat12_ent_get
00000000400d3a86 t fat16_ent_get
00000000400d3a9c t fat32_ent_get
00000000400d3ab8 t fat16_ent_next
00000000400d3ae2 t fat32_ent_next
00000000400d3b0c t fat_ent_update_ptr
00000000400d3b64 t fat12_ent_next
00000000400d3bca t fatent_brelse
00000000400d3c06 t fat_ent_read_block
00000000400d3c48 t fat12_ent_put
00000000400d3cba t fat16_ent_put
00000000400d3cd4 t fat32_ent_put
00000000400d3ce8 t fat_ent_bread
00000000400d3d56 t fat12_ent_set_ptr
00000000400d3d78 t fat12_ent_bread
00000000400d3e02 t fat_collect_bhs
00000000400d3e8a t fat_mirror_bhs
00000000400d3f46 T fat_ent_access_init
00000000400d3f80 T fat_ent_read
00000000400d3ff8 T fat_ent_read_inbh
00000000400d405e T fat_ent_write
00000000400d40b0 T fat_free_clusters
00000000400d41e0 T fat_alloc_clusters
00000000400d437e T fat_count_free_clusters
00000000400d43f2 T fat_getattr
00000000400d441a t fat_cont_expand
00000000400d4532 T fat_generic_ioctl
00000000400d457a t fat_file_release
00000000400d45a6 t fat_file_write
00000000400d48d4 t fat_file_read
00000000400d4b64 T fat_file_llseek
00000000400d4bd6 T fat_truncate
00000000400d4d54 T fat_setattr
00000000400d4df0 T format_write_sec
00000000400d4e02 T _FS_FAT_AutoFormat
00000000400d52ca T fat_format
00000000400d52ce T fat_identify
00000000400d535e T fat_mount
00000000400d5386 T fat_unmount
00000000400d539c T fsys_fatfs_init
00000000400d53cc t fat_calc_dir_size
00000000400d5416 t fat_put_super
00000000400d5422 t fat_statfs
00000000400d54b4 t fat_write_super
00000000400d54c4 t fat_delete_inode
00000000400d54e8 t fat_destroy_inode
00000000400d54fa t fat_alloc_inode
00000000400d551c t fat_clear_inode
00000000400d5550 t fat_write_inode
00000000400d568c T fat_attach
00000000400d56c8 T fat_iget
00000000400d5710 T fat_build_inode
00000000400d597a T fat_init_inodecache
00000000400d59b2 T fat_destroy_inodecache
00000000400d59c0 T fat_detach
00000000400d59de T fat_add_cluster
00000000400d5a36 T __fat_get_block
00000000400d5b3a T fat_sync_inode
00000000400d5b40 T fat_flush_inodes
00000000400d5b7e T fat_fill_super
00000000400d602c T date_dos2unix
00000000400d60ae T fat_date_unix2dos
00000000400d61a2 T fat_clusters_flush
00000000400d621a T fat_chain_add
00000000400d632e T fat_sync_bhs
00000000400d636a t vfat_striptail_len
00000000400d6388 t vfat_hashi
00000000400d63e4 t vfat_cmpi
00000000400d6456 t vfat_cmp
00000000400d649a t vfat_find
00000000400d64ce t vfat_rmdir
00000000400d6538 t vfat_unlink
00000000400d6588 t vfat_find_form
00000000400d65aa t vfat_lookup
00000000400d6632 t vfat_revalidate
00000000400d664c t vfat_hash
00000000400d6690 T isalphabeta
00000000400d66a8 t to_shortname_char.isra.4.constprop.7
00000000400d6790 t vfat_add_entry
00000000400d6f4c t vfat_rename
00000000400d7176 t vfat_mkdir
00000000400d7222 t vfat_create
00000000400d72a6 T fsys_find_part
00000000400d7354 T esFSYS_popen
00000000400d7412 T esFSYS_pread
00000000400d7448 T esFSYS_pwrite
00000000400d7484 T esFSYS_pioctrl
00000000400d74f0 T esFSYS_premove
00000000400d74f4 T esFSYS_prename
00000000400d74f8 T esFSYS_perror
00000000400d74fe T esFSYS_perrclr
00000000400d7504 T deactivate_part
00000000400d7556 T esFSYS_pclose
00000000400d75d6 t __mount_parts
00000000400d7948 t mnt_parts_task
00000000400d7972 T fsys_vpart_init
00000000400d79da T esFSYS_pdreg
00000000400d7a52 T esFSYS_pdunreg
00000000400d7ab0 T esFSYS_mntparts
00000000400d7b36 T esFSYS_umntparts
00000000400d7cce T esFSYS_statpt
00000000400d7d94 T esFSYS_querypartupdateflag
00000000400d7dea T esFSYS_clearpartupdateflag
00000000400d7e30 T dospart_read
00000000400d7e98 T dospart_write
00000000400d7f00 T dospart_ioctl
00000000400d7fc6 T dospart_mount
00000000400d80ca T dospart_identify
00000000400d816c T dospart_unmount
00000000400d81a2 T fsys_dospart_init
00000000400d81aa t parse_solaris_x86
00000000400d81ac t is_extended_partition.isra.0
00000000400d81c2 t parse_unixware
00000000400d81c4 t parse_minix
00000000400d81c6 t parse_freebsd
00000000400d81c8 t parse_netbsd
00000000400d81ca t parse_openbsd
00000000400d81cc T put_partition
00000000400d81e2 T blkdev_hardsect_size
00000000400d8214 T read_dev_sector
00000000400d825e T put_dev_sector
00000000400d8278 T check_msdos_partition
00000000400d8590 T check_partition
00000000400d85ee T bdev_logical_block_size
00000000400d860a T i_size_read
00000000400d8628 t last_lba
00000000400d863e t read_lba
00000000400d86d0 t is_gpt_valid.part.4
00000000400d885c T efi_partition
00000000400d8aa0 t dmspart_read
00000000400d8aa4 t dmspart_identify
00000000400d8ad8 t dmspart_unmount
00000000400d8afc t dmspart_mount
00000000400d8b4a t dmspart_ioctl
00000000400d8bac t dmspart_write
00000000400d8bb0 T fsys_dmspart_init
00000000400d8bb8 T rawpart_fdisk
00000000400d8bbc T rawpart_read
00000000400d8c0a T rawpart_write
00000000400d8c58 T rawpart_ioctl
00000000400d8d2a T rawpart_mount
00000000400d8d7c T rawpart_unmount
00000000400d8dac T rawpart_identify
00000000400d8de0 T fsys_rawpart_init
00000000400d8de8 T SetDevAttr
00000000400d8f44 T esDEV_DevReg
00000000400d908c T esDEV_DevUnreg
00000000400d9114 T esDEV_Lock
00000000400d912a T esDEV_Unlock
00000000400d913c T esDEV_Open
00000000400d918e T esDEV_Close
00000000400d91f4 T esDEV_Read
00000000400d9206 T esDEV_Write
00000000400d9218 T esDEV_Ioctl
00000000400d9276 T dev_init
00000000400d9296 T esDEV_Insmod
00000000400d93e2 T esDEV_Unimod
00000000400d94fa t _Hotplug
00000000400d9538 T esDEV_Plugin
00000000400d95d6 T esDEV_Plugout
00000000400d9666 t dmsdev_Open
00000000400d9674 t dmsdev_Close
00000000400d968e t dmsdev_Read
00000000400d9692 t dmsdev_Ioctrl
00000000400d96f4 t dmsdev_Write
00000000400d96f8 T dmsdev_init
00000000400d972c t ramdiskdev_open
00000000400d9742 t ramdiskdev_close
00000000400d975c t ramdiskdev_write
00000000400d97b2 t ramdiskdev_read
00000000400d9804 t ramdiskdev_ioctrl
00000000400d9832 T ramdiskdev_init
00000000400d986e T kernel_license_check_init
00000000400d988a T kernel_ioctrl_check
00000000400da130 R _global_impure_ptr
00000000400da138 r DAYS_IN_MONTH
00000000400da168 r _DAYS_BEFORE_MONTH
00000000400da198 R __month_lengths
00000000400da508 r fpi.3464
00000000400da520 r fpinan.3500
00000000400da538 r tinytens
00000000400da6d0 r blanks.4575
00000000400da6e0 r zeroes.4576
00000000400daac0 r basefix.4627
00000000400dac58 r blanks.4566
00000000400dac68 r zeroes.4567
00000000400dade8 r blanks.4590
00000000400dadf8 r zeroes.4591
00000000400dae08 r mon_name.2147
00000000400dae30 r day_name.2146
00000000400dae48 R _ctype_
00000000400daf50 R __hexdig
00000000400db050 r ezero
00000000400db068 r eone
00000000400db080 r etens
00000000400db188 r emtens
00000000400db290 r bmask
00000000400db2b8 r p05.3384
00000000400db2c8 R __mprec_tens
00000000400db390 R __mprec_tinytens
00000000400db3b8 R __mprec_bigtens
00000000400db570 r blanks.4552
00000000400db580 r zeroes.4553
00000000400db820 r basefix.4264
00000000400db900 r fivesbits
00000000400db998 R __clz_tab
00000000400dba98 r TWO52
00000000400dbaa8 r npio2_hw
00000000400dbb28 r two_over_pi
00000000400dbc30 r init_jk
00000000400dbc40 r PIo2
00000000400dbc80 r __FUNCTION__.3155
00000000400dbc98 r __FUNCTION__.3176
00000000400df518 r sun20iw1p1_memory_layout
00000000400df778 r __FUNCTION__.7125
00000000400df790 r __func__.6990
00000000400df7a8 r __func__.7081
00000000400df7c0 r __func__.7121
00000000400df7d8 r __func__.7225
00000000400df8b0 r __func__.7671
00000000400df908 r melis_banner
00000000400dfd50 r initcall_levels
00000000400dfd98 r __FUNCTION__.2933
00000000400dfdb0 r __FUNCTION__.2933
00000000400dfe00 r __FUNCTION__.2971
00000000400dfe10 r __FUNCTION__.2971
00000000400dfe20 r __FUNCTION__.2975
00000000400dfe30 r __FUNCTION__.2983
00000000400dfe40 r __FUNCTION__.2983
00000000400dfe60 r __FUNCTION__.2988
00000000400dfe70 r __FUNCTION__.2995
00000000400dfe80 r __FUNCTION__.3006
00000000400dfe90 r __FUNCTION__.3013
00000000400dfea0 r __FUNCTION__.3013
00000000400dfeb8 r __FUNCTION__.3020
00000000400dfec8 r __FUNCTION__.3024
00000000400dfed8 r __FUNCTION__.3024
00000000400dfee8 r __FUNCTION__.3031
00000000400dfef8 r __FUNCTION__.3036
00000000400dff08 r __FUNCTION__.3044
00000000400dff18 r __FUNCTION__.3054
00000000400dff30 r __FUNCTION__.3080
00000000400dff40 r __FUNCTION__.3084
00000000400dff50 r __FUNCTION__.3095
00000000400dff60 r __FUNCTION__.3110
00000000400dff70 r __FUNCTION__.3118
00000000400dff88 r __FUNCTION__.3138
00000000400dff98 r __FUNCTION__.3143
00000000400dffa8 r __FUNCTION__.3152
00000000400dffb8 r __FUNCTION__.3170
00000000400dffc8 r __FUNCTION__.3182
00000000400dffd8 r __FUNCTION__.3182
00000000400e03b0 r __FUNCTION__.2955
00000000400e03c8 R __lowest_bit_bitmap
00000000400e04c8 r large_digits.5630
00000000400e0538 r small_digits.5629
00000000400e0550 r __FUNCTION__.2810
00000000400e0560 r __FUNCTION__.2810
00000000400e0578 r __FUNCTION__.2819
00000000400e0590 r __FUNCTION__.2828
00000000400e05a8 r __FUNCTION__.2833
00000000400e05c0 r __FUNCTION__.2837
00000000400e05e0 r __FUNCTION__.2841
00000000400e0668 r __FUNCTION__.2940
00000000400e0688 r __FUNCTION__.2978
00000000400e07a0 r __FUNCTION__.2968
00000000400e07b0 r __FUNCTION__.2980
00000000400e07c0 r __FUNCTION__.2987
00000000400e07d8 r __FUNCTION__.2992
00000000400e07f0 r __FUNCTION__.3045
00000000400e0808 r __FUNCTION__.3055
00000000400e0820 r __FUNCTION__.3060
00000000400e0838 r __FUNCTION__.3066
00000000400e0850 r __FUNCTION__.3073
00000000400e09e0 r __FUNCTION__.2801
00000000400e09f0 r __FUNCTION__.2806
00000000400e0a00 r __FUNCTION__.2806
00000000400e0a10 r __FUNCTION__.2820
00000000400e0a20 r __FUNCTION__.2830
00000000400e0a30 r __FUNCTION__.2847
00000000400e0a40 r __FUNCTION__.2853
00000000400e0b60 r CSWTCH.2
00000000400e0b68 r __FUNCTION__.5484
00000000400e0b88 r pipe_fops
00000000400e0bf8 r __FUNCTION__.3161
00000000400e0c10 r __FUNCTION__.3168
00000000400e0c28 r __FUNCTION__.3215
00000000400e0c40 r __FUNCTION__.3220
00000000400e0c78 r __FUNCTION__.2824
00000000400e0c88 r __FUNCTION__.2824
00000000400e0ca0 r __FUNCTION__.2865
00000000400e0cb8 r __FUNCTION__.2905
00000000400e0cd0 r __FUNCTION__.2910
00000000400e0ce8 r __FUNCTION__.2939
00000000400e0d38 r __FUNCTION__.3040
00000000400e0d48 r __FUNCTION__.3051
00000000400e0d58 r __FUNCTION__.3056
00000000400e0d68 r __FUNCTION__.3065
00000000400e0d80 r __FUNCTION__.3086
00000000400e0d98 r __FUNCTION__.3100
00000000400e0da8 r __FUNCTION__.3114
00000000400e0fa8 r __FUNCTION__.2720
00000000400e0fc0 r __FUNCTION__.2730
00000000400e0fd0 r __FUNCTION__.2743
00000000400e0fe8 r __FUNCTION__.2754
00000000400e0ff8 r __FUNCTION__.2759
00000000400e1008 r __FUNCTION__.2766
00000000400e1018 r __FUNCTION__.2773
00000000400e1028 r __FUNCTION__.2773
00000000400e1038 r __FUNCTION__.2779
00000000400e1150 r __FUNCTION__.2818
00000000400e1160 r __FUNCTION__.2818
00000000400e1178 r __FUNCTION__.2756
00000000400e1188 r __FUNCTION__.2768
00000000400e11a0 r __FUNCTION__.2795
00000000400e11b0 r __FUNCTION__.2795
00000000400e1310 r __FUNCTION__.8178
00000000400e1748 r __FUNCTION__.3735
00000000400e1758 r __FUNCTION__.3754
00000000400e1a08 R sunxi_hal_spi_driver
00000000400e1b48 r sunxi_hal_spinor_driver
00000000400e1e30 r syspart
00000000400e1ed0 R __fsym___cmd_drvavs_desc
00000000400e1ee8 R __fsym___cmd_drvavs_name
00000000400e1ef8 R __fsym___cmd_power_info_desc
00000000400e1f08 R __fsym___cmd_power_info_name
00000000400e1f20 R __fsym_sdmmctest_desc
00000000400e1f38 R __fsym_sdmmctest_name
00000000400e1f48 R __fsym_soundcard_desc
00000000400e1f58 R __fsym_soundcard_name
00000000400e1f68 R __fsym_aplay_desc
00000000400e1f78 R __fsym_aplay_name
00000000400e1f80 R __fsym_arecord_desc
00000000400e1f90 R __fsym_arecord_name
00000000400e1f98 R __fsym_amixer_desc
00000000400e1fa8 R __fsym_amixer_name
00000000400e1fb0 R __fsym_disp_desc
00000000400e1fc0 R __fsym_disp_name
00000000400e1fc8 R __fsym_tv_resync_line_store_desc
00000000400e1fe8 R __fsym_tv_resync_line_store_name
00000000400e2000 R __fsym_tv_resync_pixel_store_desc
00000000400e2020 R __fsym_tv_resync_pixel_store_name
00000000400e2038 R __fsym_tv_faketv_store_desc
00000000400e2050 R __fsym_tv_faketv_store_name
00000000400e2060 R __fsym_tv_faketv_show_desc
00000000400e2078 R __fsym_tv_faketv_show_name
00000000400e2088 R __fsym_hal_spidev_test_desc
00000000400e20a0 R __fsym_hal_spidev_test_name
00000000400e20b0 R __fsym_hal_spi_slave_test_desc
00000000400e20c8 R __fsym_hal_spi_slave_test_name
00000000400e20e0 R __fsym_hal_spi_slave_driver_desc
00000000400e2100 R __fsym_hal_spi_slave_driver_name
00000000400e2118 R __fsym_hal_gpadc_desc
00000000400e2130 R __fsym_hal_gpadc_name
00000000400e2140 R __fsym_hal_rtc_desc
00000000400e2158 R __fsym_hal_rtc_name
00000000400e2160 R __fsym_usb_desc
00000000400e2170 R __fsym_usb_name
00000000400e2178 R __fsym_disp_mem_desc
00000000400e2188 R __fsym_disp_mem_name
00000000400e2198 R __fsym_disp_layer_cfg_desc
00000000400e21a8 R __fsym_disp_layer_cfg_name
00000000400e21b8 R __fsym___cmd_disp_layer_alpha_test_desc
00000000400e21d0 R __fsym___cmd_disp_layer_alpha_test_name
00000000400e21f0 R __fsym___cmd_disp_layer_scal_test_desc
00000000400e2208 R __fsym___cmd_disp_layer_scal_test_name
00000000400e2228 R __fsym___cmd_disp_layer_format_test_desc
00000000400e2240 R __fsym___cmd_disp_layer_format_test_name
00000000400e2260 R __fsym___cmd_disp_lbc_test_desc
00000000400e2270 R __fsym___cmd_disp_lbc_test_name
00000000400e2288 R __fsym_ths_gt_desc
00000000400e22a0 R __fsym_ths_gt_name
00000000400e22a8 R __fsym___cmd_setenv_desc
00000000400e22b0 R __fsym___cmd_setenv_name
00000000400e22c0 R __fsym___cmd_printenv_desc
00000000400e22d0 R __fsym___cmd_printenv_name
00000000400e22e0 R __fsym___cmd_list_fd_desc
00000000400e22f8 R __fsym___cmd_list_fd_name
00000000400e2308 R __fsym___cmd_mount_table_desc
00000000400e2320 R __fsym___cmd_mount_table_name
00000000400e2338 R __fsym___cmd_list_device_desc
00000000400e2350 R __fsym___cmd_list_device_name
00000000400e2368 R __fsym___cmd_list_timer_desc
00000000400e2380 R __fsym___cmd_list_timer_name
00000000400e2398 R __fsym___cmd_list_mempool_desc
00000000400e23b8 R __fsym___cmd_list_mempool_name
00000000400e23d0 R __fsym___cmd_list_memheap_desc
00000000400e23f0 R __fsym___cmd_list_memheap_name
00000000400e2408 R __fsym___cmd_list_msgqueue_desc
00000000400e2428 R __fsym___cmd_list_msgqueue_name
00000000400e2440 R __fsym___cmd_list_mailbox_desc
00000000400e2458 R __fsym___cmd_list_mailbox_name
00000000400e2470 R __fsym___cmd_list_mutex_desc
00000000400e2488 R __fsym___cmd_list_mutex_name
00000000400e24a0 R __fsym___cmd_list_event_desc
00000000400e24b8 R __fsym___cmd_list_event_name
00000000400e24d0 R __fsym___cmd_list_sem_desc
00000000400e24f0 R __fsym___cmd_list_sem_name
00000000400e2500 R __fsym___cmd_list_thread_desc
00000000400e2510 R __fsym___cmd_list_thread_name
00000000400e2528 R __fsym___cmd_free_desc
00000000400e2550 R __fsym___cmd_free_name
00000000400e2560 R __fsym___cmd_ps_desc
00000000400e2580 R __fsym___cmd_ps_name
00000000400e2590 R __fsym___cmd_help_desc
00000000400e25a8 R __fsym___cmd_help_name
00000000400e25b8 R __fsym___cmd_echo_desc
00000000400e25d0 R __fsym___cmd_echo_name
00000000400e25e0 R __fsym___cmd_df_desc
00000000400e25f0 R __fsym___cmd_df_name
00000000400e2600 R __fsym___cmd_mkfs_desc
00000000400e2620 R __fsym___cmd_mkfs_name
00000000400e2630 R __fsym___cmd_mkdir_desc
00000000400e2648 R __fsym___cmd_mkdir_name
00000000400e2658 R __fsym___cmd_pwd_desc
00000000400e2690 R __fsym___cmd_pwd_name
00000000400e26a0 R __fsym___cmd_cd_desc
00000000400e26c8 R __fsym___cmd_cd_name
00000000400e26d8 R __fsym___cmd_cat_desc
00000000400e26f0 R __fsym___cmd_cat_name
00000000400e2700 R __fsym___cmd_mv_desc
00000000400e2718 R __fsym___cmd_mv_name
00000000400e2728 R __fsym___cmd_cp_desc
00000000400e2740 R __fsym___cmd_cp_name
00000000400e2750 R __fsym___cmd_fork_desc
00000000400e2778 R __fsym___cmd_fork_name
00000000400e2788 R __fsym___cmd_m_desc
00000000400e27a8 R __fsym___cmd_m_name
00000000400e27b0 R __fsym___cmd_p_desc
00000000400e27d0 R __fsym___cmd_p_name
00000000400e27d8 R __fsym___cmd_panic_desc
00000000400e27f0 R __fsym___cmd_panic_name
00000000400e2800 R __fsym___cmd_reboot_desc
00000000400e2818 R __fsym___cmd_reboot_name
00000000400e2828 R __fsym___cmd_top_desc
00000000400e2838 R __fsym___cmd_top_name
00000000400e2848 R __fsym___cmd_mmlk_desc
00000000400e2868 R __fsym___cmd_mmlk_name
00000000400e2878 R __fsym___cmd_backtrace_desc
00000000400e2890 R __fsym___cmd_backtrace_name
00000000400e28a0 R __fsym___cmd_date_desc
00000000400e28b0 R __fsym___cmd_date_name
00000000400e28c0 R __fsym___cmd_exit_desc
00000000400e28d8 R __fsym___cmd_exit_name
00000000400e28e8 R __fsym___cmd_send_key_desc
00000000400e2910 R __fsym___cmd_send_key_name
00000000400e2920 R __fsym___cmd_uninsmod_desc
00000000400e2930 R __fsym___cmd_uninsmod_name
00000000400e2940 R __fsym___cmd_insmod_desc
00000000400e2950 R __fsym___cmd_insmod_name
00000000400e2960 R __fsym_update_desc
00000000400e2970 R __fsym_update_name
00000000400e2978 R __fsym___cmd_mtop_desc
00000000400e2988 R __fsym___cmd_mtop_name
00000000400e2998 R __fsym_ths_set_s_desc
00000000400e29b0 R __fsym_ths_set_s_name
00000000400e29c0 R __fsym___cmd_tail_desc
00000000400e29d8 R __fsym___cmd_tail_name
00000000400e29e8 R __fsym___cmd_touch_desc
00000000400e2a00 R __fsym___cmd_touch_name
00000000400e2a10 R __fsym___cmd_grep_desc
00000000400e2a28 R __fsym___cmd_grep_name
00000000400e2a38 R __fsym___cmd_hd_desc
00000000400e2a58 R __fsym___cmd_hd_name
00000000400e2a68 R __fsym___cmd_hexdump_desc
00000000400e2a88 R __fsym___cmd_hexdump_name
00000000400e2a98 R __fsym___cmd_ll_desc
00000000400e2ab0 R __fsym___cmd_ll_name
00000000400e2ac0 R __fsym___cmd_ls_desc
00000000400e2ad8 R __fsym___cmd_ls_name
00000000400e2ae8 R __fsym___cmd_rm_desc
00000000400e2b08 R __fsym___cmd_rm_name
00000000400e2b18 R __fsym___cmd_remove_file_test_desc
00000000400e2b30 R __fsym___cmd_remove_file_test_name
00000000400e2b48 R __fsym___cmd_rwcheck_desc
00000000400e2b60 R __fsym___cmd_rwcheck_name
00000000400e2b70 R __fsym___cmd_format_desc
00000000400e2b88 R __fsym___cmd_format_name
00000000400e2b98 R __fsym___cmd_umount_desc
00000000400e2bb0 R __fsym___cmd_umount_name
00000000400e2bc0 R __fsym___cmd_mount_desc
00000000400e2bd8 R __fsym___cmd_mount_name
00000000400e2bf0 r sunxi_hal_avs_driver
00000000400e2c18 r __func__.4569
00000000400e2c30 r __func__.4584
00000000400e2c48 r __func__.4584
00000000400e2c58 r __func__.4598
00000000400e2c70 r __func__.4614
00000000400e2c88 r __func__.4627
00000000400e2ca0 r __func__.5991
00000000400e2ce8 r sunxi_hal_uart_driver
00000000400e2d90 r g_uart_baudrate_map
00000000400e2dd8 r g_uart_baudrate_map
00000000400e2e50 r __func__.8578
00000000400e2f50 r __func__.8839
00000000400e2f60 r __func__.8851
00000000400e2f70 r __func__.8865
00000000400e2f80 r __func__.8888
00000000400e2f98 r __func__.8924
00000000400e2fb0 r __func__.8944
00000000400e2fc8 r __func__.8944
00000000400e2fd8 r __func__.8757
00000000400e2fe8 r __func__.8770
00000000400e2ff8 r __func__.8784
00000000400e3008 r __func__.8803
00000000400e3018 r __func__.8823
00000000400e3058 r sunxi_hal_twi_driver
00000000400e3088 r sunxi_hal_pwm_driver
00000000400e30b8 r __func__.4938
00000000400e30f8 r __func__.8246
00000000400e31d0 r __func__.7863
00000000400e32b8 r sunxi_hal_usb_driver
00000000400e3360 r __func__.4255
00000000400e3500 R ccu_mux_ops
00000000400e3588 R ccu_nm_ops
00000000400e3658 R ccu_reset_ops
00000000400e3678 R ccu_div_ops
00000000400e3700 R ccu_gate_ops
00000000400e3788 R ccu_mp_ops
00000000400e3810 R ccu_mult_ops
00000000400e3898 R ccu_nkmp_ops
00000000400e3920 R clk_fixed_factor_ops
00000000400e39a8 R clk_fixed_rate_ops
00000000400e3b98 r __compound_literal.229
00000000400e3bb8 r apb0_apb1_parents
00000000400e3bd8 r ce_parents
00000000400e3bf0 r cpux_parents
00000000400e3c28 r csi0_mclk_parents
00000000400e3c58 r csi_top_parents
00000000400e3c70 r de_di_g2d_parents
00000000400e3c90 r dmic_codec_parents
00000000400e3ca8 r dram_parents
00000000400e3cc8 r dsp_parents
00000000400e3ce8 r fanout_27m_parents
00000000400e3cf8 r fanout_parents
00000000400e3d30 r hdmi_cec_parents
00000000400e3d40 r hdmi_tcon_tve_parents
00000000400e3d70 r i2s2_asrc_parents
00000000400e3d90 r i2s_spdif_tx_parents
00000000400e3db0 r ir_parents
00000000400e3dc0 r ledc_parents
00000000400e3dd0 r mipi_dsi_parents
00000000400e3df8 r mmc0_mmc1_parents
00000000400e3e18 r mmc2_parents
00000000400e3e40 r pll_regs
00000000400e3e60 r psi_ahb_parents
00000000400e3e80 r riscv_parents
00000000400e3eb8 r spdif_rx_parents
00000000400e3ed0 r spi_parents
00000000400e3ef8 r sun8iw20_ccu_desc
00000000400e3f28 r tpadc_parents
00000000400e3f38 r tvd_parents
00000000400e3f58 r ve_parents
00000000400e3f68 r ahbs_apbs0_parents
00000000400e3f88 r r_apb0_ir_rx_parents
00000000400e3f98 r sun8iw20_r_ccu_desc
00000000400e3fc8 r osc32k_parents
00000000400e3fd8 r rtc32k_clk_parents
00000000400e3fe8 r rtc_32k_fanout_clk_parents
00000000400e4000 r sun8iw20_rtc_ccu_desc
00000000400e4030 r __func__.8908
00000000400e4048 r __func__.8940
00000000400e4068 r __func__.8955
00000000400e40a8 r hal_uart_driver
00000000400e41d0 r month_days
00000000400e4208 r rtc_days_in_month
00000000400e4260 r sunxi_spi_sample_mode
00000000400e4280 r CSWTCH.57
00000000400e4298 r sun8iw20p1_bank_base
00000000400e42b0 r sun8iw20p1_bank_irq_num
00000000400e42c8 r sun8iw20p1_irq_bank_base
00000000400e4410 r __func__.4590
00000000400e4420 r __func__.4595
00000000400e4430 r __func__.4601
00000000400e4440 r __func__.4606
00000000400e4450 r __func__.4571
00000000400e4460 r __func__.4571
00000000400e4488 r __func__.4555
00000000400e4498 r __func__.4560
00000000400e44f0 r __func__.4542
00000000400e4508 r __func__.4546
00000000400e4528 r __func__.4551
00000000400e4540 r __func__.4557
00000000400e4558 r __func__.4562
00000000400e4570 r __func__.4567
00000000400e4588 r __func__.4567
00000000400e4598 r __func__.8933
00000000400e45a8 r __func__.8942
00000000400e45c0 r __func__.8972
00000000400e45d8 r __func__.9002
00000000400e45e8 r __func__.9012
00000000400e45f8 r __func__.9040
00000000400e4610 r __func__.9047
00000000400e4620 r __func__.9053
00000000400e4630 r __func__.9065
00000000400e4640 r __func__.9071
00000000400e4650 r __func__.9077
00000000400e4660 r __func__.9082
00000000400e4ce0 r uhs_speeds.8984
00000000400e4d08 r __func__.8626
00000000400e4d28 r __func__.8632
00000000400e54d0 r __FUNCTION__.4918
00000000400e54e0 r __FUNCTION__.9322
00000000400e54f0 r __FUNCTION__.9326
00000000400e5508 r __FUNCTION__.9369
00000000400e5520 r __FUNCTION__.9537
00000000400e5538 r __FUNCTION__.9664
00000000400e5550 r __FUNCTION__.9763
00000000400e5560 r __FUNCTION__.9782
00000000400e5578 r __FUNCTION__.9798
00000000400e5588 r __FUNCTION__.9840
00000000400e5598 r __func__.8948
00000000400e55a8 r __func__.8948
00000000400e55b8 r __func__.9126
00000000400e55d0 r __func__.9167
00000000400e55e8 r __func__.9247
00000000400e5600 r __func__.9336
00000000400e5618 r __func__.9447
00000000400e5630 r __func__.9596
00000000400e5648 r __func__.9612
00000000400e5658 r __func__.9679
00000000400e5670 r __func__.9759
00000000400e5680 r __func__.9774
00000000400e5698 r __func__.9796
00000000400e56a8 r __func__.9809
00000000400e56b8 r __func__.9847
00000000400e56c8 r __func__.9856
00000000400e56d8 r __func__.9861
00000000400e6798 r __FUNCTION__.8966
00000000400e67b0 r __func__.8965
00000000400e67c8 r __func__.8978
00000000400e67e0 r __func__.9165
00000000400e67f0 r __func__.9182
00000000400e6808 r __func__.9189
00000000400e6820 r __func__.9257
00000000400e6830 r __func__.9274
00000000400e6840 r __func__.9282
00000000400e6858 r __func__.9291
00000000400e6870 r __func__.9308
00000000400e6888 r __func__.9338
00000000400e68a0 r __func__.9349
00000000400e6f00 r tran_exp
00000000400e6f20 r tran_mant
00000000400e6f30 r __func__.8907
00000000400e6f40 r __func__.9004
00000000400e6f58 r __func__.9013
00000000400e6f70 r __func__.9116
00000000400e6f88 r __func__.9132
00000000400e6fa0 r __func__.9146
00000000400e6fb0 r __func__.9160
00000000400e6fc0 r __func__.9198
00000000400e6fd8 r __func__.9220
00000000400e6fe8 r __func__.9234
00000000400e7000 r cis_tpl_funce_list
00000000400e7030 r cis_tpl_list
00000000400e7520 r speed_unit
00000000400e7540 r speed_val
00000000400e7550 r __func__.8725
00000000400e7560 r __func__.8765
00000000400e78d0 r CSWTCH.17
00000000400e78d8 r __func__.8176
00000000400e78f0 r __func__.8183
00000000400e7908 r __func__.8205
00000000400e7920 r __func__.8216
00000000400e7938 r __func__.8231
00000000400e7950 r __func__.8253
00000000400e7970 r __func__.8259
00000000400e7988 r __func__.8282
00000000400e79a0 r __func__.8307
00000000400e79b0 r __func__.8321
00000000400e79c0 r __func__.8331
00000000400e79d8 r __func__.8355
00000000400e7b58 r __FUNCTION__.9152
00000000400e7b68 r __FUNCTION__.9213
00000000400e7b78 r __FUNCTION__.9269
00000000400e7b90 r __FUNCTION__.9280
00000000400e7ba0 r __FUNCTION__.9290
00000000400e7bb0 r __FUNCTION__.9298
00000000400e7bc0 r __FUNCTION__.9323
00000000400e7bd8 r __FUNCTION__.9349
00000000400e7bf8 r __FUNCTION__.9356
00000000400e7c18 r __FUNCTION__.9363
00000000400e7c38 r __FUNCTION__.9374
00000000400e7c50 r __FUNCTION__.9381
00000000400e7c68 r __FUNCTION__.9389
00000000400e7c80 r __FUNCTION__.9408
00000000400e7c90 r __FUNCTION__.9436
00000000400e7ca8 r __FUNCTION__.9448
00000000400e7cb8 r __FUNCTION__.9458
00000000400e7cc8 r __FUNCTION__.9464
00000000400e7cd8 r __FUNCTION__.9508
00000000400e7cf0 r __FUNCTION__.9584
00000000400e7d08 r __func__.9375
00000000400e7d20 r __func__.9384
00000000400e8818 r __FUNCTION__.8931
00000000400e8830 r __FUNCTION__.8936
00000000400e8848 r __FUNCTION__.8949
00000000400e8868 r __FUNCTION__.8956
00000000400e8880 r __FUNCTION__.8967
00000000400e8898 r __FUNCTION__.8972
00000000400e88b8 r __FUNCTION__.9036
00000000400e88e0 r __func__.9037
00000000400e8b18 r __FUNCTION__.6030
00000000400e8b30 r __FUNCTION__.6086
00000000400e8b48 r __FUNCTION__.6112
00000000400e8b68 r __FUNCTION__.6136
00000000400e8b80 r __FUNCTION__.6145
00000000400e8b90 r __FUNCTION__.6158
00000000400e8ba8 r __FUNCTION__.6164
00000000400e8bb8 r __FUNCTION__.6169
00000000400e8bc0 r __FUNCTION__.6204
00000000400e8bd0 r __FUNCTION__.6244
00000000400e8be8 r __FUNCTION__.6287
00000000400e8c00 r __FUNCTION__.6316
00000000400e8c10 r __FUNCTION__.6333
00000000400e8c28 r __FUNCTION__.6345
00000000400e8c38 r __FUNCTION__.6354
00000000400e8c50 r __FUNCTION__.6453
00000000400e8c68 r __FUNCTION__.6469
00000000400e8c80 r __FUNCTION__.6487
00000000400e8c98 r __FUNCTION__.6500
00000000400e8ca8 r __FUNCTION__.6511
00000000400e8cc0 r __FUNCTION__.6519
00000000400e8cd0 r __FUNCTION__.6527
00000000400e8ce0 r __FUNCTION__.6539
00000000400e8cf0 r __FUNCTION__.6554
00000000400e8d00 r __FUNCTION__.6591
00000000400e8d18 r __FUNCTION__.6617
00000000400e8d30 r __FUNCTION__.6617
00000000400e8d48 r __FUNCTION__.6648
00000000400e9560 R snd_pcm_known_rates
00000000400e9570 r __FUNCTION__.5653
00000000400e9580 r __FUNCTION__.5670
00000000400e9590 r __FUNCTION__.5687
00000000400e95a8 r __FUNCTION__.5703
00000000400e95b8 r __FUNCTION__.5719
00000000400e95d0 r __FUNCTION__.5758
00000000400e9698 r __FUNCTION__.10075
00000000400e96b0 r __FUNCTION__.10085
00000000400e96c8 r __FUNCTION__.10124
00000000400e96e8 r __FUNCTION__.10152
00000000400e9700 r __FUNCTION__.10208
00000000400e9718 r lineout_mux
00000000400e9728 r micin_select
00000000400e9738 r pb_audio_route_text
00000000400e9768 r sample_rate_conv
00000000400e9b68 r __FUNCTION__.9360
00000000400e9b88 r __FUNCTION__.9364
00000000400e9ba8 r __FUNCTION__.9368
00000000400e9bc8 r __FUNCTION__.9395
00000000400e9c38 r sunxi_cpudai
00000000400e9d18 r __FUNCTION__.8962
00000000400e9d30 r __FUNCTION__.8999
00000000400e9d40 r __FUNCTION__.9019
00000000400e9d50 r __FUNCTION__.9033
00000000400e9ee8 R sunxi_pcm_ops
00000000400e9f30 r CSWTCH.19
00000000400e9f38 r CSWTCH.20
00000000400e9f40 r CSWTCH.20
00000000400e9f48 r __FUNCTION__.10088
00000000400e9f68 r __FUNCTION__.10108
00000000400e9f88 r __FUNCTION__.10121
00000000400e9fa8 r __FUNCTION__.10177
00000000400e9fc0 r __FUNCTION__.10197
00000000400e9fd8 r __FUNCTION__.10225
00000000400e9ff0 r __FUNCTION__.10238
00000000400ea008 r __FUNCTION__.10312
00000000400ea028 r __FUNCTION__.10330
00000000400ea040 r __FUNCTION__.10336
00000000400ea058 r __FUNCTION__.10344
00000000400ea070 r __FUNCTION__.10354
00000000400ea088 r __FUNCTION__.10361
00000000400ea0a8 r __FUNCTION__.10367
00000000400ea0c8 r __FUNCTION__.10383
00000000400ea0e0 r __FUNCTION__.10418
00000000400ea0f8 r __FUNCTION__.10425
00000000400ea118 r __FUNCTION__.10443
00000000400ea1c8 r sunxi_daudio_controls
00000000400ea3a8 r sunxi_daudio_dai
00000000400eaad8 r CSWTCH.44
00000000400eab48 r __FUNCTION__.10196
00000000400eab60 r __FUNCTION__.10206
00000000400eab70 r __FUNCTION__.10428
00000000400eab88 r __FUNCTION__.10439
00000000400eab98 r __FUNCTION__.10470
00000000400eaba8 r __FUNCTION__.10493
00000000400eabb8 r __FUNCTION__.10501
00000000400eabc8 r __FUNCTION__.10515
00000000400eabe0 r __FUNCTION__.10520
00000000400eabf8 r __FUNCTION__.10659
00000000400eac10 r __FUNCTION__.10722
00000000400eac28 r __FUNCTION__.10789
00000000400eac40 r __FUNCTION__.10801
00000000400eac58 r __FUNCTION__.10822
00000000400eac70 r __FUNCTION__.10864
00000000400eac88 r __FUNCTION__.10911
00000000400eac98 r __func__.10159
00000000400eacb0 r __func__.10164
00000000400eacc8 r __func__.10169
00000000400eace0 r __func__.10174
00000000400eacf8 r __func__.10181
00000000400ead10 r __func__.10188
00000000400ead28 r __func__.10213
00000000400ead38 r __func__.10225
00000000400ead50 r __func__.10362
00000000400ead68 r __func__.10367
00000000400ead78 r __func__.10372
00000000400ead98 r __func__.10378
00000000400eadc0 r __func__.10389
00000000400eade8 r __func__.10400
00000000400eae10 r __func__.10411
00000000400eae30 r __func__.10421
00000000400eae50 r __func__.10427
00000000400eae68 r __func__.10433
00000000400eae78 r __func__.10438
00000000400eae88 r __func__.10444
00000000400eae98 r __func__.10449
00000000400eaea8 r __func__.10453
00000000400eaeb8 r __func__.10459
00000000400eaec8 r __func__.10464
00000000400eaed8 r __func__.10469
00000000400eaee8 r __func__.10474
00000000400eaef8 r __func__.10481
00000000400eaf08 r __func__.10488
00000000400eaf18 r __func__.10492
00000000400eaf28 r __func__.10499
00000000400eaf38 r __func__.10510
00000000400eaf48 r __func__.10514
00000000400eaf60 r __func__.10519
00000000400eaf78 r __func__.10527
00000000400eaf88 r __func__.10548
00000000400eafa0 r __func__.10548
00000000400eafb8 r __func__.10553
00000000400eafd0 r __func__.10557
00000000400eaff0 r __func__.10589
00000000400eb000 r __func__.10681
00000000400eb020 r __func__.10757
00000000400eb038 r __func__.10800
00000000400eb050 r __func__.10821
00000000400eb068 r __func__.10850
00000000400eb080 r __func__.10863
00000000400eb098 r __func__.10874
00000000400eb0a8 r __func__.10910
00000000400eb0b8 r __func__.10916
00000000400eb0c8 r __func__.10920
00000000400eb0d8 r __func__.6499
00000000400eb1d0 r snd_pcm_access_names
00000000400eb530 r snd_pcm_format_names
00000000400eb938 r snd_pcm_stream_names
00000000400eb9e8 r snd_pcm_type_names
00000000400ebae8 r _snd_pcm_open_funcs
00000000400ebb88 r __FUNCTION__.6722
00000000400ebba0 r __FUNCTION__.6794
00000000400ebbb8 r __FUNCTION__.6809
00000000400ebbd0 r __func__.6624
00000000400ebbe8 r __func__.6630
00000000400ebc00 r __func__.6630
00000000400ebc20 r __func__.6636
00000000400ebc38 r __func__.6643
00000000400ebc50 r __func__.6652
00000000400ebc68 r __func__.6679
00000000400ebc80 r __func__.6685
00000000400ebc98 r __func__.6690
00000000400ebcb0 r __func__.6697
00000000400ebcc0 r __func__.6702
00000000400ebcd8 r __func__.6709
00000000400ebcf0 r __func__.6716
00000000400ebd08 r __func__.6721
00000000400ebd20 r __func__.6721
00000000400ebd38 r __func__.6729
00000000400ebd50 r __func__.6737
00000000400ebe78 r snd_pcm_hw_fast_ops
00000000400ebf18 r snd_pcm_hw_ops
00000000400ec008 r __FUNCTION__.6786
00000000400ec028 r __FUNCTION__.6786
00000000400ec048 r __FUNCTION__.6792
00000000400ec068 r __FUNCTION__.6792
00000000400ec088 r __FUNCTION__.7057
00000000400ec0a8 r __FUNCTION__.7077
00000000400ec0c8 r __FUNCTION__.7084
00000000400ec0f0 r __FUNCTION__.7093
00000000400ec120 r __FUNCTION__.7101
00000000400ec140 r __FUNCTION__.7116
00000000400ec160 r __FUNCTION__.7129
00000000400ec190 r __FUNCTION__.7135
00000000400ec1b0 r __FUNCTION__.7162
00000000400ec1d0 r __FUNCTION__.7184
00000000400ec1f0 r __FUNCTION__.7234
00000000400eca80 r __FUNCTION__.7014
00000000400eccd8 r snd_pcm_dsnoop_fast_ops
00000000400ecd78 r snd_pcm_dsnoop_ops
00000000400ece08 r __FUNCTION__.6624
00000000400ece28 r __FUNCTION__.6630
00000000400ece48 r __FUNCTION__.6890
00000000400ece60 r __FUNCTION__.6907
00000000400ece70 r __FUNCTION__.6922
00000000400ece80 r __FUNCTION__.6922
00000000400ece98 r __FUNCTION__.7063
00000000400eceb0 r __FUNCTION__.7205
00000000400ed040 r snd_pcm_dmix_fast_ops
00000000400ed0e0 r snd_pcm_dmix_ops
00000000400ed1d0 r __FUNCTION__.6459
00000000400ed1f0 r __FUNCTION__.6480
00000000400ed208 r __FUNCTION__.6505
00000000400ed228 r __FUNCTION__.6512
00000000400ed238 r __FUNCTION__.6555
00000000400ed248 r __func__.6511
00000000400ed258 r __func__.6554
00000000400ed3d0 r __FUNCTION__.6208
00000000400ed3f0 r __FUNCTION__.6229
00000000400ed410 r __FUNCTION__.6291
00000000400ed430 r __func__.5756
00000000400ed448 r __func__.5760
00000000400ed460 R snd_pcm_plugin_fast_ops
00000000400ed5a0 r __FUNCTION__.7455
00000000400ed5c0 r __FUNCTION__.7615
00000000400ed5d8 r __func__.6577
00000000400ed5f8 r __func__.6581
00000000400ed618 r __func__.7430
00000000400ed638 r __func__.7595
00000000400ed6f8 r add_labels.7315
00000000400ed718 r conv_labels.7027
00000000400edb18 r get32_labels.7149
00000000400edbf8 r get32_labels.7258
00000000400edcd8 r norm_labels.7320
00000000400edcf8 r put32_labels.7176
00000000400eddd8 r put32_labels.7285
00000000400ee088 r snd_pcm_route_ops
00000000400ee0e8 r zero_labels.7312
00000000400ee0f8 r __FUNCTION__.7304
00000000400ee110 r __FUNCTION__.7382
00000000400ee130 r __FUNCTION__.7399
00000000400ee148 r __FUNCTION__.7422
00000000400ee160 r __func__.6180
00000000400ee180 r __func__.6184
00000000400ee390 r preset_dB_value
00000000400ee858 r snd_pcm_softvol_ops
00000000400ee8b8 r CSWTCH.0
00000000400ee8c8 r CSWTCH.0
00000000400ee8d8 r linear_formats
00000000400ee918 r __FUNCTION__.6881
00000000400ee938 r __func__.6189
00000000400ee958 r __func__.6193
00000000400ee978 r __func__.6897
00000000400ee990 r __func__.6897
00000000400ee9b0 r conv_labels.6624
00000000400eedb0 r get32_labels.6746
00000000400eee90 r put32_labels.6773
00000000400ef030 r snd_pcm_linear_ops
00000000400ef090 r __FUNCTION__.6830
00000000400ef0b0 r __FUNCTION__.6852
00000000400ef0d0 r __FUNCTION__.6859
00000000400ef0f0 r __FUNCTION__.6918
00000000400ef110 r __FUNCTION__.6948
00000000400ef130 r __FUNCTION__.7044
00000000400ef148 r __func__.6746
00000000400ef160 r __func__.6774
00000000400ef180 r __func__.6798
00000000400ef280 r funcs.6870
00000000400ef2b0 r linear_format_widths
00000000400ef2d0 r linear_preferred_formats
00000000400ef588 r snd_pcm_plug_ops
00000000400ef5e8 r __FUNCTION__.6908
00000000400ef600 r __FUNCTION__.7262
00000000400ef618 r __FUNCTION__.7276
00000000400ef630 r __func__.6248
00000000400ef648 r __func__.6254
00000000400ef668 r __func__.6254
00000000400ef688 r __func__.6258
00000000400ef6a8 r __func__.6258
00000000400ef6c8 r __func__.7261
00000000400ef768 r get16_labels.6950
00000000400ef848 r put16_labels.7002
00000000400ef988 r snd_pcm_rate_fast_ops
00000000400efb50 r snd_pcm_rate_ops
00000000400efbb0 r __FUNCTION__.6688
00000000400efbc8 r __FUNCTION__.6940
00000000400efbe0 r __func__.6912
00000000400efe48 r snd_pcm_multi_fast_ops
00000000400eff80 r snd_pcm_multi_ops
00000000400effe0 r __FUNCTION__.6855
00000000400efff8 r __FUNCTION__.6887
00000000400f0018 r __func__.6465
00000000400f0028 r __func__.6472
00000000400f0038 r __func__.6489
00000000400f0050 r __func__.6495
00000000400f0070 r __func__.6501
00000000400f0090 r __func__.6506
00000000400f00a8 r __func__.6521
00000000400f00c0 r __func__.6529
00000000400f00e0 r __func__.6537
00000000400f0100 r __func__.6544
00000000400f0120 r __func__.6559
00000000400f0140 r __func__.6567
00000000400f0160 r __func__.6573
00000000400f0180 r __func__.6587
00000000400f01a0 r __func__.6604
00000000400f01c0 r __func__.6620
00000000400f01e0 r __func__.6646
00000000400f0200 r __func__.6662
00000000400f0220 r __func__.6680
00000000400f0240 r __func__.6707
00000000400f0258 r __func__.6717
00000000400f0270 r __func__.6733
00000000400f03a0 r refine_intervals
00000000400f04d8 r refine_rules
00000000400f08a0 r __FUNCTION__.6982
00000000400f08b0 r __FUNCTION__.7013
00000000400f08c8 r __FUNCTION__.7052
00000000400f08e0 r get16_labels.6737
00000000400f09c0 r get16_labels.6882
00000000400f0b18 r put16_labels.6764
00000000400f0bf8 r put16_labels.6909
00000000400f0cd8 r pcm_src_ops
00000000400f0d40 r pcm_src_ops
00000000400f0da8 r _KAISER10
00000000400f0db8 r _KAISER12
00000000400f0dc8 r _KAISER6
00000000400f0dd8 r _KAISER8
00000000400f0e00 r kaiser10_table
00000000400f0f20 r kaiser12_table
00000000400f1140 r kaiser6_table
00000000400f1260 r kaiser8_table
00000000400f1380 r quality_map
00000000400f14c0 R _snd_pcm_global_configs_size
00000000400f14c8 r snd_audiocodec_hw_config
00000000400f14d8 r snd_pcm_asym_config
00000000400f14f0 r snd_pcm_dmix1_config
00000000400f1530 r snd_pcm_dmix2_config
00000000400f1570 r snd_pcm_dmix_config
00000000400f15b0 r snd_pcm_dsnoop_config
00000000400f15f0 R snd_pcm_global_configs
00000000400f1740 r snd_pcm_plug1_config
00000000400f1780 r snd_pcm_plug2_config
00000000400f17c0 r snd_pcm_plug_config
00000000400f1800 r snd_pcm_softvol1_config
00000000400f1840 r snd_pcm_softvol2_config
00000000400f1880 r snd_pcm_softvol_config
00000000400f18c0 r snd_snddaudio1_hw_config
00000000400f18d0 r snd_snddaudio2_hw_config
00000000400f1938 r __func__.5712
00000000400f1ba8 r CSWTCH.1
00000000400f28d8 r __FUNCTION__.5667
00000000400f2978 r CSWTCH.58
00000000400f2978 r CSWTCH.59
00000000400f2980 r CSWTCH.64
00000000400f2988 r CSWTCH.65
00000000400f2990 r hal_twi_address
00000000400f29c8 r hal_twi_irq_num
00000000400f29e0 r CSWTCH.120
00000000400f29e8 r CSWTCH.121
00000000400f2b00 r pwm_gpio
00000000400f2d48 r __func__.10354
00000000400f2e78 r __func__.18550
00000000400f3180 r fs_rh_config_descriptor
00000000400f31a0 r hs_rh_config_descriptor
00000000400f31c0 r usb11_rh_dev_descriptor
00000000400f31d8 r usb2_rh_dev_descriptor
00000000400f31f0 r __func__.10397
00000000400f3200 r __func__.10475
00000000400f33a8 r __func__.11156
00000000400f33b8 r __func__.11192
00000000400f33d0 r __func__.11766
00000000400f33e0 r __func__.11781
00000000400f33f0 r __func__.11927
00000000400f3400 r __func__.11986
00000000400f35e8 r event_delays_ns
00000000400f3648 r event_handlers
00000000400f38d0 r smask_out.11578
00000000400f3990 r sunxi_ehci_hc_driver
00000000400f3b08 r cc_to_error
00000000400f3bb8 r sunxi_ohci_hc_driver
00000000400f3db0 r __func__.9749
00000000400f3dc8 r __func__.9779
00000000400f3de0 r __func__.9784
00000000400f3fb8 r __func__.9566
00000000400f3fd0 r __func__.9592
00000000400f4080 r __func__.9605
00000000400f4098 r __func__.9615
00000000400f40b0 r __func__.9625
00000000400f40c8 r __func__.9657
00000000400f46a8 r CSWTCH.28
00000000400f46c0 r usb_ehci_table
00000000400f4710 r usb_ohci_table
00000000400f4760 r usb_otg_table
00000000400f56f0 r clk_tbl
00000000400f5780 r CSWTCH.139
00000000400f5790 r sun20iw1_de_features
00000000400f57f0 r sun20iw1_de_is_support_lbc
00000000400f5800 r sun20iw1_de_is_support_scale
00000000400f5810 r sun20iw1_de_is_support_smbl
00000000400f5818 r sun20iw1_de_is_support_vep
00000000400f5828 r sun20iw1_de_is_support_wb
00000000400f5830 r sun20iw1_de_num_chns
00000000400f5838 r sun20iw1_de_num_layers
00000000400f5848 r sun20iw1_de_num_vi_chns
00000000400f5850 r sun20iw1_de_scale_line_buffer
00000000400f5858 r sun20iw1_de_supported_output_types
00000000400f5860 r enhance_mode_to_para
00000000400f5aa0 r de_clk_tbl
00000000400f5d00 r __func__.8619
00000000400f5df0 r __func__.7902
00000000400f5e00 r __func__.7929
00000000400f5e18 r __func__.7933
00000000400f5e30 r __func__.7946
00000000400f6028 r __func__.8397
00000000400f6038 r __func__.8442
00000000400f6050 r __func__.8448
00000000400f6318 r di_ioctls
00000000400f6518 r __func__.9072
00000000400f6530 r __func__.9078
00000000400f68f0 r __func__.2831
00000000400f6d10 r lopts.9134
00000000400f7500 r __func__.7811
00000000400f7f28 r __FUNCTION__.2789
00000000400f7f50 r __func__.6980
00000000400f7f60 r __func__.6992
00000000400f7f70 r __func__.6998
00000000400f7f88 r __func__.7004
00000000400f7f98 r __func__.7011
00000000400f7fb0 r __func__.7023
00000000400f80a8 R SWIHandler_entity_NLIBOPS
00000000400f8310 r crc32_table
00000000400f8710 r crc32_table
00000000400f8d70 r __FUNCTION__.8651
00000000400f8d80 r __FUNCTION__.8685
00000000400f8d88 r __FUNCTION__.8696
00000000400f8d98 r __FUNCTION__.8703
00000000400f8da0 r __FUNCTION__.8722
00000000400f8da8 r __FUNCTION__.8749
00000000400f8f08 r __FUNCTION__.5238
00000000400f92c8 r __FUNCTION__.5390
00000000400f92e0 r __FUNCTION__.5387
00000000400f92f8 r __FUNCTION__.5395
00000000400f9310 r __FUNCTION__.5403
00000000400f9328 r __FUNCTION__.5409
00000000400f9340 r __FUNCTION__.5421
00000000400f9358 r __FUNCTION__.5444
00000000400f9370 r __FUNCTION__.5454
00000000400f9388 r __FUNCTION__.5461
00000000400f93a0 r __FUNCTION__.5468
00000000400f93b8 r _device_fops
00000000400f9418 r _device_fs
00000000400f94b8 r __FUNCTION__.8826
00000000400f94d0 r __FUNCTION__.8906
00000000400f94e8 r _exfat_melis_vfs
00000000400f9538 r _fat_melis_vfs
00000000400f9588 r _melis_devfs_melis_vfs
00000000400f95d8 r _melis_vfs_fops
00000000400f9638 r _minfs_melis_vfs
00000000400f9688 r _ntfs_melis_vfs
00000000400f9710 r _root_fops
00000000400f9770 r _rootfs
00000000400f97c0 r device_type_str
00000000400f9df8 r __FUNCTION__.5397
00000000400fa0a0 r __FUNCTION__.6223
00000000400fa5a8 r __FUNCTION__.7758
00000000400fa5b8 r __FUNCTION__.7778
00000000400fa820 r keymap
00000000400fb6d0 r __FUNCTION__.4480
00000000400fb738 r __FUNCTION__.3894
00000000400fb750 r __FUNCTION__.3913
00000000400fb760 r __FUNCTION__.3963
00000000400fb770 r __FUNCTION__.3981
00000000400fb788 r __FUNCTION__.3989
00000000400fb7a0 r __FUNCTION__.3995
00000000400fb7b8 r __FUNCTION__.4001
00000000400fb7d0 r __FUNCTION__.4007
00000000400fb7e8 r __FUNCTION__.4012
00000000400fb858 r __FUNCTION__.3741
00000000400fb870 r __FUNCTION__.3745
00000000400fb888 r __FUNCTION__.3750
00000000400fb8a8 r __FUNCTION__.3755
00000000400fb8c8 r __FUNCTION__.3760
00000000400fb8e8 r __FUNCTION__.3765
00000000400fb908 r __FUNCTION__.3770
00000000400fb928 r __FUNCTION__.3775
00000000400fb948 r __FUNCTION__.3780
00000000400fb968 r __FUNCTION__.3780
00000000400fb980 r __FUNCTION__.3785
00000000400fb9a0 r __FUNCTION__.3790
00000000400fb9c0 r __FUNCTION__.3795
00000000400fb9e0 r __FUNCTION__.3801
00000000400fb9f8 r __FUNCTION__.3807
00000000400fba38 R pthread_default_attr
00000000400fba78 r __FUNCTION__.3786
00000000400fbab8 r __func__.9590
00000000400fbb18 r __func__.12090
00000000400fbb38 r __func__.12114
00000000400fbc98 r v4l2_fops
00000000400fbe48 r standards
00000000400fccb0 r std_type_ops
00000000400fde60 r v4l2_buf_ops
00000000400fde80 R vb2_dma_contig_memops
00000000400fdef8 r CSWTCH.126
00000000400fdf10 r sizes.15501
00000000400fdf20 r tvd_ctrl_ops
00000000400fdf38 r tvd_fops
00000000400fdf78 r tvd_ioctl_ops
00000000400fe278 r tvd_template
00000000400ff268 r tvd_video_qops
00000000400ff2c8 r __func__.2609
00000000400ff2e8 r charset2lower
00000000400ff3e8 r charset2upper
00000000400ff4e8 r utf8_table
00000000400ff650 r LKeyDevIds
00000000400ff750 R LKeyDevOps
00000000400ff788 r LTSDevIds
00000000400ff888 R LTSDevOps
00000000400ffa78 r seedtbl
00000000400ffb28 R SWIHandler_entity_CHS
00000000400ffb48 R SWIHandler_entity_CLK
00000000400ffbd8 R SWIHandler_entity_CONFIG
00000000400ffc38 R SWIHandler_entity_DEV
00000000400ffcb0 R SWIHandler_entity_DMA
00000000400ffd30 R SWIHandler_entity_EXEC
00000000400ffd50 R SWIHandler_entity_FSYS
00000000400ffef0 R SWIHandler_entity_HID
00000000400fff08 R SWIHandler_entity_INPUT
00000000400fff48 R SWIHandler_entity_INT
00000000400fff80 R SWIHandler_entity_KRNL
0000000040100268 R SWIHandler_entity_KSRV
0000000040100348 R SWIHandler_entity_MEM
00000000401003b8 R SWIHandler_entity_MEMS
00000000401004e0 R SWIHandler_entity_MODS
0000000040100518 R SWIHandler_entity_MSTUB
0000000040100530 R SWIHandler_entity_PINS
00000000401005b8 R SWIHandler_entity_PWM
0000000040100608 R SWIHandler_entity_RESM
0000000040100628 R SWIHandler_entity_SIOS
0000000040100650 R SWIHandler_entity_SVC
0000000040100718 R SWIHandler_entity_TIME
00000000401007d8 R SWIHandler_entity_USER
0000000040100800 R melis_syscall_table
00000000401008f0 R SWIHandler_entity_PTHREAD
0000000040100c48 r buffersize_index
0000000040100d50 R _ctype
0000000040100e50 r name.7957
0000000040100e88 r _vfs_valid_modes
0000000040100fc8 r __mon_yday
0000000040101000 R sys_tz
0000000040101008 r year_seconds
0000000040101230 r empty_fops.7904
00000000401012c0 R devfs_dir_operations
0000000040101318 R devfs_file_inode_operations
0000000040101360 R devfs_file_operations
00000000401013d0 r devfs_sops
0000000040101450 R minfs_dir_operations
00000000401014a8 R minfs_file_operations
0000000040101518 R minfs_dir_inode_operations
0000000040101560 R minfs_sops
00000000401015d8 r used_bit
00000000401016e8 R exfat_dir_ops
0000000040101740 R exfat_file_inode_ops
0000000040101788 R exfat_file_ops
00000000401017f0 r empty_fops.8314
0000000040101850 R exfat_dir_inode_ops
00000000401018b0 r exfat_sops
0000000040101928 r days_in_year
00000000401019a8 r upcase_table
0000000040103088 r ntfs_do_collate0x0
00000000401030a0 r ntfs_do_collate0x1
00000000401030c8 R ntfs_dir_inode_ops
0000000040103110 R ntfs_dir_ops
0000000040103168 R ntfs_file_inode_operations
00000000401031b0 R ntfs_file_operations
0000000040103220 r uc_byte_table.9406
0000000040103320 r uc_dup_table.9405
00000000401033e8 r uc_run_table.9404
00000000401035c8 r ntfs_sops
0000000040103670 R fat_dir_operations
00000000401036e8 R fat_file_inode_operations
0000000040103730 R fat_file_operations
00000000401037b0 r _FS_auto_rootcnt
00000000401037d8 r _FS_auto_secperclust
0000000040103840 r fat_sops
00000000401038b8 r day_n
0000000040103908 R vfat_dentry_ops
0000000040103a38 r subtypes
00000000401048f0 D __initcall0_start
00000000401048f0 D __initcall_start
00000000401048f0 d __initcall_videodev_init0
00000000401048f8 D __initcall1_start
00000000401048f8 d __initcall_disp_module_init1
0000000040104900 D __initcall2_start
0000000040104900 D __initcall3_start
0000000040104900 D __initcall4_start
0000000040104900 d __initcall_sunxi_driver_avs_init4
0000000040104908 d __initcall_drv_dma_init4
0000000040104910 d __initcall_sunxi_driver_twi_init4
0000000040104918 d __initcall_sunxi_driver_pwm_init4
0000000040104920 d __initcall_sunxi_driver_usb_init4
0000000040104928 d __initcall_hal_usb_core_init4
0000000040104930 D __initcall5_start
0000000040104930 d __initcall_sunxi_driver_audio_init5
0000000040104938 d __initcall_sunxi_soundcard_init5
0000000040104940 d __initcall_dfs_init5
0000000040104948 d __initcall_sunxi_env_initrootfs
0000000040104948 D __initcallrootfs_start
0000000040104950 d __initcall_melis_vfs_initrootfs
0000000040104958 d __initcall_dfs_rootfs_initrootfs
0000000040104960 D __initcall6_start
0000000040104960 d __initcall_sunxi_driver_spinor_init6
0000000040104968 d __initcall_g2d_probe_wrap6
0000000040104970 d __initcall_di_probe_wrap6
0000000040104978 d __initcall_init_adc_power6
0000000040104980 d __initcall_sunxi_driver_thermal_init6
0000000040104988 d __initcall_hal_usb_manager_init6
0000000040104990 D __initcall7_start
0000000040104990 d __initcall_sunxi_driver_spi_init7
0000000040104998 d __initcall_sunxi_wdt_init7
00000000401049a0 d __initcall_tv_drv_module_init7
00000000401049a8 d __initcall_hal_usb_hci_init7
00000000401049b0 d __initcall_tvd_module_init7
00000000401049b8 D __con_initcall_end
00000000401049b8 D __con_initcall_start
00000000401049b8 D __initcall_end
00000000401049e0 D __ctors_start__
00000000401049f0 D __ctors_end__
0000000040104a00 D preampvalue
0000000040104aa4 D opterr
0000000040104aa8 D optopt
0000000040104ab0 D _impure_ptr
0000000040104ab8 d impure_data
0000000040105200 D __global_locale
00000000401053a8 D _tzname
00000000401053b8 d tzinfo
0000000040105410 d fpi0.3249
0000000040105428 D environ
0000000040105430 D __fdlib_version
0000000040105438 D exception_table
0000000040105570 d arch_timer_clocksource
0000000040105598 D arch_timer_read_counter
00000000401055a0 D plic_chip
00000000401055d0 d irq_desc
0000000040108990 d rt_object_container
0000000040108ad0 d g_mem_leak_list
0000000040108af0 d spinor_device_fops
0000000040108b50 d melis_spinor_dev_op
0000000040108b78 d part_device_fops
0000000040108bd8 d spinor_acdev_op
0000000040108c00 d uart_device_fops
0000000040108c60 d uart_device_fops
0000000040108cc0 D uart_devop
0000000040108ce8 d evdev_list
0000000040108cf8 d input_dev_list
0000000040108d08 D sdmmc_dev_op
0000000040108d30 D disp_engine
0000000040108dd8 D tv_disp_engine
0000000040108e80 d reset_control_list
0000000040108e90 d clk_root_list
0000000040108ea0 d __compound_literal.0
0000000040108ea8 d __compound_literal.0
0000000040108ee0 d __compound_literal.0
0000000040108ee8 d __compound_literal.1
0000000040108f20 d __compound_literal.1
0000000040108f58 d __compound_literal.1
0000000040108f90 d __compound_literal.10
0000000040108f98 d __compound_literal.10
0000000040108fd0 d __compound_literal.10
0000000040108fd8 d __compound_literal.100
0000000040109010 d __compound_literal.101
0000000040109018 d __compound_literal.102
0000000040109050 d __compound_literal.103
0000000040109058 d __compound_literal.104
0000000040109090 d __compound_literal.105
0000000040109098 d __compound_literal.106
00000000401090d0 d __compound_literal.107
00000000401090d8 d __compound_literal.108
0000000040109110 d __compound_literal.109
0000000040109118 d __compound_literal.11
0000000040109150 d __compound_literal.11
0000000040109158 d __compound_literal.11
0000000040109190 d __compound_literal.110
00000000401091c8 d __compound_literal.111
00000000401091d0 d __compound_literal.112
0000000040109208 d __compound_literal.113
0000000040109240 d __compound_literal.114
0000000040109278 d __compound_literal.115
0000000040109280 d __compound_literal.116
00000000401092b8 d __compound_literal.117
00000000401092c0 d __compound_literal.118
00000000401092f8 d __compound_literal.119
0000000040109300 d __compound_literal.12
0000000040109308 d __compound_literal.12
0000000040109340 d __compound_literal.12
0000000040109378 d __compound_literal.120
00000000401093b0 d __compound_literal.121
00000000401093b8 d __compound_literal.122
00000000401093f0 d __compound_literal.123
0000000040109428 d __compound_literal.124
0000000040109430 d __compound_literal.125
0000000040109468 d __compound_literal.126
0000000040109470 d __compound_literal.127
00000000401094a8 d __compound_literal.128
00000000401094b0 d __compound_literal.129
00000000401094e8 d __compound_literal.13
0000000040109520 d __compound_literal.13
0000000040109528 d __compound_literal.13
0000000040109530 d __compound_literal.130
0000000040109568 d __compound_literal.131
00000000401095a0 d __compound_literal.132
00000000401095d8 d __compound_literal.133
0000000040109610 d __compound_literal.134
0000000040109618 d __compound_literal.135
0000000040109650 d __compound_literal.136
0000000040109658 d __compound_literal.137
0000000040109690 d __compound_literal.138
0000000040109698 d __compound_literal.139
00000000401096d0 d __compound_literal.14
00000000401096d8 d __compound_literal.14
0000000040109710 d __compound_literal.14
0000000040109748 d __compound_literal.140
0000000040109780 d __compound_literal.141
00000000401097b8 d __compound_literal.142
00000000401097c0 d __compound_literal.143
00000000401097f8 d __compound_literal.144
0000000040109830 d __compound_literal.145
0000000040109838 d __compound_literal.146
0000000040109870 d __compound_literal.147
00000000401098a8 d __compound_literal.148
00000000401098e0 d __compound_literal.149
00000000401098e8 d __compound_literal.15
0000000040109920 d __compound_literal.150
0000000040109958 d __compound_literal.151
0000000040109960 d __compound_literal.152
0000000040109998 d __compound_literal.153
00000000401099a0 d __compound_literal.154
00000000401099d8 d __compound_literal.155
00000000401099e0 d __compound_literal.156
0000000040109a18 d __compound_literal.157
0000000040109a20 d __compound_literal.158
0000000040109a58 d __compound_literal.159
0000000040109a60 d __compound_literal.16
0000000040109a68 d __compound_literal.160
0000000040109aa0 d __compound_literal.161
0000000040109aa8 d __compound_literal.162
0000000040109ae0 d __compound_literal.163
0000000040109ae8 d __compound_literal.164
0000000040109b20 d __compound_literal.165
0000000040109b28 d __compound_literal.166
0000000040109b60 d __compound_literal.167
0000000040109b68 d __compound_literal.168
0000000040109ba0 d __compound_literal.169
0000000040109ba8 d __compound_literal.17
0000000040109be0 d __compound_literal.170
0000000040109c18 d __compound_literal.171
0000000040109c50 d __compound_literal.172
0000000040109c58 d __compound_literal.173
0000000040109c90 d __compound_literal.174
0000000040109cc8 d __compound_literal.175
0000000040109cd0 d __compound_literal.176
0000000040109d08 d __compound_literal.177
0000000040109d40 d __compound_literal.178
0000000040109d48 d __compound_literal.179
0000000040109d80 d __compound_literal.18
0000000040109d88 d __compound_literal.180
0000000040109dc0 d __compound_literal.181
0000000040109dc8 d __compound_literal.182
0000000040109e00 d __compound_literal.183
0000000040109e38 d __compound_literal.184
0000000040109e40 d __compound_literal.185
0000000040109e78 d __compound_literal.186
0000000040109e80 d __compound_literal.187
0000000040109eb8 d __compound_literal.188
0000000040109ef0 d __compound_literal.189
0000000040109ef8 d __compound_literal.19
0000000040109f30 d __compound_literal.190
0000000040109f68 d __compound_literal.191
0000000040109f70 d __compound_literal.192
0000000040109fa8 d __compound_literal.193
0000000040109fe0 d __compound_literal.194
0000000040109fe8 d __compound_literal.195
000000004010a020 d __compound_literal.196
000000004010a058 d __compound_literal.197
000000004010a090 d __compound_literal.198
000000004010a098 d __compound_literal.199
000000004010a0d0 d __compound_literal.2
000000004010a0d8 d __compound_literal.2
000000004010a0e0 d __compound_literal.2
000000004010a0e8 d __compound_literal.20
000000004010a0f0 d __compound_literal.200
000000004010a128 d __compound_literal.201
000000004010a130 d __compound_literal.202
000000004010a168 d __compound_literal.203
000000004010a170 d __compound_literal.204
000000004010a1a8 d __compound_literal.205
000000004010a1e0 d __compound_literal.206
000000004010a1e8 d __compound_literal.207
000000004010a220 d __compound_literal.208
000000004010a258 d __compound_literal.209
000000004010a260 d __compound_literal.21
000000004010a298 d __compound_literal.210
000000004010a2d0 d __compound_literal.211
000000004010a2d8 d __compound_literal.212
000000004010a310 d __compound_literal.213
000000004010a318 d __compound_literal.214
000000004010a350 d __compound_literal.215
000000004010a358 d __compound_literal.216
000000004010a390 d __compound_literal.217
000000004010a398 d __compound_literal.218
000000004010a3d0 d __compound_literal.219
000000004010a3d8 d __compound_literal.22
000000004010a3e0 d __compound_literal.220
000000004010a418 d __compound_literal.221
000000004010a420 d __compound_literal.222
000000004010a458 d __compound_literal.223
000000004010a490 d __compound_literal.224
000000004010a498 d __compound_literal.225
000000004010a4d0 d __compound_literal.226
000000004010a508 d __compound_literal.227
000000004010a540 d __compound_literal.228
000000004010a578 d __compound_literal.23
000000004010a5b0 d __compound_literal.230
000000004010a5e8 d __compound_literal.231
000000004010a5f0 d __compound_literal.232
000000004010a628 d __compound_literal.233
000000004010a630 d __compound_literal.234
000000004010a668 d __compound_literal.235
000000004010a670 d __compound_literal.236
000000004010a6a8 d __compound_literal.237
000000004010a6b0 d __compound_literal.238
000000004010a6e8 d __compound_literal.239
000000004010a720 d __compound_literal.24
000000004010a728 d __compound_literal.240
000000004010a760 d __compound_literal.241
000000004010a798 d __compound_literal.242
000000004010a7d0 d __compound_literal.243
000000004010a808 d __compound_literal.244
000000004010a840 d __compound_literal.25
000000004010a878 d __compound_literal.26
000000004010a8b0 d __compound_literal.27
000000004010a8b8 d __compound_literal.28
000000004010a8f0 d __compound_literal.29
000000004010a8f8 d __compound_literal.3
000000004010a930 d __compound_literal.3
000000004010a968 d __compound_literal.3
000000004010a9a0 d __compound_literal.30
000000004010a9d8 d __compound_literal.31
000000004010aa10 d __compound_literal.32
000000004010aa48 d __compound_literal.33
000000004010aa80 d __compound_literal.34
000000004010aab8 d __compound_literal.35
000000004010aac0 d __compound_literal.36
000000004010aaf8 d __compound_literal.37
000000004010ab30 d __compound_literal.38
000000004010ab38 d __compound_literal.39
000000004010ab70 d __compound_literal.4
000000004010ab78 d __compound_literal.4
000000004010ab80 d __compound_literal.4
000000004010ab88 d __compound_literal.40
000000004010abc0 d __compound_literal.41
000000004010abc8 d __compound_literal.42
000000004010ac00 d __compound_literal.43
000000004010ac38 d __compound_literal.44
000000004010ac40 d __compound_literal.45
000000004010ac78 d __compound_literal.46
000000004010acb0 d __compound_literal.47
000000004010acb8 d __compound_literal.48
000000004010acf0 d __compound_literal.49
000000004010acf8 d __compound_literal.5
000000004010ad30 d __compound_literal.5
000000004010ad68 d __compound_literal.5
000000004010ada0 d __compound_literal.50
000000004010add8 d __compound_literal.51
000000004010ade0 d __compound_literal.52
000000004010ae18 d __compound_literal.53
000000004010ae20 d __compound_literal.54
000000004010ae58 d __compound_literal.55
000000004010ae60 d __compound_literal.56
000000004010ae98 d __compound_literal.57
000000004010aea0 d __compound_literal.58
000000004010aed8 d __compound_literal.59
000000004010aee0 d __compound_literal.6
000000004010aee8 d __compound_literal.6
000000004010aef0 d __compound_literal.6
000000004010af28 d __compound_literal.60
000000004010af60 d __compound_literal.61
000000004010af68 d __compound_literal.62
000000004010afa0 d __compound_literal.63
000000004010afa8 d __compound_literal.64
000000004010afe0 d __compound_literal.65
000000004010afe8 d __compound_literal.66
000000004010b020 d __compound_literal.67
000000004010b028 d __compound_literal.68
000000004010b060 d __compound_literal.69
000000004010b098 d __compound_literal.7
000000004010b0d0 d __compound_literal.7
000000004010b108 d __compound_literal.7
000000004010b110 d __compound_literal.70
000000004010b118 d __compound_literal.71
000000004010b150 d __compound_literal.72
000000004010b158 d __compound_literal.73
000000004010b190 d __compound_literal.74
000000004010b198 d __compound_literal.75
000000004010b1d0 d __compound_literal.76
000000004010b1d8 d __compound_literal.77
000000004010b210 d __compound_literal.78
000000004010b218 d __compound_literal.79
000000004010b250 d __compound_literal.8
000000004010b258 d __compound_literal.8
000000004010b290 d __compound_literal.8
000000004010b2c8 d __compound_literal.80
000000004010b2d0 d __compound_literal.81
000000004010b308 d __compound_literal.82
000000004010b310 d __compound_literal.83
000000004010b348 d __compound_literal.84
000000004010b380 d __compound_literal.85
000000004010b3b8 d __compound_literal.86
000000004010b3f0 d __compound_literal.87
000000004010b3f8 d __compound_literal.88
000000004010b430 d __compound_literal.89
000000004010b438 d __compound_literal.9
000000004010b470 d __compound_literal.9
000000004010b478 d __compound_literal.9
000000004010b4b0 d __compound_literal.90
000000004010b4e8 d __compound_literal.91
000000004010b4f0 d __compound_literal.92
000000004010b528 d __compound_literal.93
000000004010b530 d __compound_literal.94
000000004010b568 d __compound_literal.95
000000004010b570 d __compound_literal.96
000000004010b5a8 d __compound_literal.97
000000004010b5b0 d __compound_literal.98
000000004010b5e8 d __compound_literal.99
000000004010b5f0 d apb0_clk
000000004010b6a8 d apb1_clk
000000004010b760 d apb_clk
000000004010b800 d audio_codec_adc_clk
000000004010b8b8 d audio_codec_dac_clk
000000004010b970 d avs_clk
000000004010b9c0 d axi_clk
000000004010ba60 d bus_audio_codec_clk
000000004010bab0 d bus_ce_clk
000000004010bb00 d bus_csi_clk
000000004010bb50 d bus_dbg_clk
000000004010bba0 d bus_de0_clk
000000004010bbf0 d bus_di_clk
000000004010bc40 d bus_dma_clk
000000004010bc90 d bus_dmic_clk
000000004010bce0 d bus_dpss_top0_clk
000000004010bd30 d bus_dram_clk
000000004010bd80 d bus_dsp_cfg_clk
000000004010bdd0 d bus_ehci0_clk
000000004010be20 d bus_ehci1_clk
000000004010be70 d bus_emac0_clk
000000004010bec0 d bus_g2d_clk
000000004010bf10 d bus_gpadc_clk
000000004010bf60 d bus_hdmi_clk
000000004010bfb0 d bus_hstimer_clk
000000004010c000 d bus_i2c0_clk
000000004010c050 d bus_i2c1_clk
000000004010c0a0 d bus_i2c2_clk
000000004010c0f0 d bus_i2c3_clk
000000004010c140 d bus_i2s0_clk
000000004010c190 d bus_i2s1_clk
000000004010c1e0 d bus_i2s2_clk
000000004010c230 d bus_iommu_clk
000000004010c280 d bus_ir_tx_clk
000000004010c2d0 d bus_ledc_clk
000000004010c320 d bus_lradc_clk
000000004010c370 d bus_mipi_dsi_clk
000000004010c3c0 d bus_mmc0_clk
000000004010c410 d bus_mmc1_clk
000000004010c460 d bus_mmc2_clk
000000004010c4b0 d bus_msgbox0_clk
000000004010c500 d bus_msgbox1_clk
000000004010c550 d bus_msgbox2_clk
000000004010c5a0 d bus_ohci0_clk
000000004010c5f0 d bus_ohci1_clk
000000004010c640 d bus_otg_clk
000000004010c690 d bus_pwm_clk
000000004010c6e0 d bus_riscv_cfg_clk
000000004010c730 d bus_spdif_clk
000000004010c780 d bus_spi0_clk
000000004010c7d0 d bus_spi1_clk
000000004010c820 d bus_spinlock_clk
000000004010c870 d bus_tcon_lcd0_clk
000000004010c8c0 d bus_tcon_tv_clk
000000004010c910 d bus_ths_clk
000000004010c960 d bus_tpadc_clk
000000004010c9b0 d bus_tvd_clk
000000004010ca00 d bus_tvd_top_clk
000000004010ca50 d bus_tve_clk
000000004010caa0 d bus_tve_top_clk
000000004010caf0 d bus_tzma_clk
000000004010cb40 d bus_uart0_clk
000000004010cb90 d bus_uart1_clk
000000004010cbe0 d bus_uart2_clk
000000004010cc30 d bus_uart3_clk
000000004010cc80 d bus_uart4_clk
000000004010ccd0 d bus_uart5_clk
000000004010cd20 d bus_ve_clk
000000004010cd70 d ce_clk
000000004010ce28 d cpux_clk
000000004010cea8 d csi0_mclk_clk
000000004010cf48 d csi_top_clk
000000004010cfe8 d de0_clk
000000004010d088 d di_clk
000000004010d128 d dmic_clk
000000004010d1e0 d dram_clk
000000004010d298 d dsp_clk
000000004010d338 d emac0_25m_clk
000000004010d388 d fanout0_out_clk
000000004010d408 d fanout1_out_clk
000000004010d488 d fanout2_out_clk
000000004010d508 d fanout_12m_clk
000000004010d558 d fanout_16m_clk
000000004010d5a8 d fanout_24m_clk
000000004010d5f8 d fanout_25m_clk
000000004010d648 d fanout_27m_clk
000000004010d700 d fanout_32k_clk
000000004010d750 d fanout_pclk
000000004010d7f0 d g2d_clk
000000004010d890 d hdmi_24m_clk
000000004010d8e0 d hdmi_cec_32k_clk
000000004010d900 d hdmi_cec_clk
000000004010d980 d i2s0_clk
000000004010da38 d i2s1_clk
000000004010daf0 d i2s2_asrc_clk
000000004010dba8 d i2s2_clk
000000004010dc60 d ir_tx_clk
000000004010dd18 d ledc_clk
000000004010ddd0 d mbus_ce_clk
000000004010de20 d mbus_clk
000000004010de40 d mbus_csi_clk
000000004010de90 d mbus_dma_clk
000000004010dee0 d mbus_g2d_clk
000000004010df30 d mbus_tvin_clk
000000004010df80 d mbus_ve_clk
000000004010dfd0 d mipi_dsi_clk
000000004010e070 d mmc0_clk
000000004010e128 d mmc1_clk
000000004010e1e0 d mmc2_clk
000000004010e298 d osc12M_clk
000000004010e2b8 d pll_audio0_2x_clk
000000004010e2d8 d pll_audio0_4x_clk
000000004010e388 d pll_audio0_clk
000000004010e3a8 d pll_audio0_parents
000000004010e3b0 d pll_audio0_sdm_table
000000004010e410 d pll_audio1_clk
000000004010e4c0 d pll_audio1_div2_clk
000000004010e560 d pll_audio1_div5_clk
000000004010e600 d pll_cpux_clk
000000004010e6a0 d pll_cpux_div
000000004010e740 d pll_cpux_div_table
000000004010e760 d pll_ddr0_clk
000000004010e7f8 d pll_periph0_2x_clk
000000004010e898 d pll_periph0_800m_clk
000000004010e938 d pll_periph0_clk
000000004010e958 d pll_periph0_div3_clk
000000004010e978 d pll_periph0_parent_clk
000000004010ea28 d pll_ve_clk
000000004010eac0 d pll_video0_2x_clk
000000004010eae0 d pll_video0_4x_clk
000000004010eb00 d pll_video0_clk
000000004010ebb0 d pll_video0_parents
000000004010ebb8 d pll_video1_2x_clk
000000004010ebd8 d pll_video1_4x_clk
000000004010ebf8 d pll_video1_clk
000000004010eca8 d pll_video1_parents
000000004010ecb0 d psi_ahb_clk
000000004010ed68 d riscv_axi_clk
000000004010ee08 d riscv_axi_table
000000004010ee28 d riscv_clk
000000004010eea8 d spdif_rx_clk
000000004010ef60 d spdif_tx_clk
000000004010f018 d spi0_clk
000000004010f0d0 d spi1_clk
000000004010f188 d sun8iw20_ccu_clks
000000004010f5c0 d sun8iw20_ccu_resets
000000004010f7e0 d sun8iw20_hw_clks
000000004010fc88 d tcon_lcd0_clk
000000004010fd40 d tcon_tv_clk
000000004010fdf8 d tpadc_clk
000000004010fe78 d tvd_clk
000000004010ff18 d tve_clk
000000004010ffd0 d usb_ohci0_clk
0000000040110020 d usb_ohci1_clk
0000000040110070 d ve_clk
0000000040110110 d r_ahb_bus_rtc_clk
0000000040110160 d r_ahb_clk
0000000040110218 d r_apb0_bus_ir_rx_clk
0000000040110268 d r_apb0_clk
0000000040110320 d r_apb0_cpucfg_clk
0000000040110370 d r_apb0_ir_rx_clk
0000000040110428 d r_apb0_timer_clk
0000000040110478 d r_apb0_twd_clk
00000000401104c8 d r_ppu_clk
0000000040110518 d sun8iw20_r_ccu_clks
0000000040110560 d sun8iw20_r_ccu_resets
0000000040110590 d sun8iw20_r_hw_clks
00000000401105e0 d dcxo24M_div32k_clk
0000000040110630 d ext32k_gate_clk
0000000040110680 d iosc_clk
00000000401106d0 d iosc_div32k_clk
00000000401106f0 d osc32k_clk
0000000040110770 d rtc32k_clk
00000000401107f0 d rtc_1k_clk
0000000040110810 d rtc_32k_fanout_clk
0000000040110890 d rtc_spi_clk
00000000401108e0 d sun8iw20_rtc_ccu_clks
0000000040110918 d sun8iw20_rtc_ccu_hw_clks
0000000040110968 D uart_defconfig
0000000040110980 D uart_msg
0000000040110cb0 d data_year_param
0000000040110cc8 d g_sunxi_spi_params
0000000040110d78 d platform_gpio_desc
0000000040110d88 d sun8iw20p1_gpio_desc
0000000040110dd8 d idt_gd
0000000040110e20 d nor_gd
0000000040110e88 d idt_mxic
0000000040110ed0 d nor_mxic
0000000040110f38 d idt_winbond
0000000040110f68 d nor_winbond
0000000040110fd0 d idt_esmt
0000000040111018 d nor_esmt
0000000040111080 d idt_xtx
00000000401110c8 d nor_xtx
0000000040111130 d idt_fm
0000000040111160 d nor_fm
00000000401111c8 d idt_xmc
00000000401111f8 d nor_xmc
0000000040111260 d idt_puya
00000000401112f0 d nor_puya
0000000040111358 d idt_boya
00000000401113e8 d nor_boya
0000000040111450 d idt_zetta
0000000040111468 d nor_zetta
00000000401114d0 d gCardList
00000000401114e0 d pcm_ops.9322
0000000040111528 d vars.6432
0000000040111548 d pcm_formats
00000000401115f0 d rates
0000000040111628 d sun8iw20_codec_dai
00000000401116b8 d sun8iw20_codec_dai_ops
0000000040111718 D sunxi_audiocodec
0000000040111780 d sunxi_codec_controls
00000000401126f8 d sunxi_switch_text
0000000040112708 d sunxi_switch_text
0000000040112718 D dummy_codec
0000000040112780 d dummy_codec_dai
0000000040112810 d sunxi_cpudai_dai_ops
0000000040112870 d sunxi_pcm_hardware
00000000401128d0 D daudio_pa_cfg
00000000401128d8 D daudio_param
0000000040112930 d sunxi_daudio_dai_ops
0000000040112990 d g_snd_pcm_direct_ipcs
00000000401129a0 d g_shm_sum_ipcs
00000000401129b0 D _snd_pcm_global_configs
00000000401129b8 d g_card_name.5466
00000000401129f8 D dump_reg_table
0000000040112bf0 d hub_id_table
0000000040112c2c d thread_run_flag
0000000040112c30 d thread_stopped_flag
0000000040112c38 d msc_match_table
0000000040112dc8 d usbh_disk_info_op
0000000040112df0 D enable_count
0000000040112df8 d device_list
0000000040112e08 D panel_array
0000000040112e18 D default_panel
0000000040112e78 d lcm_initialization_setting_ge_tai
00000000401135c8 d lcm_initialization_setting_ge_tai_new
0000000040113d18 d g_device_fps
0000000040113d20 D bicubic4coefftab32
0000000040114520 D bicubic8coefftab32_left
0000000040114d20 D bicubic8coefftab32_right
0000000040115520 D lan2coefftab16
0000000040115920 D lan2coefftab32
0000000040116120 D lan3coefftab32_left
0000000040116920 D lan3coefftab32_right
0000000040117120 D bypass_csc
0000000040117150 D ce_bypass_lut
0000000040117250 D ce_constant_lut
0000000040117350 D fcc_range_gain
0000000040117368 D r2r
00000000401173e8 D r2y
00000000401175e8 D sin_cos
00000000401177e8 D y2r
0000000040117ae8 D y2y
0000000040117be8 D g_format
0000000040117bf0 D _csc_enhance_setting
0000000040117c20 D wb_lan2coefftab16
0000000040117c60 D wb_lan2coefftab16_down
0000000040117ca0 D dsi_lane_den
0000000040117cb0 D dsi_pixel_bits
0000000040117cc0 D tcon_div
0000000040117cc8 D g_clk_no
0000000040117fa0 D g_clk_no_len
0000000040117fa8 D g_irq_no
0000000040117fb4 D g_irq_no_len
0000000040117fb8 D g_reg_base
0000000040117fcc D g_reg_base_len
0000000040117fd0 d cali
0000000040117fe0 D reg_base
0000000040117fe8 d video_timing
0000000040118808 d lan2coefftab32_full
0000000040119008 d linearcoefftab32
0000000040119088 D Ycbcr2rgb_601
00000000401190b8 D Ycbcr2rgb_709
00000000401190e8 D rgb2Ycbcr_601
0000000040119118 D rgb2Ycbcr_709
0000000040119148 D debug_mask
0000000040119150 d g_key_info
00000000401192f8 d g_vm_struct_list
0000000040119308 d bits
0000000040119310 d default_tx
0000000040119330 d port
0000000040119334 d speed
0000000040119338 D channel
0000000040119340 d blk_head
0000000040119350 d default_console
0000000040119358 d gCliConsolelist
0000000040119368 D uart_debug_port
0000000040119370 D cli_null_console
00000000401193f0 d null_console
0000000040119418 D cli_uart_console
0000000040119498 d uart_console
00000000401194c0 D working_directory
00000000401195c0 D nodeInfo_sun8i
00000000401196e0 D nodeUnit_sun8i
0000000040119728 d v4l2_ioctls
000000004011a428 d devlist
000000004011a438 d formats
000000004011a550 D dentry_stat
000000004011a568 d dentry_unused
000000004011a578 D inode_in_use
000000004011a588 D inode_unused
000000004011a598 d default_table
000000004011a5d8 d utf8_table
000000004011a6b8 D mem_map_info
000000004011a6f8 d cache_cache
000000004011a798 d cache_sizes
000000004011a838 d clock_searchp
000000004011a840 d slab_break_gfp_order
000000004011a848 D super_blocks
000000004011a858 D devfs_fs_type
000000004011a8a8 D devfs_dentry_ops
000000004011a918 D devfs_dir_inode_operations
000000004011a960 d minfs_fs_type
000000004011a9b0 D minfs_dentry_ops
000000004011a9e8 d g_Alloc
000000004011a9f8 D exfat_dentry_ops
000000004011aa30 D exfat_fs_type
000000004011aa80 D ntfs_fs_type
000000004011aad0 D NTFS_INDEX_I30
000000004011aae0 D ntfs_dentry_ops
000000004011ab18 d fat12_ops
000000004011ab48 d fat16_ops
000000004011ab78 d fat32_ops
000000004011aba8 D fat_fs_type
000000004011abf8 D vfat_dir_inode_operations
000000004011ac40 D pPartUTbl
000000004011ac60 D pPartXTbl
000000004011acf0 D dospart_type
000000004011ad40 D dmspart_type
000000004011ad90 D rawpart_type
000000004011ade0 D devclass_nop
000000004011ae00 d DRVRoot
000000004011ae20 d dmsdev_ops
000000004011ae48 d ramdiskdev_op
000000004011ae70 D __fsym___cmd_drvavs
000000004011ae70 D __fsymtab_start
000000004011ae70 D __tdata_end
000000004011ae70 D __tdata_start
000000004011ae88 D __fsym___cmd_power_info
000000004011aea0 D __fsym_sdmmctest
000000004011aeb8 D __fsym_soundcard
000000004011aed0 D __fsym_aplay
000000004011aee8 D __fsym_arecord
000000004011af00 D __fsym_amixer
000000004011af18 D __fsym_disp
000000004011af30 D __fsym_tv_resync_line_store
000000004011af48 D __fsym_tv_resync_pixel_store
000000004011af60 D __fsym_tv_faketv_store
000000004011af78 D __fsym_tv_faketv_show
000000004011af90 D __fsym_hal_spidev_test
000000004011afa8 D __fsym_hal_spi_slave_test
000000004011afc0 D __fsym_hal_spi_slave_driver
000000004011afd8 D __fsym_hal_gpadc
000000004011aff0 D __fsym_hal_rtc
000000004011b008 D __fsym_usb
000000004011b020 D __fsym_disp_mem
000000004011b038 D __fsym_disp_layer_cfg
000000004011b050 D __fsym___cmd_disp_layer_alpha_test
000000004011b068 D __fsym___cmd_disp_layer_scal_test
000000004011b080 D __fsym___cmd_disp_layer_format_test
000000004011b098 D __fsym___cmd_disp_lbc_test
000000004011b0b0 D __fsym_ths_gt
000000004011b0c8 D __fsym___cmd_setenv
000000004011b0e0 D __fsym___cmd_printenv
000000004011b0f8 D __fsym___cmd_list_fd
000000004011b110 D __fsym___cmd_mount_table
000000004011b128 D __fsym___cmd_list_device
000000004011b140 D __fsym___cmd_list_timer
000000004011b158 D __fsym___cmd_list_mempool
000000004011b170 D __fsym___cmd_list_memheap
000000004011b188 D __fsym___cmd_list_msgqueue
000000004011b1a0 D __fsym___cmd_list_mailbox
000000004011b1b8 D __fsym___cmd_list_mutex
000000004011b1d0 D __fsym___cmd_list_event
000000004011b1e8 D __fsym___cmd_list_sem
000000004011b200 D __fsym___cmd_list_thread
000000004011b218 D __fsym___cmd_free
000000004011b230 D __fsym___cmd_ps
000000004011b248 D __fsym___cmd_help
000000004011b260 D __fsym___cmd_echo
000000004011b270 D __global_pointer$
000000004011b278 D __fsym___cmd_df
000000004011b290 D __fsym___cmd_mkfs
000000004011b2a8 D __fsym___cmd_mkdir
000000004011b2c0 D __fsym___cmd_pwd
000000004011b2d8 D __fsym___cmd_cd
000000004011b2f0 D __fsym___cmd_cat
000000004011b308 D __fsym___cmd_mv
000000004011b320 D __fsym___cmd_cp
000000004011b338 D __fsym___cmd_fork
000000004011b350 D __fsym___cmd_m
000000004011b368 D __fsym___cmd_p
000000004011b380 D __fsym___cmd_panic
000000004011b398 D __fsym___cmd_reboot
000000004011b3b0 D __fsym___cmd_top
000000004011b3c8 D __fsym___cmd_mmlk
000000004011b3e0 D __fsym___cmd_backtrace
000000004011b3f8 D __fsym___cmd_date
000000004011b410 D __fsym___cmd_exit
000000004011b428 D __fsym___cmd_send_key
000000004011b440 D __fsym___cmd_uninsmod
000000004011b458 D __fsym___cmd_insmod
000000004011b470 D __fsym_update
000000004011b488 D __fsym___cmd_mtop
000000004011b4a0 D __fsym_ths_set_s
000000004011b4b8 D __fsym___cmd_tail
000000004011b4d0 D __fsym___cmd_touch
000000004011b4e8 D __fsym___cmd_grep
000000004011b500 D __fsym___cmd_hd
000000004011b518 D __fsym___cmd_hexdump
000000004011b530 D __fsym___cmd_ll
000000004011b548 D __fsym___cmd_ls
000000004011b560 D __fsym___cmd_rm
000000004011b578 D __fsym___cmd_remove_file_test
000000004011b590 D __fsym___cmd_rwcheck
000000004011b5a8 D __fsym___cmd_format
000000004011b5c0 D __fsym___cmd_umount
000000004011b5d8 D __fsym___cmd_mount
000000004011b5f0 D __fsymtab_end
000000004011b5f0 B __init_process_stack_start__
000000004011b5f0 D __vsymtab_end
000000004011b5f0 D __vsymtab_start
000000004011c5f0 B __init_process_stack_end__
000000004011c5f0 B __interrupt_stack_start__
000000004011e5f0 B __interrupt_stack_end__
00000000401215f0 B __bss_start
00000000401215f0 B optarg
00000000401215f8 B optind
00000000401215fc b optwhere
0000000040121600 b prev_tzenv
0000000040121608 b __tzname_std
0000000040121618 b __tzname_dst
0000000040121628 B _timezone
0000000040121630 B _daylight
0000000040121638 b initial_env
0000000040121640 B melis_kernel_running
0000000040121648 B melis_ramdisk_base
0000000040121650 B melis_ramdisk_size
0000000040122000 B Lv2PageTables
0000000040126000 B system_pg_dir
0000000040127000 B c906_plic_handlers
0000000040127018 b c906_plic_regs
0000000040127020 B g_cli_direct_read
0000000040127028 b rt_tick
0000000040127030 b idle
0000000040127288 b idle_hook_list
00000000401272a8 b rt_thread_stack
00000000401292a8 b rt_interrupt_enter_hook
00000000401292b0 b rt_interrupt_leave_hook
00000000401292b8 b __rt_errno
00000000401292c0 b _console_device
00000000401292c8 B rt_assert_hook
00000000401292d0 b rt_log_buf.5742
00000000401296d0 b rt_object_attach_hook
00000000401296d8 b rt_object_detach_hook
00000000401296e0 B rt_object_put_hook
00000000401296e8 B rt_object_take_hook
00000000401296f0 B rt_object_trytake_hook
00000000401296f8 B rt_current_priority
0000000040129700 B rt_current_thread
0000000040129708 b rt_scheduler_hook
0000000040129710 B rt_thread_defunct
0000000040129720 B rt_thread_priority_table
0000000040129920 B rt_thread_ready_priority_group
0000000040129928 b rt_thread_delete_hook
0000000040129930 b rt_thread_inited_hook
0000000040129938 b rt_thread_resume_hook
0000000040129940 b rt_thread_suspend_hook
0000000040129948 b random_nr.2829
0000000040129950 b rt_soft_timer_list
0000000040129960 b rt_timer_enter_hook
0000000040129968 b rt_timer_exit_hook
0000000040129970 b rt_timer_list
0000000040129980 b timer_thread
0000000040129bd8 b timer_thread_stack
000000004012dbd8 b pipeno.5497
000000004012dbe0 b heap_end
000000004012dbe8 b heap_start
000000004012dbf0 b max_mem
000000004012dbf8 b memusage
000000004012dc00 b rt_free_hook
000000004012dc08 b rt_malloc_hook
000000004012dc10 b rt_page_list
000000004012dc18 b used_mem
000000004012dc20 b zone_array
000000004012de60 b zone_free
000000004012de68 b zone_free_cnt
000000004012de6c b zone_limit
000000004012de70 b zone_page_cnt
000000004012de74 b zone_size
000000004012de78 b coherent
000000004012df58 b printk_log_buf.8160
000000004012e358 b rt_perf_task
000000004012e360 b spi0
000000004012e418 b spi1
000000004012e4d0 b spi2
000000004012e588 b spi3
000000004012e640 b acdev
000000004012e648 b id.17504
000000004012e650 b norblk
000000004012e758 b pndev
000000004012ecd8 b avs.8411
000000004012ee68 b uart0
000000004012ef20 b uart1
000000004012efd8 b uart2
000000004012f090 b uart3
000000004012f148 b uart4
000000004012f200 b uart5
000000004012f2b8 B dev_uart
000000004012f3a8 b audio_play
0000000040130b00 b audio_rec
00000000401312c8 b twi0
0000000040131388 b twi1
0000000040131448 b twi2
0000000040131508 b twi3
00000000401315c8 b twi4
0000000040131688 b pwm
0000000040131740 b init_flag
0000000040131744 b init_flag
0000000040131748 B pDev
0000000040131760 B pDevSys
0000000040131820 B sdmmcblk
0000000040131928 b fuelguage
0000000040131930 b power_ctl
0000000040131938 b usb0
00000000401319f0 b temperature_control_strategy
00000000401319f8 b g_uart_priv
0000000040132058 b uart_mailbox
00000000401320e0 b last_mday.5409
00000000401320e4 b last_mon.5408
00000000401320e8 b last_year.5407
00000000401320f0 b sunxi_hal_rtc
0000000040132130 b g_sunxi_spi
0000000040132300 b running
0000000040132308 b running_saved
0000000040132310 b g_gpio_desc
0000000040132318 b g_reg_spinlock
000000004013232c b has_init.8140
0000000040132330 b hal_spinor_info
0000000040132350 b g_fhead
00000000401323b8 b g_nor
0000000040132400 b g_timer
00000000401324f0 b hal_avs
0000000040132530 b card_info
0000000040132548 B sdmmc_lock
0000000040132550 b sunxi_sdmmc_pin
0000000040132590 b _mci_host
00000000401325a8 b sdmmc_test_info
00000000401325c0 b dma_chan_source
0000000040132940 b dma_lock
0000000040132944 b irq_inited
0000000040132948 b dummy_codec_dai_ops
00000000401329a8 B g_snd_pcm_direct_ipcs_mutex
0000000040132a08 b g_shm_sum_ipcs_lock
0000000040132a68 B g_verbose
0000000040132a70 b g_hpcm_name
0000000040132a78 b g_pcm_name
0000000040132a80 b g_pcm_name
0000000040132aa0 b g_playback_time
0000000040132aa4 B g_capture_loop_enable
0000000040132aa8 b g_capture_then_play
0000000040132ab0 b g_last_audio_mgr
0000000040132ab8 b hal_twi
00000000401332d8 b twi_mode_table
00000000401332e4 b pwm_init
00000000401332e8 B sunxi_pwm
0000000040133378 B hal_gpadc
0000000040133460 B list_lock
0000000040133464 b device_lock
0000000040133468 b device_lock
0000000040133470 b my_usb_virt_bus
0000000040133520 b highspeed_hubs
0000000040133528 b hub_driver
0000000040133590 b hub_event_list
00000000401335a8 b hub_event_lock
00000000401335b0 B hub_thread
00000000401335b8 b hub_thread_cont
00000000401335d0 B p_app_para
00000000401335d8 B port_connect_status
00000000401335da B port_connect_status_flags
00000000401335e0 B usb_address0_sem
00000000401335e8 b lock
00000000401335ec B gen_hcd_lock
00000000401335f0 B usb_bus_list_lock
00000000401335f8 b usb_host_is_run_flag
00000000401335fc b urb_sn_now
0000000040133600 b log2_irq_thresh
0000000040133604 b park
0000000040133608 b g_sunxi_ehci
0000000040133a48 b g_sunxi_insmod_status
0000000040133a50 b g_sunxi_ohci
0000000040133e90 b g_sunxi_usb_debug
0000000040133e94 B UsbHostAoaConnectStatus
0000000040133e98 B UsbHostIsConnected
0000000040133e9c B UsbIapIsConnected
0000000040133ea0 b g_usb_cfg
0000000040133f38 B thread_suspend_flag
0000000040133f40 b g_center_info
0000000040133f58 b __usb_hw_scan
0000000040133f60 b device_insmod_delay
0000000040133f68 b g_usb_scan_info
0000000040133f80 b mscDev_id_array
0000000040133f88 b mscDrv
0000000040134000 b buffer.10925
00000000401340c0 b buffer.10932
0000000040134180 b SenseData.10665
00000000401341c0 b buffer.10618
00000000401341c8 b usbh_buff_manager
0000000040134350 b usb_disk_status
0000000040134358 b usbh_disk_info
0000000040134380 b usbh_disk_time
0000000040134510 b count
0000000040134528 B g_disp_drv
00000000401348c8 B g_disp_work_queue
00000000401348d0 b g_enhance_mode
00000000401348d8 B lyr_cfg
0000000040135458 B lyr_cfg2
00000000401360d8 B lyr_cfg2_1
0000000040136d58 B pm
0000000040136d60 b suspend_output_type
0000000040136d70 b suspend_status
0000000040136d74 b sync_finish_lock
0000000040136d78 b sync_wait_lock
0000000040136d80 b waiting
0000000040136d84 B disp_lock
0000000040136d88 B gdisp
0000000040137ab8 b lcd_data_lock
0000000040137ac0 b lcd_private
0000000040137ac8 b lcds
0000000040137ad0 b lyr_cfgs
0000000040137ad8 b lyr_private
0000000040137ae0 b lyrs
0000000040137ae8 b mgr_cfgs
0000000040137af0 b mgr_data_lock
0000000040137af8 b mgr_mlock
0000000040137b00 b mgr_private
0000000040137b08 b mgrs
0000000040137b10 b capture_private
0000000040137b18 b captures
0000000040137b20 B g_init_para
0000000040137c60 b g_ptv_devices
0000000040137c68 b g_ptv_private
0000000040137c70 b g_tv_data_lock
0000000040137c78 b enhance_private
0000000040137c80 b enhances
0000000040137c88 B g_lcd_drv
0000000040137d50 B lcd_brightness
0000000040137d54 B lcd_pwm_hdl
0000000040137d58 b fcc_dev
0000000040137d78 b fcc_para_block
0000000040137dd8 b gsu_coeff_block
0000000040137e08 b gsu_dev
0000000040137e18 b gsu_glb_block
0000000040137e48 b gsu_out_block
0000000040137e78 b gsu_scale_block
0000000040137ea8 b cpara.13726
0000000040137ee8 b g_de_blank
0000000040137eec b g_de_freq
0000000040137ef0 b lay_cfg.13810
0000000040138270 b ovl_cpara.13815
0000000040138280 b ovl_para.13814
00000000401382a0 b para.13725
0000000040138320 b al_priv
0000000040138390 b peak_block
00000000401383f0 b peak_dev
0000000040138410 b peak_gain_block
0000000040138470 b pq_dirty
0000000040138478 b bld_attr_block
00000000401384a8 b bld_ck_block
00000000401384d8 b bld_ctl_block
0000000040138508 b bld_out_block
0000000040138538 b de200_rtmx
00000000401385b8 b de_base
00000000401385c0 b de_base
00000000401385c8 b dither_ctrl_block
00000000401385f8 b glb_ctl_block
0000000040138628 b lbc_block
0000000040138658 b ui_attr_block
0000000040138718 b ui_haddr_block
0000000040138748 b ui_palette_block
0000000040138778 b ui_size_block
00000000401387a8 b vi_attr_block
0000000040138868 b vi_fc_block
0000000040138898 b vi_haddr_block
00000000401388c8 b vi_palette_block
00000000401388f8 b vi_size_block
0000000040138928 b vsu_chcoeff0_block
0000000040138958 b vsu_chcoeff1_block
0000000040138988 b vsu_cscale_block
00000000401389b8 b vsu_cvcoeff_block
00000000401389e8 b vsu_dev
00000000401389f8 b vsu_glb_block
0000000040138a28 b vsu_out_block
0000000040138a58 b vsu_yhcoeff0_block
0000000040138a88 b vsu_yhcoeff1_block
0000000040138ab8 b vsu_yscale_block
0000000040138ae8 b vsu_yvcoeff_block
0000000040138b18 b de_cur_features
0000000040138b20 b lti_block
0000000040138b80 b lti_dev
0000000040138ba0 b fce_celut_block
0000000040138c00 b fce_dev
0000000040138c20 b fce_hist_block
0000000040138c80 b fce_hw_base
0000000040138ca0 b fce_para_block
0000000040138d00 B g_ce_status
0000000040138d20 b g_celut
0000000040138d40 B g_hist_status
0000000040138d60 b hist_r.12235
0000000040139160 b hist_res
0000000040139180 b p.12236
0000000040139580 b ase_block
00000000401395e0 b ase_dev
0000000040139600 b device_num
0000000040139608 b frame_cnt
0000000040139610 b g_config
00000000401396d8 b g_demo_enable
00000000401396e0 B g_hist
0000000040139700 B g_hist_p
0000000040139720 b g_size
0000000040139740 B g_size_bypass
0000000040139748 b g_size_change
0000000040139750 B g_sum
0000000040139760 b vep_num
0000000040139768 b ccsc_dev
0000000040139788 b csc_block
00000000401397e8 b icsc_block
0000000040139848 b icsc_dev
0000000040139868 b vep_support
0000000040139878 b vi_num
0000000040139880 b dcsc2_dev
0000000040139890 b dcsc_coeff_block
00000000401398c0 b dcsc_ctl_block
00000000401398f0 b dcsc_dev
0000000040139900 b dcsc_enable_block
0000000040139930 b dcsc_gamma_block
0000000040139960 b g_dcsc_config
00000000401399d0 b gamma_dev
00000000401399e0 b is_in_smbl
00000000401399e8 b wb_config
0000000040139a90 b wb_dev
0000000040139aa0 b dphy_dev
0000000040139aa8 b dsi_dev
0000000040139ab0 b lcd_dev
0000000040139ac0 b lcd_top
0000000040139ac8 b init_get_clk.4076
0000000040139ad0 B g_tv_info
0000000040139b58 b hpd_status
0000000040139b60 b offset
0000000040139b70 b suspend
0000000040139b74 b tv_fake_detect
0000000040139b78 b tv_hpd
0000000040139b7c B tv_lock
0000000040139b80 B tv_lock_data
0000000040139b88 b tv_power
0000000040139ba1 b tv_power_enable_mask
0000000040139ba8 b tve_task
0000000040139bb0 b dac_info
0000000040139bb8 b dac_type_info
0000000040139bc0 b tve_reg_base
0000000040139bc8 b tve_top_reg_base
0000000040139bd0 b g2d_task_list
0000000040139bd8 b idr
0000000040139be0 B g2d_ext_hd
0000000040139c10 B global_lock
0000000040139c18 B para
0000000040139c60 b g2d_top
0000000040139c68 b mixer_glb
0000000040139c70 b base_addr
0000000040139c78 b current_fb_arg
0000000040139d88 b di_reg_base
0000000040139d90 b di_mem
000000004013a6f0 b client_num
000000004013a6f4 b g_c_num
000000004013a6f8 B num_lock
000000004013a6fc B Irq_count
000000004013a700 B atomic_lock
000000004013a708 b di_drvdata
000000004013a710 b efuse_lock
000000004013a714 b hal_malloc_coherent_lock
000000004013a718 b default_rx
000000004013a738 b input_tx
000000004013a740 b iterations
000000004013a744 b mode
000000004013a748 b transfer_size
000000004013a74c b verbose
000000004013a750 b verbose
000000004013a758 b spi_slave_test
000000004013aa78 b g_disp_mm
000000004013ab68 b g_screen_index
000000004013ab6c b g_screen_index
000000004013ab70 b g_screen_index
000000004013ab74 b init.4517
000000004013ab78 b console_lock
000000004013ab80 b dev.8928
000000004013ac28 b global_console
000000004013ac30 b fd
000000004013ac38 b fd
000000004013ac80 b g_parser
000000004013ac88 b arch_timer_count
000000004013ac90 b inited.10130
000000004013ac98 b sys_time
000000004013aca8 b sys_time_sem
000000004013acb0 b std_console
000000004013acb8 b dev_current
000000004013acc0 b environment
000000004013ace8 b lock_env
000000004013acf0 b obsolete_flag
000000004013acf8 b usable_envsize
000000004013ad00 b _fdtab
000000004013ad10 B filesystem_operation_table
000000004013ad90 B filesystem_table
000000004013b010 b fslock
000000004013b068 b init_ok.8639
000000004013b070 B _syscall_table_begin
000000004013b078 B _syscall_table_end
000000004013b080 B _sysvar_table_begin
000000004013b088 B _sysvar_table_end
000000004013b090 b finsh_prompt.6215
000000004013b498 b finsh_prompt_custom
000000004013b4a0 b run_script.6253
000000004013b4a8 B shell
000000004013b4b0 b i.6672
000000004013b4b8 b list_mod_head
000000004013b4c8 b delay
000000004013b4cc b exit_flag
000000004013b4d0 b idx
000000004013b4d8 b iter
000000004013b4dc b max
000000004013b4e0 b nodeCnt
000000004013b4e8 b nodeInfo
000000004013b4f0 b nodeUnit
000000004013b4f8 b total
000000004013b500 b unit
000000004013b508 b g_task
000000004013e308 b multi_thread
000000004013e310 b rwcheck_test_pthread
000000004013e318 b posix_mq_lock
000000004013e368 B pth_table
000000004013e468 b cond_num.3801
000000004013e46a b pthread_mutex_number.3769
000000004013e470 B _thread_keys
000000004013e4f0 b posix_sem_list
000000004013e4f8 b posix_sem_lock
000000004013e548 b psem_number.3853
000000004013e550 b entity_nums_bmp
000000004013e570 b devnode_nums
000000004013e5a0 b used.12061
000000004013e5a8 B vdev_files
000000004013e6a8 b video_device
000000004013e7a8 b videodev_lock
000000004013e800 b capture_first_flag
000000004013e801 b check_once.12030
000000004013e804 b fliter_count
000000004013e808 b fliter_lock
000000004013e860 b frame_cntr.15207
000000004013e864 b lock_any_latest.15209
000000004013e868 b rst_tvd_top
000000004013e870 b tvd_clk_top
000000004013e878 b tvd_count
000000004013e880 b tvd_mbus
000000004013e888 b tvd_status
000000004013e8f8 b tvd_top
000000004013e8fc b tvd_total_num
000000004013e900 b tvd_device
000000004013e920 b tvd_top_dev
000000004013e928 b CharsetMid
000000004013e92c B clk_r_type
000000004013e930 b PINManger
000000004013e940 B input_sys
000000004013e9c8 b LKeyDevMan
000000004013ecf0 b LTSDevMan
000000004013f018 b SramBlkManager
000000004013f028 B g_sram_virt_baseaddr
000000004013f030 B esXCBTbl
000000004013f828 B melis_thread
0000000040140818 b task_id.8419
0000000040140820 b app_msgq_h
0000000040140828 b app_msgq_l
0000000040140830 b display_hld
0000000040140838 b index.15639
0000000040140840 B ksrv_app_para
0000000040140848 b ksrv_msgq
0000000040140850 b mixture_mid
0000000040140858 b mixture_mp
0000000040140860 b ramdomcnt.15640
0000000040140864 b ramdomseed.15641
0000000040140868 b raw_decoder_mid
0000000040140870 b raw_decoder_mp
0000000040140878 B PwrmanStandbyPar
0000000040140928 B sys_pwrman
0000000040140a20 b ResMan
0000000040140a28 B SiosBuffer
0000000040140a38 b hOutFile
0000000040140a40 b TimeCntr
0000000040140a50 b bh_hash_mask
0000000040140a54 b bh_hash_shift
0000000040140a58 B bh_map
0000000040151a58 B buffermem_pages
0000000040151a60 b free_list
0000000040151a98 b hash_table
0000000040151aa0 b lru_list
0000000040151ab8 b nr_buffers_type
0000000040151ac8 b size_buffers_type
0000000040151ae0 b d_hash_mask
0000000040151ae4 b d_hash_shift
0000000040151ae8 b dentry_cache
0000000040151af0 b dentry_hashtable
0000000040151af8 B filp_cachep
0000000040151b00 b m_randseed
0000000040151b04 B fs_err
0000000040151b08 B nls
0000000040151b10 B pFSRoot
0000000040151b18 b vfs_mutex
0000000040151b30 b xtime
0000000040151b40 B bad_inode_ops
0000000040151b88 b counter.8055
0000000040151b90 b empty_iops.7903
0000000040151bd8 b i_hash_mask
0000000040151bdc b i_hash_shift
0000000040151be0 b inode_cachep
0000000040151be8 b inode_hashtable
0000000040151bf0 B inodes_stat
0000000040151c0c b last_ino
0000000040151c10 B files
0000000040151e10 B nr_files
0000000040151e18 b charset2lower_tbl
0000000040151f18 b charset2upper_tbl
0000000040152018 b charset_type
0000000040152020 B active_list
0000000040152030 B inactive_list
0000000040152040 B mem_map
0000000040153840 B nr_active_pages
0000000040153844 B nr_inactive_pages
0000000040153848 B num_physpages
0000000040153850 B pagepool
0000000040153858 B nr_debug_slab_pages
0000000040153860 b offslab_limit
0000000040153868 b default_op.7896
00000000401538e0 b devfs_inode_cachep
00000000401538e8 b minfs_inode_cachep
00000000401538f0 b exfat_cache_cachep
00000000401538f8 b empty_iops.8313
0000000040153940 b exfat_inode_cachep
0000000040153948 B finfo
0000000040153988 B AT_UNNAMED
0000000040153990 b fat_cache_cachep
0000000040153998 b nohit.8181
00000000401539b8 b bufname.8305
0000000040153ac0 b bufname.8357
0000000040153af8 b bufuname.8300
0000000040153b18 b bufuname.8354
0000000040153b38 b work.8304
0000000040153b40 b work.8356
0000000040153b48 b progress.8152
0000000040153b50 b fat_inode_cachep
0000000040153b58 B CurhNode
0000000040153b60 b mnt_parts_tid
0000000040153b68 b pContolSem
0000000040153b70 B pPDRoot
0000000040153b78 B pPartFTbl
0000000040153bf8 B pPartSem
0000000040153c00 B pDevSem
0000000040153c08 b dms
0000000040153c10 b dmshandle
0000000040153c18 b ramdiskdev
0000000040153c20 b ramdiskhandle
0000000040153c28 b app_handler
0000000040153c30 b kernel_code
0000000040153c50 b library_handler
0000000040153c90 A __bss_end
0000000040153c90 B __tbss_end
0000000040153c90 B __tbss_start
0000000040153c90 B _firmware_end
