/*
*********************************************************************************************************
*                                              MELIS RTOS
*                    Driver Side USB Disk Sync Demo - Dual-Buffer Optimization
*
*                                    (c) Copyright 2024, Sunny China
*                                             All Rights Reserved
*
* File    : driver_usb_sync_demo.c
* By      : Claude Code
* Version : v1.0
* Date    : 2024-12-XX
* Descript: Demo implementation of driver side USB disk synchronization
* Update  : date                auther      ver     notes
*********************************************************************************************************
*/

#include "sync_core.h"
#include "../mcu_new_protocol_handler.h"

/*
*********************************************************************************************************
*                                           LOCAL DEFINITIONS
*********************************************************************************************************
*/

// USB Disk Status with sync support
typedef struct {
    // Current track information
    struct {
        char title[64];                     // Track title
        char artist[64];                    // Artist name
        char album[64];                     // Album name
        __u32 duration;                     // Total duration in seconds
        __u32 current_time;                 // Current position in seconds
        __u16 track_number;                 // Current track number
        __u16 total_tracks;                 // Total tracks in current folder
    } track_info;
    
    // Playback state
    __u8  play_state;                       // 0=stop, 1=play, 2=pause
    __u8  repeat_mode;                      // 0=off, 1=single, 2=all
    __u8  shuffle_mode;                     // 0=off, 1=on
    __u8  volume_level;                     // 0-100
    
    // Folder/Playlist information
    struct {
        char current_folder[128];           // Current folder path
        __u16 folder_count;                 // Total folders
        __u16 current_folder_idx;           // Current folder index
        __u32 total_files;                  // Total audio files
    } folder_info;
    
    // Device information
    struct {
        char device_name[32];               // USB device name
        __u32 total_capacity;               // Total capacity in MB
        __u32 free_space;                   // Free space in MB
        __u8  device_type;                  // 0=mass storage, 1=iPod
    } device_info;
    
    // Sync fields (only 12 bytes added)
    sync_base_t sync;
} usb_disk_status_t;

/*
*********************************************************************************************************
*                                           LOCAL VARIABLES
*********************************************************************************************************
*/

// Global USB status
static usb_disk_status_t g_usb_status;
static __bool g_usb_initialized = EPDK_FALSE;

/*
*********************************************************************************************************
*                                           HELPER FUNCTIONS
*********************************************************************************************************
*/

/**
 * @brief Initialize USB disk status
 */
static void usb_status_init(void) {
    if (g_usb_initialized) return;
    
    // Initialize USB data to defaults
    eLIBs_memset(&g_usb_status.track_info, 0, sizeof(g_usb_status.track_info));
    g_usb_status.play_state = 0;        // stopped
    g_usb_status.repeat_mode = 0;       // off
    g_usb_status.shuffle_mode = 0;      // off
    g_usb_status.volume_level = 50;     // 50%
    
    eLIBs_memset(&g_usb_status.folder_info, 0, sizeof(g_usb_status.folder_info));
    eLIBs_memset(&g_usb_status.device_info, 0, sizeof(g_usb_status.device_info));
    
    // Initialize sync fields
    SYNC_INIT_BASE(&g_usb_status.sync);
    
    g_usb_initialized = EPDK_TRUE;
}

/**
 * @brief Extract 32-bit value from data buffer
 */
static __u32 extract_u32(__u8 *data) {
    return ((__u32)data[0] << 24) | ((__u32)data[1] << 16) | 
           ((__u32)data[2] << 8) | data[3];
}

/**
 * @brief Extract 16-bit value from data buffer
 */
static __u16 extract_u16(__u8 *data) {
    return ((__u16)data[0] << 8) | data[1];
}

/**
 * @brief Update string field and check if changed
 */
static __bool update_string_field(char *dest, const char *src, __u16 max_len) {
    if (eLIBs_strncmp(dest, src, max_len) != 0) {
        eLIBs_strncpy(dest, src, max_len);
        dest[max_len - 1] = '\0';
        return EPDK_TRUE;
    }
    return EPDK_FALSE;
}

/**
 * @brief Update track information
 */
static __bool update_track_info(__u8 *data, __u16 data_len) {
    __bool changed = EPDK_FALSE;
    __u16 offset = 0;
    
    // Duration (4 bytes)
    __u32 duration = extract_u32(data + offset);
    if (g_usb_status.track_info.duration != duration) {
        g_usb_status.track_info.duration = duration;
        changed = EPDK_TRUE;
    }
    offset += 4;
    
    // Track number (2 bytes)
    __u16 track_num = extract_u16(data + offset);
    if (g_usb_status.track_info.track_number != track_num) {
        g_usb_status.track_info.track_number = track_num;
        changed = EPDK_TRUE;
    }
    offset += 2;
    
    // Total tracks (2 bytes)
    __u16 total_tracks = extract_u16(data + offset);
    if (g_usb_status.track_info.total_tracks != total_tracks) {
        g_usb_status.track_info.total_tracks = total_tracks;
        changed = EPDK_TRUE;
    }
    offset += 2;
    
    // Title (variable length, null-terminated)
    if (offset < data_len) {
        char *title = (char *)(data + offset);
        if (update_string_field(g_usb_status.track_info.title, title, 64)) {
            changed = EPDK_TRUE;
        }
        offset += eLIBs_strlen(title) + 1;
    }
    
    // Artist (variable length, null-terminated)
    if (offset < data_len) {
        char *artist = (char *)(data + offset);
        if (update_string_field(g_usb_status.track_info.artist, artist, 64)) {
            changed = EPDK_TRUE;
        }
        offset += eLIBs_strlen(artist) + 1;
    }
    
    // Album (variable length, null-terminated)
    if (offset < data_len) {
        char *album = (char *)(data + offset);
        if (update_string_field(g_usb_status.track_info.album, album, 64)) {
            changed = EPDK_TRUE;
        }
    }
    
    return changed;
}

/*
*********************************************************************************************************
*                                           MAIN HANDLER
*********************************************************************************************************
*/

/**
 * @brief USB Disk Category Handler with optimized sync
 * @param operation Operation type (GET/SET/CONTROL/EVENT)
 * @param data_type Data type bitmap (reused as change mask)
 * @param data Data buffer
 * @param data_len Data length
 * @return EPDK_OK: success, others: error
 */
__s32 usb_disk_handler_optimized(__u8 operation, __u32 data_type, __u8 *data, __u16 data_len) {
    __u32 local_changes = 0;  // Accumulate changes in this handler call
    __u16 data_offset = 0;
    
    // Initialize if needed
    usb_status_init();
    
    // Process different operations
    switch (operation) {
        case OPERATION_SET: {
            // Process each data type bit
            if (data_type & USB_DATA_TRACK_INFO) {
                if (update_track_info(data + data_offset, data_len - data_offset)) {
                    local_changes |= USB_DATA_TRACK_INFO;
                }
                // Skip to next data segment (implementation dependent)
                data_offset += data_len; // For demo, assume single data type per call
            }
            
            if (data_type & USB_DATA_PLAY_STATE) {
                __u8 new_play_state = data[data_offset];
                if (g_usb_status.play_state != new_play_state) {
                    g_usb_status.play_state = new_play_state;
                    local_changes |= USB_DATA_PLAY_STATE;
                }
                data_offset += 1;
            }
            
            if (data_type & USB_DATA_PROGRESS) {
                __u32 new_time = extract_u32(data + data_offset);
                if (g_usb_status.track_info.current_time != new_time) {
                    g_usb_status.track_info.current_time = new_time;
                    local_changes |= USB_DATA_PROGRESS;
                }
                data_offset += 4;
            }
            
            if (data_type & USB_DATA_VOLUME) {
                __u8 new_volume = data[data_offset];
                if (g_usb_status.volume_level != new_volume) {
                    g_usb_status.volume_level = new_volume;
                    local_changes |= USB_DATA_VOLUME;
                }
                data_offset += 1;
            }
            
            if (data_type & USB_DATA_REPEAT_MODE) {
                __u8 new_repeat = data[data_offset];
                if (g_usb_status.repeat_mode != new_repeat) {
                    g_usb_status.repeat_mode = new_repeat;
                    local_changes |= USB_DATA_REPEAT_MODE;
                }
                data_offset += 1;
            }
            
            if (data_type & USB_DATA_SHUFFLE) {
                __u8 new_shuffle = data[data_offset];
                if (g_usb_status.shuffle_mode != new_shuffle) {
                    g_usb_status.shuffle_mode = new_shuffle;
                    local_changes |= USB_DATA_SHUFFLE;
                }
                data_offset += 1;
            }
            
            if (data_type & USB_DATA_FOLDER_INFO) {
                char *folder_path = (char *)(data + data_offset);
                if (update_string_field(g_usb_status.folder_info.current_folder, 
                                       folder_path, 128)) {
                    local_changes |= USB_DATA_FOLDER_INFO;
                }
                data_offset += eLIBs_strlen(folder_path) + 1;
                
                // Additional folder info (counts, etc.)
                if (data_offset + 8 <= data_len) {
                    __u16 folder_count = extract_u16(data + data_offset);
                    __u16 folder_idx = extract_u16(data + data_offset + 2);
                    __u32 total_files = extract_u32(data + data_offset + 4);
                    
                    if (g_usb_status.folder_info.folder_count != folder_count ||
                        g_usb_status.folder_info.current_folder_idx != folder_idx ||
                        g_usb_status.folder_info.total_files != total_files) {
                        g_usb_status.folder_info.folder_count = folder_count;
                        g_usb_status.folder_info.current_folder_idx = folder_idx;
                        g_usb_status.folder_info.total_files = total_files;
                        local_changes |= USB_DATA_FOLDER_INFO;
                    }
                    data_offset += 8;
                }
            }
            
            break;
        }
        
        case OPERATION_EVENT: {
            // Handle events (e.g., device insertion/removal)
            if (data_type & USB_DATA_DEVICE_INFO) {
                char *device_name = (char *)(data + data_offset);
                if (update_string_field(g_usb_status.device_info.device_name, 
                                       device_name, 32)) {
                    local_changes |= USB_DATA_DEVICE_INFO;
                }
                
                // Additional device info processing...
            }
            break;
        }
        
        default:
            return EPDK_FAIL;
    }
    
    // Batch commit changes at the end of handler
    if (local_changes != 0) {
        // Mark all changes in current buffer
        SYNC_MARK_CHANGED(&g_usb_status.sync, local_changes);
        
        // Commit changes and push message if needed
        SYNC_COMMIT_AND_PUSH(&g_usb_status.sync, CMD_USB_STATUS_SYNC);
    }
    
    return EPDK_OK;
}

/*
*********************************************************************************************************
*                                           PUBLIC INTERFACE
*********************************************************************************************************
*/

/**
 * @brief Get USB status (for application use)
 */
__s32 usb_get_status_optimized(usb_disk_status_t **status) {
    if (!g_usb_initialized) {
        usb_status_init();
    }
    
    *status = &g_usb_status;
    return EPDK_OK;
}

/**
 * @brief Register USB disk handler
 */
__s32 usb_disk_sync_demo_register(void) {
    // Register with protocol handler
    unified_cmd_handler_t usb_handler = {
        .category = CATEGORY_USB_DISK,
        .name = "USB_DISK_SYNC_DEMO",
        .main_handler = usb_disk_handler_optimized
    };
    
    return mcu_new_protocol_register_category_handler(&usb_handler);
}

/*
*********************************************************************************************************
*                                           DEMO USAGE EXAMPLE
*********************************************************************************************************
*/

/**
 * @brief Demo: Simulate USB track change
 */
void demo_usb_track_change(const char *title, const char *artist, __u32 duration) {
    // Pack track info data
    __u8 data[256];
    __u16 offset = 0;
    
    // Duration (4 bytes)
    data[offset++] = (duration >> 24) & 0xFF;
    data[offset++] = (duration >> 16) & 0xFF;
    data[offset++] = (duration >> 8) & 0xFF;
    data[offset++] = duration & 0xFF;
    
    // Track number (2 bytes) - dummy values
    data[offset++] = 0x00;
    data[offset++] = 0x01;
    
    // Total tracks (2 bytes) - dummy values
    data[offset++] = 0x00;
    data[offset++] = 0x64; // 100 tracks
    
    // Title (null-terminated string)
    eLIBs_strcpy((char *)(data + offset), title);
    offset += eLIBs_strlen(title) + 1;
    
    // Artist (null-terminated string)
    eLIBs_strcpy((char *)(data + offset), artist);
    offset += eLIBs_strlen(artist) + 1;
    
    // Album (null-terminated string)
    eLIBs_strcpy((char *)(data + offset), "Demo Album");
    offset += eLIBs_strlen("Demo Album") + 1;
    
    // Call handler
    usb_disk_handler_optimized(OPERATION_SET, USB_DATA_TRACK_INFO, data, offset);
}

/**
 * @brief Demo: Simulate USB play state change
 */
void demo_usb_play_state_change(__u8 new_state) {
    __u8 data[1] = { new_state };
    
    // Call handler
    usb_disk_handler_optimized(OPERATION_SET, USB_DATA_PLAY_STATE, data, 1);
}