/*
*********************************************************************************************************
*                                              MELIS RTOS
*                       Application FM Sync Demo - Optimized UI Data Synchronization
*
*                                    (c) Copyright 2024, Sunny China
*                                             All Rights Reserved
*
* File    : app_fm_sync_demo.c
* By      : Claude Code
* Version : v1.0
* Date    : 2024-12-XX
* Descript: Demo implementation of application side FM data synchronization
* Update  : date                auther      ver     notes
*********************************************************************************************************
*/

#include "sync_core.h"
#include "driver_fm_sync_demo.c"  // Include for accessing fm_tuner_status_t
#include <lvgl.h>

/*
*********************************************************************************************************
*                                           LOCAL DEFINITIONS
*********************************************************************************************************
*/

// FM UI objects
typedef struct {
    lv_obj_t *freq_label;                   // Frequency display label
    lv_obj_t *ps_name_label;                // RDS PS name label
    lv_obj_t *radio_text_label;             // RDS radio text label
    lv_obj_t *stereo_icon;                  // Stereo indicator icon
    lv_obj_t *signal_bar;                   // Signal strength bar
    lv_obj_t *preset_buttons[18];           // Preset buttons
    lv_obj_t *scan_mode_label;              // Scan mode indicator
} fm_ui_objects_t;

// FM application context
typedef struct {
    fm_ui_objects_t ui;                     // UI objects
    fm_tuner_status_t *fm_status;           // Pointer to FM status
    __u32 last_sequence;                    // Last processed sequence number
    __bool app_active;                      // Application active status
    __u32 sync_count;                       // Number of syncs processed
    __u32 full_sync_count;                  // Number of full syncs
} fm_app_context_t;

/*
*********************************************************************************************************
*                                           LOCAL VARIABLES
*********************************************************************************************************
*/

static fm_app_context_t g_fm_app = {0};
static __bool g_fm_app_initialized = EPDK_FALSE;

/*
*********************************************************************************************************
*                                           UI HELPER FUNCTIONS
*********************************************************************************************************
*/

/**
 * @brief Update frequency display
 */
static void update_frequency_display(__u32 frequency) {
    char freq_str[16];
    
    if (!g_fm_app.ui.freq_label) return;
    
    // Convert KHz to MHz with 1 decimal place
    __u32 mhz = frequency / 1000;
    __u32 khz_remainder = (frequency % 1000) / 100;
    
    eLIBs_sprintf(freq_str, "%d.%d MHz", mhz, khz_remainder);
    lv_label_set_text(g_fm_app.ui.freq_label, freq_str);
    
    eLIBs_printf("[FM_APP] Frequency updated: %s\n", freq_str);
}

/**
 * @brief Update RDS PS name display
 */
static void update_rds_ps_display(const char *ps_name) {
    if (!g_fm_app.ui.ps_name_label) return;
    
    lv_label_set_text(g_fm_app.ui.ps_name_label, ps_name);
    
    eLIBs_printf("[FM_APP] PS Name updated: %s\n", ps_name);
}

/**
 * @brief Update RDS radio text display
 */
static void update_rds_radio_text(const char *radio_text) {
    if (!g_fm_app.ui.radio_text_label) return;
    
    lv_label_set_text(g_fm_app.ui.radio_text_label, radio_text);
    
    eLIBs_printf("[FM_APP] Radio Text updated: %s\n", radio_text);
}

/**
 * @brief Update stereo indicator
 */
static void update_stereo_indicator(__u8 stereo_status) {
    if (!g_fm_app.ui.stereo_icon) return;
    
    if (stereo_status) {
        lv_obj_add_style(g_fm_app.ui.stereo_icon, &style_stereo_active, 0);
        lv_obj_clear_flag(g_fm_app.ui.stereo_icon, LV_OBJ_FLAG_HIDDEN);
    } else {
        lv_obj_remove_style(g_fm_app.ui.stereo_icon, &style_stereo_active, 0);
        lv_obj_add_flag(g_fm_app.ui.stereo_icon, LV_OBJ_FLAG_HIDDEN);
    }
    
    eLIBs_printf("[FM_APP] Stereo indicator: %s\n", stereo_status ? "ON" : "OFF");
}

/**
 * @brief Update signal strength bar
 */
static void update_signal_strength(__u16 signal_strength) {
    if (!g_fm_app.ui.signal_bar) return;
    
    // Convert signal strength to percentage (assume max is 1000)
    __u32 percentage = (signal_strength * 100) / 1000;
    if (percentage > 100) percentage = 100;
    
    lv_bar_set_value(g_fm_app.ui.signal_bar, percentage, LV_ANIM_ON);
    
    eLIBs_printf("[FM_APP] Signal strength: %d%% (%d)\n", percentage, signal_strength);
}

/**
 * @brief Update preset buttons
 */
static void update_preset_buttons(void) {
    __u32 i;
    
    for (i = 0; i < 18 && i < sizeof(g_fm_app.ui.preset_buttons)/sizeof(g_fm_app.ui.preset_buttons[0]); i++) {
        if (!g_fm_app.ui.preset_buttons[i]) continue;
        
        if (g_fm_app.fm_status->presets[i].frequency > 0) {
            char preset_text[16];
            
            // Show frequency in MHz
            __u32 mhz = g_fm_app.fm_status->presets[i].frequency / 1000;
            __u32 khz = (g_fm_app.fm_status->presets[i].frequency % 1000) / 100;
            
            if (eLIBs_strlen(g_fm_app.fm_status->presets[i].ps_name) > 0) {
                // Show PS name if available
                eLIBs_sprintf(preset_text, "%.8s", g_fm_app.fm_status->presets[i].ps_name);
            } else {
                // Show frequency
                eLIBs_sprintf(preset_text, "%d.%d", mhz, khz);
            }
            
            lv_label_set_text(lv_obj_get_child(g_fm_app.ui.preset_buttons[i], 0), preset_text);
            lv_obj_clear_flag(g_fm_app.ui.preset_buttons[i], LV_OBJ_FLAG_HIDDEN);
        } else {
            // Empty preset
            lv_obj_add_flag(g_fm_app.ui.preset_buttons[i], LV_OBJ_FLAG_HIDDEN);
        }
    }
    
    eLIBs_printf("[FM_APP] Preset buttons updated\n");
}

/**
 * @brief Update scan mode indicator
 */
static void update_scan_mode_indicator(__u8 scan_mode) {
    const char *mode_text[] = {"Manual", "Auto", "Scan", "Preset"};
    
    if (!g_fm_app.ui.scan_mode_label) return;
    
    if (scan_mode < sizeof(mode_text)/sizeof(mode_text[0])) {
        lv_label_set_text(g_fm_app.ui.scan_mode_label, mode_text[scan_mode]);
    } else {
        lv_label_set_text(g_fm_app.ui.scan_mode_label, "Unknown");
    }
    
    eLIBs_printf("[FM_APP] Scan mode updated: %s\n", mode_text[scan_mode]);
}

/*
*********************************************************************************************************
*                                           SYNC IMPLEMENTATION
*********************************************************************************************************
*/

/**
 * @brief Perform full UI refresh (application switch or initialization)
 */
static void refresh_all_fm_ui(void) {
    if (!g_fm_app.fm_status) return;
    
    eLIBs_printf("[FM_APP] Full UI refresh\n");
    
    update_frequency_display(g_fm_app.fm_status->frequency);
    update_rds_ps_display(g_fm_app.fm_status->rds_data.ps_name);
    update_rds_radio_text(g_fm_app.fm_status->rds_data.radio_text);
    update_stereo_indicator(g_fm_app.fm_status->stereo_status);
    update_signal_strength(g_fm_app.fm_status->signal_strength);
    update_preset_buttons();
    update_scan_mode_indicator(g_fm_app.fm_status->scan_mode);
    
    g_fm_app.full_sync_count++;
}

/**
 * @brief Handle FM status sync message (optimized)
 */
__s32 app_handle_fm_sync_message(void) {
    if (!g_fm_app_initialized || !g_fm_app.app_active) {
        return EPDK_OK;  // App not active, ignore
    }
    
    g_fm_app.sync_count++;
    
    // Check if we need application switch synchronization
    if (SYNC_CHECK_APP_SWITCH(&g_fm_app.fm_status->sync, &g_fm_app.last_sequence)) {
        // Full synchronization (application switch)
        refresh_all_fm_ui();
        
        // Clear any pending changes to avoid duplicate processing
        SYNC_GET_CHANGES(&g_fm_app.fm_status->sync);
        return EPDK_OK;
    }
    
    // Incremental synchronization (normal operation)
    __u32 changes = SYNC_GET_CHANGES(&g_fm_app.fm_status->sync);
    if (changes == 0) {
        return EPDK_OK;  // No changes to process
    }
    
    eLIBs_printf("[FM_APP] Processing incremental changes: 0x%08X\n", changes);
    
    // Process each change type
    if (changes & FM_DATA_FREQUENCY) {
        update_frequency_display(g_fm_app.fm_status->frequency);
    }
    
    if (changes & FM_DATA_RDS_PS) {
        update_rds_ps_display(g_fm_app.fm_status->rds_data.ps_name);
    }
    
    if (changes & FM_DATA_RDS_RT) {
        update_rds_radio_text(g_fm_app.fm_status->rds_data.radio_text);
    }
    
    if (changes & FM_DATA_STEREO) {
        update_stereo_indicator(g_fm_app.fm_status->stereo_status);
    }
    
    if (changes & FM_DATA_SIGNAL) {
        update_signal_strength(g_fm_app.fm_status->signal_strength);
    }
    
    if (changes & FM_DATA_PRESETS) {
        update_preset_buttons();
    }
    
    if (changes & FM_DATA_SCAN_MODE) {
        update_scan_mode_indicator(g_fm_app.fm_status->scan_mode);
    }
    
    return EPDK_OK;
}

/*
*********************************************************************************************************
*                                           APPLICATION LIFECYCLE
*********************************************************************************************************
*/

/**
 * @brief Initialize FM application
 */
__s32 fm_app_init(void) {
    if (g_fm_app_initialized) return EPDK_OK;
    
    // Get FM status pointer from driver
    if (fm_get_status_optimized(&g_fm_app.fm_status) != EPDK_OK) {
        return EPDK_FAIL;
    }
    
    // Initialize application context
    g_fm_app.last_sequence = 0;
    g_fm_app.app_active = EPDK_FALSE;
    g_fm_app.sync_count = 0;
    g_fm_app.full_sync_count = 0;
    
    // Initialize UI objects (would be done by UI creation code)
    eLIBs_memset(&g_fm_app.ui, 0, sizeof(g_fm_app.ui));
    
    // Register with message dispatch system
    msg_emit_register_app(dsk_get_fm_app_handle(), "FM_APP", 
                         (1 << 0) | (1 << 3) | (1 << 4));  // FM + System + Power
    
    g_fm_app_initialized = EPDK_TRUE;
    
    eLIBs_printf("[FM_APP] Application initialized\n");
    return EPDK_OK;
}

/**
 * @brief Activate FM application (source switch)
 */
__s32 fm_app_activate(void) {
    if (!g_fm_app_initialized) {
        fm_app_init();
    }
    
    g_fm_app.app_active = EPDK_TRUE;
    
    // Reset sequence tracking to force full sync on next message
    g_fm_app.last_sequence = 0;
    
    // Clear any pending changes
    if (g_fm_app.fm_status) {
        SYNC_GET_CHANGES(&g_fm_app.fm_status->sync);
    }
    
    // Perform immediate full sync
    refresh_all_fm_ui();
    if (g_fm_app.fm_status) {
        g_fm_app.last_sequence = g_fm_app.fm_status->sync.sequence_number;
    }
    
    eLIBs_printf("[FM_APP] Application activated\n");
    return EPDK_OK;
}

/**
 * @brief Deactivate FM application
 */
__s32 fm_app_deactivate(void) {
    g_fm_app.app_active = EPDK_FALSE;
    
    eLIBs_printf("[FM_APP] Application deactivated\n");
    return EPDK_OK;
}

/**
 * @brief FM application message handler
 */
__s32 fm_app_message_handler(__gui_msg_t *msg) {
    switch (msg->id) {
        case GUI_MSG_COMMAND: {
            __cmd_of_queue_t cmd = (__cmd_of_queue_t)msg->dwAddData1;
            
            switch (cmd) {
                case CMD_FM_STATUS_SYNC:
                    return app_handle_fm_sync_message();
                    
                case CMD_SYS_SOURCE: {
                    __u32 new_source = msg->dwAddData2;
                    if (new_source == ID_WORK_SOURCE_FM) {
                        return fm_app_activate();
                    } else {
                        return fm_app_deactivate();
                    }
                }
                
                case CMD_SYS_POWER:
                    // Handle power events if needed
                    break;
                    
                default:
                    break;
            }
            break;
        }
        
        default:
            break;
    }
    
    return EPDK_OK;
}

/*
*********************************************************************************************************
*                                           DEMO AND TESTING
*********************************************************************************************************
*/

/**
 * @brief Get FM application statistics
 */
void fm_app_get_statistics(void) {
    eLIBs_printf("\n=== FM Application Statistics ===\n");
    eLIBs_printf("Initialized: %s\n", g_fm_app_initialized ? "Yes" : "No");
    eLIBs_printf("Active: %s\n", g_fm_app.app_active ? "Yes" : "No");
    eLIBs_printf("Total syncs: %d\n", g_fm_app.sync_count);
    eLIBs_printf("Full syncs: %d\n", g_fm_app.full_sync_count);
    eLIBs_printf("Last sequence: %d\n", g_fm_app.last_sequence);
    
    if (g_fm_app.fm_status) {
        eLIBs_printf("Current sequence: %d\n", g_fm_app.fm_status->sync.sequence_number);
        eLIBs_printf("Pending changes: 0x%08X\n", g_fm_app.fm_status->sync.dirty_mask_pending);
        eLIBs_printf("Current changes: 0x%08X\n", g_fm_app.fm_status->sync.dirty_mask_current);
    }
    
    eLIBs_printf("=================================\n\n");
}

/**
 * @brief Demo: Test FM application sync
 */
void demo_test_fm_app_sync(void) {
    eLIBs_printf("Testing FM application synchronization...\n");
    
    // Initialize FM app
    fm_app_init();
    
    // Simulate source switch to FM
    fm_app_activate();
    
    // Simulate driver data changes
    demo_fm_frequency_change(101500);  // 101.5 MHz
    demo_fm_rds_ps_update("TEST_FM");
    
    // Wait for sync processing
    esKRNL_TimeDly(50);
    
    // Simulate source switch away and back
    fm_app_deactivate();
    demo_fm_frequency_change(95300);   // 95.3 MHz
    fm_app_activate();
    
    // Show statistics
    fm_app_get_statistics();
}