/*
*********************************************************************************************************
*                                              MELIS RTOS
*                           Dual-Buffer Realtime Sync Optimization - Core Header
*
*                                    (c) Copyright 2024, Sunny China
*                                             All Rights Reserved
*
* File    : sync_core.h
* By      : Claude Code
* Version : v1.0
* Date    : 2024-12-XX
* Descript: Core synchronization mechanisms for dual-buffer data sync optimization
* Update  : date                auther      ver     notes
*********************************************************************************************************
*/

#ifndef __SYNC_CORE_H__
#define __SYNC_CORE_H__

#include <elibs.h>
#include <typedef.h>

#ifdef __cplusplus
extern "C" {
#endif

/*
*********************************************************************************************************
*                                           CORE SYNC INTERFACE
*********************************************************************************************************
*/

/**
 * @brief Mark data changed (Driver side usage)
 * @param current_mask Pointer to current buffer mask
 * @param data_type_bits Changed data type bitmap (from New Protocol v2)
 */
static inline void mark_data_changed(volatile __u32 *current_mask, __u32 data_type_bits) {
    __sync_fetch_and_or(current_mask, data_type_bits);  // Atomic OR operation
}

/**
 * @brief Commit changes to pending buffer (Lock-free exchange)
 * @param current_mask Pointer to current buffer mask
 * @param pending_mask Pointer to pending buffer mask  
 * @param seq_num Pointer to sequence number
 * @return Committed change bitmap
 */
static inline __u32 commit_changes(volatile __u32 *current_mask, 
                                   volatile __u32 *pending_mask,
                                   volatile __u32 *seq_num) {
    __u32 changes = __sync_lock_test_and_set(current_mask, 0);  // Atomic exchange and clear
    if (changes != 0) {
        __sync_fetch_and_or(pending_mask, changes);  // Accumulate to pending
        __sync_fetch_and_add(seq_num, 1);            // Increment sequence number
    }
    return changes;
}

/**
 * @brief Get and clear changes (Application side usage)
 * @param pending_mask Pointer to pending buffer mask
 * @return Change bitmap
 */
static inline __u32 get_and_clear_changes(volatile __u32 *pending_mask) {
    return __sync_lock_test_and_set(pending_mask, 0);  // Atomic get and clear
}

/**
 * @brief Check app switch synchronization
 * @param seq_num Current sequence number
 * @param last_seq Pointer to last sequence number
 * @return EPDK_TRUE: need full sync, EPDK_FALSE: no need
 */
static inline __bool sync_on_app_switch(__u32 seq_num, __u32 *last_seq) {
    if (seq_num != *last_seq) {
        *last_seq = seq_num;
        return EPDK_TRUE;  // Need full sync
    }
    return EPDK_FALSE;     // No need for full sync
}

/**
 * @brief Push message if changed (with built-in deduplication)
 * @param changes Change bitmap
 * @param cmd Queue command
 */
static inline void push_if_changed(__u32 changes, __cmd_of_queue_t cmd) {
    if (changes != 0) {
        mcu_push_cmd_to_queue(cmd);
    }
}

/*
*********************************************************************************************************
*                                           CATEGORY STATUS BASE
*********************************************************************************************************
*/

/**
 * @brief Base structure for all category status (dual-buffer sync fields)
 */
typedef struct {
    volatile __u32 dirty_mask_current;    // Driver side write buffer
    volatile __u32 dirty_mask_pending;    // Application side read buffer
    volatile __u32 sequence_number;       // Data sequence number
} sync_base_t;

/*
*********************************************************************************************************
*                                           DATA TYPE DEFINITIONS
*********************************************************************************************************
*/

// FM Tuner Data Types (Category 0x10) - Reuse New Protocol v2 data_type bitmap
#define FM_DATA_FREQUENCY      (1 << 0)   // Current frequency
#define FM_DATA_TUNER_STATE    (1 << 1)   // Tuner state
#define FM_DATA_RDS_PS         (1 << 2)   // RDS PS name
#define FM_DATA_RDS_RT         (1 << 3)   // RDS radio text
#define FM_DATA_PRESETS        (1 << 4)   // Preset list
#define FM_DATA_SCAN_MODE      (1 << 5)   // Scan mode
#define FM_DATA_STEREO         (1 << 6)   // Stereo status
#define FM_DATA_SIGNAL         (1 << 7)   // Signal strength
#define FM_DATA_PTY            (1 << 8)   // PTY code
#define FM_DATA_AF             (1 << 9)   // AF status
#define FM_DATA_TA             (1 << 10)  // TA status

// USB Disk Data Types (Category 0x20)
#define USB_DATA_TRACK_INFO    (1 << 0)   // Current track info
#define USB_DATA_PLAY_STATE    (1 << 1)   // Play state
#define USB_DATA_PLAYLIST      (1 << 2)   // Playlist info
#define USB_DATA_FOLDER_INFO   (1 << 3)   // Folder info
#define USB_DATA_PROGRESS      (1 << 4)   // Play progress
#define USB_DATA_VOLUME        (1 << 5)   // Volume level
#define USB_DATA_REPEAT_MODE   (1 << 6)   // Repeat mode
#define USB_DATA_SHUFFLE       (1 << 7)   // Shuffle mode
#define USB_DATA_DEVICE_INFO   (1 << 8)   // Device information

// Settings Data Types (Category 0x03)
#define SETTINGS_DATA_LANGUAGE    (1 << 0)   // Language setting
#define SETTINGS_DATA_BRIGHTNESS  (1 << 1)   // Display brightness
#define SETTINGS_DATA_SOUND       (1 << 2)   // Sound settings
#define SETTINGS_DATA_CLOCK       (1 << 3)   // Clock settings
#define SETTINGS_DATA_SYSTEM      (1 << 4)   // System settings

/*
*********************************************************************************************************
*                                           COMMAND DEFINITIONS
*********************************************************************************************************
*/

// Unified sync commands (one per category)
#define CMD_FM_STATUS_SYNC        0x1001
#define CMD_USB_STATUS_SYNC       0x2001  
#define CMD_SETTINGS_STATUS_SYNC  0x3001

/*
*********************************************************************************************************
*                                           UTILITY MACROS
*********************************************************************************************************
*/

#define SYNC_INIT_BASE(base_ptr) do { \
    (base_ptr)->dirty_mask_current = 0; \
    (base_ptr)->dirty_mask_pending = 0; \
    (base_ptr)->sequence_number = 0; \
} while(0)

#define SYNC_MARK_CHANGED(base_ptr, data_bits) \
    mark_data_changed(&(base_ptr)->dirty_mask_current, (data_bits))

#define SYNC_COMMIT_AND_PUSH(base_ptr, cmd) do { \
    __u32 changes = commit_changes(&(base_ptr)->dirty_mask_current, \
                                   &(base_ptr)->dirty_mask_pending, \
                                   &(base_ptr)->sequence_number); \
    push_if_changed(changes, (cmd)); \
} while(0)

#define SYNC_GET_CHANGES(base_ptr) \
    get_and_clear_changes(&(base_ptr)->dirty_mask_pending)

#define SYNC_CHECK_APP_SWITCH(base_ptr, last_seq_ptr) \
    sync_on_app_switch((base_ptr)->sequence_number, (last_seq_ptr))

#ifdef __cplusplus
}
#endif

#endif /* __SYNC_CORE_H__ */