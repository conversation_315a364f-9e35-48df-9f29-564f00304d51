/*
*********************************************************************************************************
*                                              MELIS RTOS
*                       Message Emit Sync Demo - Optimized Message Dispatching
*
*                                    (c) Copyright 2024, Sunny China
*                                             All Rights Reserved
*
* File    : msg_emit_sync_demo.c
* By      : Claude Code
* Version : v1.0
* Date    : 2024-12-XX
* Descript: Demo implementation of optimized msg_emit message dispatching
* Update  : date                auther      ver     notes
*********************************************************************************************************
*/

#include "sync_core.h"
#include <mod_desktop.h>

/*
*********************************************************************************************************
*                                           LOCAL DEFINITIONS
*********************************************************************************************************
*/

// Message recipient info
typedef struct {
    __hdle app_handle;                      // Application handle
    char app_name[32];                      // Application name
    __u32 interested_commands;              // Bitmask of interested commands
    __bool active;                          // Is application active
    __u32 message_count;                    // Messages sent to this app
} msg_recipient_t;

// Message dispatch control
typedef struct {
    msg_recipient_t recipients[16];         // Max 16 applications
    __u32 recipient_count;                  // Number of registered recipients
    __hdle dispatch_mutex;                  // Dispatcher protection mutex
    __u32 total_dispatched;                 // Total messages dispatched
    __u32 filtered_messages;                // Messages filtered out
} msg_dispatch_ctrl_t;

/*
*********************************************************************************************************
*                                           LOCAL VARIABLES
*********************************************************************************************************
*/

// Message dispatch control
static msg_dispatch_ctrl_t g_msg_dispatch = {0};
static __bool g_dispatch_initialized = EPDK_FALSE;

// Command to interest bitmask mapping
static struct {
    __cmd_of_queue_t cmd;
    __u32 interest_mask;
} g_cmd_interest_map[] = {
    {CMD_FM_STATUS_SYNC,      (1 << 0)},   // FM interest
    {CMD_USB_STATUS_SYNC,     (1 << 1)},   // USB interest
    {CMD_SETTINGS_STATUS_SYNC,(1 << 2)},   // Settings interest
    {CMD_SYS_SOURCE,          (1 << 3)},   // System events
    {CMD_SYS_POWER,           (1 << 4)},   // Power events
    {CMD_VOLUME_CHANGED,      (1 << 5)},   // Volume events
};

static const __u32 g_cmd_map_size = sizeof(g_cmd_interest_map) / sizeof(g_cmd_interest_map[0]);

/*
*********************************************************************************************************
*                                           HELPER FUNCTIONS
*********************************************************************************************************
*/

/**
 * @brief Initialize message dispatch system
 */
static __s32 msg_dispatch_init(void) {
    if (g_dispatch_initialized) return EPDK_OK;
    
    eLIBs_memset(&g_msg_dispatch, 0, sizeof(g_msg_dispatch));
    
    // Create mutex for dispatcher protection
    g_msg_dispatch.dispatch_mutex = esKRNL_MutexCreate(KRNL_MUTEX_PRIO_INHERIT);
    if (!g_msg_dispatch.dispatch_mutex) {
        return EPDK_FAIL;
    }
    
    g_dispatch_initialized = EPDK_TRUE;
    return EPDK_OK;
}

/**
 * @brief Get interest mask for command
 */
static __u32 get_command_interest_mask(__cmd_of_queue_t cmd) {
    __u32 i;
    for (i = 0; i < g_cmd_map_size; i++) {
        if (g_cmd_interest_map[i].cmd == cmd) {
            return g_cmd_interest_map[i].interest_mask;
        }
    }
    return 0;  // No interest
}

/**
 * @brief Find recipient by handle
 */
static msg_recipient_t* find_recipient(__hdle app_handle) {
    __u32 i;
    for (i = 0; i < g_msg_dispatch.recipient_count; i++) {
        if (g_msg_dispatch.recipients[i].app_handle == app_handle) {
            return &g_msg_dispatch.recipients[i];
        }
    }
    return NULL;
}

/**
 * @brief Send message to specific application
 */
static __s32 send_message_to_app(__hdle app_handle, __cmd_of_queue_t cmd) {
    __gui_msg_t msg = {0};
    
    // Construct GUI message
    msg.h_srcwin = NULL;
    msg.h_deswin = app_handle;
    msg.id = GUI_MSG_COMMAND;
    msg.dwAddData1 = cmd;
    msg.dwAddData2 = 0;
    msg.dwReserved = 0;
    
    // Send message to application
    return GUI_SendNotifyMessage(&msg);
}

/*
*********************************************************************************************************
*                                           PUBLIC INTERFACE
*********************************************************************************************************
*/

/**
 * @brief Register application for message reception
 * @param app_handle Application handle
 * @param app_name Application name
 * @param interest_mask Bitmask of interested message types
 * @return EPDK_OK: success, others: error
 */
__s32 msg_emit_register_app(__hdle app_handle, const char *app_name, __u32 interest_mask) {
    if (!g_dispatch_initialized) {
        msg_dispatch_init();
    }
    
    if (g_msg_dispatch.recipient_count >= 16) {
        return EPDK_FAIL;  // Too many recipients
    }
    
    esKRNL_MutexPend(g_msg_dispatch.dispatch_mutex, 0, NULL);
    
    // Check if already registered
    msg_recipient_t *recipient = find_recipient(app_handle);
    if (recipient) {
        // Update existing registration
        recipient->interested_commands = interest_mask;
        recipient->active = EPDK_TRUE;
        eLIBs_strncpy(recipient->app_name, app_name, 32);
        recipient->app_name[31] = '\0';
    } else {
        // Add new recipient
        recipient = &g_msg_dispatch.recipients[g_msg_dispatch.recipient_count];
        recipient->app_handle = app_handle;
        recipient->interested_commands = interest_mask;
        recipient->active = EPDK_TRUE;
        recipient->message_count = 0;
        eLIBs_strncpy(recipient->app_name, app_name, 32);
        recipient->app_name[31] = '\0';
        g_msg_dispatch.recipient_count++;
    }
    
    esKRNL_MutexPost(g_msg_dispatch.dispatch_mutex);
    
    return EPDK_OK;
}

/**
 * @brief Unregister application from message reception
 */
__s32 msg_emit_unregister_app(__hdle app_handle) {
    if (!g_dispatch_initialized) return EPDK_FAIL;
    
    esKRNL_MutexPend(g_msg_dispatch.dispatch_mutex, 0, NULL);
    
    msg_recipient_t *recipient = find_recipient(app_handle);
    if (recipient) {
        recipient->active = EPDK_FALSE;
    }
    
    esKRNL_MutexPost(g_msg_dispatch.dispatch_mutex);
    
    return EPDK_OK;
}

/**
 * @brief Optimized message emit with filtering
 * @param cmd Command to emit
 * @return Number of recipients that received the message
 */
__s32 msg_emit_sync_optimized(__cmd_of_queue_t cmd) {
    __u32 interest_mask;
    __u32 recipients_notified = 0;
    __u32 i;
    
    if (!g_dispatch_initialized) {
        msg_dispatch_init();
    }
    
    // Get interest mask for this command
    interest_mask = get_command_interest_mask(cmd);
    if (interest_mask == 0) {
        g_msg_dispatch.filtered_messages++;
        return 0;  // No one interested
    }
    
    esKRNL_MutexPend(g_msg_dispatch.dispatch_mutex, 0, NULL);
    
    // Dispatch to interested and active recipients
    for (i = 0; i < g_msg_dispatch.recipient_count; i++) {
        msg_recipient_t *recipient = &g_msg_dispatch.recipients[i];
        
        if (recipient->active && (recipient->interested_commands & interest_mask)) {
            if (send_message_to_app(recipient->app_handle, cmd) == EPDK_OK) {
                recipient->message_count++;
                recipients_notified++;
            }
        }
    }
    
    g_msg_dispatch.total_dispatched++;
    
    esKRNL_MutexPost(g_msg_dispatch.dispatch_mutex);
    
    return recipients_notified;
}

/**
 * @brief Set application active/inactive status
 */
__s32 msg_emit_set_app_active(__hdle app_handle, __bool active) {
    if (!g_dispatch_initialized) return EPDK_FAIL;
    
    esKRNL_MutexPend(g_msg_dispatch.dispatch_mutex, 0, NULL);
    
    msg_recipient_t *recipient = find_recipient(app_handle);
    if (recipient) {
        recipient->active = active;
    }
    
    esKRNL_MutexPost(g_msg_dispatch.dispatch_mutex);
    
    return EPDK_OK;
}

/*
*********************************************************************************************************
*                                           ENHANCED DISPATCH WITH SOURCE SWITCHING
*********************************************************************************************************
*/

// Current active source tracking
static __u32 g_current_source = ID_WORK_SOURCE_FM;
static __u32 g_previous_source = ID_WORK_SOURCE_FM;

/**
 * @brief Handle source switch with optimized dispatch
 */
__s32 msg_emit_handle_source_switch(__u32 new_source) {
    __u32 old_source = g_current_source;
    
    if (old_source == new_source) {
        return EPDK_OK;  // No change
    }
    
    g_previous_source = old_source;
    g_current_source = new_source;
    
    // Deactivate old source applications
    switch (old_source) {
        case ID_WORK_SOURCE_FM:
            msg_emit_set_app_active(dsk_get_fm_app_handle(), EPDK_FALSE);
            break;
        case ID_WORK_SOURCE_USB_DISK:
        case ID_WORK_SOURCE_IPOD:
            msg_emit_set_app_active(dsk_get_music_app_handle(), EPDK_FALSE);
            break;
        case ID_WORK_SOURCE_SETTINGS:
            msg_emit_set_app_active(dsk_get_settings_app_handle(), EPDK_FALSE);
            break;
    }
    
    // Activate new source applications and trigger initial sync
    switch (new_source) {
        case ID_WORK_SOURCE_FM:
            msg_emit_set_app_active(dsk_get_fm_app_handle(), EPDK_TRUE);
            msg_emit_sync_optimized(CMD_FM_STATUS_SYNC);  // Trigger initial sync
            break;
        case ID_WORK_SOURCE_USB_DISK:
        case ID_WORK_SOURCE_IPOD:
            msg_emit_set_app_active(dsk_get_music_app_handle(), EPDK_TRUE);
            msg_emit_sync_optimized(CMD_USB_STATUS_SYNC);  // Trigger initial sync
            break;
        case ID_WORK_SOURCE_SETTINGS:
            msg_emit_set_app_active(dsk_get_settings_app_handle(), EPDK_TRUE);
            msg_emit_sync_optimized(CMD_SETTINGS_STATUS_SYNC);  // Trigger initial sync
            break;
    }
    
    // Always notify system about source change
    msg_emit_sync_optimized(CMD_SYS_SOURCE);
    
    return EPDK_OK;
}

/*
*********************************************************************************************************
*                                           STATISTICS AND DEBUG
*********************************************************************************************************
*/

/**
 * @brief Get dispatch statistics
 */
void msg_emit_get_statistics(void) {
    __u32 i;
    
    if (!g_dispatch_initialized) return;
    
    esKRNL_MutexPend(g_msg_dispatch.dispatch_mutex, 0, NULL);
    
    eLIBs_printf("\n=== Message Dispatch Statistics ===\n");
    eLIBs_printf("Total dispatched: %d\n", g_msg_dispatch.total_dispatched);
    eLIBs_printf("Filtered messages: %d\n", g_msg_dispatch.filtered_messages);
    eLIBs_printf("Active recipients: %d\n", g_msg_dispatch.recipient_count);
    
    eLIBs_printf("\n=== Recipient Details ===\n");
    for (i = 0; i < g_msg_dispatch.recipient_count; i++) {
        msg_recipient_t *recipient = &g_msg_dispatch.recipients[i];
        eLIBs_printf("%s (0x%08X): %s, Interest=0x%08X, Messages=%d\n",
                     recipient->app_name,
                     (__u32)recipient->app_handle,
                     recipient->active ? "Active" : "Inactive",
                     recipient->interested_commands,
                     recipient->message_count);
    }
    
    esKRNL_MutexPost(g_msg_dispatch.dispatch_mutex);
    
    eLIBs_printf("===================================\n\n");
}

/*
*********************************************************************************************************
*                                           DEMO APPLICATIONS
*********************************************************************************************************
*/

// Demo application handles (simulated)
static __hdle g_demo_fm_app = (__hdle)0x1000;
static __hdle g_demo_usb_app = (__hdle)0x2000;
static __hdle g_demo_settings_app = (__hdle)0x3000;
static __hdle g_demo_root_app = (__hdle)0x4000;

/**
 * @brief Demo: Initialize demo applications
 */
void demo_init_applications(void) {
    // Register FM application (interested in FM and system events)
    msg_emit_register_app(g_demo_fm_app, "FM_APP", 
                         (1 << 0) | (1 << 3) | (1 << 4));  // FM + System + Power
    
    // Register USB application (interested in USB and system events)
    msg_emit_register_app(g_demo_usb_app, "USB_APP", 
                         (1 << 1) | (1 << 3) | (1 << 4));  // USB + System + Power
    
    // Register Settings application (interested in settings and system events)
    msg_emit_register_app(g_demo_settings_app, "SETTINGS_APP", 
                         (1 << 2) | (1 << 3));             // Settings + System
    
    // Register Root application (interested in all events)
    msg_emit_register_app(g_demo_root_app, "ROOT_APP", 
                         0xFFFFFFFF);                       // All events
    
    eLIBs_printf("Demo applications registered.\n");
}

/**
 * @brief Demo: Test message dispatching
 */
void demo_test_message_dispatch(void) {
    eLIBs_printf("Testing message dispatch system...\n");
    
    demo_init_applications();
    
    // Initially FM is active
    msg_emit_set_app_active(g_demo_fm_app, EPDK_TRUE);
    msg_emit_set_app_active(g_demo_usb_app, EPDK_FALSE);
    msg_emit_set_app_active(g_demo_settings_app, EPDK_FALSE);
    msg_emit_set_app_active(g_demo_root_app, EPDK_TRUE);
    
    // Test FM messages
    eLIBs_printf("Sending FM status sync...\n");
    msg_emit_sync_optimized(CMD_FM_STATUS_SYNC);
    
    // Test source switch to USB
    eLIBs_printf("Switching to USB source...\n");
    msg_emit_handle_source_switch(ID_WORK_SOURCE_USB_DISK);
    
    // Test USB messages
    eLIBs_printf("Sending USB status sync...\n");
    msg_emit_sync_optimized(CMD_USB_STATUS_SYNC);
    
    // Test system messages (should reach active apps)
    eLIBs_printf("Sending system power message...\n");
    msg_emit_sync_optimized(CMD_SYS_POWER);
    
    msg_emit_get_statistics();
}