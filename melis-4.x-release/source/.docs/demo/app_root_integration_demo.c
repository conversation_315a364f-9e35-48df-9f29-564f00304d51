/*
*********************************************************************************************************
*                                              MELIS RTOS
*                     App Root Integration Demo - Complete System Integration Test
*
*                                    (c) Copyright 2024, Sunny China
*                                             All Rights Reserved
*
* File    : app_root_integration_demo.c
* By      : Claude Code
* Version : v1.0
* Date    : 2024-12-XX
* Descript: Demo implementation of complete system integration including app_root
* Update  : date                auther      ver     notes
*********************************************************************************************************
*/

#include "sync_core.h"
#include "message_queue_dedup_demo.c"
#include "msg_emit_sync_demo.c"
#include "app_fm_sync_demo.c"
#include "app_usb_sync_demo.c"

/*
*********************************************************************************************************
*                                           LOCAL DEFINITIONS
*********************************************************************************************************
*/

// App Root context
typedef struct {
    __u32 current_source;                   // Current active source
    __u32 previous_source;                  // Previous source
    __bool system_initialized;              // System initialization status
    __u32 source_switch_count;              // Number of source switches
    __u32 total_messages_processed;         // Total messages processed
    __hdle message_thread;                  // Message processing thread
    __bool message_thread_running;          // Thread running flag
} app_root_context_t;

/*
*********************************************************************************************************
*                                           LOCAL VARIABLES
*********************************************************************************************************
*/

static app_root_context_t g_app_root = {0};

// Source name mapping
static const struct {
    __u32 source_id;
    const char *name;
} g_source_names[] = {
    {ID_WORK_SOURCE_FM,       "FM Radio"},
    {ID_WORK_SOURCE_USB_DISK, "USB Disk"},
    {ID_WORK_SOURCE_IPOD,     "iPod"},
    {ID_WORK_SOURCE_SETTINGS, "Settings"},
    {ID_WORK_SOURCE_BT_AUDIO, "Bluetooth Audio"},
};

/*
*********************************************************************************************************
*                                           HELPER FUNCTIONS
*********************************************************************************************************
*/

/**
 * @brief Get source name string
 */
static const char* get_source_name(__u32 source_id) {
    __u32 i;
    for (i = 0; i < sizeof(g_source_names)/sizeof(g_source_names[0]); i++) {
        if (g_source_names[i].source_id == source_id) {
            return g_source_names[i].name;
        }
    }
    return "Unknown";
}

/**
 * @brief Message processing thread
 */
static void message_processing_thread(void *parg) {
    __cmd_of_queue_t cmd;
    
    eLIBs_printf("[APP_ROOT] Message processing thread started\n");
    
    while (g_app_root.message_thread_running) {
        // Get next message from queue (with 100ms timeout)
        if (mcu_get_next_queue_message(&cmd, 100) == EPDK_OK) {
            g_app_root.total_messages_processed++;
            
            eLIBs_printf("[APP_ROOT] Processing message: 0x%04X\n", cmd);
            
            // Dispatch message to interested applications
            __s32 recipients = msg_emit_sync_optimized(cmd);
            
            eLIBs_printf("[APP_ROOT] Message dispatched to %d recipients\n", recipients);
        }
    }
    
    eLIBs_printf("[APP_ROOT] Message processing thread stopped\n");
}

/*
*********************************************************************************************************
*                                           SOURCE MANAGEMENT
*********************************************************************************************************
*/

/**
 * @brief Handle source switch request
 */
__s32 app_root_switch_source(__u32 new_source) {
    if (g_app_root.current_source == new_source) {
        return EPDK_OK;  // No change needed
    }
    
    eLIBs_printf("[APP_ROOT] Source switch: %s -> %s\n", 
                 get_source_name(g_app_root.current_source),
                 get_source_name(new_source));
    
    g_app_root.previous_source = g_app_root.current_source;
    g_app_root.current_source = new_source;
    g_app_root.source_switch_count++;
    
    // Handle source switch in message dispatch system
    msg_emit_handle_source_switch(new_source);
    
    return EPDK_OK;
}

/**
 * @brief Get current source
 */
__u32 app_root_get_current_source(void) {
    return g_app_root.current_source;
}

/*
*********************************************************************************************************
*                                           INITIALIZATION
*********************************************************************************************************
*/

/**
 * @brief Initialize complete system
 */
__s32 app_root_system_init(void) {
    if (g_app_root.system_initialized) {
        return EPDK_OK;
    }
    
    eLIBs_printf("[APP_ROOT] Initializing complete system...\n");
    
    // Initialize message queue system
    if (msg_queue_init() != EPDK_OK) {
        eLIBs_printf("[APP_ROOT] Failed to initialize message queue\n");
        return EPDK_FAIL;
    }
    
    // Initialize message dispatch system
    if (msg_dispatch_init() != EPDK_OK) {
        eLIBs_printf("[APP_ROOT] Failed to initialize message dispatch\n");
        return EPDK_FAIL;
    }
    
    // Register driver handlers
    if (fm_tuner_sync_demo_register() != EPDK_OK) {
        eLIBs_printf("[APP_ROOT] Failed to register FM tuner handler\n");
        return EPDK_FAIL;
    }
    
    if (usb_disk_sync_demo_register() != EPDK_OK) {
        eLIBs_printf("[APP_ROOT] Failed to register USB disk handler\n");
        return EPDK_FAIL;
    }
    
    // Initialize applications
    fm_app_init();
    usb_app_init();
    
    // Configure deduplication windows
    mcu_configure_dedup_window(CMD_FM_STATUS_SYNC, 50);   // 50ms for FM
    mcu_configure_dedup_window(CMD_USB_STATUS_SYNC, 30);  // 30ms for USB
    mcu_configure_dedup_window(CMD_SYS_SOURCE, 0);        // No dedup for source
    mcu_configure_dedup_window(CMD_SYS_POWER, 0);         // No dedup for power
    
    // Initialize app root context
    g_app_root.current_source = ID_WORK_SOURCE_FM;  // Default to FM
    g_app_root.previous_source = ID_WORK_SOURCE_FM;
    g_app_root.source_switch_count = 0;
    g_app_root.total_messages_processed = 0;
    
    // Start message processing thread
    g_app_root.message_thread_running = EPDK_TRUE;
    g_app_root.message_thread = esKRNL_TCreate(message_processing_thread, NULL,
                                              0x2000,      // 8KB stack
                                              KRNL_priolevel4,  // Normal priority
                                              0, 0,
                                              KRNL_TASK_START,
                                              "MSG_PROC");
    
    if (!g_app_root.message_thread) {
        eLIBs_printf("[APP_ROOT] Failed to create message processing thread\n");
        return EPDK_FAIL;
    }
    
    g_app_root.system_initialized = EPDK_TRUE;
    
    eLIBs_printf("[APP_ROOT] System initialization complete\n");
    return EPDK_OK;
}

/**
 * @brief Cleanup system
 */
void app_root_system_cleanup(void) {
    if (!g_app_root.system_initialized) {
        return;
    }
    
    eLIBs_printf("[APP_ROOT] System cleanup...\n");
    
    // Stop message processing thread
    g_app_root.message_thread_running = EPDK_FALSE;
    if (g_app_root.message_thread) {
        esKRNL_TaskDel(g_app_root.message_thread, KRNL_DEL_ALWAYS, NULL);
        g_app_root.message_thread = NULL;
    }
    
    g_app_root.system_initialized = EPDK_FALSE;
    
    eLIBs_printf("[APP_ROOT] System cleanup complete\n");
}

/*
*********************************************************************************************************
*                                           STATISTICS AND MONITORING
*********************************************************************************************************
*/

/**
 * @brief Get system statistics
 */
void app_root_get_system_statistics(void) {
    eLIBs_printf("\n" "="*50 "\n");
    eLIBs_printf("         COMPLETE SYSTEM STATISTICS\n");
    eLIBs_printf("="*50 "\n");
    
    // App Root statistics
    eLIBs_printf("=== App Root ===\n");
    eLIBs_printf("System initialized: %s\n", g_app_root.system_initialized ? "Yes" : "No");
    eLIBs_printf("Current source: %s\n", get_source_name(g_app_root.current_source));
    eLIBs_printf("Previous source: %s\n", get_source_name(g_app_root.previous_source));
    eLIBs_printf("Source switches: %d\n", g_app_root.source_switch_count);
    eLIBs_printf("Messages processed: %d\n", g_app_root.total_messages_processed);
    
    // Message queue statistics
    mcu_queue_get_statistics();
    
    // Message dispatch statistics  
    msg_emit_get_statistics();
    
    // Application statistics
    fm_app_get_statistics();
    usb_app_get_statistics();
    
    eLIBs_printf("="*50 "\n\n");
}

/*
*********************************************************************************************************
*                                           COMPREHENSIVE DEMO
*********************************************************************************************************
*/

/**
 * @brief Comprehensive system demo
 */
void demo_complete_system_test(void) {
    eLIBs_printf("\n" "="*60 "\n");
    eLIBs_printf("         COMPREHENSIVE SYSTEM INTEGRATION TEST\n");
    eLIBs_printf("="*60 "\n\n");
    
    // Initialize complete system
    eLIBs_printf("Phase 1: System Initialization\n");
    eLIBs_printf("------------------------------\n");
    if (app_root_system_init() != EPDK_OK) {
        eLIBs_printf("System initialization failed!\n");
        return;
    }
    
    // Wait for system to stabilize
    esKRNL_TimeDly(100);
    
    // Phase 2: FM source testing
    eLIBs_printf("\nPhase 2: FM Source Testing\n");
    eLIBs_printf("---------------------------\n");
    
    app_root_switch_source(ID_WORK_SOURCE_FM);
    esKRNL_TimeDly(50);
    
    // Simulate FM data changes
    eLIBs_printf("Simulating FM frequency changes...\n");
    demo_fm_frequency_change(101500);  // 101.5 MHz
    demo_fm_rds_ps_update("DEMO_FM");
    esKRNL_TimeDly(100);
    
    // Rapid frequency changes (should be deduplicated)
    eLIBs_printf("Simulating rapid FM updates (deduplication test)...\n");
    for (__u32 i = 0; i < 10; i++) {
        demo_fm_frequency_change(95000 + i * 200);  // 95.0-96.8 MHz
        esKRNL_TimeDly(10);  // Fast updates
    }
    esKRNL_TimeDly(200);  // Wait for deduplication
    
    // Phase 3: USB source testing
    eLIBs_printf("\nPhase 3: USB Source Testing\n");
    eLIBs_printf("---------------------------\n");
    
    app_root_switch_source(ID_WORK_SOURCE_USB_DISK);
    esKRNL_TimeDly(50);
    
    // Simulate USB music playback
    eLIBs_printf("Simulating USB music playback...\n");
    demo_usb_track_change("Test Track 1", "Test Artist 1", 240);
    demo_usb_play_state_change(1);  // Play
    esKRNL_TimeDly(100);
    
    demo_usb_track_change("Test Track 2", "Test Artist 2", 180);
    esKRNL_TimeDly(100);
    
    // Phase 4: Source switching stress test
    eLIBs_printf("\nPhase 4: Source Switching Test\n");
    eLIBs_printf("------------------------------\n");
    
    __u32 sources[] = {ID_WORK_SOURCE_FM, ID_WORK_SOURCE_USB_DISK, 
                       ID_WORK_SOURCE_IPOD, ID_WORK_SOURCE_FM};
    
    for (__u32 i = 0; i < sizeof(sources)/sizeof(sources[0]); i++) {
        eLIBs_printf("Switching to %s...\n", get_source_name(sources[i]));
        app_root_switch_source(sources[i]);
        
        // Generate some data for each source
        if (sources[i] == ID_WORK_SOURCE_FM) {
            demo_fm_frequency_change(88000 + i * 500);
        } else if (sources[i] == ID_WORK_SOURCE_USB_DISK || 
                   sources[i] == ID_WORK_SOURCE_IPOD) {
            demo_usb_track_change("Switch Test", "Switch Artist", 120 + i * 10);
        }
        
        esKRNL_TimeDly(150);  // Allow processing
    }
    
    // Phase 5: Performance stress test
    eLIBs_printf("\nPhase 5: Performance Stress Test\n");
    eLIBs_printf("--------------------------------\n");
    
    eLIBs_printf("Generating high-frequency messages...\n");
    for (__u32 i = 0; i < 50; i++) {
        demo_fm_frequency_change(87500 + (i % 20) * 100);
        demo_usb_play_state_change(i % 3);  // Cycle through states
        esKRNL_TimeDly(5);  // Very fast updates
    }
    
    // Wait for all processing to complete
    eLIBs_TimeDly(500);
    
    // Phase 6: Final statistics
    eLIBs_printf("\nPhase 6: Final Results\n");
    eLIBs_printf("---------------------\n");
    
    app_root_get_system_statistics();
    
    // Cleanup
    eLIBs_printf("System test completed. Cleaning up...\n");
    app_root_system_cleanup();
    
    eLIBs_printf("\n" "="*60 "\n");
    eLIBs_printf("         SYSTEM INTEGRATION TEST COMPLETE\n");
    eLIBs_printf("="*60 "\n\n");
}

/*
*********************************************************************************************************
*                                           INTERACTIVE DEMO
*********************************************************************************************************
*/

/**
 * @brief Interactive demo menu
 */
void demo_interactive_menu(void) {
    char input;
    __bool running = EPDK_TRUE;
    
    // Initialize system
    app_root_system_init();
    
    eLIBs_printf("\n" "="*50 "\n");
    eLIBs_printf("    INTERACTIVE SYSTEM DEMO\n");
    eLIBs_printf("="*50 "\n");
    
    while (running) {
        eLIBs_printf("\nAvailable Commands:\n");
        eLIBs_printf("1 - Switch to FM\n");
        eLIBs_printf("2 - Switch to USB Disk\n");
        eLIBs_printf("3 - Switch to iPod\n");
        eLIBs_printf("4 - Generate FM data\n");
        eLIBs_printf("5 - Generate USB data\n");
        eLIBs_printf("6 - Show statistics\n");
        eLIBs_printf("7 - Stress test\n");
        eLIBs_printf("q - Quit\n");
        eLIBs_printf("\nEnter command: ");
        
        // In a real implementation, this would read from console
        // For demo purposes, we'll simulate some commands
        static __u32 demo_cmd = 0;
        char demo_sequence[] = {'1', '4', '2', '5', '1', '6', 'q'};
        
        if (demo_cmd < sizeof(demo_sequence)) {
            input = demo_sequence[demo_cmd++];
            eLIBs_printf("%c\n", input);
        } else {
            input = 'q';
        }
        
        switch (input) {
            case '1':
                app_root_switch_source(ID_WORK_SOURCE_FM);
                break;
                
            case '2':
                app_root_switch_source(ID_WORK_SOURCE_USB_DISK);
                break;
                
            case '3':
                app_root_switch_source(ID_WORK_SOURCE_IPOD);
                break;
                
            case '4':
                eLIBs_printf("Generating FM data...\n");
                demo_fm_frequency_change(99000 + (esKRNL_TimeGet() % 100) * 100);
                demo_fm_rds_ps_update("DEMO99FM");
                break;
                
            case '5':
                eLIBs_printf("Generating USB data...\n");
                demo_usb_track_change("Interactive Test", "Demo Artist", 195);
                demo_usb_play_state_change((esKRNL_TimeGet() % 3));
                break;
                
            case '6':
                app_root_get_system_statistics();
                break;
                
            case '7':
                eLIBs_printf("Running stress test...\n");
                demo_test_deduplication();
                demo_test_message_dispatch();
                break;
                
            case 'q':
            case 'Q':
                running = EPDK_FALSE;
                break;
                
            default:
                eLIBs_printf("Invalid command\n");
                break;
        }
        
        esKRNL_TimeDly(100);  // Brief pause between commands
    }
    
    app_root_system_cleanup();
    eLIBs_printf("Interactive demo ended.\n");
}