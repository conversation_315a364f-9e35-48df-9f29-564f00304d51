/*
*********************************************************************************************************
*                                              MELIS RTOS
*                      Application USB Sync Demo - Optimized Music Player Synchronization
*
*                                    (c) Copyright 2024, Sunny China
*                                             All Rights Reserved
*
* File    : app_usb_sync_demo.c
* By      : Claude Code
* Version : v1.0
* Date    : 2024-12-XX
* Descript: Demo implementation of application side USB music player synchronization
* Update  : date                auther      ver     notes
*********************************************************************************************************
*/

#include "sync_core.h"
#include "driver_usb_sync_demo.c"  // Include for accessing usb_disk_status_t
#include <lvgl.h>

/*
*********************************************************************************************************
*                                           LOCAL DEFINITIONS
*********************************************************************************************************
*/

// USB Music Player UI objects
typedef struct {
    lv_obj_t *track_title_label;            // Track title display
    lv_obj_t *artist_label;                 // Artist name display
    lv_obj_t *album_label;                  // Album name display
    lv_obj_t *time_progress_bar;            // Time progress bar
    lv_obj_t *time_current_label;           // Current time display
    lv_obj_t *time_total_label;             // Total time display
    lv_obj_t *play_state_icon;              // Play/Pause/Stop icon
    lv_obj_t *repeat_mode_icon;             // Repeat mode icon
    lv_obj_t *shuffle_icon;                 // Shuffle icon
    lv_obj_t *volume_slider;                // Volume slider
    lv_obj_t *folder_path_label;            // Current folder path
    lv_obj_t *track_counter_label;          // Track x of y
} usb_ui_objects_t;

// USB Music Player application context
typedef struct {
    usb_ui_objects_t ui;                    // UI objects
    usb_disk_status_t *usb_status;          // Pointer to USB status
    __u32 last_sequence;                    // Last processed sequence number
    __bool app_active;                      // Application active status
    __u32 sync_count;                       // Number of syncs processed
    __u32 full_sync_count;                  // Number of full syncs
    __u32 current_source;                   // Current source (USB_DISK or IPOD)
} usb_app_context_t;

/*
*********************************************************************************************************
*                                           LOCAL VARIABLES
*********************************************************************************************************
*/

static usb_app_context_t g_usb_app = {0};
static __bool g_usb_app_initialized = EPDK_FALSE;

/*
*********************************************************************************************************
*                                           UI HELPER FUNCTIONS
*********************************************************************************************************
*/

/**
 * @brief Format time in MM:SS format
 */
static void format_time_string(__u32 seconds, char *time_str, __u16 max_len) {
    __u32 minutes = seconds / 60;
    __u32 secs = seconds % 60;
    eLIBs_snprintf(time_str, max_len, "%02d:%02d", minutes, secs);
}

/**
 * @brief Update track information display
 */
static void update_track_info_display(void) {
    if (!g_usb_app.usb_status) return;
    
    // Update track title
    if (g_usb_app.ui.track_title_label) {
        const char *title = g_usb_app.usb_status->track_info.title;
        if (eLIBs_strlen(title) > 0) {
            lv_label_set_text(g_usb_app.ui.track_title_label, title);
        } else {
            lv_label_set_text(g_usb_app.ui.track_title_label, "Unknown Track");
        }
    }
    
    // Update artist
    if (g_usb_app.ui.artist_label) {
        const char *artist = g_usb_app.usb_status->track_info.artist;
        if (eLIBs_strlen(artist) > 0) {
            lv_label_set_text(g_usb_app.ui.artist_label, artist);
        } else {
            lv_label_set_text(g_usb_app.ui.artist_label, "Unknown Artist");
        }
    }
    
    // Update album
    if (g_usb_app.ui.album_label) {
        const char *album = g_usb_app.usb_status->track_info.album;
        if (eLIBs_strlen(album) > 0) {
            lv_label_set_text(g_usb_app.ui.album_label, album);
        } else {
            lv_label_set_text(g_usb_app.ui.album_label, "Unknown Album");
        }
    }
    
    // Update track counter
    if (g_usb_app.ui.track_counter_label) {
        char counter_str[32];
        eLIBs_snprintf(counter_str, sizeof(counter_str), "Track %d of %d",
                      g_usb_app.usb_status->track_info.track_number,
                      g_usb_app.usb_status->track_info.total_tracks);
        lv_label_set_text(g_usb_app.ui.track_counter_label, counter_str);
    }
    
    eLIBs_printf("[USB_APP] Track info updated: %s - %s\n",
                 g_usb_app.usb_status->track_info.artist,
                 g_usb_app.usb_status->track_info.title);
}

/**
 * @brief Update play state icon
 */
static void update_play_state_display(__u8 play_state) {
    const char *state_symbols[] = {"⏹", "▶", "⏸"};  // Stop, Play, Pause
    
    if (!g_usb_app.ui.play_state_icon) return;
    
    if (play_state < sizeof(state_symbols)/sizeof(state_symbols[0])) {
        lv_label_set_text(g_usb_app.ui.play_state_icon, state_symbols[play_state]);
    } else {
        lv_label_set_text(g_usb_app.ui.play_state_icon, "?");
    }
    
    eLIBs_printf("[USB_APP] Play state updated: %s\n", 
                 (play_state == 0) ? "Stop" : 
                 (play_state == 1) ? "Play" : 
                 (play_state == 2) ? "Pause" : "Unknown");
}

/**
 * @brief Update time progress display
 */
static void update_progress_display(void) {
    if (!g_usb_app.usb_status) return;
    
    __u32 current_time = g_usb_app.usb_status->track_info.current_time;
    __u32 total_time = g_usb_app.usb_status->track_info.duration;
    
    // Update progress bar
    if (g_usb_app.ui.time_progress_bar && total_time > 0) {
        __u32 progress_percent = (current_time * 100) / total_time;
        lv_bar_set_value(g_usb_app.ui.time_progress_bar, progress_percent, LV_ANIM_OFF);
    }
    
    // Update current time display
    if (g_usb_app.ui.time_current_label) {
        char time_str[8];
        format_time_string(current_time, time_str, sizeof(time_str));
        lv_label_set_text(g_usb_app.ui.time_current_label, time_str);
    }
    
    // Update total time display
    if (g_usb_app.ui.time_total_label) {
        char time_str[8];
        format_time_string(total_time, time_str, sizeof(time_str));
        lv_label_set_text(g_usb_app.ui.time_total_label, time_str);
    }
}

/**
 * @brief Update volume display
 */
static void update_volume_display(__u8 volume_level) {
    if (!g_usb_app.ui.volume_slider) return;
    
    lv_slider_set_value(g_usb_app.ui.volume_slider, volume_level, LV_ANIM_ON);
    
    eLIBs_printf("[USB_APP] Volume updated: %d%%\n", volume_level);
}

/**
 * @brief Update repeat mode icon
 */
static void update_repeat_mode_display(__u8 repeat_mode) {
    const char *repeat_symbols[] = {"🔁", "🔂", "➡"};  // Off, Single, All
    
    if (!g_usb_app.ui.repeat_mode_icon) return;
    
    if (repeat_mode < sizeof(repeat_symbols)/sizeof(repeat_symbols[0])) {
        lv_label_set_text(g_usb_app.ui.repeat_mode_icon, repeat_symbols[repeat_mode]);
        lv_obj_clear_flag(g_usb_app.ui.repeat_mode_icon, LV_OBJ_FLAG_HIDDEN);
    } else {
        lv_obj_add_flag(g_usb_app.ui.repeat_mode_icon, LV_OBJ_FLAG_HIDDEN);
    }
}

/**
 * @brief Update shuffle mode icon
 */
static void update_shuffle_display(__u8 shuffle_mode) {
    if (!g_usb_app.ui.shuffle_icon) return;
    
    if (shuffle_mode) {
        lv_label_set_text(g_usb_app.ui.shuffle_icon, "🔀");
        lv_obj_clear_flag(g_usb_app.ui.shuffle_icon, LV_OBJ_FLAG_HIDDEN);
    } else {
        lv_obj_add_flag(g_usb_app.ui.shuffle_icon, LV_OBJ_FLAG_HIDDEN);
    }
}

/**
 * @brief Update folder information display
 */
static void update_folder_info_display(void) {
    if (!g_usb_app.usb_status) return;
    
    // Update folder path (show last part only for space)
    if (g_usb_app.ui.folder_path_label) {
        const char *folder_path = g_usb_app.usb_status->folder_info.current_folder;
        const char *folder_name = strrchr(folder_path, '/');
        
        if (folder_name && folder_name[1] != '\0') {
            lv_label_set_text(g_usb_app.ui.folder_path_label, folder_name + 1);
        } else if (eLIBs_strlen(folder_path) > 0) {
            lv_label_set_text(g_usb_app.ui.folder_path_label, folder_path);
        } else {
            lv_label_set_text(g_usb_app.ui.folder_path_label, "Root");
        }
    }
    
    eLIBs_printf("[USB_APP] Folder info updated: %s (%d files)\n",
                 g_usb_app.usb_status->folder_info.current_folder,
                 g_usb_app.usb_status->folder_info.total_files);
}

/*
*********************************************************************************************************
*                                           SYNC IMPLEMENTATION
*********************************************************************************************************
*/

/**
 * @brief Perform full UI refresh (application switch or initialization)
 */
static void refresh_all_usb_ui(void) {
    if (!g_usb_app.usb_status) return;
    
    eLIBs_printf("[USB_APP] Full UI refresh\n");
    
    update_track_info_display();
    update_play_state_display(g_usb_app.usb_status->play_state);
    update_progress_display();
    update_volume_display(g_usb_app.usb_status->volume_level);
    update_repeat_mode_display(g_usb_app.usb_status->repeat_mode);
    update_shuffle_display(g_usb_app.usb_status->shuffle_mode);
    update_folder_info_display();
    
    g_usb_app.full_sync_count++;
}

/**
 * @brief Handle USB status sync message (optimized)
 */
__s32 app_handle_usb_sync_message(void) {
    if (!g_usb_app_initialized || !g_usb_app.app_active) {
        return EPDK_OK;  // App not active, ignore
    }
    
    g_usb_app.sync_count++;
    
    // Check if we need application switch synchronization
    if (SYNC_CHECK_APP_SWITCH(&g_usb_app.usb_status->sync, &g_usb_app.last_sequence)) {
        // Full synchronization (application switch)
        refresh_all_usb_ui();
        
        // Clear any pending changes to avoid duplicate processing
        SYNC_GET_CHANGES(&g_usb_app.usb_status->sync);
        return EPDK_OK;
    }
    
    // Incremental synchronization (normal operation)
    __u32 changes = SYNC_GET_CHANGES(&g_usb_app.usb_status->sync);
    if (changes == 0) {
        return EPDK_OK;  // No changes to process
    }
    
    eLIBs_printf("[USB_APP] Processing incremental changes: 0x%08X\n", changes);
    
    // Process each change type
    if (changes & USB_DATA_TRACK_INFO) {
        update_track_info_display();
        update_progress_display();  // Track info includes duration
    }
    
    if (changes & USB_DATA_PLAY_STATE) {
        update_play_state_display(g_usb_app.usb_status->play_state);
    }
    
    if (changes & USB_DATA_PROGRESS) {
        update_progress_display();
    }
    
    if (changes & USB_DATA_VOLUME) {
        update_volume_display(g_usb_app.usb_status->volume_level);
    }
    
    if (changes & USB_DATA_REPEAT_MODE) {
        update_repeat_mode_display(g_usb_app.usb_status->repeat_mode);
    }
    
    if (changes & USB_DATA_SHUFFLE) {
        update_shuffle_display(g_usb_app.usb_status->shuffle_mode);
    }
    
    if (changes & USB_DATA_FOLDER_INFO) {
        update_folder_info_display();
    }
    
    return EPDK_OK;
}

/*
*********************************************************************************************************
*                                           APPLICATION LIFECYCLE
*********************************************************************************************************
*/

/**
 * @brief Initialize USB Music Player application
 */
__s32 usb_app_init(void) {
    if (g_usb_app_initialized) return EPDK_OK;
    
    // Get USB status pointer from driver
    if (usb_get_status_optimized(&g_usb_app.usb_status) != EPDK_OK) {
        return EPDK_FAIL;
    }
    
    // Initialize application context
    g_usb_app.last_sequence = 0;
    g_usb_app.app_active = EPDK_FALSE;
    g_usb_app.sync_count = 0;
    g_usb_app.full_sync_count = 0;
    g_usb_app.current_source = ID_WORK_SOURCE_USB_DISK;
    
    // Initialize UI objects (would be done by UI creation code)
    eLIBs_memset(&g_usb_app.ui, 0, sizeof(g_usb_app.ui));
    
    // Register with message dispatch system
    msg_emit_register_app(dsk_get_music_app_handle(), "USB_MUSIC_APP", 
                         (1 << 1) | (1 << 3) | (1 << 4));  // USB + System + Power
    
    g_usb_app_initialized = EPDK_TRUE;
    
    eLIBs_printf("[USB_APP] Application initialized\n");
    return EPDK_OK;
}

/**
 * @brief Activate USB Music Player application (source switch)
 */
__s32 usb_app_activate(__u32 source_type) {
    if (!g_usb_app_initialized) {
        usb_app_init();
    }
    
    g_usb_app.app_active = EPDK_TRUE;
    g_usb_app.current_source = source_type;
    
    // Reset sequence tracking to force full sync on next message
    g_usb_app.last_sequence = 0;
    
    // Clear any pending changes
    if (g_usb_app.usb_status) {
        SYNC_GET_CHANGES(&g_usb_app.usb_status->sync);
    }
    
    // Perform immediate full sync
    refresh_all_usb_ui();
    if (g_usb_app.usb_status) {
        g_usb_app.last_sequence = g_usb_app.usb_status->sync.sequence_number;
    }
    
    eLIBs_printf("[USB_APP] Application activated for %s\n",
                 (source_type == ID_WORK_SOURCE_USB_DISK) ? "USB Disk" : "iPod");
    return EPDK_OK;
}

/**
 * @brief Deactivate USB Music Player application
 */
__s32 usb_app_deactivate(void) {
    g_usb_app.app_active = EPDK_FALSE;
    
    eLIBs_printf("[USB_APP] Application deactivated\n");
    return EPDK_OK;
}

/**
 * @brief USB Music Player application message handler
 */
__s32 usb_app_message_handler(__gui_msg_t *msg) {
    switch (msg->id) {
        case GUI_MSG_COMMAND: {
            __cmd_of_queue_t cmd = (__cmd_of_queue_t)msg->dwAddData1;
            
            switch (cmd) {
                case CMD_USB_STATUS_SYNC:
                    return app_handle_usb_sync_message();
                    
                case CMD_SYS_SOURCE: {
                    __u32 new_source = msg->dwAddData2;
                    if (new_source == ID_WORK_SOURCE_USB_DISK || 
                        new_source == ID_WORK_SOURCE_IPOD) {
                        return usb_app_activate(new_source);
                    } else {
                        return usb_app_deactivate();
                    }
                }
                
                case CMD_SYS_POWER:
                    // Handle power events if needed
                    break;
                    
                default:
                    break;
            }
            break;
        }
        
        default:
            break;
    }
    
    return EPDK_OK;
}

/*
*********************************************************************************************************
*                                           DEMO AND TESTING
*********************************************************************************************************
*/

/**
 * @brief Get USB application statistics
 */
void usb_app_get_statistics(void) {
    eLIBs_printf("\n=== USB Music Player Statistics ===\n");
    eLIBs_printf("Initialized: %s\n", g_usb_app_initialized ? "Yes" : "No");
    eLIBs_printf("Active: %s\n", g_usb_app.app_active ? "Yes" : "No");
    eLIBs_printf("Current source: %s\n", 
                 (g_usb_app.current_source == ID_WORK_SOURCE_USB_DISK) ? "USB Disk" : "iPod");
    eLIBs_printf("Total syncs: %d\n", g_usb_app.sync_count);
    eLIBs_printf("Full syncs: %d\n", g_usb_app.full_sync_count);
    eLIBs_printf("Last sequence: %d\n", g_usb_app.last_sequence);
    
    if (g_usb_app.usb_status) {
        eLIBs_printf("Current sequence: %d\n", g_usb_app.usb_status->sync.sequence_number);
        eLIBs_printf("Pending changes: 0x%08X\n", g_usb_app.usb_status->sync.dirty_mask_pending);
        eLIBs_printf("Current changes: 0x%08X\n", g_usb_app.usb_status->sync.dirty_mask_current);
    }
    
    eLIBs_printf("==================================\n\n");
}

/**
 * @brief Demo: Test USB application sync
 */
void demo_test_usb_app_sync(void) {
    eLIBs_printf("Testing USB Music Player synchronization...\n");
    
    // Initialize USB app
    usb_app_init();
    
    // Simulate source switch to USB
    usb_app_activate(ID_WORK_SOURCE_USB_DISK);
    
    // Simulate driver data changes
    demo_usb_track_change("Demo Song", "Demo Artist", 240);
    demo_usb_play_state_change(1);  // Play
    
    // Wait for sync processing
    esKRNL_TimeDly(50);
    
    // Simulate more changes
    demo_usb_track_change("Another Song", "Another Artist", 180);
    demo_usb_play_state_change(2);  // Pause
    
    // Simulate source switch away and back
    usb_app_deactivate();
    demo_usb_track_change("Third Song", "Third Artist", 200);
    usb_app_activate(ID_WORK_SOURCE_IPOD);  // Switch to iPod mode
    
    // Show statistics
    usb_app_get_statistics();
}