/*
*********************************************************************************************************
*                                              MELIS RTOS
*                     Driver Side FM Tuner Sync Demo - Dual-Buffer Optimization
*
*                                    (c) Copyright 2024, Sunny China
*                                             All Rights Reserved
*
* File    : driver_fm_sync_demo.c
* By      : Claude Code
* Version : v1.0
* Date    : 2024-12-XX
* Descript: Demo implementation of driver side FM tuner synchronization
* Update  : date                auther      ver     notes
*********************************************************************************************************
*/

#include "sync_core.h"
#include "../mcu_new_protocol_handler.h"

/*
*********************************************************************************************************
*                                           LOCAL DEFINITIONS
*********************************************************************************************************
*/

// FM Tuner Status with sync support
typedef struct {
    // Original FM data
    __u32 frequency;                        // Current frequency in KHz
    __u8  tuner_state;                      // Tuner state
    __u8  preset_number;                    // Current preset number
    __u8  stereo_status;                    // Stereo status
    __u8  scan_mode;                        // Scan mode
    __u16 signal_strength;                  // Signal strength
    
    // RDS data
    struct {
        char ps_name[9];                    // PS name (8 chars + null)
        char radio_text[65];                // Radio text (64 chars + null)
        __u8 pty_code;                      // PTY code
        __u8 af_status;                     // AF status
        __u8 ta_status;                     // TA status
    } rds_data;
    
    // Preset list
    struct {
        __u32 frequency;
        char  ps_name[9];
    } presets[18];                          // 18 presets max
    
    // Sync fields (only 12 bytes added)
    sync_base_t sync;
} fm_tuner_status_t;

/*
*********************************************************************************************************
*                                           LOCAL VARIABLES
*********************************************************************************************************
*/

// Global FM status
static fm_tuner_status_t g_fm_status;
static __bool g_fm_initialized = EPDK_FALSE;

/*
*********************************************************************************************************
*                                           HELPER FUNCTIONS
*********************************************************************************************************
*/

/**
 * @brief Initialize FM tuner status
 */
static void fm_status_init(void) {
    if (g_fm_initialized) return;
    
    // Initialize FM data to defaults
    g_fm_status.frequency = 87500;  // 87.5 MHz
    g_fm_status.tuner_state = 0;
    g_fm_status.preset_number = 0;
    g_fm_status.stereo_status = 0;
    g_fm_status.scan_mode = 0;
    g_fm_status.signal_strength = 0;
    
    eLIBs_memset(&g_fm_status.rds_data, 0, sizeof(g_fm_status.rds_data));
    eLIBs_memset(g_fm_status.presets, 0, sizeof(g_fm_status.presets));
    
    // Initialize sync fields
    SYNC_INIT_BASE(&g_fm_status.sync);
    
    g_fm_initialized = EPDK_TRUE;
}

/**
 * @brief Extract frequency from data buffer
 */
static __u32 extract_frequency(__u8 *data) {
    // Assume frequency is stored as 4-byte big-endian
    return ((__u32)data[0] << 24) | ((__u32)data[1] << 16) | 
           ((__u32)data[2] << 8) | data[3];
}

/**
 * @brief Update RDS PS name and check if changed
 */
static __bool update_rds_ps_name(__u8 *data) {
    char new_ps[9];
    eLIBs_memcpy(new_ps, data, 8);
    new_ps[8] = '\0';
    
    if (eLIBs_strcmp(g_fm_status.rds_data.ps_name, new_ps) != 0) {
        eLIBs_strcpy(g_fm_status.rds_data.ps_name, new_ps);
        return EPDK_TRUE;
    }
    return EPDK_FALSE;
}

/**
 * @brief Update RDS radio text and check if changed
 */
static __bool update_rds_radio_text(__u8 *data, __u16 len) {
    char new_rt[65];
    __u16 copy_len = (len > 64) ? 64 : len;
    
    eLIBs_memcpy(new_rt, data, copy_len);
    new_rt[copy_len] = '\0';
    
    if (eLIBs_strcmp(g_fm_status.rds_data.radio_text, new_rt) != 0) {
        eLIBs_strcpy(g_fm_status.rds_data.radio_text, new_rt);
        return EPDK_TRUE;
    }
    return EPDK_FALSE;
}

/**
 * @brief Update preset and check if changed
 */
static __bool update_preset(__u8 preset_idx, __u8 *data) {
    if (preset_idx >= 18) return EPDK_FALSE;
    
    __u32 freq = extract_frequency(data);
    char ps[9];
    eLIBs_memcpy(ps, data + 4, 8);
    ps[8] = '\0';
    
    if (g_fm_status.presets[preset_idx].frequency != freq ||
        eLIBs_strcmp(g_fm_status.presets[preset_idx].ps_name, ps) != 0) {
        g_fm_status.presets[preset_idx].frequency = freq;
        eLIBs_strcpy(g_fm_status.presets[preset_idx].ps_name, ps);
        return EPDK_TRUE;
    }
    return EPDK_FALSE;
}

/*
*********************************************************************************************************
*                                           MAIN HANDLER
*********************************************************************************************************
*/

/**
 * @brief FM Tuner Category Handler with optimized sync
 * @param operation Operation type (GET/SET/CONTROL/EVENT)
 * @param data_type Data type bitmap (reused as change mask)
 * @param data Data buffer
 * @param data_len Data length
 * @return EPDK_OK: success, others: error
 */
__s32 fm_tuner_handler_optimized(__u8 operation, __u32 data_type, __u8 *data, __u16 data_len) {
    __u32 local_changes = 0;  // Accumulate changes in this handler call
    __u16 data_offset = 0;
    
    // Initialize if needed
    fm_status_init();
    
    // Process different operations
    switch (operation) {
        case OPERATION_SET: {
            // Process each data type bit
            if (data_type & FM_DATA_FREQUENCY) {
                __u32 new_freq = extract_frequency(data + data_offset);
                if (g_fm_status.frequency != new_freq) {
                    g_fm_status.frequency = new_freq;
                    local_changes |= FM_DATA_FREQUENCY;
                }
                data_offset += 4;
            }
            
            if (data_type & FM_DATA_TUNER_STATE) {
                __u8 new_state = data[data_offset];
                if (g_fm_status.tuner_state != new_state) {
                    g_fm_status.tuner_state = new_state;
                    local_changes |= FM_DATA_TUNER_STATE;
                }
                data_offset += 1;
            }
            
            if (data_type & FM_DATA_RDS_PS) {
                if (update_rds_ps_name(data + data_offset)) {
                    local_changes |= FM_DATA_RDS_PS;
                }
                data_offset += 8;
            }
            
            if (data_type & FM_DATA_RDS_RT) {
                __u16 rt_len = (data_len - data_offset > 64) ? 64 : (data_len - data_offset);
                if (update_rds_radio_text(data + data_offset, rt_len)) {
                    local_changes |= FM_DATA_RDS_RT;
                }
                data_offset += rt_len;
            }
            
            if (data_type & FM_DATA_PRESETS) {
                __u8 preset_idx = data[data_offset++];
                if (update_preset(preset_idx, data + data_offset)) {
                    local_changes |= FM_DATA_PRESETS;
                }
                data_offset += 12; // 4 bytes freq + 8 bytes PS
            }
            
            if (data_type & FM_DATA_SCAN_MODE) {
                __u8 new_scan_mode = data[data_offset];
                if (g_fm_status.scan_mode != new_scan_mode) {
                    g_fm_status.scan_mode = new_scan_mode;
                    local_changes |= FM_DATA_SCAN_MODE;
                }
                data_offset += 1;
            }
            
            if (data_type & FM_DATA_STEREO) {
                __u8 new_stereo = data[data_offset];
                if (g_fm_status.stereo_status != new_stereo) {
                    g_fm_status.stereo_status = new_stereo;
                    local_changes |= FM_DATA_STEREO;
                }
                data_offset += 1;
            }
            
            if (data_type & FM_DATA_SIGNAL) {
                __u16 new_signal = ((__u16)data[data_offset] << 8) | data[data_offset + 1];
                if (g_fm_status.signal_strength != new_signal) {
                    g_fm_status.signal_strength = new_signal;
                    local_changes |= FM_DATA_SIGNAL;
                }
                data_offset += 2;
            }
            
            break;
        }
        
        case OPERATION_GET: {
            // Handle GET operations if needed
            // For demo, we just return current status
            break;
        }
        
        case OPERATION_EVENT: {
            // Handle EVENT operations (similar to SET but from different source)
            // Use same logic as SET case
            break;
        }
        
        default:
            return EPDK_FAIL;
    }
    
    // Batch commit changes at the end of handler
    if (local_changes != 0) {
        // Mark all changes in current buffer
        SYNC_MARK_CHANGED(&g_fm_status.sync, local_changes);
        
        // Commit changes and push message if needed
        SYNC_COMMIT_AND_PUSH(&g_fm_status.sync, CMD_FM_STATUS_SYNC);
    }
    
    return EPDK_OK;
}

/*
*********************************************************************************************************
*                                           PUBLIC INTERFACE
*********************************************************************************************************
*/

/**
 * @brief Get FM status (for application use)
 */
__s32 fm_get_status_optimized(fm_tuner_status_t **status) {
    if (!g_fm_initialized) {
        fm_status_init();
    }
    
    *status = &g_fm_status;
    return EPDK_OK;
}

/**
 * @brief Register FM tuner handler
 */
__s32 fm_tuner_sync_demo_register(void) {
    // Register with protocol handler
    unified_cmd_handler_t fm_handler = {
        .category = CATEGORY_FM,
        .name = "FM_TUNER_SYNC_DEMO",
        .main_handler = fm_tuner_handler_optimized
    };
    
    return mcu_new_protocol_register_category_handler(&fm_handler);
}

/*
*********************************************************************************************************
*                                           DEMO USAGE EXAMPLE
*********************************************************************************************************
*/

/**
 * @brief Demo: Simulate MCU sending FM frequency change
 */
void demo_fm_frequency_change(__u32 new_frequency) {
    __u8 data[4];
    
    // Pack frequency as big-endian
    data[0] = (new_frequency >> 24) & 0xFF;
    data[1] = (new_frequency >> 16) & 0xFF;
    data[2] = (new_frequency >> 8) & 0xFF;
    data[3] = new_frequency & 0xFF;
    
    // Call handler (simulating protocol reception)
    fm_tuner_handler_optimized(OPERATION_SET, FM_DATA_FREQUENCY, data, 4);
}

/**
 * @brief Demo: Simulate MCU sending RDS PS name update
 */
void demo_fm_rds_ps_update(const char *ps_name) {
    __u8 data[8];
    
    // Pack PS name (8 chars, pad with spaces)
    eLIBs_memset(data, ' ', 8);
    __u16 len = eLIBs_strlen(ps_name);
    if (len > 8) len = 8;
    eLIBs_memcpy(data, ps_name, len);
    
    // Call handler
    fm_tuner_handler_optimized(OPERATION_SET, FM_DATA_RDS_PS, data, 8);
}