/*
*********************************************************************************************************
*                                              MELIS RTOS
*                      Message Queue Deduplication Demo - Optimized Queue Management
*
*                                    (c) Copyright 2024, Sunny China
*                                             All Rights Reserved
*
* File    : message_queue_dedup_demo.c
* By      : Claude Code
* Version : v1.0
* Date    : 2024-12-XX
* Descript: Demo implementation of message queue deduplication mechanism
* Update  : date                auther      ver     notes
*********************************************************************************************************
*/

#include "sync_core.h"
#include <elibs.h>

/*
*********************************************************************************************************
*                                           LOCAL DEFINITIONS
*********************************************************************************************************
*/

// Message deduplication info
typedef struct {
    __cmd_of_queue_t cmd;                   // Command type
    __u32 last_push_time;                   // Last push timestamp
    __u32 dedup_window_ms;                  // Deduplication window in ms
    __bool pending_flag;                    // Pending push flag
    __u32 push_count;                       // Total push count (statistics)
    __u32 dedup_count;                      // Deduplicated count (statistics)
} cmd_dedup_info_t;

// Enhanced message queue entry
typedef struct msg_queue_entry {
    __cmd_of_queue_t cmd;                   // Command
    __u32 timestamp;                        // Timestamp when queued
    __u32 priority;                         // Priority (0=highest)
    struct msg_queue_entry *next;           // Next entry
} msg_queue_entry_t;

// Message queue control
typedef struct {
    msg_queue_entry_t *head;                // Queue head
    msg_queue_entry_t *tail;                // Queue tail
    __u32 count;                            // Current queue size
    __u32 max_size;                         // Maximum queue size
    __hdle queue_mutex;                     // Queue protection mutex
    __hdle queue_sem;                       // Queue semaphore
    __u32 total_messages;                   // Total messages processed
    __u32 dropped_messages;                 // Dropped due to full queue
} msg_queue_ctrl_t;

/*
*********************************************************************************************************
*                                           LOCAL VARIABLES
*********************************************************************************************************
*/

// Deduplication table for different commands
static cmd_dedup_info_t g_dedup_table[] = {
    // High frequency, can be deduplicated
    {CMD_FM_STATUS_SYNC,      0, 50,  EPDK_FALSE, 0, 0},   // 50ms window
    {CMD_USB_STATUS_SYNC,     0, 30,  EPDK_FALSE, 0, 0},   // 30ms window  
    {CMD_SETTINGS_STATUS_SYNC,0, 100, EPDK_FALSE, 0, 0},   // 100ms window
    
    // Low frequency or critical, no deduplication  
    {CMD_SYS_SOURCE,          0, 0,   EPDK_FALSE, 0, 0},   // No dedup
    {CMD_SYS_POWER,           0, 0,   EPDK_FALSE, 0, 0},   // No dedup
};

static const __u32 g_dedup_table_size = sizeof(g_dedup_table) / sizeof(cmd_dedup_info_t);

// Message queue control
static msg_queue_ctrl_t g_msg_queue = {0};
static __bool g_queue_initialized = EPDK_FALSE;

// Timer for deferred push
static __hdle g_dedup_timer = NULL;

/*
*********************************************************************************************************
*                                           HELPER FUNCTIONS
*********************************************************************************************************
*/

/**
 * @brief Find deduplication info for command
 */
static cmd_dedup_info_t* find_dedup_info(__cmd_of_queue_t cmd) {
    __u32 i;
    for (i = 0; i < g_dedup_table_size; i++) {
        if (g_dedup_table[i].cmd == cmd) {
            return &g_dedup_table[i];
        }
    }
    return NULL;
}

/**
 * @brief Initialize message queue
 */
static __s32 msg_queue_init(void) {
    if (g_queue_initialized) return EPDK_OK;
    
    g_msg_queue.head = NULL;
    g_msg_queue.tail = NULL;
    g_msg_queue.count = 0;
    g_msg_queue.max_size = 64;  // Maximum 64 pending messages
    g_msg_queue.total_messages = 0;
    g_msg_queue.dropped_messages = 0;
    
    // Create mutex for queue protection
    g_msg_queue.queue_mutex = esKRNL_MutexCreate(KRNL_MUTEX_PRIO_INHERIT);
    if (!g_msg_queue.queue_mutex) {
        return EPDK_FAIL;
    }
    
    // Create semaphore for queue notification
    g_msg_queue.queue_sem = esKRNL_SemCreate(0);
    if (!g_msg_queue.queue_sem) {
        esKRNL_MutexDel(g_msg_queue.queue_mutex, KRNL_DEL_ALWAYS, NULL);
        return EPDK_FAIL;
    }
    
    g_queue_initialized = EPDK_TRUE;
    return EPDK_OK;
}

/**
 * @brief Allocate queue entry
 */
static msg_queue_entry_t* alloc_queue_entry(__cmd_of_queue_t cmd, __u32 priority) {
    msg_queue_entry_t *entry = (msg_queue_entry_t*)esMEMS_Malloc(0, sizeof(msg_queue_entry_t));
    if (entry) {
        entry->cmd = cmd;
        entry->timestamp = esKRNL_TimeGet();
        entry->priority = priority;
        entry->next = NULL;
    }
    return entry;
}

/**
 * @brief Free queue entry
 */
static void free_queue_entry(msg_queue_entry_t *entry) {
    if (entry) {
        esMEMS_Mfree(0, entry);
    }
}

/**
 * @brief Insert entry into queue (priority-based)
 */
static __s32 insert_queue_entry(msg_queue_entry_t *entry) {
    if (g_msg_queue.count >= g_msg_queue.max_size) {
        g_msg_queue.dropped_messages++;
        return EPDK_FAIL;  // Queue full
    }
    
    // Insert based on priority (0 = highest priority)
    if (!g_msg_queue.head || entry->priority < g_msg_queue.head->priority) {
        // Insert at head
        entry->next = g_msg_queue.head;
        g_msg_queue.head = entry;
        if (!g_msg_queue.tail) {
            g_msg_queue.tail = entry;
        }
    } else {
        // Find insertion point
        msg_queue_entry_t *prev = g_msg_queue.head;
        msg_queue_entry_t *curr = prev->next;
        
        while (curr && curr->priority <= entry->priority) {
            prev = curr;
            curr = curr->next;
        }
        
        entry->next = curr;
        prev->next = entry;
        
        if (!curr) {
            g_msg_queue.tail = entry;
        }
    }
    
    g_msg_queue.count++;
    g_msg_queue.total_messages++;
    
    return EPDK_OK;
}

/**
 * @brief Remove duplicate entries for the same command
 */
static void remove_duplicate_entries(__cmd_of_queue_t cmd) {
    msg_queue_entry_t *prev = NULL;
    msg_queue_entry_t *curr = g_msg_queue.head;
    
    while (curr) {
        if (curr->cmd == cmd) {
            // Remove this entry
            if (prev) {
                prev->next = curr->next;
            } else {
                g_msg_queue.head = curr->next;
            }
            
            if (curr == g_msg_queue.tail) {
                g_msg_queue.tail = prev;
            }
            
            msg_queue_entry_t *to_free = curr;
            curr = curr->next;
            free_queue_entry(to_free);
            g_msg_queue.count--;
        } else {
            prev = curr;
            curr = curr->next;
        }
    }
}

/**
 * @brief Timer callback for deferred push
 */
static void dedup_timer_callback(__hdle timer, void *parg) {
    __u32 current_time = esKRNL_TimeGet();
    __u32 i;
    
    // Check all pending dedup entries
    for (i = 0; i < g_dedup_table_size; i++) {
        cmd_dedup_info_t *info = &g_dedup_table[i];
        
        if (info->pending_flag && info->dedup_window_ms > 0) {
            if ((current_time - info->last_push_time) >= info->dedup_window_ms) {
                // Time to push the message
                mcu_push_cmd_to_queue_direct(info->cmd);
                info->pending_flag = EPDK_FALSE;
                info->last_push_time = current_time;
            }
        }
    }
}

/*
*********************************************************************************************************
*                                           OPTIMIZED QUEUE INTERFACE
*********************************************************************************************************
*/

/**
 * @brief Optimized message push with deduplication
 * @param cmd Command to push
 * @return EPDK_OK: success, others: error
 */
__s32 mcu_push_cmd_to_queue_optimized(__cmd_of_queue_t cmd) {
    __u32 current_time = esKRNL_TimeGet();
    cmd_dedup_info_t *dedup_info = find_dedup_info(cmd);
    
    if (!g_queue_initialized) {
        msg_queue_init();
    }
    
    if (dedup_info) {
        dedup_info->push_count++;
        
        if (dedup_info->dedup_window_ms == 0) {
            // No deduplication, push immediately
            return mcu_push_cmd_to_queue_direct(cmd);
        }
        
        // Check deduplication window
        __u32 time_since_last = current_time - dedup_info->last_push_time;
        
        if (time_since_last >= dedup_info->dedup_window_ms) {
            // Push immediately
            dedup_info->last_push_time = current_time;
            dedup_info->pending_flag = EPDK_FALSE;
            return mcu_push_cmd_to_queue_direct(cmd);
        } else {
            // Within dedup window
            if (!dedup_info->pending_flag) {
                // Set pending flag and start timer if needed
                dedup_info->pending_flag = EPDK_TRUE;
                
                if (!g_dedup_timer) {
                    // Create timer for deferred push (10ms tick)
                    g_dedup_timer = esKRNL_TimerCreate(KRNL_TIMER_AUTO, 10, 
                                                       dedup_timer_callback, NULL);
                    if (g_dedup_timer) {
                        esKRNL_TimerStart(g_dedup_timer);
                    }
                }
            }
            
            dedup_info->dedup_count++;
            return EPDK_OK;  // Deduplicated
        }
    }
    
    // No dedup info, push directly
    return mcu_push_cmd_to_queue_direct(cmd);
}

/**
 * @brief Direct message push (internal)
 */
__s32 mcu_push_cmd_to_queue_direct(__cmd_of_queue_t cmd) {
    __s32 ret = EPDK_FAIL;
    __u32 priority = 1;  // Default priority
    
    if (!g_queue_initialized) {
        msg_queue_init();
    }
    
    // Determine priority based on command type
    switch (cmd) {
        case CMD_SYS_POWER:
        case CMD_SYS_SOURCE:
            priority = 0;  // Highest priority
            break;
        case CMD_FM_STATUS_SYNC:
        case CMD_USB_STATUS_SYNC:
        case CMD_SETTINGS_STATUS_SYNC:
            priority = 2;  // Lower priority for status sync
            break;
        default:
            priority = 1;  // Normal priority
            break;
    }
    
    // Acquire queue mutex
    if (esKRNL_MutexAccept(g_msg_queue.queue_mutex) != KRNL_MUTEX_ACCEPT_OK) {
        esKRNL_MutexPend(g_msg_queue.queue_mutex, 0, NULL);
    }
    
    // Remove duplicates for status sync commands to prevent accumulation
    if (cmd == CMD_FM_STATUS_SYNC || cmd == CMD_USB_STATUS_SYNC || 
        cmd == CMD_SETTINGS_STATUS_SYNC) {
        remove_duplicate_entries(cmd);
    }
    
    // Allocate and insert new entry
    msg_queue_entry_t *entry = alloc_queue_entry(cmd, priority);
    if (entry) {
        if (insert_queue_entry(entry) == EPDK_OK) {
            ret = EPDK_OK;
            esKRNL_SemPost(g_msg_queue.queue_sem);  // Notify queue consumer
        } else {
            free_queue_entry(entry);
        }
    }
    
    esKRNL_MutexPost(g_msg_queue.queue_mutex);
    
    return ret;
}

/**
 * @brief Get next message from queue
 */
__s32 mcu_get_next_queue_message(__cmd_of_queue_t *cmd, __u32 timeout_ms) {
    __s32 ret = EPDK_FAIL;
    
    if (!g_queue_initialized || !cmd) {
        return EPDK_FAIL;
    }
    
    // Wait for message availability
    if (esKRNL_SemPend(g_msg_queue.queue_sem, timeout_ms, NULL) != KRNL_SEM_PEND_OK) {
        return EPDK_FAIL;  // Timeout
    }
    
    // Acquire queue mutex
    esKRNL_MutexPend(g_msg_queue.queue_mutex, 0, NULL);
    
    if (g_msg_queue.head) {
        msg_queue_entry_t *entry = g_msg_queue.head;
        *cmd = entry->cmd;
        
        g_msg_queue.head = entry->next;
        if (!g_msg_queue.head) {
            g_msg_queue.tail = NULL;
        }
        
        g_msg_queue.count--;
        free_queue_entry(entry);
        
        ret = EPDK_OK;
    }
    
    esKRNL_MutexPost(g_msg_queue.queue_mutex);
    
    return ret;
}

/*
*********************************************************************************************************
*                                           STATISTICS AND DEBUG
*********************************************************************************************************
*/

/**
 * @brief Get queue statistics
 */
void mcu_queue_get_statistics(void) {
    __u32 i;
    
    if (!g_queue_initialized) return;
    
    eLIBs_printf("\n=== Message Queue Statistics ===\n");
    eLIBs_printf("Current queue size: %d/%d\n", g_msg_queue.count, g_msg_queue.max_size);
    eLIBs_printf("Total messages: %d\n", g_msg_queue.total_messages);
    eLIBs_printf("Dropped messages: %d\n", g_msg_queue.dropped_messages);
    
    eLIBs_printf("\n=== Deduplication Statistics ===\n");
    for (i = 0; i < g_dedup_table_size; i++) {
        cmd_dedup_info_t *info = &g_dedup_table[i];
        eLIBs_printf("CMD 0x%04X: Push=%d, Dedup=%d, Window=%dms\n", 
                     info->cmd, info->push_count, info->dedup_count, 
                     info->dedup_window_ms);
    }
    eLIBs_printf("================================\n\n");
}

/**
 * @brief Configure deduplication window for a command
 */
__s32 mcu_configure_dedup_window(__cmd_of_queue_t cmd, __u32 window_ms) {
    cmd_dedup_info_t *info = find_dedup_info(cmd);
    if (info) {
        info->dedup_window_ms = window_ms;
        return EPDK_OK;
    }
    return EPDK_FAIL;
}

/*
*********************************************************************************************************
*                                           DEMO USAGE
*********************************************************************************************************
*/

/**
 * @brief Demo: Test deduplication mechanism
 */
void demo_test_deduplication(void) {
    __u32 i;
    
    eLIBs_printf("Testing deduplication mechanism...\n");
    
    // Push multiple FM status sync messages rapidly
    for (i = 0; i < 10; i++) {
        mcu_push_cmd_to_queue_optimized(CMD_FM_STATUS_SYNC);
        esKRNL_TimeDly(5);  // 5ms delay
    }
    
    // Push critical messages (should not be deduplicated)
    for (i = 0; i < 3; i++) {
        mcu_push_cmd_to_queue_optimized(CMD_SYS_SOURCE);
        esKRNL_TimeDly(1);
    }
    
    // Wait for dedup timer to process
    esKRNL_TimeDly(100);
    
    mcu_queue_get_statistics();
}