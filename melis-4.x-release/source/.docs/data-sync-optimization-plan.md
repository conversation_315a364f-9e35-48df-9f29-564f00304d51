## 精简高效的数据同步优化策略（基于前置条件）

### 前置条件
- **handler线程化**: `mcu_*_cmd_handler` 在线程中执行，可使用短临界区或轻量锁；无需考虑 ISR 禁止阻塞。
- **消息队列无负载**: 系统消息只带 CMD，不承载数据或掩码。
- **可去重**: 各 CMD 有实时性要求，但允许窗口内合并去重。

### 目标
- **最小改动**: 不改变现有消息流，只在原有数据结构和 handler 中增少量代码。
- **精准同步**: 只同步变化的数据，减少无效 UI 刷新和跨层调用。
- **可控实时性**: 通过每 CMD 的去重窗口精细控制延迟，默认 0ms。

### 核心理念
- **单一事实来源**: 仅在各 category 状态结构中维护变更，不再维护第二套全局变更表。
- **无负载消息**: 消息只传 CMD；变更掩码由应用侧在驱动结构里拉取并清空。
- **提交/清除语义**: 写端累积到 `current_changes`，投递前提交到 `pending_changes`；应用读取使用“交换清零”避免丢失。

### 数据结构最小扩展
在各 category 的状态结构（如 `fm_tuner_status_t`, `usb_media_status_t`）中新增：

```c
volatile __u32 current_changes;   // handler 内累积的变更
volatile __u32 pending_changes;   // 待应用读取的变更
__u32 last_change_time;           // 可选：节流/统计
__u32 data_seq;                   // 可选：一致性快照序列
__hdle change_mutex;              // 可选：快照加锁（线程上下文）
```

> 注：在仅按位掩码的路径上优先使用原子按位或/交换以减少锁竞争；当需要跨字段一致性（切换快照）时再用短临界区。

### 通用内联接口（驱动侧）
```c
void mark_changed(volatile __u32 *current_changes, __u32 mask);
__u32 commit_changes(volatile __u32 *current_changes, volatile __u32 *pending_changes);
__u32 get_and_clear_changes(volatile __u32 *pending_changes);
```

语义：
- `mark_changed`：在值确实变化时 OR 指定位到 `current_changes`。
- `commit_changes`：在安全上下文将 `current_changes` 提交并清零，返回提交前的 `current_changes` 用于是否投递判断。
- `get_and_clear_changes`：应用侧一次性获取并清空 `pending_changes`。

### Handler 集成（每个 handler 的最小改动）
1) 在解析并确认“值确实变化”后：
```c
mark_changed(&status->current_changes, CHANGED_MASK_BITS);
```
2) 在 handler 尾部或合适的聚合点：
```c
__u32 new_bits = commit_changes(&status->current_changes, &status->pending_changes);
if (new_bits != 0) { maybe_push_cmd_with_dedup(cmd); }
```

### 队列去重/合并（无负载）
- 每个 CMD 维护：`last_push_tick`、`pending_flag`、`dedup_window_ms`。
- 策略：
  - `dedup_window_ms == 0`：检测到新变更立即投递（严格实时）。
  - `dedup_window_ms > 0`：设置合并窗口；窗口内只保证“至少一次”投递。到期若对应 category 的 `pending_changes != 0` 则投递一次并清 `pending_flag`。
- 推荐配置：
  - 严格实时：声源切换、播放状态 → 0ms。
  - 高频可合并：播放时间、RDS 文本、信号强度 → 30–100ms。
- 定时器懒加载：仅当某 CMD 配置窗口时，首次需要时创建并启用。

### 应用侧精准同步
应用接收到 CMD：
```c
__u32 mask = get_and_clear_*_changes(&status->pending_changes);
if (mask == 0) return EPDK_OK; // 无需要同步内容
return sync_*_changed_data(mask); // 按掩码精准刷新 UI/状态
```
可选失败恢复（默认关闭）：`restore_*_changes(mask)` 将失败位 OR 回 `pending_changes`。

### 应用切换快速同步（一次性一致快照）
- 方案A（推荐起步）：加锁复制
  - `lock(status->change_mutex) → memcpy 整个 category 状态到局部 → unlock`。
- 方案B（后续优化）：无锁双读校验
  - 读 `data_seq → memcpy → 再读 data_seq` 一致则稳定，否则重试一次。

### 并发与一致性
- 变更掩码路径：用原子按位或/交换，无锁快路径；跨字段一致性用短临界区。
- 读取清除使用“交换清零”，确保不丢变更。
- 仅保留“category 内的 pending/current”一套变更记录，避免双轨不一致。

### 渐进式落地顺序
1. 仅添加 `current_changes/pending_changes` 与三内联函数；handler 增量 OR 与 `commit_changes`；应用侧拉取掩码；全部 CMD 的去重窗口设为 0。
2. 为高频 CMD 配置 `dedup_window_ms` 并启用延迟投递计时。
3. 上线切换快照；视需要引入失败恢复/统计。

### 配置与接口建议
```c
// 去重窗口配置（毫秒；0 表示关闭去重）
void cmd_set_dedup_window(__cmd_of_queue_t cmd, __u32 window_ms);

// 应用消息处理统一入口
__s32 app_process_sync_message(__cmd_of_queue_t cmd);

// 应用切换同步接口
__s32 app_switch_sync_fm_data(void);
__s32 app_switch_sync_usb_data(void);
__s32 app_switch_sync_bt_data(void);
```

### 成功判据与指标
- 平均 UI 刷新次数下降（对比基线端到端统计）。
- 无空消息投递；重复消息显著减少。
- 应用切换耗时下降；实时 CMD 不超时窗。


