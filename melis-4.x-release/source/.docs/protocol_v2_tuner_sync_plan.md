# 协议v2 Tuner 数据同步实施规划（面向 ui_fm）

- 适用范围：新协议 V2 在调谐器类目（FM/AM/WB）与 Tuner 应用（`ui_fm`）的端到端数据同步闭环验证。
- 排除项：`docs/` 目录下的历史设计草稿，如 `docs/data_sync_optimization.h`、`docs/protocol_handler_optimized.h`（已废弃，不纳入实现）。

## 1. 目标与原则

- 目标：建立 EVENT → 同步结构 → 队列去重通知 → `ui_fm` 精准刷新 → 同步状态清理 的闭环。
- 原则：
  - 仅处理“变更位”对应的 UI 更新，避免全量刷新。
  - 去抖合并，降低 UI 抖动。
  - 消费后及时清理同步状态，防止后续事件被去重抑制。

## 2. 现状摘要（关键文件）

- 同步框架定义：`emodules/drv_mcu_uart/core/handlers/protocol/mcu_protocol_sync.h`
- 调谐器接入：`emodules/drv_mcu_uart/core/handlers/protocol/tuner/mcu_tuner_category_handler.c/.h`
  - 典型流程：比较旧值生成 `local_changes` → `MCU_PROTOCOL_SYNC_MARK` → `MCU_PROTOCOL_SYNC_COMMIT` → 若 `MCU_PROTOCOL_SYNC_NEED_QUEUE` 则 `mcu_push_cmd_to_queue(CMD_NEW_TUNER_UPDATE)`
  - 初始化：`MCU_PROTOCOL_SYNC_INIT(&g_*_status->sync)`
- 队列 API：
  - 声明：`emodules/drv_mcu_uart/core/mcu_interrupt_process.h` → `mcu_push_cmd_to_queue(__cmd_of_queue_t)`
  - 实现：`emodules/drv_mcu_uart/core/mcu_interrupt_process.c`
- 命令常量：`CMD_NEW_TUNER_UPDATE` 已在 `emodules/drv_mcu_uart/core/uart_cmd_set.h` 的 `__cmd_of_queue_t` 中定义。

## 3. 端到端流程（期望闭环）

1) MCU → Host：Tuner EVENT 到达  
2) 驱动 Tuner handler：标记变更、提交、必要时入队 `CMD_NEW_TUNER_UPDATE`  
3) `ui_fm`：收到命令  
4) `ui_fm`：一次性读取“已提交变更位”，只刷新对应 UI/状态  
5) `ui_fm`：刷新完成后清理“已提交变更位”并复位队列相关标志（通过 `mcu_protocol_sync.h` 提供的接口/内联）  
6) 记录日志与指标

## 4. ui_fm 集成步骤（重点）

- 统一入口：在处理 `CMD_NEW_TUNER_UPDATE` 的分支集中执行同步消费，避免多入口导致并发竞态。
- 读取变更位：从 `g_fm_status/g_am_status/g_wb_status` 的 `sync` 成员读取“已提交变更掩码”（使用 `mcu_protocol_sync.h` 提供的访问/内联函数，避免直接操作内部字段）。
- 按位刷新（示例）：
  - FM 常见位：
    - `FM_DATA_FREQUENCY` → 频率显示
    - `FM_DATA_TUNER_STATE` → 搜台/锁定状态
    - `FM_DATA_PRESET_NUMBER` → 预置选中
    - `FM_DATA_SCAN_MODE` → 搜台动画/模式
    - `FM_DATA_STEREO_STATUS` → 立体声指示
    - `FM_DATA_LOCAL_SEEK` → 本地搜台开关
    - `FM_DATA_TUNING_STEP` → 步进显示
    - `FM_DATA_RDS_*`（如 PS、RT、PTY、AF/TA 开关与状态）→ 文本与台标信息
    - `FM_DATA_PRESET_LIST` → 预置列表刷新
  - AM/WB 同理，按各自枚举位映射到 UI。
- 清理与复位：UI 刷新完成后，调用同步框架提供的“清空已提交变更位 / 复位队列标志”的方法，避免后续事件被抑制。
- UI 节流（建议）：UI 层设置最小刷新间隔（如 50–100ms），窗口内合并处理多次 `CMD_NEW_TUNER_UPDATE`。
- 线程模型：保持在 UI/消息线程中消费，不在中断上下文直接操作 UI。
- 异常处理：未识别掩码位 → 告警日志；字段校验失败（如频率越界）→ 错误日志并跳过刷新。

## 5. 字段→UI 映射表（示意）

- `FM_DATA_FREQUENCY` → 频率数字/频道行（WB 频道可从频率派生）
- `FM_DATA_RDS_PS` → 电台名
- `FM_DATA_RT_TEXT` → 节目信息/台文本
- `FM_DATA_PRESET_LIST` → 预置栏（可结合 PS 展示）
- `FM_DATA_SCAN_MODE` / `FM_DATA_TUNER_STATE` → 搜台动画/状态栏
- `FM_DATA_STEREO_STATUS` / `FM_DATA_LOCAL_SEEK` / `FM_DATA_TUNING_STEP` → 功能图标/菜单项

仅刷新被置位的条目，避免全量重绘。

## 6. 日志与指标（先规范，后完善）

- 驱动侧（参考现有日志）：提交掩码、是否入队、已在队列则跳过入队。
- `ui_fm`：
  - 收到 `CMD_NEW_TUNER_UPDATE` 时间戳
  - 读取到的掩码位（十六进制）
  - 刷新耗时（开始/结束）
  - 清理/复位结果
- 指标（可选）：每秒事件数、平均掩码位数、丢弃/重复率。

## 7. 测试计划

- 单元（模拟注入）：
  - 单字段变更：频率/RDS/步进等
  - 多字段同到：频率+RDS+列表
  - 重复相同值：不应触发 UI 刷新
- 集成（串口驱动通路）：
  - 高频 RDS 更新 → UI 不抖动，节流有效
  - 预置列表大批量变化
  - 扫描模式切换过程的 UI 同步
- 压测：事件风暴下的队列长度、去重有效性、UI 卡顿评估
- 回归：频率显示、预置选中、RDS 高频更新等基础功能无回退

## 8. 风险与回滚

- 风险：
  - App 未清理同步结构 → 后续事件被去重抑制
  - UI 过度刷新 → 抖动/卡顿
  - 共享状态并发访问 → 数据竞态
- 回滚预案：
  - 固化统一消费与清理流程
  - UI 侧引入轻量节流
  - 必要时临时回退到老的“消息粒度直推”策略

## 9. 后续扩展（仅规划，不立刻执行）

- 横向推广到 `audio` 类目：定义 `CMD_NEW_AUDIO_UPDATE`，复用相同闭环。
- 统一日志/指标，评估不同类目体验与负载。
- 视情况抽象通用消费工具，减少 UI 端重复代码。
