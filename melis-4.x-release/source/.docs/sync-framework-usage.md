# Protocol v2 Sync Framework 使用指南

## 概述
Protocol v2 Sync框架提供了一种高效、无竞态的数据同步机制，用于MCU UART驱动与应用层之间的数据变更通知。

## 关键特性
1. **消息去重**: 避免队列中重复消息
2. **无竞态条件**: 使用简单位操作避免并发问题
3. **变更累积**: 多次变更合并为一次通知
4. **轻量级**: 仅16字节内存开销

## 架构设计

### 数据结构
```c
typedef struct {
    volatile __u32 dirty_mask_current;  // 驱动层写入的当前变更
    volatile __u32 dirty_mask_pending;  // 等待应用层处理的累积变更
    volatile __u32 sequence_number;     // 序列号，用于检测应用切换
    volatile __u8  msg_in_queue;        // 消息队列标志，防止重复入队
    __u8 reserved[3];                   // 对齐保留
} mcu_protocol_sync_t;
```

### 工作流程

#### 驱动层（数据更新方）
1. 检测到数据变化时，调用`MCU_PROTOCOL_SYNC_MARK`标记变更
2. 调用`MCU_PROTOCOL_SYNC_COMMIT`将变更提交到pending缓冲区
3. 调用`MCU_PROTOCOL_SYNC_NEED_QUEUE`检查是否需要入队
4. 如果需要入队，调用`mcu_push_cmd_to_queue`发送通知

#### 应用层（数据消费方）
1. 收到队列消息后，调用`MCU_PROTOCOL_SYNC_PROCESS`获取并清除变更
2. 根据返回的变更位图，更新对应的UI组件
3. 处理完成后，队列标志自动清除，允许新消息入队

## 使用示例

### 驱动层代码示例
```c
// FM处理器接收到数据更新
__s32 fm_main_handler(__u8 operation, __u32 data_type, __u8 *data, __u16 data_len)
{
    __u32 local_changes = 0;
    
    // 处理频率变更
    if (data_type & FM_DATA_FREQUENCY) {
        __u32 old_freq = g_fm_status->frequency;
        // ... 更新频率数据 ...
        if (g_fm_status->frequency != old_freq) {
            local_changes |= FM_DATA_FREQUENCY;
        }
    }
    
    // 处理RDS数据变更
    if (data_type & FM_DATA_RDS_PS) {
        char old_ps[9];
        memcpy(old_ps, g_fm_status->rds_data.ps_name, 9);
        // ... 更新RDS数据 ...
        if (strcmp(g_fm_status->rds_data.ps_name, old_ps) != 0) {
            local_changes |= FM_DATA_RDS_PS;
        }
    }
    
    // 提交变更并发送通知（带去重）
    if (local_changes != 0 && operation == OPERATION_EVENT) {
        MCU_PROTOCOL_SYNC_MARK(&g_fm_status->sync, local_changes);
        __u32 committed = MCU_PROTOCOL_SYNC_COMMIT(&g_fm_status->sync);
        
        if (committed != 0 && MCU_PROTOCOL_SYNC_NEED_QUEUE(&g_fm_status->sync)) {
            mcu_push_cmd_to_queue(CMD_NEW_TUNER_FREQUENCY);
            TUNER_DBG("FM sync: changes=0x%08X queued\n", committed);
        } else if (committed != 0) {
            TUNER_DBG("FM sync: changes=0x%08X accumulated (already in queue)\n", committed);
        }
    }
    
    return EPDK_OK;
}
```

### 应用层代码示例
```c
// 处理队列消息
void handle_tuner_update_message(void)
{
    // 获取并清除所有累积的变更
    __u32 changes = MCU_PROTOCOL_SYNC_PROCESS(&g_fm_status->sync);
    
    if (changes == 0) {
        return;  // 没有变更
    }
    
    // 根据变更位图更新UI
    if (changes & FM_DATA_FREQUENCY) {
        update_frequency_display(g_fm_status->frequency);
    }
    
    if (changes & FM_DATA_RDS_PS) {
        update_rds_ps_display(g_fm_status->rds_data.ps_name);
    }
    
    if (changes & FM_DATA_STEREO_STATUS) {
        update_stereo_icon(g_fm_status->stereo_status);
    }
    
    // ... 处理其他变更 ...
    
    // 注意：队列标志已自动清除，允许新消息入队
}
```

## 关键优势

### 1. 消息去重
- 当数据快速更新时，避免队列中堆积相同的通知消息
- 多次变更累积为一次处理，提高效率

### 2. 无竞态条件
- 使用简单的位操作，避免复杂的原子操作
- 驱动层和应用层各自操作不同的缓冲区

### 3. 灵活的变更跟踪
- 32位变更位图，可跟踪最多32种数据类型
- 应用层可精确知道哪些数据发生了变化

### 4. 应用切换检测
- 序列号机制可检测应用切换
- 切换后可执行完整同步

## 注意事项

1. **初始化**: 必须在注册处理器时调用`MCU_PROTOCOL_SYNC_INIT`
2. **变更检测**: 只有实际发生变化的数据才应标记为changed
3. **队列处理**: 应用层必须调用`MCU_PROTOCOL_SYNC_PROCESS`来清除队列标志
4. **数据一致性**: 读取数据前应先处理sync变更

## 扩展到其他Category

要将此框架应用到其他category（如USB、BT、Settings等），只需：

1. 在状态结构中添加sync字段：
```c
typedef struct {
    // ... 现有字段 ...
    mcu_protocol_sync_t sync;  // 添加sync字段
} category_status_t;
```

2. 在注册时初始化：
```c
MCU_PROTOCOL_SYNC_INIT(&g_category_status->sync);
```

3. 在处理器中跟踪和提交变更：
```c
if (local_changes != 0 && operation == OPERATION_EVENT) {
    MCU_PROTOCOL_SYNC_MARK(&g_category_status->sync, local_changes);
    __u32 committed = MCU_PROTOCOL_SYNC_COMMIT(&g_category_status->sync);
    
    if (committed != 0 && MCU_PROTOCOL_SYNC_NEED_QUEUE(&g_category_status->sync)) {
        mcu_push_cmd_to_queue(CMD_NEW_CATEGORY_UPDATE);
    }
}
```

4. 在应用层处理消息：
```c
__u32 changes = MCU_PROTOCOL_SYNC_PROCESS(&g_category_status->sync);
// 根据changes更新UI
```