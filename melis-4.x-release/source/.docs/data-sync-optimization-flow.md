## 数据同步优化流程图（Mermaid）

### 驱动侧：变更标记 → 提交 → 去重投递

```mermaid
graph TD
  subgraph DriverThread[Driver Thread]
    A[解析 MCU 数据] --> B{值是否变化?}
    B -- 否 --> Z1[结束]
    B -- 是 --> C[mark_changed(current_changes, mask)]
    C --> D[commit_changes → 提交到 pending_changes]
    D --> E{new_bits != 0?}
    E -- 否 --> Z1
    E -- 是 --> F{dedup_window_ms > 0?}
    F -- 否/0ms --> G[立即 mcu_push_cmd_to_queue(cmd)]
    F -- 是 --> H[pending_flag=1; 启动/重置定时器]
    H --> I[定时器到期: 若 pending_changes!=0 则 push(cmd)]
  end
```

### 应用侧：按 CMD 拉取掩码精准同步

```mermaid
graph TD
  subgraph AppThread[App Thread]
    J[收到消息: cmd] --> K[get_and_clear_*_changes(pending_changes)]
    K --> L{mask == 0?}
    L -- 是 --> Z2[返回 OK]
    L -- 否 --> M[sync_*_changed_data(mask)] --> Z2
  end
```

### 应用切换：一致性快照（加锁复制）

```mermaid
graph TD
  S[App 切换入口] --> T[lock(change_mutex)]
  T --> U[memcpy 整个 category 状态至本地快照]
  U --> V[unlock(change_mutex)]
  V --> W[使用快照刷新 UI/状态]
```

### 端到端时序示意

```mermaid
sequenceDiagram
  participant H as Handler
  participant Q as MsgQueue
  participant A as App

  H->>H: 解析指令并比较旧值/新值
  H->>H: mark_changed(mask)
  H->>H: commit_changes()
  alt 有新变更
    H->>Q: push(cmd)
    Q-->>A: cmd
    A->>A: mask = get_and_clear_changes()
    A->>A: sync_*_changed_data(mask)
  else 无新变更
    H-->>H: 不投递消息
  end
```


