# MCU Protocol v2 Application Layer Communication Implementation Guide

## 📋 Overview

This document provides a comprehensive guide for implementing MCU Protocol v2 communication in applications, based on the successfully implemented Tuner (ui_fm) application. It covers the complete communication architecture including data synchronization, data retrieval, and control operations between driver layer and application layer.

## 🎯 Communication Architecture Summary

### Driver Layer to Application Layer Communication Flow

```
MCU UART Driver (Protocol v2 Handler)
    ↓ Receives 0xF100 protocol packets
Category Handler (tuner/audio/usb/bt/settings)
    ↓ Processes OPERATION_EVENT messages
    ↓ Updates local data structures
    ↓ Marks changes: MCU_PROTOCOL_SYNC_MARK()
    ↓ Commits changes: MCU_PROTOCOL_SYNC_COMMIT()
Message Queue System
    ↓ Pushes CMD_NEW_XXX_UPDATE to queue
    ↓ Converts to GUI_MSG_UI_NEW_XXX_UPDATE
msg_emit Dispatch
    ↓ Sends GUI_MSG_UI_UPDATE (dwAddData1=GUI_MSG_UI_NEW_XXX_UPDATE)
Application ui_update_proc Function
    ↓ Processes case GUI_MSG_UI_NEW_XXX_UPDATE
    ↓ Calls UI_MCU_SYNC_PROCESS() to get and clear pending changes
    ↓ Calls mcu_ops.sync_dirty() for data synchronization
    ↓ Updates UI based on changed_mask
```

## 🚀 Three Core Communication Methods

Based on the implemented Tuner application, there are three primary communication methods:

### 1. **Data Synchronization (Event-Driven)**
- **Direction**: Driver → Application
- **Trigger**: MCU sends data updates via OPERATION_EVENT
- **Implementation**: UI_MCU_SYNC_PROCESS() + mcu_ops.sync_dirty()

### 2. **Data Retrieval (Request-Response)**
- **Direction**: Application → Driver → MCU
- **Trigger**: Application requests specific data
- **Implementation**: mcu_ops.get_data()

### 3. **Control Operations (Command)**
- **Direction**: Application → Driver → MCU
- **Trigger**: User interactions or application logic
- **Implementation**: mcu_ops.set_control()

## 📚 Tuner Application Implementation Template

### Foundation: Application Layer Sync Macros

**File**: `livedesk/leopard/applets/apps/lib/source/ui_source_communication_hub.h`

```c
/* ===================== Application Layer MCU Sync Macros ===================== */

/**
 * @brief Application layer MCU sync processing macro
 * Function: Read pending changes, clear pending and queue flags
 * Usage: __u32 changes = UI_MCU_SYNC_PROCESS(&sync_ptr);
 */
#define UI_MCU_SYNC_PROCESS(sync_ptr) ({ \
    __u32 changes = (sync_ptr)->dirty_mask_pending; \
    (sync_ptr)->dirty_mask_pending = 0; \
    (sync_ptr)->msg_in_queue = 0; \
    changes; \
})

/**
 * @brief Application layer MCU update message unified processing macro
 * Function: Process MCU sync + call application's sync_dirty interface
 * Usage: UI_MCU_HANDLE_UPDATE(&sync_ptr, app_para->mcu_ops, app_para);
 */
#define UI_MCU_HANDLE_UPDATE(sync_ptr, mcu_ops, ctx) do { \
    __u32 changes = UI_MCU_SYNC_PROCESS(sync_ptr); \
    if (changes != 0 && (mcu_ops).sync_dirty) { \
        (mcu_ops).sync_dirty((ctx), changes); \
    } \
} while(0)

/**
 * @brief Check application switch macro (optional usage)
 * Usage: if (UI_MCU_CHECK_SWITCH(&sync_ptr, &last_seq)) { full_sync(); }
 */
#define UI_MCU_CHECK_SWITCH(sync_ptr, last_seq_ptr) ({ \
    __bool need_sync = EPDK_FALSE; \
    if ((sync_ptr)->sequence_number != *(last_seq_ptr)) { \
        *(last_seq_ptr) = (sync_ptr)->sequence_number; \
        need_sync = EPDK_TRUE; \
    } \
    need_sync; \
})
```

### Core Implementation: MCU Interface Structure

**File**: `livedesk/leopard/applets/apps/lib/source/ui_source_communication_hub.h`

```c
/**
 * @brief MCU data operations unified interface - 4 core functions
 */
typedef struct mcu_data_ops {
    // Data synchronization functions
    __s32 (*sync_all)(void *ctx);                                 // Full data sync
    __s32 (*sync_dirty)(void *ctx, __u32 dirty_mask);           // Incremental sync
    // Data retrieval function
    __s32 (*get_data)(__u32 data_type, void *param);           // Get data (with optional param)
    // Data setting/control function
    __s32 (*set_control)(__u32 data_type, void *data, __u16 data_len); // Set/control data
} mcu_data_ops_t;
```

### Template Implementation: Message Processing

**File**: `livedesk/leopard/applets/apps/fm/ui_fm.c` (Lines 2263-2297)

```c
case GUI_MSG_UI_NEW_TUNER_UPDATE:
{
    // Step 1: Error handling for null pointers
    if (!sys_para || !tuner_para || !fm_para) {
        break;
    }
    
    // Step 2: Select appropriate sync pointer based on current source/state
    mcu_protocol_sync_t *sync_ptr = NULL;
    if(sys_para->g_sys_para.current_source == SYS_SOURCE_AM)
        sync_ptr = &tuner_para->g_am_para.sync;
    else if(sys_para->g_sys_para.current_source == SYS_SOURCE_WB)
        sync_ptr = &tuner_para->g_wb_para.sync;
    else
        sync_ptr = &tuner_para->g_fm_para.sync;
    
    // Step 3: Process MCU Protocol v2 data synchronization first
    __u32 changed_mask = 0;
    if (fm_para->mcu_ops.sync_dirty) {
        changed_mask = UI_MCU_SYNC_PROCESS(sync_ptr);  // Get and clear pending
        if (changed_mask != 0) {
            __fm_msg("MCU Tuner Update: source=%d, mask=0x%08X\n", 
                    sys_para->g_sys_para.current_source, changed_mask);
            fm_para->mcu_ops.sync_dirty(fm_para, changed_mask);
        }
    }
    
    // Step 4: Early exit if no changes
    if (!changed_mask)
        break;

    // Step 5: Update UI based on specific change_mask bits
    // Example: Frequency update
    if(changed_mask & FM_DATA_FREQUENCY) {
        // Update frequency display
        ui_fm_update_frequency_display(fm_para);
        changed_mask &= (~FM_DATA_FREQUENCY);
    }
    
    // Example: RDS PS name update
    if(changed_mask & FM_DATA_RDS_PS) {
        // Update RDS PS display
        ui_fm_update_rds_ps_display(fm_para);
        changed_mask &= (~FM_DATA_RDS_PS);
    }
    
    // Handle additional data types...
}
break;
```

### Template Implementation: Application Initialization

**File**: `livedesk/leopard/applets/apps/fm/ui_fm.c` (ui_fm_init function)

```c
void ui_fm_init(void * para)
{
    if(!ui_fm_para) {
        ui_fm_para = (ui_fm_para_t *)esMEMS_Balloc(sizeof(ui_fm_para_t));
        eLIBs_memset((void*)ui_fm_para, 0, sizeof(ui_fm_para_t));

        ui_fm_internal_para_update(ui_fm_para);
        
        // Map MCU data operations interface
        ui_fm_para->mcu_ops.sync_all = ui_mcu_tuner_sync_all;
        ui_fm_para->mcu_ops.sync_dirty = ui_mcu_tuner_sync_dirty;
        ui_fm_para->mcu_ops.get_data = ui_mcu_tuner_get_data;
        ui_fm_para->mcu_ops.set_control = ui_mcu_tuner_set_control;
        
        // Execute initial full data sync
        if (ui_fm_para->mcu_ops.sync_all) {
            ui_fm_para->mcu_ops.sync_all(ui_fm_para);
        }
        
        // Continue with UI initialization...
    }
}
```

## 🛠️ Step-by-Step Implementation Guide for Other Applications

### Step 1: Identify Application Categories and Data Types

For each application, identify the corresponding MCU Protocol v2 categories:

| Application | Category Code | Data Types | Sync Structure |
|-------------|---------------|------------|----------------|
| **USB DISK** | 0x20 | Track info, play state, file list | usb_disk_para.sync |
| **USB iPod** | 0x21 | Track info, play state, library | usb_ipod_para.sync |
| **BT Audio** | 0x30 | Track info, play state, device info | bt_audio_para.sync |
| **BT Phone** | 0x31 | Call state, contacts, call history | bt_phone_para.sync |
| **BT Party** | 0x33 | Device list, connection state | bt_party_para.sync |
| **SXM** | 0x14 | Channel info, preset list | sxm_para.sync |
| **Settings** | 0x03 | System settings, user preferences | settings_para.sync |

### Step 2: Create MCU Interface Functions

For each application, implement the 4 core interface functions:

```c
// Example for USB DISK application
__s32 ui_mcu_usb_disk_sync_all(void *ctx);
__s32 ui_mcu_usb_disk_sync_dirty(void *ctx, __u32 dirty_mask);
__s32 ui_mcu_usb_disk_get_data(__u32 data_type, void *param);
__s32 ui_mcu_usb_disk_set_control(__u32 data_type, void *data, __u16 data_len);
```

### Step 3: Add MCU Interface to Application Parameter Structure

```c
// Example for USB application
typedef struct usb_para {
    // Original USB data preserved
    ui_usb_data_para_t ui_usb_data_para;
    ui_source_para_t * source_para;
    
    // MCU data operations interface
    mcu_data_ops_t mcu_ops;
} ui_usb_para_t;
```

### Step 4: Implement Message Processing Case

Add the message processing case in the application's `ui_update_proc` function:

```c
case GUI_MSG_UI_NEW_USB_UPDATE:
{
    // Step 1: Error handling
    if (!sys_para || !usb_para || !app_para) {
        break;
    }
    
    // Step 2: Select sync pointer based on USB type
    mcu_protocol_sync_t *sync_ptr = NULL;
    if (sys_para->g_sys_para.current_usb_type == USB_TYPE_IPOD)
        sync_ptr = &usb_para->g_ipod_para.sync;
    else
        sync_ptr = &usb_para->g_disk_para.sync;
    
    // Step 3: Process MCU synchronization
    __u32 changed_mask = 0;
    if (app_para->mcu_ops.sync_dirty) {
        changed_mask = UI_MCU_SYNC_PROCESS(sync_ptr);
        if (changed_mask != 0) {
            __usb_msg("MCU USB Update: type=%d, mask=0x%08X\n", 
                     sys_para->g_sys_para.current_usb_type, changed_mask);
            app_para->mcu_ops.sync_dirty(app_para, changed_mask);
        }
    }
    
    // Step 4: Early exit check
    if (!changed_mask)
        break;

    // Step 5: UI updates based on changed_mask
    if (changed_mask & USB_DATA_TRACK_INFO) {
        ui_usb_update_track_display(app_para);
        changed_mask &= (~USB_DATA_TRACK_INFO);
    }
    
    if (changed_mask & USB_DATA_PLAY_STATE) {
        ui_usb_update_play_state_display(app_para);
        changed_mask &= (~USB_DATA_PLAY_STATE);
    }
    
    // Handle additional USB data types...
}
break;
```

### Step 5: Initialize MCU Interface in Application Init

```c
void ui_usb_init(void * para)
{
    if(!ui_usb_para) {
        ui_usb_para = (ui_usb_para_t *)esMEMS_Balloc(sizeof(ui_usb_para_t));
        eLIBs_memset((void*)ui_usb_para, 0, sizeof(ui_usb_para_t));

        // Map MCU data operations interface
        ui_usb_para->mcu_ops.sync_all = ui_mcu_usb_sync_all;
        ui_usb_para->mcu_ops.sync_dirty = ui_mcu_usb_sync_dirty;
        ui_usb_para->mcu_ops.get_data = ui_mcu_usb_get_data;
        ui_usb_para->mcu_ops.set_control = ui_mcu_usb_set_control;
        
        // Execute initial full data sync
        if (ui_usb_para->mcu_ops.sync_all) {
            ui_usb_para->mcu_ops.sync_all(ui_usb_para);
        }
        
        // Continue with UI initialization...
    }
}
```

## 📝 Application-Specific Implementation Examples

### USB DISK Application

**Data Types** (Based on Protocol v2 specification):
```c
typedef enum {
    USB_DISK_DATA_TRACK_INFO     = 0x00000001,  // Current track information
    USB_DISK_DATA_PLAY_STATE     = 0x00000002,  // Play/pause/stop state
    USB_DISK_DATA_PLAY_TIME      = 0x00000004,  // Current play time
    USB_DISK_DATA_TOTAL_TIME     = 0x00000008,  // Total track time
    USB_DISK_DATA_REPEAT_MODE    = 0x00000010,  // Repeat mode
    USB_DISK_DATA_SHUFFLE_MODE   = 0x00000020,  // Shuffle mode
    USB_DISK_DATA_FILE_LIST      = 0x00000040,  // File list/folder structure
    USB_DISK_DATA_DEVICE_INFO    = 0x00000080,  // Device information
} usb_disk_data_type_t;
```

**Message Processing**:
```c
case GUI_MSG_UI_NEW_USB_UPDATE:
{
    if (!sys_para || !usb_para || !usb_app_para) {
        break;
    }
    
    mcu_protocol_sync_t *sync_ptr = &usb_para->g_disk_para.sync;
    
    __u32 changed_mask = 0;
    if (usb_app_para->mcu_ops.sync_dirty) {
        changed_mask = UI_MCU_SYNC_PROCESS(sync_ptr);
        if (changed_mask != 0) {
            __usb_msg("MCU USB DISK Update: mask=0x%08X\n", changed_mask);
            usb_app_para->mcu_ops.sync_dirty(usb_app_para, changed_mask);
        }
    }
    
    if (!changed_mask) break;

    // Handle track info changes
    if (changed_mask & USB_DISK_DATA_TRACK_INFO) {
        ui_usb_update_track_info_display(usb_app_para);
        changed_mask &= (~USB_DISK_DATA_TRACK_INFO);
    }
    
    // Handle play state changes  
    if (changed_mask & USB_DISK_DATA_PLAY_STATE) {
        ui_usb_update_play_controls(usb_app_para);
        changed_mask &= (~USB_DISK_DATA_PLAY_STATE);
    }
}
break;
```

### BT Audio Application

**Data Types**:
```c
typedef enum {
    BT_AUDIO_DATA_TRACK_INFO     = 0x00000001,  // AVRCP track information
    BT_AUDIO_DATA_PLAY_STATE     = 0x00000002,  // Play/pause state
    BT_AUDIO_DATA_DEVICE_NAME    = 0x00000004,  // Connected device name
    BT_AUDIO_DATA_CONNECTION     = 0x00000008,  // Connection status
    BT_AUDIO_DATA_VOLUME         = 0x00000010,  // Audio volume level
    BT_AUDIO_DATA_CODEC_INFO     = 0x00000020,  // Audio codec information
} bt_audio_data_type_t;
```

**Message Processing**:
```c
case GUI_MSG_UI_NEW_BT_AUDIO_UPDATE:
{
    if (!sys_para || !bt_para || !bt_app_para) {
        break;
    }
    
    mcu_protocol_sync_t *sync_ptr = &bt_para->g_audio_para.sync;
    
    __u32 changed_mask = 0;
    if (bt_app_para->mcu_ops.sync_dirty) {
        changed_mask = UI_MCU_SYNC_PROCESS(sync_ptr);
        if (changed_mask != 0) {
            __bt_msg("MCU BT Audio Update: mask=0x%08X\n", changed_mask);
            bt_app_para->mcu_ops.sync_dirty(bt_app_para, changed_mask);
        }
    }
    
    if (!changed_mask) break;

    // Handle connection status changes
    if (changed_mask & BT_AUDIO_DATA_CONNECTION) {
        ui_bt_update_connection_status(bt_app_para);
        changed_mask &= (~BT_AUDIO_DATA_CONNECTION);
    }
    
    // Handle track metadata changes
    if (changed_mask & BT_AUDIO_DATA_TRACK_INFO) {
        ui_bt_update_track_metadata(bt_app_para);
        changed_mask &= (~BT_AUDIO_DATA_TRACK_INFO);
    }
}
break;
```

### Settings Application

**Data Types**:
```c
typedef enum {
    SETTINGS_DATA_SYSTEM_INFO    = 0x00000001,  // System information
    SETTINGS_DATA_AUDIO_SETTINGS = 0x00000002,  // Audio settings
    SETTINGS_DATA_DISPLAY_SETTINGS = 0x00000004, // Display settings
    SETTINGS_DATA_NETWORK_CONFIG = 0x00000008,  // Network configuration
    SETTINGS_DATA_USER_PREFERENCES = 0x00000010, // User preferences
} settings_data_type_t;
```

**Message Processing**:
```c
case GUI_MSG_UI_NEW_SETTINGS_UPDATE:
{
    if (!sys_para || !settings_para || !settings_app_para) {
        break;
    }
    
    mcu_protocol_sync_t *sync_ptr = &settings_para->sync;
    
    __u32 changed_mask = 0;
    if (settings_app_para->mcu_ops.sync_dirty) {
        changed_mask = UI_MCU_SYNC_PROCESS(sync_ptr);
        if (changed_mask != 0) {
            __settings_msg("MCU Settings Update: mask=0x%08X\n", changed_mask);
            settings_app_para->mcu_ops.sync_dirty(settings_app_para, changed_mask);
        }
    }
    
    if (!changed_mask) break;

    // Handle audio settings changes
    if (changed_mask & SETTINGS_DATA_AUDIO_SETTINGS) {
        ui_settings_update_audio_page(settings_app_para);
        changed_mask &= (~SETTINGS_DATA_AUDIO_SETTINGS);
    }
    
    // Handle display settings changes
    if (changed_mask & SETTINGS_DATA_DISPLAY_SETTINGS) {
        ui_settings_update_display_page(settings_app_para);
        changed_mask &= (~SETTINGS_DATA_DISPLAY_SETTINGS);
    }
}
break;
```

## 🔧 Best Practices and Design Principles

### 1. **Error Handling**
- Always check for null pointers before accessing parameters
- Validate sync_ptr before calling UI_MCU_SYNC_PROCESS
- Handle edge cases gracefully

### 2. **Performance Optimization**
- Use UI_MCU_SYNC_PROCESS to get and clear pending changes in one operation
- Early exit when changed_mask is 0
- Process only the specific data types that changed

### 3. **Debugging Support**
- Add debug messages with application name and change mask
- Use consistent debug macro naming (__app_msg format)
- Include relevant context information (source type, device type, etc.)

### 4. **Code Maintainability**
- Follow the established 5-step pattern for message processing
- Use clear, descriptive comments in English
- Maintain consistent variable naming conventions
- Group related UI updates together

### 5. **Multi-Source/Multi-Mode Support**
- Select appropriate sync pointer based on current application state
- Handle different modes or device types within the same application
- Provide clear debug information about which mode/type is active

## 📋 Implementation Checklist

### For Each New Application:
- [ ] Define application-specific data types enum
- [ ] Implement 4 MCU interface functions (sync_all, sync_dirty, get_data, set_control)
- [ ] Add mcu_data_ops_t to application parameter structure
- [ ] Add GUI_MSG_UI_NEW_XXX_UPDATE case in ui_update_proc function
- [ ] Initialize MCU interface in application init function
- [ ] Test data synchronization, retrieval, and control operations
- [ ] Add appropriate debug messages and error handling
- [ ] Verify UI updates work correctly for all data types

## 🎯 Conclusion

This implementation guide provides a proven, standardized approach for implementing MCU Protocol v2 communication in applications. The Tuner application serves as a comprehensive template that demonstrates all three core communication methods: data synchronization, data retrieval, and control operations.

By following this guide, developers can ensure consistent, reliable, and maintainable MCU communication across all applications in the Melis RTOS platform.