# MCU Protocol v2 Application Layer Communication Implementation Plan

## 📋 Project Overview

This document provides the standardized implementation plan for application layer communication with MCU Protocol v2 in Melis RTOS. Based on comprehensive analysis of the complete data flow from MCU UART driver to applications, this plan addresses current FM application synchronization issues and prepares for system-wide deployment.

### Core Design Principles
- Maintain existing message processing architecture unchanged
- Achieve layer decoupling through application-layer macro definitions
- Minimize code modifications while maximizing deployment efficiency
- Ensure high-quality, concise implementation

## 🔍 Problem Analysis Summary

### 1. Identified Core Issues
- **FM Application Sync Error**: Empty `GUI_MSG_UI_NEW_TUNER_UPDATE` case at `ui_fm.c:2262` lacks MCU synchronization handling
- **Architecture Misunderstanding**: Message processing occurs in `ui_fm_ui_update_proc`, not in `ui_fm_events.c`
- **Sync Mechanism Error**: Application layer must process `dirty_mask_pending` and `msg_in_queue`, not `dirty_mask_current`

### 2. Correct Message Flow Understanding
```
MCU UART Driver (Protocol v2 Handler)
    ↓ Push CMD_NEW_TUNER_UPDATE to queue
Message Queue System  
    ↓ Convert to GUI_MSG_UI_NEW_TUNER_UPDATE
msg_emit Dispatch
    ↓ Send GUI_MSG_UI_UPDATE (dwAddData1=GUI_MSG_UI_NEW_TUNER_UPDATE)
ui_event_fm_screen
    ↓ Receive LV_EVENT_MSG_RECEIVED
ui_fm_ui_update_proc
    ↓ switch(msg->dwAddData1)
    ↓ case GUI_MSG_UI_NEW_TUNER_UPDATE: ← **Correct processing location**
MCU Sync Processing
```

### 3. Synchronization Logic Analysis
```c
// Driver Layer (Automatic): current -> pending
dirty_mask_current |= changes;          // Mark changes
dirty_mask_pending |= dirty_mask_current; // Commit to pending
dirty_mask_current = 0;                  // Clear current
msg_in_queue = 1;                        // Mark message queued

// Application Layer (Manual): Process pending
changes = dirty_mask_pending;            // Read pending changes
dirty_mask_pending = 0;                  // Clear pending
msg_in_queue = 0;                        // Clear queue flag
```

## 🎯 Implementation Plan

### Phase 1: Application Layer Sync Macro Definitions (Infrastructure)

#### File Location
`/home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/source/livedesk/leopard/applets/apps/lib/source/ui_source_communication_hub.h`

#### Specific Modifications
Add the following code at the end of the file:

```c
/* ===================== Application Layer MCU Sync Macros ===================== */

/**
 * @brief Application layer MCU sync processing macro
 * Function: Read pending changes, clear pending and queue flags
 * Implementation: Direct copy of MCU_PROTOCOL_SYNC_PROCESS logic, avoiding driver header inclusion
 * Usage: __u32 changes = UI_MCU_SYNC_PROCESS(&reg_app_para->tuner_para.sync);
 */
#define UI_MCU_SYNC_PROCESS(sync_ptr) ({ \
    __u32 changes = (sync_ptr)->dirty_mask_pending; \
    (sync_ptr)->dirty_mask_pending = 0; \
    (sync_ptr)->msg_in_queue = 0; \
    changes; \
})

/**
 * @brief Application layer MCU update message unified processing macro
 * Function: Process MCU sync + call application's sync_dirty interface
 * Usage: UI_MCU_HANDLE_UPDATE(&reg_app_para->tuner_para.sync, app_para->mcu_ops, app_para);
 */
#define UI_MCU_HANDLE_UPDATE(sync_ptr, mcu_ops, ctx) do { \
    __u32 changes = UI_MCU_SYNC_PROCESS(sync_ptr); \
    if (changes != 0 && (mcu_ops).sync_dirty) { \
        (mcu_ops).sync_dirty((ctx), changes); \
    } \
} while(0)

/**
 * @brief Check application switch macro (optional usage)
 * Function: Detect sequence_number changes, determine if full sync needed
 * Usage: if (UI_MCU_CHECK_SWITCH(&reg_app_para->tuner_para.sync, &last_seq)) { full_sync(); }
 */
#define UI_MCU_CHECK_SWITCH(sync_ptr, last_seq_ptr) ({ \
    __bool need_sync = EPDK_FALSE; \
    if ((sync_ptr)->sequence_number != *(last_seq_ptr)) { \
        *(last_seq_ptr) = (sync_ptr)->sequence_number; \
        need_sync = EPDK_TRUE; \
    } \
    need_sync; \
})
```

### Phase 2: Fix FM Application Sync Processing (Core Fix)

#### File Location
`/home/<USER>/mlleexz/PMX-30/PMX30-0722/melis-v3.0/melis-4.x-release/source/livedesk/leopard/applets/apps/fm/ui_fm.c`

#### Specific Modifications
**Modification Location**: Lines 2263-2297

**Current Code**:
```c
case GUI_MSG_UI_NEW_TUNER_UPDATE:

	break;
```

**Modified To (Optimized Implementation)**:
```c
case GUI_MSG_UI_NEW_TUNER_UPDATE:
{
    // Add error handling for null pointers
    if (!sys_para || !tuner_para || !fm_para) {
        break;
    }
    
    // Select appropriate sync pointer based on current source (FM/AM/WB)
    mcu_protocol_sync_t *sync_ptr = NULL;
    if(sys_para->g_sys_para.current_source == SYS_SOURCE_AM)
        sync_ptr = &tuner_para->g_am_para.sync;
    else if(sys_para->g_sys_para.current_source == SYS_SOURCE_WB)
        sync_ptr = &tuner_para->g_wb_para.sync;
    else
        sync_ptr = &tuner_para->g_fm_para.sync;
    
    // Process MCU Protocol v2 Tuner data synchronization first
    __u32 changed_mask = 0;
    if (fm_para->mcu_ops.sync_dirty) {
        changed_mask = UI_MCU_SYNC_PROCESS(sync_ptr);  // Get and clear pending
        if (changed_mask != 0) {
            __fm_msg("MCU Tuner Update: source=%d, mask=0x%08X\n", 
                    sys_para->g_sys_para.current_source, changed_mask);
            fm_para->mcu_ops.sync_dirty(fm_para, changed_mask);
        }
    }
    
    if (!changed_mask)
        break;

    // Update UI by change_mask
    // ... specific UI update logic for each data type
}
break;
```

### Phase 3: FM Application Switch Detection (Optional Optimization)

#### Status: Not Implemented
**Reason**: The current implementation with proper source-based sync pointer selection and immediate UI updates provides sufficient functionality without requiring application switch detection.

**Note**: This phase was determined to be unnecessary in the current architecture as the FM application correctly handles multi-source (FM/AM/WB) synchronization through direct sync pointer selection based on `current_source`.

## 🚀 Deployment Template for Other Applications

### Deployment Strategy
1. **Identify existing UI_UPDATE processing functions**
2. **Add corresponding MCU sync cases**
3. **Use standardized macro processing**

### Standard Application Adaptation Template (Based on Actual FM Implementation)

#### Music Application Adaptation Example
```c
// Add in music application's ui_update_proc function
case GUI_MSG_UI_NEW_AUDIO_UPDATE:
{
    // Add error handling for null pointers
    if (!sys_para || !audio_para || !music_para) {
        break;
    }
    
    // Select appropriate sync pointer (if multiple audio sources exist)
    mcu_protocol_sync_t *sync_ptr = &audio_para->sync;
    
    // Process MCU Protocol v2 Audio data synchronization first
    __u32 changed_mask = 0;
    if (music_para->mcu_ops.sync_dirty) {
        changed_mask = UI_MCU_SYNC_PROCESS(sync_ptr);  // Get and clear pending
        if (changed_mask != 0) {
            __music_msg("MCU Audio Update: mask=0x%08X\n", changed_mask);
            music_para->mcu_ops.sync_dirty(music_para, changed_mask);
        }
    }
    
    if (!changed_mask)
        break;

    // Update UI by change_mask
    // ... specific UI update logic for each audio data type
}
break;
```

#### Bluetooth Application Adaptation Example
```c
// Add in bluetooth application's ui_update_proc function
case GUI_MSG_UI_NEW_BT_UPDATE:
{
    // Add error handling for null pointers
    if (!sys_para || !bt_para || !bluetooth_para) {
        break;
    }
    
    // Select appropriate sync pointer based on BT mode (if applicable)
    mcu_protocol_sync_t *sync_ptr = &bt_para->sync;
    
    // Process MCU Protocol v2 Bluetooth data synchronization first
    __u32 changed_mask = 0;
    if (bluetooth_para->mcu_ops.sync_dirty) {
        changed_mask = UI_MCU_SYNC_PROCESS(sync_ptr);  // Get and clear pending
        if (changed_mask != 0) {
            __bt_msg("MCU BT Update: mask=0x%08X\n", changed_mask);
            bluetooth_para->mcu_ops.sync_dirty(bluetooth_para, changed_mask);
        }
    }
    
    if (!changed_mask)
        break;

    // Update UI by change_mask
    // ... specific UI update logic for each BT data type
}
break;
```

#### USB Application Adaptation Example
```c
// Add in USB application's ui_update_proc function
case GUI_MSG_UI_NEW_USB_UPDATE:
{
    // Add error handling for null pointers
    if (!sys_para || !usb_para || !usb_app_para) {
        break;
    }
    
    // Select appropriate sync pointer based on USB type (Disk/iPod)
    mcu_protocol_sync_t *sync_ptr = NULL;
    if (sys_para->g_sys_para.current_usb_type == USB_TYPE_IPOD)
        sync_ptr = &usb_para->g_ipod_para.sync;
    else
        sync_ptr = &usb_para->g_disk_para.sync;
    
    // Process MCU Protocol v2 USB data synchronization first
    __u32 changed_mask = 0;
    if (usb_app_para->mcu_ops.sync_dirty) {
        changed_mask = UI_MCU_SYNC_PROCESS(sync_ptr);  // Get and clear pending
        if (changed_mask != 0) {
            __usb_msg("MCU USB Update: type=%d, mask=0x%08X\n", 
                     sys_para->g_sys_para.current_usb_type, changed_mask);
            usb_app_para->mcu_ops.sync_dirty(usb_app_para, changed_mask);
        }
    }
    
    if (!changed_mask)
        break;

    // Update UI by change_mask
    // ... specific UI update logic for each USB data type
}
break;
```

## 📝 Implementation Checklist

### ✅ Phase 1 Completion Criteria
- [ ] Added 3 macro definitions in `ui_source_communication_hub.h`
- [ ] Compilation without errors, correct macro syntax
- [ ] Correct include paths and dependencies

### ✅ Phase 2 Completion Criteria  
- [ ] Added MCU sync processing at FM application `ui_fm.c:2262`
- [ ] Compilation without errors, correct linking
- [ ] Runtime FM application correctly responds to MCU messages

### ✅ Phase 3 Completion Criteria
- [ ] Added switch detection in FM application initialization
- [ ] Application switching triggers full sync
- [ ] Testing shows normal data sync when switching to FM application

### ✅ Deployment Completion Criteria
- [ ] Identified other applications requiring adaptation
- [ ] Each application has added corresponding MCU sync processing
- [ ] All applications' MCU communication functions normally

## 🔧 Technical Details

### 1. Macro Definition Design Rationale
- **Layer Decoupling**: Application layer doesn't directly include driver layer headers
- **Performance Optimization**: Macro expansion to inline code, zero function call overhead
- **Standardization**: All applications use the same processing pattern

### 2. Error Handling and Debugging
- Use `comms_debug` macro for debug output
- Pointer validity checks to avoid null pointer access
- Return value checks to ensure operation success

### 3. Quality Assurance
- All code comments in English
- Concise and high-quality implementation
- Minimal code changes for maximum effect

## 📋 Next Steps Action Plan

1. **Immediate Execution**: Phase 1 and Phase 2 (solve FM application issues)
2. **Testing Validation**: Confirm FM application MCU communication is normal
3. **Gradual Deployment**: Adapt other applications by priority (Music→Bluetooth→USB→Settings)
4. **Documentation Update**: Improve application development guide and MCU communication specifications

## 🎯 Core Implementation Advantages

### Simplicity
- **Core Modification**: Only need to modify one empty case in FM application
- **Deployment Cost**: Each application only needs to add one line of processing code
- **Macro Simplicity**: 3 core macros cover all application scenarios

### Architecture Compatibility
- **Layer Clarity**: Application layer macros don't depend on driver layer headers
- **Logic Correctness**: Direct implementation of `MCU_PROTOCOL_SYNC_PROCESS` core logic
- **Feature Completeness**: Handle pending, clear flags, detect switching

### Performance Efficiency
- **Zero Overhead**: Macro expansion to inline code, no function call overhead
- **Atomic Operations**: Direct memory operations, highest efficiency
- **Clear and Concise**: One line of code completes all MCU sync processing

This implementation plan provides a clear roadmap for complete implementation of MCU Protocol v2 application layer communication, ensuring efficient and standardized deployment.