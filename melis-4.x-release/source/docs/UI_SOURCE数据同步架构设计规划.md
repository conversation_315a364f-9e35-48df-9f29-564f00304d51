# UI_SOURCE 数据同步架构设计规划

## 项目背景

基于昨天的UI_FM数据同步分析，现在需要在`ui_source_communication_hub.c`中实现一个更加完善的数据同步架构，满足以下核心需求：

### 核心需求
1. **双模式数据同步**：
   - **全量同步**：新建或切换work source时的完整数据同步
   - **增量同步**：基于`GUI_MSG_UI_NEW_xxx_UPDATE`消息的按需数据同步
   
2. **双向MCU通信**：
   - **GET操作**：从外部MCU获取数据
   - **SET操作**：向外部MCU设置数据

3. **统一管理**：所有数据同步函数集中在`ui_source_communication_hub.c`中实现

---

## 现有架构分析

### 1. 现有通信基础设施

从代码分析发现，系统已具备完善的通信基础：

#### MCU协议v2通信函数
```c
// 已存在的基础通信函数 (ui_source_communication_hub.c:66-125)
__s32 comms_send_category_get_pkg(__u8 category, __u8 *data, __u16 data_len)
__s32 comms_send_category_set_pkg(__u8 category, __u8 *data, __u16 data_len)
__s32 comms_send_audio_get_pkg(__u8 *data, __u16 data_len)
__s32 comms_send_audio_set_pkg(__u8 *data, __u16 data_len)
```

#### 新协议v2消息类型
```c
// 已定义的新消息类型 (msg_emit.h:760-796)
typedef enum __GUI_MSG_UI_NEW_ID {
    GUI_MSG_UI_NEW_TUNER_UPDATE,        // 调谐器统一更新
    GUI_MSG_UI_NEW_TUNER_FREQUENCY,     // 频率更新
    GUI_MSG_UI_NEW_TUNER_PRESET,        // 预设更新
    GUI_MSG_UI_NEW_TUNER_PRESET_LIST,   // 预设列表更新
    GUI_MSG_UI_NEW_TUNER_STATE,         // 状态更新
    GUI_MSG_UI_NEW_USB_DISK_UPDATE,     // USB磁盘统一更新
    // ... 更多消息类型
}
```

### 2. 现有数据流向

```
驱动层 → KMSG_USR_UI_NEW_xxx → DSK_MSG_UI_UPDATE → 
GUI_MSG_UI_NEW_xxx_UPDATE → 应用层处理 → UI更新
```

---

## 新架构设计

### 1. 数据同步管理器架构

#### 1.1 核心数据结构

```c
// 数据同步模式
typedef enum {
    SYNC_MODE_FULL,          // 全量同步模式
    SYNC_MODE_INCREMENTAL,   // 增量同步模式
    SYNC_MODE_DISABLED       // 禁用同步
} data_sync_mode_t;

// 数据同步状态
typedef enum {
    SYNC_STATUS_IDLE,        // 空闲状态
    SYNC_STATUS_SYNCING,     // 同步中
    SYNC_STATUS_SUCCESS,     // 同步成功
    SYNC_STATUS_ERROR,       // 同步失败
    SYNC_STATUS_TIMEOUT      // 同步超时
} data_sync_status_t;

// 数据类型定义 (扩展现有定义)
typedef enum {
    // 调谐器数据类型
    DATA_TYPE_TUNER_FREQUENCY    = 0x00010001,
    DATA_TYPE_TUNER_PRESET       = 0x00010002,
    DATA_TYPE_TUNER_PRESET_LIST  = 0x00010004,
    DATA_TYPE_TUNER_BAND         = 0x00010008,
    DATA_TYPE_TUNER_RDS_PS       = 0x00010010,
    DATA_TYPE_TUNER_RDS_RT       = 0x00010020,
    DATA_TYPE_TUNER_STATE        = 0x00010040,
    DATA_TYPE_TUNER_ALL          = 0x0001FFFF,
    
    // USB数据类型
    DATA_TYPE_USB_TRACK_INFO     = 0x00020001,
    DATA_TYPE_USB_PLAY_STATE     = 0x00020002,
    DATA_TYPE_USB_PLAY_TIME      = 0x00020004,
    DATA_TYPE_USB_PLAY_MODE      = 0x00020008,
    DATA_TYPE_USB_ALL            = 0x0002FFFF,
    
    // 音频数据类型
    DATA_TYPE_AUDIO_VOLUME       = 0x00040001,
    DATA_TYPE_AUDIO_MUTE         = 0x00040002,
    DATA_TYPE_AUDIO_EQ           = 0x00040004,
    DATA_TYPE_AUDIO_BALANCE      = 0x00040008,
    DATA_TYPE_AUDIO_ALL          = 0x0004FFFF,
    
    // 系统数据类型
    DATA_TYPE_SYSTEM_SOURCE      = 0x00080001,
    DATA_TYPE_SYSTEM_POWER       = 0x00080002,
    DATA_TYPE_SYSTEM_SETTINGS    = 0x00080004,
    DATA_TYPE_SYSTEM_ALL         = 0x0008FFFF
} data_type_mask_t;

// 同步任务结构
typedef struct {
    __u8 category;              // 协议分类
    __u8 operation;             // 操作类型 (GET/SET/EVENT)
    __u32 data_type_mask;       // 数据类型掩码
    __u8 *data_buffer;          // 数据缓冲区
    __u16 data_length;          // 数据长度
    __u32 timestamp;            // 时间戳
    data_sync_status_t status;  // 同步状态
    __u8 retry_count;           // 重试次数
} sync_task_t;

// 同步管理器主结构
typedef struct {
    data_sync_mode_t mode;           // 同步模式
    __bool sync_enabled;             // 同步使能标志
    __u32 current_source;            // 当前音源
    
    // 同步任务队列
    sync_task_t sync_queue[16];      // 同步任务队列
    __u8 queue_head;                 // 队列头指针
    __u8 queue_tail;                 // 队列尾指针
    __u8 queue_count;                // 队列中任务数量
    
    // 同步状态统计
    __u32 sync_count_total;          // 总同步次数
    __u32 sync_count_success;        // 成功同步次数
    __u32 sync_count_error;          // 失败同步次数
    __u32 last_sync_time;            // 上次同步时间
    
    // 数据缓存
    void *tuner_data_cache;          // 调谐器数据缓存
    void *usb_data_cache;            // USB数据缓存
    void *audio_data_cache;          // 音频数据缓存
    void *system_data_cache;         // 系统数据缓存
    
} data_sync_manager_t;
```

#### 1.2 同步管理器接口

```c
// 初始化和去初始化
__s32 ui_source_sync_manager_init(ui_source_para_t *para);
__s32 ui_source_sync_manager_deinit(ui_source_para_t *para);

// 模式控制
__s32 ui_source_sync_set_mode(data_sync_mode_t mode);
__s32 ui_source_sync_enable(__bool enable);

// 全量同步接口
__s32 ui_source_sync_full_data(__u32 source_type);
__s32 ui_source_sync_tuner_full_data(void);
__s32 ui_source_sync_usb_full_data(void);
__s32 ui_source_sync_audio_full_data(void);

// 增量同步接口
__s32 ui_source_sync_incremental_data(__u32 msg_id, __u32 data_type_mask, void *data, __u16 data_len);
__s32 ui_source_sync_tuner_data(__u32 data_type_mask, void *data, __u16 data_len);
__s32 ui_source_sync_usb_data(__u32 data_type_mask, void *data, __u16 data_len);
__s32 ui_source_sync_audio_data(__u32 data_type_mask, void *data, __u16 data_len);

// GET/SET MCU通信接口
__s32 ui_source_mcu_get_data(__u8 category, __u32 data_type_mask, void *buffer, __u16 buffer_size);
__s32 ui_source_mcu_set_data(__u8 category, __u32 data_type_mask, void *data, __u16 data_len);

// 状态查询接口
__s32 ui_source_sync_get_status(data_sync_status_t *status);
__s32 ui_source_sync_get_statistics(sync_statistics_t *stats);
```

### 2. 双模式同步实现

#### 2.1 全量同步模式

**触发时机**：
- 音源切换 (ID_WORK_SOURCE_FM → ID_WORK_SOURCE_USB_DISK等)
- 应用启动初始化
- 用户手动刷新

**实现逻辑**：
```c
__s32 ui_source_sync_full_data(__u32 source_type)
{
    __s32 ret = EPDK_OK;
    
    // 设置全量同步模式
    ui_source_sync_set_mode(SYNC_MODE_FULL);
    
    switch(source_type) {
        case ID_WORK_SOURCE_FM:
        case ID_WORK_SOURCE_AM:
        case ID_WORK_SOURCE_DAB:
            ret = ui_source_sync_tuner_full_data();
            break;
            
        case ID_WORK_SOURCE_USB_DISK:
        case ID_WORK_SOURCE_USB_IPOD:
            ret = ui_source_sync_usb_full_data();
            break;
            
        case ID_WORK_SOURCE_BT_AUDIO:
            ret = ui_source_sync_bt_full_data();
            break;
            
        default:
            ret = EPDK_FAIL;
            break;
    }
    
    // 同步音频设置 (所有音源共用)
    if (ret == EPDK_OK) {
        ret = ui_source_sync_audio_full_data();
    }
    
    return ret;
}
```

**调谐器全量同步示例**：
```c
__s32 ui_source_sync_tuner_full_data(void)
{
    __s32 ret = EPDK_OK;
    __u32 data_types[] = {
        DATA_TYPE_TUNER_FREQUENCY,
        DATA_TYPE_TUNER_PRESET,
        DATA_TYPE_TUNER_PRESET_LIST,
        DATA_TYPE_TUNER_BAND,
        DATA_TYPE_TUNER_RDS_PS,
        DATA_TYPE_TUNER_STATE
    };
    
    // 批量同步所有调谐器数据
    for (__u8 i = 0; i < sizeof(data_types)/sizeof(__u32); i++) {
        ret = ui_source_sync_add_task(CATEGORY_FM, OPERATION_EVENT, 
                                     data_types[i], NULL, 0);
        if (ret != EPDK_OK) {
            break;
        }
    }
    
    // 执行同步任务队列
    return ui_source_sync_execute_queue();
}
```

#### 2.2 增量同步模式

**触发时机**：
- 接收到`GUI_MSG_UI_NEW_xxx_UPDATE`消息
- 基于`pending_mask`的数据类型进行按需同步

**实现逻辑**：
```c
__s32 ui_source_sync_incremental_data(__u32 msg_id, __u32 data_type_mask, void *data, __u16 data_len)
{
    __s32 ret = EPDK_OK;
    __u8 category = 0;
    
    // 设置增量同步模式
    ui_source_sync_set_mode(SYNC_MODE_INCREMENTAL);
    
    // 根据消息ID确定分类
    switch(msg_id) {
        case GUI_MSG_UI_NEW_TUNER_UPDATE:
        case GUI_MSG_UI_NEW_TUNER_FREQUENCY:
        case GUI_MSG_UI_NEW_TUNER_PRESET:
            category = CATEGORY_FM;
            ret = ui_source_sync_tuner_data(data_type_mask, data, data_len);
            break;
            
        case GUI_MSG_UI_NEW_USB_DISK_UPDATE:
            category = CATEGORY_USB_DISK;
            ret = ui_source_sync_usb_data(data_type_mask, data, data_len);
            break;
            
        default:
            ret = EPDK_FAIL;
            break;
    }
    
    return ret;
}
```

**基于pending_mask的按需同步**：
```c
__s32 ui_source_sync_tuner_data(__u32 data_type_mask, void *data, __u16 data_len)
{
    __s32 ret = EPDK_OK;
    
    // 根据data_type_mask同步对应的数据类型
    if (data_type_mask & DATA_TYPE_TUNER_FREQUENCY) {
        ret = ui_source_sync_add_task(CATEGORY_FM, OPERATION_EVENT,
                                     DATA_TYPE_TUNER_FREQUENCY, data, data_len);
    }
    
    if (data_type_mask & DATA_TYPE_TUNER_PRESET) {
        ret = ui_source_sync_add_task(CATEGORY_FM, OPERATION_EVENT,
                                     DATA_TYPE_TUNER_PRESET, data, data_len);
    }
    
    if (data_type_mask & DATA_TYPE_TUNER_RDS_PS) {
        ret = ui_source_sync_add_task(CATEGORY_FM, OPERATION_EVENT,
                                     DATA_TYPE_TUNER_RDS_PS, data, data_len);
    }
    
    // 执行同步任务
    return ui_source_sync_execute_queue();
}
```

### 3. GET/SET MCU通信接口

#### 3.1 数据获取接口 (GET)

```c
// 通用GET接口
__s32 ui_source_mcu_get_data(__u8 category, __u32 data_type_mask, void *buffer, __u16 buffer_size)
{
    __s32 ret = EPDK_OK;
    __u8 request_data[32];
    __u16 request_len = 0;
    
    // 构建GET请求数据包
    ret = ui_source_build_get_request(data_type_mask, request_data, &request_len);
    if (ret != EPDK_OK) {
        return ret;
    }
    
    // 发送GET请求
    ret = comms_send_category_get_pkg(category, request_data, request_len);
    if (ret != EPDK_OK) {
        return ret;
    }
    
    // 等待响应 (可选实现异步回调)
    ret = ui_source_wait_mcu_response(buffer, buffer_size, 1000); // 1秒超时
    
    return ret;
}

// 特定数据类型的GET接口
__s32 ui_source_mcu_get_tuner_frequency(__u32 *frequency)
{
    return ui_source_mcu_get_data(CATEGORY_FM, DATA_TYPE_TUNER_FREQUENCY, 
                                 frequency, sizeof(__u32));
}

__s32 ui_source_mcu_get_tuner_preset_list(__u8 *preset_list, __u16 list_size)
{
    return ui_source_mcu_get_data(CATEGORY_FM, DATA_TYPE_TUNER_PRESET_LIST,
                                 preset_list, list_size);
}

__s32 ui_source_mcu_get_audio_volume(__u8 *volume)
{
    return ui_source_mcu_get_data(CATEGORY_AUDIO, DATA_TYPE_AUDIO_VOLUME,
                                 volume, sizeof(__u8));
}
```

#### 3.2 数据设置接口 (SET)

```c
// 通用SET接口
__s32 ui_source_mcu_set_data(__u8 category, __u32 data_type_mask, void *data, __u16 data_len)
{
    __s32 ret = EPDK_OK;
    __u8 set_data[128];
    __u16 set_len = 0;
    
    // 构建SET请求数据包
    ret = ui_source_build_set_request(data_type_mask, data, data_len, set_data, &set_len);
    if (ret != EPDK_OK) {
        return ret;
    }
    
    // 发送SET请求
    ret = comms_send_category_set_pkg(category, set_data, set_len);
    if (ret != EPDK_OK) {
        return ret;
    }
    
    // 等待ACK响应 (可选)
    ret = ui_source_wait_mcu_ack(500); // 500ms超时
    
    return ret;
}

// 特定数据类型的SET接口
__s32 ui_source_mcu_set_tuner_frequency(__u32 frequency)
{
    return ui_source_mcu_set_data(CATEGORY_FM, DATA_TYPE_TUNER_FREQUENCY,
                                 &frequency, sizeof(__u32));
}

__s32 ui_source_mcu_set_tuner_preset(__u8 preset_index)
{
    return ui_source_mcu_set_data(CATEGORY_FM, DATA_TYPE_TUNER_PRESET,
                                 &preset_index, sizeof(__u8));
}

__s32 ui_source_mcu_set_audio_volume(__u8 volume)
{
    return ui_source_mcu_set_data(CATEGORY_AUDIO, DATA_TYPE_AUDIO_VOLUME,
                                 &volume, sizeof(__u8));
}
```

### 4. 消息处理集成

#### 4.1 在消息处理中集成同步

需要在现有的消息处理流程中添加数据同步调用：

```c
// 在接收到GUI_MSG_UI_NEW_xxx_UPDATE消息时触发同步
__s32 ui_source_handle_new_update_message(__gui_msg_t *msg)
{
    __s32 ret = EPDK_OK;
    __u32 data_type_mask = 0;
    void *data_ptr = NULL;
    __u16 data_len = 0;
    
    // 解析消息获取pending_mask和数据
    ret = ui_source_parse_update_message(msg, &data_type_mask, &data_ptr, &data_len);
    if (ret != EPDK_OK) {
        return ret;
    }
    
    // 触发增量同步
    ret = ui_source_sync_incremental_data(msg->id, data_type_mask, data_ptr, data_len);
    
    return ret;
}
```

#### 4.2 音源切换时的全量同步

```c
// 在音源切换时触发全量同步
__s32 ui_source_handle_source_switch(__u32 new_source)
{
    __s32 ret = EPDK_OK;
    
    // 保存当前音源
    data_sync_manager_t *manager = ui_source_get_sync_manager();
    __u32 old_source = manager->current_source;
    manager->current_source = new_source;
    
    // 如果音源发生变化，触发全量同步
    if (old_source != new_source) {
        ret = ui_source_sync_full_data(new_source);
        
        // 记录同步日志
        eLIBs_printf("Source switched from %d to %d, full sync %s\n", 
                    old_source, new_source, (ret == EPDK_OK) ? "success" : "failed");
    }
    
    return ret;
}
```

---

## 具体实施计划

### 第一阶段：基础框架搭建 (2-3天)

#### 1.1 数据结构定义
- [ ] 在`ui_source_communication_hub.c`中定义同步管理器数据结构
- [ ] 扩展现有的数据类型定义
- [ ] 定义同步任务队列和状态管理结构

#### 1.2 基础接口实现
- [ ] 实现同步管理器初始化/去初始化
- [ ] 实现同步模式控制接口
- [ ] 实现任务队列管理函数

#### 1.3 协议构建函数
- [ ] 实现GET/SET请求数据包构建函数
- [ ] 扩展现有的`comms_send_category_*_pkg`函数
- [ ] 实现响应等待和解析函数

### 第二阶段：双模式同步实现 (3-4天)

#### 2.1 全量同步实现
- [ ] 实现调谐器全量同步
- [ ] 实现USB设备全量同步
- [ ] 实现音频设置全量同步
- [ ] 实现音源切换触发逻辑

#### 2.2 增量同步实现
- [ ] 实现基于消息的增量同步
- [ ] 实现基于pending_mask的按需同步
- [ ] 集成到现有的消息处理流程

#### 2.3 GET/SET接口实现
- [ ] 实现通用GET/SET接口
- [ ] 实现特定数据类型的便捷接口
- [ ] 实现异步响应处理机制

### 第三阶段：集成测试和优化 (2-3天)

#### 3.1 功能测试
- [ ] 测试全量同步功能
- [ ] 测试增量同步功能
- [ ] 测试GET/SET MCU通信
- [ ] 测试错误处理和重试机制

#### 3.2 性能优化
- [ ] 优化同步任务队列管理
- [ ] 实现智能去重和合并
- [ ] 添加性能监控和统计

#### 3.3 错误处理
- [ ] 实现超时和重试机制
- [ ] 添加同步状态监控
- [ ] 实现降级和恢复策略

### 第四阶段：文档和维护 (1-2天)

#### 4.1 技术文档
- [ ] 完善API文档
- [ ] 编写使用说明
- [ ] 创建调试指南

#### 4.2 调试支持
- [ ] 添加调试日志系统
- [ ] 实现状态查询接口
- [ ] 创建性能分析工具

---

## 关键技术细节

### 1. 数据类型掩码设计

使用32位掩码支持多种数据类型的组合：
```c
// 示例：同时同步频率和预设
__u32 mask = DATA_TYPE_TUNER_FREQUENCY | DATA_TYPE_TUNER_PRESET;
ui_source_sync_tuner_data(mask, data, data_len);
```

### 2. 任务队列管理

使用环形缓冲区实现高效的任务队列：
```c
// 添加任务到队列
__s32 ui_source_sync_add_task(__u8 category, __u8 operation, __u32 data_type, 
                             __u8 *data, __u16 data_len)
{
    data_sync_manager_t *mgr = ui_source_get_sync_manager();
    
    if (mgr->queue_count >= 16) {
        return EPDK_FAIL; // 队列满
    }
    
    sync_task_t *task = &mgr->sync_queue[mgr->queue_tail];
    task->category = category;
    task->operation = operation;
    task->data_type_mask = data_type;
    // ... 填充其他字段
    
    mgr->queue_tail = (mgr->queue_tail + 1) % 16;
    mgr->queue_count++;
    
    return EPDK_OK;
}
```

### 3. 智能去重机制

避免重复同步相同的数据：
```c
// 检查队列中是否已存在相同的同步任务
__bool ui_source_sync_task_exists(__u8 category, __u32 data_type_mask)
{
    data_sync_manager_t *mgr = ui_source_get_sync_manager();
    
    for (__u8 i = 0; i < mgr->queue_count; i++) {
        __u8 index = (mgr->queue_head + i) % 16;
        sync_task_t *task = &mgr->sync_queue[index];
        
        if (task->category == category && 
            (task->data_type_mask & data_type_mask)) {
            return 1; // 存在重复任务
        }
    }
    
    return 0; // 不存在重复任务
}
```

### 4. 错误处理和重试

实现智能重试机制：
```c
__s32 ui_source_sync_execute_task(sync_task_t *task)
{
    __s32 ret = EPDK_OK;
    
    // 设置任务状态为同步中
    task->status = SYNC_STATUS_SYNCING;
    task->timestamp = esKRNL_TimeGet();
    
    // 执行同步操作
    ret = comms_send_category_set_pkg(task->category, task->data_buffer, task->data_length);
    
    if (ret != EPDK_OK) {
        task->retry_count++;
        if (task->retry_count < 3) {
            task->status = SYNC_STATUS_IDLE; // 准备重试
            return EPDK_RETRY;
        } else {
            task->status = SYNC_STATUS_ERROR;
            return EPDK_FAIL;
        }
    }
    
    task->status = SYNC_STATUS_SUCCESS;
    return EPDK_OK;
}
```

---

## 风险评估和缓解策略

### 主要风险

1. **性能影响**：大量同步操作可能影响系统性能
   - **缓解策略**：实现任务队列限制和优先级管理

2. **通信失败**：MCU通信可能不稳定
   - **缓解策略**：实现重试机制和降级策略

3. **数据一致性**：多个数据源可能产生冲突
   - **缓解策略**：实现数据版本控制和冲突解决

4. **内存使用**：数据缓存可能占用过多内存
   - **缓解策略**：实现LRU缓存和内存限制

### 质量保证

1. **单元测试**：对每个接口进行单元测试
2. **集成测试**：测试完整的同步流程
3. **压力测试**：测试高频同步场景
4. **兼容性测试**：确保与现有代码兼容

---

## 总结

本设计方案提供了一个完整的、可扩展的数据同步架构，满足了以下核心目标：

1. **统一管理**：所有同步功能集中在`ui_source_communication_hub.c`
2. **双模式支持**：全量同步和增量同步两种模式
3. **双向通信**：支持GET和SET操作
4. **高性能**：任务队列和智能去重机制
5. **高可靠**：错误处理和重试机制
6. **易维护**：清晰的接口设计和完善的文档

通过分阶段实施，可以确保项目的顺利进行和质量保证。

---

**文档版本**: v1.0  
**创建日期**: 2025-01-16  
**作者**: Claude Code Analysis  
**更新记录**: 初始版本，基于用户新需求设计